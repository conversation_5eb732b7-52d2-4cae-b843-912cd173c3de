name: dtp_app
description: dtp_app

publish_to: 'none'

version: 0.0.3+74

environment:
  sdk: '>3.0.0 < 4.0.0'

flutter:
  generate: true

dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  # 多言語対応（国際化）
  intl: ^0.19.0

  # 状態管理（Riverpod + Hooks）
  flutter_hooks: ^0.20.5
  hooks_riverpod: ^2.3.6

  # モデルクラスとJSONシリアライズ自動生成（Freezed）
  freezed_annotation: ^2.2.0
  json_annotation: ^4.8.1

  # Firebaseの基本機能
  firebase_core: ^3.10.1
  # プッシュ通知
  firebase_messaging: ^15.2.1
  # アクセス解析（Google Analytics）
  firebase_analytics: ^11.4.1

  # HTTP通信（APIリクエスト）
  http: ^1.1.0

  # デバイス情報取得
  device_info_plus: ^10.1.2
  # アプリ情報（バージョン・ビルド番号など）取得
  package_info_plus: ^8.0.2

  # 外部URLの起動（電話、メール、ブラウザなど）
  url_launcher: ^6.1.12

  # ローディング中のスケルトン表示（キラキラ）
  shimmer: ^3.0.0

  # スプラッシュ画面生成（ネイティブ対応）
  flutter_native_splash: ^2.3.1

  # デバッグログ出力
  logger: ^2.4.0

  # 環境変数（.env）読み込み
  flutter_dotenv: ^5.1.0

  # iOSのトラッキング許可（ATT）
  app_tracking_transparency: ^2.0.4

  # モックWebサーバー（テスト用）
  mock_web_server: ^5.0.0-nullsafety.1

  # シンプルなローカル保存（設定値など）
  shared_preferences: ^2.3.4

  # SVG画像表示
  flutter_svg: ^2.0.7

  # WebView表示（InAppWebViewは高機能）
  flutter_inappwebview: ^6.1.5
  webview_flutter: ^4.0.0
  webview_flutter_wkwebview: ^3.16.3

  # アプリリンク（Android App Links / iOS Universal Links）
  app_links: ^6.3.2

  # カレンダーUI表示
  table_calendar: ^3.0.9

  # 脱獄 / root化の検出
  flutter_jailbreak_detection: ^1.10.0

  # グラフ描画（棒グラフ、折れ線グラフなど）
  fl_chart: ^0.69.0

  # アプリのバージョン比較
  version: ^3.0.2

  # デバイス表示切り替え（開発用）
  device_preview: ^1.1.0

  # 生体認証（指紋・顔認証）
  local_auth: ^2.1.8

  # セキュアなデータ保存（トークンなど）
  flutter_secure_storage: ^9.0.0

  # ハッシュ化・暗号化処理
  crypto: ^3.0.3
  encrypt: ^5.0.3

  # カメラでQRコード読み取り
  mobile_scanner: ^6.0.0

  # NFC読み取り
  nfc_manager: ^3.4.0

  # アニメーション追加
  flutter_animate: ^4.5.0

  # 画像読み込み（カメラ/ギャラリー）
  image_picker: ^1.1.2

  # カメラやマイクなどの権限リクエスト
  permission_handler: ^11.3.1

  # 文字セット処理
  charset: ^2.0.1

  # KARTE SDK
  karte_core: ^1.4.0

dev_dependencies:
  build_runner: ^2.4.8
  freezed: ^2.4.7
  json_serializable: ^6.7.1
