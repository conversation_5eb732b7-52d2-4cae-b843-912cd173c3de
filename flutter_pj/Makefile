pub-get:
	#dart pub get
	fvm flutter clean
	# packages get してパッケージの取得、システムキャッシュへの保存
	fvm flutter pub get

# コード自動生成 (l10n.yaml の変更を検知して自動的に生成される)
gen: pub-get
	fvm flutter pub run build_runner build -d

gen-auto: pub-get
	fvm flutter pub run build_runner watch -d

setup-ios: pub-get
	cd ios && pod install && cd ..

test:
	sh ./fix-and-test.sh
	open ./coverage/lcov-html/index.html

p-score:
	sh ./package-scorer.sh
	open ./scores.html

# --flavor production を付けて、flavor, --dart-define で環境変数を指定して起動
# --debug は true 固定
run:
	flutter run --flavor $(word 2, $(MAKECMDGOALS)) --dart-define=useStub=$(word 3, $(MAKECMDGOALS)) --dart-define=allowDebugMenu=$(word 4, $(MAKECMDGOALS))
	
.PHONY: gen gen-auto setup-ios test p-score run