# ローカル環境
BASE_URL="http://localhost:3501"
AUTHORIZATION_URL="http://localhost:3501/auth_dummy.html"
TRANSFER_INPUT_URL="http://localhost:3501/transfer_info_input_dummy.html"

# MobileWebviewリスト
# WebViewホワイトリスト
WHITELISTED_LIST="http://localhost:3501,http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com,http://dtp-dv-mobile-bff-stub2.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com,http://dtp-ts-mobile-bff-other-route.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com,https://mirror.biztest.smbc.co.jp/,http://dtp-dv-mobile-bff-backend-stub.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com,http://dtp-dv-mobile-bff-backend-stub2-dtp-dv-mobile-bff.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com,http://dtp-dv-mobile-bff-backend-stub3-dtp-dv-mobile-bff.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com,https://www.smbc.co.jp/,https://www.google.com/,https://www.googletagmanager.com,https://cashflow.midori.freee.co.jp,https://accounts.midori.freee.co.jp,https://st-marketing-assets.freee.co.jp,https://midori.freee.co.jp,https://test.biztest.smbc.co.jp/mobile/api/freee/auth,https://test.biztest.smbc.co.jp/common/api/freee/auth,https://valuedoor.smbc.co.jp/oauth2/avvew9h908/vdlogin?channel=02,https://stage.biztest.smbc.co.jp/,https://ekyc-smbchk.dev-polaris.com/,https://id.biztest.smbc.co.jp/idaas-manage,https://valuedoort.smbc.co.jp/w2isp"

API_VERSION="v1"

# IOS 強制アップデートURL (null の場合はチェックしない)
IOS_FORCE_UPDATE_SIGN_URL="https://dev.biztest.smbc.co.jp/portal/forceupdate/dev/mobile_app_ios.json"

# Android 強制アップデートURL (null の場合はチェックしない)
ANDROID_FORCE_UPDATE_SIGN_URL="https://dev.biztest.smbc.co.jp/portal/forceupdate/dev/mobile_app.json"

# WebView利用規約 (null の場合はチェックしない)
VALUE_PROP_URL="http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.bl018.pl.openshiftapps.com/gp/v1"

MYNAPOCKET_BASE_URL='http://localhost:3501/api/v1'

#IDaaSアカウント作成完了画面
#IDAAS_ACCOUNT_CREATION_COMPLETION='http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/idaas_account_creation_completion_dummy'
IDAAS_ACCOUNT_CREATION_COMPLETION='http://localhost:3501/idaas_account_creation_completion_dummy'
#IDaaSのbaseUrl
#IDAAS_BASE_URL='https://id.biztest.smbc.co.jp/idaas-manage/#/82'
#IDaaS_BASE_URLのドメイン
#IDAAS_BASE_URL='http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com'
IDAAS_BASE_URL='http://localhost:3501'
#WEB1S認証後遷移先URL (ワンタイムパスワード発行) ボタン押下後のURL (ブックマーク登録)
OTP_ISSUANCE_URL='https://valuedoort.smbc.co.jp/w2isp/otpcall'
#WEB1Sアプリへのユニバーサルリンク (主にiOSで使用、下はAndroidで使用)
WEB21_APP_UNIVERSAL_LINK='https://valuedoort.smbc.co.jp/w2isp/resources/html/avvhfe02.html'
#WEB21_APP_UNIVERSAL_LINK='https://valuedoort.smbc.co.jp/w2isp/resources/html/avvhfe02.html'
# DTPWEB版 ログアウト画面
DIGITAL_TOUCH_POINT_LOGOUT='https://valuedoort.smbc.co.jp/oautho2/avve9h908/vdlogout?channel=01'

# freee リダイレクト画面
FREE_REDIRECT_SCREEN='https://stage.biztest.smbc.co.jp/portal/freee_redirect.html'
# freeeメンバー追加画面(dev2環境)
FREE_ACCOUNT_MEMBERS='http://freee.co.jp/lp2/accounting/07/03/'
# お知らせ表示用 jsonURL パターン1 (AWS の S3 に配置)
ANNOUNCE_JSON_URL='https://dev.biztest.smbc.co.jp/portal/announce/announce1.json'
# お知らせ表示用 jsonURL パターン2 (AWS の S3 に配置)
#ANNOUNCE_JSON_URL='https://dev.biztest.smbc.co.jp/portal/announce/announce2.json'
# お知らせ表示用 jsonURL パターン3 (AWS の S3 に配置)
#ANNOUNCE_JSON_URL='https://dev.biztest.smbc.co.jp/portal/announce/announce.json'
# お知らせ表示用 jsonURL パターン4 (AWS の S3 に配置)
#ANNOUNCE_JSON_URL='https://dev.biztest.smbc.co.jp/portal/announce/announce3.json'
# eKYC からのリダイレクト URL (ブックマーク登録)
EKYC_REDIRECT_URL='/webhook/ekyc_end'
# HTTP 許容評価フラグ
HTTP_ALLOWED = 'YES'