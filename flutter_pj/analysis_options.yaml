include: package:flutter_lints/flutter.yaml

linter:
  rules:
    - always_use_package_imports
    - cancel_subscriptions
    - close_sinks
    - unnecessary_statements
    - always_declare_return_types
    - only_throw_errors
    - parameter_assignments
    - prefer_final_in_for_each
    - prefer_final_locals
    - prefer_null_aware_method_calls
    - prefer_single_quotes
    - require_trailing_commas
    - unawaited_futures

analyzer:
  exclude:
    - lib/generated/**.dart
    - lib/**/*.g.dart
    - lib/**/*.freezed.dart
    - test/**/*.mocks.dart
  errors:
    invalid_annotation_target: ignore
    always_use_package_imports: error
    avoid_print: error
  language:
    strict-casts: true
    strict-raw-types: true