# ステージング環境
BASE_URL="https://stage.biztest.smbc.co.jp"
AUTHORIZATION_URL="https://stage.biztest.smbc.co.jp/auth_dummy.html"
TRANSFER_INPUT_URL="https://stage.biztest.smbc.co.jp/transfer_info_input_dummy.html"

# MobileWebviewリスト
# WebViewホワイトリスト
WHITELISTED_LIST="https://stage.biztest.smbc.co.jp,https://mirror.biztest.smbc.co.jp/,https://www.smbc.co.jp/,https://www.google.com/,https://www.googletagmanager.com,https://cashflow.midori.freee.co.jp,https://accounts.midori.freee.co.jp,https://st-marketing-assets.freee.co.jp,https://midori.freee.co.jp,https://test.biztest.smbc.co.jp/mobile/api/freee/auth,https://test.biztest.smbc.co.jp/common/api/freee/auth,https://valuedoor.smbc.co.jp/oauth2/avvew9h908/vdlogin?channel=02,https://stage.biztest.smbc.co.jp/,https://ekyc-smbchk.dev-polaris.com/,https://id.biztest.smbc.co.jp/idaas-manage,https://valuedoort.smbc.co.jp/w2isp"

API_VERSION="v1"

# IOS 強制アップデートURL (null の場合はチェックしない)
IOS_FORCE_UPDATE_SIGN_URL="https://stage.biztest.smbc.co.jp/portal/forceupdate/stg/mobile_app_ios.json"

# Android 強制アップデートURL (null の場合はチェックしない)
ANDROID_FORCE_UPDATE_SIGN_URL="https://stage.biztest.smbc.co.jp/portal/forceupdate/stg/mobile_app.json"

# WebView利用規約 (null の場合はチェックしない)
VALUE_PROP_URL="https://stage.biztest.smbc.co.jp/gp/v1"

MYNAPOCKET_BASE_URL='https://stage.biztest.smbc.co.jp/api/v1'

#IDaaSアカウント作成完了画面
IDAAS_ACCOUNT_CREATION_COMPLETION='https://stage.biztest.smbc.co.jp/idaas_account_creation_completion_dummy'
#IDaaSのbaseUrl
IDAAS_BASE_URL='https://stage.biztest.smbc.co.jp'
#WEB1S認証後遷移先URL (ワンタイムパスワード発行) ボタン押下後のURL (ブックマーク登録)
OTP_ISSUANCE_URL='https://valuedoort.smbc.co.jp/w2isp/otpcall'
#WEB1Sアプリへのユニバーサルリンク (主にiOSで使用、下はAndroidで使用)
WEB21_APP_UNIVERSAL_LINK='https://valuedoort.smbc.co.jp/w2isp/resources/html/avvhfe02.html'
# DTPWEB版 ログアウト画面
DIGITAL_TOUCH_POINT_LOGOUT='https://valuedoort.smbc.co.jp/oautho2/avve9h908/vdlogout?channel=01'

# freee リダイレクト画面
FREE_REDIRECT_SCREEN='https://stage.biztest.smbc.co.jp/portal/freee_redirect.html'
# freeeメンバー追加画面(dev2環境)
FREE_ACCOUNT_MEMBERS='http://freee.co.jp/lp2/accounting/07/03/'
# お知らせ表示用 jsonURL
ANNOUNCE_JSON_URL='https://stage.biztest.smbc.co.jp/portal/announce/announce.json'
# eKYC からのリダイレクト URL (ブックマーク登録)
EKYC_REDIRECT_URL='/webhook/ekyc_end'
# HTTP 許容評価フラグ
HTTP_ALLOWED = 'NO'
