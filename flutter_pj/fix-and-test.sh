#!/bin/sh

dart fix --apply .
dart format .
flutter analyze
# ユニットテストを実行する
flutter test --coverage
# カバレッジの計測
lcov --extract coverage/lcov.info 'lib/repositories/api_client.dart' \
    --extract coverage/lcov.info 'lib/repositories/*/*_repository_impl.dart' \
    --extract coverage/lcov.info 'lib/business_logic/*/*_screen_notifier.dart' \
    --extract coverage/lcov.info 'lib/business_logic/*/*_controller.dart' \
    --extract coverage/lcov.info 'lib/models/*/*.dart' \
    --extract coverage/lcov.info 'lib/views/*/*_settings.dart' \
    -o coverage/lcov.info
# 不要なカバレッジ情報の削除
lcov --remove coverage/lcov_extract.info 'lib/**/debug_*.dart' \
    --remove coverage/lcov_extract.info 'lib/**/*.g.dart' \
    --remove coverage/lcov_extract.info 'lib/**/*.freezed.dart' \
    --remove coverage/lcov_extract.info 'lib/repositories/image_picker' \
    --remove coverage/lcov_extract.info 'lib/repositories/karte_sdk' \
    -o coverage/lcov_extract.info
genhtml coverage/lcov_extract.info -o coverage/html