// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'validation_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ValidationError {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ValidationErrorCopyWith<$Res> {
  factory $ValidationErrorCopyWith(
          ValidationError value, $Res Function(ValidationError) then) =
      _$ValidationErrorCopyWithImpl<$Res, ValidationError>;
}

/// @nodoc
class _$ValidationErrorCopyWithImpl<$Res, $Val extends ValidationError>
    implements $ValidationErrorCopyWith<$Res> {
  _$ValidationErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$NoneImplCopyWith<$Res> {
  factory _$$NoneImplCopyWith(
          _$NoneImpl value, $Res Function(_$NoneImpl) then) =
      __$$NoneImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoneImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NoneImpl>
    implements _$$NoneImplCopyWith<$Res> {
  __$$NoneImplCopyWithImpl(_$NoneImpl _value, $Res Function(_$NoneImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoneImpl implements _None {
  const _$NoneImpl();

  @override
  String toString() {
    return 'ValidationError.none()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoneImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return none();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return none?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (none != null) {
      return none();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return none(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return none?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (none != null) {
      return none(this);
    }
    return orElse();
  }
}

abstract class _None implements ValidationError, ValidationOK {
  const factory _None() = _$NoneImpl;
}

/// @nodoc
abstract class _$$DisabledImplCopyWith<$Res> {
  factory _$$DisabledImplCopyWith(
          _$DisabledImpl value, $Res Function(_$DisabledImpl) then) =
      __$$DisabledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DisabledImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$DisabledImpl>
    implements _$$DisabledImplCopyWith<$Res> {
  __$$DisabledImplCopyWithImpl(
      _$DisabledImpl _value, $Res Function(_$DisabledImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DisabledImpl implements _Disabled {
  const _$DisabledImpl();

  @override
  String toString() {
    return 'ValidationError.disabled()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$DisabledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return disabled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return disabled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (disabled != null) {
      return disabled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return disabled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return disabled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (disabled != null) {
      return disabled(this);
    }
    return orElse();
  }
}

abstract class _Disabled implements ValidationError, ValidationOK {
  const factory _Disabled() = _$DisabledImpl;
}

/// @nodoc
abstract class _$$RequiredImplCopyWith<$Res> {
  factory _$$RequiredImplCopyWith(
          _$RequiredImpl value, $Res Function(_$RequiredImpl) then) =
      __$$RequiredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({RequiredStyle style});
}

/// @nodoc
class __$$RequiredImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$RequiredImpl>
    implements _$$RequiredImplCopyWith<$Res> {
  __$$RequiredImplCopyWithImpl(
      _$RequiredImpl _value, $Res Function(_$RequiredImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? style = null,
  }) {
    return _then(_$RequiredImpl(
      style: null == style
          ? _value.style
          : style // ignore: cast_nullable_to_non_nullable
              as RequiredStyle,
    ));
  }
}

/// @nodoc

class _$RequiredImpl implements _Required {
  const _$RequiredImpl({this.style = RequiredStyle.input});

  @override
  @JsonKey()
  final RequiredStyle style;

  @override
  String toString() {
    return 'ValidationError.required(style: $style)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RequiredImpl &&
            (identical(other.style, style) || other.style == style));
  }

  @override
  int get hashCode => Object.hash(runtimeType, style);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RequiredImplCopyWith<_$RequiredImpl> get copyWith =>
      __$$RequiredImplCopyWithImpl<_$RequiredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return required(style);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return required?.call(style);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (required != null) {
      return required(style);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return required(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return required?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (required != null) {
      return required(this);
    }
    return orElse();
  }
}

abstract class _Required implements ValidationError {
  const factory _Required({final RequiredStyle style}) = _$RequiredImpl;

  RequiredStyle get style;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RequiredImplCopyWith<_$RequiredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MaxLengthImplCopyWith<$Res> {
  factory _$$MaxLengthImplCopyWith(
          _$MaxLengthImpl value, $Res Function(_$MaxLengthImpl) then) =
      __$$MaxLengthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int maxLength});
}

/// @nodoc
class __$$MaxLengthImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$MaxLengthImpl>
    implements _$$MaxLengthImplCopyWith<$Res> {
  __$$MaxLengthImplCopyWithImpl(
      _$MaxLengthImpl _value, $Res Function(_$MaxLengthImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxLength = null,
  }) {
    return _then(_$MaxLengthImpl(
      null == maxLength
          ? _value.maxLength
          : maxLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$MaxLengthImpl implements _MaxLength {
  const _$MaxLengthImpl(this.maxLength);

  @override
  final int maxLength;

  @override
  String toString() {
    return 'ValidationError.maxLength(maxLength: $maxLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MaxLengthImpl &&
            (identical(other.maxLength, maxLength) ||
                other.maxLength == maxLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, maxLength);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MaxLengthImplCopyWith<_$MaxLengthImpl> get copyWith =>
      __$$MaxLengthImplCopyWithImpl<_$MaxLengthImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return maxLength(this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return maxLength?.call(this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (maxLength != null) {
      return maxLength(this.maxLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return maxLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return maxLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (maxLength != null) {
      return maxLength(this);
    }
    return orElse();
  }
}

abstract class _MaxLength implements ValidationError {
  const factory _MaxLength(final int maxLength) = _$MaxLengthImpl;

  int get maxLength;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MaxLengthImplCopyWith<_$MaxLengthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$sumMaxLengthImplCopyWith<$Res> {
  factory _$$sumMaxLengthImplCopyWith(
          _$sumMaxLengthImpl value, $Res Function(_$sumMaxLengthImpl) then) =
      __$$sumMaxLengthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int maxLength});
}

/// @nodoc
class __$$sumMaxLengthImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$sumMaxLengthImpl>
    implements _$$sumMaxLengthImplCopyWith<$Res> {
  __$$sumMaxLengthImplCopyWithImpl(
      _$sumMaxLengthImpl _value, $Res Function(_$sumMaxLengthImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxLength = null,
  }) {
    return _then(_$sumMaxLengthImpl(
      null == maxLength
          ? _value.maxLength
          : maxLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$sumMaxLengthImpl implements _sumMaxLength {
  const _$sumMaxLengthImpl(this.maxLength);

  @override
  final int maxLength;

  @override
  String toString() {
    return 'ValidationError.sumMaxLength(maxLength: $maxLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$sumMaxLengthImpl &&
            (identical(other.maxLength, maxLength) ||
                other.maxLength == maxLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, maxLength);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$sumMaxLengthImplCopyWith<_$sumMaxLengthImpl> get copyWith =>
      __$$sumMaxLengthImplCopyWithImpl<_$sumMaxLengthImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return sumMaxLength(this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return sumMaxLength?.call(this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (sumMaxLength != null) {
      return sumMaxLength(this.maxLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return sumMaxLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return sumMaxLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (sumMaxLength != null) {
      return sumMaxLength(this);
    }
    return orElse();
  }
}

abstract class _sumMaxLength implements ValidationError {
  const factory _sumMaxLength(final int maxLength) = _$sumMaxLengthImpl;

  int get maxLength;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$sumMaxLengthImplCopyWith<_$sumMaxLengthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MinLengthImplCopyWith<$Res> {
  factory _$$MinLengthImplCopyWith(
          _$MinLengthImpl value, $Res Function(_$MinLengthImpl) then) =
      __$$MinLengthImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int minLength});
}

/// @nodoc
class __$$MinLengthImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$MinLengthImpl>
    implements _$$MinLengthImplCopyWith<$Res> {
  __$$MinLengthImplCopyWithImpl(
      _$MinLengthImpl _value, $Res Function(_$MinLengthImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minLength = null,
  }) {
    return _then(_$MinLengthImpl(
      null == minLength
          ? _value.minLength
          : minLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$MinLengthImpl implements _MinLength {
  const _$MinLengthImpl(this.minLength);

  @override
  final int minLength;

  @override
  String toString() {
    return 'ValidationError.minLength(minLength: $minLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MinLengthImpl &&
            (identical(other.minLength, minLength) ||
                other.minLength == minLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, minLength);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MinLengthImplCopyWith<_$MinLengthImpl> get copyWith =>
      __$$MinLengthImplCopyWithImpl<_$MinLengthImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return minLength(this.minLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return minLength?.call(this.minLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (minLength != null) {
      return minLength(this.minLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return minLength(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return minLength?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (minLength != null) {
      return minLength(this);
    }
    return orElse();
  }
}

abstract class _MinLength implements ValidationError {
  const factory _MinLength(final int minLength) = _$MinLengthImpl;

  int get minLength;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MinLengthImplCopyWith<_$MinLengthImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LengthEqualsImplCopyWith<$Res> {
  factory _$$LengthEqualsImplCopyWith(
          _$LengthEqualsImpl value, $Res Function(_$LengthEqualsImpl) then) =
      __$$LengthEqualsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int length});
}

/// @nodoc
class __$$LengthEqualsImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$LengthEqualsImpl>
    implements _$$LengthEqualsImplCopyWith<$Res> {
  __$$LengthEqualsImplCopyWithImpl(
      _$LengthEqualsImpl _value, $Res Function(_$LengthEqualsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? length = null,
  }) {
    return _then(_$LengthEqualsImpl(
      null == length
          ? _value.length
          : length // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LengthEqualsImpl implements _LengthEquals {
  const _$LengthEqualsImpl(this.length);

  @override
  final int length;

  @override
  String toString() {
    return 'ValidationError.lengthEquals(length: $length)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LengthEqualsImpl &&
            (identical(other.length, length) || other.length == length));
  }

  @override
  int get hashCode => Object.hash(runtimeType, length);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LengthEqualsImplCopyWith<_$LengthEqualsImpl> get copyWith =>
      __$$LengthEqualsImplCopyWithImpl<_$LengthEqualsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return lengthEquals(length);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return lengthEquals?.call(length);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (lengthEquals != null) {
      return lengthEquals(length);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return lengthEquals(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return lengthEquals?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (lengthEquals != null) {
      return lengthEquals(this);
    }
    return orElse();
  }
}

abstract class _LengthEquals implements ValidationError {
  const factory _LengthEquals(final int length) = _$LengthEqualsImpl;

  int get length;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LengthEqualsImplCopyWith<_$LengthEqualsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ReferenceNumberLengthEqualsImplCopyWith<$Res> {
  factory _$$ReferenceNumberLengthEqualsImplCopyWith(
          _$ReferenceNumberLengthEqualsImpl value,
          $Res Function(_$ReferenceNumberLengthEqualsImpl) then) =
      __$$ReferenceNumberLengthEqualsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int minLength, int maxLength});
}

/// @nodoc
class __$$ReferenceNumberLengthEqualsImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res,
        _$ReferenceNumberLengthEqualsImpl>
    implements _$$ReferenceNumberLengthEqualsImplCopyWith<$Res> {
  __$$ReferenceNumberLengthEqualsImplCopyWithImpl(
      _$ReferenceNumberLengthEqualsImpl _value,
      $Res Function(_$ReferenceNumberLengthEqualsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minLength = null,
    Object? maxLength = null,
  }) {
    return _then(_$ReferenceNumberLengthEqualsImpl(
      null == minLength
          ? _value.minLength
          : minLength // ignore: cast_nullable_to_non_nullable
              as int,
      null == maxLength
          ? _value.maxLength
          : maxLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$ReferenceNumberLengthEqualsImpl
    implements _ReferenceNumberLengthEquals {
  const _$ReferenceNumberLengthEqualsImpl(this.minLength, this.maxLength);

  @override
  final int minLength;
  @override
  final int maxLength;

  @override
  String toString() {
    return 'ValidationError.referenceNumberLengthEquals(minLength: $minLength, maxLength: $maxLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferenceNumberLengthEqualsImpl &&
            (identical(other.minLength, minLength) ||
                other.minLength == minLength) &&
            (identical(other.maxLength, maxLength) ||
                other.maxLength == maxLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, minLength, maxLength);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferenceNumberLengthEqualsImplCopyWith<_$ReferenceNumberLengthEqualsImpl>
      get copyWith => __$$ReferenceNumberLengthEqualsImplCopyWithImpl<
          _$ReferenceNumberLengthEqualsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return referenceNumberLengthEquals(this.minLength, this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return referenceNumberLengthEquals?.call(this.minLength, this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (referenceNumberLengthEquals != null) {
      return referenceNumberLengthEquals(this.minLength, this.maxLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return referenceNumberLengthEquals(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return referenceNumberLengthEquals?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (referenceNumberLengthEquals != null) {
      return referenceNumberLengthEquals(this);
    }
    return orElse();
  }
}

abstract class _ReferenceNumberLengthEquals implements ValidationError {
  const factory _ReferenceNumberLengthEquals(
          final int minLength, final int maxLength) =
      _$ReferenceNumberLengthEqualsImpl;

  int get minLength;
  int get maxLength;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReferenceNumberLengthEqualsImplCopyWith<_$ReferenceNumberLengthEqualsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LengthRangeImplCopyWith<$Res> {
  factory _$$LengthRangeImplCopyWith(
          _$LengthRangeImpl value, $Res Function(_$LengthRangeImpl) then) =
      __$$LengthRangeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int minLength, int maxLength});
}

/// @nodoc
class __$$LengthRangeImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$LengthRangeImpl>
    implements _$$LengthRangeImplCopyWith<$Res> {
  __$$LengthRangeImplCopyWithImpl(
      _$LengthRangeImpl _value, $Res Function(_$LengthRangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minLength = null,
    Object? maxLength = null,
  }) {
    return _then(_$LengthRangeImpl(
      null == minLength
          ? _value.minLength
          : minLength // ignore: cast_nullable_to_non_nullable
              as int,
      null == maxLength
          ? _value.maxLength
          : maxLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$LengthRangeImpl implements _LengthRange {
  const _$LengthRangeImpl(this.minLength, this.maxLength);

  @override
  final int minLength;
  @override
  final int maxLength;

  @override
  String toString() {
    return 'ValidationError.lengthRange(minLength: $minLength, maxLength: $maxLength)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LengthRangeImpl &&
            (identical(other.minLength, minLength) ||
                other.minLength == minLength) &&
            (identical(other.maxLength, maxLength) ||
                other.maxLength == maxLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, minLength, maxLength);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LengthRangeImplCopyWith<_$LengthRangeImpl> get copyWith =>
      __$$LengthRangeImplCopyWithImpl<_$LengthRangeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return lengthRange(this.minLength, this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return lengthRange?.call(this.minLength, this.maxLength);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (lengthRange != null) {
      return lengthRange(this.minLength, this.maxLength);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return lengthRange(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return lengthRange?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (lengthRange != null) {
      return lengthRange(this);
    }
    return orElse();
  }
}

abstract class _LengthRange implements ValidationError {
  const factory _LengthRange(final int minLength, final int maxLength) =
      _$LengthRangeImpl;

  int get minLength;
  int get maxLength;

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LengthRangeImplCopyWith<_$LengthRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$InvalidAddressImplCopyWith<$Res> {
  factory _$$InvalidAddressImplCopyWith(_$InvalidAddressImpl value,
          $Res Function(_$InvalidAddressImpl) then) =
      __$$InvalidAddressImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidAddressImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidAddressImpl>
    implements _$$InvalidAddressImplCopyWith<$Res> {
  __$$InvalidAddressImplCopyWithImpl(
      _$InvalidAddressImpl _value, $Res Function(_$InvalidAddressImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidAddressImpl implements _InvalidAddress {
  const _$InvalidAddressImpl();

  @override
  String toString() {
    return 'ValidationError.invalidAddress()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InvalidAddressImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidAddress();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidAddress?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidAddress != null) {
      return invalidAddress();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidAddress(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidAddress?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidAddress != null) {
      return invalidAddress(this);
    }
    return orElse();
  }
}

abstract class _InvalidAddress implements ValidationError {
  const factory _InvalidAddress() = _$InvalidAddressImpl;
}

/// @nodoc
abstract class _$$InvalidEmailDomainImplCopyWith<$Res> {
  factory _$$InvalidEmailDomainImplCopyWith(_$InvalidEmailDomainImpl value,
          $Res Function(_$InvalidEmailDomainImpl) then) =
      __$$InvalidEmailDomainImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidEmailDomainImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidEmailDomainImpl>
    implements _$$InvalidEmailDomainImplCopyWith<$Res> {
  __$$InvalidEmailDomainImplCopyWithImpl(_$InvalidEmailDomainImpl _value,
      $Res Function(_$InvalidEmailDomainImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidEmailDomainImpl implements _InvalidEmailDomain {
  const _$InvalidEmailDomainImpl();

  @override
  String toString() {
    return 'ValidationError.invalidEmailDomain()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InvalidEmailDomainImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidEmailDomain();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidEmailDomain?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidEmailDomain != null) {
      return invalidEmailDomain();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidEmailDomain(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidEmailDomain?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidEmailDomain != null) {
      return invalidEmailDomain(this);
    }
    return orElse();
  }
}

abstract class _InvalidEmailDomain implements ValidationError {
  const factory _InvalidEmailDomain() = _$InvalidEmailDomainImpl;
}

/// @nodoc
abstract class _$$InvalidEmailFormatImplCopyWith<$Res> {
  factory _$$InvalidEmailFormatImplCopyWith(_$InvalidEmailFormatImpl value,
          $Res Function(_$InvalidEmailFormatImpl) then) =
      __$$InvalidEmailFormatImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidEmailFormatImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidEmailFormatImpl>
    implements _$$InvalidEmailFormatImplCopyWith<$Res> {
  __$$InvalidEmailFormatImplCopyWithImpl(_$InvalidEmailFormatImpl _value,
      $Res Function(_$InvalidEmailFormatImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidEmailFormatImpl implements _InvalidEmailFormat {
  const _$InvalidEmailFormatImpl();

  @override
  String toString() {
    return 'ValidationError.invalidEmailFormat()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InvalidEmailFormatImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidEmailFormat();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidEmailFormat?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidEmailFormat != null) {
      return invalidEmailFormat();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidEmailFormat(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidEmailFormat?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidEmailFormat != null) {
      return invalidEmailFormat(this);
    }
    return orElse();
  }
}

abstract class _InvalidEmailFormat implements ValidationError {
  const factory _InvalidEmailFormat() = _$InvalidEmailFormatImpl;
}

/// @nodoc
abstract class _$$InvalidEmailCorrelationImplCopyWith<$Res> {
  factory _$$InvalidEmailCorrelationImplCopyWith(
          _$InvalidEmailCorrelationImpl value,
          $Res Function(_$InvalidEmailCorrelationImpl) then) =
      __$$InvalidEmailCorrelationImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidEmailCorrelationImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidEmailCorrelationImpl>
    implements _$$InvalidEmailCorrelationImplCopyWith<$Res> {
  __$$InvalidEmailCorrelationImplCopyWithImpl(
      _$InvalidEmailCorrelationImpl _value,
      $Res Function(_$InvalidEmailCorrelationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidEmailCorrelationImpl implements _InvalidEmailCorrelation {
  const _$InvalidEmailCorrelationImpl();

  @override
  String toString() {
    return 'ValidationError.invalidEmailCorrelation()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidEmailCorrelationImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidEmailCorrelation();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidEmailCorrelation?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidEmailCorrelation != null) {
      return invalidEmailCorrelation();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidEmailCorrelation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidEmailCorrelation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidEmailCorrelation != null) {
      return invalidEmailCorrelation(this);
    }
    return orElse();
  }
}

abstract class _InvalidEmailCorrelation implements ValidationError {
  const factory _InvalidEmailCorrelation() = _$InvalidEmailCorrelationImpl;
}

/// @nodoc
abstract class _$$InvalidMobileNumberFormatImplCopyWith<$Res> {
  factory _$$InvalidMobileNumberFormatImplCopyWith(
          _$InvalidMobileNumberFormatImpl value,
          $Res Function(_$InvalidMobileNumberFormatImpl) then) =
      __$$InvalidMobileNumberFormatImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidMobileNumberFormatImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidMobileNumberFormatImpl>
    implements _$$InvalidMobileNumberFormatImplCopyWith<$Res> {
  __$$InvalidMobileNumberFormatImplCopyWithImpl(
      _$InvalidMobileNumberFormatImpl _value,
      $Res Function(_$InvalidMobileNumberFormatImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidMobileNumberFormatImpl implements _InvalidMobileNumberFormat {
  const _$InvalidMobileNumberFormatImpl();

  @override
  String toString() {
    return 'ValidationError.invalidMobileNumberFormat()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidMobileNumberFormatImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidMobileNumberFormat();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidMobileNumberFormat?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidMobileNumberFormat != null) {
      return invalidMobileNumberFormat();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidMobileNumberFormat(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidMobileNumberFormat?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidMobileNumberFormat != null) {
      return invalidMobileNumberFormat(this);
    }
    return orElse();
  }
}

abstract class _InvalidMobileNumberFormat implements ValidationError {
  const factory _InvalidMobileNumberFormat() = _$InvalidMobileNumberFormatImpl;
}

/// @nodoc
abstract class _$$InvalidPostalCodeFormatImplCopyWith<$Res> {
  factory _$$InvalidPostalCodeFormatImplCopyWith(
          _$InvalidPostalCodeFormatImpl value,
          $Res Function(_$InvalidPostalCodeFormatImpl) then) =
      __$$InvalidPostalCodeFormatImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidPostalCodeFormatImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidPostalCodeFormatImpl>
    implements _$$InvalidPostalCodeFormatImplCopyWith<$Res> {
  __$$InvalidPostalCodeFormatImplCopyWithImpl(
      _$InvalidPostalCodeFormatImpl _value,
      $Res Function(_$InvalidPostalCodeFormatImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidPostalCodeFormatImpl implements _InvalidPostalCodeFormat {
  const _$InvalidPostalCodeFormatImpl();

  @override
  String toString() {
    return 'ValidationError.invalidPostalCodeFormat()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InvalidPostalCodeFormatImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidPostalCodeFormat();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidPostalCodeFormat?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidPostalCodeFormat != null) {
      return invalidPostalCodeFormat();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidPostalCodeFormat(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidPostalCodeFormat?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidPostalCodeFormat != null) {
      return invalidPostalCodeFormat(this);
    }
    return orElse();
  }
}

abstract class _InvalidPostalCodeFormat implements ValidationError {
  const factory _InvalidPostalCodeFormat() = _$InvalidPostalCodeFormatImpl;
}

/// @nodoc
abstract class _$$InvalidDateImplCopyWith<$Res> {
  factory _$$InvalidDateImplCopyWith(
          _$InvalidDateImpl value, $Res Function(_$InvalidDateImpl) then) =
      __$$InvalidDateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InvalidDateImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$InvalidDateImpl>
    implements _$$InvalidDateImplCopyWith<$Res> {
  __$$InvalidDateImplCopyWithImpl(
      _$InvalidDateImpl _value, $Res Function(_$InvalidDateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InvalidDateImpl implements _InvalidDate {
  const _$InvalidDateImpl();

  @override
  String toString() {
    return 'ValidationError.invalidDate()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InvalidDateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return invalidDate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return invalidDate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidDate != null) {
      return invalidDate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return invalidDate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return invalidDate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (invalidDate != null) {
      return invalidDate(this);
    }
    return orElse();
  }
}

abstract class _InvalidDate implements ValidationError {
  const factory _InvalidDate() = _$InvalidDateImpl;
}

/// @nodoc
abstract class _$$futureDateImplCopyWith<$Res> {
  factory _$$futureDateImplCopyWith(
          _$futureDateImpl value, $Res Function(_$futureDateImpl) then) =
      __$$futureDateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$futureDateImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$futureDateImpl>
    implements _$$futureDateImplCopyWith<$Res> {
  __$$futureDateImplCopyWithImpl(
      _$futureDateImpl _value, $Res Function(_$futureDateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$futureDateImpl implements _futureDate {
  const _$futureDateImpl();

  @override
  String toString() {
    return 'ValidationError.futureDate()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$futureDateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return futureDate();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return futureDate?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (futureDate != null) {
      return futureDate();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return futureDate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return futureDate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (futureDate != null) {
      return futureDate(this);
    }
    return orElse();
  }
}

abstract class _futureDate implements ValidationError {
  const factory _futureDate() = _$futureDateImpl;
}

/// @nodoc
abstract class _$$NotDigitImplCopyWith<$Res> {
  factory _$$NotDigitImplCopyWith(
          _$NotDigitImpl value, $Res Function(_$NotDigitImpl) then) =
      __$$NotDigitImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotDigitImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NotDigitImpl>
    implements _$$NotDigitImplCopyWith<$Res> {
  __$$NotDigitImplCopyWithImpl(
      _$NotDigitImpl _value, $Res Function(_$NotDigitImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotDigitImpl implements _NotDigit {
  const _$NotDigitImpl();

  @override
  String toString() {
    return 'ValidationError.notDigit()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NotDigitImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return notDigit();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return notDigit?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notDigit != null) {
      return notDigit();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return notDigit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return notDigit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notDigit != null) {
      return notDigit(this);
    }
    return orElse();
  }
}

abstract class _NotDigit implements ValidationError {
  const factory _NotDigit() = _$NotDigitImpl;
}

/// @nodoc
abstract class _$$NotHalfWidthUpperImplCopyWith<$Res> {
  factory _$$NotHalfWidthUpperImplCopyWith(_$NotHalfWidthUpperImpl value,
          $Res Function(_$NotHalfWidthUpperImpl) then) =
      __$$NotHalfWidthUpperImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotHalfWidthUpperImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NotHalfWidthUpperImpl>
    implements _$$NotHalfWidthUpperImplCopyWith<$Res> {
  __$$NotHalfWidthUpperImplCopyWithImpl(_$NotHalfWidthUpperImpl _value,
      $Res Function(_$NotHalfWidthUpperImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotHalfWidthUpperImpl implements _NotHalfWidthUpper {
  const _$NotHalfWidthUpperImpl();

  @override
  String toString() {
    return 'ValidationError.notHalfWidthUpper()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NotHalfWidthUpperImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthUpper();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthUpper?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notHalfWidthUpper != null) {
      return notHalfWidthUpper();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthUpper(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthUpper?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notHalfWidthUpper != null) {
      return notHalfWidthUpper(this);
    }
    return orElse();
  }
}

abstract class _NotHalfWidthUpper implements ValidationError {
  const factory _NotHalfWidthUpper() = _$NotHalfWidthUpperImpl;
}

/// @nodoc
abstract class _$$NotHalfWidthSymbolImplCopyWith<$Res> {
  factory _$$NotHalfWidthSymbolImplCopyWith(_$NotHalfWidthSymbolImpl value,
          $Res Function(_$NotHalfWidthSymbolImpl) then) =
      __$$NotHalfWidthSymbolImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotHalfWidthSymbolImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NotHalfWidthSymbolImpl>
    implements _$$NotHalfWidthSymbolImplCopyWith<$Res> {
  __$$NotHalfWidthSymbolImplCopyWithImpl(_$NotHalfWidthSymbolImpl _value,
      $Res Function(_$NotHalfWidthSymbolImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotHalfWidthSymbolImpl implements _NotHalfWidthSymbol {
  const _$NotHalfWidthSymbolImpl();

  @override
  String toString() {
    return 'ValidationError.notHalfWidthSymbol()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NotHalfWidthSymbolImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthSymbol();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthSymbol?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notHalfWidthSymbol != null) {
      return notHalfWidthSymbol();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthSymbol(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return notHalfWidthSymbol?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notHalfWidthSymbol != null) {
      return notHalfWidthSymbol(this);
    }
    return orElse();
  }
}

abstract class _NotHalfWidthSymbol implements ValidationError {
  const factory _NotHalfWidthSymbol() = _$NotHalfWidthSymbolImpl;
}

/// @nodoc
abstract class _$$NotZenkakuKatakanaImplCopyWith<$Res> {
  factory _$$NotZenkakuKatakanaImplCopyWith(_$NotZenkakuKatakanaImpl value,
          $Res Function(_$NotZenkakuKatakanaImpl) then) =
      __$$NotZenkakuKatakanaImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotZenkakuKatakanaImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NotZenkakuKatakanaImpl>
    implements _$$NotZenkakuKatakanaImplCopyWith<$Res> {
  __$$NotZenkakuKatakanaImplCopyWithImpl(_$NotZenkakuKatakanaImpl _value,
      $Res Function(_$NotZenkakuKatakanaImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotZenkakuKatakanaImpl implements _NotZenkakuKatakana {
  const _$NotZenkakuKatakanaImpl();

  @override
  String toString() {
    return 'ValidationError.notZenkakuKatakana()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NotZenkakuKatakanaImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return notZenkakuKatakana();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return notZenkakuKatakana?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notZenkakuKatakana != null) {
      return notZenkakuKatakana();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return notZenkakuKatakana(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return notZenkakuKatakana?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notZenkakuKatakana != null) {
      return notZenkakuKatakana(this);
    }
    return orElse();
  }
}

abstract class _NotZenkakuKatakana implements ValidationError {
  const factory _NotZenkakuKatakana() = _$NotZenkakuKatakanaImpl;
}

/// @nodoc
abstract class _$$NotZenkakuImplCopyWith<$Res> {
  factory _$$NotZenkakuImplCopyWith(
          _$NotZenkakuImpl value, $Res Function(_$NotZenkakuImpl) then) =
      __$$NotZenkakuImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotZenkakuImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NotZenkakuImpl>
    implements _$$NotZenkakuImplCopyWith<$Res> {
  __$$NotZenkakuImplCopyWithImpl(
      _$NotZenkakuImpl _value, $Res Function(_$NotZenkakuImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotZenkakuImpl implements _NotZenkaku {
  const _$NotZenkakuImpl();

  @override
  String toString() {
    return 'ValidationError.notZenkaku()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NotZenkakuImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return notZenkaku();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return notZenkaku?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notZenkaku != null) {
      return notZenkaku();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return notZenkaku(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return notZenkaku?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notZenkaku != null) {
      return notZenkaku(this);
    }
    return orElse();
  }
}

abstract class _NotZenkaku implements ValidationError {
  const factory _NotZenkaku() = _$NotZenkakuImpl;
}

/// @nodoc
abstract class _$$NotMixedHalfWidthAlphabetAndNumberImplCopyWith<$Res> {
  factory _$$NotMixedHalfWidthAlphabetAndNumberImplCopyWith(
          _$NotMixedHalfWidthAlphabetAndNumberImpl value,
          $Res Function(_$NotMixedHalfWidthAlphabetAndNumberImpl) then) =
      __$$NotMixedHalfWidthAlphabetAndNumberImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NotMixedHalfWidthAlphabetAndNumberImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res,
        _$NotMixedHalfWidthAlphabetAndNumberImpl>
    implements _$$NotMixedHalfWidthAlphabetAndNumberImplCopyWith<$Res> {
  __$$NotMixedHalfWidthAlphabetAndNumberImplCopyWithImpl(
      _$NotMixedHalfWidthAlphabetAndNumberImpl _value,
      $Res Function(_$NotMixedHalfWidthAlphabetAndNumberImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NotMixedHalfWidthAlphabetAndNumberImpl
    implements _NotMixedHalfWidthAlphabetAndNumber {
  const _$NotMixedHalfWidthAlphabetAndNumberImpl();

  @override
  String toString() {
    return 'ValidationError.notMixedHalfWidthAlphabetAndNumber()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotMixedHalfWidthAlphabetAndNumberImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return notMixedHalfWidthAlphabetAndNumber();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return notMixedHalfWidthAlphabetAndNumber?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notMixedHalfWidthAlphabetAndNumber != null) {
      return notMixedHalfWidthAlphabetAndNumber();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return notMixedHalfWidthAlphabetAndNumber(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return notMixedHalfWidthAlphabetAndNumber?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (notMixedHalfWidthAlphabetAndNumber != null) {
      return notMixedHalfWidthAlphabetAndNumber(this);
    }
    return orElse();
  }
}

abstract class _NotMixedHalfWidthAlphabetAndNumber implements ValidationError {
  const factory _NotMixedHalfWidthAlphabetAndNumber() =
      _$NotMixedHalfWidthAlphabetAndNumberImpl;
}

/// @nodoc
abstract class _$$PasswordEqualsImplCopyWith<$Res> {
  factory _$$PasswordEqualsImplCopyWith(_$PasswordEqualsImpl value,
          $Res Function(_$PasswordEqualsImpl) then) =
      __$$PasswordEqualsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PasswordEqualsImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$PasswordEqualsImpl>
    implements _$$PasswordEqualsImplCopyWith<$Res> {
  __$$PasswordEqualsImplCopyWithImpl(
      _$PasswordEqualsImpl _value, $Res Function(_$PasswordEqualsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PasswordEqualsImpl implements _PasswordEquals {
  const _$PasswordEqualsImpl();

  @override
  String toString() {
    return 'ValidationError.passwordEquals()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$PasswordEqualsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return passwordEquals();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return passwordEquals?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (passwordEquals != null) {
      return passwordEquals();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return passwordEquals(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return passwordEquals?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (passwordEquals != null) {
      return passwordEquals(this);
    }
    return orElse();
  }
}

abstract class _PasswordEquals implements ValidationError {
  const factory _PasswordEquals() = _$PasswordEqualsImpl;
}

/// @nodoc
abstract class _$$NoneVdIdErrorImplCopyWith<$Res> {
  factory _$$NoneVdIdErrorImplCopyWith(
          _$NoneVdIdErrorImpl value, $Res Function(_$NoneVdIdErrorImpl) then) =
      __$$NoneVdIdErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoneVdIdErrorImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NoneVdIdErrorImpl>
    implements _$$NoneVdIdErrorImplCopyWith<$Res> {
  __$$NoneVdIdErrorImplCopyWithImpl(
      _$NoneVdIdErrorImpl _value, $Res Function(_$NoneVdIdErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoneVdIdErrorImpl implements _NoneVdIdError {
  const _$NoneVdIdErrorImpl();

  @override
  String toString() {
    return 'ValidationError.noneVdIdError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoneVdIdErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return noneVdIdError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return noneVdIdError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (noneVdIdError != null) {
      return noneVdIdError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return noneVdIdError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return noneVdIdError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (noneVdIdError != null) {
      return noneVdIdError(this);
    }
    return orElse();
  }
}

abstract class _NoneVdIdError implements ValidationError {
  const factory _NoneVdIdError() = _$NoneVdIdErrorImpl;
}

/// @nodoc
abstract class _$$NonePasswordErrorImplCopyWith<$Res> {
  factory _$$NonePasswordErrorImplCopyWith(_$NonePasswordErrorImpl value,
          $Res Function(_$NonePasswordErrorImpl) then) =
      __$$NonePasswordErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NonePasswordErrorImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NonePasswordErrorImpl>
    implements _$$NonePasswordErrorImplCopyWith<$Res> {
  __$$NonePasswordErrorImplCopyWithImpl(_$NonePasswordErrorImpl _value,
      $Res Function(_$NonePasswordErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NonePasswordErrorImpl implements _NonePasswordError {
  const _$NonePasswordErrorImpl();

  @override
  String toString() {
    return 'ValidationError.nonePasswordError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NonePasswordErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return nonePasswordError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return nonePasswordError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (nonePasswordError != null) {
      return nonePasswordError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return nonePasswordError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return nonePasswordError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (nonePasswordError != null) {
      return nonePasswordError(this);
    }
    return orElse();
  }
}

abstract class _NonePasswordError implements ValidationError {
  const factory _NonePasswordError() = _$NonePasswordErrorImpl;
}

/// @nodoc
abstract class _$$IsVdTypeErrorImplCopyWith<$Res> {
  factory _$$IsVdTypeErrorImplCopyWith(
          _$IsVdTypeErrorImpl value, $Res Function(_$IsVdTypeErrorImpl) then) =
      __$$IsVdTypeErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IsVdTypeErrorImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$IsVdTypeErrorImpl>
    implements _$$IsVdTypeErrorImplCopyWith<$Res> {
  __$$IsVdTypeErrorImplCopyWithImpl(
      _$IsVdTypeErrorImpl _value, $Res Function(_$IsVdTypeErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IsVdTypeErrorImpl implements _IsVdTypeError {
  const _$IsVdTypeErrorImpl();

  @override
  String toString() {
    return 'ValidationError.isVdTypeError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$IsVdTypeErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return isVdTypeError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return isVdTypeError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isVdTypeError != null) {
      return isVdTypeError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return isVdTypeError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return isVdTypeError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isVdTypeError != null) {
      return isVdTypeError(this);
    }
    return orElse();
  }
}

abstract class _IsVdTypeError implements ValidationError {
  const factory _IsVdTypeError() = _$IsVdTypeErrorImpl;
}

/// @nodoc
abstract class _$$NoneDtpIDErrorImplCopyWith<$Res> {
  factory _$$NoneDtpIDErrorImplCopyWith(_$NoneDtpIDErrorImpl value,
          $Res Function(_$NoneDtpIDErrorImpl) then) =
      __$$NoneDtpIDErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NoneDtpIDErrorImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$NoneDtpIDErrorImpl>
    implements _$$NoneDtpIDErrorImplCopyWith<$Res> {
  __$$NoneDtpIDErrorImplCopyWithImpl(
      _$NoneDtpIDErrorImpl _value, $Res Function(_$NoneDtpIDErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NoneDtpIDErrorImpl implements _NoneDtpIDError {
  const _$NoneDtpIDErrorImpl();

  @override
  String toString() {
    return 'ValidationError.noneDtpIDError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$NoneDtpIDErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return noneDtpIDError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return noneDtpIDError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (noneDtpIDError != null) {
      return noneDtpIDError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return noneDtpIDError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return noneDtpIDError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (noneDtpIDError != null) {
      return noneDtpIDError(this);
    }
    return orElse();
  }
}

abstract class _NoneDtpIDError implements ValidationError {
  const factory _NoneDtpIDError() = _$NoneDtpIDErrorImpl;
}

/// @nodoc
abstract class _$$IsDtpTypeErrorImplCopyWith<$Res> {
  factory _$$IsDtpTypeErrorImplCopyWith(_$IsDtpTypeErrorImpl value,
          $Res Function(_$IsDtpTypeErrorImpl) then) =
      __$$IsDtpTypeErrorImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IsDtpTypeErrorImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$IsDtpTypeErrorImpl>
    implements _$$IsDtpTypeErrorImplCopyWith<$Res> {
  __$$IsDtpTypeErrorImplCopyWithImpl(
      _$IsDtpTypeErrorImpl _value, $Res Function(_$IsDtpTypeErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IsDtpTypeErrorImpl implements _IsDtpTypeError {
  const _$IsDtpTypeErrorImpl();

  @override
  String toString() {
    return 'ValidationError.isDtpTypeError()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$IsDtpTypeErrorImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return isDtpTypeError();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return isDtpTypeError?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isDtpTypeError != null) {
      return isDtpTypeError();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return isDtpTypeError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return isDtpTypeError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isDtpTypeError != null) {
      return isDtpTypeError(this);
    }
    return orElse();
  }
}

abstract class _IsDtpTypeError implements ValidationError {
  const factory _IsDtpTypeError() = _$IsDtpTypeErrorImpl;
}

/// @nodoc
abstract class _$$IsDtpIdAndPasswordBrankImplCopyWith<$Res> {
  factory _$$IsDtpIdAndPasswordBrankImplCopyWith(
          _$IsDtpIdAndPasswordBrankImpl value,
          $Res Function(_$IsDtpIdAndPasswordBrankImpl) then) =
      __$$IsDtpIdAndPasswordBrankImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IsDtpIdAndPasswordBrankImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$IsDtpIdAndPasswordBrankImpl>
    implements _$$IsDtpIdAndPasswordBrankImplCopyWith<$Res> {
  __$$IsDtpIdAndPasswordBrankImplCopyWithImpl(
      _$IsDtpIdAndPasswordBrankImpl _value,
      $Res Function(_$IsDtpIdAndPasswordBrankImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IsDtpIdAndPasswordBrankImpl implements _IsDtpIdAndPasswordBrank {
  const _$IsDtpIdAndPasswordBrankImpl();

  @override
  String toString() {
    return 'ValidationError.isDtpIdAndPasswordBrank()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IsDtpIdAndPasswordBrankImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return isDtpIdAndPasswordBrank();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return isDtpIdAndPasswordBrank?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isDtpIdAndPasswordBrank != null) {
      return isDtpIdAndPasswordBrank();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return isDtpIdAndPasswordBrank(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return isDtpIdAndPasswordBrank?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isDtpIdAndPasswordBrank != null) {
      return isDtpIdAndPasswordBrank(this);
    }
    return orElse();
  }
}

abstract class _IsDtpIdAndPasswordBrank implements ValidationError {
  const factory _IsDtpIdAndPasswordBrank() = _$IsDtpIdAndPasswordBrankImpl;
}

/// @nodoc
abstract class _$$IsVdIdAndPasswordBrankImplCopyWith<$Res> {
  factory _$$IsVdIdAndPasswordBrankImplCopyWith(
          _$IsVdIdAndPasswordBrankImpl value,
          $Res Function(_$IsVdIdAndPasswordBrankImpl) then) =
      __$$IsVdIdAndPasswordBrankImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$IsVdIdAndPasswordBrankImplCopyWithImpl<$Res>
    extends _$ValidationErrorCopyWithImpl<$Res, _$IsVdIdAndPasswordBrankImpl>
    implements _$$IsVdIdAndPasswordBrankImplCopyWith<$Res> {
  __$$IsVdIdAndPasswordBrankImplCopyWithImpl(
      _$IsVdIdAndPasswordBrankImpl _value,
      $Res Function(_$IsVdIdAndPasswordBrankImpl) _then)
      : super(_value, _then);

  /// Create a copy of ValidationError
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$IsVdIdAndPasswordBrankImpl implements _IsVdIdAndPasswordBrank {
  const _$IsVdIdAndPasswordBrankImpl();

  @override
  String toString() {
    return 'ValidationError.isVdIdAndPasswordBrank()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IsVdIdAndPasswordBrankImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() none,
    required TResult Function() disabled,
    required TResult Function(RequiredStyle style) required,
    required TResult Function(int maxLength) maxLength,
    required TResult Function(int maxLength) sumMaxLength,
    required TResult Function(int minLength) minLength,
    required TResult Function(int length) lengthEquals,
    required TResult Function(int minLength, int maxLength)
        referenceNumberLengthEquals,
    required TResult Function(int minLength, int maxLength) lengthRange,
    required TResult Function() invalidAddress,
    required TResult Function() invalidEmailDomain,
    required TResult Function() invalidEmailFormat,
    required TResult Function() invalidEmailCorrelation,
    required TResult Function() invalidMobileNumberFormat,
    required TResult Function() invalidPostalCodeFormat,
    required TResult Function() invalidDate,
    required TResult Function() futureDate,
    required TResult Function() notDigit,
    required TResult Function() notHalfWidthUpper,
    required TResult Function() notHalfWidthSymbol,
    required TResult Function() notZenkakuKatakana,
    required TResult Function() notZenkaku,
    required TResult Function() notMixedHalfWidthAlphabetAndNumber,
    required TResult Function() passwordEquals,
    required TResult Function() noneVdIdError,
    required TResult Function() nonePasswordError,
    required TResult Function() isVdTypeError,
    required TResult Function() noneDtpIDError,
    required TResult Function() isDtpTypeError,
    required TResult Function() isDtpIdAndPasswordBrank,
    required TResult Function() isVdIdAndPasswordBrank,
  }) {
    return isVdIdAndPasswordBrank();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? none,
    TResult? Function()? disabled,
    TResult? Function(RequiredStyle style)? required,
    TResult? Function(int maxLength)? maxLength,
    TResult? Function(int maxLength)? sumMaxLength,
    TResult? Function(int minLength)? minLength,
    TResult? Function(int length)? lengthEquals,
    TResult? Function(int minLength, int maxLength)?
        referenceNumberLengthEquals,
    TResult? Function(int minLength, int maxLength)? lengthRange,
    TResult? Function()? invalidAddress,
    TResult? Function()? invalidEmailDomain,
    TResult? Function()? invalidEmailFormat,
    TResult? Function()? invalidEmailCorrelation,
    TResult? Function()? invalidMobileNumberFormat,
    TResult? Function()? invalidPostalCodeFormat,
    TResult? Function()? invalidDate,
    TResult? Function()? futureDate,
    TResult? Function()? notDigit,
    TResult? Function()? notHalfWidthUpper,
    TResult? Function()? notHalfWidthSymbol,
    TResult? Function()? notZenkakuKatakana,
    TResult? Function()? notZenkaku,
    TResult? Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult? Function()? passwordEquals,
    TResult? Function()? noneVdIdError,
    TResult? Function()? nonePasswordError,
    TResult? Function()? isVdTypeError,
    TResult? Function()? noneDtpIDError,
    TResult? Function()? isDtpTypeError,
    TResult? Function()? isDtpIdAndPasswordBrank,
    TResult? Function()? isVdIdAndPasswordBrank,
  }) {
    return isVdIdAndPasswordBrank?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? none,
    TResult Function()? disabled,
    TResult Function(RequiredStyle style)? required,
    TResult Function(int maxLength)? maxLength,
    TResult Function(int maxLength)? sumMaxLength,
    TResult Function(int minLength)? minLength,
    TResult Function(int length)? lengthEquals,
    TResult Function(int minLength, int maxLength)? referenceNumberLengthEquals,
    TResult Function(int minLength, int maxLength)? lengthRange,
    TResult Function()? invalidAddress,
    TResult Function()? invalidEmailDomain,
    TResult Function()? invalidEmailFormat,
    TResult Function()? invalidEmailCorrelation,
    TResult Function()? invalidMobileNumberFormat,
    TResult Function()? invalidPostalCodeFormat,
    TResult Function()? invalidDate,
    TResult Function()? futureDate,
    TResult Function()? notDigit,
    TResult Function()? notHalfWidthUpper,
    TResult Function()? notHalfWidthSymbol,
    TResult Function()? notZenkakuKatakana,
    TResult Function()? notZenkaku,
    TResult Function()? notMixedHalfWidthAlphabetAndNumber,
    TResult Function()? passwordEquals,
    TResult Function()? noneVdIdError,
    TResult Function()? nonePasswordError,
    TResult Function()? isVdTypeError,
    TResult Function()? noneDtpIDError,
    TResult Function()? isDtpTypeError,
    TResult Function()? isDtpIdAndPasswordBrank,
    TResult Function()? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isVdIdAndPasswordBrank != null) {
      return isVdIdAndPasswordBrank();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_None value) none,
    required TResult Function(_Disabled value) disabled,
    required TResult Function(_Required value) required,
    required TResult Function(_MaxLength value) maxLength,
    required TResult Function(_sumMaxLength value) sumMaxLength,
    required TResult Function(_MinLength value) minLength,
    required TResult Function(_LengthEquals value) lengthEquals,
    required TResult Function(_ReferenceNumberLengthEquals value)
        referenceNumberLengthEquals,
    required TResult Function(_LengthRange value) lengthRange,
    required TResult Function(_InvalidAddress value) invalidAddress,
    required TResult Function(_InvalidEmailDomain value) invalidEmailDomain,
    required TResult Function(_InvalidEmailFormat value) invalidEmailFormat,
    required TResult Function(_InvalidEmailCorrelation value)
        invalidEmailCorrelation,
    required TResult Function(_InvalidMobileNumberFormat value)
        invalidMobileNumberFormat,
    required TResult Function(_InvalidPostalCodeFormat value)
        invalidPostalCodeFormat,
    required TResult Function(_InvalidDate value) invalidDate,
    required TResult Function(_futureDate value) futureDate,
    required TResult Function(_NotDigit value) notDigit,
    required TResult Function(_NotHalfWidthUpper value) notHalfWidthUpper,
    required TResult Function(_NotHalfWidthSymbol value) notHalfWidthSymbol,
    required TResult Function(_NotZenkakuKatakana value) notZenkakuKatakana,
    required TResult Function(_NotZenkaku value) notZenkaku,
    required TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)
        notMixedHalfWidthAlphabetAndNumber,
    required TResult Function(_PasswordEquals value) passwordEquals,
    required TResult Function(_NoneVdIdError value) noneVdIdError,
    required TResult Function(_NonePasswordError value) nonePasswordError,
    required TResult Function(_IsVdTypeError value) isVdTypeError,
    required TResult Function(_NoneDtpIDError value) noneDtpIDError,
    required TResult Function(_IsDtpTypeError value) isDtpTypeError,
    required TResult Function(_IsDtpIdAndPasswordBrank value)
        isDtpIdAndPasswordBrank,
    required TResult Function(_IsVdIdAndPasswordBrank value)
        isVdIdAndPasswordBrank,
  }) {
    return isVdIdAndPasswordBrank(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_None value)? none,
    TResult? Function(_Disabled value)? disabled,
    TResult? Function(_Required value)? required,
    TResult? Function(_MaxLength value)? maxLength,
    TResult? Function(_sumMaxLength value)? sumMaxLength,
    TResult? Function(_MinLength value)? minLength,
    TResult? Function(_LengthEquals value)? lengthEquals,
    TResult? Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult? Function(_LengthRange value)? lengthRange,
    TResult? Function(_InvalidAddress value)? invalidAddress,
    TResult? Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult? Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult? Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult? Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult? Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult? Function(_InvalidDate value)? invalidDate,
    TResult? Function(_futureDate value)? futureDate,
    TResult? Function(_NotDigit value)? notDigit,
    TResult? Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult? Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult? Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult? Function(_NotZenkaku value)? notZenkaku,
    TResult? Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult? Function(_PasswordEquals value)? passwordEquals,
    TResult? Function(_NoneVdIdError value)? noneVdIdError,
    TResult? Function(_NonePasswordError value)? nonePasswordError,
    TResult? Function(_IsVdTypeError value)? isVdTypeError,
    TResult? Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult? Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult? Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult? Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
  }) {
    return isVdIdAndPasswordBrank?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_None value)? none,
    TResult Function(_Disabled value)? disabled,
    TResult Function(_Required value)? required,
    TResult Function(_MaxLength value)? maxLength,
    TResult Function(_sumMaxLength value)? sumMaxLength,
    TResult Function(_MinLength value)? minLength,
    TResult Function(_LengthEquals value)? lengthEquals,
    TResult Function(_ReferenceNumberLengthEquals value)?
        referenceNumberLengthEquals,
    TResult Function(_LengthRange value)? lengthRange,
    TResult Function(_InvalidAddress value)? invalidAddress,
    TResult Function(_InvalidEmailDomain value)? invalidEmailDomain,
    TResult Function(_InvalidEmailFormat value)? invalidEmailFormat,
    TResult Function(_InvalidEmailCorrelation value)? invalidEmailCorrelation,
    TResult Function(_InvalidMobileNumberFormat value)?
        invalidMobileNumberFormat,
    TResult Function(_InvalidPostalCodeFormat value)? invalidPostalCodeFormat,
    TResult Function(_InvalidDate value)? invalidDate,
    TResult Function(_futureDate value)? futureDate,
    TResult Function(_NotDigit value)? notDigit,
    TResult Function(_NotHalfWidthUpper value)? notHalfWidthUpper,
    TResult Function(_NotHalfWidthSymbol value)? notHalfWidthSymbol,
    TResult Function(_NotZenkakuKatakana value)? notZenkakuKatakana,
    TResult Function(_NotZenkaku value)? notZenkaku,
    TResult Function(_NotMixedHalfWidthAlphabetAndNumber value)?
        notMixedHalfWidthAlphabetAndNumber,
    TResult Function(_PasswordEquals value)? passwordEquals,
    TResult Function(_NoneVdIdError value)? noneVdIdError,
    TResult Function(_NonePasswordError value)? nonePasswordError,
    TResult Function(_IsVdTypeError value)? isVdTypeError,
    TResult Function(_NoneDtpIDError value)? noneDtpIDError,
    TResult Function(_IsDtpTypeError value)? isDtpTypeError,
    TResult Function(_IsDtpIdAndPasswordBrank value)? isDtpIdAndPasswordBrank,
    TResult Function(_IsVdIdAndPasswordBrank value)? isVdIdAndPasswordBrank,
    required TResult orElse(),
  }) {
    if (isVdIdAndPasswordBrank != null) {
      return isVdIdAndPasswordBrank(this);
    }
    return orElse();
  }
}

abstract class _IsVdIdAndPasswordBrank implements ValidationError {
  const factory _IsVdIdAndPasswordBrank() = _$IsVdIdAndPasswordBrankImpl;
}
