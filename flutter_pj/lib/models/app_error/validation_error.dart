import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'validation_error.freezed.dart';

abstract interface class ValidationOK {}

enum RequiredStyle {
  /// 入力
  input,

  /// 選択
  select,
}

/// バリデーションチェックエラー情報
@freezed
sealed class ValidationError with _$ValidationError {
  /// 入力値に問題がないフィールド
  @Implements<ValidationOK>()
  const factory ValidationError.none() = _None;

  /// チェック不要なフィールド
  @Implements<ValidationOK>()
  const factory ValidationError.disabled() = _Disabled;

  /// 必須項目であるのに入力されていないフィールド
  const factory ValidationError.required({
    @Default(RequiredStyle.input) RequiredStyle style,
  }) = _Required;

  /// 文字列の長さが長すぎるフィールド
  const factory ValidationError.maxLength(int maxLength) = _MaxLength;

  /// 文字列の長さが長すぎるフィールド(合計桁数)
  const factory ValidationError.sumMaxLength(int maxLength) = _sumMaxLength;

  /// 文字列の長さが短すぎるフィールド
  const factory ValidationError.minLength(int minLength) = _MinLength;

  /// 特定の桁数でない場合のエラー
  const factory ValidationError.lengthEquals(int length) = _LengthEquals;

  /// 特定の文字種別・桁数でない場合のエラー
  const factory ValidationError.referenceNumberLengthEquals(
    int minLength,
    int maxLength,
  ) = _ReferenceNumberLengthEquals;

  /// 文字列の長さが範囲外であるフィールド
  const factory ValidationError.lengthRange(
    int minLength,
    int maxLength,
  ) = _LengthRange;

  /// 無効な住所エラー
  const factory ValidationError.invalidAddress() = _InvalidAddress;

  ///メールアドレスドメインエラー
  const factory ValidationError.invalidEmailDomain() = _InvalidEmailDomain;

  /// メール形式エラー
  const factory ValidationError.invalidEmailFormat() = _InvalidEmailFormat;

  /// メール相関（不一致）エラー
  const factory ValidationError.invalidEmailCorrelation() =
      _InvalidEmailCorrelation;

  /// 携帯電話番号形式エラー
  const factory ValidationError.invalidMobileNumberFormat() =
      _InvalidMobileNumberFormat;

  /// 不正な郵便番号エラー
  const factory ValidationError.invalidPostalCodeFormat() =
      _InvalidPostalCodeFormat;

  /// 日付エラー
  const factory ValidationError.invalidDate() = _InvalidDate;

  /// 日付エラー
  const factory ValidationError.futureDate() = _futureDate;

  /// 数値以外を使用しているエラー
  const factory ValidationError.notDigit() = _NotDigit;

  /// 半角英大文字以外を使用しているエラー
  const factory ValidationError.notHalfWidthUpper() = _NotHalfWidthUpper;

  /// 半角英数記号以外を使用しているエラー
  const factory ValidationError.notHalfWidthSymbol() = _NotHalfWidthSymbol;

  /// 全角カタカナ以外を使用しているエラー
  const factory ValidationError.notZenkakuKatakana() = _NotZenkakuKatakana;

  /// 全角以外を使用しているエラー
  const factory ValidationError.notZenkaku() = _NotZenkaku;

  /// 半角英数字が混在していないエラー
  const factory ValidationError.notMixedHalfWidthAlphabetAndNumber() =
      _NotMixedHalfWidthAlphabetAndNumber;

  /// パスワードが一致していないエラー
  const factory ValidationError.passwordEquals() = _PasswordEquals;

  ///VDID(login時)が存在しない場合のエラー
  const factory ValidationError.noneVdIdError() = _NoneVdIdError;

  ///Password(login時)が存在しない場合のエラー
  const factory ValidationError.nonePasswordError() = _NonePasswordError;

  ///VDID(login時)の桁数や型が合っていない場合のエラー
  const factory ValidationError.isVdTypeError() = _IsVdTypeError;

  ///DTPID(login時)が存在しない場合のエラー
  const factory ValidationError.noneDtpIDError() = _NoneDtpIDError;

  ///DTPID(login時)の桁数や型が合っていない場合のエラー
  const factory ValidationError.isDtpTypeError() = _IsDtpTypeError;

  ///DTPIDとPassword(login時)の両方が空の場合のエラー
  const factory ValidationError.isDtpIdAndPasswordBrank() =
      _IsDtpIdAndPasswordBrank;

  ///VDIDとPassword(login時)の両方が空の場合のエラー
  const factory ValidationError.isVdIdAndPasswordBrank() =
      _IsVdIdAndPasswordBrank;
}

extension ValidationErrorExt on ValidationError {
  /// エラーがあるかどうか
  bool get hasError => this is! ValidationOK;

  /// エラーメッセージに変換する
  String? toErrorMessage(BuildContext context, String fieldName) {
    return maybeWhen(
      required: (style) => switch (style) {
        RequiredStyle.input =>
          fieldName + AppLocalizations.of(context)!.requiredError,
        RequiredStyle.select =>
          fieldName + AppLocalizations.of(context)!.requiredErrorForButton,
      },
      invalidEmailFormat: () =>
          fieldName + AppLocalizations.of(context)!.emailStructureError,
      invalidEmailCorrelation: () =>
          AppLocalizations.of(context)!.emailCorrelationError,
      invalidMobileNumberFormat: () =>
          AppLocalizations.of(context)!.mobileNumberStructureError,
      invalidDate: () => fieldName + AppLocalizations.of(context)!.dateError,
      futureDate: () => AppLocalizations.of(context)!.enterValidDOB,
      invalidEmailDomain: () => AppLocalizations.of(context)!.emailDomainError,
      maxLength: (max) =>
          fieldName +
          AppLocalizations.of(context)!.alphabetDigitErrorFirstHalf +
          max.toString() +
          AppLocalizations.of(context)!.alphabetDigitErrorSecondHalf,
      sumMaxLength: (max) =>
          AppLocalizations.of(context)!.sumMaxAddressLength +
          AppLocalizations.of(context)!.alphabetDigitErrorFirstHalf +
          max.toString() +
          AppLocalizations.of(context)!.alphabetDigitErrorSecondHalf,
      lengthEquals: (length) =>
          AppLocalizations.of(context)!.digitError(length, fieldName),
      referenceNumberLengthEquals: (minLength, maxLength) =>
          AppLocalizations.of(context)!
              .referenceNumberDigitError(maxLength, minLength),
      notDigit: () =>
          fieldName +
          AppLocalizations.of(context)!.validationErrorHalfWidthNumber,
      notHalfWidthUpper: () =>
          fieldName +
          AppLocalizations.of(context)!
              .validationErrorHalfWidthAlphanumericUpper,
      notHalfWidthSymbol: () =>
          fieldName +
          AppLocalizations.of(context)!
              .validationErrorHalfWidthAlphanumericWithSymbol,
      notZenkakuKatakana: () =>
          fieldName +
          AppLocalizations.of(context)!.validationErrorFullWidthKatakana,
      notZenkaku: () =>
          fieldName +
          AppLocalizations.of(context)!.validationErrorFullWidthKana,
      invalidPostalCodeFormat: () => AppLocalizations.of(context)!.addressError,
      notMixedHalfWidthAlphabetAndNumber: () =>
          fieldName +
          AppLocalizations.of(context)!.validationErrorMixedAlphanumeric,
      passwordEquals: () =>
          AppLocalizations.of(context)!.validationErrorPasswordEquals,
      orElse: () => null,
      noneVdIdError: () =>
          AppLocalizations.of(context)!.validationErrorNoneVdIdError,
      nonePasswordError: () =>
          AppLocalizations.of(context)!.validationErrorNonePasswordError,
      isVdTypeError: () =>
          AppLocalizations.of(context)!.validationErrorIsVdTypeError,
      noneDtpIDError: () =>
          AppLocalizations.of(context)!.validationErrorNoneDtpIdError,
      isDtpTypeError: () =>
          AppLocalizations.of(context)!.validationErrorIsDtpTypeError,
      isDtpIdAndPasswordBrank: () =>
          AppLocalizations.of(context)!.validationErrorIsDtpIdAndPasswordBrank,
      isVdIdAndPasswordBrank: () =>
          AppLocalizations.of(context)!.validationErrorIsVdIdAndPasswordBrank,
    );
  }
}

extension ValidationErrorList on List<ValidationError> {
  /// エラーがある最初のエラー情報を取得する
  /// エラーがない場合は[ValidationError.none]を返す
  ValidationError get firstError => firstWhere(
        (error) => error.hasError,
        orElse: () => const ValidationError.none(),
      );
}
