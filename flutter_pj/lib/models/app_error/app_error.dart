import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_error.freezed.dart';

@freezed

/// HACK 現状エラー発生時にstateにAppError型のインスタンスを格納しているが
/// 想定しない挙動をしてしまうことが多々ある、その為エラーの通知はstateNotifier上の関数の戻り値として渡すのはどうか
class AppError with _$AppError {
  const factory AppError({
    Object? reason,
    String? code,
    String? message,
    String? baseDate,
    String? baseTime,
    String? details,
    @Default(0) int statusCode,
    String? error,
    String? errorDescription,
    String? httpCode,
    String? httpMessage,
    String? moreInformation,
    String? faqText,
    @Default(false) bool hasFaq,
    @Default(true) bool isShowOnDialog,
    // ダイアログ表示時にエラーを削除するメソッド
    void Function()? clearErrorOnShowDialog,
    // ダイアログのボタン文言
    // 特に指定がなければ OK を表示
    String? okButtonText,
    String? faqLink,
  }) = _AppError;
}

extension AppErrorExt on AppError {
  String get baseDateTime {
    if (baseDate.isNull || baseTime.isNull) {
      return 'ー';
    }
    return '$baseDate $baseTime';
  }

  bool get isSessionTimeOutError {
    return ErrorInfo.sessionTimeOutCodeList.contains(code);
  }

  /// マイページに遷移した際、ログアウトさせる対象のエラーであるかのフラグ
  bool get isUserInfoErrorLogout {
    return code == MyPageErrorInfo.failedToLoadUserInfoCode;
  }
}
