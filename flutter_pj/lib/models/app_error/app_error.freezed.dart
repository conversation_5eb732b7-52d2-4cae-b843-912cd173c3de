// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_error.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppError {
  Object? get reason => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String? get baseDate => throw _privateConstructorUsedError;
  String? get baseTime => throw _privateConstructorUsedError;
  String? get details => throw _privateConstructorUsedError;
  int get statusCode => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;
  String? get errorDescription => throw _privateConstructorUsedError;
  String? get httpCode => throw _privateConstructorUsedError;
  String? get httpMessage => throw _privateConstructorUsedError;
  String? get moreInformation => throw _privateConstructorUsedError;
  String? get faqText => throw _privateConstructorUsedError;
  bool get hasFaq => throw _privateConstructorUsedError;
  bool get isShowOnDialog =>
      throw _privateConstructorUsedError; // ダイアログ表示時にエラーを削除するメソッド
  void Function()? get clearErrorOnShowDialog =>
      throw _privateConstructorUsedError; // ダイアログのボタン文言
// 特に指定がなければ OK を表示
  String? get okButtonText => throw _privateConstructorUsedError;
  String? get faqLink => throw _privateConstructorUsedError;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppErrorCopyWith<AppError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppErrorCopyWith<$Res> {
  factory $AppErrorCopyWith(AppError value, $Res Function(AppError) then) =
      _$AppErrorCopyWithImpl<$Res, AppError>;
  @useResult
  $Res call(
      {Object? reason,
      String? code,
      String? message,
      String? baseDate,
      String? baseTime,
      String? details,
      int statusCode,
      String? error,
      String? errorDescription,
      String? httpCode,
      String? httpMessage,
      String? moreInformation,
      String? faqText,
      bool hasFaq,
      bool isShowOnDialog,
      void Function()? clearErrorOnShowDialog,
      String? okButtonText,
      String? faqLink});
}

/// @nodoc
class _$AppErrorCopyWithImpl<$Res, $Val extends AppError>
    implements $AppErrorCopyWith<$Res> {
  _$AppErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reason = freezed,
    Object? code = freezed,
    Object? message = freezed,
    Object? baseDate = freezed,
    Object? baseTime = freezed,
    Object? details = freezed,
    Object? statusCode = null,
    Object? error = freezed,
    Object? errorDescription = freezed,
    Object? httpCode = freezed,
    Object? httpMessage = freezed,
    Object? moreInformation = freezed,
    Object? faqText = freezed,
    Object? hasFaq = null,
    Object? isShowOnDialog = null,
    Object? clearErrorOnShowDialog = freezed,
    Object? okButtonText = freezed,
    Object? faqLink = freezed,
  }) {
    return _then(_value.copyWith(
      reason: freezed == reason ? _value.reason : reason,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      errorDescription: freezed == errorDescription
          ? _value.errorDescription
          : errorDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      httpCode: freezed == httpCode
          ? _value.httpCode
          : httpCode // ignore: cast_nullable_to_non_nullable
              as String?,
      httpMessage: freezed == httpMessage
          ? _value.httpMessage
          : httpMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      moreInformation: freezed == moreInformation
          ? _value.moreInformation
          : moreInformation // ignore: cast_nullable_to_non_nullable
              as String?,
      faqText: freezed == faqText
          ? _value.faqText
          : faqText // ignore: cast_nullable_to_non_nullable
              as String?,
      hasFaq: null == hasFaq
          ? _value.hasFaq
          : hasFaq // ignore: cast_nullable_to_non_nullable
              as bool,
      isShowOnDialog: null == isShowOnDialog
          ? _value.isShowOnDialog
          : isShowOnDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      clearErrorOnShowDialog: freezed == clearErrorOnShowDialog
          ? _value.clearErrorOnShowDialog
          : clearErrorOnShowDialog // ignore: cast_nullable_to_non_nullable
              as void Function()?,
      okButtonText: freezed == okButtonText
          ? _value.okButtonText
          : okButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      faqLink: freezed == faqLink
          ? _value.faqLink
          : faqLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppErrorImplCopyWith<$Res>
    implements $AppErrorCopyWith<$Res> {
  factory _$$AppErrorImplCopyWith(
          _$AppErrorImpl value, $Res Function(_$AppErrorImpl) then) =
      __$$AppErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Object? reason,
      String? code,
      String? message,
      String? baseDate,
      String? baseTime,
      String? details,
      int statusCode,
      String? error,
      String? errorDescription,
      String? httpCode,
      String? httpMessage,
      String? moreInformation,
      String? faqText,
      bool hasFaq,
      bool isShowOnDialog,
      void Function()? clearErrorOnShowDialog,
      String? okButtonText,
      String? faqLink});
}

/// @nodoc
class __$$AppErrorImplCopyWithImpl<$Res>
    extends _$AppErrorCopyWithImpl<$Res, _$AppErrorImpl>
    implements _$$AppErrorImplCopyWith<$Res> {
  __$$AppErrorImplCopyWithImpl(
      _$AppErrorImpl _value, $Res Function(_$AppErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? reason = freezed,
    Object? code = freezed,
    Object? message = freezed,
    Object? baseDate = freezed,
    Object? baseTime = freezed,
    Object? details = freezed,
    Object? statusCode = null,
    Object? error = freezed,
    Object? errorDescription = freezed,
    Object? httpCode = freezed,
    Object? httpMessage = freezed,
    Object? moreInformation = freezed,
    Object? faqText = freezed,
    Object? hasFaq = null,
    Object? isShowOnDialog = null,
    Object? clearErrorOnShowDialog = freezed,
    Object? okButtonText = freezed,
    Object? faqLink = freezed,
  }) {
    return _then(_$AppErrorImpl(
      reason: freezed == reason ? _value.reason : reason,
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: null == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      errorDescription: freezed == errorDescription
          ? _value.errorDescription
          : errorDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      httpCode: freezed == httpCode
          ? _value.httpCode
          : httpCode // ignore: cast_nullable_to_non_nullable
              as String?,
      httpMessage: freezed == httpMessage
          ? _value.httpMessage
          : httpMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      moreInformation: freezed == moreInformation
          ? _value.moreInformation
          : moreInformation // ignore: cast_nullable_to_non_nullable
              as String?,
      faqText: freezed == faqText
          ? _value.faqText
          : faqText // ignore: cast_nullable_to_non_nullable
              as String?,
      hasFaq: null == hasFaq
          ? _value.hasFaq
          : hasFaq // ignore: cast_nullable_to_non_nullable
              as bool,
      isShowOnDialog: null == isShowOnDialog
          ? _value.isShowOnDialog
          : isShowOnDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      clearErrorOnShowDialog: freezed == clearErrorOnShowDialog
          ? _value.clearErrorOnShowDialog
          : clearErrorOnShowDialog // ignore: cast_nullable_to_non_nullable
              as void Function()?,
      okButtonText: freezed == okButtonText
          ? _value.okButtonText
          : okButtonText // ignore: cast_nullable_to_non_nullable
              as String?,
      faqLink: freezed == faqLink
          ? _value.faqLink
          : faqLink // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AppErrorImpl implements _AppError {
  const _$AppErrorImpl(
      {this.reason,
      this.code,
      this.message,
      this.baseDate,
      this.baseTime,
      this.details,
      this.statusCode = 0,
      this.error,
      this.errorDescription,
      this.httpCode,
      this.httpMessage,
      this.moreInformation,
      this.faqText,
      this.hasFaq = false,
      this.isShowOnDialog = true,
      this.clearErrorOnShowDialog,
      this.okButtonText,
      this.faqLink});

  @override
  final Object? reason;
  @override
  final String? code;
  @override
  final String? message;
  @override
  final String? baseDate;
  @override
  final String? baseTime;
  @override
  final String? details;
  @override
  @JsonKey()
  final int statusCode;
  @override
  final String? error;
  @override
  final String? errorDescription;
  @override
  final String? httpCode;
  @override
  final String? httpMessage;
  @override
  final String? moreInformation;
  @override
  final String? faqText;
  @override
  @JsonKey()
  final bool hasFaq;
  @override
  @JsonKey()
  final bool isShowOnDialog;
// ダイアログ表示時にエラーを削除するメソッド
  @override
  final void Function()? clearErrorOnShowDialog;
// ダイアログのボタン文言
// 特に指定がなければ OK を表示
  @override
  final String? okButtonText;
  @override
  final String? faqLink;

  @override
  String toString() {
    return 'AppError(reason: $reason, code: $code, message: $message, baseDate: $baseDate, baseTime: $baseTime, details: $details, statusCode: $statusCode, error: $error, errorDescription: $errorDescription, httpCode: $httpCode, httpMessage: $httpMessage, moreInformation: $moreInformation, faqText: $faqText, hasFaq: $hasFaq, isShowOnDialog: $isShowOnDialog, clearErrorOnShowDialog: $clearErrorOnShowDialog, okButtonText: $okButtonText, faqLink: $faqLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppErrorImpl &&
            const DeepCollectionEquality().equals(other.reason, reason) &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.baseDate, baseDate) ||
                other.baseDate == baseDate) &&
            (identical(other.baseTime, baseTime) ||
                other.baseTime == baseTime) &&
            (identical(other.details, details) || other.details == details) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.errorDescription, errorDescription) ||
                other.errorDescription == errorDescription) &&
            (identical(other.httpCode, httpCode) ||
                other.httpCode == httpCode) &&
            (identical(other.httpMessage, httpMessage) ||
                other.httpMessage == httpMessage) &&
            (identical(other.moreInformation, moreInformation) ||
                other.moreInformation == moreInformation) &&
            (identical(other.faqText, faqText) || other.faqText == faqText) &&
            (identical(other.hasFaq, hasFaq) || other.hasFaq == hasFaq) &&
            (identical(other.isShowOnDialog, isShowOnDialog) ||
                other.isShowOnDialog == isShowOnDialog) &&
            (identical(other.clearErrorOnShowDialog, clearErrorOnShowDialog) ||
                other.clearErrorOnShowDialog == clearErrorOnShowDialog) &&
            (identical(other.okButtonText, okButtonText) ||
                other.okButtonText == okButtonText) &&
            (identical(other.faqLink, faqLink) || other.faqLink == faqLink));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(reason),
      code,
      message,
      baseDate,
      baseTime,
      details,
      statusCode,
      error,
      errorDescription,
      httpCode,
      httpMessage,
      moreInformation,
      faqText,
      hasFaq,
      isShowOnDialog,
      clearErrorOnShowDialog,
      okButtonText,
      faqLink);

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppErrorImplCopyWith<_$AppErrorImpl> get copyWith =>
      __$$AppErrorImplCopyWithImpl<_$AppErrorImpl>(this, _$identity);
}

abstract class _AppError implements AppError {
  const factory _AppError(
      {final Object? reason,
      final String? code,
      final String? message,
      final String? baseDate,
      final String? baseTime,
      final String? details,
      final int statusCode,
      final String? error,
      final String? errorDescription,
      final String? httpCode,
      final String? httpMessage,
      final String? moreInformation,
      final String? faqText,
      final bool hasFaq,
      final bool isShowOnDialog,
      final void Function()? clearErrorOnShowDialog,
      final String? okButtonText,
      final String? faqLink}) = _$AppErrorImpl;

  @override
  Object? get reason;
  @override
  String? get code;
  @override
  String? get message;
  @override
  String? get baseDate;
  @override
  String? get baseTime;
  @override
  String? get details;
  @override
  int get statusCode;
  @override
  String? get error;
  @override
  String? get errorDescription;
  @override
  String? get httpCode;
  @override
  String? get httpMessage;
  @override
  String? get moreInformation;
  @override
  String? get faqText;
  @override
  bool get hasFaq;
  @override
  bool get isShowOnDialog; // ダイアログ表示時にエラーを削除するメソッド
  @override
  void Function()? get clearErrorOnShowDialog; // ダイアログのボタン文言
// 特に指定がなければ OK を表示
  @override
  String? get okButtonText;
  @override
  String? get faqLink;

  /// Create a copy of AppError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppErrorImplCopyWith<_$AppErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
