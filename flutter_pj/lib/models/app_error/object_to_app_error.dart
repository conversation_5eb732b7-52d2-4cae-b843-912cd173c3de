import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/vdid_login_exception/vdid_login_exception.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/mynapocket_api_client.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:flutter/services.dart';

/// try-catchしたオブジェクトをAppErrorインスタンスに変換する
extension ObjectToAppError on Object? {
  /// convertPlatformExceptionはライブラリ等からの意図しないPlatformExceptionを
  /// 「それ以外の場合」の処理にするためのフラグです。
  AppError toAppError({final bool convertPlatformException = false}) {
    if (this is AppError) {
      // すでにAppErrorの場合はそのまま返す
      return this as AppError;
    }
    if (this is ApiClientException) {
      //特定のErrorCode(I016-00126)の場合かどうか判定
      //BEからI016-00126が返却された場合には、webと異なるメッセージをフロントで表示させるため
      final isErrorCodeCanNotLogin = (this as ApiClientException).code ==
          LoginErrorInfo.smbcBusinessIdLoginWithInvalidVdidErrorCode;
      // ApiClientExceptionの場合はAppErrorに変換して返す
      return AppError(
        reason: this,
        code: (this as ApiClientException).code,
        // dynamoにて改行コードが指定されている場合、クライアントにおいては表示崩れが発生する可能性が高いため改行しない
        message: isErrorCodeCanNotLogin
            ? ErrorInfo.defaultErrorMessage
            : (this as ApiClientException).message.replaceAll('<br>', ''),
        hasFaq: (this as ApiClientException).hasFaq,
        baseDate: (this as ApiClientException).baseDate,
        baseTime: (this as ApiClientException).baseTime,
      );
    }
    if (this is MynapocketApiClientException) {
      // MynapocketApiClientException場合はAppErrorに変換して返す
      return AppError(
        reason: this,
        statusCode: (this as MynapocketApiClientException).statusCode,
        error: (this as MynapocketApiClientException).error,
        errorDescription:
            (this as MynapocketApiClientException).errorDescription,
        httpCode: (this as MynapocketApiClientException).httpCode,
        httpMessage: (this as MynapocketApiClientException).httpMessage,
        moreInformation: (this as MynapocketApiClientException).moreInformation,
      );
    }
    if (this is PlatformException && convertPlatformException) {
      // PlatformExceptionの場合はAppErrorに変換して返す
      return AppError(
        reason: this,
        code: (this as PlatformException).code,
        message: (this as PlatformException).message,
        details: (this as PlatformException).details.toString(),
      );
    }
    if (this is VdidLoginApplicationException) {
      // VdidLoginApplicationException場合はAppErrorに変換して返す
      return AppError(
        reason: this,
        code: (this as VdidLoginApplicationException).code,
        message: (this as VdidLoginApplicationException).message,
      );
    }
    if (this is VdidLoginSystemException) {
      // VdidLoginSystemException場合はAppErrorに変換して返す
      return AppError(
        reason: this,
        code: (this as VdidLoginSystemException).code,
        message: (this as VdidLoginSystemException).message,
      );
    }
    // それ以外の場合はAppErrorに変換して返す
    Log.e('Class name : ${runtimeType.toString()}, \nMessage : ${toString()}');
    return AppError(
      reason: this,
      code: ErrorInfo.defaultErrorCode,
      message: ErrorInfo.defaultErrorMessage,
    );
  }
}
