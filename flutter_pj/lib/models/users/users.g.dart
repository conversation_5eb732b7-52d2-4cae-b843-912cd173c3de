// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'users.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UsersImpl _$$UsersImplFromJson(Map<String, dynamic> json) => _$UsersImpl(
      count: (json['count'] as num).toInt(),
      accountList: (json['accountList'] as List<dynamic>)
          .map((e) => AccountList.fromJson(e as Map<String, dynamic>))
          .toList(),
      balanceAuthorityStatus: json['balanceAuthorityStatus'] as String?,
      serverDate: json['serverDate'] as String,
    );

Map<String, dynamic> _$$UsersImplToJson(_$UsersImpl instance) =>
    <String, dynamic>{
      'count': instance.count,
      'accountList': instance.accountList,
      'balanceAuthorityStatus': instance.balanceAuthorityStatus,
      'serverDate': instance.serverDate,
    };

_$AccountListImpl _$$AccountListImplFromJson(Map<String, dynamic> json) =>
    _$AccountListImpl(
      accountId: json['accountId'] as String?,
      account: json['account'] as String?,
      branchNameKana: json['branchNameKana'] as String?,
      branchNameKanji: json['branchNameKanji'] as String?,
      accountType: json['accountType'] as String?,
      accountNumber: json['accountNumber'] as String?,
      remitterName: json['remitterName'] as String?,
      bankName: json['bankName'] as String?,
      displayAccountName: json['displayAccountName'] as String?,
      isHidden: json['isHidden'] as bool,
      displayOrder: (json['index'] as num).toInt(),
      accountApiType: json['accountApiType'] as String,
    );

Map<String, dynamic> _$$AccountListImplToJson(_$AccountListImpl instance) =>
    <String, dynamic>{
      'accountId': instance.accountId,
      'account': instance.account,
      'branchNameKana': instance.branchNameKana,
      'branchNameKanji': instance.branchNameKanji,
      'accountType': instance.accountType,
      'accountNumber': instance.accountNumber,
      'remitterName': instance.remitterName,
      'bankName': instance.bankName,
      'displayAccountName': instance.displayAccountName,
      'isHidden': instance.isHidden,
      'index': instance.displayOrder,
      'accountApiType': instance.accountApiType,
    };
