// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'users.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Users _$UsersFromJson(Map<String, dynamic> json) {
  return _Users.fromJson(json);
}

/// @nodoc
mixin _$Users {
  /// 口座数
  int get count => throw _privateConstructorUsedError;

  /// 口座一覧
  List<AccountList> get accountList => throw _privateConstructorUsedError;

  /// 残高照会権限保有状態
  /// ON ：保有あり
  /// OFF：保有なし
  String? get balanceAuthorityStatus => throw _privateConstructorUsedError;

  /// サーバー日付
  String get serverDate => throw _privateConstructorUsedError;

  /// Serializes this Users to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Users
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UsersCopyWith<Users> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UsersCopyWith<$Res> {
  factory $UsersCopyWith(Users value, $Res Function(Users) then) =
      _$UsersCopyWithImpl<$Res, Users>;
  @useResult
  $Res call(
      {int count,
      List<AccountList> accountList,
      String? balanceAuthorityStatus,
      String serverDate});
}

/// @nodoc
class _$UsersCopyWithImpl<$Res, $Val extends Users>
    implements $UsersCopyWith<$Res> {
  _$UsersCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Users
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = null,
    Object? accountList = null,
    Object? balanceAuthorityStatus = freezed,
    Object? serverDate = null,
  }) {
    return _then(_value.copyWith(
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      accountList: null == accountList
          ? _value.accountList
          : accountList // ignore: cast_nullable_to_non_nullable
              as List<AccountList>,
      balanceAuthorityStatus: freezed == balanceAuthorityStatus
          ? _value.balanceAuthorityStatus
          : balanceAuthorityStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      serverDate: null == serverDate
          ? _value.serverDate
          : serverDate // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UsersImplCopyWith<$Res> implements $UsersCopyWith<$Res> {
  factory _$$UsersImplCopyWith(
          _$UsersImpl value, $Res Function(_$UsersImpl) then) =
      __$$UsersImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int count,
      List<AccountList> accountList,
      String? balanceAuthorityStatus,
      String serverDate});
}

/// @nodoc
class __$$UsersImplCopyWithImpl<$Res>
    extends _$UsersCopyWithImpl<$Res, _$UsersImpl>
    implements _$$UsersImplCopyWith<$Res> {
  __$$UsersImplCopyWithImpl(
      _$UsersImpl _value, $Res Function(_$UsersImpl) _then)
      : super(_value, _then);

  /// Create a copy of Users
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = null,
    Object? accountList = null,
    Object? balanceAuthorityStatus = freezed,
    Object? serverDate = null,
  }) {
    return _then(_$UsersImpl(
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      accountList: null == accountList
          ? _value._accountList
          : accountList // ignore: cast_nullable_to_non_nullable
              as List<AccountList>,
      balanceAuthorityStatus: freezed == balanceAuthorityStatus
          ? _value.balanceAuthorityStatus
          : balanceAuthorityStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      serverDate: null == serverDate
          ? _value.serverDate
          : serverDate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UsersImpl implements _Users {
  const _$UsersImpl(
      {required this.count,
      required final List<AccountList> accountList,
      this.balanceAuthorityStatus,
      required this.serverDate})
      : _accountList = accountList;

  factory _$UsersImpl.fromJson(Map<String, dynamic> json) =>
      _$$UsersImplFromJson(json);

  /// 口座数
  @override
  final int count;

  /// 口座一覧
  final List<AccountList> _accountList;

  /// 口座一覧
  @override
  List<AccountList> get accountList {
    if (_accountList is EqualUnmodifiableListView) return _accountList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_accountList);
  }

  /// 残高照会権限保有状態
  /// ON ：保有あり
  /// OFF：保有なし
  @override
  final String? balanceAuthorityStatus;

  /// サーバー日付
  @override
  final String serverDate;

  @override
  String toString() {
    return 'Users(count: $count, accountList: $accountList, balanceAuthorityStatus: $balanceAuthorityStatus, serverDate: $serverDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UsersImpl &&
            (identical(other.count, count) || other.count == count) &&
            const DeepCollectionEquality()
                .equals(other._accountList, _accountList) &&
            (identical(other.balanceAuthorityStatus, balanceAuthorityStatus) ||
                other.balanceAuthorityStatus == balanceAuthorityStatus) &&
            (identical(other.serverDate, serverDate) ||
                other.serverDate == serverDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      count,
      const DeepCollectionEquality().hash(_accountList),
      balanceAuthorityStatus,
      serverDate);

  /// Create a copy of Users
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UsersImplCopyWith<_$UsersImpl> get copyWith =>
      __$$UsersImplCopyWithImpl<_$UsersImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UsersImplToJson(
      this,
    );
  }
}

abstract class _Users implements Users {
  const factory _Users(
      {required final int count,
      required final List<AccountList> accountList,
      final String? balanceAuthorityStatus,
      required final String serverDate}) = _$UsersImpl;

  factory _Users.fromJson(Map<String, dynamic> json) = _$UsersImpl.fromJson;

  /// 口座数
  @override
  int get count;

  /// 口座一覧
  @override
  List<AccountList> get accountList;

  /// 残高照会権限保有状態
  /// ON ：保有あり
  /// OFF：保有なし
  @override
  String? get balanceAuthorityStatus;

  /// サーバー日付
  @override
  String get serverDate;

  /// Create a copy of Users
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UsersImplCopyWith<_$UsersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AccountList _$AccountListFromJson(Map<String, dynamic> json) {
  return _AccountList.fromJson(json);
}

/// @nodoc
mixin _$AccountList {
  /// 口座識別子
  String? get accountId => throw _privateConstructorUsedError;

  /// 口座
  /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
  /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
  String? get account => throw _privateConstructorUsedError;

  /// 支店名（カナ）
  /// カナ支店名を設定。
  /// ※半角15文字以内＋店舗属性（全角4文字以内）。
  String? get branchNameKana => throw _privateConstructorUsedError;

  /// 支店名（漢字）
  /// 漢字支店名を設定。
  /// ※全角15文字以内＋店舗属性（全角4文字以内）。
  String? get branchNameKanji => throw _privateConstructorUsedError;

  /// 預金種目名
  String? get accountType => throw _privateConstructorUsedError;

  /// 口座番号
  String? get accountNumber => throw _privateConstructorUsedError;

  /// 振込依頼人名
  String? get remitterName => throw _privateConstructorUsedError;

  /// 金融機関名
  String? get bankName => throw _privateConstructorUsedError;

  /// 口座表示名
  String? get displayAccountName => throw _privateConstructorUsedError;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  bool get isHidden => throw _privateConstructorUsedError;

  /// 一意の番号。並び順に使用
  @JsonKey(name: 'index')
  int get displayOrder => throw _privateConstructorUsedError;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  String get accountApiType => throw _privateConstructorUsedError;

  /// Serializes this AccountList to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountList
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountListCopyWith<AccountList> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountListCopyWith<$Res> {
  factory $AccountListCopyWith(
          AccountList value, $Res Function(AccountList) then) =
      _$AccountListCopyWithImpl<$Res, AccountList>;
  @useResult
  $Res call(
      {String? accountId,
      String? account,
      String? branchNameKana,
      String? branchNameKanji,
      String? accountType,
      String? accountNumber,
      String? remitterName,
      String? bankName,
      String? displayAccountName,
      bool isHidden,
      @JsonKey(name: 'index') int displayOrder,
      String accountApiType});
}

/// @nodoc
class _$AccountListCopyWithImpl<$Res, $Val extends AccountList>
    implements $AccountListCopyWith<$Res> {
  _$AccountListCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountList
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = freezed,
    Object? account = freezed,
    Object? branchNameKana = freezed,
    Object? branchNameKanji = freezed,
    Object? accountType = freezed,
    Object? accountNumber = freezed,
    Object? remitterName = freezed,
    Object? bankName = freezed,
    Object? displayAccountName = freezed,
    Object? isHidden = null,
    Object? displayOrder = null,
    Object? accountApiType = null,
  }) {
    return _then(_value.copyWith(
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKana: freezed == branchNameKana
          ? _value.branchNameKana
          : branchNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKanji: freezed == branchNameKanji
          ? _value.branchNameKanji
          : branchNameKanji // ignore: cast_nullable_to_non_nullable
              as String?,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String?,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterName: freezed == remitterName
          ? _value.remitterName
          : remitterName // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
      displayAccountName: freezed == displayAccountName
          ? _value.displayAccountName
          : displayAccountName // ignore: cast_nullable_to_non_nullable
              as String?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountListImplCopyWith<$Res>
    implements $AccountListCopyWith<$Res> {
  factory _$$AccountListImplCopyWith(
          _$AccountListImpl value, $Res Function(_$AccountListImpl) then) =
      __$$AccountListImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? accountId,
      String? account,
      String? branchNameKana,
      String? branchNameKanji,
      String? accountType,
      String? accountNumber,
      String? remitterName,
      String? bankName,
      String? displayAccountName,
      bool isHidden,
      @JsonKey(name: 'index') int displayOrder,
      String accountApiType});
}

/// @nodoc
class __$$AccountListImplCopyWithImpl<$Res>
    extends _$AccountListCopyWithImpl<$Res, _$AccountListImpl>
    implements _$$AccountListImplCopyWith<$Res> {
  __$$AccountListImplCopyWithImpl(
      _$AccountListImpl _value, $Res Function(_$AccountListImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountList
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = freezed,
    Object? account = freezed,
    Object? branchNameKana = freezed,
    Object? branchNameKanji = freezed,
    Object? accountType = freezed,
    Object? accountNumber = freezed,
    Object? remitterName = freezed,
    Object? bankName = freezed,
    Object? displayAccountName = freezed,
    Object? isHidden = null,
    Object? displayOrder = null,
    Object? accountApiType = null,
  }) {
    return _then(_$AccountListImpl(
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKana: freezed == branchNameKana
          ? _value.branchNameKana
          : branchNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKanji: freezed == branchNameKanji
          ? _value.branchNameKanji
          : branchNameKanji // ignore: cast_nullable_to_non_nullable
              as String?,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String?,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterName: freezed == remitterName
          ? _value.remitterName
          : remitterName // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
      displayAccountName: freezed == displayAccountName
          ? _value.displayAccountName
          : displayAccountName // ignore: cast_nullable_to_non_nullable
              as String?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountListImpl implements _AccountList {
  const _$AccountListImpl(
      {this.accountId,
      this.account,
      this.branchNameKana,
      this.branchNameKanji,
      this.accountType,
      this.accountNumber,
      this.remitterName,
      this.bankName,
      this.displayAccountName,
      required this.isHidden,
      @JsonKey(name: 'index') required this.displayOrder,
      required this.accountApiType});

  factory _$AccountListImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountListImplFromJson(json);

  /// 口座識別子
  @override
  final String? accountId;

  /// 口座
  /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
  /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
  @override
  final String? account;

  /// 支店名（カナ）
  /// カナ支店名を設定。
  /// ※半角15文字以内＋店舗属性（全角4文字以内）。
  @override
  final String? branchNameKana;

  /// 支店名（漢字）
  /// 漢字支店名を設定。
  /// ※全角15文字以内＋店舗属性（全角4文字以内）。
  @override
  final String? branchNameKanji;

  /// 預金種目名
  @override
  final String? accountType;

  /// 口座番号
  @override
  final String? accountNumber;

  /// 振込依頼人名
  @override
  final String? remitterName;

  /// 金融機関名
  @override
  final String? bankName;

  /// 口座表示名
  @override
  final String? displayAccountName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  final bool isHidden;

  /// 一意の番号。並び順に使用
  @override
  @JsonKey(name: 'index')
  final int displayOrder;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  final String accountApiType;

  @override
  String toString() {
    return 'AccountList(accountId: $accountId, account: $account, branchNameKana: $branchNameKana, branchNameKanji: $branchNameKanji, accountType: $accountType, accountNumber: $accountNumber, remitterName: $remitterName, bankName: $bankName, displayAccountName: $displayAccountName, isHidden: $isHidden, displayOrder: $displayOrder, accountApiType: $accountApiType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountListImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.branchNameKana, branchNameKana) ||
                other.branchNameKana == branchNameKana) &&
            (identical(other.branchNameKanji, branchNameKanji) ||
                other.branchNameKanji == branchNameKanji) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber) &&
            (identical(other.remitterName, remitterName) ||
                other.remitterName == remitterName) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.displayAccountName, displayAccountName) ||
                other.displayAccountName == displayAccountName) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.displayOrder, displayOrder) ||
                other.displayOrder == displayOrder) &&
            (identical(other.accountApiType, accountApiType) ||
                other.accountApiType == accountApiType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      accountId,
      account,
      branchNameKana,
      branchNameKanji,
      accountType,
      accountNumber,
      remitterName,
      bankName,
      displayAccountName,
      isHidden,
      displayOrder,
      accountApiType);

  /// Create a copy of AccountList
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountListImplCopyWith<_$AccountListImpl> get copyWith =>
      __$$AccountListImplCopyWithImpl<_$AccountListImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountListImplToJson(
      this,
    );
  }
}

abstract class _AccountList implements AccountList {
  const factory _AccountList(
      {final String? accountId,
      final String? account,
      final String? branchNameKana,
      final String? branchNameKanji,
      final String? accountType,
      final String? accountNumber,
      final String? remitterName,
      final String? bankName,
      final String? displayAccountName,
      required final bool isHidden,
      @JsonKey(name: 'index') required final int displayOrder,
      required final String accountApiType}) = _$AccountListImpl;

  factory _AccountList.fromJson(Map<String, dynamic> json) =
      _$AccountListImpl.fromJson;

  /// 口座識別子
  @override
  String? get accountId;

  /// 口座
  /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
  /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
  @override
  String? get account;

  /// 支店名（カナ）
  /// カナ支店名を設定。
  /// ※半角15文字以内＋店舗属性（全角4文字以内）。
  @override
  String? get branchNameKana;

  /// 支店名（漢字）
  /// 漢字支店名を設定。
  /// ※全角15文字以内＋店舗属性（全角4文字以内）。
  @override
  String? get branchNameKanji;

  /// 預金種目名
  @override
  String? get accountType;

  /// 口座番号
  @override
  String? get accountNumber;

  /// 振込依頼人名
  @override
  String? get remitterName;

  /// 金融機関名
  @override
  String? get bankName;

  /// 口座表示名
  @override
  String? get displayAccountName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  bool get isHidden;

  /// 一意の番号。並び順に使用
  @override
  @JsonKey(name: 'index')
  int get displayOrder;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  String get accountApiType;

  /// Create a copy of AccountList
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountListImplCopyWith<_$AccountListImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
