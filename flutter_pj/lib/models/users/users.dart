import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/bank_account_base_info/bank_account_base_info.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'users.freezed.dart';
part 'users.g.dart';

@freezed
class Users with _$Users {
  const factory Users({
    /// 口座数
    required int count,

    /// 口座一覧
    required List<AccountList> accountList,

    /// 残高照会権限保有状態
    /// ON ：保有あり
    /// OFF：保有なし
    String? balanceAuthorityStatus,

    /// サーバー日付
    required String serverDate,
  }) = _Users;

  factory Users.fromJson(Map<String, dynamic> json) => _$UsersFromJson(json);
}

@freezed
class AccountList with _$AccountList {
  const factory AccountList({
    /// 口座識別子
    String? accountId,

    /// 口座
    /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
    /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
    String? account,

    /// 支店名（カナ）
    /// カナ支店名を設定。
    /// ※半角15文字以内＋店舗属性（全角4文字以内）。
    String? branchNameKana,

    /// 支店名（漢字）
    /// 漢字支店名を設定。
    /// ※全角15文字以内＋店舗属性（全角4文字以内）。
    String? branchNameKanji,

    /// 預金種目名
    String? accountType,

    /// 口座番号
    String? accountNumber,

    /// 振込依頼人名
    String? remitterName,

    /// 金融機関名
    String? bankName,

    /// 口座表示名
    String? displayAccountName,

    /// 口座残高カード表示フラグ
    /// true:非表示, false:表示
    required bool isHidden,

    /// 一意の番号。並び順に使用
    @JsonKey(name: 'index') required int displayOrder,

    /// 口座種別
    /// [ 'web21', 'freee' ]
    required String accountApiType,
  }) = _AccountList;

  factory AccountList.fromJson(Map<String, dynamic> json) =>
      _$AccountListFromJson(json);
}

extension UsersExt on Users {
  List<AccountList> get visibleAccounts =>
      accountList.where((element) => !element.isHidden).toList();
}

extension AccountListExt on List<AccountList> {
  List<BankAccount> toBankAccountList([
    List<DisplayConfig> displayConfigs = const [],
  ]) {
    return mapIndexed(
      (e, i) => BankAccount(
        baseInfo: BankAccountBaseInfo(
          accountId: e.accountId,
          account: e.account,
          branchNameKana: e.branchNameKana,
          branchNameKanji: e.branchNameKanji,
          accountType: e.accountType,
          accountNumber: e.accountNumber,
          remitterName: e.remitterName,
          bankName: e.bankName,
        ),
        displayConfig: displayConfigs
                .where((config) => config.accountId == e.accountId)
                .firstOrNull ??
            DisplayConfig(
              accountId: e.accountId ?? '',
              displayOrder: i,
              displayName: e.displayAccountName ?? '',
              isHidden: e.isHidden,
              accountApiType: AccountApiType.values.byName(e.accountApiType),
            ),
      ),
    );
  }

  // 表示口座数を制限する
  List<AccountList> updateIsHiddenFlag({required int max}) {
    int count = 0;
    return map((userAccount) {
      if (count < max) {
        if (!userAccount.isHidden) {
          count++;
        }
        return userAccount;
      } else {
        return userAccount.copyWith(isHidden: true);
      }
    }).toList();
  }
}
