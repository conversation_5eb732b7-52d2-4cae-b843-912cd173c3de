// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account_base_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BankAccountBaseInfo {
  /// 口座識別子
  String? get accountId => throw _privateConstructorUsedError;

  /// 口座
  /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
  /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
  String? get account => throw _privateConstructorUsedError;

  /// 支店名（カナ）
  /// カナ支店名を設定。
  /// ※半角15文字以内＋店舗属性（全角4文字以内）。
  String? get branchNameKana => throw _privateConstructorUsedError;

  /// 支店名（漢字）
  /// 漢字支店名を設定。
  /// ※全角15文字以内＋店舗属性（全角4文字以内）。
  String? get branchNameKanji => throw _privateConstructorUsedError;

  /// 預金種目名
  String? get accountType => throw _privateConstructorUsedError;

  /// 口座番号
  String? get accountNumber => throw _privateConstructorUsedError;

  /// 振込依頼人名
  String? get remitterName => throw _privateConstructorUsedError;

  /// 金融機関名
  String? get bankName => throw _privateConstructorUsedError;

  /// Create a copy of BankAccountBaseInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankAccountBaseInfoCopyWith<BankAccountBaseInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountBaseInfoCopyWith<$Res> {
  factory $BankAccountBaseInfoCopyWith(
          BankAccountBaseInfo value, $Res Function(BankAccountBaseInfo) then) =
      _$BankAccountBaseInfoCopyWithImpl<$Res, BankAccountBaseInfo>;
  @useResult
  $Res call(
      {String? accountId,
      String? account,
      String? branchNameKana,
      String? branchNameKanji,
      String? accountType,
      String? accountNumber,
      String? remitterName,
      String? bankName});
}

/// @nodoc
class _$BankAccountBaseInfoCopyWithImpl<$Res, $Val extends BankAccountBaseInfo>
    implements $BankAccountBaseInfoCopyWith<$Res> {
  _$BankAccountBaseInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccountBaseInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = freezed,
    Object? account = freezed,
    Object? branchNameKana = freezed,
    Object? branchNameKanji = freezed,
    Object? accountType = freezed,
    Object? accountNumber = freezed,
    Object? remitterName = freezed,
    Object? bankName = freezed,
  }) {
    return _then(_value.copyWith(
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKana: freezed == branchNameKana
          ? _value.branchNameKana
          : branchNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKanji: freezed == branchNameKanji
          ? _value.branchNameKanji
          : branchNameKanji // ignore: cast_nullable_to_non_nullable
              as String?,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String?,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterName: freezed == remitterName
          ? _value.remitterName
          : remitterName // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BankAccountBaseInfoImplCopyWith<$Res>
    implements $BankAccountBaseInfoCopyWith<$Res> {
  factory _$$BankAccountBaseInfoImplCopyWith(_$BankAccountBaseInfoImpl value,
          $Res Function(_$BankAccountBaseInfoImpl) then) =
      __$$BankAccountBaseInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? accountId,
      String? account,
      String? branchNameKana,
      String? branchNameKanji,
      String? accountType,
      String? accountNumber,
      String? remitterName,
      String? bankName});
}

/// @nodoc
class __$$BankAccountBaseInfoImplCopyWithImpl<$Res>
    extends _$BankAccountBaseInfoCopyWithImpl<$Res, _$BankAccountBaseInfoImpl>
    implements _$$BankAccountBaseInfoImplCopyWith<$Res> {
  __$$BankAccountBaseInfoImplCopyWithImpl(_$BankAccountBaseInfoImpl _value,
      $Res Function(_$BankAccountBaseInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountBaseInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = freezed,
    Object? account = freezed,
    Object? branchNameKana = freezed,
    Object? branchNameKanji = freezed,
    Object? accountType = freezed,
    Object? accountNumber = freezed,
    Object? remitterName = freezed,
    Object? bankName = freezed,
  }) {
    return _then(_$BankAccountBaseInfoImpl(
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      account: freezed == account
          ? _value.account
          : account // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKana: freezed == branchNameKana
          ? _value.branchNameKana
          : branchNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      branchNameKanji: freezed == branchNameKanji
          ? _value.branchNameKanji
          : branchNameKanji // ignore: cast_nullable_to_non_nullable
              as String?,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String?,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterName: freezed == remitterName
          ? _value.remitterName
          : remitterName // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$BankAccountBaseInfoImpl implements _BankAccountBaseInfo {
  const _$BankAccountBaseInfoImpl(
      {this.accountId,
      this.account,
      this.branchNameKana,
      this.branchNameKanji,
      this.accountType,
      this.accountNumber,
      this.remitterName,
      this.bankName});

  /// 口座識別子
  @override
  final String? accountId;

  /// 口座
  /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
  /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
  @override
  final String? account;

  /// 支店名（カナ）
  /// カナ支店名を設定。
  /// ※半角15文字以内＋店舗属性（全角4文字以内）。
  @override
  final String? branchNameKana;

  /// 支店名（漢字）
  /// 漢字支店名を設定。
  /// ※全角15文字以内＋店舗属性（全角4文字以内）。
  @override
  final String? branchNameKanji;

  /// 預金種目名
  @override
  final String? accountType;

  /// 口座番号
  @override
  final String? accountNumber;

  /// 振込依頼人名
  @override
  final String? remitterName;

  /// 金融機関名
  @override
  final String? bankName;

  @override
  String toString() {
    return 'BankAccountBaseInfo(accountId: $accountId, account: $account, branchNameKana: $branchNameKana, branchNameKanji: $branchNameKanji, accountType: $accountType, accountNumber: $accountNumber, remitterName: $remitterName, bankName: $bankName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountBaseInfoImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.account, account) || other.account == account) &&
            (identical(other.branchNameKana, branchNameKana) ||
                other.branchNameKana == branchNameKana) &&
            (identical(other.branchNameKanji, branchNameKanji) ||
                other.branchNameKanji == branchNameKanji) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber) &&
            (identical(other.remitterName, remitterName) ||
                other.remitterName == remitterName) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      accountId,
      account,
      branchNameKana,
      branchNameKanji,
      accountType,
      accountNumber,
      remitterName,
      bankName);

  /// Create a copy of BankAccountBaseInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountBaseInfoImplCopyWith<_$BankAccountBaseInfoImpl> get copyWith =>
      __$$BankAccountBaseInfoImplCopyWithImpl<_$BankAccountBaseInfoImpl>(
          this, _$identity);
}

abstract class _BankAccountBaseInfo implements BankAccountBaseInfo {
  const factory _BankAccountBaseInfo(
      {final String? accountId,
      final String? account,
      final String? branchNameKana,
      final String? branchNameKanji,
      final String? accountType,
      final String? accountNumber,
      final String? remitterName,
      final String? bankName}) = _$BankAccountBaseInfoImpl;

  /// 口座識別子
  @override
  String? get accountId;

  /// 口座
  /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
  /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
  @override
  String? get account;

  /// 支店名（カナ）
  /// カナ支店名を設定。
  /// ※半角15文字以内＋店舗属性（全角4文字以内）。
  @override
  String? get branchNameKana;

  /// 支店名（漢字）
  /// 漢字支店名を設定。
  /// ※全角15文字以内＋店舗属性（全角4文字以内）。
  @override
  String? get branchNameKanji;

  /// 預金種目名
  @override
  String? get accountType;

  /// 口座番号
  @override
  String? get accountNumber;

  /// 振込依頼人名
  @override
  String? get remitterName;

  /// 金融機関名
  @override
  String? get bankName;

  /// Create a copy of BankAccountBaseInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountBaseInfoImplCopyWith<_$BankAccountBaseInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
