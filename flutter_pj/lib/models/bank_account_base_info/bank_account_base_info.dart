import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_account_base_info.freezed.dart';

/// 口座基本情報
@freezed
class BankAccountBaseInfo with _$BankAccountBaseInfo {
  const factory BankAccountBaseInfo({
    /// 口座識別子
    String? accountId,

    /// 口座
    /// 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
    /// ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
    String? account,

    /// 支店名（カナ）
    /// カナ支店名を設定。
    /// ※半角15文字以内＋店舗属性（全角4文字以内）。
    String? branchNameKana,

    /// 支店名（漢字）
    /// 漢字支店名を設定。
    /// ※全角15文字以内＋店舗属性（全角4文字以内）。
    String? branchNameKanji,

    /// 預金種目名
    String? accountType,

    /// 口座番号
    String? accountNumber,

    /// 振込依頼人名
    String? remitterName,

    /// 金融機関名
    String? bankName,
  }) = _BankAccountBaseInfo;
}
