// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'consent_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ConsentStatus {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> agreeTerms) required,
    required TResult Function() notRequired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> agreeTerms)? required,
    TResult? Function()? notRequired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> agreeTerms)? required,
    TResult Function()? notRequired,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ConsentStatusRequired value) required,
    required TResult Function(ConsentStatusNotRequired value) notRequired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ConsentStatusRequired value)? required,
    TResult? Function(ConsentStatusNotRequired value)? notRequired,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ConsentStatusRequired value)? required,
    TResult Function(ConsentStatusNotRequired value)? notRequired,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ConsentStatusCopyWith<$Res> {
  factory $ConsentStatusCopyWith(
          ConsentStatus value, $Res Function(ConsentStatus) then) =
      _$ConsentStatusCopyWithImpl<$Res, ConsentStatus>;
}

/// @nodoc
class _$ConsentStatusCopyWithImpl<$Res, $Val extends ConsentStatus>
    implements $ConsentStatusCopyWith<$Res> {
  _$ConsentStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ConsentStatus
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ConsentStatusRequiredImplCopyWith<$Res> {
  factory _$$ConsentStatusRequiredImplCopyWith(
          _$ConsentStatusRequiredImpl value,
          $Res Function(_$ConsentStatusRequiredImpl) then) =
      __$$ConsentStatusRequiredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> agreeTerms});
}

/// @nodoc
class __$$ConsentStatusRequiredImplCopyWithImpl<$Res>
    extends _$ConsentStatusCopyWithImpl<$Res, _$ConsentStatusRequiredImpl>
    implements _$$ConsentStatusRequiredImplCopyWith<$Res> {
  __$$ConsentStatusRequiredImplCopyWithImpl(_$ConsentStatusRequiredImpl _value,
      $Res Function(_$ConsentStatusRequiredImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? agreeTerms = null,
  }) {
    return _then(_$ConsentStatusRequiredImpl(
      agreeTerms: null == agreeTerms
          ? _value._agreeTerms
          : agreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$ConsentStatusRequiredImpl implements ConsentStatusRequired {
  const _$ConsentStatusRequiredImpl({required final List<String> agreeTerms})
      : _agreeTerms = agreeTerms;

  final List<String> _agreeTerms;
  @override
  List<String> get agreeTerms {
    if (_agreeTerms is EqualUnmodifiableListView) return _agreeTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_agreeTerms);
  }

  @override
  String toString() {
    return 'ConsentStatus.required(agreeTerms: $agreeTerms)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentStatusRequiredImpl &&
            const DeepCollectionEquality()
                .equals(other._agreeTerms, _agreeTerms));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_agreeTerms));

  /// Create a copy of ConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ConsentStatusRequiredImplCopyWith<_$ConsentStatusRequiredImpl>
      get copyWith => __$$ConsentStatusRequiredImplCopyWithImpl<
          _$ConsentStatusRequiredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> agreeTerms) required,
    required TResult Function() notRequired,
  }) {
    return required(agreeTerms);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> agreeTerms)? required,
    TResult? Function()? notRequired,
  }) {
    return required?.call(agreeTerms);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> agreeTerms)? required,
    TResult Function()? notRequired,
    required TResult orElse(),
  }) {
    if (required != null) {
      return required(agreeTerms);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ConsentStatusRequired value) required,
    required TResult Function(ConsentStatusNotRequired value) notRequired,
  }) {
    return required(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ConsentStatusRequired value)? required,
    TResult? Function(ConsentStatusNotRequired value)? notRequired,
  }) {
    return required?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ConsentStatusRequired value)? required,
    TResult Function(ConsentStatusNotRequired value)? notRequired,
    required TResult orElse(),
  }) {
    if (required != null) {
      return required(this);
    }
    return orElse();
  }
}

abstract class ConsentStatusRequired implements ConsentStatus {
  const factory ConsentStatusRequired(
      {required final List<String> agreeTerms}) = _$ConsentStatusRequiredImpl;

  List<String> get agreeTerms;

  /// Create a copy of ConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ConsentStatusRequiredImplCopyWith<_$ConsentStatusRequiredImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ConsentStatusNotRequiredImplCopyWith<$Res> {
  factory _$$ConsentStatusNotRequiredImplCopyWith(
          _$ConsentStatusNotRequiredImpl value,
          $Res Function(_$ConsentStatusNotRequiredImpl) then) =
      __$$ConsentStatusNotRequiredImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ConsentStatusNotRequiredImplCopyWithImpl<$Res>
    extends _$ConsentStatusCopyWithImpl<$Res, _$ConsentStatusNotRequiredImpl>
    implements _$$ConsentStatusNotRequiredImplCopyWith<$Res> {
  __$$ConsentStatusNotRequiredImplCopyWithImpl(
      _$ConsentStatusNotRequiredImpl _value,
      $Res Function(_$ConsentStatusNotRequiredImpl) _then)
      : super(_value, _then);

  /// Create a copy of ConsentStatus
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ConsentStatusNotRequiredImpl implements ConsentStatusNotRequired {
  const _$ConsentStatusNotRequiredImpl();

  @override
  String toString() {
    return 'ConsentStatus.notRequired()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ConsentStatusNotRequiredImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> agreeTerms) required,
    required TResult Function() notRequired,
  }) {
    return notRequired();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> agreeTerms)? required,
    TResult? Function()? notRequired,
  }) {
    return notRequired?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> agreeTerms)? required,
    TResult Function()? notRequired,
    required TResult orElse(),
  }) {
    if (notRequired != null) {
      return notRequired();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ConsentStatusRequired value) required,
    required TResult Function(ConsentStatusNotRequired value) notRequired,
  }) {
    return notRequired(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ConsentStatusRequired value)? required,
    TResult? Function(ConsentStatusNotRequired value)? notRequired,
  }) {
    return notRequired?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ConsentStatusRequired value)? required,
    TResult Function(ConsentStatusNotRequired value)? notRequired,
    required TResult orElse(),
  }) {
    if (notRequired != null) {
      return notRequired(this);
    }
    return orElse();
  }
}

abstract class ConsentStatusNotRequired implements ConsentStatus {
  const factory ConsentStatusNotRequired() = _$ConsentStatusNotRequiredImpl;
}
