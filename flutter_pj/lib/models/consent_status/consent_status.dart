import 'package:freezed_annotation/freezed_annotation.dart';

part 'consent_status.freezed.dart';

@freezed
class ConsentStatus with _$ConsentStatus {
  /// 利用規定同意が必要
  const factory ConsentStatus.required({
    required List<String> agreeTerms,
  }) = ConsentStatusRequired;

  /// 利用規定同意が不要
  const factory ConsentStatus.notRequired() = ConsentStatusNotRequired;
}

// DBに送信する利用規定リンクの数
const agreeTermsNumber = 1;

// DBに送信しない利用規定リンクの数
const unsavedAgreeTermsNumber = 1;
