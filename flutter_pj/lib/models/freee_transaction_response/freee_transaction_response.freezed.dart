// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_transaction_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FreeeTransactionResponse _$FreeeTransactionResponseFromJson(
    Map<String, dynamic> json) {
  return _FreeeTransactionResponse.fromJson(json);
}

/// @nodoc
mixin _$FreeeTransactionResponse {
  /// freee口座情報
  List<FreeeTransactionDetail> get transactions =>
      throw _privateConstructorUsedError;
  String? get baseTime => throw _privateConstructorUsedError;
  String? get baseDate => throw _privateConstructorUsedError;

  /// Serializes this FreeeTransactionResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeTransactionResponseCopyWith<FreeeTransactionResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeTransactionResponseCopyWith<$Res> {
  factory $FreeeTransactionResponseCopyWith(FreeeTransactionResponse value,
          $Res Function(FreeeTransactionResponse) then) =
      _$FreeeTransactionResponseCopyWithImpl<$Res, FreeeTransactionResponse>;
  @useResult
  $Res call(
      {List<FreeeTransactionDetail> transactions,
      String? baseTime,
      String? baseDate});
}

/// @nodoc
class _$FreeeTransactionResponseCopyWithImpl<$Res,
        $Val extends FreeeTransactionResponse>
    implements $FreeeTransactionResponseCopyWith<$Res> {
  _$FreeeTransactionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactions = null,
    Object? baseTime = freezed,
    Object? baseDate = freezed,
  }) {
    return _then(_value.copyWith(
      transactions: null == transactions
          ? _value.transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<FreeeTransactionDetail>,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeTransactionResponseImplCopyWith<$Res>
    implements $FreeeTransactionResponseCopyWith<$Res> {
  factory _$$FreeeTransactionResponseImplCopyWith(
          _$FreeeTransactionResponseImpl value,
          $Res Function(_$FreeeTransactionResponseImpl) then) =
      __$$FreeeTransactionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<FreeeTransactionDetail> transactions,
      String? baseTime,
      String? baseDate});
}

/// @nodoc
class __$$FreeeTransactionResponseImplCopyWithImpl<$Res>
    extends _$FreeeTransactionResponseCopyWithImpl<$Res,
        _$FreeeTransactionResponseImpl>
    implements _$$FreeeTransactionResponseImplCopyWith<$Res> {
  __$$FreeeTransactionResponseImplCopyWithImpl(
      _$FreeeTransactionResponseImpl _value,
      $Res Function(_$FreeeTransactionResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? transactions = null,
    Object? baseTime = freezed,
    Object? baseDate = freezed,
  }) {
    return _then(_$FreeeTransactionResponseImpl(
      transactions: null == transactions
          ? _value._transactions
          : transactions // ignore: cast_nullable_to_non_nullable
              as List<FreeeTransactionDetail>,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeTransactionResponseImpl implements _FreeeTransactionResponse {
  const _$FreeeTransactionResponseImpl(
      {required final List<FreeeTransactionDetail> transactions,
      this.baseTime,
      this.baseDate})
      : _transactions = transactions;

  factory _$FreeeTransactionResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$FreeeTransactionResponseImplFromJson(json);

  /// freee口座情報
  final List<FreeeTransactionDetail> _transactions;

  /// freee口座情報
  @override
  List<FreeeTransactionDetail> get transactions {
    if (_transactions is EqualUnmodifiableListView) return _transactions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions);
  }

  @override
  final String? baseTime;
  @override
  final String? baseDate;

  @override
  String toString() {
    return 'FreeeTransactionResponse(transactions: $transactions, baseTime: $baseTime, baseDate: $baseDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._transactions, _transactions) &&
            (identical(other.baseTime, baseTime) ||
                other.baseTime == baseTime) &&
            (identical(other.baseDate, baseDate) ||
                other.baseDate == baseDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_transactions), baseTime, baseDate);

  /// Create a copy of FreeeTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeTransactionResponseImplCopyWith<_$FreeeTransactionResponseImpl>
      get copyWith => __$$FreeeTransactionResponseImplCopyWithImpl<
          _$FreeeTransactionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeTransactionResponseImplToJson(
      this,
    );
  }
}

abstract class _FreeeTransactionResponse implements FreeeTransactionResponse {
  const factory _FreeeTransactionResponse(
      {required final List<FreeeTransactionDetail> transactions,
      final String? baseTime,
      final String? baseDate}) = _$FreeeTransactionResponseImpl;

  factory _FreeeTransactionResponse.fromJson(Map<String, dynamic> json) =
      _$FreeeTransactionResponseImpl.fromJson;

  /// freee口座情報
  @override
  List<FreeeTransactionDetail> get transactions;
  @override
  String? get baseTime;
  @override
  String? get baseDate;

  /// Create a copy of FreeeTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeTransactionResponseImplCopyWith<_$FreeeTransactionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FreeeTransactionDetail _$FreeeTransactionDetailFromJson(
    Map<String, dynamic> json) {
  return _FreeeTransactionDetail.fromJson(json);
}

/// @nodoc
mixin _$FreeeTransactionDetail {
  /// 取引日（yyyy/m/d）
  String? get date => throw _privateConstructorUsedError;

  /// 取引金額
  double? get amount => throw _privateConstructorUsedError;

  /// 未決済金額
  double? get dueAmount => throw _privateConstructorUsedError;

  /// 残高(銀行口座等)
  double? get balance => throw _privateConstructorUsedError;

  /// 入金／出金
  /// income : 入金
  /// expense : 出金
  String? get entrySide => throw _privateConstructorUsedError;

  /// 口座ID
  int? get walletableId => throw _privateConstructorUsedError;

  /// 口座区分
  /// bank_account : 銀行口座
  /// credit_card : クレジットカード
  /// wallet : 現金
  String? get walletableType => throw _privateConstructorUsedError;

  /// 取引内容
  String? get description => throw _privateConstructorUsedError;

  /// Serializes this FreeeTransactionDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeTransactionDetailCopyWith<FreeeTransactionDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeTransactionDetailCopyWith<$Res> {
  factory $FreeeTransactionDetailCopyWith(FreeeTransactionDetail value,
          $Res Function(FreeeTransactionDetail) then) =
      _$FreeeTransactionDetailCopyWithImpl<$Res, FreeeTransactionDetail>;
  @useResult
  $Res call(
      {String? date,
      double? amount,
      double? dueAmount,
      double? balance,
      String? entrySide,
      int? walletableId,
      String? walletableType,
      String? description});
}

/// @nodoc
class _$FreeeTransactionDetailCopyWithImpl<$Res,
        $Val extends FreeeTransactionDetail>
    implements $FreeeTransactionDetailCopyWith<$Res> {
  _$FreeeTransactionDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? amount = freezed,
    Object? dueAmount = freezed,
    Object? balance = freezed,
    Object? entrySide = freezed,
    Object? walletableId = freezed,
    Object? walletableType = freezed,
    Object? description = freezed,
  }) {
    return _then(_value.copyWith(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as double?,
      entrySide: freezed == entrySide
          ? _value.entrySide
          : entrySide // ignore: cast_nullable_to_non_nullable
              as String?,
      walletableId: freezed == walletableId
          ? _value.walletableId
          : walletableId // ignore: cast_nullable_to_non_nullable
              as int?,
      walletableType: freezed == walletableType
          ? _value.walletableType
          : walletableType // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeTransactionDetailImplCopyWith<$Res>
    implements $FreeeTransactionDetailCopyWith<$Res> {
  factory _$$FreeeTransactionDetailImplCopyWith(
          _$FreeeTransactionDetailImpl value,
          $Res Function(_$FreeeTransactionDetailImpl) then) =
      __$$FreeeTransactionDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? date,
      double? amount,
      double? dueAmount,
      double? balance,
      String? entrySide,
      int? walletableId,
      String? walletableType,
      String? description});
}

/// @nodoc
class __$$FreeeTransactionDetailImplCopyWithImpl<$Res>
    extends _$FreeeTransactionDetailCopyWithImpl<$Res,
        _$FreeeTransactionDetailImpl>
    implements _$$FreeeTransactionDetailImplCopyWith<$Res> {
  __$$FreeeTransactionDetailImplCopyWithImpl(
      _$FreeeTransactionDetailImpl _value,
      $Res Function(_$FreeeTransactionDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = freezed,
    Object? amount = freezed,
    Object? dueAmount = freezed,
    Object? balance = freezed,
    Object? entrySide = freezed,
    Object? walletableId = freezed,
    Object? walletableType = freezed,
    Object? description = freezed,
  }) {
    return _then(_$FreeeTransactionDetailImpl(
      date: freezed == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      dueAmount: freezed == dueAmount
          ? _value.dueAmount
          : dueAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      balance: freezed == balance
          ? _value.balance
          : balance // ignore: cast_nullable_to_non_nullable
              as double?,
      entrySide: freezed == entrySide
          ? _value.entrySide
          : entrySide // ignore: cast_nullable_to_non_nullable
              as String?,
      walletableId: freezed == walletableId
          ? _value.walletableId
          : walletableId // ignore: cast_nullable_to_non_nullable
              as int?,
      walletableType: freezed == walletableType
          ? _value.walletableType
          : walletableType // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeTransactionDetailImpl implements _FreeeTransactionDetail {
  const _$FreeeTransactionDetailImpl(
      {this.date,
      this.amount,
      this.dueAmount,
      this.balance,
      this.entrySide,
      this.walletableId,
      this.walletableType,
      this.description});

  factory _$FreeeTransactionDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$FreeeTransactionDetailImplFromJson(json);

  /// 取引日（yyyy/m/d）
  @override
  final String? date;

  /// 取引金額
  @override
  final double? amount;

  /// 未決済金額
  @override
  final double? dueAmount;

  /// 残高(銀行口座等)
  @override
  final double? balance;

  /// 入金／出金
  /// income : 入金
  /// expense : 出金
  @override
  final String? entrySide;

  /// 口座ID
  @override
  final int? walletableId;

  /// 口座区分
  /// bank_account : 銀行口座
  /// credit_card : クレジットカード
  /// wallet : 現金
  @override
  final String? walletableType;

  /// 取引内容
  @override
  final String? description;

  @override
  String toString() {
    return 'FreeeTransactionDetail(date: $date, amount: $amount, dueAmount: $dueAmount, balance: $balance, entrySide: $entrySide, walletableId: $walletableId, walletableType: $walletableType, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionDetailImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.dueAmount, dueAmount) ||
                other.dueAmount == dueAmount) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.entrySide, entrySide) ||
                other.entrySide == entrySide) &&
            (identical(other.walletableId, walletableId) ||
                other.walletableId == walletableId) &&
            (identical(other.walletableType, walletableType) ||
                other.walletableType == walletableType) &&
            (identical(other.description, description) ||
                other.description == description));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, amount, dueAmount, balance,
      entrySide, walletableId, walletableType, description);

  /// Create a copy of FreeeTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeTransactionDetailImplCopyWith<_$FreeeTransactionDetailImpl>
      get copyWith => __$$FreeeTransactionDetailImplCopyWithImpl<
          _$FreeeTransactionDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeTransactionDetailImplToJson(
      this,
    );
  }
}

abstract class _FreeeTransactionDetail implements FreeeTransactionDetail {
  const factory _FreeeTransactionDetail(
      {final String? date,
      final double? amount,
      final double? dueAmount,
      final double? balance,
      final String? entrySide,
      final int? walletableId,
      final String? walletableType,
      final String? description}) = _$FreeeTransactionDetailImpl;

  factory _FreeeTransactionDetail.fromJson(Map<String, dynamic> json) =
      _$FreeeTransactionDetailImpl.fromJson;

  /// 取引日（yyyy/m/d）
  @override
  String? get date;

  /// 取引金額
  @override
  double? get amount;

  /// 未決済金額
  @override
  double? get dueAmount;

  /// 残高(銀行口座等)
  @override
  double? get balance;

  /// 入金／出金
  /// income : 入金
  /// expense : 出金
  @override
  String? get entrySide;

  /// 口座ID
  @override
  int? get walletableId;

  /// 口座区分
  /// bank_account : 銀行口座
  /// credit_card : クレジットカード
  /// wallet : 現金
  @override
  String? get walletableType;

  /// 取引内容
  @override
  String? get description;

  /// Create a copy of FreeeTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeTransactionDetailImplCopyWith<_$FreeeTransactionDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}
