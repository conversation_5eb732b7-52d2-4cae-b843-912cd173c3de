import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_transaction_response.freezed.dart';
part 'freee_transaction_response.g.dart';

@freezed
class FreeeTransactionResponse with _$FreeeTransactionResponse {
  const factory FreeeTransactionResponse({
    /// freee口座情報
    required List<FreeeTransactionDetail> transactions,
    String? baseTime,
    String? baseDate,
  }) = _FreeeTransactionResponse;

  factory FreeeTransactionResponse.fromJson(Map<String, dynamic> json) =>
      _$FreeeTransactionResponseFromJson(json);
}

@freezed
class FreeeTransactionDetail with _$FreeeTransactionDetail {
  const factory FreeeTransactionDetail({
    /// 取引日（yyyy/m/d）
    String? date,

    /// 取引金額
    double? amount,

    /// 未決済金額
    double? dueAmount,

    /// 残高(銀行口座等)
    double? balance,

    /// 入金／出金
    /// income : 入金
    /// expense : 出金
    String? entrySide,

    /// 口座ID
    int? walletableId,

    /// 口座区分
    /// bank_account : 銀行口座
    /// credit_card : クレジットカード
    /// wallet : 現金
    String? walletableType,

    /// 取引内容
    String? description,
  }) = _FreeeTransactionDetail;

  factory FreeeTransactionDetail.fromJson(Map<String, dynamic> json) =>
      _$FreeeTransactionDetailFromJson(json);
}
