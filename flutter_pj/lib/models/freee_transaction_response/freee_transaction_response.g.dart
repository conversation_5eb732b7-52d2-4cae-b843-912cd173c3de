// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freee_transaction_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FreeeTransactionResponseImpl _$$FreeeTransactionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$FreeeTransactionResponseImpl(
      transactions: (json['transactions'] as List<dynamic>)
          .map(
              (e) => FreeeTransactionDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      baseTime: json['baseTime'] as String?,
      baseDate: json['baseDate'] as String?,
    );

Map<String, dynamic> _$$FreeeTransactionResponseImplToJson(
        _$FreeeTransactionResponseImpl instance) =>
    <String, dynamic>{
      'transactions': instance.transactions,
      'baseTime': instance.baseTime,
      'baseDate': instance.baseDate,
    };

_$FreeeTransactionDetailImpl _$$FreeeTransactionDetailImplFromJson(
        Map<String, dynamic> json) =>
    _$FreeeTransactionDetailImpl(
      date: json['date'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      dueAmount: (json['dueAmount'] as num?)?.toDouble(),
      balance: (json['balance'] as num?)?.toDouble(),
      entrySide: json['entrySide'] as String?,
      walletableId: (json['walletableId'] as num?)?.toInt(),
      walletableType: json['walletableType'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$$FreeeTransactionDetailImplToJson(
        _$FreeeTransactionDetailImpl instance) =>
    <String, dynamic>{
      'date': instance.date,
      'amount': instance.amount,
      'dueAmount': instance.dueAmount,
      'balance': instance.balance,
      'entrySide': instance.entrySide,
      'walletableId': instance.walletableId,
      'walletableType': instance.walletableType,
      'description': instance.description,
    };
