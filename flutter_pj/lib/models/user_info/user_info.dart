import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_info.freezed.dart';

@freezed
class UserInfo with _$UserInfo {
  const factory UserInfo({
    /// 利用者ID(VDID)
    @Default('') String vdId,

    /// 認証区分
    @Default('') String ninsyoKbn,

    /// 利用者名
    @Default('') String userSeiMei,

    /// 利用者区分
    @Default('') String userType,

    /// 取引先名
    @Default('') String compName,

    /// 申込代表口座 - 店番(4桁)
    @Default('') String branchCode,

    /// 申込代表口座 - 科目(2桁)
    @Default('') String accountType,

    /// 申込代表口座 - 口座番号(8桁)
    @Default('') String accountNumber,

    /// メールアドレス
    @Default('') String email,

    /// DTPID
    @Default('') String dtpId,

    /// Web21契約種別
    @Default('') String contractType,

    /// 内部ID
    @Default('') String internalId,

    /// 利用者権限
    @Default('') String userAuths,
  }) = _UserInfo;
}
