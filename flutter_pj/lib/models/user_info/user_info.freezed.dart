// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UserInfo {
  /// 利用者ID(VDID)
  String get vdId => throw _privateConstructorUsedError;

  /// 認証区分
  String get ninsyoKbn => throw _privateConstructorUsedError;

  /// 利用者名
  String get userSeiMei => throw _privateConstructorUsedError;

  /// 利用者区分
  String get userType => throw _privateConstructorUsedError;

  /// 取引先名
  String get compName => throw _privateConstructorUsedError;

  /// 申込代表口座 - 店番(4桁)
  String get branchCode => throw _privateConstructorUsedError;

  /// 申込代表口座 - 科目(2桁)
  String get accountType => throw _privateConstructorUsedError;

  /// 申込代表口座 - 口座番号(8桁)
  String get accountNumber => throw _privateConstructorUsedError;

  /// メールアドレス
  String get email => throw _privateConstructorUsedError;

  /// DTPID
  String get dtpId => throw _privateConstructorUsedError;

  /// Web21契約種別
  String get contractType => throw _privateConstructorUsedError;

  /// 内部ID
  String get internalId => throw _privateConstructorUsedError;

  /// 利用者権限
  String get userAuths => throw _privateConstructorUsedError;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserInfoCopyWith<UserInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) then) =
      _$UserInfoCopyWithImpl<$Res, UserInfo>;
  @useResult
  $Res call(
      {String vdId,
      String ninsyoKbn,
      String userSeiMei,
      String userType,
      String compName,
      String branchCode,
      String accountType,
      String accountNumber,
      String email,
      String dtpId,
      String contractType,
      String internalId,
      String userAuths});
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res, $Val extends UserInfo>
    implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vdId = null,
    Object? ninsyoKbn = null,
    Object? userSeiMei = null,
    Object? userType = null,
    Object? compName = null,
    Object? branchCode = null,
    Object? accountType = null,
    Object? accountNumber = null,
    Object? email = null,
    Object? dtpId = null,
    Object? contractType = null,
    Object? internalId = null,
    Object? userAuths = null,
  }) {
    return _then(_value.copyWith(
      vdId: null == vdId
          ? _value.vdId
          : vdId // ignore: cast_nullable_to_non_nullable
              as String,
      ninsyoKbn: null == ninsyoKbn
          ? _value.ninsyoKbn
          : ninsyoKbn // ignore: cast_nullable_to_non_nullable
              as String,
      userSeiMei: null == userSeiMei
          ? _value.userSeiMei
          : userSeiMei // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      compName: null == compName
          ? _value.compName
          : compName // ignore: cast_nullable_to_non_nullable
              as String,
      branchCode: null == branchCode
          ? _value.branchCode
          : branchCode // ignore: cast_nullable_to_non_nullable
              as String,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String,
      accountNumber: null == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      dtpId: null == dtpId
          ? _value.dtpId
          : dtpId // ignore: cast_nullable_to_non_nullable
              as String,
      contractType: null == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as String,
      internalId: null == internalId
          ? _value.internalId
          : internalId // ignore: cast_nullable_to_non_nullable
              as String,
      userAuths: null == userAuths
          ? _value.userAuths
          : userAuths // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserInfoImplCopyWith<$Res>
    implements $UserInfoCopyWith<$Res> {
  factory _$$UserInfoImplCopyWith(
          _$UserInfoImpl value, $Res Function(_$UserInfoImpl) then) =
      __$$UserInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String vdId,
      String ninsyoKbn,
      String userSeiMei,
      String userType,
      String compName,
      String branchCode,
      String accountType,
      String accountNumber,
      String email,
      String dtpId,
      String contractType,
      String internalId,
      String userAuths});
}

/// @nodoc
class __$$UserInfoImplCopyWithImpl<$Res>
    extends _$UserInfoCopyWithImpl<$Res, _$UserInfoImpl>
    implements _$$UserInfoImplCopyWith<$Res> {
  __$$UserInfoImplCopyWithImpl(
      _$UserInfoImpl _value, $Res Function(_$UserInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vdId = null,
    Object? ninsyoKbn = null,
    Object? userSeiMei = null,
    Object? userType = null,
    Object? compName = null,
    Object? branchCode = null,
    Object? accountType = null,
    Object? accountNumber = null,
    Object? email = null,
    Object? dtpId = null,
    Object? contractType = null,
    Object? internalId = null,
    Object? userAuths = null,
  }) {
    return _then(_$UserInfoImpl(
      vdId: null == vdId
          ? _value.vdId
          : vdId // ignore: cast_nullable_to_non_nullable
              as String,
      ninsyoKbn: null == ninsyoKbn
          ? _value.ninsyoKbn
          : ninsyoKbn // ignore: cast_nullable_to_non_nullable
              as String,
      userSeiMei: null == userSeiMei
          ? _value.userSeiMei
          : userSeiMei // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      compName: null == compName
          ? _value.compName
          : compName // ignore: cast_nullable_to_non_nullable
              as String,
      branchCode: null == branchCode
          ? _value.branchCode
          : branchCode // ignore: cast_nullable_to_non_nullable
              as String,
      accountType: null == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String,
      accountNumber: null == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      dtpId: null == dtpId
          ? _value.dtpId
          : dtpId // ignore: cast_nullable_to_non_nullable
              as String,
      contractType: null == contractType
          ? _value.contractType
          : contractType // ignore: cast_nullable_to_non_nullable
              as String,
      internalId: null == internalId
          ? _value.internalId
          : internalId // ignore: cast_nullable_to_non_nullable
              as String,
      userAuths: null == userAuths
          ? _value.userAuths
          : userAuths // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UserInfoImpl implements _UserInfo {
  const _$UserInfoImpl(
      {this.vdId = '',
      this.ninsyoKbn = '',
      this.userSeiMei = '',
      this.userType = '',
      this.compName = '',
      this.branchCode = '',
      this.accountType = '',
      this.accountNumber = '',
      this.email = '',
      this.dtpId = '',
      this.contractType = '',
      this.internalId = '',
      this.userAuths = ''});

  /// 利用者ID(VDID)
  @override
  @JsonKey()
  final String vdId;

  /// 認証区分
  @override
  @JsonKey()
  final String ninsyoKbn;

  /// 利用者名
  @override
  @JsonKey()
  final String userSeiMei;

  /// 利用者区分
  @override
  @JsonKey()
  final String userType;

  /// 取引先名
  @override
  @JsonKey()
  final String compName;

  /// 申込代表口座 - 店番(4桁)
  @override
  @JsonKey()
  final String branchCode;

  /// 申込代表口座 - 科目(2桁)
  @override
  @JsonKey()
  final String accountType;

  /// 申込代表口座 - 口座番号(8桁)
  @override
  @JsonKey()
  final String accountNumber;

  /// メールアドレス
  @override
  @JsonKey()
  final String email;

  /// DTPID
  @override
  @JsonKey()
  final String dtpId;

  /// Web21契約種別
  @override
  @JsonKey()
  final String contractType;

  /// 内部ID
  @override
  @JsonKey()
  final String internalId;

  /// 利用者権限
  @override
  @JsonKey()
  final String userAuths;

  @override
  String toString() {
    return 'UserInfo(vdId: $vdId, ninsyoKbn: $ninsyoKbn, userSeiMei: $userSeiMei, userType: $userType, compName: $compName, branchCode: $branchCode, accountType: $accountType, accountNumber: $accountNumber, email: $email, dtpId: $dtpId, contractType: $contractType, internalId: $internalId, userAuths: $userAuths)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserInfoImpl &&
            (identical(other.vdId, vdId) || other.vdId == vdId) &&
            (identical(other.ninsyoKbn, ninsyoKbn) ||
                other.ninsyoKbn == ninsyoKbn) &&
            (identical(other.userSeiMei, userSeiMei) ||
                other.userSeiMei == userSeiMei) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.compName, compName) ||
                other.compName == compName) &&
            (identical(other.branchCode, branchCode) ||
                other.branchCode == branchCode) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.dtpId, dtpId) || other.dtpId == dtpId) &&
            (identical(other.contractType, contractType) ||
                other.contractType == contractType) &&
            (identical(other.internalId, internalId) ||
                other.internalId == internalId) &&
            (identical(other.userAuths, userAuths) ||
                other.userAuths == userAuths));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      vdId,
      ninsyoKbn,
      userSeiMei,
      userType,
      compName,
      branchCode,
      accountType,
      accountNumber,
      email,
      dtpId,
      contractType,
      internalId,
      userAuths);

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserInfoImplCopyWith<_$UserInfoImpl> get copyWith =>
      __$$UserInfoImplCopyWithImpl<_$UserInfoImpl>(this, _$identity);
}

abstract class _UserInfo implements UserInfo {
  const factory _UserInfo(
      {final String vdId,
      final String ninsyoKbn,
      final String userSeiMei,
      final String userType,
      final String compName,
      final String branchCode,
      final String accountType,
      final String accountNumber,
      final String email,
      final String dtpId,
      final String contractType,
      final String internalId,
      final String userAuths}) = _$UserInfoImpl;

  /// 利用者ID(VDID)
  @override
  String get vdId;

  /// 認証区分
  @override
  String get ninsyoKbn;

  /// 利用者名
  @override
  String get userSeiMei;

  /// 利用者区分
  @override
  String get userType;

  /// 取引先名
  @override
  String get compName;

  /// 申込代表口座 - 店番(4桁)
  @override
  String get branchCode;

  /// 申込代表口座 - 科目(2桁)
  @override
  String get accountType;

  /// 申込代表口座 - 口座番号(8桁)
  @override
  String get accountNumber;

  /// メールアドレス
  @override
  String get email;

  /// DTPID
  @override
  String get dtpId;

  /// Web21契約種別
  @override
  String get contractType;

  /// 内部ID
  @override
  String get internalId;

  /// 利用者権限
  @override
  String get userAuths;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserInfoImplCopyWith<_$UserInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
