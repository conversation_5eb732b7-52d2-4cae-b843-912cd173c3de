// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freee_transaction_total_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FreeeTransactionTotalResponseImpl
    _$$FreeeTransactionTotalResponseImplFromJson(Map<String, dynamic> json) =>
        _$FreeeTransactionTotalResponseImpl(
          startDate: json['startDate'] as String,
          endDate: json['endDate'] as String,
          totalExpense: (json['totalExpense'] as num).toInt(),
          serverDateTime: json['serverDateTime'] as String?,
        );

Map<String, dynamic> _$$FreeeTransactionTotalResponseImplToJson(
        _$FreeeTransactionTotalResponseImpl instance) =>
    <String, dynamic>{
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'totalExpense': instance.totalExpense,
      'serverDateTime': instance.serverDateTime,
    };
