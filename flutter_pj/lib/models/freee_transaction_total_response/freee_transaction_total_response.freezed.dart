// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_transaction_total_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FreeeTransactionTotalResponse _$FreeeTransactionTotalResponseFromJson(
    Map<String, dynamic> json) {
  return _FreeeTransactionTotalResponse.fromJson(json);
}

/// @nodoc
mixin _$FreeeTransactionTotalResponse {
  ///取引絞り込みで使用した開始日
  String get startDate => throw _privateConstructorUsedError;

  ///取引絞り込みで使用した終了日
  String get endDate => throw _privateConstructorUsedError;

  ///使用した金額合計（負の数許容）
  int get totalExpense => throw _privateConstructorUsedError;

  /// 情報取得時刻
  String? get serverDateTime => throw _privateConstructorUsedError;

  /// Serializes this FreeeTransactionTotalResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeTransactionTotalResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeTransactionTotalResponseCopyWith<FreeeTransactionTotalResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeTransactionTotalResponseCopyWith<$Res> {
  factory $FreeeTransactionTotalResponseCopyWith(
          FreeeTransactionTotalResponse value,
          $Res Function(FreeeTransactionTotalResponse) then) =
      _$FreeeTransactionTotalResponseCopyWithImpl<$Res,
          FreeeTransactionTotalResponse>;
  @useResult
  $Res call(
      {String startDate,
      String endDate,
      int totalExpense,
      String? serverDateTime});
}

/// @nodoc
class _$FreeeTransactionTotalResponseCopyWithImpl<$Res,
        $Val extends FreeeTransactionTotalResponse>
    implements $FreeeTransactionTotalResponseCopyWith<$Res> {
  _$FreeeTransactionTotalResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeTransactionTotalResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? totalExpense = null,
    Object? serverDateTime = freezed,
  }) {
    return _then(_value.copyWith(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String,
      totalExpense: null == totalExpense
          ? _value.totalExpense
          : totalExpense // ignore: cast_nullable_to_non_nullable
              as int,
      serverDateTime: freezed == serverDateTime
          ? _value.serverDateTime
          : serverDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeTransactionTotalResponseImplCopyWith<$Res>
    implements $FreeeTransactionTotalResponseCopyWith<$Res> {
  factory _$$FreeeTransactionTotalResponseImplCopyWith(
          _$FreeeTransactionTotalResponseImpl value,
          $Res Function(_$FreeeTransactionTotalResponseImpl) then) =
      __$$FreeeTransactionTotalResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String startDate,
      String endDate,
      int totalExpense,
      String? serverDateTime});
}

/// @nodoc
class __$$FreeeTransactionTotalResponseImplCopyWithImpl<$Res>
    extends _$FreeeTransactionTotalResponseCopyWithImpl<$Res,
        _$FreeeTransactionTotalResponseImpl>
    implements _$$FreeeTransactionTotalResponseImplCopyWith<$Res> {
  __$$FreeeTransactionTotalResponseImplCopyWithImpl(
      _$FreeeTransactionTotalResponseImpl _value,
      $Res Function(_$FreeeTransactionTotalResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionTotalResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? totalExpense = null,
    Object? serverDateTime = freezed,
  }) {
    return _then(_$FreeeTransactionTotalResponseImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String,
      totalExpense: null == totalExpense
          ? _value.totalExpense
          : totalExpense // ignore: cast_nullable_to_non_nullable
              as int,
      serverDateTime: freezed == serverDateTime
          ? _value.serverDateTime
          : serverDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeTransactionTotalResponseImpl
    implements _FreeeTransactionTotalResponse {
  const _$FreeeTransactionTotalResponseImpl(
      {required this.startDate,
      required this.endDate,
      required this.totalExpense,
      this.serverDateTime});

  factory _$FreeeTransactionTotalResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$FreeeTransactionTotalResponseImplFromJson(json);

  ///取引絞り込みで使用した開始日
  @override
  final String startDate;

  ///取引絞り込みで使用した終了日
  @override
  final String endDate;

  ///使用した金額合計（負の数許容）
  @override
  final int totalExpense;

  /// 情報取得時刻
  @override
  final String? serverDateTime;

  @override
  String toString() {
    return 'FreeeTransactionTotalResponse(startDate: $startDate, endDate: $endDate, totalExpense: $totalExpense, serverDateTime: $serverDateTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionTotalResponseImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.totalExpense, totalExpense) ||
                other.totalExpense == totalExpense) &&
            (identical(other.serverDateTime, serverDateTime) ||
                other.serverDateTime == serverDateTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, startDate, endDate, totalExpense, serverDateTime);

  /// Create a copy of FreeeTransactionTotalResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeTransactionTotalResponseImplCopyWith<
          _$FreeeTransactionTotalResponseImpl>
      get copyWith => __$$FreeeTransactionTotalResponseImplCopyWithImpl<
          _$FreeeTransactionTotalResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeTransactionTotalResponseImplToJson(
      this,
    );
  }
}

abstract class _FreeeTransactionTotalResponse
    implements FreeeTransactionTotalResponse {
  const factory _FreeeTransactionTotalResponse(
      {required final String startDate,
      required final String endDate,
      required final int totalExpense,
      final String? serverDateTime}) = _$FreeeTransactionTotalResponseImpl;

  factory _FreeeTransactionTotalResponse.fromJson(Map<String, dynamic> json) =
      _$FreeeTransactionTotalResponseImpl.fromJson;

  ///取引絞り込みで使用した開始日
  @override
  String get startDate;

  ///取引絞り込みで使用した終了日
  @override
  String get endDate;

  ///使用した金額合計（負の数許容）
  @override
  int get totalExpense;

  /// 情報取得時刻
  @override
  String? get serverDateTime;

  /// Create a copy of FreeeTransactionTotalResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeTransactionTotalResponseImplCopyWith<
          _$FreeeTransactionTotalResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
