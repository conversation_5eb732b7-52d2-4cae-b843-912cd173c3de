import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_transaction_total_response.freezed.dart';
part 'freee_transaction_total_response.g.dart';

/// freeeクレジットカード利用額取得APIからのレスポンスを受け取るためのクラス
@freezed
class FreeeTransactionTotalResponse with _$FreeeTransactionTotalResponse {
  const factory FreeeTransactionTotalResponse({
    ///取引絞り込みで使用した開始日
    required String startDate,

    ///取引絞り込みで使用した終了日
    required String endDate,

    ///使用した金額合計（負の数許容）
    required int totalExpense,

    /// 情報取得時刻
    String? serverDateTime,
  }) = _FreeeTransactionTotalResponse;

  factory FreeeTransactionTotalResponse.fromJson(Map<String, dynamic> json) =>
      _$FreeeTransactionTotalResponseFromJson(json);
}
