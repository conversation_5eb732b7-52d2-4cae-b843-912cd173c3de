// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Session {
  SessionPermissionStatus get permissionStatus =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)
        identified,
    required TResult Function(SessionPermissionStatus permissionStatus) none,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)?
        identified,
    TResult? Function(SessionPermissionStatus permissionStatus)? none,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)?
        identified,
    TResult Function(SessionPermissionStatus permissionStatus)? none,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SessionIdentified value) identified,
    required TResult Function(SessionNone value) none,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SessionIdentified value)? identified,
    TResult? Function(SessionNone value)? none,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SessionIdentified value)? identified,
    TResult Function(SessionNone value)? none,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SessionCopyWith<Session> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionCopyWith<$Res> {
  factory $SessionCopyWith(Session value, $Res Function(Session) then) =
      _$SessionCopyWithImpl<$Res, Session>;
  @useResult
  $Res call({SessionPermissionStatus permissionStatus});
}

/// @nodoc
class _$SessionCopyWithImpl<$Res, $Val extends Session>
    implements $SessionCopyWith<$Res> {
  _$SessionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? permissionStatus = null,
  }) {
    return _then(_value.copyWith(
      permissionStatus: null == permissionStatus
          ? _value.permissionStatus
          : permissionStatus // ignore: cast_nullable_to_non_nullable
              as SessionPermissionStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionIdentifiedImplCopyWith<$Res>
    implements $SessionCopyWith<$Res> {
  factory _$$SessionIdentifiedImplCopyWith(_$SessionIdentifiedImpl value,
          $Res Function(_$SessionIdentifiedImpl) then) =
      __$$SessionIdentifiedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SessionPermissionStatus permissionStatus, AppError? accessTokenError});

  $AppErrorCopyWith<$Res>? get accessTokenError;
}

/// @nodoc
class __$$SessionIdentifiedImplCopyWithImpl<$Res>
    extends _$SessionCopyWithImpl<$Res, _$SessionIdentifiedImpl>
    implements _$$SessionIdentifiedImplCopyWith<$Res> {
  __$$SessionIdentifiedImplCopyWithImpl(_$SessionIdentifiedImpl _value,
      $Res Function(_$SessionIdentifiedImpl) _then)
      : super(_value, _then);

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? permissionStatus = null,
    Object? accessTokenError = freezed,
  }) {
    return _then(_$SessionIdentifiedImpl(
      permissionStatus: null == permissionStatus
          ? _value.permissionStatus
          : permissionStatus // ignore: cast_nullable_to_non_nullable
              as SessionPermissionStatus,
      accessTokenError: freezed == accessTokenError
          ? _value.accessTokenError
          : accessTokenError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get accessTokenError {
    if (_value.accessTokenError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.accessTokenError!, (value) {
      return _then(_value.copyWith(accessTokenError: value));
    });
  }
}

/// @nodoc

class _$SessionIdentifiedImpl implements SessionIdentified {
  const _$SessionIdentifiedImpl(
      {required this.permissionStatus, this.accessTokenError});

  @override
  final SessionPermissionStatus permissionStatus;
  @override
  final AppError? accessTokenError;

  @override
  String toString() {
    return 'Session.identified(permissionStatus: $permissionStatus, accessTokenError: $accessTokenError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionIdentifiedImpl &&
            (identical(other.permissionStatus, permissionStatus) ||
                other.permissionStatus == permissionStatus) &&
            (identical(other.accessTokenError, accessTokenError) ||
                other.accessTokenError == accessTokenError));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, permissionStatus, accessTokenError);

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionIdentifiedImplCopyWith<_$SessionIdentifiedImpl> get copyWith =>
      __$$SessionIdentifiedImplCopyWithImpl<_$SessionIdentifiedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)
        identified,
    required TResult Function(SessionPermissionStatus permissionStatus) none,
  }) {
    return identified(permissionStatus, accessTokenError);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)?
        identified,
    TResult? Function(SessionPermissionStatus permissionStatus)? none,
  }) {
    return identified?.call(permissionStatus, accessTokenError);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)?
        identified,
    TResult Function(SessionPermissionStatus permissionStatus)? none,
    required TResult orElse(),
  }) {
    if (identified != null) {
      return identified(permissionStatus, accessTokenError);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SessionIdentified value) identified,
    required TResult Function(SessionNone value) none,
  }) {
    return identified(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SessionIdentified value)? identified,
    TResult? Function(SessionNone value)? none,
  }) {
    return identified?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SessionIdentified value)? identified,
    TResult Function(SessionNone value)? none,
    required TResult orElse(),
  }) {
    if (identified != null) {
      return identified(this);
    }
    return orElse();
  }
}

abstract class SessionIdentified implements Session {
  const factory SessionIdentified(
      {required final SessionPermissionStatus permissionStatus,
      final AppError? accessTokenError}) = _$SessionIdentifiedImpl;

  @override
  SessionPermissionStatus get permissionStatus;
  AppError? get accessTokenError;

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionIdentifiedImplCopyWith<_$SessionIdentifiedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SessionNoneImplCopyWith<$Res>
    implements $SessionCopyWith<$Res> {
  factory _$$SessionNoneImplCopyWith(
          _$SessionNoneImpl value, $Res Function(_$SessionNoneImpl) then) =
      __$$SessionNoneImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({SessionPermissionStatus permissionStatus});
}

/// @nodoc
class __$$SessionNoneImplCopyWithImpl<$Res>
    extends _$SessionCopyWithImpl<$Res, _$SessionNoneImpl>
    implements _$$SessionNoneImplCopyWith<$Res> {
  __$$SessionNoneImplCopyWithImpl(
      _$SessionNoneImpl _value, $Res Function(_$SessionNoneImpl) _then)
      : super(_value, _then);

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? permissionStatus = null,
  }) {
    return _then(_$SessionNoneImpl(
      permissionStatus: null == permissionStatus
          ? _value.permissionStatus
          : permissionStatus // ignore: cast_nullable_to_non_nullable
              as SessionPermissionStatus,
    ));
  }
}

/// @nodoc

class _$SessionNoneImpl implements SessionNone {
  const _$SessionNoneImpl(
      {this.permissionStatus = SessionPermissionStatus.unknown});

  @override
  @JsonKey()
  final SessionPermissionStatus permissionStatus;

  @override
  String toString() {
    return 'Session.none(permissionStatus: $permissionStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionNoneImpl &&
            (identical(other.permissionStatus, permissionStatus) ||
                other.permissionStatus == permissionStatus));
  }

  @override
  int get hashCode => Object.hash(runtimeType, permissionStatus);

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionNoneImplCopyWith<_$SessionNoneImpl> get copyWith =>
      __$$SessionNoneImplCopyWithImpl<_$SessionNoneImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)
        identified,
    required TResult Function(SessionPermissionStatus permissionStatus) none,
  }) {
    return none(permissionStatus);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)?
        identified,
    TResult? Function(SessionPermissionStatus permissionStatus)? none,
  }) {
    return none?.call(permissionStatus);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(SessionPermissionStatus permissionStatus,
            AppError? accessTokenError)?
        identified,
    TResult Function(SessionPermissionStatus permissionStatus)? none,
    required TResult orElse(),
  }) {
    if (none != null) {
      return none(permissionStatus);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(SessionIdentified value) identified,
    required TResult Function(SessionNone value) none,
  }) {
    return none(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(SessionIdentified value)? identified,
    TResult? Function(SessionNone value)? none,
  }) {
    return none?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(SessionIdentified value)? identified,
    TResult Function(SessionNone value)? none,
    required TResult orElse(),
  }) {
    if (none != null) {
      return none(this);
    }
    return orElse();
  }
}

abstract class SessionNone implements Session {
  const factory SessionNone({final SessionPermissionStatus permissionStatus}) =
      _$SessionNoneImpl;

  @override
  SessionPermissionStatus get permissionStatus;

  /// Create a copy of Session
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionNoneImplCopyWith<_$SessionNoneImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
