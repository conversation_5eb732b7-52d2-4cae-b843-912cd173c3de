import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/session_permission_status/session_permission_status.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'session.freezed.dart';

/// HACK 現状Session型を用いてログイン状態管理をする意味が薄い為、削除予定
@freezed
class Session with _$Session {
  const factory Session.identified({
    required SessionPermissionStatus permissionStatus,
    AppError? accessTokenError, // アクセストーンエラー
  }) = SessionIdentified;

  const factory Session.none({
    @Default(SessionPermissionStatus.unknown)
    SessionPermissionStatus permissionStatus,
  }) = SessionNone;
}
