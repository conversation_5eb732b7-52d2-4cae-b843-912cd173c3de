// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jpki_identification_api.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JpkiIdentificationApi _$JpkiIdentificationApiFromJson(
    Map<String, dynamic> json) {
  return _JpkiIdentificationApi.fromJson(json);
}

/// @nodoc
mixin _$JpkiIdentificationApi {
// ステータス
  String get status => throw _privateConstructorUsedError; // 詳細
  String get detail => throw _privateConstructorUsedError; // UUID
  String get uuId => throw _privateConstructorUsedError; // インタフェースID
  String? get ifid => throw _privateConstructorUsedError; // 入力トランザクションID
  String? get inTransactionId =>
      throw _privateConstructorUsedError; // 出力トランザクションID
  String? get outTransactionId =>
      throw _privateConstructorUsedError; // BizPICO結果コード
  String? get resultCode => throw _privateConstructorUsedError; // 結果詳細メッセージ
  String? get resultDetailMessage =>
      throw _privateConstructorUsedError; // デバイス種別
  String? get deviceType => throw _privateConstructorUsedError; // PICO ID
  String? get picoId => throw _privateConstructorUsedError; // 本人確認番号
  String? get authrizNum => throw _privateConstructorUsedError; // 本人確認依頼受信日時
  String? get reqAuthDate => throw _privateConstructorUsedError; // 本人確認責任部署
  String? get authDepart => throw _privateConstructorUsedError; // 本人確認責任者
  String? get authPerson => throw _privateConstructorUsedError; // 本人確認実施方法
  String? get authImpMethod => throw _privateConstructorUsedError; // 署名用証明書の氏名
  String? get nameBp => throw _privateConstructorUsedError; // 署名用証明書の生年月日
  String? get dobBp => throw _privateConstructorUsedError; // 署名用証明書の性別
  String? get genderBp => throw _privateConstructorUsedError; // 署名用証明書の住所
  String? get addressBp => throw _privateConstructorUsedError; // 本人確認書類名称
  String? get authDoc => throw _privateConstructorUsedError; // 本人確認証跡番号
  String? get authTrailNum =>
      throw _privateConstructorUsedError; // 署名検証者での本人確認記録
  String? get authVerifyRecord =>
      throw _privateConstructorUsedError; // カード用証明書更新フラグ
  String? get certUpdFlg => throw _privateConstructorUsedError; // スマホ用証明書更新フラグ
  String? get mobileCertUpdFlg =>
      throw _privateConstructorUsedError; // ステータスコード
  String? get statusCode => throw _privateConstructorUsedError; // 受理番号
  String? get acceptanceNo => throw _privateConstructorUsedError; // 利用者同意有効期限
  String? get expirationDate => throw _privateConstructorUsedError;

  /// Serializes this JpkiIdentificationApi to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JpkiIdentificationApi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JpkiIdentificationApiCopyWith<JpkiIdentificationApi> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JpkiIdentificationApiCopyWith<$Res> {
  factory $JpkiIdentificationApiCopyWith(JpkiIdentificationApi value,
          $Res Function(JpkiIdentificationApi) then) =
      _$JpkiIdentificationApiCopyWithImpl<$Res, JpkiIdentificationApi>;
  @useResult
  $Res call(
      {String status,
      String detail,
      String uuId,
      String? ifid,
      String? inTransactionId,
      String? outTransactionId,
      String? resultCode,
      String? resultDetailMessage,
      String? deviceType,
      String? picoId,
      String? authrizNum,
      String? reqAuthDate,
      String? authDepart,
      String? authPerson,
      String? authImpMethod,
      String? nameBp,
      String? dobBp,
      String? genderBp,
      String? addressBp,
      String? authDoc,
      String? authTrailNum,
      String? authVerifyRecord,
      String? certUpdFlg,
      String? mobileCertUpdFlg,
      String? statusCode,
      String? acceptanceNo,
      String? expirationDate});
}

/// @nodoc
class _$JpkiIdentificationApiCopyWithImpl<$Res,
        $Val extends JpkiIdentificationApi>
    implements $JpkiIdentificationApiCopyWith<$Res> {
  _$JpkiIdentificationApiCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JpkiIdentificationApi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? detail = null,
    Object? uuId = null,
    Object? ifid = freezed,
    Object? inTransactionId = freezed,
    Object? outTransactionId = freezed,
    Object? resultCode = freezed,
    Object? resultDetailMessage = freezed,
    Object? deviceType = freezed,
    Object? picoId = freezed,
    Object? authrizNum = freezed,
    Object? reqAuthDate = freezed,
    Object? authDepart = freezed,
    Object? authPerson = freezed,
    Object? authImpMethod = freezed,
    Object? nameBp = freezed,
    Object? dobBp = freezed,
    Object? genderBp = freezed,
    Object? addressBp = freezed,
    Object? authDoc = freezed,
    Object? authTrailNum = freezed,
    Object? authVerifyRecord = freezed,
    Object? certUpdFlg = freezed,
    Object? mobileCertUpdFlg = freezed,
    Object? statusCode = freezed,
    Object? acceptanceNo = freezed,
    Object? expirationDate = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      detail: null == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as String,
      uuId: null == uuId
          ? _value.uuId
          : uuId // ignore: cast_nullable_to_non_nullable
              as String,
      ifid: freezed == ifid
          ? _value.ifid
          : ifid // ignore: cast_nullable_to_non_nullable
              as String?,
      inTransactionId: freezed == inTransactionId
          ? _value.inTransactionId
          : inTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      outTransactionId: freezed == outTransactionId
          ? _value.outTransactionId
          : outTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      resultCode: freezed == resultCode
          ? _value.resultCode
          : resultCode // ignore: cast_nullable_to_non_nullable
              as String?,
      resultDetailMessage: freezed == resultDetailMessage
          ? _value.resultDetailMessage
          : resultDetailMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      picoId: freezed == picoId
          ? _value.picoId
          : picoId // ignore: cast_nullable_to_non_nullable
              as String?,
      authrizNum: freezed == authrizNum
          ? _value.authrizNum
          : authrizNum // ignore: cast_nullable_to_non_nullable
              as String?,
      reqAuthDate: freezed == reqAuthDate
          ? _value.reqAuthDate
          : reqAuthDate // ignore: cast_nullable_to_non_nullable
              as String?,
      authDepart: freezed == authDepart
          ? _value.authDepart
          : authDepart // ignore: cast_nullable_to_non_nullable
              as String?,
      authPerson: freezed == authPerson
          ? _value.authPerson
          : authPerson // ignore: cast_nullable_to_non_nullable
              as String?,
      authImpMethod: freezed == authImpMethod
          ? _value.authImpMethod
          : authImpMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      nameBp: freezed == nameBp
          ? _value.nameBp
          : nameBp // ignore: cast_nullable_to_non_nullable
              as String?,
      dobBp: freezed == dobBp
          ? _value.dobBp
          : dobBp // ignore: cast_nullable_to_non_nullable
              as String?,
      genderBp: freezed == genderBp
          ? _value.genderBp
          : genderBp // ignore: cast_nullable_to_non_nullable
              as String?,
      addressBp: freezed == addressBp
          ? _value.addressBp
          : addressBp // ignore: cast_nullable_to_non_nullable
              as String?,
      authDoc: freezed == authDoc
          ? _value.authDoc
          : authDoc // ignore: cast_nullable_to_non_nullable
              as String?,
      authTrailNum: freezed == authTrailNum
          ? _value.authTrailNum
          : authTrailNum // ignore: cast_nullable_to_non_nullable
              as String?,
      authVerifyRecord: freezed == authVerifyRecord
          ? _value.authVerifyRecord
          : authVerifyRecord // ignore: cast_nullable_to_non_nullable
              as String?,
      certUpdFlg: freezed == certUpdFlg
          ? _value.certUpdFlg
          : certUpdFlg // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileCertUpdFlg: freezed == mobileCertUpdFlg
          ? _value.mobileCertUpdFlg
          : mobileCertUpdFlg // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as String?,
      acceptanceNo: freezed == acceptanceNo
          ? _value.acceptanceNo
          : acceptanceNo // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JpkiIdentificationApiImplCopyWith<$Res>
    implements $JpkiIdentificationApiCopyWith<$Res> {
  factory _$$JpkiIdentificationApiImplCopyWith(
          _$JpkiIdentificationApiImpl value,
          $Res Function(_$JpkiIdentificationApiImpl) then) =
      __$$JpkiIdentificationApiImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String status,
      String detail,
      String uuId,
      String? ifid,
      String? inTransactionId,
      String? outTransactionId,
      String? resultCode,
      String? resultDetailMessage,
      String? deviceType,
      String? picoId,
      String? authrizNum,
      String? reqAuthDate,
      String? authDepart,
      String? authPerson,
      String? authImpMethod,
      String? nameBp,
      String? dobBp,
      String? genderBp,
      String? addressBp,
      String? authDoc,
      String? authTrailNum,
      String? authVerifyRecord,
      String? certUpdFlg,
      String? mobileCertUpdFlg,
      String? statusCode,
      String? acceptanceNo,
      String? expirationDate});
}

/// @nodoc
class __$$JpkiIdentificationApiImplCopyWithImpl<$Res>
    extends _$JpkiIdentificationApiCopyWithImpl<$Res,
        _$JpkiIdentificationApiImpl>
    implements _$$JpkiIdentificationApiImplCopyWith<$Res> {
  __$$JpkiIdentificationApiImplCopyWithImpl(_$JpkiIdentificationApiImpl _value,
      $Res Function(_$JpkiIdentificationApiImpl) _then)
      : super(_value, _then);

  /// Create a copy of JpkiIdentificationApi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? detail = null,
    Object? uuId = null,
    Object? ifid = freezed,
    Object? inTransactionId = freezed,
    Object? outTransactionId = freezed,
    Object? resultCode = freezed,
    Object? resultDetailMessage = freezed,
    Object? deviceType = freezed,
    Object? picoId = freezed,
    Object? authrizNum = freezed,
    Object? reqAuthDate = freezed,
    Object? authDepart = freezed,
    Object? authPerson = freezed,
    Object? authImpMethod = freezed,
    Object? nameBp = freezed,
    Object? dobBp = freezed,
    Object? genderBp = freezed,
    Object? addressBp = freezed,
    Object? authDoc = freezed,
    Object? authTrailNum = freezed,
    Object? authVerifyRecord = freezed,
    Object? certUpdFlg = freezed,
    Object? mobileCertUpdFlg = freezed,
    Object? statusCode = freezed,
    Object? acceptanceNo = freezed,
    Object? expirationDate = freezed,
  }) {
    return _then(_$JpkiIdentificationApiImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      detail: null == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as String,
      uuId: null == uuId
          ? _value.uuId
          : uuId // ignore: cast_nullable_to_non_nullable
              as String,
      ifid: freezed == ifid
          ? _value.ifid
          : ifid // ignore: cast_nullable_to_non_nullable
              as String?,
      inTransactionId: freezed == inTransactionId
          ? _value.inTransactionId
          : inTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      outTransactionId: freezed == outTransactionId
          ? _value.outTransactionId
          : outTransactionId // ignore: cast_nullable_to_non_nullable
              as String?,
      resultCode: freezed == resultCode
          ? _value.resultCode
          : resultCode // ignore: cast_nullable_to_non_nullable
              as String?,
      resultDetailMessage: freezed == resultDetailMessage
          ? _value.resultDetailMessage
          : resultDetailMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceType: freezed == deviceType
          ? _value.deviceType
          : deviceType // ignore: cast_nullable_to_non_nullable
              as String?,
      picoId: freezed == picoId
          ? _value.picoId
          : picoId // ignore: cast_nullable_to_non_nullable
              as String?,
      authrizNum: freezed == authrizNum
          ? _value.authrizNum
          : authrizNum // ignore: cast_nullable_to_non_nullable
              as String?,
      reqAuthDate: freezed == reqAuthDate
          ? _value.reqAuthDate
          : reqAuthDate // ignore: cast_nullable_to_non_nullable
              as String?,
      authDepart: freezed == authDepart
          ? _value.authDepart
          : authDepart // ignore: cast_nullable_to_non_nullable
              as String?,
      authPerson: freezed == authPerson
          ? _value.authPerson
          : authPerson // ignore: cast_nullable_to_non_nullable
              as String?,
      authImpMethod: freezed == authImpMethod
          ? _value.authImpMethod
          : authImpMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      nameBp: freezed == nameBp
          ? _value.nameBp
          : nameBp // ignore: cast_nullable_to_non_nullable
              as String?,
      dobBp: freezed == dobBp
          ? _value.dobBp
          : dobBp // ignore: cast_nullable_to_non_nullable
              as String?,
      genderBp: freezed == genderBp
          ? _value.genderBp
          : genderBp // ignore: cast_nullable_to_non_nullable
              as String?,
      addressBp: freezed == addressBp
          ? _value.addressBp
          : addressBp // ignore: cast_nullable_to_non_nullable
              as String?,
      authDoc: freezed == authDoc
          ? _value.authDoc
          : authDoc // ignore: cast_nullable_to_non_nullable
              as String?,
      authTrailNum: freezed == authTrailNum
          ? _value.authTrailNum
          : authTrailNum // ignore: cast_nullable_to_non_nullable
              as String?,
      authVerifyRecord: freezed == authVerifyRecord
          ? _value.authVerifyRecord
          : authVerifyRecord // ignore: cast_nullable_to_non_nullable
              as String?,
      certUpdFlg: freezed == certUpdFlg
          ? _value.certUpdFlg
          : certUpdFlg // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileCertUpdFlg: freezed == mobileCertUpdFlg
          ? _value.mobileCertUpdFlg
          : mobileCertUpdFlg // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCode: freezed == statusCode
          ? _value.statusCode
          : statusCode // ignore: cast_nullable_to_non_nullable
              as String?,
      acceptanceNo: freezed == acceptanceNo
          ? _value.acceptanceNo
          : acceptanceNo // ignore: cast_nullable_to_non_nullable
              as String?,
      expirationDate: freezed == expirationDate
          ? _value.expirationDate
          : expirationDate // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JpkiIdentificationApiImpl implements _JpkiIdentificationApi {
  const _$JpkiIdentificationApiImpl(
      {required this.status,
      required this.detail,
      required this.uuId,
      this.ifid,
      this.inTransactionId,
      this.outTransactionId,
      this.resultCode,
      this.resultDetailMessage,
      this.deviceType,
      this.picoId,
      this.authrizNum,
      this.reqAuthDate,
      this.authDepart,
      this.authPerson,
      this.authImpMethod,
      this.nameBp,
      this.dobBp,
      this.genderBp,
      this.addressBp,
      this.authDoc,
      this.authTrailNum,
      this.authVerifyRecord,
      this.certUpdFlg,
      this.mobileCertUpdFlg,
      this.statusCode,
      this.acceptanceNo,
      this.expirationDate});

  factory _$JpkiIdentificationApiImpl.fromJson(Map<String, dynamic> json) =>
      _$$JpkiIdentificationApiImplFromJson(json);

// ステータス
  @override
  final String status;
// 詳細
  @override
  final String detail;
// UUID
  @override
  final String uuId;
// インタフェースID
  @override
  final String? ifid;
// 入力トランザクションID
  @override
  final String? inTransactionId;
// 出力トランザクションID
  @override
  final String? outTransactionId;
// BizPICO結果コード
  @override
  final String? resultCode;
// 結果詳細メッセージ
  @override
  final String? resultDetailMessage;
// デバイス種別
  @override
  final String? deviceType;
// PICO ID
  @override
  final String? picoId;
// 本人確認番号
  @override
  final String? authrizNum;
// 本人確認依頼受信日時
  @override
  final String? reqAuthDate;
// 本人確認責任部署
  @override
  final String? authDepart;
// 本人確認責任者
  @override
  final String? authPerson;
// 本人確認実施方法
  @override
  final String? authImpMethod;
// 署名用証明書の氏名
  @override
  final String? nameBp;
// 署名用証明書の生年月日
  @override
  final String? dobBp;
// 署名用証明書の性別
  @override
  final String? genderBp;
// 署名用証明書の住所
  @override
  final String? addressBp;
// 本人確認書類名称
  @override
  final String? authDoc;
// 本人確認証跡番号
  @override
  final String? authTrailNum;
// 署名検証者での本人確認記録
  @override
  final String? authVerifyRecord;
// カード用証明書更新フラグ
  @override
  final String? certUpdFlg;
// スマホ用証明書更新フラグ
  @override
  final String? mobileCertUpdFlg;
// ステータスコード
  @override
  final String? statusCode;
// 受理番号
  @override
  final String? acceptanceNo;
// 利用者同意有効期限
  @override
  final String? expirationDate;

  @override
  String toString() {
    return 'JpkiIdentificationApi(status: $status, detail: $detail, uuId: $uuId, ifid: $ifid, inTransactionId: $inTransactionId, outTransactionId: $outTransactionId, resultCode: $resultCode, resultDetailMessage: $resultDetailMessage, deviceType: $deviceType, picoId: $picoId, authrizNum: $authrizNum, reqAuthDate: $reqAuthDate, authDepart: $authDepart, authPerson: $authPerson, authImpMethod: $authImpMethod, nameBp: $nameBp, dobBp: $dobBp, genderBp: $genderBp, addressBp: $addressBp, authDoc: $authDoc, authTrailNum: $authTrailNum, authVerifyRecord: $authVerifyRecord, certUpdFlg: $certUpdFlg, mobileCertUpdFlg: $mobileCertUpdFlg, statusCode: $statusCode, acceptanceNo: $acceptanceNo, expirationDate: $expirationDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JpkiIdentificationApiImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.detail, detail) || other.detail == detail) &&
            (identical(other.uuId, uuId) || other.uuId == uuId) &&
            (identical(other.ifid, ifid) || other.ifid == ifid) &&
            (identical(other.inTransactionId, inTransactionId) ||
                other.inTransactionId == inTransactionId) &&
            (identical(other.outTransactionId, outTransactionId) ||
                other.outTransactionId == outTransactionId) &&
            (identical(other.resultCode, resultCode) ||
                other.resultCode == resultCode) &&
            (identical(other.resultDetailMessage, resultDetailMessage) ||
                other.resultDetailMessage == resultDetailMessage) &&
            (identical(other.deviceType, deviceType) ||
                other.deviceType == deviceType) &&
            (identical(other.picoId, picoId) || other.picoId == picoId) &&
            (identical(other.authrizNum, authrizNum) ||
                other.authrizNum == authrizNum) &&
            (identical(other.reqAuthDate, reqAuthDate) ||
                other.reqAuthDate == reqAuthDate) &&
            (identical(other.authDepart, authDepart) ||
                other.authDepart == authDepart) &&
            (identical(other.authPerson, authPerson) ||
                other.authPerson == authPerson) &&
            (identical(other.authImpMethod, authImpMethod) ||
                other.authImpMethod == authImpMethod) &&
            (identical(other.nameBp, nameBp) || other.nameBp == nameBp) &&
            (identical(other.dobBp, dobBp) || other.dobBp == dobBp) &&
            (identical(other.genderBp, genderBp) ||
                other.genderBp == genderBp) &&
            (identical(other.addressBp, addressBp) ||
                other.addressBp == addressBp) &&
            (identical(other.authDoc, authDoc) || other.authDoc == authDoc) &&
            (identical(other.authTrailNum, authTrailNum) ||
                other.authTrailNum == authTrailNum) &&
            (identical(other.authVerifyRecord, authVerifyRecord) ||
                other.authVerifyRecord == authVerifyRecord) &&
            (identical(other.certUpdFlg, certUpdFlg) ||
                other.certUpdFlg == certUpdFlg) &&
            (identical(other.mobileCertUpdFlg, mobileCertUpdFlg) ||
                other.mobileCertUpdFlg == mobileCertUpdFlg) &&
            (identical(other.statusCode, statusCode) ||
                other.statusCode == statusCode) &&
            (identical(other.acceptanceNo, acceptanceNo) ||
                other.acceptanceNo == acceptanceNo) &&
            (identical(other.expirationDate, expirationDate) ||
                other.expirationDate == expirationDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        status,
        detail,
        uuId,
        ifid,
        inTransactionId,
        outTransactionId,
        resultCode,
        resultDetailMessage,
        deviceType,
        picoId,
        authrizNum,
        reqAuthDate,
        authDepart,
        authPerson,
        authImpMethod,
        nameBp,
        dobBp,
        genderBp,
        addressBp,
        authDoc,
        authTrailNum,
        authVerifyRecord,
        certUpdFlg,
        mobileCertUpdFlg,
        statusCode,
        acceptanceNo,
        expirationDate
      ]);

  /// Create a copy of JpkiIdentificationApi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JpkiIdentificationApiImplCopyWith<_$JpkiIdentificationApiImpl>
      get copyWith => __$$JpkiIdentificationApiImplCopyWithImpl<
          _$JpkiIdentificationApiImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JpkiIdentificationApiImplToJson(
      this,
    );
  }
}

abstract class _JpkiIdentificationApi implements JpkiIdentificationApi {
  const factory _JpkiIdentificationApi(
      {required final String status,
      required final String detail,
      required final String uuId,
      final String? ifid,
      final String? inTransactionId,
      final String? outTransactionId,
      final String? resultCode,
      final String? resultDetailMessage,
      final String? deviceType,
      final String? picoId,
      final String? authrizNum,
      final String? reqAuthDate,
      final String? authDepart,
      final String? authPerson,
      final String? authImpMethod,
      final String? nameBp,
      final String? dobBp,
      final String? genderBp,
      final String? addressBp,
      final String? authDoc,
      final String? authTrailNum,
      final String? authVerifyRecord,
      final String? certUpdFlg,
      final String? mobileCertUpdFlg,
      final String? statusCode,
      final String? acceptanceNo,
      final String? expirationDate}) = _$JpkiIdentificationApiImpl;

  factory _JpkiIdentificationApi.fromJson(Map<String, dynamic> json) =
      _$JpkiIdentificationApiImpl.fromJson;

// ステータス
  @override
  String get status; // 詳細
  @override
  String get detail; // UUID
  @override
  String get uuId; // インタフェースID
  @override
  String? get ifid; // 入力トランザクションID
  @override
  String? get inTransactionId; // 出力トランザクションID
  @override
  String? get outTransactionId; // BizPICO結果コード
  @override
  String? get resultCode; // 結果詳細メッセージ
  @override
  String? get resultDetailMessage; // デバイス種別
  @override
  String? get deviceType; // PICO ID
  @override
  String? get picoId; // 本人確認番号
  @override
  String? get authrizNum; // 本人確認依頼受信日時
  @override
  String? get reqAuthDate; // 本人確認責任部署
  @override
  String? get authDepart; // 本人確認責任者
  @override
  String? get authPerson; // 本人確認実施方法
  @override
  String? get authImpMethod; // 署名用証明書の氏名
  @override
  String? get nameBp; // 署名用証明書の生年月日
  @override
  String? get dobBp; // 署名用証明書の性別
  @override
  String? get genderBp; // 署名用証明書の住所
  @override
  String? get addressBp; // 本人確認書類名称
  @override
  String? get authDoc; // 本人確認証跡番号
  @override
  String? get authTrailNum; // 署名検証者での本人確認記録
  @override
  String? get authVerifyRecord; // カード用証明書更新フラグ
  @override
  String? get certUpdFlg; // スマホ用証明書更新フラグ
  @override
  String? get mobileCertUpdFlg; // ステータスコード
  @override
  String? get statusCode; // 受理番号
  @override
  String? get acceptanceNo; // 利用者同意有効期限
  @override
  String? get expirationDate;

  /// Create a copy of JpkiIdentificationApi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JpkiIdentificationApiImplCopyWith<_$JpkiIdentificationApiImpl>
      get copyWith => throw _privateConstructorUsedError;
}
