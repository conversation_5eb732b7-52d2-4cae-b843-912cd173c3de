import 'package:freezed_annotation/freezed_annotation.dart';

part 'jpki_identification_api.freezed.dart';
part 'jpki_identification_api.g.dart';

@freezed
class JpkiIdentificationApi with _$JpkiIdentificationApi {
  const factory JpkiIdentificationApi({
    // ステータス
    required String status,
    // 詳細
    required String detail,
    // UUID
    required String uuId,
    // インタフェースID
    String? ifid,
    // 入力トランザクションID
    String? inTransactionId,
    // 出力トランザクションID
    String? outTransactionId,
    // BizPICO結果コード
    String? resultCode,
    // 結果詳細メッセージ
    String? resultDetailMessage,
    // デバイス種別
    String? deviceType,
    // PICO ID
    String? picoId,
    // 本人確認番号
    String? authrizNum,
    // 本人確認依頼受信日時
    String? reqAuthDate,
    // 本人確認責任部署
    String? authDepart,
    // 本人確認責任者
    String? authPerson,
    // 本人確認実施方法
    String? authImpMethod,
    // 署名用証明書の氏名
    String? nameBp,
    // 署名用証明書の生年月日
    String? dobBp,
    // 署名用証明書の性別
    String? genderBp,
    // 署名用証明書の住所
    String? addressBp,
    // 本人確認書類名称
    String? authDoc,
    // 本人確認証跡番号
    String? authTrailNum,
    // 署名検証者での本人確認記録
    String? authVerifyRecord,
    // カード用証明書更新フラグ
    String? certUpdFlg,
    // スマホ用証明書更新フラグ
    String? mobileCertUpdFlg,
    // ステータスコード
    String? statusCode,
    // 受理番号
    String? acceptanceNo,
    // 利用者同意有効期限
    String? expirationDate,
  }) = _JpkiIdentificationApi;

  factory JpkiIdentificationApi.fromJson(Map<String, dynamic> json) =>
      _$JpkiIdentificationApiFromJson(json);
}
