// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jpki_identification_api.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JpkiIdentificationApiImpl _$$JpkiIdentificationApiImplFromJson(
        Map<String, dynamic> json) =>
    _$JpkiIdentificationApiImpl(
      status: json['status'] as String,
      detail: json['detail'] as String,
      uuId: json['uuId'] as String,
      ifid: json['ifid'] as String?,
      inTransactionId: json['inTransactionId'] as String?,
      outTransactionId: json['outTransactionId'] as String?,
      resultCode: json['resultCode'] as String?,
      resultDetailMessage: json['resultDetailMessage'] as String?,
      deviceType: json['deviceType'] as String?,
      picoId: json['picoId'] as String?,
      authrizNum: json['authrizNum'] as String?,
      reqAuthDate: json['reqAuthDate'] as String?,
      authDepart: json['authDepart'] as String?,
      authPerson: json['authPerson'] as String?,
      authImpMethod: json['authImpMethod'] as String?,
      nameBp: json['nameBp'] as String?,
      dobBp: json['dobBp'] as String?,
      genderBp: json['genderBp'] as String?,
      addressBp: json['addressBp'] as String?,
      authDoc: json['authDoc'] as String?,
      authTrailNum: json['authTrailNum'] as String?,
      authVerifyRecord: json['authVerifyRecord'] as String?,
      certUpdFlg: json['certUpdFlg'] as String?,
      mobileCertUpdFlg: json['mobileCertUpdFlg'] as String?,
      statusCode: json['statusCode'] as String?,
      acceptanceNo: json['acceptanceNo'] as String?,
      expirationDate: json['expirationDate'] as String?,
    );

Map<String, dynamic> _$$JpkiIdentificationApiImplToJson(
        _$JpkiIdentificationApiImpl instance) =>
    <String, dynamic>{
      'status': instance.status,
      'detail': instance.detail,
      'uuId': instance.uuId,
      'ifid': instance.ifid,
      'inTransactionId': instance.inTransactionId,
      'outTransactionId': instance.outTransactionId,
      'resultCode': instance.resultCode,
      'resultDetailMessage': instance.resultDetailMessage,
      'deviceType': instance.deviceType,
      'picoId': instance.picoId,
      'authrizNum': instance.authrizNum,
      'reqAuthDate': instance.reqAuthDate,
      'authDepart': instance.authDepart,
      'authPerson': instance.authPerson,
      'authImpMethod': instance.authImpMethod,
      'nameBp': instance.nameBp,
      'dobBp': instance.dobBp,
      'genderBp': instance.genderBp,
      'addressBp': instance.addressBp,
      'authDoc': instance.authDoc,
      'authTrailNum': instance.authTrailNum,
      'authVerifyRecord': instance.authVerifyRecord,
      'certUpdFlg': instance.certUpdFlg,
      'mobileCertUpdFlg': instance.mobileCertUpdFlg,
      'statusCode': instance.statusCode,
      'acceptanceNo': instance.acceptanceNo,
      'expirationDate': instance.expirationDate,
    };
