// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jpki_consent_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$JpkiConsentStatus {
  List<String> get jpkiAgreeTerms => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> jpkiAgreeTerms) required,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> jpkiAgreeTerms)? required,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> jpkiAgreeTerms)? required,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JpkiConsentStatusRequired value) required,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JpkiConsentStatusRequired value)? required,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JpkiConsentStatusRequired value)? required,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of JpkiConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JpkiConsentStatusCopyWith<JpkiConsentStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JpkiConsentStatusCopyWith<$Res> {
  factory $JpkiConsentStatusCopyWith(
          JpkiConsentStatus value, $Res Function(JpkiConsentStatus) then) =
      _$JpkiConsentStatusCopyWithImpl<$Res, JpkiConsentStatus>;
  @useResult
  $Res call({List<String> jpkiAgreeTerms});
}

/// @nodoc
class _$JpkiConsentStatusCopyWithImpl<$Res, $Val extends JpkiConsentStatus>
    implements $JpkiConsentStatusCopyWith<$Res> {
  _$JpkiConsentStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JpkiConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jpkiAgreeTerms = null,
  }) {
    return _then(_value.copyWith(
      jpkiAgreeTerms: null == jpkiAgreeTerms
          ? _value.jpkiAgreeTerms
          : jpkiAgreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JpkiConsentStatusRequiredImplCopyWith<$Res>
    implements $JpkiConsentStatusCopyWith<$Res> {
  factory _$$JpkiConsentStatusRequiredImplCopyWith(
          _$JpkiConsentStatusRequiredImpl value,
          $Res Function(_$JpkiConsentStatusRequiredImpl) then) =
      __$$JpkiConsentStatusRequiredImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String> jpkiAgreeTerms});
}

/// @nodoc
class __$$JpkiConsentStatusRequiredImplCopyWithImpl<$Res>
    extends _$JpkiConsentStatusCopyWithImpl<$Res,
        _$JpkiConsentStatusRequiredImpl>
    implements _$$JpkiConsentStatusRequiredImplCopyWith<$Res> {
  __$$JpkiConsentStatusRequiredImplCopyWithImpl(
      _$JpkiConsentStatusRequiredImpl _value,
      $Res Function(_$JpkiConsentStatusRequiredImpl) _then)
      : super(_value, _then);

  /// Create a copy of JpkiConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jpkiAgreeTerms = null,
  }) {
    return _then(_$JpkiConsentStatusRequiredImpl(
      jpkiAgreeTerms: null == jpkiAgreeTerms
          ? _value._jpkiAgreeTerms
          : jpkiAgreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$JpkiConsentStatusRequiredImpl implements JpkiConsentStatusRequired {
  const _$JpkiConsentStatusRequiredImpl(
      {required final List<String> jpkiAgreeTerms})
      : _jpkiAgreeTerms = jpkiAgreeTerms;

  final List<String> _jpkiAgreeTerms;
  @override
  List<String> get jpkiAgreeTerms {
    if (_jpkiAgreeTerms is EqualUnmodifiableListView) return _jpkiAgreeTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_jpkiAgreeTerms);
  }

  @override
  String toString() {
    return 'JpkiConsentStatus.required(jpkiAgreeTerms: $jpkiAgreeTerms)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JpkiConsentStatusRequiredImpl &&
            const DeepCollectionEquality()
                .equals(other._jpkiAgreeTerms, _jpkiAgreeTerms));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_jpkiAgreeTerms));

  /// Create a copy of JpkiConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JpkiConsentStatusRequiredImplCopyWith<_$JpkiConsentStatusRequiredImpl>
      get copyWith => __$$JpkiConsentStatusRequiredImplCopyWithImpl<
          _$JpkiConsentStatusRequiredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> jpkiAgreeTerms) required,
  }) {
    return required(jpkiAgreeTerms);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> jpkiAgreeTerms)? required,
  }) {
    return required?.call(jpkiAgreeTerms);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> jpkiAgreeTerms)? required,
    required TResult orElse(),
  }) {
    if (required != null) {
      return required(jpkiAgreeTerms);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(JpkiConsentStatusRequired value) required,
  }) {
    return required(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(JpkiConsentStatusRequired value)? required,
  }) {
    return required?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(JpkiConsentStatusRequired value)? required,
    required TResult orElse(),
  }) {
    if (required != null) {
      return required(this);
    }
    return orElse();
  }
}

abstract class JpkiConsentStatusRequired implements JpkiConsentStatus {
  const factory JpkiConsentStatusRequired(
          {required final List<String> jpkiAgreeTerms}) =
      _$JpkiConsentStatusRequiredImpl;

  @override
  List<String> get jpkiAgreeTerms;

  /// Create a copy of JpkiConsentStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JpkiConsentStatusRequiredImplCopyWith<_$JpkiConsentStatusRequiredImpl>
      get copyWith => throw _privateConstructorUsedError;
}
