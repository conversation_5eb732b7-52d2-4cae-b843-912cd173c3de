// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authorization_status.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AuthorizationStatusImpl _$$AuthorizationStatusImplFromJson(
        Map<String, dynamic> json) =>
    _$AuthorizationStatusImpl(
      authorizationStatus: json['authorizationStatus'] as String,
      hasNoAuth: json['hasNoAuth'] as bool,
      hasInquiryAuth: json['hasInquiryAuth'] as bool,
      hasTransferAuth: json['hasTransferAuth'] as bool,
      hasApprovalAuth: json['hasApprovalAuth'] as bool,
      hasKigyoCd: json['hasKigyoCd'] as bool,
      errorMessage: json['errorMessage'] as String?,
      errorCode: json['errorCode'] as String?,
      hasFaq: json['hasFaq'] as bool?,
    );

Map<String, dynamic> _$$AuthorizationStatusImplToJson(
        _$AuthorizationStatusImpl instance) =>
    <String, dynamic>{
      'authorizationStatus': instance.authorizationStatus,
      'hasNoAuth': instance.hasNoAuth,
      'hasInquiryAuth': instance.hasInquiryAuth,
      'hasTransferAuth': instance.hasTransferAuth,
      'hasApprovalAuth': instance.hasApprovalAuth,
      'hasKigyoCd': instance.hasKigyoCd,
      'errorMessage': instance.errorMessage,
      'errorCode': instance.errorCode,
      'hasFaq': instance.hasFaq,
    };
