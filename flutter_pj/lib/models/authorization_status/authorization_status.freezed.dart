// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'authorization_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AuthorizationStatus _$AuthorizationStatusFromJson(Map<String, dynamic> json) {
  return _AuthorizationStatus.fromJson(json);
}

/// @nodoc
mixin _$AuthorizationStatus {
// 認可ステータス（01: 要認可、 02: 認可不要）
  String get authorizationStatus =>
      throw _privateConstructorUsedError; // Web21保有権限なし
  bool get hasNoAuth => throw _privateConstructorUsedError; // 口座照会権限
  bool get hasInquiryAuth => throw _privateConstructorUsedError; // 振込権限
  bool get hasTransferAuth => throw _privateConstructorUsedError; // 承認権限
  bool get hasApprovalAuth =>
      throw _privateConstructorUsedError; // Web21企業コード有無
  bool get hasKigyoCd =>
      throw _privateConstructorUsedError; // Web21企業コードがない際のエラー情報
// クラスがBFFとのIFの役割を果たしており、IFの独立性を担保するためにもエラー情報をAppError型に格納しない
  String? get errorMessage => throw _privateConstructorUsedError;
  String? get errorCode => throw _privateConstructorUsedError;
  bool? get hasFaq => throw _privateConstructorUsedError;

  /// Serializes this AuthorizationStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AuthorizationStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AuthorizationStatusCopyWith<AuthorizationStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AuthorizationStatusCopyWith<$Res> {
  factory $AuthorizationStatusCopyWith(
          AuthorizationStatus value, $Res Function(AuthorizationStatus) then) =
      _$AuthorizationStatusCopyWithImpl<$Res, AuthorizationStatus>;
  @useResult
  $Res call(
      {String authorizationStatus,
      bool hasNoAuth,
      bool hasInquiryAuth,
      bool hasTransferAuth,
      bool hasApprovalAuth,
      bool hasKigyoCd,
      String? errorMessage,
      String? errorCode,
      bool? hasFaq});
}

/// @nodoc
class _$AuthorizationStatusCopyWithImpl<$Res, $Val extends AuthorizationStatus>
    implements $AuthorizationStatusCopyWith<$Res> {
  _$AuthorizationStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AuthorizationStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authorizationStatus = null,
    Object? hasNoAuth = null,
    Object? hasInquiryAuth = null,
    Object? hasTransferAuth = null,
    Object? hasApprovalAuth = null,
    Object? hasKigyoCd = null,
    Object? errorMessage = freezed,
    Object? errorCode = freezed,
    Object? hasFaq = freezed,
  }) {
    return _then(_value.copyWith(
      authorizationStatus: null == authorizationStatus
          ? _value.authorizationStatus
          : authorizationStatus // ignore: cast_nullable_to_non_nullable
              as String,
      hasNoAuth: null == hasNoAuth
          ? _value.hasNoAuth
          : hasNoAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasInquiryAuth: null == hasInquiryAuth
          ? _value.hasInquiryAuth
          : hasInquiryAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasTransferAuth: null == hasTransferAuth
          ? _value.hasTransferAuth
          : hasTransferAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasApprovalAuth: null == hasApprovalAuth
          ? _value.hasApprovalAuth
          : hasApprovalAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasKigyoCd: null == hasKigyoCd
          ? _value.hasKigyoCd
          : hasKigyoCd // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      hasFaq: freezed == hasFaq
          ? _value.hasFaq
          : hasFaq // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AuthorizationStatusImplCopyWith<$Res>
    implements $AuthorizationStatusCopyWith<$Res> {
  factory _$$AuthorizationStatusImplCopyWith(_$AuthorizationStatusImpl value,
          $Res Function(_$AuthorizationStatusImpl) then) =
      __$$AuthorizationStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String authorizationStatus,
      bool hasNoAuth,
      bool hasInquiryAuth,
      bool hasTransferAuth,
      bool hasApprovalAuth,
      bool hasKigyoCd,
      String? errorMessage,
      String? errorCode,
      bool? hasFaq});
}

/// @nodoc
class __$$AuthorizationStatusImplCopyWithImpl<$Res>
    extends _$AuthorizationStatusCopyWithImpl<$Res, _$AuthorizationStatusImpl>
    implements _$$AuthorizationStatusImplCopyWith<$Res> {
  __$$AuthorizationStatusImplCopyWithImpl(_$AuthorizationStatusImpl _value,
      $Res Function(_$AuthorizationStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of AuthorizationStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? authorizationStatus = null,
    Object? hasNoAuth = null,
    Object? hasInquiryAuth = null,
    Object? hasTransferAuth = null,
    Object? hasApprovalAuth = null,
    Object? hasKigyoCd = null,
    Object? errorMessage = freezed,
    Object? errorCode = freezed,
    Object? hasFaq = freezed,
  }) {
    return _then(_$AuthorizationStatusImpl(
      authorizationStatus: null == authorizationStatus
          ? _value.authorizationStatus
          : authorizationStatus // ignore: cast_nullable_to_non_nullable
              as String,
      hasNoAuth: null == hasNoAuth
          ? _value.hasNoAuth
          : hasNoAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasInquiryAuth: null == hasInquiryAuth
          ? _value.hasInquiryAuth
          : hasInquiryAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasTransferAuth: null == hasTransferAuth
          ? _value.hasTransferAuth
          : hasTransferAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasApprovalAuth: null == hasApprovalAuth
          ? _value.hasApprovalAuth
          : hasApprovalAuth // ignore: cast_nullable_to_non_nullable
              as bool,
      hasKigyoCd: null == hasKigyoCd
          ? _value.hasKigyoCd
          : hasKigyoCd // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      errorCode: freezed == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String?,
      hasFaq: freezed == hasFaq
          ? _value.hasFaq
          : hasFaq // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AuthorizationStatusImpl implements _AuthorizationStatus {
  const _$AuthorizationStatusImpl(
      {required this.authorizationStatus,
      required this.hasNoAuth,
      required this.hasInquiryAuth,
      required this.hasTransferAuth,
      required this.hasApprovalAuth,
      required this.hasKigyoCd,
      this.errorMessage,
      this.errorCode,
      this.hasFaq});

  factory _$AuthorizationStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$AuthorizationStatusImplFromJson(json);

// 認可ステータス（01: 要認可、 02: 認可不要）
  @override
  final String authorizationStatus;
// Web21保有権限なし
  @override
  final bool hasNoAuth;
// 口座照会権限
  @override
  final bool hasInquiryAuth;
// 振込権限
  @override
  final bool hasTransferAuth;
// 承認権限
  @override
  final bool hasApprovalAuth;
// Web21企業コード有無
  @override
  final bool hasKigyoCd;
// Web21企業コードがない際のエラー情報
// クラスがBFFとのIFの役割を果たしており、IFの独立性を担保するためにもエラー情報をAppError型に格納しない
  @override
  final String? errorMessage;
  @override
  final String? errorCode;
  @override
  final bool? hasFaq;

  @override
  String toString() {
    return 'AuthorizationStatus(authorizationStatus: $authorizationStatus, hasNoAuth: $hasNoAuth, hasInquiryAuth: $hasInquiryAuth, hasTransferAuth: $hasTransferAuth, hasApprovalAuth: $hasApprovalAuth, hasKigyoCd: $hasKigyoCd, errorMessage: $errorMessage, errorCode: $errorCode, hasFaq: $hasFaq)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthorizationStatusImpl &&
            (identical(other.authorizationStatus, authorizationStatus) ||
                other.authorizationStatus == authorizationStatus) &&
            (identical(other.hasNoAuth, hasNoAuth) ||
                other.hasNoAuth == hasNoAuth) &&
            (identical(other.hasInquiryAuth, hasInquiryAuth) ||
                other.hasInquiryAuth == hasInquiryAuth) &&
            (identical(other.hasTransferAuth, hasTransferAuth) ||
                other.hasTransferAuth == hasTransferAuth) &&
            (identical(other.hasApprovalAuth, hasApprovalAuth) ||
                other.hasApprovalAuth == hasApprovalAuth) &&
            (identical(other.hasKigyoCd, hasKigyoCd) ||
                other.hasKigyoCd == hasKigyoCd) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.hasFaq, hasFaq) || other.hasFaq == hasFaq));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      authorizationStatus,
      hasNoAuth,
      hasInquiryAuth,
      hasTransferAuth,
      hasApprovalAuth,
      hasKigyoCd,
      errorMessage,
      errorCode,
      hasFaq);

  /// Create a copy of AuthorizationStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthorizationStatusImplCopyWith<_$AuthorizationStatusImpl> get copyWith =>
      __$$AuthorizationStatusImplCopyWithImpl<_$AuthorizationStatusImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AuthorizationStatusImplToJson(
      this,
    );
  }
}

abstract class _AuthorizationStatus implements AuthorizationStatus {
  const factory _AuthorizationStatus(
      {required final String authorizationStatus,
      required final bool hasNoAuth,
      required final bool hasInquiryAuth,
      required final bool hasTransferAuth,
      required final bool hasApprovalAuth,
      required final bool hasKigyoCd,
      final String? errorMessage,
      final String? errorCode,
      final bool? hasFaq}) = _$AuthorizationStatusImpl;

  factory _AuthorizationStatus.fromJson(Map<String, dynamic> json) =
      _$AuthorizationStatusImpl.fromJson;

// 認可ステータス（01: 要認可、 02: 認可不要）
  @override
  String get authorizationStatus; // Web21保有権限なし
  @override
  bool get hasNoAuth; // 口座照会権限
  @override
  bool get hasInquiryAuth; // 振込権限
  @override
  bool get hasTransferAuth; // 承認権限
  @override
  bool get hasApprovalAuth; // Web21企業コード有無
  @override
  bool get hasKigyoCd; // Web21企業コードがない際のエラー情報
// クラスがBFFとのIFの役割を果たしており、IFの独立性を担保するためにもエラー情報をAppError型に格納しない
  @override
  String? get errorMessage;
  @override
  String? get errorCode;
  @override
  bool? get hasFaq;

  /// Create a copy of AuthorizationStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthorizationStatusImplCopyWith<_$AuthorizationStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
