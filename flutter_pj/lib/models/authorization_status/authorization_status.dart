import 'package:freezed_annotation/freezed_annotation.dart';

part 'authorization_status.freezed.dart';
part 'authorization_status.g.dart';

@freezed

/// web21権限情報を持ち回るクラス
class AuthorizationStatus with _$AuthorizationStatus {
  const factory AuthorizationStatus({
    // 認可ステータス（01: 要認可、 02: 認可不要）
    required String authorizationStatus,
    // Web21保有権限なし
    required bool hasNoAuth,
    // 口座照会権限
    required bool hasInquiryAuth,
    // 振込権限
    required bool hasTransferAuth,
    // 承認権限
    required bool hasApprovalAuth,
    // Web21企業コード有無
    required bool hasKigyoCd,
    // Web21企業コードがない際のエラー情報
    // クラスがBFFとのIFの役割を果たしており、IFの独立性を担保するためにもエラー情報をAppError型に格納しない
    String? errorMessage,
    String? errorCode,
    bool? hasFaq,
  }) = _AuthorizationStatus;

  factory AuthorizationStatus.fromJson(Map<String, dynamic> json) =>
      _$AuthorizationStatusFromJson(json);
}
