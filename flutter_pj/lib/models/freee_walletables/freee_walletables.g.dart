// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freee_walletables.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FreeeWalletablesImpl _$$FreeeWalletablesImplFromJson(
        Map<String, dynamic> json) =>
    _$FreeeWalletablesImpl(
      walletables: (json['walletables'] as List<dynamic>)
          .map(
              (e) => FreeeWalletablesDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$FreeeWalletablesImplToJson(
        _$FreeeWalletablesImpl instance) =>
    <String, dynamic>{
      'walletables': instance.walletables,
    };

_$FreeeWalletablesDetailImpl _$$FreeeWalletablesDetailImplFromJson(
        Map<String, dynamic> json) =>
    _$FreeeWalletablesDetailImpl(
      id: (json['id'] as num).toInt(),
      accountName: json['name'] as String,
      bankId: (json['bankId'] as num?)?.toInt(),
      inquiryCategory: json['type'] as String,
      lastBalance: (json['lastBalance'] as num?)?.toInt(),
      walletableBalance: (json['walletableBalance'] as num?)?.toInt(),
      serverDateTime: json['serverDateTime'] as String?,
      lastSyncedAt: json['lastSyncedAt'] as String?,
      bankName: json['bankName'] as String?,
      isHidden: json['isHidden'] as bool,
      displayOrder: (json['displayOrder'] as num).toInt(),
      accountApiType: json['accountApiType'] as String,
      syncStatus: json['syncStatus'] as String?,
    );

Map<String, dynamic> _$$FreeeWalletablesDetailImplToJson(
        _$FreeeWalletablesDetailImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.accountName,
      'bankId': instance.bankId,
      'type': instance.inquiryCategory,
      'lastBalance': instance.lastBalance,
      'walletableBalance': instance.walletableBalance,
      'serverDateTime': instance.serverDateTime,
      'lastSyncedAt': instance.lastSyncedAt,
      'bankName': instance.bankName,
      'isHidden': instance.isHidden,
      'displayOrder': instance.displayOrder,
      'accountApiType': instance.accountApiType,
      'syncStatus': instance.syncStatus,
    };
