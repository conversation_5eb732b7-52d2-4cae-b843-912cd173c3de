import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/bank_account_base_info/bank_account_base_info.dart';
import 'package:dtp_app/models/freee_credit_card_expense/freee_credit_card_expense.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_walletables.freezed.dart';
part 'freee_walletables.g.dart';

@freezed
class FreeeWalletables with _$FreeeWalletables {
  const factory FreeeWalletables({
    required List<FreeeWalletablesDetail> walletables,
  }) = _FreeeWalletables;

  factory FreeeWalletables.fromJson(Map<String, dynamic> json) =>
      _$FreeeWalletablesFromJson(json);
}

@freezed
class FreeeWalletablesDetail with _$FreeeWalletablesDetail {
  const factory FreeeWalletablesDetail({
    ///口座ID
    required int id,

    ///口座名
    @JsonKey(name: 'name') required String accountName,

    ///サービスID
    int? bankId,

    ///照会区分
    @JsonKey(name: 'type') required String inquiryCategory,

    ///同期残高
    int? lastBalance,

    ///登録残高
    int? walletableBalance,

    /// 取得時刻
    String? serverDateTime,

    /// 最終同期日時
    String? lastSyncedAt,

    /// 金融機関名
    String? bankName,

    /// 口座残高カード表示フラグ
    /// true:非表示, false:表示
    required bool isHidden,

    /// 一意の番号。並び順に使用
    required int displayOrder,

    /// 口座種別
    /// [ 'web21', 'freee' ]
    required String accountApiType,

    /// freee-金融機関の連携期限切れの際に再連携ボタン表示フラグ(0:ボタン非表示, 1:ボタン表示)
    String? syncStatus,
  }) = _FreeeWalletablesDetail;

  factory FreeeWalletablesDetail.fromJson(Map<String, dynamic> json) =>
      _$FreeeWalletablesDetailFromJson(json);
}

extension FreeeWalletablesDetailList on List<FreeeWalletablesDetail> {
  ///同期残高合計
  int? getTotalLastBalance() {
    int sum = 0;
    for (final detail in this) {
      sum += detail.lastBalance ?? 0;
    }
    return sum;
  }

  ///List<BankAccountBalanceDetail>に変換する
  List<BankAccountWithBalanceDetail> convertToBankAccount() {
    final bankAccountBalances = <BankAccountWithBalanceDetail>[];
    for (final walletable in this) {
      bankAccountBalances.add(
        BankAccount.withBalanceDetail(
          baseInfo: BankAccountBaseInfo(bankName: walletable.bankName),
          displayConfig: DisplayConfig(
            accountId: walletable.id.toString(),
            displayOrder: walletable.displayOrder,
            displayName: walletable.accountName,
            isHidden: walletable.isHidden,
            accountApiType: AccountApiType.values.byName(
              walletable.accountApiType,
            ),
          ),
          balanceDetail: BankAccountBalanceDetail(
            accountId: walletable.id.toString(),
            bankName: walletable.bankName,
            displayAccountName: walletable.accountName,
            currentBalance: walletable.lastBalance,
            withdrawableBalance: walletable.lastBalance,
            baseDateTime: walletable.serverDateTime,
            lastSyncedAt: walletable.lastSyncedAt,
            isFreeeReConnectFinancialInstitution: walletable.syncStatus == '1',
          ),
        ) as BankAccountWithBalanceDetail,
      );
    }
    return bankAccountBalances;
  }

  /// freee口座一覧情報を、freeeクレジットカード利用額情報へと変換(最大100件)
  List<CreditCardExpense> convertToFreeeCreditCardExpense() {
    // freeeクレジットカードは100件まで
    const creditCardMax = 100;
    final creditCardExpenses = <CreditCardExpense>[];
    for (final walletable in this) {
      creditCardExpenses.add(
        CreditCardExpense(
          id: walletable.id,
          baseDateTime: walletable.serverDateTime!,
          lastSyncedAt: walletable.lastSyncedAt,
          creditCardName: walletable.accountName,
          isLoading: true,
          isFreeeReConnectFinancialInstitution: walletable.syncStatus == '1',
        ),
      );
    }
    return creditCardExpenses.take(creditCardMax).toList();
  }

  // 表示口座数を制限する
  List<FreeeWalletablesDetail> updateIsHiddenFlag({required int max}) {
    int count = 0;
    return map((walletable) {
      if (count < max) {
        if (!walletable.isHidden) {
          count++;
        }
        return walletable;
      } else {
        // 表示口座数の制限を超えた場合はisHiddenをtrueに更新する
        return walletable.copyWith(isHidden: true);
      }
    }).toList();
  }
}

enum InquiryCategory {
  ///銀行口座
  bankAccount,

  ///クレジットカード
  creditCard,

  ///現金
  wallet,
}
