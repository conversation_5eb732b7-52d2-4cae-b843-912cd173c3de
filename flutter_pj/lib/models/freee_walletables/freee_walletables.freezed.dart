// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_walletables.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FreeeWalletables _$FreeeWalletablesFromJson(Map<String, dynamic> json) {
  return _FreeeWalletables.fromJson(json);
}

/// @nodoc
mixin _$FreeeWalletables {
  List<FreeeWalletablesDetail> get walletables =>
      throw _privateConstructorUsedError;

  /// Serializes this FreeeWalletables to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeWalletables
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeWalletablesCopyWith<FreeeWalletables> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeWalletablesCopyWith<$Res> {
  factory $FreeeWalletablesCopyWith(
          FreeeWalletables value, $Res Function(FreeeWalletables) then) =
      _$FreeeWalletablesCopyWithImpl<$Res, FreeeWalletables>;
  @useResult
  $Res call({List<FreeeWalletablesDetail> walletables});
}

/// @nodoc
class _$FreeeWalletablesCopyWithImpl<$Res, $Val extends FreeeWalletables>
    implements $FreeeWalletablesCopyWith<$Res> {
  _$FreeeWalletablesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeWalletables
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? walletables = null,
  }) {
    return _then(_value.copyWith(
      walletables: null == walletables
          ? _value.walletables
          : walletables // ignore: cast_nullable_to_non_nullable
              as List<FreeeWalletablesDetail>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeWalletablesImplCopyWith<$Res>
    implements $FreeeWalletablesCopyWith<$Res> {
  factory _$$FreeeWalletablesImplCopyWith(_$FreeeWalletablesImpl value,
          $Res Function(_$FreeeWalletablesImpl) then) =
      __$$FreeeWalletablesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<FreeeWalletablesDetail> walletables});
}

/// @nodoc
class __$$FreeeWalletablesImplCopyWithImpl<$Res>
    extends _$FreeeWalletablesCopyWithImpl<$Res, _$FreeeWalletablesImpl>
    implements _$$FreeeWalletablesImplCopyWith<$Res> {
  __$$FreeeWalletablesImplCopyWithImpl(_$FreeeWalletablesImpl _value,
      $Res Function(_$FreeeWalletablesImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeWalletables
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? walletables = null,
  }) {
    return _then(_$FreeeWalletablesImpl(
      walletables: null == walletables
          ? _value._walletables
          : walletables // ignore: cast_nullable_to_non_nullable
              as List<FreeeWalletablesDetail>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeWalletablesImpl implements _FreeeWalletables {
  const _$FreeeWalletablesImpl(
      {required final List<FreeeWalletablesDetail> walletables})
      : _walletables = walletables;

  factory _$FreeeWalletablesImpl.fromJson(Map<String, dynamic> json) =>
      _$$FreeeWalletablesImplFromJson(json);

  final List<FreeeWalletablesDetail> _walletables;
  @override
  List<FreeeWalletablesDetail> get walletables {
    if (_walletables is EqualUnmodifiableListView) return _walletables;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_walletables);
  }

  @override
  String toString() {
    return 'FreeeWalletables(walletables: $walletables)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeWalletablesImpl &&
            const DeepCollectionEquality()
                .equals(other._walletables, _walletables));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_walletables));

  /// Create a copy of FreeeWalletables
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeWalletablesImplCopyWith<_$FreeeWalletablesImpl> get copyWith =>
      __$$FreeeWalletablesImplCopyWithImpl<_$FreeeWalletablesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeWalletablesImplToJson(
      this,
    );
  }
}

abstract class _FreeeWalletables implements FreeeWalletables {
  const factory _FreeeWalletables(
          {required final List<FreeeWalletablesDetail> walletables}) =
      _$FreeeWalletablesImpl;

  factory _FreeeWalletables.fromJson(Map<String, dynamic> json) =
      _$FreeeWalletablesImpl.fromJson;

  @override
  List<FreeeWalletablesDetail> get walletables;

  /// Create a copy of FreeeWalletables
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeWalletablesImplCopyWith<_$FreeeWalletablesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FreeeWalletablesDetail _$FreeeWalletablesDetailFromJson(
    Map<String, dynamic> json) {
  return _FreeeWalletablesDetail.fromJson(json);
}

/// @nodoc
mixin _$FreeeWalletablesDetail {
  ///口座ID
  int get id => throw _privateConstructorUsedError;

  ///口座名
  @JsonKey(name: 'name')
  String get accountName => throw _privateConstructorUsedError;

  ///サービスID
  int? get bankId => throw _privateConstructorUsedError;

  ///照会区分
  @JsonKey(name: 'type')
  String get inquiryCategory => throw _privateConstructorUsedError;

  ///同期残高
  int? get lastBalance => throw _privateConstructorUsedError;

  ///登録残高
  int? get walletableBalance => throw _privateConstructorUsedError;

  /// 取得時刻
  String? get serverDateTime => throw _privateConstructorUsedError;

  /// 最終同期日時
  String? get lastSyncedAt => throw _privateConstructorUsedError;

  /// 金融機関名
  String? get bankName => throw _privateConstructorUsedError;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  bool get isHidden => throw _privateConstructorUsedError;

  /// 一意の番号。並び順に使用
  int get displayOrder => throw _privateConstructorUsedError;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  String get accountApiType => throw _privateConstructorUsedError;

  /// freee-金融機関の連携期限切れの際に再連携ボタン表示フラグ(0:ボタン非表示, 1:ボタン表示)
  String? get syncStatus => throw _privateConstructorUsedError;

  /// Serializes this FreeeWalletablesDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeWalletablesDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeWalletablesDetailCopyWith<FreeeWalletablesDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeWalletablesDetailCopyWith<$Res> {
  factory $FreeeWalletablesDetailCopyWith(FreeeWalletablesDetail value,
          $Res Function(FreeeWalletablesDetail) then) =
      _$FreeeWalletablesDetailCopyWithImpl<$Res, FreeeWalletablesDetail>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'name') String accountName,
      int? bankId,
      @JsonKey(name: 'type') String inquiryCategory,
      int? lastBalance,
      int? walletableBalance,
      String? serverDateTime,
      String? lastSyncedAt,
      String? bankName,
      bool isHidden,
      int displayOrder,
      String accountApiType,
      String? syncStatus});
}

/// @nodoc
class _$FreeeWalletablesDetailCopyWithImpl<$Res,
        $Val extends FreeeWalletablesDetail>
    implements $FreeeWalletablesDetailCopyWith<$Res> {
  _$FreeeWalletablesDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeWalletablesDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountName = null,
    Object? bankId = freezed,
    Object? inquiryCategory = null,
    Object? lastBalance = freezed,
    Object? walletableBalance = freezed,
    Object? serverDateTime = freezed,
    Object? lastSyncedAt = freezed,
    Object? bankName = freezed,
    Object? isHidden = null,
    Object? displayOrder = null,
    Object? accountApiType = null,
    Object? syncStatus = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      accountName: null == accountName
          ? _value.accountName
          : accountName // ignore: cast_nullable_to_non_nullable
              as String,
      bankId: freezed == bankId
          ? _value.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as int?,
      inquiryCategory: null == inquiryCategory
          ? _value.inquiryCategory
          : inquiryCategory // ignore: cast_nullable_to_non_nullable
              as String,
      lastBalance: freezed == lastBalance
          ? _value.lastBalance
          : lastBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      walletableBalance: freezed == walletableBalance
          ? _value.walletableBalance
          : walletableBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      serverDateTime: freezed == serverDateTime
          ? _value.serverDateTime
          : serverDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as String,
      syncStatus: freezed == syncStatus
          ? _value.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeWalletablesDetailImplCopyWith<$Res>
    implements $FreeeWalletablesDetailCopyWith<$Res> {
  factory _$$FreeeWalletablesDetailImplCopyWith(
          _$FreeeWalletablesDetailImpl value,
          $Res Function(_$FreeeWalletablesDetailImpl) then) =
      __$$FreeeWalletablesDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'name') String accountName,
      int? bankId,
      @JsonKey(name: 'type') String inquiryCategory,
      int? lastBalance,
      int? walletableBalance,
      String? serverDateTime,
      String? lastSyncedAt,
      String? bankName,
      bool isHidden,
      int displayOrder,
      String accountApiType,
      String? syncStatus});
}

/// @nodoc
class __$$FreeeWalletablesDetailImplCopyWithImpl<$Res>
    extends _$FreeeWalletablesDetailCopyWithImpl<$Res,
        _$FreeeWalletablesDetailImpl>
    implements _$$FreeeWalletablesDetailImplCopyWith<$Res> {
  __$$FreeeWalletablesDetailImplCopyWithImpl(
      _$FreeeWalletablesDetailImpl _value,
      $Res Function(_$FreeeWalletablesDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeWalletablesDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountName = null,
    Object? bankId = freezed,
    Object? inquiryCategory = null,
    Object? lastBalance = freezed,
    Object? walletableBalance = freezed,
    Object? serverDateTime = freezed,
    Object? lastSyncedAt = freezed,
    Object? bankName = freezed,
    Object? isHidden = null,
    Object? displayOrder = null,
    Object? accountApiType = null,
    Object? syncStatus = freezed,
  }) {
    return _then(_$FreeeWalletablesDetailImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      accountName: null == accountName
          ? _value.accountName
          : accountName // ignore: cast_nullable_to_non_nullable
              as String,
      bankId: freezed == bankId
          ? _value.bankId
          : bankId // ignore: cast_nullable_to_non_nullable
              as int?,
      inquiryCategory: null == inquiryCategory
          ? _value.inquiryCategory
          : inquiryCategory // ignore: cast_nullable_to_non_nullable
              as String,
      lastBalance: freezed == lastBalance
          ? _value.lastBalance
          : lastBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      walletableBalance: freezed == walletableBalance
          ? _value.walletableBalance
          : walletableBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      serverDateTime: freezed == serverDateTime
          ? _value.serverDateTime
          : serverDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as String,
      syncStatus: freezed == syncStatus
          ? _value.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeWalletablesDetailImpl implements _FreeeWalletablesDetail {
  const _$FreeeWalletablesDetailImpl(
      {required this.id,
      @JsonKey(name: 'name') required this.accountName,
      this.bankId,
      @JsonKey(name: 'type') required this.inquiryCategory,
      this.lastBalance,
      this.walletableBalance,
      this.serverDateTime,
      this.lastSyncedAt,
      this.bankName,
      required this.isHidden,
      required this.displayOrder,
      required this.accountApiType,
      this.syncStatus});

  factory _$FreeeWalletablesDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$FreeeWalletablesDetailImplFromJson(json);

  ///口座ID
  @override
  final int id;

  ///口座名
  @override
  @JsonKey(name: 'name')
  final String accountName;

  ///サービスID
  @override
  final int? bankId;

  ///照会区分
  @override
  @JsonKey(name: 'type')
  final String inquiryCategory;

  ///同期残高
  @override
  final int? lastBalance;

  ///登録残高
  @override
  final int? walletableBalance;

  /// 取得時刻
  @override
  final String? serverDateTime;

  /// 最終同期日時
  @override
  final String? lastSyncedAt;

  /// 金融機関名
  @override
  final String? bankName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  final bool isHidden;

  /// 一意の番号。並び順に使用
  @override
  final int displayOrder;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  final String accountApiType;

  /// freee-金融機関の連携期限切れの際に再連携ボタン表示フラグ(0:ボタン非表示, 1:ボタン表示)
  @override
  final String? syncStatus;

  @override
  String toString() {
    return 'FreeeWalletablesDetail(id: $id, accountName: $accountName, bankId: $bankId, inquiryCategory: $inquiryCategory, lastBalance: $lastBalance, walletableBalance: $walletableBalance, serverDateTime: $serverDateTime, lastSyncedAt: $lastSyncedAt, bankName: $bankName, isHidden: $isHidden, displayOrder: $displayOrder, accountApiType: $accountApiType, syncStatus: $syncStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeWalletablesDetailImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountName, accountName) ||
                other.accountName == accountName) &&
            (identical(other.bankId, bankId) || other.bankId == bankId) &&
            (identical(other.inquiryCategory, inquiryCategory) ||
                other.inquiryCategory == inquiryCategory) &&
            (identical(other.lastBalance, lastBalance) ||
                other.lastBalance == lastBalance) &&
            (identical(other.walletableBalance, walletableBalance) ||
                other.walletableBalance == walletableBalance) &&
            (identical(other.serverDateTime, serverDateTime) ||
                other.serverDateTime == serverDateTime) &&
            (identical(other.lastSyncedAt, lastSyncedAt) ||
                other.lastSyncedAt == lastSyncedAt) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.displayOrder, displayOrder) ||
                other.displayOrder == displayOrder) &&
            (identical(other.accountApiType, accountApiType) ||
                other.accountApiType == accountApiType) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      accountName,
      bankId,
      inquiryCategory,
      lastBalance,
      walletableBalance,
      serverDateTime,
      lastSyncedAt,
      bankName,
      isHidden,
      displayOrder,
      accountApiType,
      syncStatus);

  /// Create a copy of FreeeWalletablesDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeWalletablesDetailImplCopyWith<_$FreeeWalletablesDetailImpl>
      get copyWith => __$$FreeeWalletablesDetailImplCopyWithImpl<
          _$FreeeWalletablesDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeWalletablesDetailImplToJson(
      this,
    );
  }
}

abstract class _FreeeWalletablesDetail implements FreeeWalletablesDetail {
  const factory _FreeeWalletablesDetail(
      {required final int id,
      @JsonKey(name: 'name') required final String accountName,
      final int? bankId,
      @JsonKey(name: 'type') required final String inquiryCategory,
      final int? lastBalance,
      final int? walletableBalance,
      final String? serverDateTime,
      final String? lastSyncedAt,
      final String? bankName,
      required final bool isHidden,
      required final int displayOrder,
      required final String accountApiType,
      final String? syncStatus}) = _$FreeeWalletablesDetailImpl;

  factory _FreeeWalletablesDetail.fromJson(Map<String, dynamic> json) =
      _$FreeeWalletablesDetailImpl.fromJson;

  ///口座ID
  @override
  int get id;

  ///口座名
  @override
  @JsonKey(name: 'name')
  String get accountName;

  ///サービスID
  @override
  int? get bankId;

  ///照会区分
  @override
  @JsonKey(name: 'type')
  String get inquiryCategory;

  ///同期残高
  @override
  int? get lastBalance;

  ///登録残高
  @override
  int? get walletableBalance;

  /// 取得時刻
  @override
  String? get serverDateTime;

  /// 最終同期日時
  @override
  String? get lastSyncedAt;

  /// 金融機関名
  @override
  String? get bankName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  bool get isHidden;

  /// 一意の番号。並び順に使用
  @override
  int get displayOrder;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  String get accountApiType;

  /// freee-金融機関の連携期限切れの際に再連携ボタン表示フラグ(0:ボタン非表示, 1:ボタン表示)
  @override
  String? get syncStatus;

  /// Create a copy of FreeeWalletablesDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeWalletablesDetailImplCopyWith<_$FreeeWalletablesDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}
