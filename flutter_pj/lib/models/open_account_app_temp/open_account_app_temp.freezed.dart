// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'open_account_app_temp.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OpenAccountAppTemp _$OpenAccountAppTempFromJson(Map<String, dynamic> json) {
  return _OpenAccountAppTemp.fromJson(json);
}

/// @nodoc
mixin _$OpenAccountAppTemp {
  /// 進行中STEP
  String get validationStatus => throw _privateConstructorUsedError;

  /// 本人確認実施者
  String get userType => throw _privateConstructorUsedError;

  /// 本人確認実施済フラグ
  bool get isIdentityVerified => throw _privateConstructorUsedError;

  /// Serializes this OpenAccountAppTemp to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OpenAccountAppTemp
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OpenAccountAppTempCopyWith<OpenAccountAppTemp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OpenAccountAppTempCopyWith<$Res> {
  factory $OpenAccountAppTempCopyWith(
          OpenAccountAppTemp value, $Res Function(OpenAccountAppTemp) then) =
      _$OpenAccountAppTempCopyWithImpl<$Res, OpenAccountAppTemp>;
  @useResult
  $Res call(
      {String validationStatus, String userType, bool isIdentityVerified});
}

/// @nodoc
class _$OpenAccountAppTempCopyWithImpl<$Res, $Val extends OpenAccountAppTemp>
    implements $OpenAccountAppTempCopyWith<$Res> {
  _$OpenAccountAppTempCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OpenAccountAppTemp
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? validationStatus = null,
    Object? userType = null,
    Object? isIdentityVerified = null,
  }) {
    return _then(_value.copyWith(
      validationStatus: null == validationStatus
          ? _value.validationStatus
          : validationStatus // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      isIdentityVerified: null == isIdentityVerified
          ? _value.isIdentityVerified
          : isIdentityVerified // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OpenAccountAppTempImplCopyWith<$Res>
    implements $OpenAccountAppTempCopyWith<$Res> {
  factory _$$OpenAccountAppTempImplCopyWith(_$OpenAccountAppTempImpl value,
          $Res Function(_$OpenAccountAppTempImpl) then) =
      __$$OpenAccountAppTempImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String validationStatus, String userType, bool isIdentityVerified});
}

/// @nodoc
class __$$OpenAccountAppTempImplCopyWithImpl<$Res>
    extends _$OpenAccountAppTempCopyWithImpl<$Res, _$OpenAccountAppTempImpl>
    implements _$$OpenAccountAppTempImplCopyWith<$Res> {
  __$$OpenAccountAppTempImplCopyWithImpl(_$OpenAccountAppTempImpl _value,
      $Res Function(_$OpenAccountAppTempImpl) _then)
      : super(_value, _then);

  /// Create a copy of OpenAccountAppTemp
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? validationStatus = null,
    Object? userType = null,
    Object? isIdentityVerified = null,
  }) {
    return _then(_$OpenAccountAppTempImpl(
      validationStatus: null == validationStatus
          ? _value.validationStatus
          : validationStatus // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      isIdentityVerified: null == isIdentityVerified
          ? _value.isIdentityVerified
          : isIdentityVerified // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OpenAccountAppTempImpl implements _OpenAccountAppTemp {
  const _$OpenAccountAppTempImpl(
      {required this.validationStatus,
      required this.userType,
      required this.isIdentityVerified});

  factory _$OpenAccountAppTempImpl.fromJson(Map<String, dynamic> json) =>
      _$$OpenAccountAppTempImplFromJson(json);

  /// 進行中STEP
  @override
  final String validationStatus;

  /// 本人確認実施者
  @override
  final String userType;

  /// 本人確認実施済フラグ
  @override
  final bool isIdentityVerified;

  @override
  String toString() {
    return 'OpenAccountAppTemp(validationStatus: $validationStatus, userType: $userType, isIdentityVerified: $isIdentityVerified)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OpenAccountAppTempImpl &&
            (identical(other.validationStatus, validationStatus) ||
                other.validationStatus == validationStatus) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.isIdentityVerified, isIdentityVerified) ||
                other.isIdentityVerified == isIdentityVerified));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, validationStatus, userType, isIdentityVerified);

  /// Create a copy of OpenAccountAppTemp
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OpenAccountAppTempImplCopyWith<_$OpenAccountAppTempImpl> get copyWith =>
      __$$OpenAccountAppTempImplCopyWithImpl<_$OpenAccountAppTempImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OpenAccountAppTempImplToJson(
      this,
    );
  }
}

abstract class _OpenAccountAppTemp implements OpenAccountAppTemp {
  const factory _OpenAccountAppTemp(
      {required final String validationStatus,
      required final String userType,
      required final bool isIdentityVerified}) = _$OpenAccountAppTempImpl;

  factory _OpenAccountAppTemp.fromJson(Map<String, dynamic> json) =
      _$OpenAccountAppTempImpl.fromJson;

  /// 進行中STEP
  @override
  String get validationStatus;

  /// 本人確認実施者
  @override
  String get userType;

  /// 本人確認実施済フラグ
  @override
  bool get isIdentityVerified;

  /// Create a copy of OpenAccountAppTemp
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OpenAccountAppTempImplCopyWith<_$OpenAccountAppTempImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
