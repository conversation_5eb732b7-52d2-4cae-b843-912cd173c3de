import 'package:dtp_app/models/identity_verification_reference_number_info/identity_verification_reference_number_info.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'open_account_app_temp.freezed.dart';
part 'open_account_app_temp.g.dart';

/// 口座開設申込の一時保存情報
@freezed
class OpenAccountAppTemp with _$OpenAccountAppTemp {
  const factory OpenAccountAppTemp({
    /// 進行中STEP
    required String validationStatus,

    /// 本人確認実施者
    required String userType,

    /// 本人確認実施済フラグ
    required bool isIdentityVerified,
  }) = _OpenAccountAppTemp;

  factory OpenAccountAppTemp.fromJson(Map<String, dynamic> json) =>
      _$OpenAccountAppTempFromJson(json);
}

/// 本人確認状態の実施済みSTEP数を表す
enum ValidationSteps {
  step1,
  step2,
  step3,
  step4,
  step5,
  undefined;

  bool get isBeneficiary {
    return this == ValidationSteps.step4;
  }
}

/// 本人確認実施状況を表す
enum IdentityVerificationPhases {
  /// JPKI認証が必要
  jpki,

  /// 実施済み
  completed,

  /// 初期状態
  initial;
}

enum ProcedurePerson {
  representative,
  tradingManager,
  beneficiary1,
  beneficiary2,
  beneficiary3,
  beneficiary4,
  notSelected,
}

/// OpenAccountAppTempの拡張メソッド
extension OpenAccountAppTempExt on OpenAccountAppTemp {
  /// 一時保存テーブルの内容から本人確認実施状況を判定する
  /// 手続き番号入力後に呼び出す
  IdentityVerificationPhases getIdentityVerificationPhases(
    ReferenceNumberInfo? referenceNumberInfo,
  ) {
    final step =
        ValidationStatusExt.fromString(validationStatus, isIdentityVerified);
    switch (step) {
      case ValidationSteps.undefined:
        // 値が判定できない場合は初期状態として扱う
        return IdentityVerificationPhases.initial;
      case ValidationSteps.step1:
        // STEP1の場合は初期状態として扱う
        return IdentityVerificationPhases.initial;
      case ValidationSteps.step2:
        // STEP2の場合は本人確認実施済みステータスをチェック
        if (isIdentityVerifiedIncludeHasFault(referenceNumberInfo)) {
          // 本人確認実施済の場合は実施済み画面に遷移
          return IdentityVerificationPhases.completed;
        } else {
          // 本人確認未実施の場合
          return IdentityVerificationPhases.jpki;
        }
      case ValidationSteps.step3:
        // STEP3の場合は本人確認実施済み画面に遷移
        return IdentityVerificationPhases.completed;
      case ValidationSteps.step4:
        // STEP4の場合は本人確認実施済みステータスをチェック
        if (isIdentityVerifiedIncludeHasFault(referenceNumberInfo)) {
          // 本人確認実施済みの場合
          return IdentityVerificationPhases.completed;
        } else {
          // 本人確認未実施の場合
          return IdentityVerificationPhases.jpki;
        }
      case ValidationSteps.step5:
        // STEP5の場合は本人確認実施済み画面に遷移
        return IdentityVerificationPhases.completed;
    }
  }

  ProcedurePerson getBeneficiary(String referenceNumber) {
    final lastIndex = referenceNumber.length - 1;
    final lastChar = referenceNumber.substring(lastIndex);

    if (lastChar == '1') {
      return ProcedurePerson.beneficiary1;
    }
    if (lastChar == '2') {
      return ProcedurePerson.beneficiary2;
    }
    if (lastChar == '3') {
      return ProcedurePerson.beneficiary3;
    }
    if (lastChar == '4') {
      return ProcedurePerson.beneficiary4;
    }

    return ProcedurePerson.notSelected;
  }

  // 口座開設情報から手続き者属性フラグを取得する
  String getUserType(String referenceNumber) {
    final step = ValidationStatusExt.fromString(validationStatus);

    if (step == ValidationSteps.step2) {
      if (userType == 'REPRESENTATIVE') {
        // STEP2かつ本人確認実施者が代表者の場合、userTypeに01を入れる
        return ProcedurePerson.representative.changeStatus();
      } else {
        // STEP2かつ本人確認実施者が取引責任者（代理人）の場合userTypeに02を入れる
        return ProcedurePerson.tradingManager.changeStatus();
      }
    }

    // step4の時は実質的支配者が本人確認を行う。
    if (step == ValidationSteps.step4) {
      // お手続き番号の末尾に応じてuserTypeを入れる
      return getBeneficiary(referenceNumber).changeStatus();
    }

    return ProcedurePerson.notSelected.changeStatus();
  }

  // 口座開設構内審査ステータスの不備情報も加味して本人情報実施済確認を行う
  bool isIdentityVerifiedIncludeHasFault(
    ReferenceNumberInfo? referenceNumberInfo,
  ) {
    switch (userType) {
      // 代表者の場合
      case 'REPRESENTATIVE':
        // 不備情報がなく、本人確認済の場合本人確認済
        if (referenceNumberInfo != null &&
            !referenceNumberInfo.isRepresentativeHasFault &&
            isIdentityVerified) {
          return true;
        }
        return false;
      // 取引責任者の場合
      case 'AGENT':
        // 不備情報がなく、本人確認済の場合本人確認済
        if (referenceNumberInfo != null &&
            !referenceNumberInfo.isAgentHasFault &&
            isIdentityVerified) {
          return true;
        }
        return false;
      // 実質的支配者1の場合
      case 'BENEFICIARY1':
        // 不備情報がなく、本人確認済の場合本人確認済
        if (referenceNumberInfo != null &&
            !referenceNumberInfo.isBeneficiary1HasFault &&
            isIdentityVerified) {
          return true;
        }
        return false;
      // 実質的支配者2の場合
      case 'BENEFICIARY2':
        // 不備情報がなく、本人確認済の場合本人確認済
        if (referenceNumberInfo != null &&
            !referenceNumberInfo.isBeneficiary2HasFault &&
            isIdentityVerified) {
          return true;
        }
        return false;
      // 実質的支配者3の場合
      case 'BENEFICIARY3':
        // 不備情報がなく、本人確認済の場合本人確認済
        if (referenceNumberInfo != null &&
            !referenceNumberInfo.isBeneficiary3HasFault &&
            isIdentityVerified) {
          return true;
        }
        return false;
      // 実質的支配者4の場合
      case 'BENEFICIARY4':
        // 不備情報がなく、本人確認済の場合本人確認済
        if (referenceNumberInfo != null &&
            !referenceNumberInfo.isBeneficiary4HasFault &&
            isIdentityVerified) {
          return true;
        }
        return false;
      // userTypeが正しくない時は本人確認未実施
      default:
        return false;
    }
  }
}

/// ValidationStatusの拡張メソッド
extension ValidationStatusExt on ValidationSteps? {
  /// 本人確認実施済みSTEP数をenumに変換する
  static ValidationSteps fromString(String value, [bool? identityVerified]) {
    switch (value) {
      case 'STEP1':
        return ValidationSteps.step1;
      case 'STEP2':
        return ValidationSteps.step2;
      case 'STEP3':
        return ValidationSteps.step3;
      case 'STEP4':
        return ValidationSteps.step4;
      case 'STEP5':
        return ValidationSteps.step5;
      default:
        if (identityVerified != null && identityVerified) {
          //identityVerified=trueならば本人確認ずみ画面へ遷移させる。
          return ValidationSteps.step5;
        }
        // いずれにも合致しなければ判定不能とする
        return ValidationSteps.undefined;
    }
  }
}

/// 手続き者属性判定の拡張メソッド
// APIセット時はString配列にて送信する必要があるため変換する
extension ValidateProcedurePersonExt on ProcedurePerson {
  String changeStatus() {
    switch (this) {
      // どれにも当てはまらない場合はバリデーションを発生させるため空文字に設定する
      case ProcedurePerson.notSelected:
        return '';
      case ProcedurePerson.representative:
        return '01';
      case ProcedurePerson.tradingManager:
        return '02';
      case ProcedurePerson.beneficiary1:
        return '03';
      case ProcedurePerson.beneficiary2:
        return '04';
      case ProcedurePerson.beneficiary3:
        return '05';
      case ProcedurePerson.beneficiary4:
        return '06';
    }
  }
}
