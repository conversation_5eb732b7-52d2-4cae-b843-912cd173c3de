// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vdid_login_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VdidLoginResponseImpl _$$VdidLoginResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$VdidLoginResponseImpl(
      ukeId: json['UkeID'] as String?,
      gyoumuCode: json['GymCd'] as String?,
      sessionId: json['SessionId'] as String?,
      apNextScreenId: json['APNextScrID'] as String?,
      outPageId: json['OutPageId'] as String?,
      hasErrorMessage: json['ErrMesUmuF'] as String?,
      errorMessageId: json['ErrMesID'] as String? ?? ErrorInfo.defaultErrorCode,
      errorTarget:
          json['ErrTaisyo'] as String? ?? ErrorInfo.defaultErrorMessage,
      currentSessionId: json['_W_CurSessionID'] as String?,
      isOtpLateActivationEnable: json['_W_ActivateButtonFlag'] == null
          ? false
          : _isOtpActivateEnabledFromJson(
              json['_W_ActivateButtonFlag'] as String?),
      user: json['User'] as String?,
      redirectUrl: json['RedirectURL'] as String?,
      otpSerialNo: json['OtpSerialNo'] as String?,
      otpKind: json['OtpKind'] == null
          ? VdOtpKind.none
          : _otpKindFromJson(json['OtpKind'] as String?),
      otpActivateKigen: json['OtpActivateKigen'] as String?,
      otpHuchakueKbn: json['OtpHuchakueKbn'] == null
          ? VdOtpDeliveryType.outOfTarget
          : _otpDeliveryTypeFromJson(json['OtpHuchakueKbn'] as String?),
      otpHuchakueRsn: json['OtpHuchakueRsn'] == null
          ? null
          : _otpHuchakueRsnFromJson(json['OtpHuchakueRsn'] as String?),
      fromVdParameter: json['from_vd_param'] as String?,
    );

Map<String, dynamic> _$$VdidLoginResponseImplToJson(
        _$VdidLoginResponseImpl instance) =>
    <String, dynamic>{
      'UkeID': instance.ukeId,
      'GymCd': instance.gyoumuCode,
      'SessionId': instance.sessionId,
      'APNextScrID': instance.apNextScreenId,
      'OutPageId': instance.outPageId,
      'ErrMesUmuF': instance.hasErrorMessage,
      'ErrMesID': instance.errorMessageId,
      'ErrTaisyo': instance.errorTarget,
      '_W_CurSessionID': instance.currentSessionId,
      '_W_ActivateButtonFlag': instance.isOtpLateActivationEnable,
      'User': instance.user,
      'RedirectURL': instance.redirectUrl,
      'OtpSerialNo': instance.otpSerialNo,
      'OtpKind': _$VdOtpKindEnumMap[instance.otpKind]!,
      'OtpActivateKigen': instance.otpActivateKigen,
      'OtpHuchakueKbn': _$VdOtpDeliveryTypeEnumMap[instance.otpHuchakueKbn]!,
      'OtpHuchakueRsn': instance.otpHuchakueRsn,
      'from_vd_param': instance.fromVdParameter,
    };

const _$VdOtpKindEnumMap = {
  VdOtpKind.card: 'card',
  VdOtpKind.app: 'app',
  VdOtpKind.token: 'token',
  VdOtpKind.none: 'none',
};

const _$VdOtpDeliveryTypeEnumMap = {
  VdOtpDeliveryType.received: 'received',
  VdOtpDeliveryType.nonDelivery: 'nonDelivery',
  VdOtpDeliveryType.outOfTarget: 'outOfTarget',
};
