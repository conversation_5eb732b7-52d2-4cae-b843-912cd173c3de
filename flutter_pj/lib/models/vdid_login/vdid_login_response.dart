import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/vdid_login_exception/vdid_login_exception.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'vdid_login_response.freezed.dart';
part 'vdid_login_response.g.dart';

/// OTPの認証種別
enum VdOtpKind {
  card,
  app,
  token,
  // otp利用なし
  none,
}

/// OTP不着区分
enum VdOtpDeliveryType {
  // 不着でない
  received,
  // 不着
  nonDelivery,
  // その他（不着区分未設定）
  outOfTarget,
}

@freezed
sealed class VdidLoginResponse with _$VdidLoginResponse {
  const factory VdidLoginResponse({
    /// 取引要求元処理種別
    @JsonKey(name: 'UkeID') String? ukeId,

    /// 業務コード
    @JsonKey(name: 'GymCd') String? gyoumuCode,

    /// セッションID
    @Json<PERSON>ey(name: 'SessionId') String? sessionId,

    /// AP次画面ID
    @Json<PERSON>ey(name: 'APNextScrID') String? apNextScreenId,

    /// 表示画面識別
    @JsonKey(name: 'OutPageId') String? outPageId,

    /// 業務処理結果
    @JsonKey(name: 'ErrMesUmuF') String? hasErrorMessage,

    /// ポップアップメッセージID
    @JsonKey(name: 'ErrMesID')
    @Default(ErrorInfo.defaultErrorCode)
    String errorMessageId,

    /// ポップアップテキスト
    @JsonKey(name: 'ErrTaisyo')
    @Default(ErrorInfo.defaultErrorMessage)
    String errorTarget,

    /// カレントセッションID
    @JsonKey(name: '_W_CurSessionID') String? currentSessionId,

    /// あとで有効化ボタン非表示フラグ
    @JsonKey(
      name: '_W_ActivateButtonFlag',
      fromJson: _isOtpActivateEnabledFromJson,
    )
    @Default(false)
    bool isOtpLateActivationEnable,

    /// ユーザーID
    @JsonKey(name: 'User') String? user,

    /// リダイレクトURL
    @JsonKey(name: 'RedirectURL') String? redirectUrl,

    /// シリアル番号
    @JsonKey(name: 'OtpSerialNo') String? otpSerialNo,

    /// OTP種類
    @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson)
    @Default(VdOtpKind.none)
    VdOtpKind otpKind,

    /// OTP有効化期限
    @JsonKey(name: 'OtpActivateKigen') String? otpActivateKigen,

    /// OTP不着区分
    @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
    @Default(VdOtpDeliveryType.outOfTarget)
    VdOtpDeliveryType otpHuchakueKbn,

    /// OTP不着理由
    @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
    @Default(null)
    String? otpHuchakueRsn,

    /// Web21リダイレクト用パラメータ
    @JsonKey(name: 'from_vd_param') String? fromVdParameter,
  }) = _VdidLoginResponse;

  factory VdidLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$VdidLoginResponseFromJson(json);
}

/// VDからの返却値をeNumに変換するクラス(OTP種別)
VdOtpKind _otpKindFromJson(String? otpKind) {
  if (otpKind == '01') return VdOtpKind.card;
  if (otpKind == '02') return VdOtpKind.app;
  if (otpKind == '03') return VdOtpKind.token;
  // 一致しない場合にはOTP利用なし
  return VdOtpKind.none;
}

/// VDからの返却値をeNumに変換するクラス(OTP不着区分)
VdOtpDeliveryType _otpDeliveryTypeFromJson(String? otpHuchakueKbn) {
  if (otpHuchakueKbn == '0') return VdOtpDeliveryType.received;
  if (otpHuchakueKbn == '1') return VdOtpDeliveryType.nonDelivery;
  return VdOtpDeliveryType.outOfTarget;
}

/// VDからの返却値をStringに変換するクラス(OTP不着理由)
String _otpHuchakueRsnFromJson(String? otpHuchakueRsn) {
  if (otpHuchakueRsn == '1') return OtpHuchakueRsnInfo.addresUnknouwn;
  if (otpHuchakueRsn == '2') return OtpHuchakueRsnInfo.periodPasses;
  if (otpHuchakueRsn == '3') return OtpHuchakueRsnInfo.noTransferRequired;
  if (otpHuchakueRsn == '4') return OtpHuchakueRsnInfo.relocation;
  if (otpHuchakueRsn == '5') return OtpHuchakueRsnInfo.incomleteDestination;
  if (otpHuchakueRsn == '6') return OtpHuchakueRsnInfo.roomNumberUnknown;
  if (otpHuchakueRsn == '7') return OtpHuchakueRsnInfo.relocationUnknown;
  return OtpHuchakueRsnInfo.other;
}

/// VDからの返却値をboolに変換するクラス(OTP後で有効化フラグ)
// _W_ActivateButtonFlagが'00'の際には期限切れとなり、VDでは後で有効化ボタンが表示されない仕様となっている
// 平仄を合わせる形で後で有効化ボタンが表示されない際には後で有効化を実施しない。
bool _isOtpActivateEnabledFromJson(String? activateButtonFlag) {
  if (activateButtonFlag == '0' || activateButtonFlag == '1') return true;
  return false;
}

extension VdidLoginResponseExt on VdidLoginResponse {
  /// 有効期限切れ
  bool get isExpiration => apNextScreenId == 'AVVE9H025';

  /// パスワード変更
  bool get isPasswordChange =>
      apNextScreenId == 'AVVE9H003' &&
      outPageId == '0' &&
      hasErrorMessage == '1' &&
      errorMessageId == 'C0028';

  /// OTP有効化（即時）
  bool get isOtpEnabled => apNextScreenId == 'AVVE9H211';

  /// OTP有効化（スキップ）
  bool get isOtpEnabledComplete => apNextScreenId == 'AVVE9H212';

  /// ログイン成功
  bool get isLoggedIn => apNextScreenId == 'AVV999100';

  /// otp有効化エラー
  bool get isOtpEnableError => apNextScreenId == 'AVV999110';

  /// 業務エラーがある
  bool get hasApplicationError =>
      outPageId == '1' &&
      hasErrorMessage == '1' &&
      (errorMessageId.startsWith('C'));

  /// システムエラーがある
  bool get hasSystemError =>
      outPageId == '2' &&
      hasErrorMessage == '1' &&
      (errorMessageId.startsWith('S'));

  void checkApplicationOrSystemError() {
    if (hasApplicationError) {
      // 業務エラーがあった場合
      throw VdidLoginApplicationException(
        message: errorTarget,
        code: errorMessageId,
        // TODO:返却値によって遷移先を変更 (暫定対応：いつでも元の画面に戻る)
        pageHandleType: const PageHandleType.backToPreviousPage(),
        // ? PageHandleType.goToNextPage(vdidLoginResponse.apNextScreenId!)
        // : const PageHandleType.backToPreviousPage(),
      );
    }
    if (hasSystemError) {
      // システムエラーがあった場合
      throw VdidLoginSystemException(
        message: errorTarget,
        code: errorMessageId,
      );
    }
  }

  bool containsErrorMessageId() {
    // エラーメッセージID対象一覧
    // 怪しさ判定APIの実行判定に利用する（errorMessageIdが対象一覧に含まれていたらAPI実行）
    final errorMessageIds = [
      '********',
      '********',
      '********',
      '********',
      'D0989',
      'C0007',
      'C0025',
      'C0048',
      'C0049',
      'C0002',
      'C0036',
      'C0046',
      'C0001',
      'C0005',
      'C0026',
      'D1068',
      'S2001',
    ];

    return errorMessageIds.contains(errorMessageId);
  }

  /// OTP種別判断フラグ
  bool get _isOtpKindCard => otpKind == VdOtpKind.card;
  bool get _isOtpKindApp => otpKind == VdOtpKind.app;
  bool get _isOtpKindToken => otpKind == VdOtpKind.token;

  /// OTP認証情報フラグ
  /// 認証に必要な情報（カード、トークン、通知書）が届いている場合には true を返却
  bool get isOtpReceived => otpHuchakueKbn == VdOtpDeliveryType.received;

  /// 受け取った準正常系を異常系に変換
  AppError toAppErrorFromVdResponse() {
    late final String errorMessage;
    late final bool hasFaq;
    late final String faqMessage;
    if (isPasswordChange) {
      errorMessage = ErrorInfo.changePasswordMessage;
      hasFaq = true;
      faqMessage = LoginErrorInfo.changePwFaqText;
    } else if (isExpiration) {
      errorMessage = ErrorInfo.passwordExpirationNoticeMessage;
      hasFaq = true;
      faqMessage = LoginErrorInfo.changePwFaqText;
    } else if (isOtpEnableError) {
      // OTP有効化エラーの際にはVDからのエラーメッセージを使用
      errorMessage = errorTarget;
      hasFaq = false;
      faqMessage = '';
    } else {
      // 該当のエラーがなかった場合にはデフォルトのエラーメッセージ
      errorMessage = ErrorInfo.defaultErrorMessage;
      hasFaq = false;
      faqMessage = '';
    }
    return AppError(
      code: errorMessageId,
      message: errorMessage,
      hasFaq: hasFaq,
      faqText: faqMessage,
    );
  }

  /// otp関連のエラーに変換
  AppError toAppErrorRelatedOtp() {
    late final String errorMessage;
    switch (otpHuchakueKbn) {
      // otpトークン・カード等受領済みの場合
      case VdOtpDeliveryType.received:
        if (_isOtpKindCard || _isOtpKindToken) {
          errorMessage = LoginErrorInfo.vdOtpExpirationTokenError;
        }
        if (_isOtpKindApp) {
          errorMessage = LoginErrorInfo.vdOtpExpirationAppError;
        }
      // otpトークン・カード等受領未済の場合
      case VdOtpDeliveryType.nonDelivery:
        if (_isOtpKindCard) {
          // カード不着
          errorMessage =
              '${LoginErrorInfo.vdOtpCardNotReceivedError}$otpHuchakueRsn';
        }
        if (_isOtpKindApp) {
          // 通知書不着
          errorMessage =
              '${LoginErrorInfo.vdOtpAppNotReceivedError}$otpHuchakueRsn';
        }
        if (_isOtpKindToken) {
          // トークン不着
          errorMessage =
              '${LoginErrorInfo.vdOtpTokenNotReceivedError}$otpHuchakueRsn';
        }
      default:
        errorMessage = ErrorInfo.defaultErrorMessage;
    }
    return AppError(message: errorMessage, hasFaq: true, code: errorMessageId);
  }
}
