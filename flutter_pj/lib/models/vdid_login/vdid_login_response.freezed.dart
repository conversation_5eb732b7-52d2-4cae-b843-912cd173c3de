// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'vdid_login_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VdidLoginResponse _$VdidLoginResponseFromJson(Map<String, dynamic> json) {
  return _VdidLoginResponse.fromJson(json);
}

/// @nodoc
mixin _$VdidLoginResponse {
  /// 取引要求元処理種別
  @JsonKey(name: 'UkeID')
  String? get ukeId => throw _privateConstructorUsedError;

  /// 業務コード
  @JsonKey(name: 'GymCd')
  String? get gyoumuCode => throw _privateConstructorUsedError;

  /// セッションID
  @JsonKey(name: 'SessionId')
  String? get sessionId => throw _privateConstructorUsedError;

  /// AP次画面ID
  @JsonKey(name: 'APNextScrID')
  String? get apNextScreenId => throw _privateConstructorUsedError;

  /// 表示画面識別
  @JsonKey(name: 'OutPageId')
  String? get outPageId => throw _privateConstructorUsedError;

  /// 業務処理結果
  @JsonKey(name: 'ErrMesUmuF')
  String? get hasErrorMessage => throw _privateConstructorUsedError;

  /// ポップアップメッセージID
  @JsonKey(name: 'ErrMesID')
  String get errorMessageId => throw _privateConstructorUsedError;

  /// ポップアップテキスト
  @JsonKey(name: 'ErrTaisyo')
  String get errorTarget => throw _privateConstructorUsedError;

  /// カレントセッションID
  @JsonKey(name: '_W_CurSessionID')
  String? get currentSessionId => throw _privateConstructorUsedError;

  /// あとで有効化ボタン非表示フラグ
  @JsonKey(
      name: '_W_ActivateButtonFlag', fromJson: _isOtpActivateEnabledFromJson)
  bool get isOtpLateActivationEnable => throw _privateConstructorUsedError;

  /// ユーザーID
  @JsonKey(name: 'User')
  String? get user => throw _privateConstructorUsedError;

  /// リダイレクトURL
  @JsonKey(name: 'RedirectURL')
  String? get redirectUrl => throw _privateConstructorUsedError;

  /// シリアル番号
  @JsonKey(name: 'OtpSerialNo')
  String? get otpSerialNo => throw _privateConstructorUsedError;

  /// OTP種類
  @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson)
  VdOtpKind get otpKind => throw _privateConstructorUsedError;

  /// OTP有効化期限
  @JsonKey(name: 'OtpActivateKigen')
  String? get otpActivateKigen => throw _privateConstructorUsedError;

  /// OTP不着区分
  @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
  VdOtpDeliveryType get otpHuchakueKbn => throw _privateConstructorUsedError;

  /// OTP不着理由
  @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
  String? get otpHuchakueRsn => throw _privateConstructorUsedError;

  /// Web21リダイレクト用パラメータ
  @JsonKey(name: 'from_vd_param')
  String? get fromVdParameter => throw _privateConstructorUsedError;

  /// Serializes this VdidLoginResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VdidLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VdidLoginResponseCopyWith<VdidLoginResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VdidLoginResponseCopyWith<$Res> {
  factory $VdidLoginResponseCopyWith(
          VdidLoginResponse value, $Res Function(VdidLoginResponse) then) =
      _$VdidLoginResponseCopyWithImpl<$Res, VdidLoginResponse>;
  @useResult
  $Res call(
      {@JsonKey(name: 'UkeID') String? ukeId,
      @JsonKey(name: 'GymCd') String? gyoumuCode,
      @JsonKey(name: 'SessionId') String? sessionId,
      @JsonKey(name: 'APNextScrID') String? apNextScreenId,
      @JsonKey(name: 'OutPageId') String? outPageId,
      @JsonKey(name: 'ErrMesUmuF') String? hasErrorMessage,
      @JsonKey(name: 'ErrMesID') String errorMessageId,
      @JsonKey(name: 'ErrTaisyo') String errorTarget,
      @JsonKey(name: '_W_CurSessionID') String? currentSessionId,
      @JsonKey(
          name: '_W_ActivateButtonFlag',
          fromJson: _isOtpActivateEnabledFromJson)
      bool isOtpLateActivationEnable,
      @JsonKey(name: 'User') String? user,
      @JsonKey(name: 'RedirectURL') String? redirectUrl,
      @JsonKey(name: 'OtpSerialNo') String? otpSerialNo,
      @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson) VdOtpKind otpKind,
      @JsonKey(name: 'OtpActivateKigen') String? otpActivateKigen,
      @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
      VdOtpDeliveryType otpHuchakueKbn,
      @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
      String? otpHuchakueRsn,
      @JsonKey(name: 'from_vd_param') String? fromVdParameter});
}

/// @nodoc
class _$VdidLoginResponseCopyWithImpl<$Res, $Val extends VdidLoginResponse>
    implements $VdidLoginResponseCopyWith<$Res> {
  _$VdidLoginResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VdidLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ukeId = freezed,
    Object? gyoumuCode = freezed,
    Object? sessionId = freezed,
    Object? apNextScreenId = freezed,
    Object? outPageId = freezed,
    Object? hasErrorMessage = freezed,
    Object? errorMessageId = null,
    Object? errorTarget = null,
    Object? currentSessionId = freezed,
    Object? isOtpLateActivationEnable = null,
    Object? user = freezed,
    Object? redirectUrl = freezed,
    Object? otpSerialNo = freezed,
    Object? otpKind = null,
    Object? otpActivateKigen = freezed,
    Object? otpHuchakueKbn = null,
    Object? otpHuchakueRsn = freezed,
    Object? fromVdParameter = freezed,
  }) {
    return _then(_value.copyWith(
      ukeId: freezed == ukeId
          ? _value.ukeId
          : ukeId // ignore: cast_nullable_to_non_nullable
              as String?,
      gyoumuCode: freezed == gyoumuCode
          ? _value.gyoumuCode
          : gyoumuCode // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      apNextScreenId: freezed == apNextScreenId
          ? _value.apNextScreenId
          : apNextScreenId // ignore: cast_nullable_to_non_nullable
              as String?,
      outPageId: freezed == outPageId
          ? _value.outPageId
          : outPageId // ignore: cast_nullable_to_non_nullable
              as String?,
      hasErrorMessage: freezed == hasErrorMessage
          ? _value.hasErrorMessage
          : hasErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessageId: null == errorMessageId
          ? _value.errorMessageId
          : errorMessageId // ignore: cast_nullable_to_non_nullable
              as String,
      errorTarget: null == errorTarget
          ? _value.errorTarget
          : errorTarget // ignore: cast_nullable_to_non_nullable
              as String,
      currentSessionId: freezed == currentSessionId
          ? _value.currentSessionId
          : currentSessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      isOtpLateActivationEnable: null == isOtpLateActivationEnable
          ? _value.isOtpLateActivationEnable
          : isOtpLateActivationEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      otpSerialNo: freezed == otpSerialNo
          ? _value.otpSerialNo
          : otpSerialNo // ignore: cast_nullable_to_non_nullable
              as String?,
      otpKind: null == otpKind
          ? _value.otpKind
          : otpKind // ignore: cast_nullable_to_non_nullable
              as VdOtpKind,
      otpActivateKigen: freezed == otpActivateKigen
          ? _value.otpActivateKigen
          : otpActivateKigen // ignore: cast_nullable_to_non_nullable
              as String?,
      otpHuchakueKbn: null == otpHuchakueKbn
          ? _value.otpHuchakueKbn
          : otpHuchakueKbn // ignore: cast_nullable_to_non_nullable
              as VdOtpDeliveryType,
      otpHuchakueRsn: freezed == otpHuchakueRsn
          ? _value.otpHuchakueRsn
          : otpHuchakueRsn // ignore: cast_nullable_to_non_nullable
              as String?,
      fromVdParameter: freezed == fromVdParameter
          ? _value.fromVdParameter
          : fromVdParameter // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VdidLoginResponseImplCopyWith<$Res>
    implements $VdidLoginResponseCopyWith<$Res> {
  factory _$$VdidLoginResponseImplCopyWith(_$VdidLoginResponseImpl value,
          $Res Function(_$VdidLoginResponseImpl) then) =
      __$$VdidLoginResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'UkeID') String? ukeId,
      @JsonKey(name: 'GymCd') String? gyoumuCode,
      @JsonKey(name: 'SessionId') String? sessionId,
      @JsonKey(name: 'APNextScrID') String? apNextScreenId,
      @JsonKey(name: 'OutPageId') String? outPageId,
      @JsonKey(name: 'ErrMesUmuF') String? hasErrorMessage,
      @JsonKey(name: 'ErrMesID') String errorMessageId,
      @JsonKey(name: 'ErrTaisyo') String errorTarget,
      @JsonKey(name: '_W_CurSessionID') String? currentSessionId,
      @JsonKey(
          name: '_W_ActivateButtonFlag',
          fromJson: _isOtpActivateEnabledFromJson)
      bool isOtpLateActivationEnable,
      @JsonKey(name: 'User') String? user,
      @JsonKey(name: 'RedirectURL') String? redirectUrl,
      @JsonKey(name: 'OtpSerialNo') String? otpSerialNo,
      @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson) VdOtpKind otpKind,
      @JsonKey(name: 'OtpActivateKigen') String? otpActivateKigen,
      @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
      VdOtpDeliveryType otpHuchakueKbn,
      @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
      String? otpHuchakueRsn,
      @JsonKey(name: 'from_vd_param') String? fromVdParameter});
}

/// @nodoc
class __$$VdidLoginResponseImplCopyWithImpl<$Res>
    extends _$VdidLoginResponseCopyWithImpl<$Res, _$VdidLoginResponseImpl>
    implements _$$VdidLoginResponseImplCopyWith<$Res> {
  __$$VdidLoginResponseImplCopyWithImpl(_$VdidLoginResponseImpl _value,
      $Res Function(_$VdidLoginResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of VdidLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ukeId = freezed,
    Object? gyoumuCode = freezed,
    Object? sessionId = freezed,
    Object? apNextScreenId = freezed,
    Object? outPageId = freezed,
    Object? hasErrorMessage = freezed,
    Object? errorMessageId = null,
    Object? errorTarget = null,
    Object? currentSessionId = freezed,
    Object? isOtpLateActivationEnable = null,
    Object? user = freezed,
    Object? redirectUrl = freezed,
    Object? otpSerialNo = freezed,
    Object? otpKind = null,
    Object? otpActivateKigen = freezed,
    Object? otpHuchakueKbn = null,
    Object? otpHuchakueRsn = freezed,
    Object? fromVdParameter = freezed,
  }) {
    return _then(_$VdidLoginResponseImpl(
      ukeId: freezed == ukeId
          ? _value.ukeId
          : ukeId // ignore: cast_nullable_to_non_nullable
              as String?,
      gyoumuCode: freezed == gyoumuCode
          ? _value.gyoumuCode
          : gyoumuCode // ignore: cast_nullable_to_non_nullable
              as String?,
      sessionId: freezed == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      apNextScreenId: freezed == apNextScreenId
          ? _value.apNextScreenId
          : apNextScreenId // ignore: cast_nullable_to_non_nullable
              as String?,
      outPageId: freezed == outPageId
          ? _value.outPageId
          : outPageId // ignore: cast_nullable_to_non_nullable
              as String?,
      hasErrorMessage: freezed == hasErrorMessage
          ? _value.hasErrorMessage
          : hasErrorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      errorMessageId: null == errorMessageId
          ? _value.errorMessageId
          : errorMessageId // ignore: cast_nullable_to_non_nullable
              as String,
      errorTarget: null == errorTarget
          ? _value.errorTarget
          : errorTarget // ignore: cast_nullable_to_non_nullable
              as String,
      currentSessionId: freezed == currentSessionId
          ? _value.currentSessionId
          : currentSessionId // ignore: cast_nullable_to_non_nullable
              as String?,
      isOtpLateActivationEnable: null == isOtpLateActivationEnable
          ? _value.isOtpLateActivationEnable
          : isOtpLateActivationEnable // ignore: cast_nullable_to_non_nullable
              as bool,
      user: freezed == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as String?,
      redirectUrl: freezed == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      otpSerialNo: freezed == otpSerialNo
          ? _value.otpSerialNo
          : otpSerialNo // ignore: cast_nullable_to_non_nullable
              as String?,
      otpKind: null == otpKind
          ? _value.otpKind
          : otpKind // ignore: cast_nullable_to_non_nullable
              as VdOtpKind,
      otpActivateKigen: freezed == otpActivateKigen
          ? _value.otpActivateKigen
          : otpActivateKigen // ignore: cast_nullable_to_non_nullable
              as String?,
      otpHuchakueKbn: null == otpHuchakueKbn
          ? _value.otpHuchakueKbn
          : otpHuchakueKbn // ignore: cast_nullable_to_non_nullable
              as VdOtpDeliveryType,
      otpHuchakueRsn: freezed == otpHuchakueRsn
          ? _value.otpHuchakueRsn
          : otpHuchakueRsn // ignore: cast_nullable_to_non_nullable
              as String?,
      fromVdParameter: freezed == fromVdParameter
          ? _value.fromVdParameter
          : fromVdParameter // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VdidLoginResponseImpl implements _VdidLoginResponse {
  const _$VdidLoginResponseImpl(
      {@JsonKey(name: 'UkeID') this.ukeId,
      @JsonKey(name: 'GymCd') this.gyoumuCode,
      @JsonKey(name: 'SessionId') this.sessionId,
      @JsonKey(name: 'APNextScrID') this.apNextScreenId,
      @JsonKey(name: 'OutPageId') this.outPageId,
      @JsonKey(name: 'ErrMesUmuF') this.hasErrorMessage,
      @JsonKey(name: 'ErrMesID')
      this.errorMessageId = ErrorInfo.defaultErrorCode,
      @JsonKey(name: 'ErrTaisyo')
      this.errorTarget = ErrorInfo.defaultErrorMessage,
      @JsonKey(name: '_W_CurSessionID') this.currentSessionId,
      @JsonKey(
          name: '_W_ActivateButtonFlag',
          fromJson: _isOtpActivateEnabledFromJson)
      this.isOtpLateActivationEnable = false,
      @JsonKey(name: 'User') this.user,
      @JsonKey(name: 'RedirectURL') this.redirectUrl,
      @JsonKey(name: 'OtpSerialNo') this.otpSerialNo,
      @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson)
      this.otpKind = VdOtpKind.none,
      @JsonKey(name: 'OtpActivateKigen') this.otpActivateKigen,
      @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
      this.otpHuchakueKbn = VdOtpDeliveryType.outOfTarget,
      @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
      this.otpHuchakueRsn = null,
      @JsonKey(name: 'from_vd_param') this.fromVdParameter});

  factory _$VdidLoginResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$VdidLoginResponseImplFromJson(json);

  /// 取引要求元処理種別
  @override
  @JsonKey(name: 'UkeID')
  final String? ukeId;

  /// 業務コード
  @override
  @JsonKey(name: 'GymCd')
  final String? gyoumuCode;

  /// セッションID
  @override
  @JsonKey(name: 'SessionId')
  final String? sessionId;

  /// AP次画面ID
  @override
  @JsonKey(name: 'APNextScrID')
  final String? apNextScreenId;

  /// 表示画面識別
  @override
  @JsonKey(name: 'OutPageId')
  final String? outPageId;

  /// 業務処理結果
  @override
  @JsonKey(name: 'ErrMesUmuF')
  final String? hasErrorMessage;

  /// ポップアップメッセージID
  @override
  @JsonKey(name: 'ErrMesID')
  final String errorMessageId;

  /// ポップアップテキスト
  @override
  @JsonKey(name: 'ErrTaisyo')
  final String errorTarget;

  /// カレントセッションID
  @override
  @JsonKey(name: '_W_CurSessionID')
  final String? currentSessionId;

  /// あとで有効化ボタン非表示フラグ
  @override
  @JsonKey(
      name: '_W_ActivateButtonFlag', fromJson: _isOtpActivateEnabledFromJson)
  final bool isOtpLateActivationEnable;

  /// ユーザーID
  @override
  @JsonKey(name: 'User')
  final String? user;

  /// リダイレクトURL
  @override
  @JsonKey(name: 'RedirectURL')
  final String? redirectUrl;

  /// シリアル番号
  @override
  @JsonKey(name: 'OtpSerialNo')
  final String? otpSerialNo;

  /// OTP種類
  @override
  @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson)
  final VdOtpKind otpKind;

  /// OTP有効化期限
  @override
  @JsonKey(name: 'OtpActivateKigen')
  final String? otpActivateKigen;

  /// OTP不着区分
  @override
  @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
  final VdOtpDeliveryType otpHuchakueKbn;

  /// OTP不着理由
  @override
  @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
  final String? otpHuchakueRsn;

  /// Web21リダイレクト用パラメータ
  @override
  @JsonKey(name: 'from_vd_param')
  final String? fromVdParameter;

  @override
  String toString() {
    return 'VdidLoginResponse(ukeId: $ukeId, gyoumuCode: $gyoumuCode, sessionId: $sessionId, apNextScreenId: $apNextScreenId, outPageId: $outPageId, hasErrorMessage: $hasErrorMessage, errorMessageId: $errorMessageId, errorTarget: $errorTarget, currentSessionId: $currentSessionId, isOtpLateActivationEnable: $isOtpLateActivationEnable, user: $user, redirectUrl: $redirectUrl, otpSerialNo: $otpSerialNo, otpKind: $otpKind, otpActivateKigen: $otpActivateKigen, otpHuchakueKbn: $otpHuchakueKbn, otpHuchakueRsn: $otpHuchakueRsn, fromVdParameter: $fromVdParameter)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VdidLoginResponseImpl &&
            (identical(other.ukeId, ukeId) || other.ukeId == ukeId) &&
            (identical(other.gyoumuCode, gyoumuCode) ||
                other.gyoumuCode == gyoumuCode) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.apNextScreenId, apNextScreenId) ||
                other.apNextScreenId == apNextScreenId) &&
            (identical(other.outPageId, outPageId) ||
                other.outPageId == outPageId) &&
            (identical(other.hasErrorMessage, hasErrorMessage) ||
                other.hasErrorMessage == hasErrorMessage) &&
            (identical(other.errorMessageId, errorMessageId) ||
                other.errorMessageId == errorMessageId) &&
            (identical(other.errorTarget, errorTarget) ||
                other.errorTarget == errorTarget) &&
            (identical(other.currentSessionId, currentSessionId) ||
                other.currentSessionId == currentSessionId) &&
            (identical(other.isOtpLateActivationEnable,
                    isOtpLateActivationEnable) ||
                other.isOtpLateActivationEnable == isOtpLateActivationEnable) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.otpSerialNo, otpSerialNo) ||
                other.otpSerialNo == otpSerialNo) &&
            (identical(other.otpKind, otpKind) || other.otpKind == otpKind) &&
            (identical(other.otpActivateKigen, otpActivateKigen) ||
                other.otpActivateKigen == otpActivateKigen) &&
            (identical(other.otpHuchakueKbn, otpHuchakueKbn) ||
                other.otpHuchakueKbn == otpHuchakueKbn) &&
            (identical(other.otpHuchakueRsn, otpHuchakueRsn) ||
                other.otpHuchakueRsn == otpHuchakueRsn) &&
            (identical(other.fromVdParameter, fromVdParameter) ||
                other.fromVdParameter == fromVdParameter));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      ukeId,
      gyoumuCode,
      sessionId,
      apNextScreenId,
      outPageId,
      hasErrorMessage,
      errorMessageId,
      errorTarget,
      currentSessionId,
      isOtpLateActivationEnable,
      user,
      redirectUrl,
      otpSerialNo,
      otpKind,
      otpActivateKigen,
      otpHuchakueKbn,
      otpHuchakueRsn,
      fromVdParameter);

  /// Create a copy of VdidLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VdidLoginResponseImplCopyWith<_$VdidLoginResponseImpl> get copyWith =>
      __$$VdidLoginResponseImplCopyWithImpl<_$VdidLoginResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VdidLoginResponseImplToJson(
      this,
    );
  }
}

abstract class _VdidLoginResponse implements VdidLoginResponse {
  const factory _VdidLoginResponse(
          {@JsonKey(name: 'UkeID') final String? ukeId,
          @JsonKey(name: 'GymCd') final String? gyoumuCode,
          @JsonKey(name: 'SessionId') final String? sessionId,
          @JsonKey(name: 'APNextScrID') final String? apNextScreenId,
          @JsonKey(name: 'OutPageId') final String? outPageId,
          @JsonKey(name: 'ErrMesUmuF') final String? hasErrorMessage,
          @JsonKey(name: 'ErrMesID') final String errorMessageId,
          @JsonKey(name: 'ErrTaisyo') final String errorTarget,
          @JsonKey(name: '_W_CurSessionID') final String? currentSessionId,
          @JsonKey(
              name: '_W_ActivateButtonFlag',
              fromJson: _isOtpActivateEnabledFromJson)
          final bool isOtpLateActivationEnable,
          @JsonKey(name: 'User') final String? user,
          @JsonKey(name: 'RedirectURL') final String? redirectUrl,
          @JsonKey(name: 'OtpSerialNo') final String? otpSerialNo,
          @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson)
          final VdOtpKind otpKind,
          @JsonKey(name: 'OtpActivateKigen') final String? otpActivateKigen,
          @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
          final VdOtpDeliveryType otpHuchakueKbn,
          @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
          final String? otpHuchakueRsn,
          @JsonKey(name: 'from_vd_param') final String? fromVdParameter}) =
      _$VdidLoginResponseImpl;

  factory _VdidLoginResponse.fromJson(Map<String, dynamic> json) =
      _$VdidLoginResponseImpl.fromJson;

  /// 取引要求元処理種別
  @override
  @JsonKey(name: 'UkeID')
  String? get ukeId;

  /// 業務コード
  @override
  @JsonKey(name: 'GymCd')
  String? get gyoumuCode;

  /// セッションID
  @override
  @JsonKey(name: 'SessionId')
  String? get sessionId;

  /// AP次画面ID
  @override
  @JsonKey(name: 'APNextScrID')
  String? get apNextScreenId;

  /// 表示画面識別
  @override
  @JsonKey(name: 'OutPageId')
  String? get outPageId;

  /// 業務処理結果
  @override
  @JsonKey(name: 'ErrMesUmuF')
  String? get hasErrorMessage;

  /// ポップアップメッセージID
  @override
  @JsonKey(name: 'ErrMesID')
  String get errorMessageId;

  /// ポップアップテキスト
  @override
  @JsonKey(name: 'ErrTaisyo')
  String get errorTarget;

  /// カレントセッションID
  @override
  @JsonKey(name: '_W_CurSessionID')
  String? get currentSessionId;

  /// あとで有効化ボタン非表示フラグ
  @override
  @JsonKey(
      name: '_W_ActivateButtonFlag', fromJson: _isOtpActivateEnabledFromJson)
  bool get isOtpLateActivationEnable;

  /// ユーザーID
  @override
  @JsonKey(name: 'User')
  String? get user;

  /// リダイレクトURL
  @override
  @JsonKey(name: 'RedirectURL')
  String? get redirectUrl;

  /// シリアル番号
  @override
  @JsonKey(name: 'OtpSerialNo')
  String? get otpSerialNo;

  /// OTP種類
  @override
  @JsonKey(name: 'OtpKind', fromJson: _otpKindFromJson)
  VdOtpKind get otpKind;

  /// OTP有効化期限
  @override
  @JsonKey(name: 'OtpActivateKigen')
  String? get otpActivateKigen;

  /// OTP不着区分
  @override
  @JsonKey(name: 'OtpHuchakueKbn', fromJson: _otpDeliveryTypeFromJson)
  VdOtpDeliveryType get otpHuchakueKbn;

  /// OTP不着理由
  @override
  @JsonKey(name: 'OtpHuchakueRsn', fromJson: _otpHuchakueRsnFromJson)
  String? get otpHuchakueRsn;

  /// Web21リダイレクト用パラメータ
  @override
  @JsonKey(name: 'from_vd_param')
  String? get fromVdParameter;

  /// Create a copy of VdidLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VdidLoginResponseImplCopyWith<_$VdidLoginResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
