// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bank_account_balance.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BankAccountBalanceImpl _$$BankAccountBalanceImplFromJson(
        Map<String, dynamic> json) =>
    _$BankAccountBalanceImpl(
      count: (json['count'] as num).toInt(),
      accounts: (json['accounts'] as List<dynamic>)
          .map((e) =>
              BankAccountBalanceDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      serverDate: json['serverDate'] as String,
    );

Map<String, dynamic> _$$BankAccountBalanceImplToJson(
        _$BankAccountBalanceImpl instance) =>
    <String, dynamic>{
      'count': instance.count,
      'accounts': instance.accounts,
      'serverDate': instance.serverDate,
    };

_$BankAccountBalanceDetailImpl _$$BankAccountBalanceDetailImplFromJson(
        Map<String, dynamic> json) =>
    _$BankAccountBalanceDetailImpl(
      contactName: json['contactName'] as String?,
      accountId: json['accountId'] as String?,
      bankName: json['bankName'] as String?,
      branchName: json['branchName'] as String?,
      branchCode: json['branchCode'] as String?,
      accountType: json['accountType'] as String?,
      accountNumber: json['accountNumber'] as String?,
      baseDate: json['baseDate'] as String?,
      baseTime: json['baseTime'] as String?,
      baseDateTime: json['baseDateTime'] as String?,
      lastSyncedAt: json['lastSyncedAt'] as String?,
      currentBalance: (json['currentBalance'] as num?)?.toInt(),
      checksIssuedByOtherBanks:
          (json['checksIssuedByOtherBanks'] as num?)?.toInt(),
      overdraftLimit: (json['overdraftLimit'] as num?)?.toInt(),
      withdrawableBalance: (json['withdrawableBalance'] as num?)?.toInt(),
      displayAccountName: json['displayAccountName'] as String?,
      displayAccountFlag: json['displayAccountFlag'] as bool?,
      displayOrder: (json['displayOrder'] as num?)?.toInt(),
      remitterName: json['remitterName'] as String?,
      isFreeeReConnectFinancialInstitution:
          json['isFreeeReConnectFinancialInstitution'] as bool? ?? false,
    );

Map<String, dynamic> _$$BankAccountBalanceDetailImplToJson(
        _$BankAccountBalanceDetailImpl instance) =>
    <String, dynamic>{
      'contactName': instance.contactName,
      'accountId': instance.accountId,
      'bankName': instance.bankName,
      'branchName': instance.branchName,
      'branchCode': instance.branchCode,
      'accountType': instance.accountType,
      'accountNumber': instance.accountNumber,
      'baseDate': instance.baseDate,
      'baseTime': instance.baseTime,
      'baseDateTime': instance.baseDateTime,
      'lastSyncedAt': instance.lastSyncedAt,
      'currentBalance': instance.currentBalance,
      'checksIssuedByOtherBanks': instance.checksIssuedByOtherBanks,
      'overdraftLimit': instance.overdraftLimit,
      'withdrawableBalance': instance.withdrawableBalance,
      'displayAccountName': instance.displayAccountName,
      'displayAccountFlag': instance.displayAccountFlag,
      'displayOrder': instance.displayOrder,
      'remitterName': instance.remitterName,
      'isFreeeReConnectFinancialInstitution':
          instance.isFreeeReConnectFinancialInstitution,
    };
