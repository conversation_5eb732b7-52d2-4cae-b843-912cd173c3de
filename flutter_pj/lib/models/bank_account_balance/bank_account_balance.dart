import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_account_balance.freezed.dart';

part 'bank_account_balance.g.dart';

@freezed
class BankAccountBalance with _$BankAccountBalance {
  const factory BankAccountBalance({
    required int count,
    required List<BankAccountBalanceDetail> accounts,
    required String serverDate,
  }) = _BankAccountBalance;

  factory BankAccountBalance.fromJson(Map<String, dynamic> json) =>
      _$BankAccountBalanceFromJson(json);
}

extension BankAccountBalanceExt on BankAccountBalance {
  List<BankAccountBalanceDetail> get visibleAccounts => accounts
      .where(
        (element) => element.displayAccountFlag ?? true,
      )
      .toList();
}

@freezed
class BankAccountBalanceDetail with _$BankAccountBalanceDetail {
  const factory BankAccountBalanceDetail({
    String? contactName,
    String? accountId,
    String? bankName,
    String? branchName,
    String? branchCode,
    String? accountType,
    String? accountNumber,
    String? baseDate,
    String? baseTime,
    String? baseDateTime,
    String? lastSyncedAt,
    int? currentBalance,
    int? checksIssuedByOtherBanks,
    int? overdraftLimit,
    int? withdrawableBalance,
    String? displayAccountName, // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
    bool? displayAccountFlag, // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
    int? displayOrder, // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
    String? remitterName,
    @Default(false) bool isFreeeReConnectFinancialInstitution,
  }) = _BankAccountBalanceDetail;

  factory BankAccountBalanceDetail.fromJson(Map<String, dynamic> json) =>
      _$BankAccountBalanceDetailFromJson(json);
}

extension BankAccountBalanceDetailList on List<BankAccountBalanceDetail> {
  /// 口座残高合計
  int? getTotalBalance() {
    int sum = 0;
    bool hasNonNullValue = false;
    for (final detail in this) {
      if (detail.currentBalance != null) {
        sum += detail.currentBalance!;
        hasNonNullValue = true;
      }
    }
    return hasNonNullValue ? sum : null;
  }

  /// 内他店手形合計
  int? getTotalChecksIssuedByOtherBanksAmount() {
    int sum = 0;
    bool hasNonNullValue = false;
    for (final detail in this) {
      if (detail.checksIssuedByOtherBanks != null) {
        sum += detail.checksIssuedByOtherBanks!;
        hasNonNullValue = true;
      }
    }
    return hasNonNullValue ? sum : null;
  }

  /// 貸越限度額合計
  int? getTotalOverdraftLimit() {
    int sum = 0;
    bool hasNonNullValue = false;
    for (final detail in this) {
      if (detail.overdraftLimit != null) {
        sum += detail.overdraftLimit!;
        hasNonNullValue = true;
      }
    }
    return hasNonNullValue ? sum : null;
  }

  /// 支払可能残高合計
  int? getTotalWithdrawableBalance() {
    int sum = 0;
    bool hasNonNullValue = false;
    for (final detail in this) {
      if (detail.withdrawableBalance != null) {
        sum += detail.withdrawableBalance!;
        hasNonNullValue = true;
      }
    }
    return hasNonNullValue ? sum : null;
  }
}
