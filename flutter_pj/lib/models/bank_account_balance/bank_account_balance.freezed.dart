// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account_balance.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BankAccountBalance _$BankAccountBalanceFromJson(Map<String, dynamic> json) {
  return _BankAccountBalance.fromJson(json);
}

/// @nodoc
mixin _$BankAccountBalance {
  int get count => throw _privateConstructorUsedError;
  List<BankAccountBalanceDetail> get accounts =>
      throw _privateConstructorUsedError;
  String get serverDate => throw _privateConstructorUsedError;

  /// Serializes this BankAccountBalance to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BankAccountBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankAccountBalanceCopyWith<BankAccountBalance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountBalanceCopyWith<$Res> {
  factory $BankAccountBalanceCopyWith(
          BankAccountBalance value, $Res Function(BankAccountBalance) then) =
      _$BankAccountBalanceCopyWithImpl<$Res, BankAccountBalance>;
  @useResult
  $Res call(
      {int count, List<BankAccountBalanceDetail> accounts, String serverDate});
}

/// @nodoc
class _$BankAccountBalanceCopyWithImpl<$Res, $Val extends BankAccountBalance>
    implements $BankAccountBalanceCopyWith<$Res> {
  _$BankAccountBalanceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccountBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = null,
    Object? accounts = null,
    Object? serverDate = null,
  }) {
    return _then(_value.copyWith(
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccountBalanceDetail>,
      serverDate: null == serverDate
          ? _value.serverDate
          : serverDate // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BankAccountBalanceImplCopyWith<$Res>
    implements $BankAccountBalanceCopyWith<$Res> {
  factory _$$BankAccountBalanceImplCopyWith(_$BankAccountBalanceImpl value,
          $Res Function(_$BankAccountBalanceImpl) then) =
      __$$BankAccountBalanceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int count, List<BankAccountBalanceDetail> accounts, String serverDate});
}

/// @nodoc
class __$$BankAccountBalanceImplCopyWithImpl<$Res>
    extends _$BankAccountBalanceCopyWithImpl<$Res, _$BankAccountBalanceImpl>
    implements _$$BankAccountBalanceImplCopyWith<$Res> {
  __$$BankAccountBalanceImplCopyWithImpl(_$BankAccountBalanceImpl _value,
      $Res Function(_$BankAccountBalanceImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? count = null,
    Object? accounts = null,
    Object? serverDate = null,
  }) {
    return _then(_$BankAccountBalanceImpl(
      count: null == count
          ? _value.count
          : count // ignore: cast_nullable_to_non_nullable
              as int,
      accounts: null == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccountBalanceDetail>,
      serverDate: null == serverDate
          ? _value.serverDate
          : serverDate // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BankAccountBalanceImpl implements _BankAccountBalance {
  const _$BankAccountBalanceImpl(
      {required this.count,
      required final List<BankAccountBalanceDetail> accounts,
      required this.serverDate})
      : _accounts = accounts;

  factory _$BankAccountBalanceImpl.fromJson(Map<String, dynamic> json) =>
      _$$BankAccountBalanceImplFromJson(json);

  @override
  final int count;
  final List<BankAccountBalanceDetail> _accounts;
  @override
  List<BankAccountBalanceDetail> get accounts {
    if (_accounts is EqualUnmodifiableListView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_accounts);
  }

  @override
  final String serverDate;

  @override
  String toString() {
    return 'BankAccountBalance(count: $count, accounts: $accounts, serverDate: $serverDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountBalanceImpl &&
            (identical(other.count, count) || other.count == count) &&
            const DeepCollectionEquality().equals(other._accounts, _accounts) &&
            (identical(other.serverDate, serverDate) ||
                other.serverDate == serverDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, count,
      const DeepCollectionEquality().hash(_accounts), serverDate);

  /// Create a copy of BankAccountBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountBalanceImplCopyWith<_$BankAccountBalanceImpl> get copyWith =>
      __$$BankAccountBalanceImplCopyWithImpl<_$BankAccountBalanceImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BankAccountBalanceImplToJson(
      this,
    );
  }
}

abstract class _BankAccountBalance implements BankAccountBalance {
  const factory _BankAccountBalance(
      {required final int count,
      required final List<BankAccountBalanceDetail> accounts,
      required final String serverDate}) = _$BankAccountBalanceImpl;

  factory _BankAccountBalance.fromJson(Map<String, dynamic> json) =
      _$BankAccountBalanceImpl.fromJson;

  @override
  int get count;
  @override
  List<BankAccountBalanceDetail> get accounts;
  @override
  String get serverDate;

  /// Create a copy of BankAccountBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountBalanceImplCopyWith<_$BankAccountBalanceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BankAccountBalanceDetail _$BankAccountBalanceDetailFromJson(
    Map<String, dynamic> json) {
  return _BankAccountBalanceDetail.fromJson(json);
}

/// @nodoc
mixin _$BankAccountBalanceDetail {
  String? get contactName => throw _privateConstructorUsedError;
  String? get accountId => throw _privateConstructorUsedError;
  String? get bankName => throw _privateConstructorUsedError;
  String? get branchName => throw _privateConstructorUsedError;
  String? get branchCode => throw _privateConstructorUsedError;
  String? get accountType => throw _privateConstructorUsedError;
  String? get accountNumber => throw _privateConstructorUsedError;
  String? get baseDate => throw _privateConstructorUsedError;
  String? get baseTime => throw _privateConstructorUsedError;
  String? get baseDateTime => throw _privateConstructorUsedError;
  String? get lastSyncedAt => throw _privateConstructorUsedError;
  int? get currentBalance => throw _privateConstructorUsedError;
  int? get checksIssuedByOtherBanks => throw _privateConstructorUsedError;
  int? get overdraftLimit => throw _privateConstructorUsedError;
  int? get withdrawableBalance => throw _privateConstructorUsedError;
  String? get displayAccountName =>
      throw _privateConstructorUsedError; // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  bool? get displayAccountFlag =>
      throw _privateConstructorUsedError; // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  int? get displayOrder =>
      throw _privateConstructorUsedError; // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  String? get remitterName => throw _privateConstructorUsedError;
  bool get isFreeeReConnectFinancialInstitution =>
      throw _privateConstructorUsedError;

  /// Serializes this BankAccountBalanceDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BankAccountBalanceDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankAccountBalanceDetailCopyWith<BankAccountBalanceDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountBalanceDetailCopyWith<$Res> {
  factory $BankAccountBalanceDetailCopyWith(BankAccountBalanceDetail value,
          $Res Function(BankAccountBalanceDetail) then) =
      _$BankAccountBalanceDetailCopyWithImpl<$Res, BankAccountBalanceDetail>;
  @useResult
  $Res call(
      {String? contactName,
      String? accountId,
      String? bankName,
      String? branchName,
      String? branchCode,
      String? accountType,
      String? accountNumber,
      String? baseDate,
      String? baseTime,
      String? baseDateTime,
      String? lastSyncedAt,
      int? currentBalance,
      int? checksIssuedByOtherBanks,
      int? overdraftLimit,
      int? withdrawableBalance,
      String? displayAccountName,
      bool? displayAccountFlag,
      int? displayOrder,
      String? remitterName,
      bool isFreeeReConnectFinancialInstitution});
}

/// @nodoc
class _$BankAccountBalanceDetailCopyWithImpl<$Res,
        $Val extends BankAccountBalanceDetail>
    implements $BankAccountBalanceDetailCopyWith<$Res> {
  _$BankAccountBalanceDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccountBalanceDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactName = freezed,
    Object? accountId = freezed,
    Object? bankName = freezed,
    Object? branchName = freezed,
    Object? branchCode = freezed,
    Object? accountType = freezed,
    Object? accountNumber = freezed,
    Object? baseDate = freezed,
    Object? baseTime = freezed,
    Object? baseDateTime = freezed,
    Object? lastSyncedAt = freezed,
    Object? currentBalance = freezed,
    Object? checksIssuedByOtherBanks = freezed,
    Object? overdraftLimit = freezed,
    Object? withdrawableBalance = freezed,
    Object? displayAccountName = freezed,
    Object? displayAccountFlag = freezed,
    Object? displayOrder = freezed,
    Object? remitterName = freezed,
    Object? isFreeeReConnectFinancialInstitution = null,
  }) {
    return _then(_value.copyWith(
      contactName: freezed == contactName
          ? _value.contactName
          : contactName // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
      branchName: freezed == branchName
          ? _value.branchName
          : branchName // ignore: cast_nullable_to_non_nullable
              as String?,
      branchCode: freezed == branchCode
          ? _value.branchCode
          : branchCode // ignore: cast_nullable_to_non_nullable
              as String?,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String?,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDateTime: freezed == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      currentBalance: freezed == currentBalance
          ? _value.currentBalance
          : currentBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      checksIssuedByOtherBanks: freezed == checksIssuedByOtherBanks
          ? _value.checksIssuedByOtherBanks
          : checksIssuedByOtherBanks // ignore: cast_nullable_to_non_nullable
              as int?,
      overdraftLimit: freezed == overdraftLimit
          ? _value.overdraftLimit
          : overdraftLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      withdrawableBalance: freezed == withdrawableBalance
          ? _value.withdrawableBalance
          : withdrawableBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      displayAccountName: freezed == displayAccountName
          ? _value.displayAccountName
          : displayAccountName // ignore: cast_nullable_to_non_nullable
              as String?,
      displayAccountFlag: freezed == displayAccountFlag
          ? _value.displayAccountFlag
          : displayAccountFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      displayOrder: freezed == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      remitterName: freezed == remitterName
          ? _value.remitterName
          : remitterName // ignore: cast_nullable_to_non_nullable
              as String?,
      isFreeeReConnectFinancialInstitution: null ==
              isFreeeReConnectFinancialInstitution
          ? _value.isFreeeReConnectFinancialInstitution
          : isFreeeReConnectFinancialInstitution // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BankAccountBalanceDetailImplCopyWith<$Res>
    implements $BankAccountBalanceDetailCopyWith<$Res> {
  factory _$$BankAccountBalanceDetailImplCopyWith(
          _$BankAccountBalanceDetailImpl value,
          $Res Function(_$BankAccountBalanceDetailImpl) then) =
      __$$BankAccountBalanceDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? contactName,
      String? accountId,
      String? bankName,
      String? branchName,
      String? branchCode,
      String? accountType,
      String? accountNumber,
      String? baseDate,
      String? baseTime,
      String? baseDateTime,
      String? lastSyncedAt,
      int? currentBalance,
      int? checksIssuedByOtherBanks,
      int? overdraftLimit,
      int? withdrawableBalance,
      String? displayAccountName,
      bool? displayAccountFlag,
      int? displayOrder,
      String? remitterName,
      bool isFreeeReConnectFinancialInstitution});
}

/// @nodoc
class __$$BankAccountBalanceDetailImplCopyWithImpl<$Res>
    extends _$BankAccountBalanceDetailCopyWithImpl<$Res,
        _$BankAccountBalanceDetailImpl>
    implements _$$BankAccountBalanceDetailImplCopyWith<$Res> {
  __$$BankAccountBalanceDetailImplCopyWithImpl(
      _$BankAccountBalanceDetailImpl _value,
      $Res Function(_$BankAccountBalanceDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountBalanceDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? contactName = freezed,
    Object? accountId = freezed,
    Object? bankName = freezed,
    Object? branchName = freezed,
    Object? branchCode = freezed,
    Object? accountType = freezed,
    Object? accountNumber = freezed,
    Object? baseDate = freezed,
    Object? baseTime = freezed,
    Object? baseDateTime = freezed,
    Object? lastSyncedAt = freezed,
    Object? currentBalance = freezed,
    Object? checksIssuedByOtherBanks = freezed,
    Object? overdraftLimit = freezed,
    Object? withdrawableBalance = freezed,
    Object? displayAccountName = freezed,
    Object? displayAccountFlag = freezed,
    Object? displayOrder = freezed,
    Object? remitterName = freezed,
    Object? isFreeeReConnectFinancialInstitution = null,
  }) {
    return _then(_$BankAccountBalanceDetailImpl(
      contactName: freezed == contactName
          ? _value.contactName
          : contactName // ignore: cast_nullable_to_non_nullable
              as String?,
      accountId: freezed == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String?,
      bankName: freezed == bankName
          ? _value.bankName
          : bankName // ignore: cast_nullable_to_non_nullable
              as String?,
      branchName: freezed == branchName
          ? _value.branchName
          : branchName // ignore: cast_nullable_to_non_nullable
              as String?,
      branchCode: freezed == branchCode
          ? _value.branchCode
          : branchCode // ignore: cast_nullable_to_non_nullable
              as String?,
      accountType: freezed == accountType
          ? _value.accountType
          : accountType // ignore: cast_nullable_to_non_nullable
              as String?,
      accountNumber: freezed == accountNumber
          ? _value.accountNumber
          : accountNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      baseDateTime: freezed == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      currentBalance: freezed == currentBalance
          ? _value.currentBalance
          : currentBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      checksIssuedByOtherBanks: freezed == checksIssuedByOtherBanks
          ? _value.checksIssuedByOtherBanks
          : checksIssuedByOtherBanks // ignore: cast_nullable_to_non_nullable
              as int?,
      overdraftLimit: freezed == overdraftLimit
          ? _value.overdraftLimit
          : overdraftLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      withdrawableBalance: freezed == withdrawableBalance
          ? _value.withdrawableBalance
          : withdrawableBalance // ignore: cast_nullable_to_non_nullable
              as int?,
      displayAccountName: freezed == displayAccountName
          ? _value.displayAccountName
          : displayAccountName // ignore: cast_nullable_to_non_nullable
              as String?,
      displayAccountFlag: freezed == displayAccountFlag
          ? _value.displayAccountFlag
          : displayAccountFlag // ignore: cast_nullable_to_non_nullable
              as bool?,
      displayOrder: freezed == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int?,
      remitterName: freezed == remitterName
          ? _value.remitterName
          : remitterName // ignore: cast_nullable_to_non_nullable
              as String?,
      isFreeeReConnectFinancialInstitution: null ==
              isFreeeReConnectFinancialInstitution
          ? _value.isFreeeReConnectFinancialInstitution
          : isFreeeReConnectFinancialInstitution // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BankAccountBalanceDetailImpl implements _BankAccountBalanceDetail {
  const _$BankAccountBalanceDetailImpl(
      {this.contactName,
      this.accountId,
      this.bankName,
      this.branchName,
      this.branchCode,
      this.accountType,
      this.accountNumber,
      this.baseDate,
      this.baseTime,
      this.baseDateTime,
      this.lastSyncedAt,
      this.currentBalance,
      this.checksIssuedByOtherBanks,
      this.overdraftLimit,
      this.withdrawableBalance,
      this.displayAccountName,
      this.displayAccountFlag,
      this.displayOrder,
      this.remitterName,
      this.isFreeeReConnectFinancialInstitution = false});

  factory _$BankAccountBalanceDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$BankAccountBalanceDetailImplFromJson(json);

  @override
  final String? contactName;
  @override
  final String? accountId;
  @override
  final String? bankName;
  @override
  final String? branchName;
  @override
  final String? branchCode;
  @override
  final String? accountType;
  @override
  final String? accountNumber;
  @override
  final String? baseDate;
  @override
  final String? baseTime;
  @override
  final String? baseDateTime;
  @override
  final String? lastSyncedAt;
  @override
  final int? currentBalance;
  @override
  final int? checksIssuedByOtherBanks;
  @override
  final int? overdraftLimit;
  @override
  final int? withdrawableBalance;
  @override
  final String? displayAccountName;
// HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  @override
  final bool? displayAccountFlag;
// HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  @override
  final int? displayOrder;
// HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  @override
  final String? remitterName;
  @override
  @JsonKey()
  final bool isFreeeReConnectFinancialInstitution;

  @override
  String toString() {
    return 'BankAccountBalanceDetail(contactName: $contactName, accountId: $accountId, bankName: $bankName, branchName: $branchName, branchCode: $branchCode, accountType: $accountType, accountNumber: $accountNumber, baseDate: $baseDate, baseTime: $baseTime, baseDateTime: $baseDateTime, lastSyncedAt: $lastSyncedAt, currentBalance: $currentBalance, checksIssuedByOtherBanks: $checksIssuedByOtherBanks, overdraftLimit: $overdraftLimit, withdrawableBalance: $withdrawableBalance, displayAccountName: $displayAccountName, displayAccountFlag: $displayAccountFlag, displayOrder: $displayOrder, remitterName: $remitterName, isFreeeReConnectFinancialInstitution: $isFreeeReConnectFinancialInstitution)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountBalanceDetailImpl &&
            (identical(other.contactName, contactName) ||
                other.contactName == contactName) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.bankName, bankName) ||
                other.bankName == bankName) &&
            (identical(other.branchName, branchName) ||
                other.branchName == branchName) &&
            (identical(other.branchCode, branchCode) ||
                other.branchCode == branchCode) &&
            (identical(other.accountType, accountType) ||
                other.accountType == accountType) &&
            (identical(other.accountNumber, accountNumber) ||
                other.accountNumber == accountNumber) &&
            (identical(other.baseDate, baseDate) ||
                other.baseDate == baseDate) &&
            (identical(other.baseTime, baseTime) ||
                other.baseTime == baseTime) &&
            (identical(other.baseDateTime, baseDateTime) ||
                other.baseDateTime == baseDateTime) &&
            (identical(other.lastSyncedAt, lastSyncedAt) ||
                other.lastSyncedAt == lastSyncedAt) &&
            (identical(other.currentBalance, currentBalance) ||
                other.currentBalance == currentBalance) &&
            (identical(
                    other.checksIssuedByOtherBanks, checksIssuedByOtherBanks) ||
                other.checksIssuedByOtherBanks == checksIssuedByOtherBanks) &&
            (identical(other.overdraftLimit, overdraftLimit) ||
                other.overdraftLimit == overdraftLimit) &&
            (identical(other.withdrawableBalance, withdrawableBalance) ||
                other.withdrawableBalance == withdrawableBalance) &&
            (identical(other.displayAccountName, displayAccountName) ||
                other.displayAccountName == displayAccountName) &&
            (identical(other.displayAccountFlag, displayAccountFlag) ||
                other.displayAccountFlag == displayAccountFlag) &&
            (identical(other.displayOrder, displayOrder) ||
                other.displayOrder == displayOrder) &&
            (identical(other.remitterName, remitterName) ||
                other.remitterName == remitterName) &&
            (identical(other.isFreeeReConnectFinancialInstitution,
                    isFreeeReConnectFinancialInstitution) ||
                other.isFreeeReConnectFinancialInstitution ==
                    isFreeeReConnectFinancialInstitution));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        contactName,
        accountId,
        bankName,
        branchName,
        branchCode,
        accountType,
        accountNumber,
        baseDate,
        baseTime,
        baseDateTime,
        lastSyncedAt,
        currentBalance,
        checksIssuedByOtherBanks,
        overdraftLimit,
        withdrawableBalance,
        displayAccountName,
        displayAccountFlag,
        displayOrder,
        remitterName,
        isFreeeReConnectFinancialInstitution
      ]);

  /// Create a copy of BankAccountBalanceDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountBalanceDetailImplCopyWith<_$BankAccountBalanceDetailImpl>
      get copyWith => __$$BankAccountBalanceDetailImplCopyWithImpl<
          _$BankAccountBalanceDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BankAccountBalanceDetailImplToJson(
      this,
    );
  }
}

abstract class _BankAccountBalanceDetail implements BankAccountBalanceDetail {
  const factory _BankAccountBalanceDetail(
          {final String? contactName,
          final String? accountId,
          final String? bankName,
          final String? branchName,
          final String? branchCode,
          final String? accountType,
          final String? accountNumber,
          final String? baseDate,
          final String? baseTime,
          final String? baseDateTime,
          final String? lastSyncedAt,
          final int? currentBalance,
          final int? checksIssuedByOtherBanks,
          final int? overdraftLimit,
          final int? withdrawableBalance,
          final String? displayAccountName,
          final bool? displayAccountFlag,
          final int? displayOrder,
          final String? remitterName,
          final bool isFreeeReConnectFinancialInstitution}) =
      _$BankAccountBalanceDetailImpl;

  factory _BankAccountBalanceDetail.fromJson(Map<String, dynamic> json) =
      _$BankAccountBalanceDetailImpl.fromJson;

  @override
  String? get contactName;
  @override
  String? get accountId;
  @override
  String? get bankName;
  @override
  String? get branchName;
  @override
  String? get branchCode;
  @override
  String? get accountType;
  @override
  String? get accountNumber;
  @override
  String? get baseDate;
  @override
  String? get baseTime;
  @override
  String? get baseDateTime;
  @override
  String? get lastSyncedAt;
  @override
  int? get currentBalance;
  @override
  int? get checksIssuedByOtherBanks;
  @override
  int? get overdraftLimit;
  @override
  int? get withdrawableBalance;
  @override
  String?
      get displayAccountName; // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  @override
  bool? get displayAccountFlag; // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  @override
  int? get displayOrder; // HACKME : DisplayConfigクラスのfieldと重複しているため要削除検討
  @override
  String? get remitterName;
  @override
  bool get isFreeeReConnectFinancialInstitution;

  /// Create a copy of BankAccountBalanceDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountBalanceDetailImplCopyWith<_$BankAccountBalanceDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}
