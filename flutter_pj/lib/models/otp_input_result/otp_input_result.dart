import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'otp_input_result.freezed.dart';

@freezed
sealed class OtpInputResult with _$OtpInputResult {
  // IDaaSのOTP認証にて取得した暗号化Cookieを引数にセットする
  const factory OtpInputResult.success(EncryptedCookie idaasEncryptedCookie) =
      _Success;

  const factory OtpInputResult.cancel() = _Cancel;

  const factory OtpInputResult.error(AppError error) = _Error;
}
