import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_transaction_history.freezed.dart';

@freezed
class AccountTransactionHistory with _$AccountTransactionHistory {
  const factory AccountTransactionHistory.empty() =
      AccountTransactionHistoryEmpty;

  const factory AccountTransactionHistory.data({
    String? baseDateTime,
    int? itemCount,
    int? totalIncome,
    int? totalExpenditure,
    required List<AccountTransactionHistoryDetail> items,
  }) = AccountTransactionHistoryData;
}

@freezed
class AccountTransactionHistoryDetail with _$AccountTransactionHistoryDetail {
  const factory AccountTransactionHistoryDetail({
    /// 入金または出金
    required TransactionHistoryType type,

    /// 固定制預金または流動性預金
    required DepositType depositType,

    /// 振込依頼人名
    String? payerName,

    /// 起算日
    String? startingDate,

    /// 取引日
    String? tradingDate,

    /// 取引種別
    TransactionType? transactionType,

    /// 金額
    int? amount,

    /// 預入番号または照会番号
    String? referenceNumber,

    /// 依頼人コード
    String? remitterCode,

    /// 内他店手形
    int? checksIssuedByOtherBanksAmount,

    /// 手形・小切手区分名
    String? billAndCheckTypeName,

    /// 手形・小切手番号
    String? billAndCheckNumber,

    /// EDI情報
    String? ediInfo,

    /// 利率
    String? interestRate,

    /// 満期日
    String? maturityDateAd,

    /// 摘要
    String? abstract,
  }) = _AccountTransactionHistoryDetail;

  const AccountTransactionHistoryDetail._();

  String? get fixedInterestRate {
    if (interestRate == null) {
      return null;
    }
    //小数点区切り（上2桁整数部・下4桁小数部）
    final value =
        '${interestRate!.substring(0, 2)}.${interestRate!.substring(2)}%';
    // 整数部10の位が0の場合は0を除外
    if (value[0] == '0') {
      final nonZeroValue = value.substring(1);
      return nonZeroValue;
    }
    return value;
  }
}

enum TransactionHistoryType {
  /// 入金
  income,

  /// 出金
  expenditure,
}

enum DepositType {
  /// 固定制預金
  fixedDeposit,

  /// 流動性預金
  liquidityDeposit,
}

enum TransactionHistorySortType {
  /// 日時
  date,

  /// 金額
  amount,
}
