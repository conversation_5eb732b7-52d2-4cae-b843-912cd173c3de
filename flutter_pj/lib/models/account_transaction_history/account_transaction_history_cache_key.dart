import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_transaction_history_cache_key.freezed.dart';

@freezed
class AccountTransactionHistoryCacheKey
    with _$AccountTransactionHistoryCacheKey {
  const factory AccountTransactionHistoryCacheKey({
    required String accountId,
    required DateTime dateFrom,
    required DateTime dateTo,
  }) = _AccountTransactionHistoryCacheKey;
}
