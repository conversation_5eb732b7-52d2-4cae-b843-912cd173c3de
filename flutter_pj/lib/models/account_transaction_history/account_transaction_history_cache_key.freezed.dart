// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_transaction_history_cache_key.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountTransactionHistoryCacheKey {
  String get accountId => throw _privateConstructorUsedError;
  DateTime get dateFrom => throw _privateConstructorUsedError;
  DateTime get dateTo => throw _privateConstructorUsedError;

  /// Create a copy of AccountTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountTransactionHistoryCacheKeyCopyWith<AccountTransactionHistoryCacheKey>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTransactionHistoryCacheKeyCopyWith<$Res> {
  factory $AccountTransactionHistoryCacheKeyCopyWith(
          AccountTransactionHistoryCacheKey value,
          $Res Function(AccountTransactionHistoryCacheKey) then) =
      _$AccountTransactionHistoryCacheKeyCopyWithImpl<$Res,
          AccountTransactionHistoryCacheKey>;
  @useResult
  $Res call({String accountId, DateTime dateFrom, DateTime dateTo});
}

/// @nodoc
class _$AccountTransactionHistoryCacheKeyCopyWithImpl<$Res,
        $Val extends AccountTransactionHistoryCacheKey>
    implements $AccountTransactionHistoryCacheKeyCopyWith<$Res> {
  _$AccountTransactionHistoryCacheKeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? dateFrom = null,
    Object? dateTo = null,
  }) {
    return _then(_value.copyWith(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      dateFrom: null == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dateTo: null == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountTransactionHistoryCacheKeyImplCopyWith<$Res>
    implements $AccountTransactionHistoryCacheKeyCopyWith<$Res> {
  factory _$$AccountTransactionHistoryCacheKeyImplCopyWith(
          _$AccountTransactionHistoryCacheKeyImpl value,
          $Res Function(_$AccountTransactionHistoryCacheKeyImpl) then) =
      __$$AccountTransactionHistoryCacheKeyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accountId, DateTime dateFrom, DateTime dateTo});
}

/// @nodoc
class __$$AccountTransactionHistoryCacheKeyImplCopyWithImpl<$Res>
    extends _$AccountTransactionHistoryCacheKeyCopyWithImpl<$Res,
        _$AccountTransactionHistoryCacheKeyImpl>
    implements _$$AccountTransactionHistoryCacheKeyImplCopyWith<$Res> {
  __$$AccountTransactionHistoryCacheKeyImplCopyWithImpl(
      _$AccountTransactionHistoryCacheKeyImpl _value,
      $Res Function(_$AccountTransactionHistoryCacheKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? dateFrom = null,
    Object? dateTo = null,
  }) {
    return _then(_$AccountTransactionHistoryCacheKeyImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      dateFrom: null == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dateTo: null == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$AccountTransactionHistoryCacheKeyImpl
    implements _AccountTransactionHistoryCacheKey {
  const _$AccountTransactionHistoryCacheKeyImpl(
      {required this.accountId, required this.dateFrom, required this.dateTo});

  @override
  final String accountId;
  @override
  final DateTime dateFrom;
  @override
  final DateTime dateTo;

  @override
  String toString() {
    return 'AccountTransactionHistoryCacheKey(accountId: $accountId, dateFrom: $dateFrom, dateTo: $dateTo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionHistoryCacheKeyImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, dateFrom, dateTo);

  /// Create a copy of AccountTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTransactionHistoryCacheKeyImplCopyWith<
          _$AccountTransactionHistoryCacheKeyImpl>
      get copyWith => __$$AccountTransactionHistoryCacheKeyImplCopyWithImpl<
          _$AccountTransactionHistoryCacheKeyImpl>(this, _$identity);
}

abstract class _AccountTransactionHistoryCacheKey
    implements AccountTransactionHistoryCacheKey {
  const factory _AccountTransactionHistoryCacheKey(
          {required final String accountId,
          required final DateTime dateFrom,
          required final DateTime dateTo}) =
      _$AccountTransactionHistoryCacheKeyImpl;

  @override
  String get accountId;
  @override
  DateTime get dateFrom;
  @override
  DateTime get dateTo;

  /// Create a copy of AccountTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTransactionHistoryCacheKeyImplCopyWith<
          _$AccountTransactionHistoryCacheKeyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
