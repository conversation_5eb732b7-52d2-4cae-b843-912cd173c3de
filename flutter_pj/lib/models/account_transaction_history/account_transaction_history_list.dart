import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:intl/intl.dart';

/// フィルターやソートのロジックをまとめた拡張関数
extension AccountTransactionHistoryDetailList
    on List<AccountTransactionHistoryDetail> {
  DateTime _parseDate(String dateText) {
    return DateFormat('yyyy/M/d').parse(dateText);
  }

  List<AccountTransactionHistoryDetail> filterByType(
    TransactionHistoryFilterType filterType,
  ) {
    if (filterType != TransactionHistoryFilterType.all) {
      // 入金 or 出金でフィルタリング
      final type = filterType == TransactionHistoryFilterType.income
          ? TransactionHistoryType.income
          : TransactionHistoryType.expenditure;
      return where((item) => item.type == type).toList();
    } else {
      // 何もしない
      return this;
    }
  }

  List<AccountTransactionHistoryDetail> filterByStartingDatePeriod(
    DateTime? from,
    DateTime? to,
  ) {
    if (from is DateTime && to is DateTime) {
      // 開始日と終了日の範囲でフィルタリング
      return where((item) {
        final startingDate = item.startingDate;
        if (startingDate is String) {
          final date = _parseDate(startingDate);
          return date.isSameDateOrAfter(from) && date.isSameDateOrBefore(to);
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> filterByTradingDatePeriod(
    DateTime? from,
    DateTime? to,
  ) {
    if (from is DateTime && to is DateTime) {
      // 開始日と終了日の範囲でフィルタリング
      return where((item) {
        final tradingDate = item.tradingDate;
        if (tradingDate is String) {
          final date = _parseDate(tradingDate);
          return date.isSameDateOrAfter(from) && date.isSameDateOrBefore(to);
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> filterByTransactionType(
    List<TransactionTypeCode>? transactionTypeCode,
  ) {
    if (transactionTypeCode is List<TransactionTypeCode>) {
      // 取引区分でフィルタリング
      return where((item) {
        final transactionType = item.transactionType;
        if (transactionType is TransactionType) {
          return transactionTypeCode.contains(transactionType.code);
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> filterByPayerName(
    String? payerName,
  ) {
    if (payerName is String) {
      // 振込依頼人名でフィルタリング
      return where((item) {
        return item.payerName?.contains(payerName) ?? false;
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> filterByMinAmount(
    int? minAmount,
  ) {
    if (minAmount is int) {
      // 金額の最小値でフィルタリング
      return where((item) {
        final amount = item.amount;
        if (amount is int) {
          return amount >= minAmount;
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> filterByMaxAmount(
    int? maxAmount,
  ) {
    if (maxAmount is int) {
      // 金額の最大値でフィルタリング
      return where((item) {
        final amount = item.amount;
        if (amount is int) {
          return amount <= maxAmount;
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> filterByReferenceNumber(
    String? referenceNumber,
  ) {
    if (referenceNumber is String) {
      // 照会番号（預入番号）でフィルタリング
      return where((item) {
        return item.referenceNumber == referenceNumber;
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<AccountTransactionHistoryDetail> sortItems(
    TransactionHistorySortType sortType,
    TransactionHistorySortOrder order,
  ) {
    final List<AccountTransactionHistoryDetail> sortedItems;
    if (sortType == TransactionHistorySortType.date) {
      // 日時
      if (order == TransactionHistorySortOrder.ascending) {
        // 昇順
        sortedItems = sorted(
          (a, b) {
            // 日時ソートの実施
            // 取引日が存在している場合のみ実施し、取引日がない場合には照会番号にてソート
            if (a.tradingDate != null && b.tradingDate != null) {
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              final dateTimeComparison = tradingDateA.compareTo(tradingDateB);
              // 日時によるソートが同値になった場合、照会番号にて第二ソート
              if (dateTimeComparison != 0) {
                return dateTimeComparison;
              } else {
                final referenceNumberA = a.referenceNumber ?? '';
                final referenceNumberB = b.referenceNumber ?? '';
                return referenceNumberA.compareTo(referenceNumberB);
              }
            } else {
              final referenceNumberA = a.referenceNumber ?? '';
              final referenceNumberB = b.referenceNumber ?? '';
              return referenceNumberA.compareTo(referenceNumberB);
            }
          },
        );
      } else {
        // 降順
        sortedItems = sorted(
          (a, b) {
            // 日時ソートの実施
            // 取引日が存在している場合のみ実施し、取引日がない場合には照会番号にてソート
            if (a.tradingDate != null && b.tradingDate != null) {
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              final dateTimeComparison = tradingDateB.compareTo(tradingDateA);
              // 日時によるソートが同値になった場合、照会番号にて第二ソート
              if (dateTimeComparison != 0) {
                return dateTimeComparison;
              } else {
                final referenceNumberA = a.referenceNumber ?? '';
                final referenceNumberB = b.referenceNumber ?? '';
                return referenceNumberB.compareTo(referenceNumberA);
              }
            } else {
              final referenceNumberA = a.referenceNumber ?? '';
              final referenceNumberB = b.referenceNumber ?? '';
              return referenceNumberB.compareTo(referenceNumberA);
            }
          },
        );
      }
    } else {
      // 金額
      if (order == TransactionHistorySortOrder.ascending) {
        // 昇順
        sortedItems = sorted((a, b) {
          final amountComparison = a.amount!.compareTo(b.amount!);
          if (amountComparison != 0) {
            return amountComparison;
          } else {
            // 同額の場合は照会番号（預入番号）降順
            return b.referenceNumber!.compareTo(a.referenceNumber!);
          }
        });
      } else {
        // 降順
        sortedItems = sorted((a, b) {
          final amountComparison = b.amount!.compareTo(a.amount!);
          if (amountComparison != 0) {
            return amountComparison;
          } else {
            // 同額の場合は照会番号（預入番号）降順
            return b.referenceNumber!.compareTo(a.referenceNumber!);
          }
        });
      }
    }
    return sortedItems;
  }
}

extension _DateTimeExt on DateTime {
  bool isSameDate(DateTime? other) {
    if (other is DateTime) {
      return year == other.year && month == other.month && day == other.day;
    }
    return false;
  }

  bool isSameDateOrAfter(DateTime? other) {
    if (other is DateTime) {
      return isAfter(other) || isSameDate(other);
    }
    return false;
  }

  bool isSameDateOrBefore(DateTime? other) {
    if (other is DateTime) {
      return isBefore(other) || isSameDate(other);
    }
    return false;
  }
}

DateTime _convertToDateTime(String date) {
  return DateFormat('yyyy/MM/dd').parse(date);
}
