// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_transaction_history.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AccountTransactionHistory {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            String? baseDateTime,
            int? itemCount,
            int? totalIncome,
            int? totalExpenditure,
            List<AccountTransactionHistoryDetail> items)
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(String? baseDateTime, int? itemCount, int? totalIncome,
            int? totalExpenditure, List<AccountTransactionHistoryDetail> items)?
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(String? baseDateTime, int? itemCount, int? totalIncome,
            int? totalExpenditure, List<AccountTransactionHistoryDetail> items)?
        data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountTransactionHistoryEmpty value) empty,
    required TResult Function(AccountTransactionHistoryData value) data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountTransactionHistoryEmpty value)? empty,
    TResult? Function(AccountTransactionHistoryData value)? data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountTransactionHistoryEmpty value)? empty,
    TResult Function(AccountTransactionHistoryData value)? data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTransactionHistoryCopyWith<$Res> {
  factory $AccountTransactionHistoryCopyWith(AccountTransactionHistory value,
          $Res Function(AccountTransactionHistory) then) =
      _$AccountTransactionHistoryCopyWithImpl<$Res, AccountTransactionHistory>;
}

/// @nodoc
class _$AccountTransactionHistoryCopyWithImpl<$Res,
        $Val extends AccountTransactionHistory>
    implements $AccountTransactionHistoryCopyWith<$Res> {
  _$AccountTransactionHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$AccountTransactionHistoryEmptyImplCopyWith<$Res> {
  factory _$$AccountTransactionHistoryEmptyImplCopyWith(
          _$AccountTransactionHistoryEmptyImpl value,
          $Res Function(_$AccountTransactionHistoryEmptyImpl) then) =
      __$$AccountTransactionHistoryEmptyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$AccountTransactionHistoryEmptyImplCopyWithImpl<$Res>
    extends _$AccountTransactionHistoryCopyWithImpl<$Res,
        _$AccountTransactionHistoryEmptyImpl>
    implements _$$AccountTransactionHistoryEmptyImplCopyWith<$Res> {
  __$$AccountTransactionHistoryEmptyImplCopyWithImpl(
      _$AccountTransactionHistoryEmptyImpl _value,
      $Res Function(_$AccountTransactionHistoryEmptyImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$AccountTransactionHistoryEmptyImpl
    implements AccountTransactionHistoryEmpty {
  const _$AccountTransactionHistoryEmptyImpl();

  @override
  String toString() {
    return 'AccountTransactionHistory.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionHistoryEmptyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            String? baseDateTime,
            int? itemCount,
            int? totalIncome,
            int? totalExpenditure,
            List<AccountTransactionHistoryDetail> items)
        data,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(String? baseDateTime, int? itemCount, int? totalIncome,
            int? totalExpenditure, List<AccountTransactionHistoryDetail> items)?
        data,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(String? baseDateTime, int? itemCount, int? totalIncome,
            int? totalExpenditure, List<AccountTransactionHistoryDetail> items)?
        data,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountTransactionHistoryEmpty value) empty,
    required TResult Function(AccountTransactionHistoryData value) data,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountTransactionHistoryEmpty value)? empty,
    TResult? Function(AccountTransactionHistoryData value)? data,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountTransactionHistoryEmpty value)? empty,
    TResult Function(AccountTransactionHistoryData value)? data,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class AccountTransactionHistoryEmpty
    implements AccountTransactionHistory {
  const factory AccountTransactionHistoryEmpty() =
      _$AccountTransactionHistoryEmptyImpl;
}

/// @nodoc
abstract class _$$AccountTransactionHistoryDataImplCopyWith<$Res> {
  factory _$$AccountTransactionHistoryDataImplCopyWith(
          _$AccountTransactionHistoryDataImpl value,
          $Res Function(_$AccountTransactionHistoryDataImpl) then) =
      __$$AccountTransactionHistoryDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String? baseDateTime,
      int? itemCount,
      int? totalIncome,
      int? totalExpenditure,
      List<AccountTransactionHistoryDetail> items});
}

/// @nodoc
class __$$AccountTransactionHistoryDataImplCopyWithImpl<$Res>
    extends _$AccountTransactionHistoryCopyWithImpl<$Res,
        _$AccountTransactionHistoryDataImpl>
    implements _$$AccountTransactionHistoryDataImplCopyWith<$Res> {
  __$$AccountTransactionHistoryDataImplCopyWithImpl(
      _$AccountTransactionHistoryDataImpl _value,
      $Res Function(_$AccountTransactionHistoryDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseDateTime = freezed,
    Object? itemCount = freezed,
    Object? totalIncome = freezed,
    Object? totalExpenditure = freezed,
    Object? items = null,
  }) {
    return _then(_$AccountTransactionHistoryDataImpl(
      baseDateTime: freezed == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCount: freezed == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalIncome: freezed == totalIncome
          ? _value.totalIncome
          : totalIncome // ignore: cast_nullable_to_non_nullable
              as int?,
      totalExpenditure: freezed == totalExpenditure
          ? _value.totalExpenditure
          : totalExpenditure // ignore: cast_nullable_to_non_nullable
              as int?,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<AccountTransactionHistoryDetail>,
    ));
  }
}

/// @nodoc

class _$AccountTransactionHistoryDataImpl
    implements AccountTransactionHistoryData {
  const _$AccountTransactionHistoryDataImpl(
      {this.baseDateTime,
      this.itemCount,
      this.totalIncome,
      this.totalExpenditure,
      required final List<AccountTransactionHistoryDetail> items})
      : _items = items;

  @override
  final String? baseDateTime;
  @override
  final int? itemCount;
  @override
  final int? totalIncome;
  @override
  final int? totalExpenditure;
  final List<AccountTransactionHistoryDetail> _items;
  @override
  List<AccountTransactionHistoryDetail> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'AccountTransactionHistory.data(baseDateTime: $baseDateTime, itemCount: $itemCount, totalIncome: $totalIncome, totalExpenditure: $totalExpenditure, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionHistoryDataImpl &&
            (identical(other.baseDateTime, baseDateTime) ||
                other.baseDateTime == baseDateTime) &&
            (identical(other.itemCount, itemCount) ||
                other.itemCount == itemCount) &&
            (identical(other.totalIncome, totalIncome) ||
                other.totalIncome == totalIncome) &&
            (identical(other.totalExpenditure, totalExpenditure) ||
                other.totalExpenditure == totalExpenditure) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      baseDateTime,
      itemCount,
      totalIncome,
      totalExpenditure,
      const DeepCollectionEquality().hash(_items));

  /// Create a copy of AccountTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTransactionHistoryDataImplCopyWith<
          _$AccountTransactionHistoryDataImpl>
      get copyWith => __$$AccountTransactionHistoryDataImplCopyWithImpl<
          _$AccountTransactionHistoryDataImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            String? baseDateTime,
            int? itemCount,
            int? totalIncome,
            int? totalExpenditure,
            List<AccountTransactionHistoryDetail> items)
        data,
  }) {
    return data(baseDateTime, itemCount, totalIncome, totalExpenditure, items);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(String? baseDateTime, int? itemCount, int? totalIncome,
            int? totalExpenditure, List<AccountTransactionHistoryDetail> items)?
        data,
  }) {
    return data?.call(
        baseDateTime, itemCount, totalIncome, totalExpenditure, items);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(String? baseDateTime, int? itemCount, int? totalIncome,
            int? totalExpenditure, List<AccountTransactionHistoryDetail> items)?
        data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(
          baseDateTime, itemCount, totalIncome, totalExpenditure, items);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountTransactionHistoryEmpty value) empty,
    required TResult Function(AccountTransactionHistoryData value) data,
  }) {
    return data(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountTransactionHistoryEmpty value)? empty,
    TResult? Function(AccountTransactionHistoryData value)? data,
  }) {
    return data?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountTransactionHistoryEmpty value)? empty,
    TResult Function(AccountTransactionHistoryData value)? data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(this);
    }
    return orElse();
  }
}

abstract class AccountTransactionHistoryData
    implements AccountTransactionHistory {
  const factory AccountTransactionHistoryData(
          {final String? baseDateTime,
          final int? itemCount,
          final int? totalIncome,
          final int? totalExpenditure,
          required final List<AccountTransactionHistoryDetail> items}) =
      _$AccountTransactionHistoryDataImpl;

  String? get baseDateTime;
  int? get itemCount;
  int? get totalIncome;
  int? get totalExpenditure;
  List<AccountTransactionHistoryDetail> get items;

  /// Create a copy of AccountTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTransactionHistoryDataImplCopyWith<
          _$AccountTransactionHistoryDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$AccountTransactionHistoryDetail {
  /// 入金または出金
  TransactionHistoryType get type => throw _privateConstructorUsedError;

  /// 固定制預金または流動性預金
  DepositType get depositType => throw _privateConstructorUsedError;

  /// 振込依頼人名
  String? get payerName => throw _privateConstructorUsedError;

  /// 起算日
  String? get startingDate => throw _privateConstructorUsedError;

  /// 取引日
  String? get tradingDate => throw _privateConstructorUsedError;

  /// 取引種別
  TransactionType? get transactionType => throw _privateConstructorUsedError;

  /// 金額
  int? get amount => throw _privateConstructorUsedError;

  /// 預入番号または照会番号
  String? get referenceNumber => throw _privateConstructorUsedError;

  /// 依頼人コード
  String? get remitterCode => throw _privateConstructorUsedError;

  /// 内他店手形
  int? get checksIssuedByOtherBanksAmount => throw _privateConstructorUsedError;

  /// 手形・小切手区分名
  String? get billAndCheckTypeName => throw _privateConstructorUsedError;

  /// 手形・小切手番号
  String? get billAndCheckNumber => throw _privateConstructorUsedError;

  /// EDI情報
  String? get ediInfo => throw _privateConstructorUsedError;

  /// 利率
  String? get interestRate => throw _privateConstructorUsedError;

  /// 満期日
  String? get maturityDateAd => throw _privateConstructorUsedError;

  /// 摘要
  String? get abstract => throw _privateConstructorUsedError;

  /// Create a copy of AccountTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountTransactionHistoryDetailCopyWith<AccountTransactionHistoryDetail>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTransactionHistoryDetailCopyWith<$Res> {
  factory $AccountTransactionHistoryDetailCopyWith(
          AccountTransactionHistoryDetail value,
          $Res Function(AccountTransactionHistoryDetail) then) =
      _$AccountTransactionHistoryDetailCopyWithImpl<$Res,
          AccountTransactionHistoryDetail>;
  @useResult
  $Res call(
      {TransactionHistoryType type,
      DepositType depositType,
      String? payerName,
      String? startingDate,
      String? tradingDate,
      TransactionType? transactionType,
      int? amount,
      String? referenceNumber,
      String? remitterCode,
      int? checksIssuedByOtherBanksAmount,
      String? billAndCheckTypeName,
      String? billAndCheckNumber,
      String? ediInfo,
      String? interestRate,
      String? maturityDateAd,
      String? abstract});

  $TransactionTypeCopyWith<$Res>? get transactionType;
}

/// @nodoc
class _$AccountTransactionHistoryDetailCopyWithImpl<$Res,
        $Val extends AccountTransactionHistoryDetail>
    implements $AccountTransactionHistoryDetailCopyWith<$Res> {
  _$AccountTransactionHistoryDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? depositType = null,
    Object? payerName = freezed,
    Object? startingDate = freezed,
    Object? tradingDate = freezed,
    Object? transactionType = freezed,
    Object? amount = freezed,
    Object? referenceNumber = freezed,
    Object? remitterCode = freezed,
    Object? checksIssuedByOtherBanksAmount = freezed,
    Object? billAndCheckTypeName = freezed,
    Object? billAndCheckNumber = freezed,
    Object? ediInfo = freezed,
    Object? interestRate = freezed,
    Object? maturityDateAd = freezed,
    Object? abstract = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TransactionHistoryType,
      depositType: null == depositType
          ? _value.depositType
          : depositType // ignore: cast_nullable_to_non_nullable
              as DepositType,
      payerName: freezed == payerName
          ? _value.payerName
          : payerName // ignore: cast_nullable_to_non_nullable
              as String?,
      startingDate: freezed == startingDate
          ? _value.startingDate
          : startingDate // ignore: cast_nullable_to_non_nullable
              as String?,
      tradingDate: freezed == tradingDate
          ? _value.tradingDate
          : tradingDate // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: freezed == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as TransactionType?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      referenceNumber: freezed == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterCode: freezed == remitterCode
          ? _value.remitterCode
          : remitterCode // ignore: cast_nullable_to_non_nullable
              as String?,
      checksIssuedByOtherBanksAmount: freezed == checksIssuedByOtherBanksAmount
          ? _value.checksIssuedByOtherBanksAmount
          : checksIssuedByOtherBanksAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      billAndCheckTypeName: freezed == billAndCheckTypeName
          ? _value.billAndCheckTypeName
          : billAndCheckTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      billAndCheckNumber: freezed == billAndCheckNumber
          ? _value.billAndCheckNumber
          : billAndCheckNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      ediInfo: freezed == ediInfo
          ? _value.ediInfo
          : ediInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as String?,
      maturityDateAd: freezed == maturityDateAd
          ? _value.maturityDateAd
          : maturityDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      abstract: freezed == abstract
          ? _value.abstract
          : abstract // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of AccountTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TransactionTypeCopyWith<$Res>? get transactionType {
    if (_value.transactionType == null) {
      return null;
    }

    return $TransactionTypeCopyWith<$Res>(_value.transactionType!, (value) {
      return _then(_value.copyWith(transactionType: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AccountTransactionHistoryDetailImplCopyWith<$Res>
    implements $AccountTransactionHistoryDetailCopyWith<$Res> {
  factory _$$AccountTransactionHistoryDetailImplCopyWith(
          _$AccountTransactionHistoryDetailImpl value,
          $Res Function(_$AccountTransactionHistoryDetailImpl) then) =
      __$$AccountTransactionHistoryDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TransactionHistoryType type,
      DepositType depositType,
      String? payerName,
      String? startingDate,
      String? tradingDate,
      TransactionType? transactionType,
      int? amount,
      String? referenceNumber,
      String? remitterCode,
      int? checksIssuedByOtherBanksAmount,
      String? billAndCheckTypeName,
      String? billAndCheckNumber,
      String? ediInfo,
      String? interestRate,
      String? maturityDateAd,
      String? abstract});

  @override
  $TransactionTypeCopyWith<$Res>? get transactionType;
}

/// @nodoc
class __$$AccountTransactionHistoryDetailImplCopyWithImpl<$Res>
    extends _$AccountTransactionHistoryDetailCopyWithImpl<$Res,
        _$AccountTransactionHistoryDetailImpl>
    implements _$$AccountTransactionHistoryDetailImplCopyWith<$Res> {
  __$$AccountTransactionHistoryDetailImplCopyWithImpl(
      _$AccountTransactionHistoryDetailImpl _value,
      $Res Function(_$AccountTransactionHistoryDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? depositType = null,
    Object? payerName = freezed,
    Object? startingDate = freezed,
    Object? tradingDate = freezed,
    Object? transactionType = freezed,
    Object? amount = freezed,
    Object? referenceNumber = freezed,
    Object? remitterCode = freezed,
    Object? checksIssuedByOtherBanksAmount = freezed,
    Object? billAndCheckTypeName = freezed,
    Object? billAndCheckNumber = freezed,
    Object? ediInfo = freezed,
    Object? interestRate = freezed,
    Object? maturityDateAd = freezed,
    Object? abstract = freezed,
  }) {
    return _then(_$AccountTransactionHistoryDetailImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TransactionHistoryType,
      depositType: null == depositType
          ? _value.depositType
          : depositType // ignore: cast_nullable_to_non_nullable
              as DepositType,
      payerName: freezed == payerName
          ? _value.payerName
          : payerName // ignore: cast_nullable_to_non_nullable
              as String?,
      startingDate: freezed == startingDate
          ? _value.startingDate
          : startingDate // ignore: cast_nullable_to_non_nullable
              as String?,
      tradingDate: freezed == tradingDate
          ? _value.tradingDate
          : tradingDate // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: freezed == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as TransactionType?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      referenceNumber: freezed == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterCode: freezed == remitterCode
          ? _value.remitterCode
          : remitterCode // ignore: cast_nullable_to_non_nullable
              as String?,
      checksIssuedByOtherBanksAmount: freezed == checksIssuedByOtherBanksAmount
          ? _value.checksIssuedByOtherBanksAmount
          : checksIssuedByOtherBanksAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      billAndCheckTypeName: freezed == billAndCheckTypeName
          ? _value.billAndCheckTypeName
          : billAndCheckTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      billAndCheckNumber: freezed == billAndCheckNumber
          ? _value.billAndCheckNumber
          : billAndCheckNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      ediInfo: freezed == ediInfo
          ? _value.ediInfo
          : ediInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as String?,
      maturityDateAd: freezed == maturityDateAd
          ? _value.maturityDateAd
          : maturityDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      abstract: freezed == abstract
          ? _value.abstract
          : abstract // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$AccountTransactionHistoryDetailImpl
    extends _AccountTransactionHistoryDetail {
  const _$AccountTransactionHistoryDetailImpl(
      {required this.type,
      required this.depositType,
      this.payerName,
      this.startingDate,
      this.tradingDate,
      this.transactionType,
      this.amount,
      this.referenceNumber,
      this.remitterCode,
      this.checksIssuedByOtherBanksAmount,
      this.billAndCheckTypeName,
      this.billAndCheckNumber,
      this.ediInfo,
      this.interestRate,
      this.maturityDateAd,
      this.abstract})
      : super._();

  /// 入金または出金
  @override
  final TransactionHistoryType type;

  /// 固定制預金または流動性預金
  @override
  final DepositType depositType;

  /// 振込依頼人名
  @override
  final String? payerName;

  /// 起算日
  @override
  final String? startingDate;

  /// 取引日
  @override
  final String? tradingDate;

  /// 取引種別
  @override
  final TransactionType? transactionType;

  /// 金額
  @override
  final int? amount;

  /// 預入番号または照会番号
  @override
  final String? referenceNumber;

  /// 依頼人コード
  @override
  final String? remitterCode;

  /// 内他店手形
  @override
  final int? checksIssuedByOtherBanksAmount;

  /// 手形・小切手区分名
  @override
  final String? billAndCheckTypeName;

  /// 手形・小切手番号
  @override
  final String? billAndCheckNumber;

  /// EDI情報
  @override
  final String? ediInfo;

  /// 利率
  @override
  final String? interestRate;

  /// 満期日
  @override
  final String? maturityDateAd;

  /// 摘要
  @override
  final String? abstract;

  @override
  String toString() {
    return 'AccountTransactionHistoryDetail(type: $type, depositType: $depositType, payerName: $payerName, startingDate: $startingDate, tradingDate: $tradingDate, transactionType: $transactionType, amount: $amount, referenceNumber: $referenceNumber, remitterCode: $remitterCode, checksIssuedByOtherBanksAmount: $checksIssuedByOtherBanksAmount, billAndCheckTypeName: $billAndCheckTypeName, billAndCheckNumber: $billAndCheckNumber, ediInfo: $ediInfo, interestRate: $interestRate, maturityDateAd: $maturityDateAd, abstract: $abstract)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionHistoryDetailImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.depositType, depositType) ||
                other.depositType == depositType) &&
            (identical(other.payerName, payerName) ||
                other.payerName == payerName) &&
            (identical(other.startingDate, startingDate) ||
                other.startingDate == startingDate) &&
            (identical(other.tradingDate, tradingDate) ||
                other.tradingDate == tradingDate) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.referenceNumber, referenceNumber) ||
                other.referenceNumber == referenceNumber) &&
            (identical(other.remitterCode, remitterCode) ||
                other.remitterCode == remitterCode) &&
            (identical(other.checksIssuedByOtherBanksAmount,
                    checksIssuedByOtherBanksAmount) ||
                other.checksIssuedByOtherBanksAmount ==
                    checksIssuedByOtherBanksAmount) &&
            (identical(other.billAndCheckTypeName, billAndCheckTypeName) ||
                other.billAndCheckTypeName == billAndCheckTypeName) &&
            (identical(other.billAndCheckNumber, billAndCheckNumber) ||
                other.billAndCheckNumber == billAndCheckNumber) &&
            (identical(other.ediInfo, ediInfo) || other.ediInfo == ediInfo) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.maturityDateAd, maturityDateAd) ||
                other.maturityDateAd == maturityDateAd) &&
            (identical(other.abstract, abstract) ||
                other.abstract == abstract));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      type,
      depositType,
      payerName,
      startingDate,
      tradingDate,
      transactionType,
      amount,
      referenceNumber,
      remitterCode,
      checksIssuedByOtherBanksAmount,
      billAndCheckTypeName,
      billAndCheckNumber,
      ediInfo,
      interestRate,
      maturityDateAd,
      abstract);

  /// Create a copy of AccountTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTransactionHistoryDetailImplCopyWith<
          _$AccountTransactionHistoryDetailImpl>
      get copyWith => __$$AccountTransactionHistoryDetailImplCopyWithImpl<
          _$AccountTransactionHistoryDetailImpl>(this, _$identity);
}

abstract class _AccountTransactionHistoryDetail
    extends AccountTransactionHistoryDetail {
  const factory _AccountTransactionHistoryDetail(
      {required final TransactionHistoryType type,
      required final DepositType depositType,
      final String? payerName,
      final String? startingDate,
      final String? tradingDate,
      final TransactionType? transactionType,
      final int? amount,
      final String? referenceNumber,
      final String? remitterCode,
      final int? checksIssuedByOtherBanksAmount,
      final String? billAndCheckTypeName,
      final String? billAndCheckNumber,
      final String? ediInfo,
      final String? interestRate,
      final String? maturityDateAd,
      final String? abstract}) = _$AccountTransactionHistoryDetailImpl;
  const _AccountTransactionHistoryDetail._() : super._();

  /// 入金または出金
  @override
  TransactionHistoryType get type;

  /// 固定制預金または流動性預金
  @override
  DepositType get depositType;

  /// 振込依頼人名
  @override
  String? get payerName;

  /// 起算日
  @override
  String? get startingDate;

  /// 取引日
  @override
  String? get tradingDate;

  /// 取引種別
  @override
  TransactionType? get transactionType;

  /// 金額
  @override
  int? get amount;

  /// 預入番号または照会番号
  @override
  String? get referenceNumber;

  /// 依頼人コード
  @override
  String? get remitterCode;

  /// 内他店手形
  @override
  int? get checksIssuedByOtherBanksAmount;

  /// 手形・小切手区分名
  @override
  String? get billAndCheckTypeName;

  /// 手形・小切手番号
  @override
  String? get billAndCheckNumber;

  /// EDI情報
  @override
  String? get ediInfo;

  /// 利率
  @override
  String? get interestRate;

  /// 満期日
  @override
  String? get maturityDateAd;

  /// 摘要
  @override
  String? get abstract;

  /// Create a copy of AccountTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTransactionHistoryDetailImplCopyWith<
          _$AccountTransactionHistoryDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}
