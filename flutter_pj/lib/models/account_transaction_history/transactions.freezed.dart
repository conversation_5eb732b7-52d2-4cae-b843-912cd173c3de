// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transactions.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$Transactions {
  AccountTransactionHistoryData? get web21Transactions =>
      throw _privateConstructorUsedError;
  FreeeTransactionHistoryData? get freeeTransactions =>
      throw _privateConstructorUsedError;

  /// Create a copy of Transactions
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionsCopyWith<Transactions> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionsCopyWith<$Res> {
  factory $TransactionsCopyWith(
          Transactions value, $Res Function(Transactions) then) =
      _$TransactionsCopyWithImpl<$Res, Transactions>;
  @useResult
  $Res call(
      {AccountTransactionHistoryData? web21Transactions,
      FreeeTransactionHistoryData? freeeTransactions});
}

/// @nodoc
class _$TransactionsCopyWithImpl<$Res, $Val extends Transactions>
    implements $TransactionsCopyWith<$Res> {
  _$TransactionsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Transactions
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? web21Transactions = freezed,
    Object? freeeTransactions = freezed,
  }) {
    return _then(_value.copyWith(
      web21Transactions: freezed == web21Transactions
          ? _value.web21Transactions
          : web21Transactions // ignore: cast_nullable_to_non_nullable
              as AccountTransactionHistoryData?,
      freeeTransactions: freezed == freeeTransactions
          ? _value.freeeTransactions
          : freeeTransactions // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistoryData?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransactionsImplCopyWith<$Res>
    implements $TransactionsCopyWith<$Res> {
  factory _$$TransactionsImplCopyWith(
          _$TransactionsImpl value, $Res Function(_$TransactionsImpl) then) =
      __$$TransactionsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AccountTransactionHistoryData? web21Transactions,
      FreeeTransactionHistoryData? freeeTransactions});
}

/// @nodoc
class __$$TransactionsImplCopyWithImpl<$Res>
    extends _$TransactionsCopyWithImpl<$Res, _$TransactionsImpl>
    implements _$$TransactionsImplCopyWith<$Res> {
  __$$TransactionsImplCopyWithImpl(
      _$TransactionsImpl _value, $Res Function(_$TransactionsImpl) _then)
      : super(_value, _then);

  /// Create a copy of Transactions
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? web21Transactions = freezed,
    Object? freeeTransactions = freezed,
  }) {
    return _then(_$TransactionsImpl(
      web21Transactions: freezed == web21Transactions
          ? _value.web21Transactions
          : web21Transactions // ignore: cast_nullable_to_non_nullable
              as AccountTransactionHistoryData?,
      freeeTransactions: freezed == freeeTransactions
          ? _value.freeeTransactions
          : freeeTransactions // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistoryData?,
    ));
  }
}

/// @nodoc

class _$TransactionsImpl implements _Transactions {
  const _$TransactionsImpl({this.web21Transactions, this.freeeTransactions});

  @override
  final AccountTransactionHistoryData? web21Transactions;
  @override
  final FreeeTransactionHistoryData? freeeTransactions;

  @override
  String toString() {
    return 'Transactions(web21Transactions: $web21Transactions, freeeTransactions: $freeeTransactions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionsImpl &&
            const DeepCollectionEquality()
                .equals(other.web21Transactions, web21Transactions) &&
            const DeepCollectionEquality()
                .equals(other.freeeTransactions, freeeTransactions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(web21Transactions),
      const DeepCollectionEquality().hash(freeeTransactions));

  /// Create a copy of Transactions
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionsImplCopyWith<_$TransactionsImpl> get copyWith =>
      __$$TransactionsImplCopyWithImpl<_$TransactionsImpl>(this, _$identity);
}

abstract class _Transactions implements Transactions {
  const factory _Transactions(
          {final AccountTransactionHistoryData? web21Transactions,
          final FreeeTransactionHistoryData? freeeTransactions}) =
      _$TransactionsImpl;

  @override
  AccountTransactionHistoryData? get web21Transactions;
  @override
  FreeeTransactionHistoryData? get freeeTransactions;

  /// Create a copy of Transactions
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionsImplCopyWith<_$TransactionsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
