import 'package:freezed_annotation/freezed_annotation.dart';

part 'transaction_type.freezed.dart';

@freezed
class TransactionType with _$TransactionType {
  const factory TransactionType({
    required TransactionTypeCode code,
    required String name,
  }) = _TransactionType;

  static TransactionType? from(String? codeValue, String? name) {
    final code = TransactionTypeCode.of(codeValue);
    if (code is TransactionTypeCode && name is String) {
      return TransactionType(
        code: code,
        name: name,
      );
    } else {
      return null;
    }
  }
}

/// 取引区分
/// 10：現金
/// 11：振込
/// 12：他店券入金
/// 13：交換（取立入金および交換払）
/// 14：振替
/// 15：継続
/// 18：その他
/// 19：訂正
/// 31：でんさい
enum TransactionTypeCode {
  cash('10'),
  transfer('11'),
  otherBankCheck('12'),
  exchange('13'),
  transfer2('14'),
  continuation('15'),
  other('18'),
  correction('19'),
  densai('31');

  final String value;
  const TransactionTypeCode(this.value);

  static TransactionTypeCode? of(String? value) {
    if (value == '10') {
      return TransactionTypeCode.cash;
    }
    if (value == '11') {
      return TransactionTypeCode.transfer;
    }
    if (value == '12') {
      return TransactionTypeCode.otherBankCheck;
    }
    if (value == '13') {
      return TransactionTypeCode.exchange;
    }
    if (value == '14') {
      return TransactionTypeCode.transfer2;
    }
    if (value == '15') {
      return TransactionTypeCode.continuation;
    }
    if (value == '18') {
      return TransactionTypeCode.other;
    }
    if (value == '19') {
      return TransactionTypeCode.correction;
    }
    if (value == '31') {
      return TransactionTypeCode.densai;
    }
    return null;
  }
}
