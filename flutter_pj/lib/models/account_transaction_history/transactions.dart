import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'transactions.freezed.dart';

@freezed
class Transactions with _$Transactions {
  const factory Transactions({
    AccountTransactionHistoryData? web21Transactions,
    FreeeTransactionHistoryData? freeeTransactions,
  }) = _Transactions;
}
