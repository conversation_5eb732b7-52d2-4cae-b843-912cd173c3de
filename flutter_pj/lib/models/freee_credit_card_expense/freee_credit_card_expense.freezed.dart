// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_credit_card_expense.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditCardExpense {
  int get id => throw _privateConstructorUsedError;
  String get baseDateTime => throw _privateConstructorUsedError;
  String get creditCardName => throw _privateConstructorUsedError;
  String? get lastSyncedAt =>
      throw _privateConstructorUsedError; // 以下コードはPh1未対応の為、暫定的にコメントアウトにて対応
//required String creditCardIssuer,
  bool get isLoading =>
      throw _privateConstructorUsedError; //　以下クレジットカード使用額項目に関しては、上記項目格納後にAPIを叩くため非必須項目
  String? get startDate => throw _privateConstructorUsedError;
  String? get endDate => throw _privateConstructorUsedError;
  int? get totalExpense => throw _privateConstructorUsedError;
  AppError? get freeeCreditCardError => throw _privateConstructorUsedError;
  bool get isFreeeReConnectFinancialInstitution =>
      throw _privateConstructorUsedError;

  /// Create a copy of CreditCardExpense
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditCardExpenseCopyWith<CreditCardExpense> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditCardExpenseCopyWith<$Res> {
  factory $CreditCardExpenseCopyWith(
          CreditCardExpense value, $Res Function(CreditCardExpense) then) =
      _$CreditCardExpenseCopyWithImpl<$Res, CreditCardExpense>;
  @useResult
  $Res call(
      {int id,
      String baseDateTime,
      String creditCardName,
      String? lastSyncedAt,
      bool isLoading,
      String? startDate,
      String? endDate,
      int? totalExpense,
      AppError? freeeCreditCardError,
      bool isFreeeReConnectFinancialInstitution});

  $AppErrorCopyWith<$Res>? get freeeCreditCardError;
}

/// @nodoc
class _$CreditCardExpenseCopyWithImpl<$Res, $Val extends CreditCardExpense>
    implements $CreditCardExpenseCopyWith<$Res> {
  _$CreditCardExpenseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditCardExpense
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? baseDateTime = null,
    Object? creditCardName = null,
    Object? lastSyncedAt = freezed,
    Object? isLoading = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? totalExpense = freezed,
    Object? freeeCreditCardError = freezed,
    Object? isFreeeReConnectFinancialInstitution = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      baseDateTime: null == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as String,
      creditCardName: null == creditCardName
          ? _value.creditCardName
          : creditCardName // ignore: cast_nullable_to_non_nullable
              as String,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String?,
      totalExpense: freezed == totalExpense
          ? _value.totalExpense
          : totalExpense // ignore: cast_nullable_to_non_nullable
              as int?,
      freeeCreditCardError: freezed == freeeCreditCardError
          ? _value.freeeCreditCardError
          : freeeCreditCardError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      isFreeeReConnectFinancialInstitution: null ==
              isFreeeReConnectFinancialInstitution
          ? _value.isFreeeReConnectFinancialInstitution
          : isFreeeReConnectFinancialInstitution // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }

  /// Create a copy of CreditCardExpense
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get freeeCreditCardError {
    if (_value.freeeCreditCardError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.freeeCreditCardError!, (value) {
      return _then(_value.copyWith(freeeCreditCardError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreditCardExpenseImplCopyWith<$Res>
    implements $CreditCardExpenseCopyWith<$Res> {
  factory _$$CreditCardExpenseImplCopyWith(_$CreditCardExpenseImpl value,
          $Res Function(_$CreditCardExpenseImpl) then) =
      __$$CreditCardExpenseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String baseDateTime,
      String creditCardName,
      String? lastSyncedAt,
      bool isLoading,
      String? startDate,
      String? endDate,
      int? totalExpense,
      AppError? freeeCreditCardError,
      bool isFreeeReConnectFinancialInstitution});

  @override
  $AppErrorCopyWith<$Res>? get freeeCreditCardError;
}

/// @nodoc
class __$$CreditCardExpenseImplCopyWithImpl<$Res>
    extends _$CreditCardExpenseCopyWithImpl<$Res, _$CreditCardExpenseImpl>
    implements _$$CreditCardExpenseImplCopyWith<$Res> {
  __$$CreditCardExpenseImplCopyWithImpl(_$CreditCardExpenseImpl _value,
      $Res Function(_$CreditCardExpenseImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditCardExpense
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? baseDateTime = null,
    Object? creditCardName = null,
    Object? lastSyncedAt = freezed,
    Object? isLoading = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? totalExpense = freezed,
    Object? freeeCreditCardError = freezed,
    Object? isFreeeReConnectFinancialInstitution = null,
  }) {
    return _then(_$CreditCardExpenseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      baseDateTime: null == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as String,
      creditCardName: null == creditCardName
          ? _value.creditCardName
          : creditCardName // ignore: cast_nullable_to_non_nullable
              as String,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as String?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as String?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as String?,
      totalExpense: freezed == totalExpense
          ? _value.totalExpense
          : totalExpense // ignore: cast_nullable_to_non_nullable
              as int?,
      freeeCreditCardError: freezed == freeeCreditCardError
          ? _value.freeeCreditCardError
          : freeeCreditCardError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      isFreeeReConnectFinancialInstitution: null ==
              isFreeeReConnectFinancialInstitution
          ? _value.isFreeeReConnectFinancialInstitution
          : isFreeeReConnectFinancialInstitution // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CreditCardExpenseImpl implements _CreditCardExpense {
  _$CreditCardExpenseImpl(
      {required this.id,
      required this.baseDateTime,
      required this.creditCardName,
      this.lastSyncedAt,
      required this.isLoading,
      this.startDate = '',
      this.endDate = '',
      this.totalExpense,
      this.freeeCreditCardError,
      this.isFreeeReConnectFinancialInstitution = false});

  @override
  final int id;
  @override
  final String baseDateTime;
  @override
  final String creditCardName;
  @override
  final String? lastSyncedAt;
// 以下コードはPh1未対応の為、暫定的にコメントアウトにて対応
//required String creditCardIssuer,
  @override
  final bool isLoading;
//　以下クレジットカード使用額項目に関しては、上記項目格納後にAPIを叩くため非必須項目
  @override
  @JsonKey()
  final String? startDate;
  @override
  @JsonKey()
  final String? endDate;
  @override
  final int? totalExpense;
  @override
  final AppError? freeeCreditCardError;
  @override
  @JsonKey()
  final bool isFreeeReConnectFinancialInstitution;

  @override
  String toString() {
    return 'CreditCardExpense(id: $id, baseDateTime: $baseDateTime, creditCardName: $creditCardName, lastSyncedAt: $lastSyncedAt, isLoading: $isLoading, startDate: $startDate, endDate: $endDate, totalExpense: $totalExpense, freeeCreditCardError: $freeeCreditCardError, isFreeeReConnectFinancialInstitution: $isFreeeReConnectFinancialInstitution)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditCardExpenseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.baseDateTime, baseDateTime) ||
                other.baseDateTime == baseDateTime) &&
            (identical(other.creditCardName, creditCardName) ||
                other.creditCardName == creditCardName) &&
            (identical(other.lastSyncedAt, lastSyncedAt) ||
                other.lastSyncedAt == lastSyncedAt) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.totalExpense, totalExpense) ||
                other.totalExpense == totalExpense) &&
            (identical(other.freeeCreditCardError, freeeCreditCardError) ||
                other.freeeCreditCardError == freeeCreditCardError) &&
            (identical(other.isFreeeReConnectFinancialInstitution,
                    isFreeeReConnectFinancialInstitution) ||
                other.isFreeeReConnectFinancialInstitution ==
                    isFreeeReConnectFinancialInstitution));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      baseDateTime,
      creditCardName,
      lastSyncedAt,
      isLoading,
      startDate,
      endDate,
      totalExpense,
      freeeCreditCardError,
      isFreeeReConnectFinancialInstitution);

  /// Create a copy of CreditCardExpense
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditCardExpenseImplCopyWith<_$CreditCardExpenseImpl> get copyWith =>
      __$$CreditCardExpenseImplCopyWithImpl<_$CreditCardExpenseImpl>(
          this, _$identity);
}

abstract class _CreditCardExpense implements CreditCardExpense {
  factory _CreditCardExpense(
          {required final int id,
          required final String baseDateTime,
          required final String creditCardName,
          final String? lastSyncedAt,
          required final bool isLoading,
          final String? startDate,
          final String? endDate,
          final int? totalExpense,
          final AppError? freeeCreditCardError,
          final bool isFreeeReConnectFinancialInstitution}) =
      _$CreditCardExpenseImpl;

  @override
  int get id;
  @override
  String get baseDateTime;
  @override
  String get creditCardName;
  @override
  String? get lastSyncedAt; // 以下コードはPh1未対応の為、暫定的にコメントアウトにて対応
//required String creditCardIssuer,
  @override
  bool get isLoading; //　以下クレジットカード使用額項目に関しては、上記項目格納後にAPIを叩くため非必須項目
  @override
  String? get startDate;
  @override
  String? get endDate;
  @override
  int? get totalExpense;
  @override
  AppError? get freeeCreditCardError;
  @override
  bool get isFreeeReConnectFinancialInstitution;

  /// Create a copy of CreditCardExpense
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditCardExpenseImplCopyWith<_$CreditCardExpenseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
