import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_credit_card_expense.freezed.dart';

/// Viewのクレジットカード使用額合計カードに情報を渡す際のモデル
@freezed
class CreditCardExpense with _$CreditCardExpense {
  factory CreditCardExpense({
    required int id,
    required String baseDateTime,
    required String creditCardName,
    String? lastSyncedAt,
    // 以下コードはPh1未対応の為、暫定的にコメントアウトにて対応
    //required String creditCardIssuer,
    required bool isLoading,
    //　以下クレジットカード使用額項目に関しては、上記項目格納後にAPIを叩くため非必須項目
    @Default('') String? startDate,
    @Default('') String? endDate,
    int? totalExpense,
    AppError? freeeCreditCardError,
    @Default(false) bool isFreeeReConnectFinancialInstitution,
  }) = _CreditCardExpense;
}

extension CreditCardExpenseList on List<CreditCardExpense> {
  /// クレジットカード利用額合計
  int? getTotalCreditCardExpense() {
    int sum = 0;
    bool hasNonNullValue = false;
    for (final detail in this) {
      if (detail.totalExpense != null) {
        sum += detail.totalExpense!;
        hasNonNullValue = true;
      }
    }
    return hasNonNullValue ? sum : null;
  }

  // freee・金融機関再連携が必要なものを全て抽出する
  bool get isFreeeReConnect {
    final isFreeeReConnectAccounts =
        where((e) => e.isFreeeReConnectFinancialInstitution).toList();
    return isFreeeReConnectAccounts.isNotEmpty;
  }
}
