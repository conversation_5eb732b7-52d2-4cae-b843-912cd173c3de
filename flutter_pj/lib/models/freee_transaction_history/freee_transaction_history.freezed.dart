// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_transaction_history.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FreeeTransactionHistory {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)?
        data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)?
        data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FreeeTransactionHistoryEmpty value) empty,
    required TResult Function(FreeeTransactionHistoryData value) data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FreeeTransactionHistoryEmpty value)? empty,
    TResult? Function(FreeeTransactionHistoryData value)? data,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FreeeTransactionHistoryEmpty value)? empty,
    TResult Function(FreeeTransactionHistoryData value)? data,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeTransactionHistoryCopyWith<$Res> {
  factory $FreeeTransactionHistoryCopyWith(FreeeTransactionHistory value,
          $Res Function(FreeeTransactionHistory) then) =
      _$FreeeTransactionHistoryCopyWithImpl<$Res, FreeeTransactionHistory>;
}

/// @nodoc
class _$FreeeTransactionHistoryCopyWithImpl<$Res,
        $Val extends FreeeTransactionHistory>
    implements $FreeeTransactionHistoryCopyWith<$Res> {
  _$FreeeTransactionHistoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$FreeeTransactionHistoryEmptyImplCopyWith<$Res> {
  factory _$$FreeeTransactionHistoryEmptyImplCopyWith(
          _$FreeeTransactionHistoryEmptyImpl value,
          $Res Function(_$FreeeTransactionHistoryEmptyImpl) then) =
      __$$FreeeTransactionHistoryEmptyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FreeeTransactionHistoryEmptyImplCopyWithImpl<$Res>
    extends _$FreeeTransactionHistoryCopyWithImpl<$Res,
        _$FreeeTransactionHistoryEmptyImpl>
    implements _$$FreeeTransactionHistoryEmptyImplCopyWith<$Res> {
  __$$FreeeTransactionHistoryEmptyImplCopyWithImpl(
      _$FreeeTransactionHistoryEmptyImpl _value,
      $Res Function(_$FreeeTransactionHistoryEmptyImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FreeeTransactionHistoryEmptyImpl
    implements FreeeTransactionHistoryEmpty {
  const _$FreeeTransactionHistoryEmptyImpl();

  @override
  String toString() {
    return 'FreeeTransactionHistory.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionHistoryEmptyImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)
        data,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)?
        data,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)?
        data,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FreeeTransactionHistoryEmpty value) empty,
    required TResult Function(FreeeTransactionHistoryData value) data,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FreeeTransactionHistoryEmpty value)? empty,
    TResult? Function(FreeeTransactionHistoryData value)? data,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FreeeTransactionHistoryEmpty value)? empty,
    TResult Function(FreeeTransactionHistoryData value)? data,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }
}

abstract class FreeeTransactionHistoryEmpty implements FreeeTransactionHistory {
  const factory FreeeTransactionHistoryEmpty() =
      _$FreeeTransactionHistoryEmptyImpl;
}

/// @nodoc
abstract class _$$FreeeTransactionHistoryDataImplCopyWith<$Res> {
  factory _$$FreeeTransactionHistoryDataImplCopyWith(
          _$FreeeTransactionHistoryDataImpl value,
          $Res Function(_$FreeeTransactionHistoryDataImpl) then) =
      __$$FreeeTransactionHistoryDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<FreeeTransactionHistoryDetail> items, String? baseDateTime});
}

/// @nodoc
class __$$FreeeTransactionHistoryDataImplCopyWithImpl<$Res>
    extends _$FreeeTransactionHistoryCopyWithImpl<$Res,
        _$FreeeTransactionHistoryDataImpl>
    implements _$$FreeeTransactionHistoryDataImplCopyWith<$Res> {
  __$$FreeeTransactionHistoryDataImplCopyWithImpl(
      _$FreeeTransactionHistoryDataImpl _value,
      $Res Function(_$FreeeTransactionHistoryDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? baseDateTime = freezed,
  }) {
    return _then(_$FreeeTransactionHistoryDataImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<FreeeTransactionHistoryDetail>,
      baseDateTime: freezed == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$FreeeTransactionHistoryDataImpl implements FreeeTransactionHistoryData {
  const _$FreeeTransactionHistoryDataImpl(
      {required final List<FreeeTransactionHistoryDetail> items,
      this.baseDateTime})
      : _items = items;

  final List<FreeeTransactionHistoryDetail> _items;
  @override
  List<FreeeTransactionHistoryDetail> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final String? baseDateTime;

  @override
  String toString() {
    return 'FreeeTransactionHistory.data(items: $items, baseDateTime: $baseDateTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionHistoryDataImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.baseDateTime, baseDateTime) ||
                other.baseDateTime == baseDateTime));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_items), baseDateTime);

  /// Create a copy of FreeeTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeTransactionHistoryDataImplCopyWith<_$FreeeTransactionHistoryDataImpl>
      get copyWith => __$$FreeeTransactionHistoryDataImplCopyWithImpl<
          _$FreeeTransactionHistoryDataImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() empty,
    required TResult Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)
        data,
  }) {
    return data(items, baseDateTime);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? empty,
    TResult? Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)?
        data,
  }) {
    return data?.call(items, baseDateTime);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? empty,
    TResult Function(
            List<FreeeTransactionHistoryDetail> items, String? baseDateTime)?
        data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(items, baseDateTime);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(FreeeTransactionHistoryEmpty value) empty,
    required TResult Function(FreeeTransactionHistoryData value) data,
  }) {
    return data(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(FreeeTransactionHistoryEmpty value)? empty,
    TResult? Function(FreeeTransactionHistoryData value)? data,
  }) {
    return data?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(FreeeTransactionHistoryEmpty value)? empty,
    TResult Function(FreeeTransactionHistoryData value)? data,
    required TResult orElse(),
  }) {
    if (data != null) {
      return data(this);
    }
    return orElse();
  }
}

abstract class FreeeTransactionHistoryData implements FreeeTransactionHistory {
  const factory FreeeTransactionHistoryData(
      {required final List<FreeeTransactionHistoryDetail> items,
      final String? baseDateTime}) = _$FreeeTransactionHistoryDataImpl;

  List<FreeeTransactionHistoryDetail> get items;
  String? get baseDateTime;

  /// Create a copy of FreeeTransactionHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeTransactionHistoryDataImplCopyWith<_$FreeeTransactionHistoryDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

FreeeTransactionHistoryDetail _$FreeeTransactionHistoryDetailFromJson(
    Map<String, dynamic> json) {
  return _FreeeTransactionHistoryDetail.fromJson(json);
}

/// @nodoc
mixin _$FreeeTransactionHistoryDetail {
  ///入金／出金 (入金: income, 出金: expense)
  FreeeTransactionHistoryType get type => throw _privateConstructorUsedError;

  ///取引内容
  String? get description => throw _privateConstructorUsedError;

  /// 取引日
  String? get tradingDate => throw _privateConstructorUsedError;

  ///口座区分 (銀行口座: freeeBankAccount, クレジットカード: credit_card, 現金: wallet)
  FreeeTransactionsType get transactionType =>
      throw _privateConstructorUsedError;

  /// 金額
  double? get amount => throw _privateConstructorUsedError;

  /// Serializes this FreeeTransactionHistoryDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeTransactionHistoryDetailCopyWith<FreeeTransactionHistoryDetail>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeTransactionHistoryDetailCopyWith<$Res> {
  factory $FreeeTransactionHistoryDetailCopyWith(
          FreeeTransactionHistoryDetail value,
          $Res Function(FreeeTransactionHistoryDetail) then) =
      _$FreeeTransactionHistoryDetailCopyWithImpl<$Res,
          FreeeTransactionHistoryDetail>;
  @useResult
  $Res call(
      {FreeeTransactionHistoryType type,
      String? description,
      String? tradingDate,
      FreeeTransactionsType transactionType,
      double? amount});
}

/// @nodoc
class _$FreeeTransactionHistoryDetailCopyWithImpl<$Res,
        $Val extends FreeeTransactionHistoryDetail>
    implements $FreeeTransactionHistoryDetailCopyWith<$Res> {
  _$FreeeTransactionHistoryDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? description = freezed,
    Object? tradingDate = freezed,
    Object? transactionType = null,
    Object? amount = freezed,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistoryType,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      tradingDate: freezed == tradingDate
          ? _value.tradingDate
          : tradingDate // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionsType,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeTransactionHistoryDetailImplCopyWith<$Res>
    implements $FreeeTransactionHistoryDetailCopyWith<$Res> {
  factory _$$FreeeTransactionHistoryDetailImplCopyWith(
          _$FreeeTransactionHistoryDetailImpl value,
          $Res Function(_$FreeeTransactionHistoryDetailImpl) then) =
      __$$FreeeTransactionHistoryDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {FreeeTransactionHistoryType type,
      String? description,
      String? tradingDate,
      FreeeTransactionsType transactionType,
      double? amount});
}

/// @nodoc
class __$$FreeeTransactionHistoryDetailImplCopyWithImpl<$Res>
    extends _$FreeeTransactionHistoryDetailCopyWithImpl<$Res,
        _$FreeeTransactionHistoryDetailImpl>
    implements _$$FreeeTransactionHistoryDetailImplCopyWith<$Res> {
  __$$FreeeTransactionHistoryDetailImplCopyWithImpl(
      _$FreeeTransactionHistoryDetailImpl _value,
      $Res Function(_$FreeeTransactionHistoryDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? description = freezed,
    Object? tradingDate = freezed,
    Object? transactionType = null,
    Object? amount = freezed,
  }) {
    return _then(_$FreeeTransactionHistoryDetailImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistoryType,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      tradingDate: freezed == tradingDate
          ? _value.tradingDate
          : tradingDate // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: null == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionsType,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeTransactionHistoryDetailImpl
    implements _FreeeTransactionHistoryDetail {
  const _$FreeeTransactionHistoryDetailImpl(
      {required this.type,
      this.description,
      this.tradingDate,
      required this.transactionType,
      this.amount});

  factory _$FreeeTransactionHistoryDetailImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$FreeeTransactionHistoryDetailImplFromJson(json);

  ///入金／出金 (入金: income, 出金: expense)
  @override
  final FreeeTransactionHistoryType type;

  ///取引内容
  @override
  final String? description;

  /// 取引日
  @override
  final String? tradingDate;

  ///口座区分 (銀行口座: freeeBankAccount, クレジットカード: credit_card, 現金: wallet)
  @override
  final FreeeTransactionsType transactionType;

  /// 金額
  @override
  final double? amount;

  @override
  String toString() {
    return 'FreeeTransactionHistoryDetail(type: $type, description: $description, tradingDate: $tradingDate, transactionType: $transactionType, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionHistoryDetailImpl &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.tradingDate, tradingDate) ||
                other.tradingDate == tradingDate) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, type, description, tradingDate, transactionType, amount);

  /// Create a copy of FreeeTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeTransactionHistoryDetailImplCopyWith<
          _$FreeeTransactionHistoryDetailImpl>
      get copyWith => __$$FreeeTransactionHistoryDetailImplCopyWithImpl<
          _$FreeeTransactionHistoryDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeTransactionHistoryDetailImplToJson(
      this,
    );
  }
}

abstract class _FreeeTransactionHistoryDetail
    implements FreeeTransactionHistoryDetail {
  const factory _FreeeTransactionHistoryDetail(
      {required final FreeeTransactionHistoryType type,
      final String? description,
      final String? tradingDate,
      required final FreeeTransactionsType transactionType,
      final double? amount}) = _$FreeeTransactionHistoryDetailImpl;

  factory _FreeeTransactionHistoryDetail.fromJson(Map<String, dynamic> json) =
      _$FreeeTransactionHistoryDetailImpl.fromJson;

  ///入金／出金 (入金: income, 出金: expense)
  @override
  FreeeTransactionHistoryType get type;

  ///取引内容
  @override
  String? get description;

  /// 取引日
  @override
  String? get tradingDate;

  ///口座区分 (銀行口座: freeeBankAccount, クレジットカード: credit_card, 現金: wallet)
  @override
  FreeeTransactionsType get transactionType;

  /// 金額
  @override
  double? get amount;

  /// Create a copy of FreeeTransactionHistoryDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeTransactionHistoryDetailImplCopyWith<
          _$FreeeTransactionHistoryDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}
