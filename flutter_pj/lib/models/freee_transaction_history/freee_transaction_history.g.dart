// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'freee_transaction_history.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$FreeeTransactionHistoryDetailImpl
    _$$FreeeTransactionHistoryDetailImplFromJson(Map<String, dynamic> json) =>
        _$FreeeTransactionHistoryDetailImpl(
          type: $enumDecode(_$FreeeTransactionHistoryTypeEnumMap, json['type']),
          description: json['description'] as String?,
          tradingDate: json['tradingDate'] as String?,
          transactionType: $enumDecode(
              _$FreeeTransactionsTypeEnumMap, json['transactionType']),
          amount: (json['amount'] as num?)?.toDouble(),
        );

Map<String, dynamic> _$$FreeeTransactionHistoryDetailImplToJson(
        _$FreeeTransactionHistoryDetailImpl instance) =>
    <String, dynamic>{
      'type': _$FreeeTransactionHistoryTypeEnumMap[instance.type]!,
      'description': instance.description,
      'tradingDate': instance.tradingDate,
      'transactionType':
          _$FreeeTransactionsTypeEnumMap[instance.transactionType]!,
      'amount': instance.amount,
    };

const _$FreeeTransactionHistoryTypeEnumMap = {
  FreeeTransactionHistoryType.income: 'income',
  FreeeTransactionHistoryType.expenditure: 'expenditure',
};

const _$FreeeTransactionsTypeEnumMap = {
  FreeeTransactionsType.freeeBankAccount: 'freeeBankAccount',
  FreeeTransactionsType.creditCard: 'creditCard',
  FreeeTransactionsType.wallet: 'wallet',
};
