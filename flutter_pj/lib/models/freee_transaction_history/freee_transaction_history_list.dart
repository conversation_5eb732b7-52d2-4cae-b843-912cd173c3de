import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:intl/intl.dart';

/// フィルターやソートのロジックをまとめた拡張関数
extension FreeeTransactionHistoryDetailList
    on List<FreeeTransactionHistoryDetail> {
  DateTime _parseDate(String dateText) {
    return DateFormat('yyyy/M/d').parse(dateText);
  }

  List<FreeeTransactionHistoryDetail> filterByType(
    TransactionHistoryFilterType filterType,
  ) {
    if (filterType != TransactionHistoryFilterType.all) {
      // 入金 or 出金でフィルタリング
      final type = filterType == TransactionHistoryFilterType.income
          ? FreeeTransactionHistoryType.income
          : FreeeTransactionHistoryType.expenditure;
      return where((item) => item.type == type).toList();
    } else {
      // 何もしない
      return this;
    }
  }

  List<FreeeTransactionHistoryDetail> filterByTradingDatePeriod(
    DateTime? from,
    DateTime? to,
  ) {
    if (from is DateTime && to is DateTime) {
      // 開始日と終了日の範囲でフィルタリング
      return where((item) {
        final tradingDate = item.tradingDate;
        if (tradingDate is String) {
          final date = _parseDate(tradingDate);
          return date.isSameDateOrAfter(from) && date.isSameDateOrBefore(to);
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<FreeeTransactionHistoryDetail> filterByMinAmount(
    double? minAmount,
  ) {
    if (minAmount is double) {
      // 金額の最小値でフィルタリング
      return where((item) {
        final amount = item.amount;
        if (amount is double) {
          return amount >= minAmount;
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<FreeeTransactionHistoryDetail> filterByMaxAmount(
    double? maxAmount,
  ) {
    if (maxAmount is double) {
      // 金額の最大値でフィルタリング
      return where((item) {
        final amount = item.amount;
        if (amount is double) {
          return amount <= maxAmount;
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<FreeeTransactionHistoryDetail> filterByMinAmountWithSign(
    double? minAmount,
  ) {
    if (minAmount is double) {
      // 金額の最小値でフィルタリング
      return where((item) {
        final amount = item.amount;
        if (amount is double) {
          final amountWithSign =
              (item.type == FreeeTransactionHistoryType.income)
                  ? amount * -1
                  : amount;

          return amountWithSign >= minAmount;
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<FreeeTransactionHistoryDetail> filterByMaxAmountWithSign(
    double? maxAmount,
  ) {
    if (maxAmount is double) {
      // 金額の最大値でフィルタリング
      return where((item) {
        final amount = item.amount;
        if (amount is double) {
          final amountWithSign =
              (item.type == FreeeTransactionHistoryType.income)
                  ? amount * -1
                  : amount;

          return amountWithSign <= maxAmount;
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  List<FreeeTransactionHistoryDetail> filterDescription(
    String? descriptionSearch,
  ) {
    if (descriptionSearch is String) {
      // 利用明細の文字列でフィルタリング
      return where((item) {
        final description = item.description;
        if (description is String) {
          return inputContains(description, descriptionSearch);
        } else {
          return false;
        }
      }).toList();
    }
    // 何もしない
    return this;
  }

  // 引数の文字列をすべて半角小文字に揃えて返却する
  String convertHalfWidthLower(String input) {
    String halfWidthLowerInput = input;

    // 半角カナを全角カナに変換
    halfWidthLowerInput = getFullWidthKana(input: input);

    // 英字を全て小文字に変換
    halfWidthLowerInput = halfWidthLowerInput.toLowerCase();

    return halfWidthLowerInput;
  }

  // 半角カナを全角カナ変換をマッピングを元に実行
  String getFullWidthKana({required String input}) {
    final fullWidthKana =
        input.replaceAllMapped(RegExp('[ｱ-ﾝﾞﾟｰァ-ｮ]ﾞ?ﾟ?'), (Match match) {
      final result = _kanaMap[match.group(0)!];
      return result ?? match.group(0)!;
    });
    return fullWidthKana;
  }

  // 引数に渡された文字列で、大/小文字、全角/半角を区別せず検索
  bool inputContains(String text, String searchText) {
    final halfWidthLowerText = convertHalfWidthLower(text);
    final halfWidthLowerSearchText = convertHalfWidthLower(searchText);
    return halfWidthLowerText.contains(halfWidthLowerSearchText);
  }

  List<FreeeTransactionHistoryDetail> sortItems(
    TransactionHistorySortType sortType,
    TransactionHistorySortOrder order,
  ) {
    final List<FreeeTransactionHistoryDetail> sortedItems;
    if (sortType == TransactionHistorySortType.date) {
      // 日時
      if (order == TransactionHistorySortOrder.ascending) {
        // 昇順
        sortedItems = sorted(
          (a, b) {
            if (a.tradingDate != null && b.tradingDate != null) {
              // 日時ソートの実施
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              return tradingDateA.compareTo(tradingDateB);
            } else {
              // 日時がnullの場合、何もしない
              return 0;
            }
          },
        );
      } else {
        // 降順
        sortedItems = sorted(
          (a, b) {
            // 日時ソートの実施
            if (a.tradingDate != null && b.tradingDate != null) {
              // 日時ソートの実施
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              return tradingDateB.compareTo(tradingDateA);
            } else {
              // 日時がnullの場合、何もしない
              return 0;
            }
          },
        );
      }
    } else {
      // 金額
      if (order == TransactionHistorySortOrder.ascending) {
        // 昇順
        sortedItems = sorted((a, b) {
          return a.amount!.compareTo(b.amount!);
        });
      } else {
        // 降順
        sortedItems = sorted((a, b) {
          return b.amount!.compareTo(a.amount!);
        });
      }
    }
    return sortedItems;
  }

  List<FreeeTransactionHistoryDetail> sortCreditCardTransactionHistoryItems(
    TransactionHistorySortType sortType,
    TransactionHistorySortOrder order,
  ) {
    final List<FreeeTransactionHistoryDetail> sortedItems;
    if (sortType == TransactionHistorySortType.date) {
      // 日時
      if (order == TransactionHistorySortOrder.ascending) {
        // 昇順
        sortedItems = sorted(
          (a, b) {
            if (a.tradingDate != null && b.tradingDate != null) {
              // 日時ソートの実施
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              return tradingDateA.compareTo(tradingDateB);
            } else {
              // 日時がnullの場合、何もしない
              return 0;
            }
          },
        );
      } else {
        // 降順
        sortedItems = sorted(
          (a, b) {
            // 日時ソートの実施
            if (a.tradingDate != null && b.tradingDate != null) {
              // 日時ソートの実施
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              return tradingDateB.compareTo(tradingDateA);
            } else {
              // 日時がnullの場合、何もしない
              return 0;
            }
          },
        );
      }
    } else {
      // 金額
      if (order == TransactionHistorySortOrder.ascending) {
        // 昇順
        // マイナスも含めて並び替え　例　[3000, -500, 8000, 15000, -45000] => [-45000, -500, 3000, 8000, 15000]
        sortedItems = sorted((a, b) {
          double comparisonStandard = a.amount!;
          double comparisonTarget = b.amount!;
          // 入金の場合は符号を逆にして比較
          if (a.type == FreeeTransactionHistoryType.income) {
            comparisonStandard = -1 * comparisonStandard;
          }
          if (b.type == FreeeTransactionHistoryType.income) {
            comparisonTarget = -1 * comparisonTarget;
          }
          // 金額が同じであれば日付の降順に並び替え
          if (comparisonStandard == comparisonTarget) {
            // 日時ソートの実施
            if (a.tradingDate != null && b.tradingDate != null) {
              // 日時ソートの実施
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              return tradingDateB.compareTo(tradingDateA);
            } else {
              // 日時がnullの場合、何もしない
              return 0;
            }
          }
          return comparisonStandard.compareTo(comparisonTarget);
        });
      } else {
        // 降順
        // マイナスも含めて並び替え　例　[3000, -500, 8000, 15000, -45000] => [15000, 8000, 3000, -500, -45000]
        sortedItems = sorted((a, b) {
          double comparisonStandard = a.amount!;
          double comparisonTarget = b.amount!;
          // 入金の場合は符号を逆にして比較
          if (a.type == FreeeTransactionHistoryType.income) {
            comparisonStandard = -1 * comparisonStandard;
          }
          if (b.type == FreeeTransactionHistoryType.income) {
            comparisonTarget = -1 * comparisonTarget;
          }
          // 金額が同じであれば日付の降順に並び替え
          if (comparisonStandard == comparisonTarget) {
            // 日時ソートの実施
            if (a.tradingDate != null && b.tradingDate != null) {
              // 日時ソートの実施
              final tradingDateA = _convertToDateTime(a.tradingDate!);
              final tradingDateB = _convertToDateTime(b.tradingDate!);
              return tradingDateB.compareTo(tradingDateA);
            } else {
              // 日時がnullの場合、何もしない
              return 0;
            }
          }
          return comparisonTarget.compareTo(comparisonStandard);
        });
      }
    }
    return sortedItems;
  }
}

extension _DateTimeExt on DateTime {
  bool isSameDate(DateTime? other) {
    if (other is DateTime) {
      return year == other.year && month == other.month && day == other.day;
    }
    return false;
  }

  bool isSameDateOrAfter(DateTime? other) {
    if (other is DateTime) {
      return isAfter(other) || isSameDate(other);
    }
    return false;
  }

  bool isSameDateOrBefore(DateTime? other) {
    if (other is DateTime) {
      return isBefore(other) || isSameDate(other);
    }
    return false;
  }
}

DateTime _convertToDateTime(String date) {
  return DateFormat('yyyy/MM/dd').parse(date);
}

final _kanaMap = {
  'ｱ': 'ア',
  'ｲ': 'イ',
  'ｳ': 'ウ',
  'ｴ': 'エ',
  'ｵ': 'オ',
  'ｶ': 'カ',
  'ｷ': 'キ',
  'ｸ': 'ク',
  'ｹ': 'ケ',
  'ｺ': 'コ',
  'ｻ': 'サ',
  'ｼ': 'シ',
  'ｽ': 'ス',
  'ｾ': 'セ',
  'ｿ': 'ソ',
  'ﾀ': 'タ',
  'ﾁ': 'チ',
  'ﾂ': 'ツ',
  'ﾃ': 'テ',
  'ﾄ': 'ト',
  'ﾅ': 'ナ',
  'ﾆ': 'ニ',
  'ﾇ': 'ヌ',
  'ﾈ': 'ネ',
  'ﾉ': 'ノ',
  'ﾊ': 'ハ',
  'ﾋ': 'ヒ',
  'ﾌ': 'フ',
  'ﾍ': 'ヘ',
  'ﾎ': 'ホ',
  'ﾏ': 'マ',
  'ﾐ': 'ミ',
  'ﾑ': 'ム',
  'ﾒ': 'メ',
  'ﾓ': 'モ',
  'ﾔ': 'ヤ',
  'ﾕ': 'ユ',
  'ﾖ': 'ヨ',
  'ﾗ': 'ラ',
  'ﾘ': 'リ',
  'ﾙ': 'ル',
  'ﾚ': 'レ',
  'ﾛ': 'ロ',
  'ﾜ': 'ワ',
  'ｦ': 'ヲ',
  'ﾝ': 'ン',
  'ｳﾞ': 'ヴ',
  'ｶﾞ': 'ガ',
  'ｷﾞ': 'ギ',
  'ｸﾞ': 'グ',
  'ｹﾞ': 'ゲ',
  'ｺﾞ': 'ゴ',
  'ｻﾞ': 'ザ',
  'ｼﾞ': 'ジ',
  'ｽﾞ': 'ズ',
  'ｾﾞ': 'ゼ',
  'ｿﾞ': 'ゾ',
  'ﾀﾞ': 'ダ',
  'ﾁﾞ': 'ヂ',
  'ﾂﾞ': 'ヅ',
  'ﾃﾞ': 'デ',
  'ﾄﾞ': 'ド',
  'ﾊﾞ': 'バ',
  'ﾋﾞ': 'ビ',
  'ﾌﾞ': 'ブ',
  'ﾍﾞ': 'ベ',
  'ﾎﾞ': 'ボ',
  'ﾊﾟ': 'パ',
  'ﾋﾟ': 'ピ',
  'ﾌﾟ': 'プ',
  'ﾍﾟ': 'ペ',
  'ﾎﾟ': 'ポ',
  'ｧ': 'ァ',
  'ｨ': 'ィ',
  'ｩ': 'ゥ',
  'ｪ': 'ェ',
  'ｫ': 'ォ',
  'ｯ': 'ッ',
  'ｬ': 'ャ',
  'ｭ': 'ュ',
  'ｮ': 'ョ',
  'ｰ': 'ー',
};
