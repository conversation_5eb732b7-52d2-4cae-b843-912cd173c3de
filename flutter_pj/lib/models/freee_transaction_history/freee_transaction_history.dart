import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_transaction_history.freezed.dart';
part 'freee_transaction_history.g.dart';

@freezed
class FreeeTransactionHistory with _$FreeeTransactionHistory {
  const factory FreeeTransactionHistory.empty() = FreeeTransactionHistoryEmpty;

  const factory FreeeTransactionHistory.data({
    required List<FreeeTransactionHistoryDetail> items,
    String? baseDateTime,
  }) = FreeeTransactionHistoryData;
}

@freezed
class FreeeTransactionHistoryDetail with _$FreeeTransactionHistoryDetail {
  const factory FreeeTransactionHistoryDetail({
    ///入金／出金 (入金: income, 出金: expense)
    required FreeeTransactionHistoryType type,

    ///取引内容
    String? description,

    /// 取引日
    String? tradingDate,

    ///口座区分 (銀行口座: freeeBankAccount, クレジットカード: credit_card, 現金: wallet)
    required FreeeTransactionsType transactionType,

    /// 金額
    double? amount,
  }) = _FreeeTransactionHistoryDetail;

  factory FreeeTransactionHistoryDetail.fromJson(Map<String, dynamic> json) =>
      _$FreeeTransactionHistoryDetailFromJson(json);
}

enum FreeeTransactionHistoryType {
  /// 入金
  income,

  /// 出金
  expenditure,
}

enum FreeeTransactionsType {
  /// 銀行口座
  freeeBankAccount,

  /// クレジットカード
  creditCard,

  /// 現金
  wallet,
}

extension FreeeTransactionExt on FreeeTransactionHistoryData {
  double getTotalIncome() {
    final incomeList =
        items.where((item) => item.type == FreeeTransactionHistoryType.income);
    double result = 0;
    for (final details in incomeList) {
      result += details.amount ?? 0;
    }
    return result;
  }

  double getTotalExpenditure() {
    final incomeList = items
        .where((item) => item.type == FreeeTransactionHistoryType.expenditure);
    double result = 0;
    for (final details in incomeList) {
      result += details.amount ?? 0;
    }
    return result;
  }

  // 利用額合計
  double getTotalCreditCardExpense() {
    return getTotalExpenditure() - getTotalIncome();
  }
}
