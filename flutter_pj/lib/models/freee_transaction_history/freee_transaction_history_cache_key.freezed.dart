// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_transaction_history_cache_key.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FreeeTransactionHistoryCacheKey {
  int get walletableId => throw _privateConstructorUsedError;
  DateTime get dateFrom => throw _privateConstructorUsedError;
  DateTime get dateTo => throw _privateConstructorUsedError;

  /// Create a copy of FreeeTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeTransactionHistoryCacheKeyCopyWith<FreeeTransactionHistoryCacheKey>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeTransactionHistoryCacheKeyCopyWith<$Res> {
  factory $FreeeTransactionHistoryCacheKeyCopyWith(
          FreeeTransactionHistoryCacheKey value,
          $Res Function(FreeeTransactionHistoryCacheKey) then) =
      _$FreeeTransactionHistoryCacheKeyCopyWithImpl<$Res,
          FreeeTransactionHistoryCacheKey>;
  @useResult
  $Res call({int walletableId, DateTime dateFrom, DateTime dateTo});
}

/// @nodoc
class _$FreeeTransactionHistoryCacheKeyCopyWithImpl<$Res,
        $Val extends FreeeTransactionHistoryCacheKey>
    implements $FreeeTransactionHistoryCacheKeyCopyWith<$Res> {
  _$FreeeTransactionHistoryCacheKeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? walletableId = null,
    Object? dateFrom = null,
    Object? dateTo = null,
  }) {
    return _then(_value.copyWith(
      walletableId: null == walletableId
          ? _value.walletableId
          : walletableId // ignore: cast_nullable_to_non_nullable
              as int,
      dateFrom: null == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dateTo: null == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeTransactionHistoryCacheKeyImplCopyWith<$Res>
    implements $FreeeTransactionHistoryCacheKeyCopyWith<$Res> {
  factory _$$FreeeTransactionHistoryCacheKeyImplCopyWith(
          _$FreeeTransactionHistoryCacheKeyImpl value,
          $Res Function(_$FreeeTransactionHistoryCacheKeyImpl) then) =
      __$$FreeeTransactionHistoryCacheKeyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int walletableId, DateTime dateFrom, DateTime dateTo});
}

/// @nodoc
class __$$FreeeTransactionHistoryCacheKeyImplCopyWithImpl<$Res>
    extends _$FreeeTransactionHistoryCacheKeyCopyWithImpl<$Res,
        _$FreeeTransactionHistoryCacheKeyImpl>
    implements _$$FreeeTransactionHistoryCacheKeyImplCopyWith<$Res> {
  __$$FreeeTransactionHistoryCacheKeyImplCopyWithImpl(
      _$FreeeTransactionHistoryCacheKeyImpl _value,
      $Res Function(_$FreeeTransactionHistoryCacheKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? walletableId = null,
    Object? dateFrom = null,
    Object? dateTo = null,
  }) {
    return _then(_$FreeeTransactionHistoryCacheKeyImpl(
      walletableId: null == walletableId
          ? _value.walletableId
          : walletableId // ignore: cast_nullable_to_non_nullable
              as int,
      dateFrom: null == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dateTo: null == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$FreeeTransactionHistoryCacheKeyImpl
    implements _FreeeTransactionHistoryCacheKey {
  const _$FreeeTransactionHistoryCacheKeyImpl(
      {required this.walletableId,
      required this.dateFrom,
      required this.dateTo});

  @override
  final int walletableId;
  @override
  final DateTime dateFrom;
  @override
  final DateTime dateTo;

  @override
  String toString() {
    return 'FreeeTransactionHistoryCacheKey(walletableId: $walletableId, dateFrom: $dateFrom, dateTo: $dateTo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeTransactionHistoryCacheKeyImpl &&
            (identical(other.walletableId, walletableId) ||
                other.walletableId == walletableId) &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, walletableId, dateFrom, dateTo);

  /// Create a copy of FreeeTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeTransactionHistoryCacheKeyImplCopyWith<
          _$FreeeTransactionHistoryCacheKeyImpl>
      get copyWith => __$$FreeeTransactionHistoryCacheKeyImplCopyWithImpl<
          _$FreeeTransactionHistoryCacheKeyImpl>(this, _$identity);
}

abstract class _FreeeTransactionHistoryCacheKey
    implements FreeeTransactionHistoryCacheKey {
  const factory _FreeeTransactionHistoryCacheKey(
      {required final int walletableId,
      required final DateTime dateFrom,
      required final DateTime dateTo}) = _$FreeeTransactionHistoryCacheKeyImpl;

  @override
  int get walletableId;
  @override
  DateTime get dateFrom;
  @override
  DateTime get dateTo;

  /// Create a copy of FreeeTransactionHistoryCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeTransactionHistoryCacheKeyImplCopyWith<
          _$FreeeTransactionHistoryCacheKeyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
