import 'package:dtp_app/datas/remote_config/force_update/response/force_update_config_response.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'force_update_config.freezed.dart';
part 'force_update_config.g.dart';

enum UpdateType {
  // 強制
  force,
  // 任意
  optional,
  // なし
  none,
  // エラー
  error,
}

@freezed
class ForceUpdateConfig with _$ForceUpdateConfig {
  const factory ForceUpdateConfig({
    required UpdateType updateType,
    ForceUpdateConfigResponse? forceUpdateConfigResponse,
    UpdateError? updateError,
  }) = _ForceUpdateConfig;

  factory ForceUpdateConfig.fromJson(Map<String, dynamic> json) =>
      _$ForceUpdateConfigFromJson(json);
}

@freezed
class UpdateError with _$UpdateError {
  const factory UpdateError({
    required String errorCode,
    required String message,
  }) = _UpdateError;

  factory UpdateError.fromJson(Map<String, dynamic> json) =>
      _$UpdateErrorFromJson(json);
}
