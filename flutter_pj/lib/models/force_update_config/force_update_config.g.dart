// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'force_update_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ForceUpdateConfigImpl _$$ForceUpdateConfigImplFromJson(
        Map<String, dynamic> json) =>
    _$ForceUpdateConfigImpl(
      updateType: $enumDecode(_$UpdateTypeEnumMap, json['updateType']),
      forceUpdateConfigResponse: json['forceUpdateConfigResponse'] == null
          ? null
          : ForceUpdateConfigResponse.fromJson(
              json['forceUpdateConfigResponse'] as Map<String, dynamic>),
      updateError: json['updateError'] == null
          ? null
          : UpdateError.fromJson(json['updateError'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ForceUpdateConfigImplToJson(
        _$ForceUpdateConfigImpl instance) =>
    <String, dynamic>{
      'updateType': _$UpdateTypeEnumMap[instance.updateType]!,
      'forceUpdateConfigResponse': instance.forceUpdateConfigResponse,
      'updateError': instance.updateError,
    };

const _$UpdateTypeEnumMap = {
  UpdateType.force: 'force',
  UpdateType.optional: 'optional',
  UpdateType.none: 'none',
  UpdateType.error: 'error',
};

_$UpdateErrorImpl _$$UpdateErrorImplFromJson(Map<String, dynamic> json) =>
    _$UpdateErrorImpl(
      errorCode: json['errorCode'] as String,
      message: json['message'] as String,
    );

Map<String, dynamic> _$$UpdateErrorImplToJson(_$UpdateErrorImpl instance) =>
    <String, dynamic>{
      'errorCode': instance.errorCode,
      'message': instance.message,
    };
