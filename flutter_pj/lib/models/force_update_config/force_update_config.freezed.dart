// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'force_update_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ForceUpdateConfig _$ForceUpdateConfigFromJson(Map<String, dynamic> json) {
  return _ForceUpdateConfig.fromJson(json);
}

/// @nodoc
mixin _$ForceUpdateConfig {
  UpdateType get updateType => throw _privateConstructorUsedError;
  ForceUpdateConfigResponse? get forceUpdateConfigResponse =>
      throw _privateConstructorUsedError;
  UpdateError? get updateError => throw _privateConstructorUsedError;

  /// Serializes this ForceUpdateConfig to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ForceUpdateConfigCopyWith<ForceUpdateConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForceUpdateConfigCopyWith<$Res> {
  factory $ForceUpdateConfigCopyWith(
          ForceUpdateConfig value, $Res Function(ForceUpdateConfig) then) =
      _$ForceUpdateConfigCopyWithImpl<$Res, ForceUpdateConfig>;
  @useResult
  $Res call(
      {UpdateType updateType,
      ForceUpdateConfigResponse? forceUpdateConfigResponse,
      UpdateError? updateError});

  $ForceUpdateConfigResponseCopyWith<$Res>? get forceUpdateConfigResponse;
  $UpdateErrorCopyWith<$Res>? get updateError;
}

/// @nodoc
class _$ForceUpdateConfigCopyWithImpl<$Res, $Val extends ForceUpdateConfig>
    implements $ForceUpdateConfigCopyWith<$Res> {
  _$ForceUpdateConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updateType = null,
    Object? forceUpdateConfigResponse = freezed,
    Object? updateError = freezed,
  }) {
    return _then(_value.copyWith(
      updateType: null == updateType
          ? _value.updateType
          : updateType // ignore: cast_nullable_to_non_nullable
              as UpdateType,
      forceUpdateConfigResponse: freezed == forceUpdateConfigResponse
          ? _value.forceUpdateConfigResponse
          : forceUpdateConfigResponse // ignore: cast_nullable_to_non_nullable
              as ForceUpdateConfigResponse?,
      updateError: freezed == updateError
          ? _value.updateError
          : updateError // ignore: cast_nullable_to_non_nullable
              as UpdateError?,
    ) as $Val);
  }

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ForceUpdateConfigResponseCopyWith<$Res>? get forceUpdateConfigResponse {
    if (_value.forceUpdateConfigResponse == null) {
      return null;
    }

    return $ForceUpdateConfigResponseCopyWith<$Res>(
        _value.forceUpdateConfigResponse!, (value) {
      return _then(_value.copyWith(forceUpdateConfigResponse: value) as $Val);
    });
  }

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UpdateErrorCopyWith<$Res>? get updateError {
    if (_value.updateError == null) {
      return null;
    }

    return $UpdateErrorCopyWith<$Res>(_value.updateError!, (value) {
      return _then(_value.copyWith(updateError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ForceUpdateConfigImplCopyWith<$Res>
    implements $ForceUpdateConfigCopyWith<$Res> {
  factory _$$ForceUpdateConfigImplCopyWith(_$ForceUpdateConfigImpl value,
          $Res Function(_$ForceUpdateConfigImpl) then) =
      __$$ForceUpdateConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UpdateType updateType,
      ForceUpdateConfigResponse? forceUpdateConfigResponse,
      UpdateError? updateError});

  @override
  $ForceUpdateConfigResponseCopyWith<$Res>? get forceUpdateConfigResponse;
  @override
  $UpdateErrorCopyWith<$Res>? get updateError;
}

/// @nodoc
class __$$ForceUpdateConfigImplCopyWithImpl<$Res>
    extends _$ForceUpdateConfigCopyWithImpl<$Res, _$ForceUpdateConfigImpl>
    implements _$$ForceUpdateConfigImplCopyWith<$Res> {
  __$$ForceUpdateConfigImplCopyWithImpl(_$ForceUpdateConfigImpl _value,
      $Res Function(_$ForceUpdateConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updateType = null,
    Object? forceUpdateConfigResponse = freezed,
    Object? updateError = freezed,
  }) {
    return _then(_$ForceUpdateConfigImpl(
      updateType: null == updateType
          ? _value.updateType
          : updateType // ignore: cast_nullable_to_non_nullable
              as UpdateType,
      forceUpdateConfigResponse: freezed == forceUpdateConfigResponse
          ? _value.forceUpdateConfigResponse
          : forceUpdateConfigResponse // ignore: cast_nullable_to_non_nullable
              as ForceUpdateConfigResponse?,
      updateError: freezed == updateError
          ? _value.updateError
          : updateError // ignore: cast_nullable_to_non_nullable
              as UpdateError?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ForceUpdateConfigImpl implements _ForceUpdateConfig {
  const _$ForceUpdateConfigImpl(
      {required this.updateType,
      this.forceUpdateConfigResponse,
      this.updateError});

  factory _$ForceUpdateConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$ForceUpdateConfigImplFromJson(json);

  @override
  final UpdateType updateType;
  @override
  final ForceUpdateConfigResponse? forceUpdateConfigResponse;
  @override
  final UpdateError? updateError;

  @override
  String toString() {
    return 'ForceUpdateConfig(updateType: $updateType, forceUpdateConfigResponse: $forceUpdateConfigResponse, updateError: $updateError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForceUpdateConfigImpl &&
            (identical(other.updateType, updateType) ||
                other.updateType == updateType) &&
            (identical(other.forceUpdateConfigResponse,
                    forceUpdateConfigResponse) ||
                other.forceUpdateConfigResponse == forceUpdateConfigResponse) &&
            (identical(other.updateError, updateError) ||
                other.updateError == updateError));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, updateType, forceUpdateConfigResponse, updateError);

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForceUpdateConfigImplCopyWith<_$ForceUpdateConfigImpl> get copyWith =>
      __$$ForceUpdateConfigImplCopyWithImpl<_$ForceUpdateConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ForceUpdateConfigImplToJson(
      this,
    );
  }
}

abstract class _ForceUpdateConfig implements ForceUpdateConfig {
  const factory _ForceUpdateConfig(
      {required final UpdateType updateType,
      final ForceUpdateConfigResponse? forceUpdateConfigResponse,
      final UpdateError? updateError}) = _$ForceUpdateConfigImpl;

  factory _ForceUpdateConfig.fromJson(Map<String, dynamic> json) =
      _$ForceUpdateConfigImpl.fromJson;

  @override
  UpdateType get updateType;
  @override
  ForceUpdateConfigResponse? get forceUpdateConfigResponse;
  @override
  UpdateError? get updateError;

  /// Create a copy of ForceUpdateConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForceUpdateConfigImplCopyWith<_$ForceUpdateConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UpdateError _$UpdateErrorFromJson(Map<String, dynamic> json) {
  return _UpdateError.fromJson(json);
}

/// @nodoc
mixin _$UpdateError {
  String get errorCode => throw _privateConstructorUsedError;
  String get message => throw _privateConstructorUsedError;

  /// Serializes this UpdateError to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateErrorCopyWith<UpdateError> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateErrorCopyWith<$Res> {
  factory $UpdateErrorCopyWith(
          UpdateError value, $Res Function(UpdateError) then) =
      _$UpdateErrorCopyWithImpl<$Res, UpdateError>;
  @useResult
  $Res call({String errorCode, String message});
}

/// @nodoc
class _$UpdateErrorCopyWithImpl<$Res, $Val extends UpdateError>
    implements $UpdateErrorCopyWith<$Res> {
  _$UpdateErrorCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorCode = null,
    Object? message = null,
  }) {
    return _then(_value.copyWith(
      errorCode: null == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UpdateErrorImplCopyWith<$Res>
    implements $UpdateErrorCopyWith<$Res> {
  factory _$$UpdateErrorImplCopyWith(
          _$UpdateErrorImpl value, $Res Function(_$UpdateErrorImpl) then) =
      __$$UpdateErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String errorCode, String message});
}

/// @nodoc
class __$$UpdateErrorImplCopyWithImpl<$Res>
    extends _$UpdateErrorCopyWithImpl<$Res, _$UpdateErrorImpl>
    implements _$$UpdateErrorImplCopyWith<$Res> {
  __$$UpdateErrorImplCopyWithImpl(
      _$UpdateErrorImpl _value, $Res Function(_$UpdateErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? errorCode = null,
    Object? message = null,
  }) {
    return _then(_$UpdateErrorImpl(
      errorCode: null == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String,
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateErrorImpl implements _UpdateError {
  const _$UpdateErrorImpl({required this.errorCode, required this.message});

  factory _$UpdateErrorImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateErrorImplFromJson(json);

  @override
  final String errorCode;
  @override
  final String message;

  @override
  String toString() {
    return 'UpdateError(errorCode: $errorCode, message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateErrorImpl &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, errorCode, message);

  /// Create a copy of UpdateError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateErrorImplCopyWith<_$UpdateErrorImpl> get copyWith =>
      __$$UpdateErrorImplCopyWithImpl<_$UpdateErrorImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateErrorImplToJson(
      this,
    );
  }
}

abstract class _UpdateError implements UpdateError {
  const factory _UpdateError(
      {required final String errorCode,
      required final String message}) = _$UpdateErrorImpl;

  factory _UpdateError.fromJson(Map<String, dynamic> json) =
      _$UpdateErrorImpl.fromJson;

  @override
  String get errorCode;
  @override
  String get message;

  /// Create a copy of UpdateError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateErrorImplCopyWith<_$UpdateErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
