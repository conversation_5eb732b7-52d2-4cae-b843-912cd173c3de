// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jpki_service_check_api_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JpkiServiceCheckApiResponse _$JpkiServiceCheckApiResponseFromJson(
    Map<String, dynamic> json) {
  return _JpkiServiceCheckApiResponse.fromJson(json);
}

/// @nodoc
mixin _$JpkiServiceCheckApiResponse {
// ステータス
  String get status => throw _privateConstructorUsedError; // 詳細
  String? get detail => throw _privateConstructorUsedError;

  /// Serializes this JpkiServiceCheckApiResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JpkiServiceCheckApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JpkiServiceCheckApiResponseCopyWith<JpkiServiceCheckApiResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JpkiServiceCheckApiResponseCopyWith<$Res> {
  factory $JpkiServiceCheckApiResponseCopyWith(
          JpkiServiceCheckApiResponse value,
          $Res Function(JpkiServiceCheckApiResponse) then) =
      _$JpkiServiceCheckApiResponseCopyWithImpl<$Res,
          JpkiServiceCheckApiResponse>;
  @useResult
  $Res call({String status, String? detail});
}

/// @nodoc
class _$JpkiServiceCheckApiResponseCopyWithImpl<$Res,
        $Val extends JpkiServiceCheckApiResponse>
    implements $JpkiServiceCheckApiResponseCopyWith<$Res> {
  _$JpkiServiceCheckApiResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JpkiServiceCheckApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? detail = freezed,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      detail: freezed == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JpkiServiceCheckApiResponseImplCopyWith<$Res>
    implements $JpkiServiceCheckApiResponseCopyWith<$Res> {
  factory _$$JpkiServiceCheckApiResponseImplCopyWith(
          _$JpkiServiceCheckApiResponseImpl value,
          $Res Function(_$JpkiServiceCheckApiResponseImpl) then) =
      __$$JpkiServiceCheckApiResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String status, String? detail});
}

/// @nodoc
class __$$JpkiServiceCheckApiResponseImplCopyWithImpl<$Res>
    extends _$JpkiServiceCheckApiResponseCopyWithImpl<$Res,
        _$JpkiServiceCheckApiResponseImpl>
    implements _$$JpkiServiceCheckApiResponseImplCopyWith<$Res> {
  __$$JpkiServiceCheckApiResponseImplCopyWithImpl(
      _$JpkiServiceCheckApiResponseImpl _value,
      $Res Function(_$JpkiServiceCheckApiResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of JpkiServiceCheckApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? detail = freezed,
  }) {
    return _then(_$JpkiServiceCheckApiResponseImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      detail: freezed == detail
          ? _value.detail
          : detail // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JpkiServiceCheckApiResponseImpl
    implements _JpkiServiceCheckApiResponse {
  const _$JpkiServiceCheckApiResponseImpl({required this.status, this.detail});

  factory _$JpkiServiceCheckApiResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$JpkiServiceCheckApiResponseImplFromJson(json);

// ステータス
  @override
  final String status;
// 詳細
  @override
  final String? detail;

  @override
  String toString() {
    return 'JpkiServiceCheckApiResponse(status: $status, detail: $detail)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JpkiServiceCheckApiResponseImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.detail, detail) || other.detail == detail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, status, detail);

  /// Create a copy of JpkiServiceCheckApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JpkiServiceCheckApiResponseImplCopyWith<_$JpkiServiceCheckApiResponseImpl>
      get copyWith => __$$JpkiServiceCheckApiResponseImplCopyWithImpl<
          _$JpkiServiceCheckApiResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JpkiServiceCheckApiResponseImplToJson(
      this,
    );
  }
}

abstract class _JpkiServiceCheckApiResponse
    implements JpkiServiceCheckApiResponse {
  const factory _JpkiServiceCheckApiResponse(
      {required final String status,
      final String? detail}) = _$JpkiServiceCheckApiResponseImpl;

  factory _JpkiServiceCheckApiResponse.fromJson(Map<String, dynamic> json) =
      _$JpkiServiceCheckApiResponseImpl.fromJson;

// ステータス
  @override
  String get status; // 詳細
  @override
  String? get detail;

  /// Create a copy of JpkiServiceCheckApiResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JpkiServiceCheckApiResponseImplCopyWith<_$JpkiServiceCheckApiResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
