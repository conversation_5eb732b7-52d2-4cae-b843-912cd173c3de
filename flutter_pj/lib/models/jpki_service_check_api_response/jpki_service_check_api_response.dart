import 'package:freezed_annotation/freezed_annotation.dart';

part 'jpki_service_check_api_response.freezed.dart';
part 'jpki_service_check_api_response.g.dart';

@freezed
class JpkiServiceCheckApiResponse with _$JpkiServiceCheckApiResponse {
  const factory JpkiServiceCheckApiResponse({
    // ステータス
    required String status,
    // 詳細
    String? detail,
  }) = _JpkiServiceCheckApiResponse;

  factory JpkiServiceCheckApiResponse.fromJson(Map<String, dynamic> json) =>
      _$JpkiServiceCheckApiResponseFromJson(json);
}
