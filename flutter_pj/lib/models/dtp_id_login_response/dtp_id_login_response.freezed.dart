// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dtp_id_login_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DtpIdLoginResponse _$DtpIdLoginResponseFromJson(Map<String, dynamic> json) {
  return _DtpIdLoginResponse.fromJson(json);
}

/// @nodoc
mixin _$DtpIdLoginResponse {
  String get sessionId => throw _privateConstructorUsedError;
  String get encryptedCookie => throw _privateConstructorUsedError;

  /// Serializes this DtpIdLoginResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DtpIdLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DtpIdLoginResponseCopyWith<DtpIdLoginResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DtpIdLoginResponseCopyWith<$Res> {
  factory $DtpIdLoginResponseCopyWith(
          DtpIdLoginResponse value, $Res Function(DtpIdLoginResponse) then) =
      _$DtpIdLoginResponseCopyWithImpl<$Res, DtpIdLoginResponse>;
  @useResult
  $Res call({String sessionId, String encryptedCookie});
}

/// @nodoc
class _$DtpIdLoginResponseCopyWithImpl<$Res, $Val extends DtpIdLoginResponse>
    implements $DtpIdLoginResponseCopyWith<$Res> {
  _$DtpIdLoginResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DtpIdLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? encryptedCookie = null,
  }) {
    return _then(_value.copyWith(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      encryptedCookie: null == encryptedCookie
          ? _value.encryptedCookie
          : encryptedCookie // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DtpIdLoginResponseImplCopyWith<$Res>
    implements $DtpIdLoginResponseCopyWith<$Res> {
  factory _$$DtpIdLoginResponseImplCopyWith(_$DtpIdLoginResponseImpl value,
          $Res Function(_$DtpIdLoginResponseImpl) then) =
      __$$DtpIdLoginResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String sessionId, String encryptedCookie});
}

/// @nodoc
class __$$DtpIdLoginResponseImplCopyWithImpl<$Res>
    extends _$DtpIdLoginResponseCopyWithImpl<$Res, _$DtpIdLoginResponseImpl>
    implements _$$DtpIdLoginResponseImplCopyWith<$Res> {
  __$$DtpIdLoginResponseImplCopyWithImpl(_$DtpIdLoginResponseImpl _value,
      $Res Function(_$DtpIdLoginResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of DtpIdLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? encryptedCookie = null,
  }) {
    return _then(_$DtpIdLoginResponseImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      encryptedCookie: null == encryptedCookie
          ? _value.encryptedCookie
          : encryptedCookie // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DtpIdLoginResponseImpl implements _DtpIdLoginResponse {
  const _$DtpIdLoginResponseImpl(
      {required this.sessionId, required this.encryptedCookie});

  factory _$DtpIdLoginResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DtpIdLoginResponseImplFromJson(json);

  @override
  final String sessionId;
  @override
  final String encryptedCookie;

  @override
  String toString() {
    return 'DtpIdLoginResponse(sessionId: $sessionId, encryptedCookie: $encryptedCookie)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DtpIdLoginResponseImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.encryptedCookie, encryptedCookie) ||
                other.encryptedCookie == encryptedCookie));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sessionId, encryptedCookie);

  /// Create a copy of DtpIdLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DtpIdLoginResponseImplCopyWith<_$DtpIdLoginResponseImpl> get copyWith =>
      __$$DtpIdLoginResponseImplCopyWithImpl<_$DtpIdLoginResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DtpIdLoginResponseImplToJson(
      this,
    );
  }
}

abstract class _DtpIdLoginResponse implements DtpIdLoginResponse {
  const factory _DtpIdLoginResponse(
      {required final String sessionId,
      required final String encryptedCookie}) = _$DtpIdLoginResponseImpl;

  factory _DtpIdLoginResponse.fromJson(Map<String, dynamic> json) =
      _$DtpIdLoginResponseImpl.fromJson;

  @override
  String get sessionId;
  @override
  String get encryptedCookie;

  /// Create a copy of DtpIdLoginResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DtpIdLoginResponseImplCopyWith<_$DtpIdLoginResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
