import 'package:freezed_annotation/freezed_annotation.dart';

part 'dtp_id_login_response.freezed.dart';
part 'dtp_id_login_response.g.dart';

@freezed
// DTPログイン時のレスポンスを格納するクラス
class DtpIdLoginResponse with _$DtpIdLoginResponse {
  const factory DtpIdLoginResponse({
    required String sessionId,
    required String encryptedCookie,
  }) = _DtpIdLoginResponse;

  factory DtpIdLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$DtpIdLoginResponseFromJson(json);
}
