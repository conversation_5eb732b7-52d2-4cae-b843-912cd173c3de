import 'package:freezed_annotation/freezed_annotation.dart';

part 'classified_client.freezed.dart';
part 'classified_client.g.dart';

@freezed
class ClassifiedClient with _$ClassifiedClient {
  const factory ClassifiedClient({
    @Default('') String clientId,
    @Default('') String clientSecret,
  }) = _ClassifiedClient;

  factory ClassifiedClient.fromJson(Map<String, dynamic> json) =>
      _$ClassifiedClientFromJson(json);
}
