// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'classified_client.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ClassifiedClient _$ClassifiedClientFromJson(Map<String, dynamic> json) {
  return _ClassifiedClient.fromJson(json);
}

/// @nodoc
mixin _$ClassifiedClient {
  String get clientId => throw _privateConstructorUsedError;
  String get clientSecret => throw _privateConstructorUsedError;

  /// Serializes this ClassifiedClient to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ClassifiedClient
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ClassifiedClientCopyWith<ClassifiedClient> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ClassifiedClientCopyWith<$Res> {
  factory $ClassifiedClientCopyWith(
          ClassifiedClient value, $Res Function(ClassifiedClient) then) =
      _$ClassifiedClientCopyWithImpl<$Res, ClassifiedClient>;
  @useResult
  $Res call({String clientId, String clientSecret});
}

/// @nodoc
class _$ClassifiedClientCopyWithImpl<$Res, $Val extends ClassifiedClient>
    implements $ClassifiedClientCopyWith<$Res> {
  _$ClassifiedClientCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ClassifiedClient
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientId = null,
    Object? clientSecret = null,
  }) {
    return _then(_value.copyWith(
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      clientSecret: null == clientSecret
          ? _value.clientSecret
          : clientSecret // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ClassifiedClientImplCopyWith<$Res>
    implements $ClassifiedClientCopyWith<$Res> {
  factory _$$ClassifiedClientImplCopyWith(_$ClassifiedClientImpl value,
          $Res Function(_$ClassifiedClientImpl) then) =
      __$$ClassifiedClientImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String clientId, String clientSecret});
}

/// @nodoc
class __$$ClassifiedClientImplCopyWithImpl<$Res>
    extends _$ClassifiedClientCopyWithImpl<$Res, _$ClassifiedClientImpl>
    implements _$$ClassifiedClientImplCopyWith<$Res> {
  __$$ClassifiedClientImplCopyWithImpl(_$ClassifiedClientImpl _value,
      $Res Function(_$ClassifiedClientImpl) _then)
      : super(_value, _then);

  /// Create a copy of ClassifiedClient
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? clientId = null,
    Object? clientSecret = null,
  }) {
    return _then(_$ClassifiedClientImpl(
      clientId: null == clientId
          ? _value.clientId
          : clientId // ignore: cast_nullable_to_non_nullable
              as String,
      clientSecret: null == clientSecret
          ? _value.clientSecret
          : clientSecret // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ClassifiedClientImpl implements _ClassifiedClient {
  const _$ClassifiedClientImpl({this.clientId = '', this.clientSecret = ''});

  factory _$ClassifiedClientImpl.fromJson(Map<String, dynamic> json) =>
      _$$ClassifiedClientImplFromJson(json);

  @override
  @JsonKey()
  final String clientId;
  @override
  @JsonKey()
  final String clientSecret;

  @override
  String toString() {
    return 'ClassifiedClient(clientId: $clientId, clientSecret: $clientSecret)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ClassifiedClientImpl &&
            (identical(other.clientId, clientId) ||
                other.clientId == clientId) &&
            (identical(other.clientSecret, clientSecret) ||
                other.clientSecret == clientSecret));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, clientId, clientSecret);

  /// Create a copy of ClassifiedClient
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ClassifiedClientImplCopyWith<_$ClassifiedClientImpl> get copyWith =>
      __$$ClassifiedClientImplCopyWithImpl<_$ClassifiedClientImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ClassifiedClientImplToJson(
      this,
    );
  }
}

abstract class _ClassifiedClient implements ClassifiedClient {
  const factory _ClassifiedClient(
      {final String clientId,
      final String clientSecret}) = _$ClassifiedClientImpl;

  factory _ClassifiedClient.fromJson(Map<String, dynamic> json) =
      _$ClassifiedClientImpl.fromJson;

  @override
  String get clientId;
  @override
  String get clientSecret;

  /// Create a copy of ClassifiedClient
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ClassifiedClientImplCopyWith<_$ClassifiedClientImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
