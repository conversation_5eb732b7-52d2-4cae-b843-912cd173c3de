import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_info_response.freezed.dart';
part 'user_info_response.g.dart';

@freezed
class UserInfoResponse with _$UserInfoResponse {
  const factory UserInfoResponse({
    /// 利用者ID(VDID)
    required String vdId,

    /// 認証区分
    required String ninsyoKbn,

    /// 利用者名
    required String userSeiMei,

    /// 利用者区分
    @JsonKey(name: 'userTyp') required String userType,

    /// 取引先名
    required String compName,

    /// 申込代表口座(店番+科目+口座番号)
    required String compId,

    /// メールアドレス
    @Json<PERSON>ey(name: 'Email') @Default('') String email,

    /// 利用者ID(DTPID）
    @Default('') String dtpId,

    /// Web21契約種別
    @Default('') String keiyakuType,

    /// 内部ID
    @Default('') String userId,

    /// 利用者権限
    @Default('') String userAuths,
  }) = _UserInfoResponse;

  factory UserInfoResponse.fromJson(Map<String, dynamic> json) =>
      _$UserInfoResponseFromJson(json);
}
