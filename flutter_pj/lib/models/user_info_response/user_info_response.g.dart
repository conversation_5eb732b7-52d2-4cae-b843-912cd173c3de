// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_info_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserInfoResponseImpl _$$UserInfoResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$UserInfoResponseImpl(
      vdId: json['vdId'] as String,
      ninsyoKbn: json['ninsyoKbn'] as String,
      userSeiMei: json['userSeiMei'] as String,
      userType: json['userTyp'] as String,
      compName: json['compName'] as String,
      compId: json['compId'] as String,
      email: json['Email'] as String? ?? '',
      dtpId: json['dtpId'] as String? ?? '',
      keiyakuType: json['keiyakuType'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      userAuths: json['userAuths'] as String? ?? '',
    );

Map<String, dynamic> _$$UserInfoResponseImplToJson(
        _$UserInfoResponseImpl instance) =>
    <String, dynamic>{
      'vdId': instance.vdId,
      'ninsyoKbn': instance.ninsyoKbn,
      'userSeiMei': instance.userSeiMei,
      'userTyp': instance.userType,
      'compName': instance.compName,
      'compId': instance.compId,
      'Email': instance.email,
      'dtpId': instance.dtpId,
      'keiyakuType': instance.keiyakuType,
      'userId': instance.userId,
      'userAuths': instance.userAuths,
    };
