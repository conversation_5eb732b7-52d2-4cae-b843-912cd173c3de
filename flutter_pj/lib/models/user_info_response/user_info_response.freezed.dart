// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserInfoResponse _$UserInfoResponseFromJson(Map<String, dynamic> json) {
  return _UserInfoResponse.fromJson(json);
}

/// @nodoc
mixin _$UserInfoResponse {
  /// 利用者ID(VDID)
  String get vdId => throw _privateConstructorUsedError;

  /// 認証区分
  String get ninsyoKbn => throw _privateConstructorUsedError;

  /// 利用者名
  String get userSeiMei => throw _privateConstructorUsedError;

  /// 利用者区分
  @JsonKey(name: 'userTyp')
  String get userType => throw _privateConstructorUsedError;

  /// 取引先名
  String get compName => throw _privateConstructorUsedError;

  /// 申込代表口座(店番+科目+口座番号)
  String get compId => throw _privateConstructorUsedError;

  /// メールアドレス
  @JsonKey(name: 'Email')
  String get email => throw _privateConstructorUsedError;

  /// 利用者ID(DTPID）
  String get dtpId => throw _privateConstructorUsedError;

  /// Web21契約種別
  String get keiyakuType => throw _privateConstructorUsedError;

  /// 内部ID
  String get userId => throw _privateConstructorUsedError;

  /// 利用者権限
  String get userAuths => throw _privateConstructorUsedError;

  /// Serializes this UserInfoResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserInfoResponseCopyWith<UserInfoResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserInfoResponseCopyWith<$Res> {
  factory $UserInfoResponseCopyWith(
          UserInfoResponse value, $Res Function(UserInfoResponse) then) =
      _$UserInfoResponseCopyWithImpl<$Res, UserInfoResponse>;
  @useResult
  $Res call(
      {String vdId,
      String ninsyoKbn,
      String userSeiMei,
      @JsonKey(name: 'userTyp') String userType,
      String compName,
      String compId,
      @JsonKey(name: 'Email') String email,
      String dtpId,
      String keiyakuType,
      String userId,
      String userAuths});
}

/// @nodoc
class _$UserInfoResponseCopyWithImpl<$Res, $Val extends UserInfoResponse>
    implements $UserInfoResponseCopyWith<$Res> {
  _$UserInfoResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vdId = null,
    Object? ninsyoKbn = null,
    Object? userSeiMei = null,
    Object? userType = null,
    Object? compName = null,
    Object? compId = null,
    Object? email = null,
    Object? dtpId = null,
    Object? keiyakuType = null,
    Object? userId = null,
    Object? userAuths = null,
  }) {
    return _then(_value.copyWith(
      vdId: null == vdId
          ? _value.vdId
          : vdId // ignore: cast_nullable_to_non_nullable
              as String,
      ninsyoKbn: null == ninsyoKbn
          ? _value.ninsyoKbn
          : ninsyoKbn // ignore: cast_nullable_to_non_nullable
              as String,
      userSeiMei: null == userSeiMei
          ? _value.userSeiMei
          : userSeiMei // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      compName: null == compName
          ? _value.compName
          : compName // ignore: cast_nullable_to_non_nullable
              as String,
      compId: null == compId
          ? _value.compId
          : compId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      dtpId: null == dtpId
          ? _value.dtpId
          : dtpId // ignore: cast_nullable_to_non_nullable
              as String,
      keiyakuType: null == keiyakuType
          ? _value.keiyakuType
          : keiyakuType // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userAuths: null == userAuths
          ? _value.userAuths
          : userAuths // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserInfoResponseImplCopyWith<$Res>
    implements $UserInfoResponseCopyWith<$Res> {
  factory _$$UserInfoResponseImplCopyWith(_$UserInfoResponseImpl value,
          $Res Function(_$UserInfoResponseImpl) then) =
      __$$UserInfoResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String vdId,
      String ninsyoKbn,
      String userSeiMei,
      @JsonKey(name: 'userTyp') String userType,
      String compName,
      String compId,
      @JsonKey(name: 'Email') String email,
      String dtpId,
      String keiyakuType,
      String userId,
      String userAuths});
}

/// @nodoc
class __$$UserInfoResponseImplCopyWithImpl<$Res>
    extends _$UserInfoResponseCopyWithImpl<$Res, _$UserInfoResponseImpl>
    implements _$$UserInfoResponseImplCopyWith<$Res> {
  __$$UserInfoResponseImplCopyWithImpl(_$UserInfoResponseImpl _value,
      $Res Function(_$UserInfoResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? vdId = null,
    Object? ninsyoKbn = null,
    Object? userSeiMei = null,
    Object? userType = null,
    Object? compName = null,
    Object? compId = null,
    Object? email = null,
    Object? dtpId = null,
    Object? keiyakuType = null,
    Object? userId = null,
    Object? userAuths = null,
  }) {
    return _then(_$UserInfoResponseImpl(
      vdId: null == vdId
          ? _value.vdId
          : vdId // ignore: cast_nullable_to_non_nullable
              as String,
      ninsyoKbn: null == ninsyoKbn
          ? _value.ninsyoKbn
          : ninsyoKbn // ignore: cast_nullable_to_non_nullable
              as String,
      userSeiMei: null == userSeiMei
          ? _value.userSeiMei
          : userSeiMei // ignore: cast_nullable_to_non_nullable
              as String,
      userType: null == userType
          ? _value.userType
          : userType // ignore: cast_nullable_to_non_nullable
              as String,
      compName: null == compName
          ? _value.compName
          : compName // ignore: cast_nullable_to_non_nullable
              as String,
      compId: null == compId
          ? _value.compId
          : compId // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      dtpId: null == dtpId
          ? _value.dtpId
          : dtpId // ignore: cast_nullable_to_non_nullable
              as String,
      keiyakuType: null == keiyakuType
          ? _value.keiyakuType
          : keiyakuType // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      userAuths: null == userAuths
          ? _value.userAuths
          : userAuths // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserInfoResponseImpl implements _UserInfoResponse {
  const _$UserInfoResponseImpl(
      {required this.vdId,
      required this.ninsyoKbn,
      required this.userSeiMei,
      @JsonKey(name: 'userTyp') required this.userType,
      required this.compName,
      required this.compId,
      @JsonKey(name: 'Email') this.email = '',
      this.dtpId = '',
      this.keiyakuType = '',
      this.userId = '',
      this.userAuths = ''});

  factory _$UserInfoResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserInfoResponseImplFromJson(json);

  /// 利用者ID(VDID)
  @override
  final String vdId;

  /// 認証区分
  @override
  final String ninsyoKbn;

  /// 利用者名
  @override
  final String userSeiMei;

  /// 利用者区分
  @override
  @JsonKey(name: 'userTyp')
  final String userType;

  /// 取引先名
  @override
  final String compName;

  /// 申込代表口座(店番+科目+口座番号)
  @override
  final String compId;

  /// メールアドレス
  @override
  @JsonKey(name: 'Email')
  final String email;

  /// 利用者ID(DTPID）
  @override
  @JsonKey()
  final String dtpId;

  /// Web21契約種別
  @override
  @JsonKey()
  final String keiyakuType;

  /// 内部ID
  @override
  @JsonKey()
  final String userId;

  /// 利用者権限
  @override
  @JsonKey()
  final String userAuths;

  @override
  String toString() {
    return 'UserInfoResponse(vdId: $vdId, ninsyoKbn: $ninsyoKbn, userSeiMei: $userSeiMei, userType: $userType, compName: $compName, compId: $compId, email: $email, dtpId: $dtpId, keiyakuType: $keiyakuType, userId: $userId, userAuths: $userAuths)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserInfoResponseImpl &&
            (identical(other.vdId, vdId) || other.vdId == vdId) &&
            (identical(other.ninsyoKbn, ninsyoKbn) ||
                other.ninsyoKbn == ninsyoKbn) &&
            (identical(other.userSeiMei, userSeiMei) ||
                other.userSeiMei == userSeiMei) &&
            (identical(other.userType, userType) ||
                other.userType == userType) &&
            (identical(other.compName, compName) ||
                other.compName == compName) &&
            (identical(other.compId, compId) || other.compId == compId) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.dtpId, dtpId) || other.dtpId == dtpId) &&
            (identical(other.keiyakuType, keiyakuType) ||
                other.keiyakuType == keiyakuType) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAuths, userAuths) ||
                other.userAuths == userAuths));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, vdId, ninsyoKbn, userSeiMei,
      userType, compName, compId, email, dtpId, keiyakuType, userId, userAuths);

  /// Create a copy of UserInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserInfoResponseImplCopyWith<_$UserInfoResponseImpl> get copyWith =>
      __$$UserInfoResponseImplCopyWithImpl<_$UserInfoResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserInfoResponseImplToJson(
      this,
    );
  }
}

abstract class _UserInfoResponse implements UserInfoResponse {
  const factory _UserInfoResponse(
      {required final String vdId,
      required final String ninsyoKbn,
      required final String userSeiMei,
      @JsonKey(name: 'userTyp') required final String userType,
      required final String compName,
      required final String compId,
      @JsonKey(name: 'Email') final String email,
      final String dtpId,
      final String keiyakuType,
      final String userId,
      final String userAuths}) = _$UserInfoResponseImpl;

  factory _UserInfoResponse.fromJson(Map<String, dynamic> json) =
      _$UserInfoResponseImpl.fromJson;

  /// 利用者ID(VDID)
  @override
  String get vdId;

  /// 認証区分
  @override
  String get ninsyoKbn;

  /// 利用者名
  @override
  String get userSeiMei;

  /// 利用者区分
  @override
  @JsonKey(name: 'userTyp')
  String get userType;

  /// 取引先名
  @override
  String get compName;

  /// 申込代表口座(店番+科目+口座番号)
  @override
  String get compId;

  /// メールアドレス
  @override
  @JsonKey(name: 'Email')
  String get email;

  /// 利用者ID(DTPID）
  @override
  String get dtpId;

  /// Web21契約種別
  @override
  String get keiyakuType;

  /// 内部ID
  @override
  String get userId;

  /// 利用者権限
  @override
  String get userAuths;

  /// Create a copy of UserInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserInfoResponseImplCopyWith<_$UserInfoResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
