// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_status.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LoginStatus {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Session session) login,
    required TResult Function() logout,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Session session)? login,
    TResult? Function()? logout,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Session session)? login,
    TResult Function()? logout,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoginStatusLogin value) login,
    required TResult Function(LoginStatusLogout value) logout,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoginStatusLogin value)? login,
    TResult? Function(LoginStatusLogout value)? logout,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoginStatusLogin value)? login,
    TResult Function(LoginStatusLogout value)? logout,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginStatusCopyWith<$Res> {
  factory $LoginStatusCopyWith(
          LoginStatus value, $Res Function(LoginStatus) then) =
      _$LoginStatusCopyWithImpl<$Res, LoginStatus>;
}

/// @nodoc
class _$LoginStatusCopyWithImpl<$Res, $Val extends LoginStatus>
    implements $LoginStatusCopyWith<$Res> {
  _$LoginStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginStatus
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoginStatusLoginImplCopyWith<$Res> {
  factory _$$LoginStatusLoginImplCopyWith(_$LoginStatusLoginImpl value,
          $Res Function(_$LoginStatusLoginImpl) then) =
      __$$LoginStatusLoginImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Session session});

  $SessionCopyWith<$Res> get session;
}

/// @nodoc
class __$$LoginStatusLoginImplCopyWithImpl<$Res>
    extends _$LoginStatusCopyWithImpl<$Res, _$LoginStatusLoginImpl>
    implements _$$LoginStatusLoginImplCopyWith<$Res> {
  __$$LoginStatusLoginImplCopyWithImpl(_$LoginStatusLoginImpl _value,
      $Res Function(_$LoginStatusLoginImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? session = null,
  }) {
    return _then(_$LoginStatusLoginImpl(
      session: null == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session,
    ));
  }

  /// Create a copy of LoginStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SessionCopyWith<$Res> get session {
    return $SessionCopyWith<$Res>(_value.session, (value) {
      return _then(_value.copyWith(session: value));
    });
  }
}

/// @nodoc

class _$LoginStatusLoginImpl implements LoginStatusLogin {
  const _$LoginStatusLoginImpl({required this.session});

  @override
  final Session session;

  @override
  String toString() {
    return 'LoginStatus.login(session: $session)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginStatusLoginImpl &&
            (identical(other.session, session) || other.session == session));
  }

  @override
  int get hashCode => Object.hash(runtimeType, session);

  /// Create a copy of LoginStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginStatusLoginImplCopyWith<_$LoginStatusLoginImpl> get copyWith =>
      __$$LoginStatusLoginImplCopyWithImpl<_$LoginStatusLoginImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Session session) login,
    required TResult Function() logout,
  }) {
    return login(session);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Session session)? login,
    TResult? Function()? logout,
  }) {
    return login?.call(session);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Session session)? login,
    TResult Function()? logout,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login(session);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoginStatusLogin value) login,
    required TResult Function(LoginStatusLogout value) logout,
  }) {
    return login(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoginStatusLogin value)? login,
    TResult? Function(LoginStatusLogout value)? logout,
  }) {
    return login?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoginStatusLogin value)? login,
    TResult Function(LoginStatusLogout value)? logout,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login(this);
    }
    return orElse();
  }
}

abstract class LoginStatusLogin implements LoginStatus {
  const factory LoginStatusLogin({required final Session session}) =
      _$LoginStatusLoginImpl;

  Session get session;

  /// Create a copy of LoginStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginStatusLoginImplCopyWith<_$LoginStatusLoginImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoginStatusLogoutImplCopyWith<$Res> {
  factory _$$LoginStatusLogoutImplCopyWith(_$LoginStatusLogoutImpl value,
          $Res Function(_$LoginStatusLogoutImpl) then) =
      __$$LoginStatusLogoutImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoginStatusLogoutImplCopyWithImpl<$Res>
    extends _$LoginStatusCopyWithImpl<$Res, _$LoginStatusLogoutImpl>
    implements _$$LoginStatusLogoutImplCopyWith<$Res> {
  __$$LoginStatusLogoutImplCopyWithImpl(_$LoginStatusLogoutImpl _value,
      $Res Function(_$LoginStatusLogoutImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginStatus
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoginStatusLogoutImpl implements LoginStatusLogout {
  const _$LoginStatusLogoutImpl();

  @override
  String toString() {
    return 'LoginStatus.logout()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoginStatusLogoutImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Session session) login,
    required TResult Function() logout,
  }) {
    return logout();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Session session)? login,
    TResult? Function()? logout,
  }) {
    return logout?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Session session)? login,
    TResult Function()? logout,
    required TResult orElse(),
  }) {
    if (logout != null) {
      return logout();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoginStatusLogin value) login,
    required TResult Function(LoginStatusLogout value) logout,
  }) {
    return logout(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoginStatusLogin value)? login,
    TResult? Function(LoginStatusLogout value)? logout,
  }) {
    return logout?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoginStatusLogin value)? login,
    TResult Function(LoginStatusLogout value)? logout,
    required TResult orElse(),
  }) {
    if (logout != null) {
      return logout(this);
    }
    return orElse();
  }
}

abstract class LoginStatusLogout implements LoginStatus {
  const factory LoginStatusLogout() = _$LoginStatusLogoutImpl;
}
