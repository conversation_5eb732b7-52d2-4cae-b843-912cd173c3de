import 'package:freezed_annotation/freezed_annotation.dart';

part 'identity_verification_address.freezed.dart';
part 'identity_verification_address.g.dart';

@freezed
class Address with _$Address {
  const factory Address({
    /// 郵便番号
    String? postcode,

    /// 都道府県
    String? prefecture,

    /// 市区町村名
    String? city,

    /// 町域名
    String? street,
  }) = _Address;

  factory Address.fromJson(Map<String, dynamic> json) =>
      _$AddressFromJson(json);
}
