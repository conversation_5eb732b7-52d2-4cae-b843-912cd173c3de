import 'package:freezed_annotation/freezed_annotation.dart';

part 'identity_verification_reference_number_info.freezed.dart';
part 'identity_verification_reference_number_info.g.dart';

@freezed
class ReferenceNumberInfo with _$ReferenceNumberInfo {
  const factory ReferenceNumberInfo({
    /// 口座開設申込済みかどうか
    required bool isAccountOpeningRequestSubmitted,

    /// 代表者：本人確認実施不備フラグ
    required bool isRepresentativeHasFault,

    /// 取引責任者（代理人）：本人確認実施不備フラグ
    required bool isAgentHasFault,

    /// 実質的支配者①：本人確認実施不備フラグ
    required bool isBeneficiary1HasFault,

    /// 実質的支配者②：本人確認実施不備フラグ
    required bool isBeneficiary2HasFault,

    /// 実質的支配者③：本人確認実施不備フラグ
    required bool isBeneficiary3HasFault,

    /// 実質的支配者④：本人確認実施不備フラグ
    required bool isBeneficiary4HasFault,
  }) = _ReferenceNumberInfo;

  factory ReferenceNumberInfo.fromJson(Map<String, dynamic> json) =>
      _$ReferenceNumberInfoFromJson(json);
}
