// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'identity_verification_reference_number_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReferenceNumberInfoImpl _$$ReferenceNumberInfoImplFromJson(
        Map<String, dynamic> json) =>
    _$ReferenceNumberInfoImpl(
      isAccountOpeningRequestSubmitted:
          json['isAccountOpeningRequestSubmitted'] as bool,
      isRepresentativeHasFault: json['isRepresentativeHasFault'] as bool,
      isAgentHasFault: json['isAgentHasFault'] as bool,
      isBeneficiary1HasFault: json['isBeneficiary1HasFault'] as bool,
      isBeneficiary2HasFault: json['isBeneficiary2HasFault'] as bool,
      isBeneficiary3HasFault: json['isBeneficiary3HasFault'] as bool,
      isBeneficiary4HasFault: json['isBeneficiary4HasFault'] as bool,
    );

Map<String, dynamic> _$$ReferenceNumberInfoImplToJson(
        _$ReferenceNumberInfoImpl instance) =>
    <String, dynamic>{
      'isAccountOpeningRequestSubmitted':
          instance.isAccountOpeningRequestSubmitted,
      'isRepresentativeHasFault': instance.isRepresentativeHasFault,
      'isAgentHasFault': instance.isAgentHasFault,
      'isBeneficiary1HasFault': instance.isBeneficiary1HasFault,
      'isBeneficiary2HasFault': instance.isBeneficiary2HasFault,
      'isBeneficiary3HasFault': instance.isBeneficiary3HasFault,
      'isBeneficiary4HasFault': instance.isBeneficiary4HasFault,
    };
