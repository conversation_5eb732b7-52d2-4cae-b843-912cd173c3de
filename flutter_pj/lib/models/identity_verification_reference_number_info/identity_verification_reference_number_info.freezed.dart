// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identity_verification_reference_number_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReferenceNumberInfo _$ReferenceNumberInfoFromJson(Map<String, dynamic> json) {
  return _ReferenceNumberInfo.fromJson(json);
}

/// @nodoc
mixin _$ReferenceNumberInfo {
  /// 口座開設申込済みかどうか
  bool get isAccountOpeningRequestSubmitted =>
      throw _privateConstructorUsedError;

  /// 代表者：本人確認実施不備フラグ
  bool get isRepresentativeHasFault => throw _privateConstructorUsedError;

  /// 取引責任者（代理人）：本人確認実施不備フラグ
  bool get isAgentHasFault => throw _privateConstructorUsedError;

  /// 実質的支配者①：本人確認実施不備フラグ
  bool get isBeneficiary1HasFault => throw _privateConstructorUsedError;

  /// 実質的支配者②：本人確認実施不備フラグ
  bool get isBeneficiary2HasFault => throw _privateConstructorUsedError;

  /// 実質的支配者③：本人確認実施不備フラグ
  bool get isBeneficiary3HasFault => throw _privateConstructorUsedError;

  /// 実質的支配者④：本人確認実施不備フラグ
  bool get isBeneficiary4HasFault => throw _privateConstructorUsedError;

  /// Serializes this ReferenceNumberInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReferenceNumberInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReferenceNumberInfoCopyWith<ReferenceNumberInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReferenceNumberInfoCopyWith<$Res> {
  factory $ReferenceNumberInfoCopyWith(
          ReferenceNumberInfo value, $Res Function(ReferenceNumberInfo) then) =
      _$ReferenceNumberInfoCopyWithImpl<$Res, ReferenceNumberInfo>;
  @useResult
  $Res call(
      {bool isAccountOpeningRequestSubmitted,
      bool isRepresentativeHasFault,
      bool isAgentHasFault,
      bool isBeneficiary1HasFault,
      bool isBeneficiary2HasFault,
      bool isBeneficiary3HasFault,
      bool isBeneficiary4HasFault});
}

/// @nodoc
class _$ReferenceNumberInfoCopyWithImpl<$Res, $Val extends ReferenceNumberInfo>
    implements $ReferenceNumberInfoCopyWith<$Res> {
  _$ReferenceNumberInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReferenceNumberInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAccountOpeningRequestSubmitted = null,
    Object? isRepresentativeHasFault = null,
    Object? isAgentHasFault = null,
    Object? isBeneficiary1HasFault = null,
    Object? isBeneficiary2HasFault = null,
    Object? isBeneficiary3HasFault = null,
    Object? isBeneficiary4HasFault = null,
  }) {
    return _then(_value.copyWith(
      isAccountOpeningRequestSubmitted: null == isAccountOpeningRequestSubmitted
          ? _value.isAccountOpeningRequestSubmitted
          : isAccountOpeningRequestSubmitted // ignore: cast_nullable_to_non_nullable
              as bool,
      isRepresentativeHasFault: null == isRepresentativeHasFault
          ? _value.isRepresentativeHasFault
          : isRepresentativeHasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isAgentHasFault: null == isAgentHasFault
          ? _value.isAgentHasFault
          : isAgentHasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary1HasFault: null == isBeneficiary1HasFault
          ? _value.isBeneficiary1HasFault
          : isBeneficiary1HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary2HasFault: null == isBeneficiary2HasFault
          ? _value.isBeneficiary2HasFault
          : isBeneficiary2HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary3HasFault: null == isBeneficiary3HasFault
          ? _value.isBeneficiary3HasFault
          : isBeneficiary3HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary4HasFault: null == isBeneficiary4HasFault
          ? _value.isBeneficiary4HasFault
          : isBeneficiary4HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReferenceNumberInfoImplCopyWith<$Res>
    implements $ReferenceNumberInfoCopyWith<$Res> {
  factory _$$ReferenceNumberInfoImplCopyWith(_$ReferenceNumberInfoImpl value,
          $Res Function(_$ReferenceNumberInfoImpl) then) =
      __$$ReferenceNumberInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isAccountOpeningRequestSubmitted,
      bool isRepresentativeHasFault,
      bool isAgentHasFault,
      bool isBeneficiary1HasFault,
      bool isBeneficiary2HasFault,
      bool isBeneficiary3HasFault,
      bool isBeneficiary4HasFault});
}

/// @nodoc
class __$$ReferenceNumberInfoImplCopyWithImpl<$Res>
    extends _$ReferenceNumberInfoCopyWithImpl<$Res, _$ReferenceNumberInfoImpl>
    implements _$$ReferenceNumberInfoImplCopyWith<$Res> {
  __$$ReferenceNumberInfoImplCopyWithImpl(_$ReferenceNumberInfoImpl _value,
      $Res Function(_$ReferenceNumberInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReferenceNumberInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isAccountOpeningRequestSubmitted = null,
    Object? isRepresentativeHasFault = null,
    Object? isAgentHasFault = null,
    Object? isBeneficiary1HasFault = null,
    Object? isBeneficiary2HasFault = null,
    Object? isBeneficiary3HasFault = null,
    Object? isBeneficiary4HasFault = null,
  }) {
    return _then(_$ReferenceNumberInfoImpl(
      isAccountOpeningRequestSubmitted: null == isAccountOpeningRequestSubmitted
          ? _value.isAccountOpeningRequestSubmitted
          : isAccountOpeningRequestSubmitted // ignore: cast_nullable_to_non_nullable
              as bool,
      isRepresentativeHasFault: null == isRepresentativeHasFault
          ? _value.isRepresentativeHasFault
          : isRepresentativeHasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isAgentHasFault: null == isAgentHasFault
          ? _value.isAgentHasFault
          : isAgentHasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary1HasFault: null == isBeneficiary1HasFault
          ? _value.isBeneficiary1HasFault
          : isBeneficiary1HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary2HasFault: null == isBeneficiary2HasFault
          ? _value.isBeneficiary2HasFault
          : isBeneficiary2HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary3HasFault: null == isBeneficiary3HasFault
          ? _value.isBeneficiary3HasFault
          : isBeneficiary3HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
      isBeneficiary4HasFault: null == isBeneficiary4HasFault
          ? _value.isBeneficiary4HasFault
          : isBeneficiary4HasFault // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReferenceNumberInfoImpl implements _ReferenceNumberInfo {
  const _$ReferenceNumberInfoImpl(
      {required this.isAccountOpeningRequestSubmitted,
      required this.isRepresentativeHasFault,
      required this.isAgentHasFault,
      required this.isBeneficiary1HasFault,
      required this.isBeneficiary2HasFault,
      required this.isBeneficiary3HasFault,
      required this.isBeneficiary4HasFault});

  factory _$ReferenceNumberInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReferenceNumberInfoImplFromJson(json);

  /// 口座開設申込済みかどうか
  @override
  final bool isAccountOpeningRequestSubmitted;

  /// 代表者：本人確認実施不備フラグ
  @override
  final bool isRepresentativeHasFault;

  /// 取引責任者（代理人）：本人確認実施不備フラグ
  @override
  final bool isAgentHasFault;

  /// 実質的支配者①：本人確認実施不備フラグ
  @override
  final bool isBeneficiary1HasFault;

  /// 実質的支配者②：本人確認実施不備フラグ
  @override
  final bool isBeneficiary2HasFault;

  /// 実質的支配者③：本人確認実施不備フラグ
  @override
  final bool isBeneficiary3HasFault;

  /// 実質的支配者④：本人確認実施不備フラグ
  @override
  final bool isBeneficiary4HasFault;

  @override
  String toString() {
    return 'ReferenceNumberInfo(isAccountOpeningRequestSubmitted: $isAccountOpeningRequestSubmitted, isRepresentativeHasFault: $isRepresentativeHasFault, isAgentHasFault: $isAgentHasFault, isBeneficiary1HasFault: $isBeneficiary1HasFault, isBeneficiary2HasFault: $isBeneficiary2HasFault, isBeneficiary3HasFault: $isBeneficiary3HasFault, isBeneficiary4HasFault: $isBeneficiary4HasFault)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReferenceNumberInfoImpl &&
            (identical(other.isAccountOpeningRequestSubmitted,
                    isAccountOpeningRequestSubmitted) ||
                other.isAccountOpeningRequestSubmitted ==
                    isAccountOpeningRequestSubmitted) &&
            (identical(
                    other.isRepresentativeHasFault, isRepresentativeHasFault) ||
                other.isRepresentativeHasFault == isRepresentativeHasFault) &&
            (identical(other.isAgentHasFault, isAgentHasFault) ||
                other.isAgentHasFault == isAgentHasFault) &&
            (identical(other.isBeneficiary1HasFault, isBeneficiary1HasFault) ||
                other.isBeneficiary1HasFault == isBeneficiary1HasFault) &&
            (identical(other.isBeneficiary2HasFault, isBeneficiary2HasFault) ||
                other.isBeneficiary2HasFault == isBeneficiary2HasFault) &&
            (identical(other.isBeneficiary3HasFault, isBeneficiary3HasFault) ||
                other.isBeneficiary3HasFault == isBeneficiary3HasFault) &&
            (identical(other.isBeneficiary4HasFault, isBeneficiary4HasFault) ||
                other.isBeneficiary4HasFault == isBeneficiary4HasFault));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isAccountOpeningRequestSubmitted,
      isRepresentativeHasFault,
      isAgentHasFault,
      isBeneficiary1HasFault,
      isBeneficiary2HasFault,
      isBeneficiary3HasFault,
      isBeneficiary4HasFault);

  /// Create a copy of ReferenceNumberInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReferenceNumberInfoImplCopyWith<_$ReferenceNumberInfoImpl> get copyWith =>
      __$$ReferenceNumberInfoImplCopyWithImpl<_$ReferenceNumberInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReferenceNumberInfoImplToJson(
      this,
    );
  }
}

abstract class _ReferenceNumberInfo implements ReferenceNumberInfo {
  const factory _ReferenceNumberInfo(
      {required final bool isAccountOpeningRequestSubmitted,
      required final bool isRepresentativeHasFault,
      required final bool isAgentHasFault,
      required final bool isBeneficiary1HasFault,
      required final bool isBeneficiary2HasFault,
      required final bool isBeneficiary3HasFault,
      required final bool isBeneficiary4HasFault}) = _$ReferenceNumberInfoImpl;

  factory _ReferenceNumberInfo.fromJson(Map<String, dynamic> json) =
      _$ReferenceNumberInfoImpl.fromJson;

  /// 口座開設申込済みかどうか
  @override
  bool get isAccountOpeningRequestSubmitted;

  /// 代表者：本人確認実施不備フラグ
  @override
  bool get isRepresentativeHasFault;

  /// 取引責任者（代理人）：本人確認実施不備フラグ
  @override
  bool get isAgentHasFault;

  /// 実質的支配者①：本人確認実施不備フラグ
  @override
  bool get isBeneficiary1HasFault;

  /// 実質的支配者②：本人確認実施不備フラグ
  @override
  bool get isBeneficiary2HasFault;

  /// 実質的支配者③：本人確認実施不備フラグ
  @override
  bool get isBeneficiary3HasFault;

  /// 実質的支配者④：本人確認実施不備フラグ
  @override
  bool get isBeneficiary4HasFault;

  /// Create a copy of ReferenceNumberInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReferenceNumberInfoImplCopyWith<_$ReferenceNumberInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
