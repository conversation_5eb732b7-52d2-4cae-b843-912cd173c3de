// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'encrypted_cookie.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EncryptedCookieDataImpl _$$EncryptedCookieDataImplFromJson(
        Map<String, dynamic> json) =>
    _$EncryptedCookieDataImpl(
      ticket: json['ticket'] as String,
      domain: json['domain'] as String,
      path: json['path'] as String,
      secure: json['secure'] as String,
      httpOnly: json['httpOnly'] as String,
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$EncryptedCookieDataImplToJson(
        _$EncryptedCookieDataImpl instance) =>
    <String, dynamic>{
      'ticket': instance.ticket,
      'domain': instance.domain,
      'path': instance.path,
      'secure': instance.secure,
      'httpOnly': instance.httpOnly,
      'runtimeType': instance.$type,
    };

_$EncryptedCookieEmptyImpl _$$EncryptedCookieEmptyImplFromJson(
        Map<String, dynamic> json) =>
    _$EncryptedCookieEmptyImpl(
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$EncryptedCookieEmptyImplToJson(
        _$EncryptedCookieEmptyImpl instance) =>
    <String, dynamic>{
      'runtimeType': instance.$type,
    };
