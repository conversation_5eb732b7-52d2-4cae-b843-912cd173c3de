import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';

part 'encrypted_cookie.freezed.dart';
part 'encrypted_cookie.g.dart';

/// 暗号化Cookieを保持するクラス
/// 一度使用された際には、再度新規で暗号化Cookieを指定する必要がある
@freezed
class EncryptedCookie with _$EncryptedCookie {
  const factory EncryptedCookie({
    required String ticket,
    required String domain,
    required String path,
    required String secure,
    required String httpOnly,
  }) = EncryptedCookieData;

  const factory EncryptedCookie.empty() = EncryptedCookieEmpty;

  factory EncryptedCookie.fromJson(Map<String, dynamic> json) =>
      _$EncryptedCookieFromJson(json);
}

extension EncryptedCookieExt on EncryptedCookie {
  String get toLogText => when(
        (ticket, domain, path, secure, httpOnly) =>
            'Cookie Settings - Ticket: $ticket\nCookie Settings - Domain: $domain\nCookie Settings - Path: $path\nCookie Settings - Secure: $secure\nCookie Settings - HttpOnly: $httpOnly',
        empty: () => '',
      );

  String get ticket {
    return when(
      (ticket, domain, path, secure, httpOnly) => ticket,
      empty: () => '',
    );
  }

  /// オブジェクトをJSON文字列に変換する
  String jsonStringify() => jsonEncode(toJson());
}
