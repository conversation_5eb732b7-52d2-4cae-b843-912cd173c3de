// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'encrypted_cookie.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

EncryptedCookie _$EncryptedCookieFromJson(Map<String, dynamic> json) {
  switch (json['runtimeType']) {
    case 'default':
      return EncryptedCookieData.fromJson(json);
    case 'empty':
      return EncryptedCookieEmpty.fromJson(json);

    default:
      throw CheckedFromJsonException(json, 'runtimeType', 'EncryptedCookie',
          'Invalid union type "${json['runtimeType']}"!');
  }
}

/// @nodoc
mixin _$EncryptedCookie {
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String ticket, String domain, String path, String secure,
            String httpOnly)
        $default, {
    required TResult Function() empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String ticket, String domain, String path, String secure,
            String httpOnly)?
        $default, {
    TResult? Function()? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String ticket, String domain, String path, String secure,
            String httpOnly)?
        $default, {
    TResult Function()? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(EncryptedCookieData value) $default, {
    required TResult Function(EncryptedCookieEmpty value) empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(EncryptedCookieData value)? $default, {
    TResult? Function(EncryptedCookieEmpty value)? empty,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(EncryptedCookieData value)? $default, {
    TResult Function(EncryptedCookieEmpty value)? empty,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Serializes this EncryptedCookie to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EncryptedCookieCopyWith<$Res> {
  factory $EncryptedCookieCopyWith(
          EncryptedCookie value, $Res Function(EncryptedCookie) then) =
      _$EncryptedCookieCopyWithImpl<$Res, EncryptedCookie>;
}

/// @nodoc
class _$EncryptedCookieCopyWithImpl<$Res, $Val extends EncryptedCookie>
    implements $EncryptedCookieCopyWith<$Res> {
  _$EncryptedCookieCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EncryptedCookie
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$EncryptedCookieDataImplCopyWith<$Res> {
  factory _$$EncryptedCookieDataImplCopyWith(_$EncryptedCookieDataImpl value,
          $Res Function(_$EncryptedCookieDataImpl) then) =
      __$$EncryptedCookieDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String ticket,
      String domain,
      String path,
      String secure,
      String httpOnly});
}

/// @nodoc
class __$$EncryptedCookieDataImplCopyWithImpl<$Res>
    extends _$EncryptedCookieCopyWithImpl<$Res, _$EncryptedCookieDataImpl>
    implements _$$EncryptedCookieDataImplCopyWith<$Res> {
  __$$EncryptedCookieDataImplCopyWithImpl(_$EncryptedCookieDataImpl _value,
      $Res Function(_$EncryptedCookieDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of EncryptedCookie
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ticket = null,
    Object? domain = null,
    Object? path = null,
    Object? secure = null,
    Object? httpOnly = null,
  }) {
    return _then(_$EncryptedCookieDataImpl(
      ticket: null == ticket
          ? _value.ticket
          : ticket // ignore: cast_nullable_to_non_nullable
              as String,
      domain: null == domain
          ? _value.domain
          : domain // ignore: cast_nullable_to_non_nullable
              as String,
      path: null == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String,
      secure: null == secure
          ? _value.secure
          : secure // ignore: cast_nullable_to_non_nullable
              as String,
      httpOnly: null == httpOnly
          ? _value.httpOnly
          : httpOnly // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EncryptedCookieDataImpl implements EncryptedCookieData {
  const _$EncryptedCookieDataImpl(
      {required this.ticket,
      required this.domain,
      required this.path,
      required this.secure,
      required this.httpOnly,
      final String? $type})
      : $type = $type ?? 'default';

  factory _$EncryptedCookieDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$EncryptedCookieDataImplFromJson(json);

  @override
  final String ticket;
  @override
  final String domain;
  @override
  final String path;
  @override
  final String secure;
  @override
  final String httpOnly;

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'EncryptedCookie(ticket: $ticket, domain: $domain, path: $path, secure: $secure, httpOnly: $httpOnly)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EncryptedCookieDataImpl &&
            (identical(other.ticket, ticket) || other.ticket == ticket) &&
            (identical(other.domain, domain) || other.domain == domain) &&
            (identical(other.path, path) || other.path == path) &&
            (identical(other.secure, secure) || other.secure == secure) &&
            (identical(other.httpOnly, httpOnly) ||
                other.httpOnly == httpOnly));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, ticket, domain, path, secure, httpOnly);

  /// Create a copy of EncryptedCookie
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EncryptedCookieDataImplCopyWith<_$EncryptedCookieDataImpl> get copyWith =>
      __$$EncryptedCookieDataImplCopyWithImpl<_$EncryptedCookieDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String ticket, String domain, String path, String secure,
            String httpOnly)
        $default, {
    required TResult Function() empty,
  }) {
    return $default(ticket, domain, path, secure, httpOnly);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String ticket, String domain, String path, String secure,
            String httpOnly)?
        $default, {
    TResult? Function()? empty,
  }) {
    return $default?.call(ticket, domain, path, secure, httpOnly);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String ticket, String domain, String path, String secure,
            String httpOnly)?
        $default, {
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(ticket, domain, path, secure, httpOnly);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(EncryptedCookieData value) $default, {
    required TResult Function(EncryptedCookieEmpty value) empty,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(EncryptedCookieData value)? $default, {
    TResult? Function(EncryptedCookieEmpty value)? empty,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(EncryptedCookieData value)? $default, {
    TResult Function(EncryptedCookieEmpty value)? empty,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$EncryptedCookieDataImplToJson(
      this,
    );
  }
}

abstract class EncryptedCookieData implements EncryptedCookie {
  const factory EncryptedCookieData(
      {required final String ticket,
      required final String domain,
      required final String path,
      required final String secure,
      required final String httpOnly}) = _$EncryptedCookieDataImpl;

  factory EncryptedCookieData.fromJson(Map<String, dynamic> json) =
      _$EncryptedCookieDataImpl.fromJson;

  String get ticket;
  String get domain;
  String get path;
  String get secure;
  String get httpOnly;

  /// Create a copy of EncryptedCookie
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EncryptedCookieDataImplCopyWith<_$EncryptedCookieDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EncryptedCookieEmptyImplCopyWith<$Res> {
  factory _$$EncryptedCookieEmptyImplCopyWith(_$EncryptedCookieEmptyImpl value,
          $Res Function(_$EncryptedCookieEmptyImpl) then) =
      __$$EncryptedCookieEmptyImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$EncryptedCookieEmptyImplCopyWithImpl<$Res>
    extends _$EncryptedCookieCopyWithImpl<$Res, _$EncryptedCookieEmptyImpl>
    implements _$$EncryptedCookieEmptyImplCopyWith<$Res> {
  __$$EncryptedCookieEmptyImplCopyWithImpl(_$EncryptedCookieEmptyImpl _value,
      $Res Function(_$EncryptedCookieEmptyImpl) _then)
      : super(_value, _then);

  /// Create a copy of EncryptedCookie
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
@JsonSerializable()
class _$EncryptedCookieEmptyImpl implements EncryptedCookieEmpty {
  const _$EncryptedCookieEmptyImpl({final String? $type})
      : $type = $type ?? 'empty';

  factory _$EncryptedCookieEmptyImpl.fromJson(Map<String, dynamic> json) =>
      _$$EncryptedCookieEmptyImplFromJson(json);

  @JsonKey(name: 'runtimeType')
  final String $type;

  @override
  String toString() {
    return 'EncryptedCookie.empty()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EncryptedCookieEmptyImpl);
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String ticket, String domain, String path, String secure,
            String httpOnly)
        $default, {
    required TResult Function() empty,
  }) {
    return empty();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String ticket, String domain, String path, String secure,
            String httpOnly)?
        $default, {
    TResult? Function()? empty,
  }) {
    return empty?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String ticket, String domain, String path, String secure,
            String httpOnly)?
        $default, {
    TResult Function()? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(EncryptedCookieData value) $default, {
    required TResult Function(EncryptedCookieEmpty value) empty,
  }) {
    return empty(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(EncryptedCookieData value)? $default, {
    TResult? Function(EncryptedCookieEmpty value)? empty,
  }) {
    return empty?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(EncryptedCookieData value)? $default, {
    TResult Function(EncryptedCookieEmpty value)? empty,
    required TResult orElse(),
  }) {
    if (empty != null) {
      return empty(this);
    }
    return orElse();
  }

  @override
  Map<String, dynamic> toJson() {
    return _$$EncryptedCookieEmptyImplToJson(
      this,
    );
  }
}

abstract class EncryptedCookieEmpty implements EncryptedCookie {
  const factory EncryptedCookieEmpty() = _$EncryptedCookieEmptyImpl;

  factory EncryptedCookieEmpty.fromJson(Map<String, dynamic> json) =
      _$EncryptedCookieEmptyImpl.fromJson;
}
