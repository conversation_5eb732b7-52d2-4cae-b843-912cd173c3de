import 'package:freezed_annotation/freezed_annotation.dart';

part 'api_response_with_date.freezed.dart';

@freezed
// APIの返却値に日時を付与したクラス
class ApiResponseWithDate with _$ApiResponseWithDate {
  const factory ApiResponseWithDate({
    required String body,
    required Map<String, String> header,
    String? baseDate,
    String? baseTime,
  }) = _ApiResponseWithDate;
}

extension StringToApiResponseExt on String {
  // テスト時にレスポンスデータを作成する用の拡張関数
  ApiResponseWithDate toApiResponse() {
    return ApiResponseWithDate(
      body: this,
      header: {'set-cookie': 'VDSESSIONID=hogehoge'},
    );
  }
}

extension ApiResponseExt on ApiResponseWithDate {
  /// VD関連APIにて
  /// ヘッダーからVdSessionIdを取り出す際に使用
  /// 取得できなかった際にはVD側でのエラー応答にて対応
  String extractCookie() {
    return header['set-cookie'] ?? '';
  }
}
