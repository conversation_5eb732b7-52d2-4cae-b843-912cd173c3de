// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_response_with_date.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ApiResponseWithDate {
  String get body => throw _privateConstructorUsedError;
  Map<String, String> get header => throw _privateConstructorUsedError;
  String? get baseDate => throw _privateConstructorUsedError;
  String? get baseTime => throw _privateConstructorUsedError;

  /// Create a copy of ApiResponseWithDate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ApiResponseWithDateCopyWith<ApiResponseWithDate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiResponseWithDateCopyWith<$Res> {
  factory $ApiResponseWithDateCopyWith(
          ApiResponseWithDate value, $Res Function(ApiResponseWithDate) then) =
      _$ApiResponseWithDateCopyWithImpl<$Res, ApiResponseWithDate>;
  @useResult
  $Res call(
      {String body,
      Map<String, String> header,
      String? baseDate,
      String? baseTime});
}

/// @nodoc
class _$ApiResponseWithDateCopyWithImpl<$Res, $Val extends ApiResponseWithDate>
    implements $ApiResponseWithDateCopyWith<$Res> {
  _$ApiResponseWithDateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiResponseWithDate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
    Object? header = null,
    Object? baseDate = freezed,
    Object? baseTime = freezed,
  }) {
    return _then(_value.copyWith(
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      header: null == header
          ? _value.header
          : header // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ApiResponseWithDateImplCopyWith<$Res>
    implements $ApiResponseWithDateCopyWith<$Res> {
  factory _$$ApiResponseWithDateImplCopyWith(_$ApiResponseWithDateImpl value,
          $Res Function(_$ApiResponseWithDateImpl) then) =
      __$$ApiResponseWithDateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String body,
      Map<String, String> header,
      String? baseDate,
      String? baseTime});
}

/// @nodoc
class __$$ApiResponseWithDateImplCopyWithImpl<$Res>
    extends _$ApiResponseWithDateCopyWithImpl<$Res, _$ApiResponseWithDateImpl>
    implements _$$ApiResponseWithDateImplCopyWith<$Res> {
  __$$ApiResponseWithDateImplCopyWithImpl(_$ApiResponseWithDateImpl _value,
      $Res Function(_$ApiResponseWithDateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiResponseWithDate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
    Object? header = null,
    Object? baseDate = freezed,
    Object? baseTime = freezed,
  }) {
    return _then(_$ApiResponseWithDateImpl(
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      header: null == header
          ? _value._header
          : header // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$ApiResponseWithDateImpl implements _ApiResponseWithDate {
  const _$ApiResponseWithDateImpl(
      {required this.body,
      required final Map<String, String> header,
      this.baseDate,
      this.baseTime})
      : _header = header;

  @override
  final String body;
  final Map<String, String> _header;
  @override
  Map<String, String> get header {
    if (_header is EqualUnmodifiableMapView) return _header;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_header);
  }

  @override
  final String? baseDate;
  @override
  final String? baseTime;

  @override
  String toString() {
    return 'ApiResponseWithDate(body: $body, header: $header, baseDate: $baseDate, baseTime: $baseTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiResponseWithDateImpl &&
            (identical(other.body, body) || other.body == body) &&
            const DeepCollectionEquality().equals(other._header, _header) &&
            (identical(other.baseDate, baseDate) ||
                other.baseDate == baseDate) &&
            (identical(other.baseTime, baseTime) ||
                other.baseTime == baseTime));
  }

  @override
  int get hashCode => Object.hash(runtimeType, body,
      const DeepCollectionEquality().hash(_header), baseDate, baseTime);

  /// Create a copy of ApiResponseWithDate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiResponseWithDateImplCopyWith<_$ApiResponseWithDateImpl> get copyWith =>
      __$$ApiResponseWithDateImplCopyWithImpl<_$ApiResponseWithDateImpl>(
          this, _$identity);
}

abstract class _ApiResponseWithDate implements ApiResponseWithDate {
  const factory _ApiResponseWithDate(
      {required final String body,
      required final Map<String, String> header,
      final String? baseDate,
      final String? baseTime}) = _$ApiResponseWithDateImpl;

  @override
  String get body;
  @override
  Map<String, String> get header;
  @override
  String? get baseDate;
  @override
  String? get baseTime;

  /// Create a copy of ApiResponseWithDate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiResponseWithDateImplCopyWith<_$ApiResponseWithDateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
