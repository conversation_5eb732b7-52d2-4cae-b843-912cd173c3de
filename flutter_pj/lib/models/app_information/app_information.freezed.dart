// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_information.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AppInformation {
  String get appName => throw _privateConstructorUsedError;
  String get versionName => throw _privateConstructorUsedError;
  int get versionInt => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String appName, String versionName, int versionInt)
        undefined,
    required TResult Function(
            String appName, String versionName, int versionInt)
        ios,
    required TResult Function(
            String appName, String versionName, int versionInt)
        android,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult? Function(String appName, String versionName, int versionInt)? ios,
    TResult? Function(String appName, String versionName, int versionInt)?
        android,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult Function(String appName, String versionName, int versionInt)? ios,
    TResult Function(String appName, String versionName, int versionInt)?
        android,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AppInformationUndefined value) undefined,
    required TResult Function(AppInformationIOS value) ios,
    required TResult Function(AppInformationAndroid value) android,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AppInformationUndefined value)? undefined,
    TResult? Function(AppInformationIOS value)? ios,
    TResult? Function(AppInformationAndroid value)? android,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AppInformationUndefined value)? undefined,
    TResult Function(AppInformationIOS value)? ios,
    TResult Function(AppInformationAndroid value)? android,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppInformationCopyWith<AppInformation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppInformationCopyWith<$Res> {
  factory $AppInformationCopyWith(
          AppInformation value, $Res Function(AppInformation) then) =
      _$AppInformationCopyWithImpl<$Res, AppInformation>;
  @useResult
  $Res call({String appName, String versionName, int versionInt});
}

/// @nodoc
class _$AppInformationCopyWithImpl<$Res, $Val extends AppInformation>
    implements $AppInformationCopyWith<$Res> {
  _$AppInformationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appName = null,
    Object? versionName = null,
    Object? versionInt = null,
  }) {
    return _then(_value.copyWith(
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      versionName: null == versionName
          ? _value.versionName
          : versionName // ignore: cast_nullable_to_non_nullable
              as String,
      versionInt: null == versionInt
          ? _value.versionInt
          : versionInt // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppInformationUndefinedImplCopyWith<$Res>
    implements $AppInformationCopyWith<$Res> {
  factory _$$AppInformationUndefinedImplCopyWith(
          _$AppInformationUndefinedImpl value,
          $Res Function(_$AppInformationUndefinedImpl) then) =
      __$$AppInformationUndefinedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String appName, String versionName, int versionInt});
}

/// @nodoc
class __$$AppInformationUndefinedImplCopyWithImpl<$Res>
    extends _$AppInformationCopyWithImpl<$Res, _$AppInformationUndefinedImpl>
    implements _$$AppInformationUndefinedImplCopyWith<$Res> {
  __$$AppInformationUndefinedImplCopyWithImpl(
      _$AppInformationUndefinedImpl _value,
      $Res Function(_$AppInformationUndefinedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appName = null,
    Object? versionName = null,
    Object? versionInt = null,
  }) {
    return _then(_$AppInformationUndefinedImpl(
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      versionName: null == versionName
          ? _value.versionName
          : versionName // ignore: cast_nullable_to_non_nullable
              as String,
      versionInt: null == versionInt
          ? _value.versionInt
          : versionInt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$AppInformationUndefinedImpl implements AppInformationUndefined {
  const _$AppInformationUndefinedImpl(
      {required this.appName,
      required this.versionName,
      required this.versionInt});

  @override
  final String appName;
  @override
  final String versionName;
  @override
  final int versionInt;

  @override
  String toString() {
    return 'AppInformation.undefined(appName: $appName, versionName: $versionName, versionInt: $versionInt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppInformationUndefinedImpl &&
            (identical(other.appName, appName) || other.appName == appName) &&
            (identical(other.versionName, versionName) ||
                other.versionName == versionName) &&
            (identical(other.versionInt, versionInt) ||
                other.versionInt == versionInt));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, appName, versionName, versionInt);

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppInformationUndefinedImplCopyWith<_$AppInformationUndefinedImpl>
      get copyWith => __$$AppInformationUndefinedImplCopyWithImpl<
          _$AppInformationUndefinedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String appName, String versionName, int versionInt)
        undefined,
    required TResult Function(
            String appName, String versionName, int versionInt)
        ios,
    required TResult Function(
            String appName, String versionName, int versionInt)
        android,
  }) {
    return undefined(appName, versionName, versionInt);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult? Function(String appName, String versionName, int versionInt)? ios,
    TResult? Function(String appName, String versionName, int versionInt)?
        android,
  }) {
    return undefined?.call(appName, versionName, versionInt);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult Function(String appName, String versionName, int versionInt)? ios,
    TResult Function(String appName, String versionName, int versionInt)?
        android,
    required TResult orElse(),
  }) {
    if (undefined != null) {
      return undefined(appName, versionName, versionInt);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AppInformationUndefined value) undefined,
    required TResult Function(AppInformationIOS value) ios,
    required TResult Function(AppInformationAndroid value) android,
  }) {
    return undefined(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AppInformationUndefined value)? undefined,
    TResult? Function(AppInformationIOS value)? ios,
    TResult? Function(AppInformationAndroid value)? android,
  }) {
    return undefined?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AppInformationUndefined value)? undefined,
    TResult Function(AppInformationIOS value)? ios,
    TResult Function(AppInformationAndroid value)? android,
    required TResult orElse(),
  }) {
    if (undefined != null) {
      return undefined(this);
    }
    return orElse();
  }
}

abstract class AppInformationUndefined implements AppInformation {
  const factory AppInformationUndefined(
      {required final String appName,
      required final String versionName,
      required final int versionInt}) = _$AppInformationUndefinedImpl;

  @override
  String get appName;
  @override
  String get versionName;
  @override
  int get versionInt;

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppInformationUndefinedImplCopyWith<_$AppInformationUndefinedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AppInformationIOSImplCopyWith<$Res>
    implements $AppInformationCopyWith<$Res> {
  factory _$$AppInformationIOSImplCopyWith(_$AppInformationIOSImpl value,
          $Res Function(_$AppInformationIOSImpl) then) =
      __$$AppInformationIOSImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String appName, String versionName, int versionInt});
}

/// @nodoc
class __$$AppInformationIOSImplCopyWithImpl<$Res>
    extends _$AppInformationCopyWithImpl<$Res, _$AppInformationIOSImpl>
    implements _$$AppInformationIOSImplCopyWith<$Res> {
  __$$AppInformationIOSImplCopyWithImpl(_$AppInformationIOSImpl _value,
      $Res Function(_$AppInformationIOSImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appName = null,
    Object? versionName = null,
    Object? versionInt = null,
  }) {
    return _then(_$AppInformationIOSImpl(
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      versionName: null == versionName
          ? _value.versionName
          : versionName // ignore: cast_nullable_to_non_nullable
              as String,
      versionInt: null == versionInt
          ? _value.versionInt
          : versionInt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$AppInformationIOSImpl implements AppInformationIOS {
  const _$AppInformationIOSImpl(
      {required this.appName,
      required this.versionName,
      required this.versionInt});

  @override
  final String appName;
  @override
  final String versionName;
  @override
  final int versionInt;

  @override
  String toString() {
    return 'AppInformation.ios(appName: $appName, versionName: $versionName, versionInt: $versionInt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppInformationIOSImpl &&
            (identical(other.appName, appName) || other.appName == appName) &&
            (identical(other.versionName, versionName) ||
                other.versionName == versionName) &&
            (identical(other.versionInt, versionInt) ||
                other.versionInt == versionInt));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, appName, versionName, versionInt);

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppInformationIOSImplCopyWith<_$AppInformationIOSImpl> get copyWith =>
      __$$AppInformationIOSImplCopyWithImpl<_$AppInformationIOSImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String appName, String versionName, int versionInt)
        undefined,
    required TResult Function(
            String appName, String versionName, int versionInt)
        ios,
    required TResult Function(
            String appName, String versionName, int versionInt)
        android,
  }) {
    return ios(appName, versionName, versionInt);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult? Function(String appName, String versionName, int versionInt)? ios,
    TResult? Function(String appName, String versionName, int versionInt)?
        android,
  }) {
    return ios?.call(appName, versionName, versionInt);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult Function(String appName, String versionName, int versionInt)? ios,
    TResult Function(String appName, String versionName, int versionInt)?
        android,
    required TResult orElse(),
  }) {
    if (ios != null) {
      return ios(appName, versionName, versionInt);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AppInformationUndefined value) undefined,
    required TResult Function(AppInformationIOS value) ios,
    required TResult Function(AppInformationAndroid value) android,
  }) {
    return ios(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AppInformationUndefined value)? undefined,
    TResult? Function(AppInformationIOS value)? ios,
    TResult? Function(AppInformationAndroid value)? android,
  }) {
    return ios?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AppInformationUndefined value)? undefined,
    TResult Function(AppInformationIOS value)? ios,
    TResult Function(AppInformationAndroid value)? android,
    required TResult orElse(),
  }) {
    if (ios != null) {
      return ios(this);
    }
    return orElse();
  }
}

abstract class AppInformationIOS implements AppInformation {
  const factory AppInformationIOS(
      {required final String appName,
      required final String versionName,
      required final int versionInt}) = _$AppInformationIOSImpl;

  @override
  String get appName;
  @override
  String get versionName;
  @override
  int get versionInt;

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppInformationIOSImplCopyWith<_$AppInformationIOSImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AppInformationAndroidImplCopyWith<$Res>
    implements $AppInformationCopyWith<$Res> {
  factory _$$AppInformationAndroidImplCopyWith(
          _$AppInformationAndroidImpl value,
          $Res Function(_$AppInformationAndroidImpl) then) =
      __$$AppInformationAndroidImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String appName, String versionName, int versionInt});
}

/// @nodoc
class __$$AppInformationAndroidImplCopyWithImpl<$Res>
    extends _$AppInformationCopyWithImpl<$Res, _$AppInformationAndroidImpl>
    implements _$$AppInformationAndroidImplCopyWith<$Res> {
  __$$AppInformationAndroidImplCopyWithImpl(_$AppInformationAndroidImpl _value,
      $Res Function(_$AppInformationAndroidImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? appName = null,
    Object? versionName = null,
    Object? versionInt = null,
  }) {
    return _then(_$AppInformationAndroidImpl(
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      versionName: null == versionName
          ? _value.versionName
          : versionName // ignore: cast_nullable_to_non_nullable
              as String,
      versionInt: null == versionInt
          ? _value.versionInt
          : versionInt // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$AppInformationAndroidImpl implements AppInformationAndroid {
  const _$AppInformationAndroidImpl(
      {required this.appName,
      required this.versionName,
      required this.versionInt});

  @override
  final String appName;
  @override
  final String versionName;
  @override
  final int versionInt;

  @override
  String toString() {
    return 'AppInformation.android(appName: $appName, versionName: $versionName, versionInt: $versionInt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppInformationAndroidImpl &&
            (identical(other.appName, appName) || other.appName == appName) &&
            (identical(other.versionName, versionName) ||
                other.versionName == versionName) &&
            (identical(other.versionInt, versionInt) ||
                other.versionInt == versionInt));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, appName, versionName, versionInt);

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppInformationAndroidImplCopyWith<_$AppInformationAndroidImpl>
      get copyWith => __$$AppInformationAndroidImplCopyWithImpl<
          _$AppInformationAndroidImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            String appName, String versionName, int versionInt)
        undefined,
    required TResult Function(
            String appName, String versionName, int versionInt)
        ios,
    required TResult Function(
            String appName, String versionName, int versionInt)
        android,
  }) {
    return android(appName, versionName, versionInt);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult? Function(String appName, String versionName, int versionInt)? ios,
    TResult? Function(String appName, String versionName, int versionInt)?
        android,
  }) {
    return android?.call(appName, versionName, versionInt);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String appName, String versionName, int versionInt)?
        undefined,
    TResult Function(String appName, String versionName, int versionInt)? ios,
    TResult Function(String appName, String versionName, int versionInt)?
        android,
    required TResult orElse(),
  }) {
    if (android != null) {
      return android(appName, versionName, versionInt);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AppInformationUndefined value) undefined,
    required TResult Function(AppInformationIOS value) ios,
    required TResult Function(AppInformationAndroid value) android,
  }) {
    return android(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AppInformationUndefined value)? undefined,
    TResult? Function(AppInformationIOS value)? ios,
    TResult? Function(AppInformationAndroid value)? android,
  }) {
    return android?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AppInformationUndefined value)? undefined,
    TResult Function(AppInformationIOS value)? ios,
    TResult Function(AppInformationAndroid value)? android,
    required TResult orElse(),
  }) {
    if (android != null) {
      return android(this);
    }
    return orElse();
  }
}

abstract class AppInformationAndroid implements AppInformation {
  const factory AppInformationAndroid(
      {required final String appName,
      required final String versionName,
      required final int versionInt}) = _$AppInformationAndroidImpl;

  @override
  String get appName;
  @override
  String get versionName;
  @override
  int get versionInt;

  /// Create a copy of AppInformation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppInformationAndroidImplCopyWith<_$AppInformationAndroidImpl>
      get copyWith => throw _privateConstructorUsedError;
}
