import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_information.freezed.dart';

@freezed
class AppInformation with _$AppInformation {
  const factory AppInformation.undefined({
    required String appName,
    required String versionName,
    required int versionInt,
  }) = AppInformationUndefined;

  const factory AppInformation.ios({
    required String appName,
    required String versionName,
    required int versionInt,
  }) = AppInformationIOS;

  const factory AppInformation.android({
    required String appName,
    required String versionName,
    required int versionInt,
  }) = AppInformationAndroid;
}
