// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jpki_oauth_api.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$JpkiOAuthApiImpl _$$JpkiOAuthApiImplFromJson(Map<String, dynamic> json) =>
    _$JpkiOAuthApiImpl(
      tokenType: json['tokenType'] as String,
      accessToken: json['accessToken'] as String,
      expiresIn: (json['expiresIn'] as num?)?.toInt(),
      consentedOn: (json['consentedOn'] as num?)?.toInt(),
      scope: json['scope'] as String?,
    );

Map<String, dynamic> _$$JpkiOAuthApiImplToJson(_$JpkiOAuthApiImpl instance) =>
    <String, dynamic>{
      'tokenType': instance.tokenType,
      'accessToken': instance.accessToken,
      'expiresIn': instance.expiresIn,
      'consentedOn': instance.consentedOn,
      'scope': instance.scope,
    };
