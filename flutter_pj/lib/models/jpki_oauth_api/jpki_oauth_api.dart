import 'package:freezed_annotation/freezed_annotation.dart';

part 'jpki_oauth_api.freezed.dart';
part 'jpki_oauth_api.g.dart';

@freezed
class JpkiOAuthApi with _$JpkiOAuthApi {
  const factory JpkiOAuthApi({
    // トークンタイプ
    required String tokenType,
    // アクセストークン
    required String accessToken,
    // アクセストークン有効時間(秒)
    int? expiresIn,
    // アクセストークンの取得時刻(UNIX)
    int? consentedOn,
    // アクセストークンの有効な操作範囲(スコープ)
    String? scope,
  }) = _JpkiOAuthApi;

  factory JpkiOAuthApi.fromJson(Map<String, dynamic> json) => JpkiOAuthApi(
        // APIのレスポンスはスネークケース
        tokenType: json['token_type'] as String,
        accessToken: json['access_token'] as String,
        expiresIn: json['expires_in'] as int,
        consentedOn: json['consented_on'] as int,
        scope: json['scope'] as String,
      );
}
