// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jpki_oauth_api.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

JpkiOAuthApi _$JpkiOAuthApiFromJson(Map<String, dynamic> json) {
  return _JpkiOAuthApi.fromJson(json);
}

/// @nodoc
mixin _$JpkiOAuthApi {
// トークンタイプ
  String get tokenType => throw _privateConstructorUsedError; // アクセストークン
  String get accessToken =>
      throw _privateConstructorUsedError; // アクセストークン有効時間(秒)
  int? get expiresIn =>
      throw _privateConstructorUsedError; // アクセストークンの取得時刻(UNIX)
  int? get consentedOn =>
      throw _privateConstructorUsedError; // アクセストークンの有効な操作範囲(スコープ)
  String? get scope => throw _privateConstructorUsedError;

  /// Serializes this JpkiOAuthApi to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of JpkiOAuthApi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JpkiOAuthApiCopyWith<JpkiOAuthApi> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JpkiOAuthApiCopyWith<$Res> {
  factory $JpkiOAuthApiCopyWith(
          JpkiOAuthApi value, $Res Function(JpkiOAuthApi) then) =
      _$JpkiOAuthApiCopyWithImpl<$Res, JpkiOAuthApi>;
  @useResult
  $Res call(
      {String tokenType,
      String accessToken,
      int? expiresIn,
      int? consentedOn,
      String? scope});
}

/// @nodoc
class _$JpkiOAuthApiCopyWithImpl<$Res, $Val extends JpkiOAuthApi>
    implements $JpkiOAuthApiCopyWith<$Res> {
  _$JpkiOAuthApiCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JpkiOAuthApi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tokenType = null,
    Object? accessToken = null,
    Object? expiresIn = freezed,
    Object? consentedOn = freezed,
    Object? scope = freezed,
  }) {
    return _then(_value.copyWith(
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: freezed == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int?,
      consentedOn: freezed == consentedOn
          ? _value.consentedOn
          : consentedOn // ignore: cast_nullable_to_non_nullable
              as int?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JpkiOAuthApiImplCopyWith<$Res>
    implements $JpkiOAuthApiCopyWith<$Res> {
  factory _$$JpkiOAuthApiImplCopyWith(
          _$JpkiOAuthApiImpl value, $Res Function(_$JpkiOAuthApiImpl) then) =
      __$$JpkiOAuthApiImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String tokenType,
      String accessToken,
      int? expiresIn,
      int? consentedOn,
      String? scope});
}

/// @nodoc
class __$$JpkiOAuthApiImplCopyWithImpl<$Res>
    extends _$JpkiOAuthApiCopyWithImpl<$Res, _$JpkiOAuthApiImpl>
    implements _$$JpkiOAuthApiImplCopyWith<$Res> {
  __$$JpkiOAuthApiImplCopyWithImpl(
      _$JpkiOAuthApiImpl _value, $Res Function(_$JpkiOAuthApiImpl) _then)
      : super(_value, _then);

  /// Create a copy of JpkiOAuthApi
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tokenType = null,
    Object? accessToken = null,
    Object? expiresIn = freezed,
    Object? consentedOn = freezed,
    Object? scope = freezed,
  }) {
    return _then(_$JpkiOAuthApiImpl(
      tokenType: null == tokenType
          ? _value.tokenType
          : tokenType // ignore: cast_nullable_to_non_nullable
              as String,
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      expiresIn: freezed == expiresIn
          ? _value.expiresIn
          : expiresIn // ignore: cast_nullable_to_non_nullable
              as int?,
      consentedOn: freezed == consentedOn
          ? _value.consentedOn
          : consentedOn // ignore: cast_nullable_to_non_nullable
              as int?,
      scope: freezed == scope
          ? _value.scope
          : scope // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$JpkiOAuthApiImpl implements _JpkiOAuthApi {
  const _$JpkiOAuthApiImpl(
      {required this.tokenType,
      required this.accessToken,
      this.expiresIn,
      this.consentedOn,
      this.scope});

  factory _$JpkiOAuthApiImpl.fromJson(Map<String, dynamic> json) =>
      _$$JpkiOAuthApiImplFromJson(json);

// トークンタイプ
  @override
  final String tokenType;
// アクセストークン
  @override
  final String accessToken;
// アクセストークン有効時間(秒)
  @override
  final int? expiresIn;
// アクセストークンの取得時刻(UNIX)
  @override
  final int? consentedOn;
// アクセストークンの有効な操作範囲(スコープ)
  @override
  final String? scope;

  @override
  String toString() {
    return 'JpkiOAuthApi(tokenType: $tokenType, accessToken: $accessToken, expiresIn: $expiresIn, consentedOn: $consentedOn, scope: $scope)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JpkiOAuthApiImpl &&
            (identical(other.tokenType, tokenType) ||
                other.tokenType == tokenType) &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.expiresIn, expiresIn) ||
                other.expiresIn == expiresIn) &&
            (identical(other.consentedOn, consentedOn) ||
                other.consentedOn == consentedOn) &&
            (identical(other.scope, scope) || other.scope == scope));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, tokenType, accessToken, expiresIn, consentedOn, scope);

  /// Create a copy of JpkiOAuthApi
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JpkiOAuthApiImplCopyWith<_$JpkiOAuthApiImpl> get copyWith =>
      __$$JpkiOAuthApiImplCopyWithImpl<_$JpkiOAuthApiImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$JpkiOAuthApiImplToJson(
      this,
    );
  }
}

abstract class _JpkiOAuthApi implements JpkiOAuthApi {
  const factory _JpkiOAuthApi(
      {required final String tokenType,
      required final String accessToken,
      final int? expiresIn,
      final int? consentedOn,
      final String? scope}) = _$JpkiOAuthApiImpl;

  factory _JpkiOAuthApi.fromJson(Map<String, dynamic> json) =
      _$JpkiOAuthApiImpl.fromJson;

// トークンタイプ
  @override
  String get tokenType; // アクセストークン
  @override
  String get accessToken; // アクセストークン有効時間(秒)
  @override
  int? get expiresIn; // アクセストークンの取得時刻(UNIX)
  @override
  int? get consentedOn; // アクセストークンの有効な操作範囲(スコープ)
  @override
  String? get scope;

  /// Create a copy of JpkiOAuthApi
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JpkiOAuthApiImplCopyWith<_$JpkiOAuthApiImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
