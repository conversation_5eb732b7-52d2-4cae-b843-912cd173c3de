/// VDIDログイン時に発生した業務エラー
class VdidLoginApplicationException implements Exception {
  const VdidLoginApplicationException({
    required this.message,
    required this.code,
    required this.pageHandleType,
  });

  final String message;
  final String code;
  final PageHandleType pageHandleType;
}

/// VDIDログイン時に発生したシステムエラー
class VdidLoginSystemException implements Exception {
  const VdidLoginSystemException({
    required this.message,
    required this.code,
  });

  final String message;
  final String code;
}

/// 業務エラー発生時にどの画面へ遷移するか
sealed class PageHandleType {
  const factory PageHandleType.goToNextPage(String nextPageId) = GoToNextPage;
  const factory PageHandleType.backToPreviousPage() = BackToPreviousPage;
}

final class GoToNextPage implements PageHandleType {
  const GoToNextPage(this.nextPageId);

  final String nextPageId;
}

final class BackToPreviousPage implements PageHandleType {
  const BackToPreviousPage();
}
