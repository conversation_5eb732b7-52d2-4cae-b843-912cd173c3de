// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_daily_total_cache_key.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TransactionDailyTotalCacheKey {
  String get accountId => throw _privateConstructorUsedError;
  String get dateTo => throw _privateConstructorUsedError;

  /// Create a copy of TransactionDailyTotalCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionDailyTotalCacheKeyCopyWith<TransactionDailyTotalCacheKey>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionDailyTotalCacheKeyCopyWith<$Res> {
  factory $TransactionDailyTotalCacheKeyCopyWith(
          TransactionDailyTotalCacheKey value,
          $Res Function(TransactionDailyTotalCacheKey) then) =
      _$TransactionDailyTotalCacheKeyCopyWithImpl<$Res,
          TransactionDailyTotalCacheKey>;
  @useResult
  $Res call({String accountId, String dateTo});
}

/// @nodoc
class _$TransactionDailyTotalCacheKeyCopyWithImpl<$Res,
        $Val extends TransactionDailyTotalCacheKey>
    implements $TransactionDailyTotalCacheKeyCopyWith<$Res> {
  _$TransactionDailyTotalCacheKeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionDailyTotalCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? dateTo = null,
  }) {
    return _then(_value.copyWith(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      dateTo: null == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransactionDailyTotalCacheKeyImplCopyWith<$Res>
    implements $TransactionDailyTotalCacheKeyCopyWith<$Res> {
  factory _$$TransactionDailyTotalCacheKeyImplCopyWith(
          _$TransactionDailyTotalCacheKeyImpl value,
          $Res Function(_$TransactionDailyTotalCacheKeyImpl) then) =
      __$$TransactionDailyTotalCacheKeyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accountId, String dateTo});
}

/// @nodoc
class __$$TransactionDailyTotalCacheKeyImplCopyWithImpl<$Res>
    extends _$TransactionDailyTotalCacheKeyCopyWithImpl<$Res,
        _$TransactionDailyTotalCacheKeyImpl>
    implements _$$TransactionDailyTotalCacheKeyImplCopyWith<$Res> {
  __$$TransactionDailyTotalCacheKeyImplCopyWithImpl(
      _$TransactionDailyTotalCacheKeyImpl _value,
      $Res Function(_$TransactionDailyTotalCacheKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of TransactionDailyTotalCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? dateTo = null,
  }) {
    return _then(_$TransactionDailyTotalCacheKeyImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      dateTo: null == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TransactionDailyTotalCacheKeyImpl
    implements _TransactionDailyTotalCacheKey {
  const _$TransactionDailyTotalCacheKeyImpl(
      {required this.accountId, required this.dateTo});

  @override
  final String accountId;
  @override
  final String dateTo;

  @override
  String toString() {
    return 'TransactionDailyTotalCacheKey(accountId: $accountId, dateTo: $dateTo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionDailyTotalCacheKeyImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, dateTo);

  /// Create a copy of TransactionDailyTotalCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionDailyTotalCacheKeyImplCopyWith<
          _$TransactionDailyTotalCacheKeyImpl>
      get copyWith => __$$TransactionDailyTotalCacheKeyImplCopyWithImpl<
          _$TransactionDailyTotalCacheKeyImpl>(this, _$identity);
}

abstract class _TransactionDailyTotalCacheKey
    implements TransactionDailyTotalCacheKey {
  const factory _TransactionDailyTotalCacheKey(
      {required final String accountId,
      required final String dateTo}) = _$TransactionDailyTotalCacheKeyImpl;

  @override
  String get accountId;
  @override
  String get dateTo;

  /// Create a copy of TransactionDailyTotalCacheKey
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionDailyTotalCacheKeyImplCopyWith<
          _$TransactionDailyTotalCacheKeyImpl>
      get copyWith => throw _privateConstructorUsedError;
}
