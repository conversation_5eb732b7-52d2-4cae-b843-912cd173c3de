// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction_daily_total.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TransactionDailyTotal {
  DateTime get date => throw _privateConstructorUsedError;
  int get totalDeposit => throw _privateConstructorUsedError;
  int get totalWithdrawal => throw _privateConstructorUsedError;

  /// Create a copy of TransactionDailyTotal
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionDailyTotalCopyWith<TransactionDailyTotal> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionDailyTotalCopyWith<$Res> {
  factory $TransactionDailyTotalCopyWith(TransactionDailyTotal value,
          $Res Function(TransactionDailyTotal) then) =
      _$TransactionDailyTotalCopyWithImpl<$Res, TransactionDailyTotal>;
  @useResult
  $Res call({DateTime date, int totalDeposit, int totalWithdrawal});
}

/// @nodoc
class _$TransactionDailyTotalCopyWithImpl<$Res,
        $Val extends TransactionDailyTotal>
    implements $TransactionDailyTotalCopyWith<$Res> {
  _$TransactionDailyTotalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionDailyTotal
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? totalDeposit = null,
    Object? totalWithdrawal = null,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalDeposit: null == totalDeposit
          ? _value.totalDeposit
          : totalDeposit // ignore: cast_nullable_to_non_nullable
              as int,
      totalWithdrawal: null == totalWithdrawal
          ? _value.totalWithdrawal
          : totalWithdrawal // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransactionDailyTotalImplCopyWith<$Res>
    implements $TransactionDailyTotalCopyWith<$Res> {
  factory _$$TransactionDailyTotalImplCopyWith(
          _$TransactionDailyTotalImpl value,
          $Res Function(_$TransactionDailyTotalImpl) then) =
      __$$TransactionDailyTotalImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime date, int totalDeposit, int totalWithdrawal});
}

/// @nodoc
class __$$TransactionDailyTotalImplCopyWithImpl<$Res>
    extends _$TransactionDailyTotalCopyWithImpl<$Res,
        _$TransactionDailyTotalImpl>
    implements _$$TransactionDailyTotalImplCopyWith<$Res> {
  __$$TransactionDailyTotalImplCopyWithImpl(_$TransactionDailyTotalImpl _value,
      $Res Function(_$TransactionDailyTotalImpl) _then)
      : super(_value, _then);

  /// Create a copy of TransactionDailyTotal
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? totalDeposit = null,
    Object? totalWithdrawal = null,
  }) {
    return _then(_$TransactionDailyTotalImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalDeposit: null == totalDeposit
          ? _value.totalDeposit
          : totalDeposit // ignore: cast_nullable_to_non_nullable
              as int,
      totalWithdrawal: null == totalWithdrawal
          ? _value.totalWithdrawal
          : totalWithdrawal // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$TransactionDailyTotalImpl implements _TransactionDailyTotal {
  const _$TransactionDailyTotalImpl(
      {required this.date,
      required this.totalDeposit,
      required this.totalWithdrawal});

  @override
  final DateTime date;
  @override
  final int totalDeposit;
  @override
  final int totalWithdrawal;

  @override
  String toString() {
    return 'TransactionDailyTotal(date: $date, totalDeposit: $totalDeposit, totalWithdrawal: $totalWithdrawal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionDailyTotalImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.totalDeposit, totalDeposit) ||
                other.totalDeposit == totalDeposit) &&
            (identical(other.totalWithdrawal, totalWithdrawal) ||
                other.totalWithdrawal == totalWithdrawal));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, date, totalDeposit, totalWithdrawal);

  /// Create a copy of TransactionDailyTotal
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionDailyTotalImplCopyWith<_$TransactionDailyTotalImpl>
      get copyWith => __$$TransactionDailyTotalImplCopyWithImpl<
          _$TransactionDailyTotalImpl>(this, _$identity);
}

abstract class _TransactionDailyTotal implements TransactionDailyTotal {
  const factory _TransactionDailyTotal(
      {required final DateTime date,
      required final int totalDeposit,
      required final int totalWithdrawal}) = _$TransactionDailyTotalImpl;

  @override
  DateTime get date;
  @override
  int get totalDeposit;
  @override
  int get totalWithdrawal;

  /// Create a copy of TransactionDailyTotal
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionDailyTotalImplCopyWith<_$TransactionDailyTotalImpl>
      get copyWith => throw _privateConstructorUsedError;
}
