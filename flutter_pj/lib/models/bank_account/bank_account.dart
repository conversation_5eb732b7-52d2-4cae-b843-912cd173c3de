import 'package:dtp_app/models/account_settings/account_settings.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/bank_account_base_info/bank_account_base_info.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_account.freezed.dart';

@freezed
class BankAccount with _$BankAccount {
  const factory BankAccount({
    required BankAccountBaseInfo baseInfo,
    required DisplayConfig displayConfig,
  }) = _BankAccount;

  const factory BankAccount.withBalanceDetail({
    required BankAccountBaseInfo baseInfo,
    required BankAccountBalanceDetail balanceDetail,
    required DisplayConfig displayConfig,
  }) = BankAccountWithBalanceDetail;

  const factory BankAccount.withError({
    required BankAccountBaseInfo baseInfo,
    required AppError error,
    required DisplayConfig displayConfig,
  }) = BankAccountWithError;
}

extension BankAccountExt on BankAccount {
  String? get accountId => baseInfo.accountId;

  String? get baseDate {
    return map(
      (e) => null,
      withBalanceDetail: (e) => e.balanceDetail.baseDate,
      withError: (e) => e.error.baseDate,
    );
  }

  String? get baseTime {
    return map(
      (e) => null,
      withBalanceDetail: (e) => e.balanceDetail.baseTime,
      withError: (e) => e.error.baseTime,
    );
  }
}

extension BankAccountListExt on List<BankAccount> {
  /// 引数として渡された口座詳細情報で、既存の口座情報を更新する
  /// すでに持っていた詳細情報は上書きされる
  List<BankAccount> overwriteBalanceDetails(
    List<BankAccountBalanceDetail> balanceDetailList,
  ) {
    return map((e) {
      final balanceDetail = balanceDetailList
          .where(
            (detail) => detail.accountId == e.accountId,
          )
          .firstOrNull;
      if (balanceDetail == null) {
        return e;
      } else {
        return e.when(
          (baseInfo, displayConfig) => BankAccount.withBalanceDetail(
            baseInfo: baseInfo,
            displayConfig: displayConfig,
            balanceDetail: balanceDetail,
          ),
          withBalanceDetail: (baseInfo, _, displayConfig) =>
              BankAccount.withBalanceDetail(
            baseInfo: baseInfo,
            balanceDetail: balanceDetail,
            displayConfig: displayConfig,
          ),
          withError: (baseInfo, error, displayConfig) =>
              BankAccount.withBalanceDetail(
            baseInfo: baseInfo,
            balanceDetail: balanceDetail,
            displayConfig: displayConfig,
          ),
        );
      }
    }).toList();
  }

  /// エラー情報をセットして上書きする
  List<BankAccount> overwriteError(String? accountId, AppError error) {
    return map((e) {
      if (e.accountId == accountId) {
        return BankAccountWithError(
          baseInfo: e.baseInfo,
          error: error.copyWith(isShowOnDialog: false),
          displayConfig: e.displayConfig,
        );
      } else {
        return e;
      }
    }).toList();
  }

  /// DisplayConfigの表示順序にしたがって口座情報を並び替える
  List<BankAccount> sortedByDisplayOrder() {
    return sorted(
      (a, b) =>
          a.displayConfig.displayOrder.compareTo(b.displayConfig.displayOrder),
    ).toList();
  }

  ///  口座表示設定情報を口座残高情報に適用する
  List<BankAccount> overwriteDisplayConfig(AccountSettings accountSettings) {
    // 口座表示設定にindexを付与する
    final displayConfigs = accountSettings.accounts
        .mapIndexed(
          (setting, index) => DisplayConfig(
            accountId: setting.accountId,
            displayOrder: index,
            displayName: setting.displayName,
            isHidden: setting.isHidden,
            accountApiType: AccountApiType.values.byName(
              setting.accountApiType,
            ),
          ),
        )
        .toList();

    return map((e) {
      final displayConfig = displayConfigs
          .where((displayConfig) => e.accountId == displayConfig.accountId)
          .firstOrNull;
      if (displayConfig != null) {
        return e.copyWith(displayConfig: displayConfig);
      } else {
        return e;
      }
    }).toList();
  }

  /// エラー情報を削除する
  /// エラーがないものはそのまま
  List<BankAccount> resetError() {
    return map((e) {
      return e.when(
        (baseInfo, displayConfig) => e,
        withBalanceDetail: (baseInfo, balanceDetail, displayConfig) => e,
        withError: (baseInfo, error, displayConfig) => BankAccount(
          baseInfo: baseInfo,
          displayConfig: displayConfig,
        ),
      );
    }).toList();
  }

  /// 残高詳細情報を取り除き、ローディング中の券面を表示させる
  /// HACK 型での出し訳によってややこしくなっている為、ローディングフラグをそれぞれのBankAccount
  /// インスタンスに保持させる形に改修させる
  List<BankAccount> overwriteLoading(String accountId) {
    return map((e) {
      if (e.accountId == accountId) {
        return BankAccount(
          baseInfo: e.baseInfo,
          displayConfig: e.displayConfig,
        );
      } else {
        return e;
      }
    }).toList();
  }
}

extension BankAccountIterable on Iterable<BankAccount> {
  /// 口座表示設定がTrueになっている口座のaccountIdのみを返す
  List<String> get visibleAccountIds {
    return onlyVisible().map((it) => it.accountId).whereType<String>().toList();
  }

  /// 口座詳細情報のリストに変換する
  /// 詳細情報を持たない口座については無視される
  List<BankAccountBalanceDetail> toBalanceDetailList() {
    return map(
      (e) => e.when(
        (baseInfo, displayConfig) => null,
        withBalanceDetail: (baseInfo, balanceDetail, displayConfig) =>
            balanceDetail,
        withError: (baseInfo, error, displayConfig) => null,
      ),
    ).whereType<BankAccountBalanceDetail>().toList();
  }

  /// 指定口座残高取得APIで詳細情報取得済みの口座だけを抽出する
  /// 残高詳細を持たない口座は無視される
  List<BankAccountWithBalanceDetail> onlyHasBalanceDetail() {
    return whereType<BankAccountWithBalanceDetail>().toList();
  }

  Iterable<BankAccount> onlyVisible() {
    final accounts = where((e) => !e.displayConfig.isHidden);
    return accounts;
  }

  AppError? get firstError {
    return whereType<BankAccountWithError>().firstOrNull?.error;
  }

  //freee・金融機関再連携が必要なものを全て抽出する
  bool get isFreeeReConnect {
    final isFreeeReConnectAccounts = onlyVisible()
        .onlyHasBalanceDetail()
        .where((e) => e.balanceDetail.isFreeeReConnectFinancialInstitution)
        .toList();
    return isFreeeReConnectAccounts.isNotEmpty;
  }

  // 保有するエラー情報を全て抽出する
  List<AppError> get errorList {
    return whereType<BankAccountWithError>().map((e) => e.error).toList();
  }
}
