// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BankAccount {
  BankAccountBaseInfo get baseInfo => throw _privateConstructorUsedError;
  DisplayConfig get displayConfig => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)
        $default, {
    required TResult Function(BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail, DisplayConfig displayConfig)
        withBalanceDetail,
    required TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)
        withError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult? Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult? Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_BankAccount value) $default, {
    required TResult Function(BankAccountWithBalanceDetail value)
        withBalanceDetail,
    required TResult Function(BankAccountWithError value) withError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_BankAccount value)? $default, {
    TResult? Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult? Function(BankAccountWithError value)? withError,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_BankAccount value)? $default, {
    TResult Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult Function(BankAccountWithError value)? withError,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankAccountCopyWith<BankAccount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountCopyWith<$Res> {
  factory $BankAccountCopyWith(
          BankAccount value, $Res Function(BankAccount) then) =
      _$BankAccountCopyWithImpl<$Res, BankAccount>;
  @useResult
  $Res call({BankAccountBaseInfo baseInfo, DisplayConfig displayConfig});

  $BankAccountBaseInfoCopyWith<$Res> get baseInfo;
  $DisplayConfigCopyWith<$Res> get displayConfig;
}

/// @nodoc
class _$BankAccountCopyWithImpl<$Res, $Val extends BankAccount>
    implements $BankAccountCopyWith<$Res> {
  _$BankAccountCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseInfo = null,
    Object? displayConfig = null,
  }) {
    return _then(_value.copyWith(
      baseInfo: null == baseInfo
          ? _value.baseInfo
          : baseInfo // ignore: cast_nullable_to_non_nullable
              as BankAccountBaseInfo,
      displayConfig: null == displayConfig
          ? _value.displayConfig
          : displayConfig // ignore: cast_nullable_to_non_nullable
              as DisplayConfig,
    ) as $Val);
  }

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BankAccountBaseInfoCopyWith<$Res> get baseInfo {
    return $BankAccountBaseInfoCopyWith<$Res>(_value.baseInfo, (value) {
      return _then(_value.copyWith(baseInfo: value) as $Val);
    });
  }

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DisplayConfigCopyWith<$Res> get displayConfig {
    return $DisplayConfigCopyWith<$Res>(_value.displayConfig, (value) {
      return _then(_value.copyWith(displayConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BankAccountImplCopyWith<$Res>
    implements $BankAccountCopyWith<$Res> {
  factory _$$BankAccountImplCopyWith(
          _$BankAccountImpl value, $Res Function(_$BankAccountImpl) then) =
      __$$BankAccountImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({BankAccountBaseInfo baseInfo, DisplayConfig displayConfig});

  @override
  $BankAccountBaseInfoCopyWith<$Res> get baseInfo;
  @override
  $DisplayConfigCopyWith<$Res> get displayConfig;
}

/// @nodoc
class __$$BankAccountImplCopyWithImpl<$Res>
    extends _$BankAccountCopyWithImpl<$Res, _$BankAccountImpl>
    implements _$$BankAccountImplCopyWith<$Res> {
  __$$BankAccountImplCopyWithImpl(
      _$BankAccountImpl _value, $Res Function(_$BankAccountImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseInfo = null,
    Object? displayConfig = null,
  }) {
    return _then(_$BankAccountImpl(
      baseInfo: null == baseInfo
          ? _value.baseInfo
          : baseInfo // ignore: cast_nullable_to_non_nullable
              as BankAccountBaseInfo,
      displayConfig: null == displayConfig
          ? _value.displayConfig
          : displayConfig // ignore: cast_nullable_to_non_nullable
              as DisplayConfig,
    ));
  }
}

/// @nodoc

class _$BankAccountImpl implements _BankAccount {
  const _$BankAccountImpl(
      {required this.baseInfo, required this.displayConfig});

  @override
  final BankAccountBaseInfo baseInfo;
  @override
  final DisplayConfig displayConfig;

  @override
  String toString() {
    return 'BankAccount(baseInfo: $baseInfo, displayConfig: $displayConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountImpl &&
            (identical(other.baseInfo, baseInfo) ||
                other.baseInfo == baseInfo) &&
            (identical(other.displayConfig, displayConfig) ||
                other.displayConfig == displayConfig));
  }

  @override
  int get hashCode => Object.hash(runtimeType, baseInfo, displayConfig);

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountImplCopyWith<_$BankAccountImpl> get copyWith =>
      __$$BankAccountImplCopyWithImpl<_$BankAccountImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)
        $default, {
    required TResult Function(BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail, DisplayConfig displayConfig)
        withBalanceDetail,
    required TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)
        withError,
  }) {
    return $default(baseInfo, displayConfig);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult? Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult? Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
  }) {
    return $default?.call(baseInfo, displayConfig);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(baseInfo, displayConfig);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_BankAccount value) $default, {
    required TResult Function(BankAccountWithBalanceDetail value)
        withBalanceDetail,
    required TResult Function(BankAccountWithError value) withError,
  }) {
    return $default(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_BankAccount value)? $default, {
    TResult? Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult? Function(BankAccountWithError value)? withError,
  }) {
    return $default?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_BankAccount value)? $default, {
    TResult Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult Function(BankAccountWithError value)? withError,
    required TResult orElse(),
  }) {
    if ($default != null) {
      return $default(this);
    }
    return orElse();
  }
}

abstract class _BankAccount implements BankAccount {
  const factory _BankAccount(
      {required final BankAccountBaseInfo baseInfo,
      required final DisplayConfig displayConfig}) = _$BankAccountImpl;

  @override
  BankAccountBaseInfo get baseInfo;
  @override
  DisplayConfig get displayConfig;

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountImplCopyWith<_$BankAccountImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountWithBalanceDetailImplCopyWith<$Res>
    implements $BankAccountCopyWith<$Res> {
  factory _$$BankAccountWithBalanceDetailImplCopyWith(
          _$BankAccountWithBalanceDetailImpl value,
          $Res Function(_$BankAccountWithBalanceDetailImpl) then) =
      __$$BankAccountWithBalanceDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BankAccountBaseInfo baseInfo,
      BankAccountBalanceDetail balanceDetail,
      DisplayConfig displayConfig});

  @override
  $BankAccountBaseInfoCopyWith<$Res> get baseInfo;
  $BankAccountBalanceDetailCopyWith<$Res> get balanceDetail;
  @override
  $DisplayConfigCopyWith<$Res> get displayConfig;
}

/// @nodoc
class __$$BankAccountWithBalanceDetailImplCopyWithImpl<$Res>
    extends _$BankAccountCopyWithImpl<$Res, _$BankAccountWithBalanceDetailImpl>
    implements _$$BankAccountWithBalanceDetailImplCopyWith<$Res> {
  __$$BankAccountWithBalanceDetailImplCopyWithImpl(
      _$BankAccountWithBalanceDetailImpl _value,
      $Res Function(_$BankAccountWithBalanceDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseInfo = null,
    Object? balanceDetail = null,
    Object? displayConfig = null,
  }) {
    return _then(_$BankAccountWithBalanceDetailImpl(
      baseInfo: null == baseInfo
          ? _value.baseInfo
          : baseInfo // ignore: cast_nullable_to_non_nullable
              as BankAccountBaseInfo,
      balanceDetail: null == balanceDetail
          ? _value.balanceDetail
          : balanceDetail // ignore: cast_nullable_to_non_nullable
              as BankAccountBalanceDetail,
      displayConfig: null == displayConfig
          ? _value.displayConfig
          : displayConfig // ignore: cast_nullable_to_non_nullable
              as DisplayConfig,
    ));
  }

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BankAccountBalanceDetailCopyWith<$Res> get balanceDetail {
    return $BankAccountBalanceDetailCopyWith<$Res>(_value.balanceDetail,
        (value) {
      return _then(_value.copyWith(balanceDetail: value));
    });
  }
}

/// @nodoc

class _$BankAccountWithBalanceDetailImpl
    implements BankAccountWithBalanceDetail {
  const _$BankAccountWithBalanceDetailImpl(
      {required this.baseInfo,
      required this.balanceDetail,
      required this.displayConfig});

  @override
  final BankAccountBaseInfo baseInfo;
  @override
  final BankAccountBalanceDetail balanceDetail;
  @override
  final DisplayConfig displayConfig;

  @override
  String toString() {
    return 'BankAccount.withBalanceDetail(baseInfo: $baseInfo, balanceDetail: $balanceDetail, displayConfig: $displayConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountWithBalanceDetailImpl &&
            (identical(other.baseInfo, baseInfo) ||
                other.baseInfo == baseInfo) &&
            (identical(other.balanceDetail, balanceDetail) ||
                other.balanceDetail == balanceDetail) &&
            (identical(other.displayConfig, displayConfig) ||
                other.displayConfig == displayConfig));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, baseInfo, balanceDetail, displayConfig);

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountWithBalanceDetailImplCopyWith<
          _$BankAccountWithBalanceDetailImpl>
      get copyWith => __$$BankAccountWithBalanceDetailImplCopyWithImpl<
          _$BankAccountWithBalanceDetailImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)
        $default, {
    required TResult Function(BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail, DisplayConfig displayConfig)
        withBalanceDetail,
    required TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)
        withError,
  }) {
    return withBalanceDetail(baseInfo, balanceDetail, displayConfig);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult? Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult? Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
  }) {
    return withBalanceDetail?.call(baseInfo, balanceDetail, displayConfig);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
    required TResult orElse(),
  }) {
    if (withBalanceDetail != null) {
      return withBalanceDetail(baseInfo, balanceDetail, displayConfig);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_BankAccount value) $default, {
    required TResult Function(BankAccountWithBalanceDetail value)
        withBalanceDetail,
    required TResult Function(BankAccountWithError value) withError,
  }) {
    return withBalanceDetail(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_BankAccount value)? $default, {
    TResult? Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult? Function(BankAccountWithError value)? withError,
  }) {
    return withBalanceDetail?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_BankAccount value)? $default, {
    TResult Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult Function(BankAccountWithError value)? withError,
    required TResult orElse(),
  }) {
    if (withBalanceDetail != null) {
      return withBalanceDetail(this);
    }
    return orElse();
  }
}

abstract class BankAccountWithBalanceDetail implements BankAccount {
  const factory BankAccountWithBalanceDetail(
          {required final BankAccountBaseInfo baseInfo,
          required final BankAccountBalanceDetail balanceDetail,
          required final DisplayConfig displayConfig}) =
      _$BankAccountWithBalanceDetailImpl;

  @override
  BankAccountBaseInfo get baseInfo;
  BankAccountBalanceDetail get balanceDetail;
  @override
  DisplayConfig get displayConfig;

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountWithBalanceDetailImplCopyWith<
          _$BankAccountWithBalanceDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountWithErrorImplCopyWith<$Res>
    implements $BankAccountCopyWith<$Res> {
  factory _$$BankAccountWithErrorImplCopyWith(_$BankAccountWithErrorImpl value,
          $Res Function(_$BankAccountWithErrorImpl) then) =
      __$$BankAccountWithErrorImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {BankAccountBaseInfo baseInfo,
      AppError error,
      DisplayConfig displayConfig});

  @override
  $BankAccountBaseInfoCopyWith<$Res> get baseInfo;
  $AppErrorCopyWith<$Res> get error;
  @override
  $DisplayConfigCopyWith<$Res> get displayConfig;
}

/// @nodoc
class __$$BankAccountWithErrorImplCopyWithImpl<$Res>
    extends _$BankAccountCopyWithImpl<$Res, _$BankAccountWithErrorImpl>
    implements _$$BankAccountWithErrorImplCopyWith<$Res> {
  __$$BankAccountWithErrorImplCopyWithImpl(_$BankAccountWithErrorImpl _value,
      $Res Function(_$BankAccountWithErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseInfo = null,
    Object? error = null,
    Object? displayConfig = null,
  }) {
    return _then(_$BankAccountWithErrorImpl(
      baseInfo: null == baseInfo
          ? _value.baseInfo
          : baseInfo // ignore: cast_nullable_to_non_nullable
              as BankAccountBaseInfo,
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
      displayConfig: null == displayConfig
          ? _value.displayConfig
          : displayConfig // ignore: cast_nullable_to_non_nullable
              as DisplayConfig,
    ));
  }

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$BankAccountWithErrorImpl implements BankAccountWithError {
  const _$BankAccountWithErrorImpl(
      {required this.baseInfo,
      required this.error,
      required this.displayConfig});

  @override
  final BankAccountBaseInfo baseInfo;
  @override
  final AppError error;
  @override
  final DisplayConfig displayConfig;

  @override
  String toString() {
    return 'BankAccount.withError(baseInfo: $baseInfo, error: $error, displayConfig: $displayConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountWithErrorImpl &&
            (identical(other.baseInfo, baseInfo) ||
                other.baseInfo == baseInfo) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.displayConfig, displayConfig) ||
                other.displayConfig == displayConfig));
  }

  @override
  int get hashCode => Object.hash(runtimeType, baseInfo, error, displayConfig);

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountWithErrorImplCopyWith<_$BankAccountWithErrorImpl>
      get copyWith =>
          __$$BankAccountWithErrorImplCopyWithImpl<_$BankAccountWithErrorImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)
        $default, {
    required TResult Function(BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail, DisplayConfig displayConfig)
        withBalanceDetail,
    required TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)
        withError,
  }) {
    return withError(baseInfo, error, displayConfig);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult? Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult? Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
  }) {
    return withError?.call(baseInfo, error, displayConfig);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(BankAccountBaseInfo baseInfo, DisplayConfig displayConfig)?
        $default, {
    TResult Function(
            BankAccountBaseInfo baseInfo,
            BankAccountBalanceDetail balanceDetail,
            DisplayConfig displayConfig)?
        withBalanceDetail,
    TResult Function(BankAccountBaseInfo baseInfo, AppError error,
            DisplayConfig displayConfig)?
        withError,
    required TResult orElse(),
  }) {
    if (withError != null) {
      return withError(baseInfo, error, displayConfig);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_BankAccount value) $default, {
    required TResult Function(BankAccountWithBalanceDetail value)
        withBalanceDetail,
    required TResult Function(BankAccountWithError value) withError,
  }) {
    return withError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_BankAccount value)? $default, {
    TResult? Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult? Function(BankAccountWithError value)? withError,
  }) {
    return withError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_BankAccount value)? $default, {
    TResult Function(BankAccountWithBalanceDetail value)? withBalanceDetail,
    TResult Function(BankAccountWithError value)? withError,
    required TResult orElse(),
  }) {
    if (withError != null) {
      return withError(this);
    }
    return orElse();
  }
}

abstract class BankAccountWithError implements BankAccount {
  const factory BankAccountWithError(
      {required final BankAccountBaseInfo baseInfo,
      required final AppError error,
      required final DisplayConfig displayConfig}) = _$BankAccountWithErrorImpl;

  @override
  BankAccountBaseInfo get baseInfo;
  AppError get error;
  @override
  DisplayConfig get displayConfig;

  /// Create a copy of BankAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountWithErrorImplCopyWith<_$BankAccountWithErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}
