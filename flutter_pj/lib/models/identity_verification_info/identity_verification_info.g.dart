// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'identity_verification_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$IdentityVerificationInfoImpl _$$IdentityVerificationInfoImplFromJson(
        Map<String, dynamic> json) =>
    _$IdentityVerificationInfoImpl(
      referenceNumber: json['referenceNumber'] as String?,
      role: json['role'] as String,
      dataSource: json['dataSource'] as String,
      familyName: json['familyName'] as String?,
      givenName: json['givenName'] as String?,
      familyNameKana: json['familyNameKana'] as String?,
      givenNameKana: json['givenNameKana'] as String?,
      familyNameAlphabetic: json['familyNameAlphabetic'] as String?,
      givenNameAlphabetic: json['givenNameAlphabetic'] as String?,
      hepburnStyle: json['hepburnStyle'] as bool?,
      position: json['position'] as String?,
      birthYear: (json['birthYear'] as num?)?.toInt(),
      birthMonth: (json['birthMonth'] as num?)?.toInt(),
      birthDay: (json['birthDay'] as num?)?.toInt(),
      postCode: json['postCode'] as String?,
      prefecture: json['prefecture'] as String?,
      city: json['city'] as String?,
      sectionNumberAndBuildingName:
          json['sectionNumberAndBuildingName'] as String?,
    );

Map<String, dynamic> _$$IdentityVerificationInfoImplToJson(
        _$IdentityVerificationInfoImpl instance) =>
    <String, dynamic>{
      'referenceNumber': instance.referenceNumber,
      'role': instance.role,
      'dataSource': instance.dataSource,
      'familyName': instance.familyName,
      'givenName': instance.givenName,
      'familyNameKana': instance.familyNameKana,
      'givenNameKana': instance.givenNameKana,
      'familyNameAlphabetic': instance.familyNameAlphabetic,
      'givenNameAlphabetic': instance.givenNameAlphabetic,
      'hepburnStyle': instance.hepburnStyle,
      'position': instance.position,
      'birthYear': instance.birthYear,
      'birthMonth': instance.birthMonth,
      'birthDay': instance.birthDay,
      'postCode': instance.postCode,
      'prefecture': instance.prefecture,
      'city': instance.city,
      'sectionNumberAndBuildingName': instance.sectionNumberAndBuildingName,
    };
