import 'package:freezed_annotation/freezed_annotation.dart';

part 'identity_verification_info.freezed.dart';
part 'identity_verification_info.g.dart';

/// 本人確認情報
@freezed
sealed class IdentityVerificationInfo with _$IdentityVerificationInfo {
  const factory IdentityVerificationInfo({
    /// お手続き番号
    String? referenceNumber,

    /// 代表者:01、代理人:02、実質的支配者:03〜06
    required String role,

    /// 本人確認の方法（JPKIまたはeKYC）
    required String dataSource,

    /// 氏名-姓
    String? familyName,

    /// 氏名-名
    String? givenName,

    /// 氏名-セイ
    String? familyNameKana,

    /// 氏名-メイ
    String? givenNameKana,

    /// 氏名-Last Name
    String? familyNameAlphabetic,

    /// 氏名-First Name
    String? givenNameAlphabetic,

    /// 氏名-ヘボン式編集有無
    bool? hepburnStyle,

    /// 役職名
    String? position,

    /// 生年月日（年）
    int? birthYear,

    /// 生年月日（月）
    int? birthMonth,

    /// 生年月日（日）
    int? birthDay,

    /// 郵便番号
    String? postCode,

    /// 住所-都道府県
    String? prefecture,

    /// 住所-市区町村
    String? city,

    /// 住所-町名・番地・号・建物名
    String? sectionNumberAndBuildingName,
  }) = _IdentityVerificationInfo;

  factory IdentityVerificationInfo.fromJson(Map<String, dynamic> json) =>
      _$IdentityVerificationInfoFromJson(json);
}

extension IdentityVerificationInfoExt on IdentityVerificationInfo {
  /// roleが実質的支配者かどうか
  bool get isBeneficiaryRole {
    return role == 'BENEFICIARY';
  }

  /// roleが代表者かどうか
  bool get isRepresentativeRole {
    return role == 'REPRESENTATIVE';
  }

  /// roleが取引責任者(代理人)かどうか
  bool get isAgentRole {
    return role == 'AGENT';
  }
}

extension IdentityVerificationInfoListExt on List<IdentityVerificationInfo> {
  /// roleが代表者（REPRESENTATIVE）の行が存在しない場合true
  bool get hasNoRepresentative {
    return every((element) => !element.isRepresentativeRole);
  }

  ///　roleが代理人（AGENT）の行が存在しない場合true
  bool get hasNoAgent {
    return every((element) => !element.isAgentRole);
  }

  /// 代表者も代理人も存在しない場合true
  /// どちらか一方でも存在すればfalse
  bool get hasNoRepresentativeAndNoAgent {
    return hasNoRepresentative && hasNoAgent;
  }
}
