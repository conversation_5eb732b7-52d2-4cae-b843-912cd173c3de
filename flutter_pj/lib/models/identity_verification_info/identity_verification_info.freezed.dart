// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identity_verification_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

IdentityVerificationInfo _$IdentityVerificationInfoFromJson(
    Map<String, dynamic> json) {
  return _IdentityVerificationInfo.fromJson(json);
}

/// @nodoc
mixin _$IdentityVerificationInfo {
  /// お手続き番号
  String? get referenceNumber => throw _privateConstructorUsedError;

  /// 代表者:01、代理人:02、実質的支配者:03〜06
  String get role => throw _privateConstructorUsedError;

  /// 本人確認の方法（JPKIまたはeKYC）
  String get dataSource => throw _privateConstructorUsedError;

  /// 氏名-姓
  String? get familyName => throw _privateConstructorUsedError;

  /// 氏名-名
  String? get givenName => throw _privateConstructorUsedError;

  /// 氏名-セイ
  String? get familyNameKana => throw _privateConstructorUsedError;

  /// 氏名-メイ
  String? get givenNameKana => throw _privateConstructorUsedError;

  /// 氏名-Last Name
  String? get familyNameAlphabetic => throw _privateConstructorUsedError;

  /// 氏名-First Name
  String? get givenNameAlphabetic => throw _privateConstructorUsedError;

  /// 氏名-ヘボン式編集有無
  bool? get hepburnStyle => throw _privateConstructorUsedError;

  /// 役職名
  String? get position => throw _privateConstructorUsedError;

  /// 生年月日（年）
  int? get birthYear => throw _privateConstructorUsedError;

  /// 生年月日（月）
  int? get birthMonth => throw _privateConstructorUsedError;

  /// 生年月日（日）
  int? get birthDay => throw _privateConstructorUsedError;

  /// 郵便番号
  String? get postCode => throw _privateConstructorUsedError;

  /// 住所-都道府県
  String? get prefecture => throw _privateConstructorUsedError;

  /// 住所-市区町村
  String? get city => throw _privateConstructorUsedError;

  /// 住所-町名・番地・号・建物名
  String? get sectionNumberAndBuildingName =>
      throw _privateConstructorUsedError;

  /// Serializes this IdentityVerificationInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of IdentityVerificationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityVerificationInfoCopyWith<IdentityVerificationInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityVerificationInfoCopyWith<$Res> {
  factory $IdentityVerificationInfoCopyWith(IdentityVerificationInfo value,
          $Res Function(IdentityVerificationInfo) then) =
      _$IdentityVerificationInfoCopyWithImpl<$Res, IdentityVerificationInfo>;
  @useResult
  $Res call(
      {String? referenceNumber,
      String role,
      String dataSource,
      String? familyName,
      String? givenName,
      String? familyNameKana,
      String? givenNameKana,
      String? familyNameAlphabetic,
      String? givenNameAlphabetic,
      bool? hepburnStyle,
      String? position,
      int? birthYear,
      int? birthMonth,
      int? birthDay,
      String? postCode,
      String? prefecture,
      String? city,
      String? sectionNumberAndBuildingName});
}

/// @nodoc
class _$IdentityVerificationInfoCopyWithImpl<$Res,
        $Val extends IdentityVerificationInfo>
    implements $IdentityVerificationInfoCopyWith<$Res> {
  _$IdentityVerificationInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityVerificationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referenceNumber = freezed,
    Object? role = null,
    Object? dataSource = null,
    Object? familyName = freezed,
    Object? givenName = freezed,
    Object? familyNameKana = freezed,
    Object? givenNameKana = freezed,
    Object? familyNameAlphabetic = freezed,
    Object? givenNameAlphabetic = freezed,
    Object? hepburnStyle = freezed,
    Object? position = freezed,
    Object? birthYear = freezed,
    Object? birthMonth = freezed,
    Object? birthDay = freezed,
    Object? postCode = freezed,
    Object? prefecture = freezed,
    Object? city = freezed,
    Object? sectionNumberAndBuildingName = freezed,
  }) {
    return _then(_value.copyWith(
      referenceNumber: freezed == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      dataSource: null == dataSource
          ? _value.dataSource
          : dataSource // ignore: cast_nullable_to_non_nullable
              as String,
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      givenName: freezed == givenName
          ? _value.givenName
          : givenName // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameKana: freezed == familyNameKana
          ? _value.familyNameKana
          : familyNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameKana: freezed == givenNameKana
          ? _value.givenNameKana
          : givenNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameAlphabetic: freezed == familyNameAlphabetic
          ? _value.familyNameAlphabetic
          : familyNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameAlphabetic: freezed == givenNameAlphabetic
          ? _value.givenNameAlphabetic
          : givenNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      hepburnStyle: freezed == hepburnStyle
          ? _value.hepburnStyle
          : hepburnStyle // ignore: cast_nullable_to_non_nullable
              as bool?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as String?,
      birthYear: freezed == birthYear
          ? _value.birthYear
          : birthYear // ignore: cast_nullable_to_non_nullable
              as int?,
      birthMonth: freezed == birthMonth
          ? _value.birthMonth
          : birthMonth // ignore: cast_nullable_to_non_nullable
              as int?,
      birthDay: freezed == birthDay
          ? _value.birthDay
          : birthDay // ignore: cast_nullable_to_non_nullable
              as int?,
      postCode: freezed == postCode
          ? _value.postCode
          : postCode // ignore: cast_nullable_to_non_nullable
              as String?,
      prefecture: freezed == prefecture
          ? _value.prefecture
          : prefecture // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      sectionNumberAndBuildingName: freezed == sectionNumberAndBuildingName
          ? _value.sectionNumberAndBuildingName
          : sectionNumberAndBuildingName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IdentityVerificationInfoImplCopyWith<$Res>
    implements $IdentityVerificationInfoCopyWith<$Res> {
  factory _$$IdentityVerificationInfoImplCopyWith(
          _$IdentityVerificationInfoImpl value,
          $Res Function(_$IdentityVerificationInfoImpl) then) =
      __$$IdentityVerificationInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? referenceNumber,
      String role,
      String dataSource,
      String? familyName,
      String? givenName,
      String? familyNameKana,
      String? givenNameKana,
      String? familyNameAlphabetic,
      String? givenNameAlphabetic,
      bool? hepburnStyle,
      String? position,
      int? birthYear,
      int? birthMonth,
      int? birthDay,
      String? postCode,
      String? prefecture,
      String? city,
      String? sectionNumberAndBuildingName});
}

/// @nodoc
class __$$IdentityVerificationInfoImplCopyWithImpl<$Res>
    extends _$IdentityVerificationInfoCopyWithImpl<$Res,
        _$IdentityVerificationInfoImpl>
    implements _$$IdentityVerificationInfoImplCopyWith<$Res> {
  __$$IdentityVerificationInfoImplCopyWithImpl(
      _$IdentityVerificationInfoImpl _value,
      $Res Function(_$IdentityVerificationInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityVerificationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referenceNumber = freezed,
    Object? role = null,
    Object? dataSource = null,
    Object? familyName = freezed,
    Object? givenName = freezed,
    Object? familyNameKana = freezed,
    Object? givenNameKana = freezed,
    Object? familyNameAlphabetic = freezed,
    Object? givenNameAlphabetic = freezed,
    Object? hepburnStyle = freezed,
    Object? position = freezed,
    Object? birthYear = freezed,
    Object? birthMonth = freezed,
    Object? birthDay = freezed,
    Object? postCode = freezed,
    Object? prefecture = freezed,
    Object? city = freezed,
    Object? sectionNumberAndBuildingName = freezed,
  }) {
    return _then(_$IdentityVerificationInfoImpl(
      referenceNumber: freezed == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      dataSource: null == dataSource
          ? _value.dataSource
          : dataSource // ignore: cast_nullable_to_non_nullable
              as String,
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      givenName: freezed == givenName
          ? _value.givenName
          : givenName // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameKana: freezed == familyNameKana
          ? _value.familyNameKana
          : familyNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameKana: freezed == givenNameKana
          ? _value.givenNameKana
          : givenNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameAlphabetic: freezed == familyNameAlphabetic
          ? _value.familyNameAlphabetic
          : familyNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameAlphabetic: freezed == givenNameAlphabetic
          ? _value.givenNameAlphabetic
          : givenNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      hepburnStyle: freezed == hepburnStyle
          ? _value.hepburnStyle
          : hepburnStyle // ignore: cast_nullable_to_non_nullable
              as bool?,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as String?,
      birthYear: freezed == birthYear
          ? _value.birthYear
          : birthYear // ignore: cast_nullable_to_non_nullable
              as int?,
      birthMonth: freezed == birthMonth
          ? _value.birthMonth
          : birthMonth // ignore: cast_nullable_to_non_nullable
              as int?,
      birthDay: freezed == birthDay
          ? _value.birthDay
          : birthDay // ignore: cast_nullable_to_non_nullable
              as int?,
      postCode: freezed == postCode
          ? _value.postCode
          : postCode // ignore: cast_nullable_to_non_nullable
              as String?,
      prefecture: freezed == prefecture
          ? _value.prefecture
          : prefecture // ignore: cast_nullable_to_non_nullable
              as String?,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      sectionNumberAndBuildingName: freezed == sectionNumberAndBuildingName
          ? _value.sectionNumberAndBuildingName
          : sectionNumberAndBuildingName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$IdentityVerificationInfoImpl implements _IdentityVerificationInfo {
  const _$IdentityVerificationInfoImpl(
      {this.referenceNumber,
      required this.role,
      required this.dataSource,
      this.familyName,
      this.givenName,
      this.familyNameKana,
      this.givenNameKana,
      this.familyNameAlphabetic,
      this.givenNameAlphabetic,
      this.hepburnStyle,
      this.position,
      this.birthYear,
      this.birthMonth,
      this.birthDay,
      this.postCode,
      this.prefecture,
      this.city,
      this.sectionNumberAndBuildingName});

  factory _$IdentityVerificationInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$IdentityVerificationInfoImplFromJson(json);

  /// お手続き番号
  @override
  final String? referenceNumber;

  /// 代表者:01、代理人:02、実質的支配者:03〜06
  @override
  final String role;

  /// 本人確認の方法（JPKIまたはeKYC）
  @override
  final String dataSource;

  /// 氏名-姓
  @override
  final String? familyName;

  /// 氏名-名
  @override
  final String? givenName;

  /// 氏名-セイ
  @override
  final String? familyNameKana;

  /// 氏名-メイ
  @override
  final String? givenNameKana;

  /// 氏名-Last Name
  @override
  final String? familyNameAlphabetic;

  /// 氏名-First Name
  @override
  final String? givenNameAlphabetic;

  /// 氏名-ヘボン式編集有無
  @override
  final bool? hepburnStyle;

  /// 役職名
  @override
  final String? position;

  /// 生年月日（年）
  @override
  final int? birthYear;

  /// 生年月日（月）
  @override
  final int? birthMonth;

  /// 生年月日（日）
  @override
  final int? birthDay;

  /// 郵便番号
  @override
  final String? postCode;

  /// 住所-都道府県
  @override
  final String? prefecture;

  /// 住所-市区町村
  @override
  final String? city;

  /// 住所-町名・番地・号・建物名
  @override
  final String? sectionNumberAndBuildingName;

  @override
  String toString() {
    return 'IdentityVerificationInfo(referenceNumber: $referenceNumber, role: $role, dataSource: $dataSource, familyName: $familyName, givenName: $givenName, familyNameKana: $familyNameKana, givenNameKana: $givenNameKana, familyNameAlphabetic: $familyNameAlphabetic, givenNameAlphabetic: $givenNameAlphabetic, hepburnStyle: $hepburnStyle, position: $position, birthYear: $birthYear, birthMonth: $birthMonth, birthDay: $birthDay, postCode: $postCode, prefecture: $prefecture, city: $city, sectionNumberAndBuildingName: $sectionNumberAndBuildingName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityVerificationInfoImpl &&
            (identical(other.referenceNumber, referenceNumber) ||
                other.referenceNumber == referenceNumber) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.dataSource, dataSource) ||
                other.dataSource == dataSource) &&
            (identical(other.familyName, familyName) ||
                other.familyName == familyName) &&
            (identical(other.givenName, givenName) ||
                other.givenName == givenName) &&
            (identical(other.familyNameKana, familyNameKana) ||
                other.familyNameKana == familyNameKana) &&
            (identical(other.givenNameKana, givenNameKana) ||
                other.givenNameKana == givenNameKana) &&
            (identical(other.familyNameAlphabetic, familyNameAlphabetic) ||
                other.familyNameAlphabetic == familyNameAlphabetic) &&
            (identical(other.givenNameAlphabetic, givenNameAlphabetic) ||
                other.givenNameAlphabetic == givenNameAlphabetic) &&
            (identical(other.hepburnStyle, hepburnStyle) ||
                other.hepburnStyle == hepburnStyle) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.birthYear, birthYear) ||
                other.birthYear == birthYear) &&
            (identical(other.birthMonth, birthMonth) ||
                other.birthMonth == birthMonth) &&
            (identical(other.birthDay, birthDay) ||
                other.birthDay == birthDay) &&
            (identical(other.postCode, postCode) ||
                other.postCode == postCode) &&
            (identical(other.prefecture, prefecture) ||
                other.prefecture == prefecture) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.sectionNumberAndBuildingName,
                    sectionNumberAndBuildingName) ||
                other.sectionNumberAndBuildingName ==
                    sectionNumberAndBuildingName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      referenceNumber,
      role,
      dataSource,
      familyName,
      givenName,
      familyNameKana,
      givenNameKana,
      familyNameAlphabetic,
      givenNameAlphabetic,
      hepburnStyle,
      position,
      birthYear,
      birthMonth,
      birthDay,
      postCode,
      prefecture,
      city,
      sectionNumberAndBuildingName);

  /// Create a copy of IdentityVerificationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityVerificationInfoImplCopyWith<_$IdentityVerificationInfoImpl>
      get copyWith => __$$IdentityVerificationInfoImplCopyWithImpl<
          _$IdentityVerificationInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$IdentityVerificationInfoImplToJson(
      this,
    );
  }
}

abstract class _IdentityVerificationInfo implements IdentityVerificationInfo {
  const factory _IdentityVerificationInfo(
          {final String? referenceNumber,
          required final String role,
          required final String dataSource,
          final String? familyName,
          final String? givenName,
          final String? familyNameKana,
          final String? givenNameKana,
          final String? familyNameAlphabetic,
          final String? givenNameAlphabetic,
          final bool? hepburnStyle,
          final String? position,
          final int? birthYear,
          final int? birthMonth,
          final int? birthDay,
          final String? postCode,
          final String? prefecture,
          final String? city,
          final String? sectionNumberAndBuildingName}) =
      _$IdentityVerificationInfoImpl;

  factory _IdentityVerificationInfo.fromJson(Map<String, dynamic> json) =
      _$IdentityVerificationInfoImpl.fromJson;

  /// お手続き番号
  @override
  String? get referenceNumber;

  /// 代表者:01、代理人:02、実質的支配者:03〜06
  @override
  String get role;

  /// 本人確認の方法（JPKIまたはeKYC）
  @override
  String get dataSource;

  /// 氏名-姓
  @override
  String? get familyName;

  /// 氏名-名
  @override
  String? get givenName;

  /// 氏名-セイ
  @override
  String? get familyNameKana;

  /// 氏名-メイ
  @override
  String? get givenNameKana;

  /// 氏名-Last Name
  @override
  String? get familyNameAlphabetic;

  /// 氏名-First Name
  @override
  String? get givenNameAlphabetic;

  /// 氏名-ヘボン式編集有無
  @override
  bool? get hepburnStyle;

  /// 役職名
  @override
  String? get position;

  /// 生年月日（年）
  @override
  int? get birthYear;

  /// 生年月日（月）
  @override
  int? get birthMonth;

  /// 生年月日（日）
  @override
  int? get birthDay;

  /// 郵便番号
  @override
  String? get postCode;

  /// 住所-都道府県
  @override
  String? get prefecture;

  /// 住所-市区町村
  @override
  String? get city;

  /// 住所-町名・番地・号・建物名
  @override
  String? get sectionNumberAndBuildingName;

  /// Create a copy of IdentityVerificationInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityVerificationInfoImplCopyWith<_$IdentityVerificationInfoImpl>
      get copyWith => throw _privateConstructorUsedError;
}
