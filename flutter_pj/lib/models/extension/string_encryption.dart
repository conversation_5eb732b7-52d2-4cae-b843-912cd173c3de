import 'dart:convert';
import 'package:crypto/crypto.dart';

/// String型の暗号化にまつわる拡張メソッドを定義する
extension StringEncryption on String {
  /// SHA-256でハッシュ化された値を取得する
  /// ハッシュ化する際にはソルトを追加する
  /// 引数のlimitに値が設定されている場合、その桁数まで文字列を切り詰める
  /// 空文字の場合はハッシュ化しない
  String convertToSHA256Hash([int? limit]) {
    if (isEmpty) {
      return this;
    }

    const salt = 'DigitalTP';
    final bytes = utf8.encode(this + salt);
    final digest = sha256.convert(bytes).toString();
    if (limit != null && digest.length > limit) {
      return digest.substring(0, limit);
    } else {
      return digest;
    }
  }
}
