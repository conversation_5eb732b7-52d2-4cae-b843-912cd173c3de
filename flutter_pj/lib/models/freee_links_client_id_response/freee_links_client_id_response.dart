import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_links_client_id_response.freezed.dart';

part 'freee_links_client_id_response.g.dart';

/// 取引先ID(clientId)に紐づくデータがfreee_linksテーブルに存在するか確認するクラス
@freezed
class FreeeLinksClientIdResponse with _$FreeeLinksClientIdResponse {
  const factory FreeeLinksClientIdResponse({
    /// 取引先ID(clientID)に紐づくデータ有無フラグ
    required bool exists,
  }) = _FreeeLinksClientIdResponse;

  factory FreeeLinksClientIdResponse.fromJson(Map<String, dynamic> json) =>
      _$FreeeLinksClientIdResponseFromJson(json);
}
