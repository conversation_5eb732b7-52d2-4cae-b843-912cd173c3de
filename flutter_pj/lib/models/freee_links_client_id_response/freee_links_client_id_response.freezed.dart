// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_links_client_id_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FreeeLinksClientIdResponse _$FreeeLinksClientIdResponseFromJson(
    Map<String, dynamic> json) {
  return _FreeeLinksClientIdResponse.fromJson(json);
}

/// @nodoc
mixin _$FreeeLinksClientIdResponse {
  /// 取引先ID(clientID)に紐づくデータ有無フラグ
  bool get exists => throw _privateConstructorUsedError;

  /// Serializes this FreeeLinksClientIdResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeLinksClientIdResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeLinksClientIdResponseCopyWith<FreeeLinksClientIdResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeLinksClientIdResponseCopyWith<$Res> {
  factory $FreeeLinksClientIdResponseCopyWith(FreeeLinksClientIdResponse value,
          $Res Function(FreeeLinksClientIdResponse) then) =
      _$FreeeLinksClientIdResponseCopyWithImpl<$Res,
          FreeeLinksClientIdResponse>;
  @useResult
  $Res call({bool exists});
}

/// @nodoc
class _$FreeeLinksClientIdResponseCopyWithImpl<$Res,
        $Val extends FreeeLinksClientIdResponse>
    implements $FreeeLinksClientIdResponseCopyWith<$Res> {
  _$FreeeLinksClientIdResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeLinksClientIdResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exists = null,
  }) {
    return _then(_value.copyWith(
      exists: null == exists
          ? _value.exists
          : exists // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeLinksClientIdResponseImplCopyWith<$Res>
    implements $FreeeLinksClientIdResponseCopyWith<$Res> {
  factory _$$FreeeLinksClientIdResponseImplCopyWith(
          _$FreeeLinksClientIdResponseImpl value,
          $Res Function(_$FreeeLinksClientIdResponseImpl) then) =
      __$$FreeeLinksClientIdResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool exists});
}

/// @nodoc
class __$$FreeeLinksClientIdResponseImplCopyWithImpl<$Res>
    extends _$FreeeLinksClientIdResponseCopyWithImpl<$Res,
        _$FreeeLinksClientIdResponseImpl>
    implements _$$FreeeLinksClientIdResponseImplCopyWith<$Res> {
  __$$FreeeLinksClientIdResponseImplCopyWithImpl(
      _$FreeeLinksClientIdResponseImpl _value,
      $Res Function(_$FreeeLinksClientIdResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeLinksClientIdResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? exists = null,
  }) {
    return _then(_$FreeeLinksClientIdResponseImpl(
      exists: null == exists
          ? _value.exists
          : exists // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeLinksClientIdResponseImpl implements _FreeeLinksClientIdResponse {
  const _$FreeeLinksClientIdResponseImpl({required this.exists});

  factory _$FreeeLinksClientIdResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$FreeeLinksClientIdResponseImplFromJson(json);

  /// 取引先ID(clientID)に紐づくデータ有無フラグ
  @override
  final bool exists;

  @override
  String toString() {
    return 'FreeeLinksClientIdResponse(exists: $exists)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeLinksClientIdResponseImpl &&
            (identical(other.exists, exists) || other.exists == exists));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, exists);

  /// Create a copy of FreeeLinksClientIdResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeLinksClientIdResponseImplCopyWith<_$FreeeLinksClientIdResponseImpl>
      get copyWith => __$$FreeeLinksClientIdResponseImplCopyWithImpl<
          _$FreeeLinksClientIdResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeLinksClientIdResponseImplToJson(
      this,
    );
  }
}

abstract class _FreeeLinksClientIdResponse
    implements FreeeLinksClientIdResponse {
  const factory _FreeeLinksClientIdResponse({required final bool exists}) =
      _$FreeeLinksClientIdResponseImpl;

  factory _FreeeLinksClientIdResponse.fromJson(Map<String, dynamic> json) =
      _$FreeeLinksClientIdResponseImpl.fromJson;

  /// 取引先ID(clientID)に紐づくデータ有無フラグ
  @override
  bool get exists;

  /// Create a copy of FreeeLinksClientIdResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeLinksClientIdResponseImplCopyWith<_$FreeeLinksClientIdResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
