import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_settings.freezed.dart';
part 'account_settings.g.dart';

@freezed
class AccountSettings with _$AccountSettings {
  const factory AccountSettings({
    required List<DisplayConfigResponse> accounts,
  }) = _AccountSettings;

  factory AccountSettings.fromJson(Map<String, Object?> json) =>
      _$AccountSettingsFromJson(json);
}

/// 口座表示設定（口座表示設定取得APIのレスポンス用）
@freezed
class DisplayConfigResponse with _$DisplayConfigResponse {
  const factory DisplayConfigResponse({
    required String accountId,

    /// 口座表示名
    @Default('') String displayName,

    /// 口座残高カード表示フラグ
    /// true:非表示, false:表示
    required bool isHidden,

    /// 口座種別
    /// [ 'web21', 'freee' ]
    required String accountApiType,
  }) = _DisplayConfigResponse;

  factory DisplayConfigResponse.fromJson(Map<String, Object?> json) =>
      _$DisplayConfigResponseFromJson(json);
}
