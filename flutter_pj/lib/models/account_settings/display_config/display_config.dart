import 'package:freezed_annotation/freezed_annotation.dart';

part 'display_config.freezed.dart';

/// 口座表示設定（indexをフロント側で付与）
@freezed
class DisplayConfig with _$DisplayConfig {
  const factory DisplayConfig({
    required String accountId,

    /// 一意の番号。並び順に使用
    required int displayOrder,

    /// 口座表示名
    @Default('') String displayName,

    /// 口座残高カード表示フラグ
    /// true:非表示, false:表示
    required bool isHidden,

    /// 口座種別
    /// [ 'web21', 'freee' ]
    required AccountApiType accountApiType,
  }) = _DisplayConfig;
}

extension DisplayConfigList on List<DisplayConfig> {
  /// 口座表示設定がオンになっている設定のみを返す
  List<DisplayConfig> getDisplayConfigOn() {
    return where((element) => !element.isHidden).toList();
  }
}

enum AccountApiType {
  web21,
  freee,
}

extension AccountApiTypeExt on AccountApiType {
  bool get isWeb21 => this == AccountApiType.web21;

  bool get isFreee => this == AccountApiType.freee;
}
