// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'display_config.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DisplayConfig {
  String get accountId => throw _privateConstructorUsedError;

  /// 一意の番号。並び順に使用
  int get displayOrder => throw _privateConstructorUsedError;

  /// 口座表示名
  String get displayName => throw _privateConstructorUsedError;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  bool get isHidden => throw _privateConstructorUsedError;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  AccountApiType get accountApiType => throw _privateConstructorUsedError;

  /// Create a copy of DisplayConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DisplayConfigCopyWith<DisplayConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DisplayConfigCopyWith<$Res> {
  factory $DisplayConfigCopyWith(
          DisplayConfig value, $Res Function(DisplayConfig) then) =
      _$DisplayConfigCopyWithImpl<$Res, DisplayConfig>;
  @useResult
  $Res call(
      {String accountId,
      int displayOrder,
      String displayName,
      bool isHidden,
      AccountApiType accountApiType});
}

/// @nodoc
class _$DisplayConfigCopyWithImpl<$Res, $Val extends DisplayConfig>
    implements $DisplayConfigCopyWith<$Res> {
  _$DisplayConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DisplayConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? displayOrder = null,
    Object? displayName = null,
    Object? isHidden = null,
    Object? accountApiType = null,
  }) {
    return _then(_value.copyWith(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as AccountApiType,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DisplayConfigImplCopyWith<$Res>
    implements $DisplayConfigCopyWith<$Res> {
  factory _$$DisplayConfigImplCopyWith(
          _$DisplayConfigImpl value, $Res Function(_$DisplayConfigImpl) then) =
      __$$DisplayConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accountId,
      int displayOrder,
      String displayName,
      bool isHidden,
      AccountApiType accountApiType});
}

/// @nodoc
class __$$DisplayConfigImplCopyWithImpl<$Res>
    extends _$DisplayConfigCopyWithImpl<$Res, _$DisplayConfigImpl>
    implements _$$DisplayConfigImplCopyWith<$Res> {
  __$$DisplayConfigImplCopyWithImpl(
      _$DisplayConfigImpl _value, $Res Function(_$DisplayConfigImpl) _then)
      : super(_value, _then);

  /// Create a copy of DisplayConfig
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? displayOrder = null,
    Object? displayName = null,
    Object? isHidden = null,
    Object? accountApiType = null,
  }) {
    return _then(_$DisplayConfigImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      displayOrder: null == displayOrder
          ? _value.displayOrder
          : displayOrder // ignore: cast_nullable_to_non_nullable
              as int,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as AccountApiType,
    ));
  }
}

/// @nodoc

class _$DisplayConfigImpl implements _DisplayConfig {
  const _$DisplayConfigImpl(
      {required this.accountId,
      required this.displayOrder,
      this.displayName = '',
      required this.isHidden,
      required this.accountApiType});

  @override
  final String accountId;

  /// 一意の番号。並び順に使用
  @override
  final int displayOrder;

  /// 口座表示名
  @override
  @JsonKey()
  final String displayName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  final bool isHidden;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  final AccountApiType accountApiType;

  @override
  String toString() {
    return 'DisplayConfig(accountId: $accountId, displayOrder: $displayOrder, displayName: $displayName, isHidden: $isHidden, accountApiType: $accountApiType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisplayConfigImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.displayOrder, displayOrder) ||
                other.displayOrder == displayOrder) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.accountApiType, accountApiType) ||
                other.accountApiType == accountApiType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountId, displayOrder,
      displayName, isHidden, accountApiType);

  /// Create a copy of DisplayConfig
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisplayConfigImplCopyWith<_$DisplayConfigImpl> get copyWith =>
      __$$DisplayConfigImplCopyWithImpl<_$DisplayConfigImpl>(this, _$identity);
}

abstract class _DisplayConfig implements DisplayConfig {
  const factory _DisplayConfig(
      {required final String accountId,
      required final int displayOrder,
      final String displayName,
      required final bool isHidden,
      required final AccountApiType accountApiType}) = _$DisplayConfigImpl;

  @override
  String get accountId;

  /// 一意の番号。並び順に使用
  @override
  int get displayOrder;

  /// 口座表示名
  @override
  String get displayName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  bool get isHidden;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  AccountApiType get accountApiType;

  /// Create a copy of DisplayConfig
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisplayConfigImplCopyWith<_$DisplayConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
