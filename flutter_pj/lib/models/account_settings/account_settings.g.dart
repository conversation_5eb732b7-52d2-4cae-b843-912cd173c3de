// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountSettingsImpl _$$AccountSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$AccountSettingsImpl(
      accounts: (json['accounts'] as List<dynamic>)
          .map((e) => DisplayConfigResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AccountSettingsImplToJson(
        _$AccountSettingsImpl instance) =>
    <String, dynamic>{
      'accounts': instance.accounts,
    };

_$DisplayConfigResponseImpl _$$DisplayConfigResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$DisplayConfigResponseImpl(
      accountId: json['accountId'] as String,
      displayName: json['displayName'] as String? ?? '',
      isHidden: json['isHidden'] as bool,
      accountApiType: json['accountApiType'] as String,
    );

Map<String, dynamic> _$$DisplayConfigResponseImplToJson(
        _$DisplayConfigResponseImpl instance) =>
    <String, dynamic>{
      'accountId': instance.accountId,
      'displayName': instance.displayName,
      'isHidden': instance.isHidden,
      'accountApiType': instance.accountApiType,
    };
