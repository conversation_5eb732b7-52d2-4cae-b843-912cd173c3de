// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountSettings _$AccountSettingsFromJson(Map<String, dynamic> json) {
  return _AccountSettings.fromJson(json);
}

/// @nodoc
mixin _$AccountSettings {
  List<DisplayConfigResponse> get accounts =>
      throw _privateConstructorUsedError;

  /// Serializes this AccountSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountSettingsCopyWith<AccountSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountSettingsCopyWith<$Res> {
  factory $AccountSettingsCopyWith(
          AccountSettings value, $Res Function(AccountSettings) then) =
      _$AccountSettingsCopyWithImpl<$Res, AccountSettings>;
  @useResult
  $Res call({List<DisplayConfigResponse> accounts});
}

/// @nodoc
class _$AccountSettingsCopyWithImpl<$Res, $Val extends AccountSettings>
    implements $AccountSettingsCopyWith<$Res> {
  _$AccountSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accounts = null,
  }) {
    return _then(_value.copyWith(
      accounts: null == accounts
          ? _value.accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<DisplayConfigResponse>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountSettingsImplCopyWith<$Res>
    implements $AccountSettingsCopyWith<$Res> {
  factory _$$AccountSettingsImplCopyWith(_$AccountSettingsImpl value,
          $Res Function(_$AccountSettingsImpl) then) =
      __$$AccountSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<DisplayConfigResponse> accounts});
}

/// @nodoc
class __$$AccountSettingsImplCopyWithImpl<$Res>
    extends _$AccountSettingsCopyWithImpl<$Res, _$AccountSettingsImpl>
    implements _$$AccountSettingsImplCopyWith<$Res> {
  __$$AccountSettingsImplCopyWithImpl(
      _$AccountSettingsImpl _value, $Res Function(_$AccountSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accounts = null,
  }) {
    return _then(_$AccountSettingsImpl(
      accounts: null == accounts
          ? _value._accounts
          : accounts // ignore: cast_nullable_to_non_nullable
              as List<DisplayConfigResponse>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountSettingsImpl implements _AccountSettings {
  const _$AccountSettingsImpl(
      {required final List<DisplayConfigResponse> accounts})
      : _accounts = accounts;

  factory _$AccountSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountSettingsImplFromJson(json);

  final List<DisplayConfigResponse> _accounts;
  @override
  List<DisplayConfigResponse> get accounts {
    if (_accounts is EqualUnmodifiableListView) return _accounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_accounts);
  }

  @override
  String toString() {
    return 'AccountSettings(accounts: $accounts)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountSettingsImpl &&
            const DeepCollectionEquality().equals(other._accounts, _accounts));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_accounts));

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountSettingsImplCopyWith<_$AccountSettingsImpl> get copyWith =>
      __$$AccountSettingsImplCopyWithImpl<_$AccountSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountSettingsImplToJson(
      this,
    );
  }
}

abstract class _AccountSettings implements AccountSettings {
  const factory _AccountSettings(
          {required final List<DisplayConfigResponse> accounts}) =
      _$AccountSettingsImpl;

  factory _AccountSettings.fromJson(Map<String, dynamic> json) =
      _$AccountSettingsImpl.fromJson;

  @override
  List<DisplayConfigResponse> get accounts;

  /// Create a copy of AccountSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountSettingsImplCopyWith<_$AccountSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

DisplayConfigResponse _$DisplayConfigResponseFromJson(
    Map<String, dynamic> json) {
  return _DisplayConfigResponse.fromJson(json);
}

/// @nodoc
mixin _$DisplayConfigResponse {
  String get accountId => throw _privateConstructorUsedError;

  /// 口座表示名
  String get displayName => throw _privateConstructorUsedError;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  bool get isHidden => throw _privateConstructorUsedError;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  String get accountApiType => throw _privateConstructorUsedError;

  /// Serializes this DisplayConfigResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DisplayConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DisplayConfigResponseCopyWith<DisplayConfigResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DisplayConfigResponseCopyWith<$Res> {
  factory $DisplayConfigResponseCopyWith(DisplayConfigResponse value,
          $Res Function(DisplayConfigResponse) then) =
      _$DisplayConfigResponseCopyWithImpl<$Res, DisplayConfigResponse>;
  @useResult
  $Res call(
      {String accountId,
      String displayName,
      bool isHidden,
      String accountApiType});
}

/// @nodoc
class _$DisplayConfigResponseCopyWithImpl<$Res,
        $Val extends DisplayConfigResponse>
    implements $DisplayConfigResponseCopyWith<$Res> {
  _$DisplayConfigResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DisplayConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? displayName = null,
    Object? isHidden = null,
    Object? accountApiType = null,
  }) {
    return _then(_value.copyWith(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DisplayConfigResponseImplCopyWith<$Res>
    implements $DisplayConfigResponseCopyWith<$Res> {
  factory _$$DisplayConfigResponseImplCopyWith(
          _$DisplayConfigResponseImpl value,
          $Res Function(_$DisplayConfigResponseImpl) then) =
      __$$DisplayConfigResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String accountId,
      String displayName,
      bool isHidden,
      String accountApiType});
}

/// @nodoc
class __$$DisplayConfigResponseImplCopyWithImpl<$Res>
    extends _$DisplayConfigResponseCopyWithImpl<$Res,
        _$DisplayConfigResponseImpl>
    implements _$$DisplayConfigResponseImplCopyWith<$Res> {
  __$$DisplayConfigResponseImplCopyWithImpl(_$DisplayConfigResponseImpl _value,
      $Res Function(_$DisplayConfigResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of DisplayConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountId = null,
    Object? displayName = null,
    Object? isHidden = null,
    Object? accountApiType = null,
  }) {
    return _then(_$DisplayConfigResponseImpl(
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      displayName: null == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String,
      isHidden: null == isHidden
          ? _value.isHidden
          : isHidden // ignore: cast_nullable_to_non_nullable
              as bool,
      accountApiType: null == accountApiType
          ? _value.accountApiType
          : accountApiType // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DisplayConfigResponseImpl implements _DisplayConfigResponse {
  const _$DisplayConfigResponseImpl(
      {required this.accountId,
      this.displayName = '',
      required this.isHidden,
      required this.accountApiType});

  factory _$DisplayConfigResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$DisplayConfigResponseImplFromJson(json);

  @override
  final String accountId;

  /// 口座表示名
  @override
  @JsonKey()
  final String displayName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  final bool isHidden;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  final String accountApiType;

  @override
  String toString() {
    return 'DisplayConfigResponse(accountId: $accountId, displayName: $displayName, isHidden: $isHidden, accountApiType: $accountApiType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DisplayConfigResponseImpl &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.isHidden, isHidden) ||
                other.isHidden == isHidden) &&
            (identical(other.accountApiType, accountApiType) ||
                other.accountApiType == accountApiType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, accountId, displayName, isHidden, accountApiType);

  /// Create a copy of DisplayConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DisplayConfigResponseImplCopyWith<_$DisplayConfigResponseImpl>
      get copyWith => __$$DisplayConfigResponseImplCopyWithImpl<
          _$DisplayConfigResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DisplayConfigResponseImplToJson(
      this,
    );
  }
}

abstract class _DisplayConfigResponse implements DisplayConfigResponse {
  const factory _DisplayConfigResponse(
      {required final String accountId,
      final String displayName,
      required final bool isHidden,
      required final String accountApiType}) = _$DisplayConfigResponseImpl;

  factory _DisplayConfigResponse.fromJson(Map<String, dynamic> json) =
      _$DisplayConfigResponseImpl.fromJson;

  @override
  String get accountId;

  /// 口座表示名
  @override
  String get displayName;

  /// 口座残高カード表示フラグ
  /// true:非表示, false:表示
  @override
  bool get isHidden;

  /// 口座種別
  /// [ 'web21', 'freee' ]
  @override
  String get accountApiType;

  /// Create a copy of DisplayConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DisplayConfigResponseImplCopyWith<_$DisplayConfigResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
