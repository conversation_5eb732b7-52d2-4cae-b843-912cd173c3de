// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_transaction_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AccountTransactionResponse _$AccountTransactionResponseFromJson(
    Map<String, dynamic> json) {
  return _AccountTransactionResponse.fromJson(json);
}

/// @nodoc
mixin _$AccountTransactionResponse {
  /// 基準日
  String? get baseDate => throw _privateConstructorUsedError;

  /// 基準時刻
  String? get baseTime => throw _privateConstructorUsedError;

  /// 預金種目コード
  String? get accountTypeCode => throw _privateConstructorUsedError;

  /// 入金額合計
  int? get totalDepositAmount => throw _privateConstructorUsedError;

  /// 出金額合計
  int? get totalWithdrawalAmount => throw _privateConstructorUsedError;

  /// 取引データ件数
  int? get transactionDataCount => throw _privateConstructorUsedError;

  /// 流動制預金の取引明細
  List<AccountTransactionDetail> get transactions1 =>
      throw _privateConstructorUsedError;

  /// 固定制預金の取引明細
  List<AccountTransactionDetailFixed> get transactions2 =>
      throw _privateConstructorUsedError;

  /// Serializes this AccountTransactionResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountTransactionResponseCopyWith<AccountTransactionResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTransactionResponseCopyWith<$Res> {
  factory $AccountTransactionResponseCopyWith(AccountTransactionResponse value,
          $Res Function(AccountTransactionResponse) then) =
      _$AccountTransactionResponseCopyWithImpl<$Res,
          AccountTransactionResponse>;
  @useResult
  $Res call(
      {String? baseDate,
      String? baseTime,
      String? accountTypeCode,
      int? totalDepositAmount,
      int? totalWithdrawalAmount,
      int? transactionDataCount,
      List<AccountTransactionDetail> transactions1,
      List<AccountTransactionDetailFixed> transactions2});
}

/// @nodoc
class _$AccountTransactionResponseCopyWithImpl<$Res,
        $Val extends AccountTransactionResponse>
    implements $AccountTransactionResponseCopyWith<$Res> {
  _$AccountTransactionResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseDate = freezed,
    Object? baseTime = freezed,
    Object? accountTypeCode = freezed,
    Object? totalDepositAmount = freezed,
    Object? totalWithdrawalAmount = freezed,
    Object? transactionDataCount = freezed,
    Object? transactions1 = null,
    Object? transactions2 = null,
  }) {
    return _then(_value.copyWith(
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      accountTypeCode: freezed == accountTypeCode
          ? _value.accountTypeCode
          : accountTypeCode // ignore: cast_nullable_to_non_nullable
              as String?,
      totalDepositAmount: freezed == totalDepositAmount
          ? _value.totalDepositAmount
          : totalDepositAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalWithdrawalAmount: freezed == totalWithdrawalAmount
          ? _value.totalWithdrawalAmount
          : totalWithdrawalAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      transactionDataCount: freezed == transactionDataCount
          ? _value.transactionDataCount
          : transactionDataCount // ignore: cast_nullable_to_non_nullable
              as int?,
      transactions1: null == transactions1
          ? _value.transactions1
          : transactions1 // ignore: cast_nullable_to_non_nullable
              as List<AccountTransactionDetail>,
      transactions2: null == transactions2
          ? _value.transactions2
          : transactions2 // ignore: cast_nullable_to_non_nullable
              as List<AccountTransactionDetailFixed>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountTransactionResponseImplCopyWith<$Res>
    implements $AccountTransactionResponseCopyWith<$Res> {
  factory _$$AccountTransactionResponseImplCopyWith(
          _$AccountTransactionResponseImpl value,
          $Res Function(_$AccountTransactionResponseImpl) then) =
      __$$AccountTransactionResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? baseDate,
      String? baseTime,
      String? accountTypeCode,
      int? totalDepositAmount,
      int? totalWithdrawalAmount,
      int? transactionDataCount,
      List<AccountTransactionDetail> transactions1,
      List<AccountTransactionDetailFixed> transactions2});
}

/// @nodoc
class __$$AccountTransactionResponseImplCopyWithImpl<$Res>
    extends _$AccountTransactionResponseCopyWithImpl<$Res,
        _$AccountTransactionResponseImpl>
    implements _$$AccountTransactionResponseImplCopyWith<$Res> {
  __$$AccountTransactionResponseImplCopyWithImpl(
      _$AccountTransactionResponseImpl _value,
      $Res Function(_$AccountTransactionResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? baseDate = freezed,
    Object? baseTime = freezed,
    Object? accountTypeCode = freezed,
    Object? totalDepositAmount = freezed,
    Object? totalWithdrawalAmount = freezed,
    Object? transactionDataCount = freezed,
    Object? transactions1 = null,
    Object? transactions2 = null,
  }) {
    return _then(_$AccountTransactionResponseImpl(
      baseDate: freezed == baseDate
          ? _value.baseDate
          : baseDate // ignore: cast_nullable_to_non_nullable
              as String?,
      baseTime: freezed == baseTime
          ? _value.baseTime
          : baseTime // ignore: cast_nullable_to_non_nullable
              as String?,
      accountTypeCode: freezed == accountTypeCode
          ? _value.accountTypeCode
          : accountTypeCode // ignore: cast_nullable_to_non_nullable
              as String?,
      totalDepositAmount: freezed == totalDepositAmount
          ? _value.totalDepositAmount
          : totalDepositAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      totalWithdrawalAmount: freezed == totalWithdrawalAmount
          ? _value.totalWithdrawalAmount
          : totalWithdrawalAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      transactionDataCount: freezed == transactionDataCount
          ? _value.transactionDataCount
          : transactionDataCount // ignore: cast_nullable_to_non_nullable
              as int?,
      transactions1: null == transactions1
          ? _value._transactions1
          : transactions1 // ignore: cast_nullable_to_non_nullable
              as List<AccountTransactionDetail>,
      transactions2: null == transactions2
          ? _value._transactions2
          : transactions2 // ignore: cast_nullable_to_non_nullable
              as List<AccountTransactionDetailFixed>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountTransactionResponseImpl implements _AccountTransactionResponse {
  const _$AccountTransactionResponseImpl(
      {this.baseDate,
      this.baseTime,
      this.accountTypeCode,
      this.totalDepositAmount,
      this.totalWithdrawalAmount,
      this.transactionDataCount,
      required final List<AccountTransactionDetail> transactions1,
      required final List<AccountTransactionDetailFixed> transactions2})
      : _transactions1 = transactions1,
        _transactions2 = transactions2;

  factory _$AccountTransactionResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AccountTransactionResponseImplFromJson(json);

  /// 基準日
  @override
  final String? baseDate;

  /// 基準時刻
  @override
  final String? baseTime;

  /// 預金種目コード
  @override
  final String? accountTypeCode;

  /// 入金額合計
  @override
  final int? totalDepositAmount;

  /// 出金額合計
  @override
  final int? totalWithdrawalAmount;

  /// 取引データ件数
  @override
  final int? transactionDataCount;

  /// 流動制預金の取引明細
  final List<AccountTransactionDetail> _transactions1;

  /// 流動制預金の取引明細
  @override
  List<AccountTransactionDetail> get transactions1 {
    if (_transactions1 is EqualUnmodifiableListView) return _transactions1;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions1);
  }

  /// 固定制預金の取引明細
  final List<AccountTransactionDetailFixed> _transactions2;

  /// 固定制預金の取引明細
  @override
  List<AccountTransactionDetailFixed> get transactions2 {
    if (_transactions2 is EqualUnmodifiableListView) return _transactions2;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactions2);
  }

  @override
  String toString() {
    return 'AccountTransactionResponse(baseDate: $baseDate, baseTime: $baseTime, accountTypeCode: $accountTypeCode, totalDepositAmount: $totalDepositAmount, totalWithdrawalAmount: $totalWithdrawalAmount, transactionDataCount: $transactionDataCount, transactions1: $transactions1, transactions2: $transactions2)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionResponseImpl &&
            (identical(other.baseDate, baseDate) ||
                other.baseDate == baseDate) &&
            (identical(other.baseTime, baseTime) ||
                other.baseTime == baseTime) &&
            (identical(other.accountTypeCode, accountTypeCode) ||
                other.accountTypeCode == accountTypeCode) &&
            (identical(other.totalDepositAmount, totalDepositAmount) ||
                other.totalDepositAmount == totalDepositAmount) &&
            (identical(other.totalWithdrawalAmount, totalWithdrawalAmount) ||
                other.totalWithdrawalAmount == totalWithdrawalAmount) &&
            (identical(other.transactionDataCount, transactionDataCount) ||
                other.transactionDataCount == transactionDataCount) &&
            const DeepCollectionEquality()
                .equals(other._transactions1, _transactions1) &&
            const DeepCollectionEquality()
                .equals(other._transactions2, _transactions2));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      baseDate,
      baseTime,
      accountTypeCode,
      totalDepositAmount,
      totalWithdrawalAmount,
      transactionDataCount,
      const DeepCollectionEquality().hash(_transactions1),
      const DeepCollectionEquality().hash(_transactions2));

  /// Create a copy of AccountTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTransactionResponseImplCopyWith<_$AccountTransactionResponseImpl>
      get copyWith => __$$AccountTransactionResponseImplCopyWithImpl<
          _$AccountTransactionResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountTransactionResponseImplToJson(
      this,
    );
  }
}

abstract class _AccountTransactionResponse
    implements AccountTransactionResponse {
  const factory _AccountTransactionResponse(
          {final String? baseDate,
          final String? baseTime,
          final String? accountTypeCode,
          final int? totalDepositAmount,
          final int? totalWithdrawalAmount,
          final int? transactionDataCount,
          required final List<AccountTransactionDetail> transactions1,
          required final List<AccountTransactionDetailFixed> transactions2}) =
      _$AccountTransactionResponseImpl;

  factory _AccountTransactionResponse.fromJson(Map<String, dynamic> json) =
      _$AccountTransactionResponseImpl.fromJson;

  /// 基準日
  @override
  String? get baseDate;

  /// 基準時刻
  @override
  String? get baseTime;

  /// 預金種目コード
  @override
  String? get accountTypeCode;

  /// 入金額合計
  @override
  int? get totalDepositAmount;

  /// 出金額合計
  @override
  int? get totalWithdrawalAmount;

  /// 取引データ件数
  @override
  int? get transactionDataCount;

  /// 流動制預金の取引明細
  @override
  List<AccountTransactionDetail> get transactions1;

  /// 固定制預金の取引明細
  @override
  List<AccountTransactionDetailFixed> get transactions2;

  /// Create a copy of AccountTransactionResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTransactionResponseImplCopyWith<_$AccountTransactionResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AccountTransactionDetail _$AccountTransactionDetailFromJson(
    Map<String, dynamic> json) {
  return _AccountTransactionDetail.fromJson(json);
}

/// @nodoc
mixin _$AccountTransactionDetail {
  /// 照会番号
  String? get inquiryNumber => throw _privateConstructorUsedError;

  /// 取引日（西暦）
  String? get transactionDateAd => throw _privateConstructorUsedError;

  /// 起算日（西暦）
  String? get valueDateAd => throw _privateConstructorUsedError;

  /// 入払区分
  /// 1：入金
  /// 2：出金
  String? get depositCreditType => throw _privateConstructorUsedError;

  /// 取引区分
  /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
  String? get transactionType => throw _privateConstructorUsedError;

  /// 取引区分名
  /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
  String? get transactionTypeName => throw _privateConstructorUsedError;

  /// 金額
  int? get amount => throw _privateConstructorUsedError;

  /// 振込依頼人名または契約者番号
  String? get remitterNameContractorNumber =>
      throw _privateConstructorUsedError;

  /// 依頼人コード
  String? get remitterCode => throw _privateConstructorUsedError;

  /// 内他店手形
  int? get checksIssuedByOtherBanksAmount => throw _privateConstructorUsedError;

  /// 手形・小切手区分名
  /// [ 小切手, 約束手形, 為替手形 ]
  String? get billAndCheckTypeName => throw _privateConstructorUsedError;

  /// 手形・小切手番号
  String? get billAndCheckNumber => throw _privateConstructorUsedError;

  /// EDI情報
  String? get ediInfo => throw _privateConstructorUsedError;

  /// 摘要
  String? get abstract => throw _privateConstructorUsedError;

  /// Serializes this AccountTransactionDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountTransactionDetailCopyWith<AccountTransactionDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTransactionDetailCopyWith<$Res> {
  factory $AccountTransactionDetailCopyWith(AccountTransactionDetail value,
          $Res Function(AccountTransactionDetail) then) =
      _$AccountTransactionDetailCopyWithImpl<$Res, AccountTransactionDetail>;
  @useResult
  $Res call(
      {String? inquiryNumber,
      String? transactionDateAd,
      String? valueDateAd,
      String? depositCreditType,
      String? transactionType,
      String? transactionTypeName,
      int? amount,
      String? remitterNameContractorNumber,
      String? remitterCode,
      int? checksIssuedByOtherBanksAmount,
      String? billAndCheckTypeName,
      String? billAndCheckNumber,
      String? ediInfo,
      String? abstract});
}

/// @nodoc
class _$AccountTransactionDetailCopyWithImpl<$Res,
        $Val extends AccountTransactionDetail>
    implements $AccountTransactionDetailCopyWith<$Res> {
  _$AccountTransactionDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inquiryNumber = freezed,
    Object? transactionDateAd = freezed,
    Object? valueDateAd = freezed,
    Object? depositCreditType = freezed,
    Object? transactionType = freezed,
    Object? transactionTypeName = freezed,
    Object? amount = freezed,
    Object? remitterNameContractorNumber = freezed,
    Object? remitterCode = freezed,
    Object? checksIssuedByOtherBanksAmount = freezed,
    Object? billAndCheckTypeName = freezed,
    Object? billAndCheckNumber = freezed,
    Object? ediInfo = freezed,
    Object? abstract = freezed,
  }) {
    return _then(_value.copyWith(
      inquiryNumber: freezed == inquiryNumber
          ? _value.inquiryNumber
          : inquiryNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionDateAd: freezed == transactionDateAd
          ? _value.transactionDateAd
          : transactionDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      valueDateAd: freezed == valueDateAd
          ? _value.valueDateAd
          : valueDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      depositCreditType: freezed == depositCreditType
          ? _value.depositCreditType
          : depositCreditType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: freezed == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionTypeName: freezed == transactionTypeName
          ? _value.transactionTypeName
          : transactionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      remitterNameContractorNumber: freezed == remitterNameContractorNumber
          ? _value.remitterNameContractorNumber
          : remitterNameContractorNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterCode: freezed == remitterCode
          ? _value.remitterCode
          : remitterCode // ignore: cast_nullable_to_non_nullable
              as String?,
      checksIssuedByOtherBanksAmount: freezed == checksIssuedByOtherBanksAmount
          ? _value.checksIssuedByOtherBanksAmount
          : checksIssuedByOtherBanksAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      billAndCheckTypeName: freezed == billAndCheckTypeName
          ? _value.billAndCheckTypeName
          : billAndCheckTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      billAndCheckNumber: freezed == billAndCheckNumber
          ? _value.billAndCheckNumber
          : billAndCheckNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      ediInfo: freezed == ediInfo
          ? _value.ediInfo
          : ediInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      abstract: freezed == abstract
          ? _value.abstract
          : abstract // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountTransactionDetailImplCopyWith<$Res>
    implements $AccountTransactionDetailCopyWith<$Res> {
  factory _$$AccountTransactionDetailImplCopyWith(
          _$AccountTransactionDetailImpl value,
          $Res Function(_$AccountTransactionDetailImpl) then) =
      __$$AccountTransactionDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? inquiryNumber,
      String? transactionDateAd,
      String? valueDateAd,
      String? depositCreditType,
      String? transactionType,
      String? transactionTypeName,
      int? amount,
      String? remitterNameContractorNumber,
      String? remitterCode,
      int? checksIssuedByOtherBanksAmount,
      String? billAndCheckTypeName,
      String? billAndCheckNumber,
      String? ediInfo,
      String? abstract});
}

/// @nodoc
class __$$AccountTransactionDetailImplCopyWithImpl<$Res>
    extends _$AccountTransactionDetailCopyWithImpl<$Res,
        _$AccountTransactionDetailImpl>
    implements _$$AccountTransactionDetailImplCopyWith<$Res> {
  __$$AccountTransactionDetailImplCopyWithImpl(
      _$AccountTransactionDetailImpl _value,
      $Res Function(_$AccountTransactionDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inquiryNumber = freezed,
    Object? transactionDateAd = freezed,
    Object? valueDateAd = freezed,
    Object? depositCreditType = freezed,
    Object? transactionType = freezed,
    Object? transactionTypeName = freezed,
    Object? amount = freezed,
    Object? remitterNameContractorNumber = freezed,
    Object? remitterCode = freezed,
    Object? checksIssuedByOtherBanksAmount = freezed,
    Object? billAndCheckTypeName = freezed,
    Object? billAndCheckNumber = freezed,
    Object? ediInfo = freezed,
    Object? abstract = freezed,
  }) {
    return _then(_$AccountTransactionDetailImpl(
      inquiryNumber: freezed == inquiryNumber
          ? _value.inquiryNumber
          : inquiryNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionDateAd: freezed == transactionDateAd
          ? _value.transactionDateAd
          : transactionDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      valueDateAd: freezed == valueDateAd
          ? _value.valueDateAd
          : valueDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      depositCreditType: freezed == depositCreditType
          ? _value.depositCreditType
          : depositCreditType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: freezed == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionTypeName: freezed == transactionTypeName
          ? _value.transactionTypeName
          : transactionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      remitterNameContractorNumber: freezed == remitterNameContractorNumber
          ? _value.remitterNameContractorNumber
          : remitterNameContractorNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      remitterCode: freezed == remitterCode
          ? _value.remitterCode
          : remitterCode // ignore: cast_nullable_to_non_nullable
              as String?,
      checksIssuedByOtherBanksAmount: freezed == checksIssuedByOtherBanksAmount
          ? _value.checksIssuedByOtherBanksAmount
          : checksIssuedByOtherBanksAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      billAndCheckTypeName: freezed == billAndCheckTypeName
          ? _value.billAndCheckTypeName
          : billAndCheckTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      billAndCheckNumber: freezed == billAndCheckNumber
          ? _value.billAndCheckNumber
          : billAndCheckNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      ediInfo: freezed == ediInfo
          ? _value.ediInfo
          : ediInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      abstract: freezed == abstract
          ? _value.abstract
          : abstract // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountTransactionDetailImpl implements _AccountTransactionDetail {
  const _$AccountTransactionDetailImpl(
      {this.inquiryNumber,
      this.transactionDateAd,
      this.valueDateAd,
      this.depositCreditType,
      this.transactionType,
      this.transactionTypeName,
      this.amount,
      this.remitterNameContractorNumber,
      this.remitterCode,
      this.checksIssuedByOtherBanksAmount,
      this.billAndCheckTypeName,
      this.billAndCheckNumber,
      this.ediInfo,
      this.abstract});

  factory _$AccountTransactionDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$AccountTransactionDetailImplFromJson(json);

  /// 照会番号
  @override
  final String? inquiryNumber;

  /// 取引日（西暦）
  @override
  final String? transactionDateAd;

  /// 起算日（西暦）
  @override
  final String? valueDateAd;

  /// 入払区分
  /// 1：入金
  /// 2：出金
  @override
  final String? depositCreditType;

  /// 取引区分
  /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
  @override
  final String? transactionType;

  /// 取引区分名
  /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
  @override
  final String? transactionTypeName;

  /// 金額
  @override
  final int? amount;

  /// 振込依頼人名または契約者番号
  @override
  final String? remitterNameContractorNumber;

  /// 依頼人コード
  @override
  final String? remitterCode;

  /// 内他店手形
  @override
  final int? checksIssuedByOtherBanksAmount;

  /// 手形・小切手区分名
  /// [ 小切手, 約束手形, 為替手形 ]
  @override
  final String? billAndCheckTypeName;

  /// 手形・小切手番号
  @override
  final String? billAndCheckNumber;

  /// EDI情報
  @override
  final String? ediInfo;

  /// 摘要
  @override
  final String? abstract;

  @override
  String toString() {
    return 'AccountTransactionDetail(inquiryNumber: $inquiryNumber, transactionDateAd: $transactionDateAd, valueDateAd: $valueDateAd, depositCreditType: $depositCreditType, transactionType: $transactionType, transactionTypeName: $transactionTypeName, amount: $amount, remitterNameContractorNumber: $remitterNameContractorNumber, remitterCode: $remitterCode, checksIssuedByOtherBanksAmount: $checksIssuedByOtherBanksAmount, billAndCheckTypeName: $billAndCheckTypeName, billAndCheckNumber: $billAndCheckNumber, ediInfo: $ediInfo, abstract: $abstract)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionDetailImpl &&
            (identical(other.inquiryNumber, inquiryNumber) ||
                other.inquiryNumber == inquiryNumber) &&
            (identical(other.transactionDateAd, transactionDateAd) ||
                other.transactionDateAd == transactionDateAd) &&
            (identical(other.valueDateAd, valueDateAd) ||
                other.valueDateAd == valueDateAd) &&
            (identical(other.depositCreditType, depositCreditType) ||
                other.depositCreditType == depositCreditType) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.transactionTypeName, transactionTypeName) ||
                other.transactionTypeName == transactionTypeName) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.remitterNameContractorNumber,
                    remitterNameContractorNumber) ||
                other.remitterNameContractorNumber ==
                    remitterNameContractorNumber) &&
            (identical(other.remitterCode, remitterCode) ||
                other.remitterCode == remitterCode) &&
            (identical(other.checksIssuedByOtherBanksAmount,
                    checksIssuedByOtherBanksAmount) ||
                other.checksIssuedByOtherBanksAmount ==
                    checksIssuedByOtherBanksAmount) &&
            (identical(other.billAndCheckTypeName, billAndCheckTypeName) ||
                other.billAndCheckTypeName == billAndCheckTypeName) &&
            (identical(other.billAndCheckNumber, billAndCheckNumber) ||
                other.billAndCheckNumber == billAndCheckNumber) &&
            (identical(other.ediInfo, ediInfo) || other.ediInfo == ediInfo) &&
            (identical(other.abstract, abstract) ||
                other.abstract == abstract));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      inquiryNumber,
      transactionDateAd,
      valueDateAd,
      depositCreditType,
      transactionType,
      transactionTypeName,
      amount,
      remitterNameContractorNumber,
      remitterCode,
      checksIssuedByOtherBanksAmount,
      billAndCheckTypeName,
      billAndCheckNumber,
      ediInfo,
      abstract);

  /// Create a copy of AccountTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTransactionDetailImplCopyWith<_$AccountTransactionDetailImpl>
      get copyWith => __$$AccountTransactionDetailImplCopyWithImpl<
          _$AccountTransactionDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountTransactionDetailImplToJson(
      this,
    );
  }
}

abstract class _AccountTransactionDetail implements AccountTransactionDetail {
  const factory _AccountTransactionDetail(
      {final String? inquiryNumber,
      final String? transactionDateAd,
      final String? valueDateAd,
      final String? depositCreditType,
      final String? transactionType,
      final String? transactionTypeName,
      final int? amount,
      final String? remitterNameContractorNumber,
      final String? remitterCode,
      final int? checksIssuedByOtherBanksAmount,
      final String? billAndCheckTypeName,
      final String? billAndCheckNumber,
      final String? ediInfo,
      final String? abstract}) = _$AccountTransactionDetailImpl;

  factory _AccountTransactionDetail.fromJson(Map<String, dynamic> json) =
      _$AccountTransactionDetailImpl.fromJson;

  /// 照会番号
  @override
  String? get inquiryNumber;

  /// 取引日（西暦）
  @override
  String? get transactionDateAd;

  /// 起算日（西暦）
  @override
  String? get valueDateAd;

  /// 入払区分
  /// 1：入金
  /// 2：出金
  @override
  String? get depositCreditType;

  /// 取引区分
  /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
  @override
  String? get transactionType;

  /// 取引区分名
  /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
  @override
  String? get transactionTypeName;

  /// 金額
  @override
  int? get amount;

  /// 振込依頼人名または契約者番号
  @override
  String? get remitterNameContractorNumber;

  /// 依頼人コード
  @override
  String? get remitterCode;

  /// 内他店手形
  @override
  int? get checksIssuedByOtherBanksAmount;

  /// 手形・小切手区分名
  /// [ 小切手, 約束手形, 為替手形 ]
  @override
  String? get billAndCheckTypeName;

  /// 手形・小切手番号
  @override
  String? get billAndCheckNumber;

  /// EDI情報
  @override
  String? get ediInfo;

  /// 摘要
  @override
  String? get abstract;

  /// Create a copy of AccountTransactionDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTransactionDetailImplCopyWith<_$AccountTransactionDetailImpl>
      get copyWith => throw _privateConstructorUsedError;
}

AccountTransactionDetailFixed _$AccountTransactionDetailFixedFromJson(
    Map<String, dynamic> json) {
  return _AccountTransactionDetailFixed.fromJson(json);
}

/// @nodoc
mixin _$AccountTransactionDetailFixed {
  /// 照会番号
  String? get identificationNumber => throw _privateConstructorUsedError;

  /// 取引日（西暦）
  String? get transactionDateAd => throw _privateConstructorUsedError;

  /// 起算日（西暦）
  String? get valueDateAd => throw _privateConstructorUsedError;

  /// 入払区分
  /// 1：入金
  /// 2：出金
  String? get depositCreditType => throw _privateConstructorUsedError;

  /// 取引区分
  /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
  String? get transactionType => throw _privateConstructorUsedError;

  /// 取引区分名
  /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
  String? get transactionTypeName => throw _privateConstructorUsedError;

  /// 金額
  int? get amount => throw _privateConstructorUsedError;

  /// 内他店手形
  int? get checksIssuedByOtherBanksAmount => throw _privateConstructorUsedError;

  /// 利率
  /// 上2桁整数、下4桁小数部
  String? get interestRate => throw _privateConstructorUsedError;

  /// 満期日
  String? get maturityDateAd => throw _privateConstructorUsedError;

  /// 摘要
  String? get abstract => throw _privateConstructorUsedError;

  /// Serializes this AccountTransactionDetailFixed to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AccountTransactionDetailFixed
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AccountTransactionDetailFixedCopyWith<AccountTransactionDetailFixed>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AccountTransactionDetailFixedCopyWith<$Res> {
  factory $AccountTransactionDetailFixedCopyWith(
          AccountTransactionDetailFixed value,
          $Res Function(AccountTransactionDetailFixed) then) =
      _$AccountTransactionDetailFixedCopyWithImpl<$Res,
          AccountTransactionDetailFixed>;
  @useResult
  $Res call(
      {String? identificationNumber,
      String? transactionDateAd,
      String? valueDateAd,
      String? depositCreditType,
      String? transactionType,
      String? transactionTypeName,
      int? amount,
      int? checksIssuedByOtherBanksAmount,
      String? interestRate,
      String? maturityDateAd,
      String? abstract});
}

/// @nodoc
class _$AccountTransactionDetailFixedCopyWithImpl<$Res,
        $Val extends AccountTransactionDetailFixed>
    implements $AccountTransactionDetailFixedCopyWith<$Res> {
  _$AccountTransactionDetailFixedCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AccountTransactionDetailFixed
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identificationNumber = freezed,
    Object? transactionDateAd = freezed,
    Object? valueDateAd = freezed,
    Object? depositCreditType = freezed,
    Object? transactionType = freezed,
    Object? transactionTypeName = freezed,
    Object? amount = freezed,
    Object? checksIssuedByOtherBanksAmount = freezed,
    Object? interestRate = freezed,
    Object? maturityDateAd = freezed,
    Object? abstract = freezed,
  }) {
    return _then(_value.copyWith(
      identificationNumber: freezed == identificationNumber
          ? _value.identificationNumber
          : identificationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionDateAd: freezed == transactionDateAd
          ? _value.transactionDateAd
          : transactionDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      valueDateAd: freezed == valueDateAd
          ? _value.valueDateAd
          : valueDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      depositCreditType: freezed == depositCreditType
          ? _value.depositCreditType
          : depositCreditType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: freezed == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionTypeName: freezed == transactionTypeName
          ? _value.transactionTypeName
          : transactionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      checksIssuedByOtherBanksAmount: freezed == checksIssuedByOtherBanksAmount
          ? _value.checksIssuedByOtherBanksAmount
          : checksIssuedByOtherBanksAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as String?,
      maturityDateAd: freezed == maturityDateAd
          ? _value.maturityDateAd
          : maturityDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      abstract: freezed == abstract
          ? _value.abstract
          : abstract // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AccountTransactionDetailFixedImplCopyWith<$Res>
    implements $AccountTransactionDetailFixedCopyWith<$Res> {
  factory _$$AccountTransactionDetailFixedImplCopyWith(
          _$AccountTransactionDetailFixedImpl value,
          $Res Function(_$AccountTransactionDetailFixedImpl) then) =
      __$$AccountTransactionDetailFixedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? identificationNumber,
      String? transactionDateAd,
      String? valueDateAd,
      String? depositCreditType,
      String? transactionType,
      String? transactionTypeName,
      int? amount,
      int? checksIssuedByOtherBanksAmount,
      String? interestRate,
      String? maturityDateAd,
      String? abstract});
}

/// @nodoc
class __$$AccountTransactionDetailFixedImplCopyWithImpl<$Res>
    extends _$AccountTransactionDetailFixedCopyWithImpl<$Res,
        _$AccountTransactionDetailFixedImpl>
    implements _$$AccountTransactionDetailFixedImplCopyWith<$Res> {
  __$$AccountTransactionDetailFixedImplCopyWithImpl(
      _$AccountTransactionDetailFixedImpl _value,
      $Res Function(_$AccountTransactionDetailFixedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AccountTransactionDetailFixed
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identificationNumber = freezed,
    Object? transactionDateAd = freezed,
    Object? valueDateAd = freezed,
    Object? depositCreditType = freezed,
    Object? transactionType = freezed,
    Object? transactionTypeName = freezed,
    Object? amount = freezed,
    Object? checksIssuedByOtherBanksAmount = freezed,
    Object? interestRate = freezed,
    Object? maturityDateAd = freezed,
    Object? abstract = freezed,
  }) {
    return _then(_$AccountTransactionDetailFixedImpl(
      identificationNumber: freezed == identificationNumber
          ? _value.identificationNumber
          : identificationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionDateAd: freezed == transactionDateAd
          ? _value.transactionDateAd
          : transactionDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      valueDateAd: freezed == valueDateAd
          ? _value.valueDateAd
          : valueDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      depositCreditType: freezed == depositCreditType
          ? _value.depositCreditType
          : depositCreditType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionType: freezed == transactionType
          ? _value.transactionType
          : transactionType // ignore: cast_nullable_to_non_nullable
              as String?,
      transactionTypeName: freezed == transactionTypeName
          ? _value.transactionTypeName
          : transactionTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int?,
      checksIssuedByOtherBanksAmount: freezed == checksIssuedByOtherBanksAmount
          ? _value.checksIssuedByOtherBanksAmount
          : checksIssuedByOtherBanksAmount // ignore: cast_nullable_to_non_nullable
              as int?,
      interestRate: freezed == interestRate
          ? _value.interestRate
          : interestRate // ignore: cast_nullable_to_non_nullable
              as String?,
      maturityDateAd: freezed == maturityDateAd
          ? _value.maturityDateAd
          : maturityDateAd // ignore: cast_nullable_to_non_nullable
              as String?,
      abstract: freezed == abstract
          ? _value.abstract
          : abstract // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AccountTransactionDetailFixedImpl
    implements _AccountTransactionDetailFixed {
  const _$AccountTransactionDetailFixedImpl(
      {this.identificationNumber,
      this.transactionDateAd,
      this.valueDateAd,
      this.depositCreditType,
      this.transactionType,
      this.transactionTypeName,
      this.amount,
      this.checksIssuedByOtherBanksAmount,
      this.interestRate,
      this.maturityDateAd,
      this.abstract});

  factory _$AccountTransactionDetailFixedImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$AccountTransactionDetailFixedImplFromJson(json);

  /// 照会番号
  @override
  final String? identificationNumber;

  /// 取引日（西暦）
  @override
  final String? transactionDateAd;

  /// 起算日（西暦）
  @override
  final String? valueDateAd;

  /// 入払区分
  /// 1：入金
  /// 2：出金
  @override
  final String? depositCreditType;

  /// 取引区分
  /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
  @override
  final String? transactionType;

  /// 取引区分名
  /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
  @override
  final String? transactionTypeName;

  /// 金額
  @override
  final int? amount;

  /// 内他店手形
  @override
  final int? checksIssuedByOtherBanksAmount;

  /// 利率
  /// 上2桁整数、下4桁小数部
  @override
  final String? interestRate;

  /// 満期日
  @override
  final String? maturityDateAd;

  /// 摘要
  @override
  final String? abstract;

  @override
  String toString() {
    return 'AccountTransactionDetailFixed(identificationNumber: $identificationNumber, transactionDateAd: $transactionDateAd, valueDateAd: $valueDateAd, depositCreditType: $depositCreditType, transactionType: $transactionType, transactionTypeName: $transactionTypeName, amount: $amount, checksIssuedByOtherBanksAmount: $checksIssuedByOtherBanksAmount, interestRate: $interestRate, maturityDateAd: $maturityDateAd, abstract: $abstract)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AccountTransactionDetailFixedImpl &&
            (identical(other.identificationNumber, identificationNumber) ||
                other.identificationNumber == identificationNumber) &&
            (identical(other.transactionDateAd, transactionDateAd) ||
                other.transactionDateAd == transactionDateAd) &&
            (identical(other.valueDateAd, valueDateAd) ||
                other.valueDateAd == valueDateAd) &&
            (identical(other.depositCreditType, depositCreditType) ||
                other.depositCreditType == depositCreditType) &&
            (identical(other.transactionType, transactionType) ||
                other.transactionType == transactionType) &&
            (identical(other.transactionTypeName, transactionTypeName) ||
                other.transactionTypeName == transactionTypeName) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.checksIssuedByOtherBanksAmount,
                    checksIssuedByOtherBanksAmount) ||
                other.checksIssuedByOtherBanksAmount ==
                    checksIssuedByOtherBanksAmount) &&
            (identical(other.interestRate, interestRate) ||
                other.interestRate == interestRate) &&
            (identical(other.maturityDateAd, maturityDateAd) ||
                other.maturityDateAd == maturityDateAd) &&
            (identical(other.abstract, abstract) ||
                other.abstract == abstract));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      identificationNumber,
      transactionDateAd,
      valueDateAd,
      depositCreditType,
      transactionType,
      transactionTypeName,
      amount,
      checksIssuedByOtherBanksAmount,
      interestRate,
      maturityDateAd,
      abstract);

  /// Create a copy of AccountTransactionDetailFixed
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AccountTransactionDetailFixedImplCopyWith<
          _$AccountTransactionDetailFixedImpl>
      get copyWith => __$$AccountTransactionDetailFixedImplCopyWithImpl<
          _$AccountTransactionDetailFixedImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AccountTransactionDetailFixedImplToJson(
      this,
    );
  }
}

abstract class _AccountTransactionDetailFixed
    implements AccountTransactionDetailFixed {
  const factory _AccountTransactionDetailFixed(
      {final String? identificationNumber,
      final String? transactionDateAd,
      final String? valueDateAd,
      final String? depositCreditType,
      final String? transactionType,
      final String? transactionTypeName,
      final int? amount,
      final int? checksIssuedByOtherBanksAmount,
      final String? interestRate,
      final String? maturityDateAd,
      final String? abstract}) = _$AccountTransactionDetailFixedImpl;

  factory _AccountTransactionDetailFixed.fromJson(Map<String, dynamic> json) =
      _$AccountTransactionDetailFixedImpl.fromJson;

  /// 照会番号
  @override
  String? get identificationNumber;

  /// 取引日（西暦）
  @override
  String? get transactionDateAd;

  /// 起算日（西暦）
  @override
  String? get valueDateAd;

  /// 入払区分
  /// 1：入金
  /// 2：出金
  @override
  String? get depositCreditType;

  /// 取引区分
  /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
  @override
  String? get transactionType;

  /// 取引区分名
  /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
  @override
  String? get transactionTypeName;

  /// 金額
  @override
  int? get amount;

  /// 内他店手形
  @override
  int? get checksIssuedByOtherBanksAmount;

  /// 利率
  /// 上2桁整数、下4桁小数部
  @override
  String? get interestRate;

  /// 満期日
  @override
  String? get maturityDateAd;

  /// 摘要
  @override
  String? get abstract;

  /// Create a copy of AccountTransactionDetailFixed
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AccountTransactionDetailFixedImplCopyWith<
          _$AccountTransactionDetailFixedImpl>
      get copyWith => throw _privateConstructorUsedError;
}
