// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'account_transaction_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AccountTransactionResponseImpl _$$AccountTransactionResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$AccountTransactionResponseImpl(
      baseDate: json['baseDate'] as String?,
      baseTime: json['baseTime'] as String?,
      accountTypeCode: json['accountTypeCode'] as String?,
      totalDepositAmount: (json['totalDepositAmount'] as num?)?.toInt(),
      totalWithdrawalAmount: (json['totalWithdrawalAmount'] as num?)?.toInt(),
      transactionDataCount: (json['transactionDataCount'] as num?)?.toInt(),
      transactions1: (json['transactions1'] as List<dynamic>)
          .map((e) =>
              AccountTransactionDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
      transactions2: (json['transactions2'] as List<dynamic>)
          .map((e) =>
              AccountTransactionDetailFixed.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AccountTransactionResponseImplToJson(
        _$AccountTransactionResponseImpl instance) =>
    <String, dynamic>{
      'baseDate': instance.baseDate,
      'baseTime': instance.baseTime,
      'accountTypeCode': instance.accountTypeCode,
      'totalDepositAmount': instance.totalDepositAmount,
      'totalWithdrawalAmount': instance.totalWithdrawalAmount,
      'transactionDataCount': instance.transactionDataCount,
      'transactions1': instance.transactions1,
      'transactions2': instance.transactions2,
    };

_$AccountTransactionDetailImpl _$$AccountTransactionDetailImplFromJson(
        Map<String, dynamic> json) =>
    _$AccountTransactionDetailImpl(
      inquiryNumber: json['inquiryNumber'] as String?,
      transactionDateAd: json['transactionDateAd'] as String?,
      valueDateAd: json['valueDateAd'] as String?,
      depositCreditType: json['depositCreditType'] as String?,
      transactionType: json['transactionType'] as String?,
      transactionTypeName: json['transactionTypeName'] as String?,
      amount: (json['amount'] as num?)?.toInt(),
      remitterNameContractorNumber:
          json['remitterNameContractorNumber'] as String?,
      remitterCode: json['remitterCode'] as String?,
      checksIssuedByOtherBanksAmount:
          (json['checksIssuedByOtherBanksAmount'] as num?)?.toInt(),
      billAndCheckTypeName: json['billAndCheckTypeName'] as String?,
      billAndCheckNumber: json['billAndCheckNumber'] as String?,
      ediInfo: json['ediInfo'] as String?,
      abstract: json['abstract'] as String?,
    );

Map<String, dynamic> _$$AccountTransactionDetailImplToJson(
        _$AccountTransactionDetailImpl instance) =>
    <String, dynamic>{
      'inquiryNumber': instance.inquiryNumber,
      'transactionDateAd': instance.transactionDateAd,
      'valueDateAd': instance.valueDateAd,
      'depositCreditType': instance.depositCreditType,
      'transactionType': instance.transactionType,
      'transactionTypeName': instance.transactionTypeName,
      'amount': instance.amount,
      'remitterNameContractorNumber': instance.remitterNameContractorNumber,
      'remitterCode': instance.remitterCode,
      'checksIssuedByOtherBanksAmount': instance.checksIssuedByOtherBanksAmount,
      'billAndCheckTypeName': instance.billAndCheckTypeName,
      'billAndCheckNumber': instance.billAndCheckNumber,
      'ediInfo': instance.ediInfo,
      'abstract': instance.abstract,
    };

_$AccountTransactionDetailFixedImpl
    _$$AccountTransactionDetailFixedImplFromJson(Map<String, dynamic> json) =>
        _$AccountTransactionDetailFixedImpl(
          identificationNumber: json['identificationNumber'] as String?,
          transactionDateAd: json['transactionDateAd'] as String?,
          valueDateAd: json['valueDateAd'] as String?,
          depositCreditType: json['depositCreditType'] as String?,
          transactionType: json['transactionType'] as String?,
          transactionTypeName: json['transactionTypeName'] as String?,
          amount: (json['amount'] as num?)?.toInt(),
          checksIssuedByOtherBanksAmount:
              (json['checksIssuedByOtherBanksAmount'] as num?)?.toInt(),
          interestRate: json['interestRate'] as String?,
          maturityDateAd: json['maturityDateAd'] as String?,
          abstract: json['abstract'] as String?,
        );

Map<String, dynamic> _$$AccountTransactionDetailFixedImplToJson(
        _$AccountTransactionDetailFixedImpl instance) =>
    <String, dynamic>{
      'identificationNumber': instance.identificationNumber,
      'transactionDateAd': instance.transactionDateAd,
      'valueDateAd': instance.valueDateAd,
      'depositCreditType': instance.depositCreditType,
      'transactionType': instance.transactionType,
      'transactionTypeName': instance.transactionTypeName,
      'amount': instance.amount,
      'checksIssuedByOtherBanksAmount': instance.checksIssuedByOtherBanksAmount,
      'interestRate': instance.interestRate,
      'maturityDateAd': instance.maturityDateAd,
      'abstract': instance.abstract,
    };
