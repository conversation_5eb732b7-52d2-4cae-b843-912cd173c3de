import 'package:freezed_annotation/freezed_annotation.dart';

part 'account_transaction_response.freezed.dart';
part 'account_transaction_response.g.dart';

@freezed
class AccountTransactionResponse with _$AccountTransactionResponse {
  const factory AccountTransactionResponse({
    /// 基準日
    String? baseDate,

    /// 基準時刻
    String? baseTime,

    /// 預金種目コード
    String? accountTypeCode,

    /// 入金額合計
    int? totalDepositAmount,

    /// 出金額合計
    int? totalWithdrawalAmount,

    /// 取引データ件数
    int? transactionDataCount,

    /// 流動制預金の取引明細
    required List<AccountTransactionDetail> transactions1,

    /// 固定制預金の取引明細
    required List<AccountTransactionDetailFixed> transactions2,
  }) = _AccountTransactionResponse;

  factory AccountTransactionResponse.fromJson(Map<String, dynamic> json) =>
      _$AccountTransactionResponseFromJson(json);
}

extension AccountTransactionResponseExt on AccountTransactionResponse {
  /// 預金種別コードによって預金の種類を判定する
  /// 流動制預金：01（普通）, 02（当座）, 04（貯蓄預金）
  /// 固定制預金：05（通知預金）, 06（定期預金）, 07（積立定期預金）
  /// 対象外：03（納税準備預金）, 08（定期積金）, 09（その他）
  T whenAccountType<T>({
    required T Function() liquidity,
    required T Function() fixed,
    required T Function() other,
  }) {
    if (accountTypeCode == '01' ||
        accountTypeCode == '02' ||
        accountTypeCode == '04') {
      return liquidity();
    } else if (accountTypeCode == '05' ||
        accountTypeCode == '06' ||
        accountTypeCode == '07') {
      return fixed();
    } else {
      return other();
    }
  }
}

@freezed
class AccountTransactionDetail with _$AccountTransactionDetail {
  const factory AccountTransactionDetail({
    /// 照会番号
    String? inquiryNumber,

    /// 取引日（西暦）
    String? transactionDateAd,

    /// 起算日（西暦）
    String? valueDateAd,

    /// 入払区分
    /// 1：入金
    /// 2：出金
    String? depositCreditType,

    /// 取引区分
    /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
    String? transactionType,

    /// 取引区分名
    /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
    String? transactionTypeName,

    /// 金額
    int? amount,

    /// 振込依頼人名または契約者番号
    String? remitterNameContractorNumber,

    /// 依頼人コード
    String? remitterCode,

    /// 内他店手形
    int? checksIssuedByOtherBanksAmount,

    /// 手形・小切手区分名
    /// [ 小切手, 約束手形, 為替手形 ]
    String? billAndCheckTypeName,

    /// 手形・小切手番号
    String? billAndCheckNumber,

    /// EDI情報
    String? ediInfo,

    /// 摘要
    String? abstract,
  }) = _AccountTransactionDetail;

  factory AccountTransactionDetail.fromJson(Map<String, dynamic> json) =>
      _$AccountTransactionDetailFromJson(json);
}

@freezed
class AccountTransactionDetailFixed with _$AccountTransactionDetailFixed {
  const factory AccountTransactionDetailFixed({
    /// 照会番号
    String? identificationNumber,

    /// 取引日（西暦）
    String? transactionDateAd,

    /// 起算日（西暦）
    String? valueDateAd,

    /// 入払区分
    /// 1：入金
    /// 2：出金
    String? depositCreditType,

    /// 取引区分
    /// [ 10, 11, 12, 13, 14, 18, 19, 31 ]
    String? transactionType,

    /// 取引区分名
    /// [ 現金, 振込, 他店券入金, 交換（取立入金および交換払）, 振替, その他, 訂正, でんさい ]
    String? transactionTypeName,

    /// 金額
    int? amount,

    /// 内他店手形
    int? checksIssuedByOtherBanksAmount,

    /// 利率
    /// 上2桁整数、下4桁小数部
    String? interestRate,

    /// 満期日
    String? maturityDateAd,

    /// 摘要
    String? abstract,
  }) = _AccountTransactionDetailFixed;

  factory AccountTransactionDetailFixed.fromJson(Map<String, dynamic> json) =>
      _$AccountTransactionDetailFixedFromJson(json);
}
