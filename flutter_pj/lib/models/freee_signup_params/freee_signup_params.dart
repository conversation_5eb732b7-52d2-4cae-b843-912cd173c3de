import 'package:freezed_annotation/freezed_annotation.dart';

part 'freee_signup_params.freezed.dart';
part 'freee_signup_params.g.dart';

@freezed

/// freee初回連携時に必要な情報を保持するクラス
class FreeeSsoParams with _$FreeeSsoParams {
  const factory FreeeSsoParams({
    required String state,
    // 企業毎に紐づくID
    required String partnerId,
    // 連携済み企業フラグ
    @JsonKey(name: 'exists') required bool isPartnerIdExists,
  }) = _FreeeSsoParams;

  factory FreeeSsoParams.fromJson(Map<String, dynamic> json) =>
      _$FreeeSsoParamsFromJson(json);
}
