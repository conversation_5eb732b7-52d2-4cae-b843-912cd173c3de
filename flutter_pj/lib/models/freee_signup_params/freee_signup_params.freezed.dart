// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'freee_signup_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

FreeeSsoParams _$FreeeSsoParamsFromJson(Map<String, dynamic> json) {
  return _FreeeSsoParams.fromJson(json);
}

/// @nodoc
mixin _$FreeeSsoParams {
  String get state => throw _privateConstructorUsedError; // 企業毎に紐づくID
  String get partnerId => throw _privateConstructorUsedError; // 連携済み企業フラグ
  @JsonKey(name: 'exists')
  bool get isPartnerIdExists => throw _privateConstructorUsedError;

  /// Serializes this FreeeSsoParams to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of FreeeSsoParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeSsoParamsCopyWith<FreeeSsoParams> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeSsoParamsCopyWith<$Res> {
  factory $FreeeSsoParamsCopyWith(
          FreeeSsoParams value, $Res Function(FreeeSsoParams) then) =
      _$FreeeSsoParamsCopyWithImpl<$Res, FreeeSsoParams>;
  @useResult
  $Res call(
      {String state,
      String partnerId,
      @JsonKey(name: 'exists') bool isPartnerIdExists});
}

/// @nodoc
class _$FreeeSsoParamsCopyWithImpl<$Res, $Val extends FreeeSsoParams>
    implements $FreeeSsoParamsCopyWith<$Res> {
  _$FreeeSsoParamsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeSsoParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? partnerId = null,
    Object? isPartnerIdExists = null,
  }) {
    return _then(_value.copyWith(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
      partnerId: null == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as String,
      isPartnerIdExists: null == isPartnerIdExists
          ? _value.isPartnerIdExists
          : isPartnerIdExists // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeSsoParamsImplCopyWith<$Res>
    implements $FreeeSsoParamsCopyWith<$Res> {
  factory _$$FreeeSsoParamsImplCopyWith(_$FreeeSsoParamsImpl value,
          $Res Function(_$FreeeSsoParamsImpl) then) =
      __$$FreeeSsoParamsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String state,
      String partnerId,
      @JsonKey(name: 'exists') bool isPartnerIdExists});
}

/// @nodoc
class __$$FreeeSsoParamsImplCopyWithImpl<$Res>
    extends _$FreeeSsoParamsCopyWithImpl<$Res, _$FreeeSsoParamsImpl>
    implements _$$FreeeSsoParamsImplCopyWith<$Res> {
  __$$FreeeSsoParamsImplCopyWithImpl(
      _$FreeeSsoParamsImpl _value, $Res Function(_$FreeeSsoParamsImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeSsoParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? state = null,
    Object? partnerId = null,
    Object? isPartnerIdExists = null,
  }) {
    return _then(_$FreeeSsoParamsImpl(
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
      partnerId: null == partnerId
          ? _value.partnerId
          : partnerId // ignore: cast_nullable_to_non_nullable
              as String,
      isPartnerIdExists: null == isPartnerIdExists
          ? _value.isPartnerIdExists
          : isPartnerIdExists // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FreeeSsoParamsImpl implements _FreeeSsoParams {
  const _$FreeeSsoParamsImpl(
      {required this.state,
      required this.partnerId,
      @JsonKey(name: 'exists') required this.isPartnerIdExists});

  factory _$FreeeSsoParamsImpl.fromJson(Map<String, dynamic> json) =>
      _$$FreeeSsoParamsImplFromJson(json);

  @override
  final String state;
// 企業毎に紐づくID
  @override
  final String partnerId;
// 連携済み企業フラグ
  @override
  @JsonKey(name: 'exists')
  final bool isPartnerIdExists;

  @override
  String toString() {
    return 'FreeeSsoParams(state: $state, partnerId: $partnerId, isPartnerIdExists: $isPartnerIdExists)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeSsoParamsImpl &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.partnerId, partnerId) ||
                other.partnerId == partnerId) &&
            (identical(other.isPartnerIdExists, isPartnerIdExists) ||
                other.isPartnerIdExists == isPartnerIdExists));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, state, partnerId, isPartnerIdExists);

  /// Create a copy of FreeeSsoParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeSsoParamsImplCopyWith<_$FreeeSsoParamsImpl> get copyWith =>
      __$$FreeeSsoParamsImplCopyWithImpl<_$FreeeSsoParamsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FreeeSsoParamsImplToJson(
      this,
    );
  }
}

abstract class _FreeeSsoParams implements FreeeSsoParams {
  const factory _FreeeSsoParams(
          {required final String state,
          required final String partnerId,
          @JsonKey(name: 'exists') required final bool isPartnerIdExists}) =
      _$FreeeSsoParamsImpl;

  factory _FreeeSsoParams.fromJson(Map<String, dynamic> json) =
      _$FreeeSsoParamsImpl.fromJson;

  @override
  String get state; // 企業毎に紐づくID
  @override
  String get partnerId; // 連携済み企業フラグ
  @override
  @JsonKey(name: 'exists')
  bool get isPartnerIdExists;

  /// Create a copy of FreeeSsoParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeSsoParamsImplCopyWith<_$FreeeSsoParamsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
