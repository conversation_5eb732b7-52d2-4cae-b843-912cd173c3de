import 'package:freezed_annotation/freezed_annotation.dart';

part 'session_id_or_high_risk_user_id.freezed.dart';

@freezed
sealed class SessionIdOrHighRiskUserId with _$SessionIdOrHighRiskUserId {
  const factory SessionIdOrHighRiskUserId.sessionId(String value) = _SessionId;

  const factory SessionIdOrHighRiskUserId.highRiskUserId(String value) =
      _HighRiskUserId;

  static SessionIdOrHighRiskUserId? from({
    dynamic sessionId,
    dynamic highRiskUserId,
  }) {
    if (sessionId is String && sessionId.isNotEmpty) {
      return SessionIdOrHighRiskUserId.sessionId(sessionId);
    } else if (highRiskUserId is String && highRiskUserId.isNotEmpty) {
      return SessionIdOrHighRiskUserId.highRiskUserId(highRiskUserId);
    } else {
      return null;
    }
  }
}

extension SessionIdOrHighRiskUserIdExt on SessionIdOrHighRiskUserId {
  bool get isSessionId => this is _SessionId;

  bool get isHighRiskUserId => this is _HighRiskUserId;
}
