// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_id_or_high_risk_user_id.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SessionIdOrHighRiskUserId {
  String get value => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String value) sessionId,
    required TResult Function(String value) highRiskUserId,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String value)? sessionId,
    TResult? Function(String value)? highRiskUserId,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String value)? sessionId,
    TResult Function(String value)? highRiskUserId,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionId value) sessionId,
    required TResult Function(_HighRiskUserId value) highRiskUserId,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionId value)? sessionId,
    TResult? Function(_HighRiskUserId value)? highRiskUserId,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionId value)? sessionId,
    TResult Function(_HighRiskUserId value)? highRiskUserId,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SessionIdOrHighRiskUserIdCopyWith<SessionIdOrHighRiskUserId> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionIdOrHighRiskUserIdCopyWith<$Res> {
  factory $SessionIdOrHighRiskUserIdCopyWith(SessionIdOrHighRiskUserId value,
          $Res Function(SessionIdOrHighRiskUserId) then) =
      _$SessionIdOrHighRiskUserIdCopyWithImpl<$Res, SessionIdOrHighRiskUserId>;
  @useResult
  $Res call({String value});
}

/// @nodoc
class _$SessionIdOrHighRiskUserIdCopyWithImpl<$Res,
        $Val extends SessionIdOrHighRiskUserId>
    implements $SessionIdOrHighRiskUserIdCopyWith<$Res> {
  _$SessionIdOrHighRiskUserIdCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_value.copyWith(
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionIdImplCopyWith<$Res>
    implements $SessionIdOrHighRiskUserIdCopyWith<$Res> {
  factory _$$SessionIdImplCopyWith(
          _$SessionIdImpl value, $Res Function(_$SessionIdImpl) then) =
      __$$SessionIdImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$SessionIdImplCopyWithImpl<$Res>
    extends _$SessionIdOrHighRiskUserIdCopyWithImpl<$Res, _$SessionIdImpl>
    implements _$$SessionIdImplCopyWith<$Res> {
  __$$SessionIdImplCopyWithImpl(
      _$SessionIdImpl _value, $Res Function(_$SessionIdImpl) _then)
      : super(_value, _then);

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$SessionIdImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SessionIdImpl implements _SessionId {
  const _$SessionIdImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'SessionIdOrHighRiskUserId.sessionId(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionIdImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionIdImplCopyWith<_$SessionIdImpl> get copyWith =>
      __$$SessionIdImplCopyWithImpl<_$SessionIdImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String value) sessionId,
    required TResult Function(String value) highRiskUserId,
  }) {
    return sessionId(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String value)? sessionId,
    TResult? Function(String value)? highRiskUserId,
  }) {
    return sessionId?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String value)? sessionId,
    TResult Function(String value)? highRiskUserId,
    required TResult orElse(),
  }) {
    if (sessionId != null) {
      return sessionId(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionId value) sessionId,
    required TResult Function(_HighRiskUserId value) highRiskUserId,
  }) {
    return sessionId(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionId value)? sessionId,
    TResult? Function(_HighRiskUserId value)? highRiskUserId,
  }) {
    return sessionId?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionId value)? sessionId,
    TResult Function(_HighRiskUserId value)? highRiskUserId,
    required TResult orElse(),
  }) {
    if (sessionId != null) {
      return sessionId(this);
    }
    return orElse();
  }
}

abstract class _SessionId implements SessionIdOrHighRiskUserId {
  const factory _SessionId(final String value) = _$SessionIdImpl;

  @override
  String get value;

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SessionIdImplCopyWith<_$SessionIdImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$HighRiskUserIdImplCopyWith<$Res>
    implements $SessionIdOrHighRiskUserIdCopyWith<$Res> {
  factory _$$HighRiskUserIdImplCopyWith(_$HighRiskUserIdImpl value,
          $Res Function(_$HighRiskUserIdImpl) then) =
      __$$HighRiskUserIdImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String value});
}

/// @nodoc
class __$$HighRiskUserIdImplCopyWithImpl<$Res>
    extends _$SessionIdOrHighRiskUserIdCopyWithImpl<$Res, _$HighRiskUserIdImpl>
    implements _$$HighRiskUserIdImplCopyWith<$Res> {
  __$$HighRiskUserIdImplCopyWithImpl(
      _$HighRiskUserIdImpl _value, $Res Function(_$HighRiskUserIdImpl) _then)
      : super(_value, _then);

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
  }) {
    return _then(_$HighRiskUserIdImpl(
      null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$HighRiskUserIdImpl implements _HighRiskUserId {
  const _$HighRiskUserIdImpl(this.value);

  @override
  final String value;

  @override
  String toString() {
    return 'SessionIdOrHighRiskUserId.highRiskUserId(value: $value)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HighRiskUserIdImpl &&
            (identical(other.value, value) || other.value == value));
  }

  @override
  int get hashCode => Object.hash(runtimeType, value);

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HighRiskUserIdImplCopyWith<_$HighRiskUserIdImpl> get copyWith =>
      __$$HighRiskUserIdImplCopyWithImpl<_$HighRiskUserIdImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String value) sessionId,
    required TResult Function(String value) highRiskUserId,
  }) {
    return highRiskUserId(value);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String value)? sessionId,
    TResult? Function(String value)? highRiskUserId,
  }) {
    return highRiskUserId?.call(value);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String value)? sessionId,
    TResult Function(String value)? highRiskUserId,
    required TResult orElse(),
  }) {
    if (highRiskUserId != null) {
      return highRiskUserId(value);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_SessionId value) sessionId,
    required TResult Function(_HighRiskUserId value) highRiskUserId,
  }) {
    return highRiskUserId(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_SessionId value)? sessionId,
    TResult? Function(_HighRiskUserId value)? highRiskUserId,
  }) {
    return highRiskUserId?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_SessionId value)? sessionId,
    TResult Function(_HighRiskUserId value)? highRiskUserId,
    required TResult orElse(),
  }) {
    if (highRiskUserId != null) {
      return highRiskUserId(this);
    }
    return orElse();
  }
}

abstract class _HighRiskUserId implements SessionIdOrHighRiskUserId {
  const factory _HighRiskUserId(final String value) = _$HighRiskUserIdImpl;

  @override
  String get value;

  /// Create a copy of SessionIdOrHighRiskUserId
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HighRiskUserIdImplCopyWith<_$HighRiskUserIdImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
