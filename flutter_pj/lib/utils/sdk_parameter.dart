import 'dart:convert';
import 'dart:typed_data';

class SdkParameter {
  // SDKに渡すパラメータ一覧(iOS)
  static const String statusMessage = '読み取りの準備ができました';
  static const String errorStatusMessage = '読み取りに失敗しました';
  static const String successStatusMessage = '読み取りに成功しました';
  static const String readingMessage = '読み取り中';

  // SDKに渡すパラメータ一覧(Android)
  // NFCリーダーモード設定に渡すタイムアウト値(ミリ秒)
  static const int timeout = 60000;
  // 残試行回数取得の関数に渡すType値（0は署名用パスワード）
  static const int type = 0;

  // 暫定的な署名対象ドキュメント;
  static const String data = 'aaa';

  static String getEncodedData() {
    final Uint8List bytes = Uint8List.fromList(utf8.encode(data));
    final String encodedData = base64Encode(bytes);
    return encodedData;
  }
}
