import 'package:flutter/widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CodeMap {
  ///認証区分コードを日本語に変換するメソッド
  static String ninsyoKbn(BuildContext context, String ninsyoKbn) {
    final String value;
    switch (ninsyoKbn) {
      case '01':
        value = AppLocalizations.of(context)!.passwordNinshou;
        break;
      case '02':
        value = AppLocalizations.of(context)!.icCardNinshou;
        break;
      case '03':
        value = AppLocalizations.of(context)!.icCardNinshou;
        break;
      default:
        //想定外の値の場合は空白を返す
        value = '';
    }
    return value;
  }

  ///科目コードを日本語に変換するメソッド(全銀仕様)
  /*
  static String accountType(BuildContext context, String accountType) {
    final String value;
    switch (accountType) {
      case '01':
        value = AppLocalizations.of(context)!.futsu;
        break;
      case '02':
        value = AppLocalizations.of(context)!.touza;
        break;
      case '03':
        value = AppLocalizations.of(context)!.nouzeiJunbiYokin;
        break;
      case '04':
        value = AppLocalizations.of(context)!.tyochikuYokin;
        break;
      case '05':
        value = AppLocalizations.of(context)!.tsuchiYokin;
        break;
      case '06':
        value = AppLocalizations.of(context)!.teikiYokin;
        break;
      case '07':
        value = AppLocalizations.of(context)!.tsumitateTeikiYokin;
        break;
      case '08':
        value = AppLocalizations.of(context)!.teikiTsumikin;
        break;
      case '09':
        value = AppLocalizations.of(context)!.sonota;
        break;
      default:
        //想定外の値の場合は空白を返す
        value = '';
    }
    return value;
  }
  */

  ///科目コードを日本語に変換するメソッド(VD仕様)
  static String accountTypeFromVD(BuildContext context, String accountType) {
    final String value;
    switch (accountType) {
      case '21':
        value = AppLocalizations.of(context)!.touza;
        break;
      case '22':
        value = AppLocalizations.of(context)!.futsu;
        break;
      default:
        //想定外の値の場合は空白を返す
        value = '';
    }
    return value;
  }

  /// 利用者区分を日本語に変換するメソッド
  static String userType(BuildContext context, String userType) {
    final String value;
    switch (userType) {
      case '01':
        // 管理専用ID
        value = AppLocalizations.of(context)!.kanriSenyouID;
        break;
      case '02':
        // 管理専用ID（副）
        value = AppLocalizations.of(context)!.kanriSenyouIDFuku;
        break;
      case '03':
      case '04':
        // 利用者ID
        value = AppLocalizations.of(context)!.riyoushaID;
        break;
      case '05':
        // 電子契約専用ID
        value = AppLocalizations.of(context)!.denshiKeiyakuSenyouID;
        break;
      default:
        //想定外の値の場合は空文字を返す
        value = '';
    }
    return value;
  }
}
