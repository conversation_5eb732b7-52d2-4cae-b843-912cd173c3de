import 'dart:io';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

/// URLからページを開く処理を管理するユーティリティクラス
class UrlLauncher {
  /// リンクIDとして定義済みのリンクにおけるベースURL
  final String linkIdBaseUrl;

  /// SMBC法人用アプリ（iOS）のストアページURL
  final String corporationAppAppStorePageUrl;

  /// SMBC法人用アプリ（Android）のストアページURL
  final String corporationAppPlayStorePageUrl;

  /// 暗号化Cookieを取得する関数
  Future<EncryptedCookie> Function() getEncryptedCookie;

  // KARTE送信用リポジトリ
  final AnalyticsLogController analyticsLogController;

  UrlLauncher({
    required this.linkIdBaseUrl,
    required this.corporationAppAppStorePageUrl,
    required this.corporationAppPlayStorePageUrl,
    required this.getEncryptedCookie,
    required this.analyticsLogController,
  });

  /// URL文字列からそのページが開けるかどうか判定する
  Future<bool> canLaunch(String url) async {
    return canLaunchUrl(Uri.parse(url));
  }

  /// urlに暗号化Cookieのクエリパラメータ(ticket)を追加する
  Future<Uri> _appendTicket(String url) async {
    const encryptKeyName = 'ticket';
    final encryptedCookie = await getEncryptedCookie();
    final original = Uri.parse(url);
    if (original.queryParameters[encryptKeyName] != null) {
      // 設定済みであればそのまま
      return original;
    } else {
      // 暗号化Cookie情報をクエリパラメータに追加する
      return Uri.parse(
        '$original${original.hasQuery ? '&' : '?'}$encryptKeyName=${encryptedCookie.ticket}',
      );
    }
  }

  /// URL文字列から外部ブラウザでページを開く
  /// useEncryptedCookieにtrueを設定すると、クエリパラメータに"encrypt"を追加する
  /// HACK 引数が多いので名前付き引数に変更
  Future<void> launchExternalUrl(
    String buttonName,
    String screenIdNumber,
    String url, {
    bool useEncryptedCookie = false,
  }) async {
    analyticsLogController.sendTrack(
      buttonName: buttonName,
      screenIdNumber: screenIdNumber,
    );
    final targetUri =
        useEncryptedCookie ? await _appendTicket(url) : Uri.parse(url);
    await launchUrl(
      targetUri,
      mode: LaunchMode.externalApplication,
    );
  }

  /// 法人アプリのストアページを開く
  Future<void> launchCorporationAppStorePage(
    String buttonName,
    String screenIdNumber,
  ) async {
    // AppStoreの法人アプリページ or GooglePlayStoreの法人アプリページを開く
    await launchExternalUrl(
      buttonName,
      screenIdNumber,
      Platform.isIOS
          ? corporationAppAppStorePageUrl
          : corporationAppPlayStorePageUrl,
    );
  }

  /// リンクIDとして定義済みのリンクを開く
  /// HACK 引数が多いので名前付き引数に変更
  Future<void> launchLinkId(
    LinkIds linkId,
    String buttonName,
    String screenIdNumber,
  ) async {
    await launchExternalUrl(
      buttonName,
      screenIdNumber,
      '$linkIdBaseUrl?id=${linkId.key}',
    );
  }

  /// リンクIDとして定義済みのurlを返す
  String getLinkIdUrl(LinkIds linkId) {
    return '$linkIdBaseUrl?id=${linkId.key}';
  }
}

final urlLauncherProvider = Provider<UrlLauncher>((ref) {
  final bc = ref.watch(buildConfigProvider);
  // 最新の物を取得する為、encryptedCookie更新時にはインスタンスを再生成する
  final md = ref.watch(mdManagerProvider);
  final analyticsLogController = ref.read(analyticsLogControllerProvider);
  return UrlLauncher(
    linkIdBaseUrl: bc.linkIdBaseUrl,
    corporationAppAppStorePageUrl: bc.corporationAppAppStorePageUrl,
    corporationAppPlayStorePageUrl: bc.corporationAppPlayStorePageUrl,
    getEncryptedCookie: md.encryptedCookie.load,
    analyticsLogController: analyticsLogController,
  );
});

enum LinkIds {
  /// WA01_04I FAQ画面_パスワードをお忘れの場合
  forgotVdidPassword('WA01_04I'),

  /// WA01_05I FAQ画面_ValueDoorIDに関するよくあるお問い合わせ
  valueDoorIDFaq('WA01_05I'),

  /// WA01_06I 法人口座開設についてくわしくはこちら
  hojinSpecialKouza('WA01_06I'),

  /// WA01_07I 個人情報の取り扱いについて
  personalInformationPolicy('WA01_07I'),

  /// WA01_08I クッキー等の使用について
  useCookie('WA01_08I'),

  /// WA02_01I SMBCBUSINESS利用規定
  smbcBusinessTerm('WA02_01I'),

  /// WA02_02I 個人情報の取扱いについて
  aboutPersonalInfo('WA02_02I'),

  /// WA02_03I 配信停止方法はこちら
  unsubscribeHere('WA02_03I'),

  /// WA02_04I SMBCBUSINESS利用規定(JPKI/eKYC本人確認)
  jpkiEkycSmbcBusinessTerm('WA02_04I'),

  /// WA03_01I よくある質問
  faq('WA03_01I'),

  /// WA03_02I freee連携FAQ
  freeeLinksFaq('WA03_02I'),

  /// WA04_01I プライバシーポリシー
  privacyPolicy('WA04_01I'),

  /// WA04_02I SMBC BUSINESS利用規定（マイページ画面）
  termsOfUse('WA04_02I'),

  /// WA04_03I FAQトップ
  faqTop('WA04_03I'),

  /// WA05_03E 上記を試してもエラーになる場合は
  cardNotReadingExplain('WA05_03E'),

  /// WA07_03I freee初回連携モーダル 代表者
  freeeFistLogin('WA07_03I'),

  /// WA07_04I freee初回連携モーダル 従業員
  freeeFistLoginEmployee('WA07_04I'),

  /// WA08_01E 機種ごとのカード読み取り位置
  cardReadingPositionExplain('WA08_01E'),

  /// WA08_02E 署名用電子証明書用暗唱番号を忘れた場合
  forgotPassword('WA08_02E'),

  /// WC06_01I Business Navi
  businessNavi('WC06_01I'),

  /// WC06_02E DX-link
  dxLink('WC06_02E'),

  /// WC06_03I 外国為替情報
  fxInformation('WC06_03I'),

  /// WC06_04I 経済・業界動向レポート
  businessTrendReport('WC06_04I'),

  /// WC06_05I 各国資料
  nationalMaterials('WC06_05I'),

  /// WW03_01IDTP専用FAQページ
  bankAccountFaq('WW03_01I');

  const LinkIds(this.key);

  final String key;
}
