class ScreenIdNumber {
  //未定義のID
  static const String undecidedId = 'XXXXX';

  //ホーム
  static const String homeScreenId = 'HMH01A';

  //利用規定同意
  static const String termsScreenId = 'LGT01A';

  //口座表示設定
  static const String bankAccountDisplayConfigScreenId = 'HMB01A';

  //取引口座照会
  static const String accountInquiryScreenId = 'BAB01A';

  //残高照会
  static const String balanceDetailsScreenId = 'BAB02A';

  //入出金明細
  static const String incomeAndExpenditureScreenId = 'BAS01A';

  //入出金明細詳細
  static const String incomeAndExpenditureDetailScreenId = 'BAS02A';

  //入出金明細絞り込み(自行)
  static const String incomeAndExpenditureNarrowDownScreenId = 'BAS03A';

  //入出金明細絞り込み(他行)
  static const String incomeAndExpenditureNarrowDownFreeeScreenId = 'BAS05A';

  //入出金明細連携口座
  static const String incomeAndExpenditureFreeeScreenId = 'BAS04A';

  //マイページ
  static const String myPageScreenId = 'UAA01A';

  //ログイン前ホーム
  static const String beforeLoginScreenId = 'ACH01A';

  //マイナンバーカードパスワード入力
  static const String passwordInputScreenId = 'IVJ01A';

  //証明書選択
  static const String identityDocumentScreenId = 'IVR05A';

  //カード読み取り
  static const String cardReaderScreenId = 'IVJ02A';

  //利用規約同意
  static const String jpkiTermsScreenId = 'IVT01A';

  //お手続き番号入力
  static const String identityReferenceNumberScreenId = 'IVR01A';

  //QRコード読み取り
  static const String identifyQrReaderScreenId = 'IVR02A';

  //ログイン
  static const String loginScreenId = 'LGL02A';

  //署名用電子証明書暗証番号説明
  static const String passwordInputExplanationScreenId = 'IVJ03A';

  //クレジットカード利用明細
  static const String cardDetailScreenId = 'CCS01A';

  //クレジットカード切替
  static const String cardSwitchModalId = 'CCS02A';

  //クレジットカード絞り込み
  static const String cardNarrowDownModalId = 'CCS03A';

  //読み取り時の注意事項説明モーダル
  static const String scanAttentionInfoModalId = 'IVJ08A';

  //カード読み取り準備完了モーダル
  static const String cardReaderSetupModalId = 'IVJ04A';

  //カード読み取り中モーダル
  static const String cardReaderReadingModalId = 'IVJ05A';

  //カード読み取り完了モーダル
  static const String cardReaderSuccessModaId = 'IVJ06A';

  //eKYC概要説明
  static const String ekycExplanationScreenId = 'IVE01A';

  //口座開設申込済み
  static const String accountOpeningSubmittedScreenId = 'IVR09A';

  //本人確認実施済み
  static const String verifiedUserScreenId = 'IVC01A';

  //本人情報（名前）入力画面
  static const String nameScreenId = 'IVR06A';

  //本人情報（役職）入力画面
  static const String positionScreenId = 'IVR07A';

  //本人情報（生年月日）入力画面
  static const String dateOfBirthScreenId = 'IVR08A';

  //本人情報（自宅）入力画面
  static const String addressScreenId = 'IVR11A';

  //本人情報（郵便番号）入力画面
  static const String postCodeScreenId = 'IVR12A';

  //本人情報送信完了画面(手続き者)
  static const String finishedTosendInfoScreenId = 'IVC02A';

  //本人情報送信完了画面(実質的支配者)
  static const String finishedTosendInfoBeneficiaryScreenId = 'IVC03A';

  //振込画面
  static const String paymentScreenId = 'WBW01A';

  //承認画面
  static const String approvalScreenId = 'WBW02A';

  //ID管理画面
  static const String idLinkageScreenId = 'UAI01A';

  //freee 連携説明 STEP2
  static const String freeeLinkStep2ModalId = 'FLL03A';

  //freee freeeIDログイン説明
  static const String freeeLinkLoginModalId = 'FLL05A';

  //PW再設定メールアドレス入力画面
  static const String idaasResetPWInputEmailScreenId = 'IDM02003G001';

  //OTP入力画面
  static const String otpInputScreenId = 'OTP01001G001';

  //アカウント作成画面
  static const String accountCreateWebViewScreenId = 'IDM01001G001';

  //PW変更画面
  static const String passwordChangeWebViewScreenId = 'IDM02002G001';

  //メールアドレス変更画面
  static const String emailChangeWebViewScreenId = 'IDM02001G001';

  //Web21認可画面
  static const String web21AuthWebViewScreenId = 'WBW03A';

  //ekyc終了
  static const String ekycWebViewCompleteScreenId = 'IVE16A';

  //TODO:以下11.21現在で使用が確認されなかった画面番号。必要ないことが完全に決定次第削除（PBI未定）
  // "screenNumberOfIdentifyDocument": "IVE01A",
  // "screenNumberOfIncomeAndExpenditureNarrowDownFreee":"BAS05A",
  // "screenNumberOfLicense":"UAL01A",
  // "screenNumberOfIdentifyRepresentative": "IVR03A",
  // "screenNumberOfIdentifyTrader": "IVR04A",
}
