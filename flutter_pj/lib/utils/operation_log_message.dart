/// Auroraに保存する操作ログの設定内容を定義するクラス
abstract class OperationLogMessage {
  // ログイン
  static const login = 'ログイン';
  static const vdId = 'VDID';
  static const dtpId = 'DTPID';

  // 利用規定同意
  static const termsOfUse = '利用規定';
  static const agree = '同意';
  static const disagree = '同意しない';

  // メニュー
  static const home = 'ホーム';
  static const accountInquiry = '取引口座照会';
  static const inquiry = '照会';
  static const payment = '振込';
  static const paymentSSO = '振込SSO';

  // ホーム
  static const freeeIntegration = 'freee 連携';
  static const freeeReIntegration = 'freee 再連携';
  static const freeeAddAccount = 'freee 口座追加';
  static const freeeAddCard = 'freee カード追加';

  // マイページ
  static const myPage = 'マイページ';
  static const logout = 'ログアウト';

  // ID管理
  static const idLinkage = 'ID管理';
  static const link = '連携';
  static const unlink = '連携解除';

  // 結果
  static const normal = '正常';
  static const abnormality = '異常';
  static const smbcBankTransactions = '自行口座入出金明細';
  static const otherBankTransactions = '他行口座入出金明細';
}
