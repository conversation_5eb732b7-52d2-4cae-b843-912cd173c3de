import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FaqContent extends HookConsumerWidget {
  /// 関連するFAQはこちら
  const FaqContent({
    super.key,
    required this.linkId,
    required this.buttonName,
    required this.screenId,
    required this.screenName,
    required this.icon,
    this.fontSize,
    this.padding,
  });

  /// リンクID
  final LinkIds linkId;

  /// ボタン名
  final String buttonName;

  /// 画面Id
  final String screenId;

  /// 画面名
  final String screenName;

  /// アイコン
  final String icon;

  // フォントサイズ
  final double? fontSize;

// padding指定
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return Padding(
      padding: padding ?? EdgeInsets.only(left: 32),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            AppLocalizations.of(context)!.relatedFAQ,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontSize: fontSize ?? 16,
                ),
          ),
          GestureDetector(
            onTap: () {
              // FirebaseAnalyticsログ送信
              logController.sendButtonLog(
                buttonName: buttonName,
                screenName: screenName,
              );

              ref
                  .read(urlLauncherProvider)
                  .launchLinkId(linkId, buttonName, screenId);
            },
            child: Text(
              AppLocalizations.of(context)!.here,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: fontSize ?? 16,
                    color: AppColors.tradGreen,
                    decoration: TextDecoration.underline,
                    decorationColor: AppColors.tradGreen,
                  ),
            ),
          ),
          const SizedBox(width: 4),
          SvgPicture.asset(icon),
        ],
      ),
    );
  }
}
