// エラーパターン一覧

enum ErrorPattern {
  // パターン1(サービス利用不可1)
  errorPattern1,
  // パターン2(処理に失敗)
  errorPattern2,
  // パターン4(カード読み取り失敗)
  errorPattern4,
  // パターン5(パスワード間違い)
  errorPattern5,
  // パターン6(パスワードロック)
  errorPattern6,
  // パターン9(処理に失敗)
  errorPattern9,
  // パターン10(eKYCWebView警告)
  errorPattern10,
  // パターン11(法人Web閉局時)
  errorPattern11,
  // パターン12(NFC設定オフ)
  errorPattern12,
  // パターン13(業務閉塞ログイン前ホーム)
  errorPattern13,
  // 当てはまらないパターン(暫定で追加)
  other,
}

// ダイアログで表示するテキストのクラス
class DialogErrorTexts {
  final String dialogTitle;
  final String dialogContent;
  final String dialogButtonText1;
  final String dialogButtonText2;

  DialogErrorTexts(
    this.dialogTitle,
    this.dialogContent,
    this.dialogButtonText1,
    this.dialogButtonText2,
  );
}

class ErrorInfoJpki {
  // JPKIエラーパターン情報
  // コードに対応するパターンを定義する
  static const Set<int> _commonErrorPattern1 = {503};
  static const Set<int> _commonErrorPattern2 = {401, 404, 405, 429, 500};
  static const Map<Set<int>, ErrorPattern> commonErrorPattern = {
    _commonErrorPattern1: ErrorPattern.errorPattern1,
    _commonErrorPattern2: ErrorPattern.errorPattern2,
  };

  static const Set<int> _jpkiOAuthApiErrorPattern1 = {
    400,
    401,
    404,
    405,
    422,
    429,
    500,
    503,
  };
  static const Map<Set<int>, ErrorPattern> jpkiOAuthApiErrorPattern = {
    _jpkiOAuthApiErrorPattern1: ErrorPattern.errorPattern1,
  };

  static const Set<int> _jpkiServiceCheckApiErrorPattern1 = {
    400,
    401,
    404,
    405,
    422,
    429,
    500,
    503,
  };
  static const Map<Set<int>, ErrorPattern> jpkiServiceCheckApiErrorPattern = {
    _jpkiServiceCheckApiErrorPattern1: ErrorPattern.errorPattern1,
  };

  static const Set<int> _jpkiIdentificationApiErrorPattern1 = {503};
  static const Set<int> _jpkiIdentificationApiErrorPattern2 = {
    400,
    401,
    404,
    405,
    422,
    429,
    500,
  };
  static const Map<Set<int>, ErrorPattern> jpkiIdentificationApiErrorPattern = {
    _jpkiIdentificationApiErrorPattern1: ErrorPattern.errorPattern1,
    _jpkiIdentificationApiErrorPattern2: ErrorPattern.errorPattern2,
  };

  // エラーパターンを取得する
  static ErrorPattern getErrorPattern(
    int httpErrorCode,
    Map<Set<int>, ErrorPattern> errorPattern,
  ) {
    // 引数で指定されたパターン一覧の中から合致するパターンを返却
    for (final i in errorPattern.keys) {
      if (i.contains(httpErrorCode)) {
        final result = errorPattern[i];
        if (result != null) return result;
      }
    }

    // 引数のパターンに存在しない場合、共通のエラーコードで合致するパターンがあれば返却
    for (final i in commonErrorPattern.keys) {
      if (i.contains(httpErrorCode)) {
        final result = commonErrorPattern[i];
        if (result != null) return result;
      }
    }

    // エラーパターンが取得できない場合
    return ErrorPattern.other;
  }
}

class ErrorInfoJpkiSdkIos {
  // SDK(iOS)のエラーパターン情報
  // コードに対応するパターンを定義する

  static const Set<String> _sdkIosErrorPattern4 = {
    'E_11_101',
    'E_11_103',
    'E_11_104',
    'E_15_301',
    'E_15_302',
    'E_15_303',
    'E_15_304',
    'E_15_305',
    'E_15_306',
    'E_15_307',
    'E_15_401',
    'E_15_402',
    'E_15_403',
    'E_15_404',
    'E_15_405',
    'E_15_406',
    'E_15_407',
    'E_21_302',
    'E_21_303',
    'E_21_402',
    'E_22_301',
    'E_23_302',
    'E_24_302',
    'E_25_301',
    'E_25_302',
  };

  static const Set<String> _sdkIosErrorPattern5 = {
    'E_21_201',
    'E_21_403',
  };

  static const Set<String> _sdkIosErrorPattern6 = {
    'E_21_402',
    'E_21_403',
  };

  // エラーパターンを取得する
  static ErrorPattern getErrorPattern({
    String? errorCode,
    String? details,
  }) {
    // 引数で指定されたパターン一覧の中から合致するパターンを返却
    // detailsが6984の時はエラーパターン6になる
    if (_sdkIosErrorPattern4.contains(errorCode) && details != '6984') {
      return ErrorPattern.errorPattern4;
    }

    if (_sdkIosErrorPattern5.contains(errorCode) && details != '6984') {
      return ErrorPattern.errorPattern5;
    }

    if (_sdkIosErrorPattern6.contains(errorCode)) {
      return ErrorPattern.errorPattern6;
    }

    // どのエラーパターンにも属さないエラーコードはエラーパターン4を出す
    return ErrorPattern.errorPattern4;
  }
}

class ErrorInfoJpkiSdkAndroid {
  // SDK(Android)のエラーパターン情報
  // コードに対応するパターンを定義する
  static const Set<String> _sdkAndroidErrorPattern2 = {
    'M.ERR.COM.5',
    'M.ERR.COM.6',
    'M.ERR.F_JLA_JPK_01.4',
    'M.ERR.F_JLA_JPK_01.5',
    'M.ERR.F_JLA_JPK_02.7',
    'M.ERR.F_JLA_JPK_03.7',
    'M.ERR.F_JLA_JPK_04.7',
    'M.ERR.F_JLA_JPK_05.7',
    'M.ERR.F_JLA_03.1',
  };

  static const Set<String> _sdkAndroidErrorPattern4 = {
    'M.ERR.COM.1',
    'M.ERR.COM.3',
    'M.ERR.COM.4',
    'M.ERR.F_JLA_JPK_02.4',
    'M.ERR.F_JLA_JPK_02.5',
    'M.ERR.F_JLA_JPK_02.6',
    'M.ERR.F_JLA_JPK_03.4',
    'M.ERR.F_JLA_JPK_03.5',
    'M.ERR.F_JLA_JPK_03.6',
    'M.ERR.F_JLA_JPK_07.4',
    'M.ERR.F_JLA_03.2',
    'M.ERR.F_JLA_03.3',
  };

  static const Set<String> _sdkAndroidErrorPattern5 = {
    'M.ERR.F_JLA_JPK_01.1',
    'M.ERR.F_JLA_JPK_01.2',
    'M.ERR.F_JLA_JPK_02.1',
    'M.ERR.F_JLA_JPK_02.2',
    'M.ERR.F_JLA_JPK_03.1',
    'M.ERR.F_JLA_JPK_03.2',
    'M.ERR.F_JLA_JPK_07.1',
    'M.ERR.F_JLA_JPK_07.2',
  };

  static const Set<String> _sdkAndroidErrorPattern6 = {
    'M.ERR.F_JLA_JPK_01.3',
    'M.ERR.F_JLA_JPK_02.3',
    'M.ERR.F_JLA_JPK_03.3',
    'M.ERR.F_JLA_JPK_07.3',
  };

  // エラーパターンを取得する
  static ErrorPattern getErrorPattern({
    int? retryCounter,
    String? errorCode,
  }) {
    // 引数で指定されたパターン一覧の中から合致するパターンを返却
    if (_sdkAndroidErrorPattern2.contains(errorCode)) {
      return ErrorPattern.errorPattern2;
    }

    if (_sdkAndroidErrorPattern4.contains(errorCode)) {
      return ErrorPattern.errorPattern4;
    }

    if (_sdkAndroidErrorPattern5.contains(errorCode)) {
      return ErrorPattern.errorPattern5;
    }

    if (_sdkAndroidErrorPattern6.contains(errorCode)) {
      return ErrorPattern.errorPattern6;
    }

    // どのエラーパターンにも属さないエラーコードはエラーパターン4を出す
    return ErrorPattern.errorPattern4;
  }
}
