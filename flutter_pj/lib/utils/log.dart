import 'package:logger/logger.dart';

var _logger = Logger();

final prettyPrinter = PrettyPrinter();

abstract class Log {
  /// Log a message at level [Level.trace]
  /// 一時的な検証用に使い、用が済んだら削除する
  static void v(dynamic message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.t(message, error: error, stackTrace: stackTrace);

  /// Log a message at level [Level.debug].
  /// 定常的なデバッグログを出力する
  static void d(dynamic message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.d(message, error: error, stackTrace: stackTrace);

  /// Log a message at level [Level.info].
  /// 開発者向けのメッセージを出力する
  static void i(dynamic message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.i(message, error: error, stackTrace: stackTrace);

  /// Log a message at level [Level.warning].
  /// エラーではない異常系処理のログを出力する
  static void w(dynamic message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.w(message, error: error, stackTrace: stackTrace);

  /// Log a message at level [Level.error].
  /// エラーログを出力する
  static void e(dynamic message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.e(message, error: error, stackTrace: stackTrace);

  /// Log a message at level [Level.fatal].
  /// システムレベルの深刻なエラーログを出力する
  static void wtf(dynamic message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.f(message, error: error, stackTrace: stackTrace);
}

/// ログをシンプルモード（1行表示）にする
void setSimpleLoggingMode(bool value) {
  if (value) {
    _logger = Logger(printer: _SimpleLogPrinter());
  } else {
    _logger = Logger();
  }
}

/// filterFlags: ログにフィルターを設定する
/// 文字列内の文字に応じて、そのレベルのログだけを表示する
/// t, d, i, w, e (fは常に表示)
/// simpleMode: ログをシンプルモード（1行表示）にする
void setLoggingMode({
  String filterFlags = '',
  bool simpleMode = false,
}) {
  final logger = Logger(
    filter: _LevelFilter(
      shouldShowTrace: filterFlags.contains('t'),
      shouldShowDebug: filterFlags.contains('d'),
      shouldShowInfo: filterFlags.contains('i'),
      shouldShowWarning: filterFlags.contains('w'),
      shouldShowError: filterFlags.contains('e'),
    ),
    printer: simpleMode ? _SimpleLogPrinter() : null,
  );
  _logger = logger;
}

/// 開発時用（ログを一行で表示可能）
class _SimpleLogPrinter extends LogPrinter {
  _SimpleLogPrinter();

  @override
  List<String> log(LogEvent event) {
    final color = prettyPrinter.levelColors![event.level];
    final emoji = prettyPrinter.levelEmojis![event.level];
    return [color!('$emoji${event.message}')];
  }
}

/// ログをレベルに応じてフィルタリングする
class _LevelFilter extends LogFilter {
  _LevelFilter({
    this.shouldShowTrace = true,
    this.shouldShowDebug = true,
    this.shouldShowInfo = true,
    this.shouldShowWarning = true,
    this.shouldShowError = true,
  });

  final bool shouldShowTrace;
  final bool shouldShowDebug;
  final bool shouldShowInfo;
  final bool shouldShowWarning;
  final bool shouldShowError;

  @override
  bool shouldLog(LogEvent event) {
    return switch (event.level) {
      Level.trace => shouldShowTrace,
      Level.debug => shouldShowDebug,
      Level.info => shouldShowInfo,
      Level.warning => shouldShowWarning,
      Level.error => shouldShowError,
      Level.fatal => true,
      Level.off => true,
      Level.all => true,
      _ => true
    };
  }
}
