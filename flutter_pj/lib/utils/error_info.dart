class ErrorInfo {
  //エラーコード
  /// 通信エラー(強制アップデート読み込み)
  static const String forceUpdateConnectionErrorCode = 'I006-00001';

  /// レスポンス欠如(強制アップデート読み込み)
  static const String brokenResponseErrorCode = 'E006-00001';

  /// 内部処理エラー(強制アップデートファイル読み込み)
  static const String forceUpdateInternalErrorCode = 'E006-00002';

  /// セッションタイムアウトエラー(ヘッダーにセッションがない)
  static const String noSessionErrorCode = 'I005-00001';

  /// セッションタイムアウトエラー(TTL切れ)
  static const String sessionTimeOutErrorTTLCode = 'I005-00002';

  /// セッションタイムアウトエラー(共通)(有効期限切れ)
  static const String sessionTimeOutErrorCode = 'I005-00003';

  /// 認可バックエンドエラー
  static const String authBackEndErrorCode = 'E005-00002';

  /// 認可拒否履歴あり
  static const String notAuthenticatedErrorCode = 'I005-00005';

  /// freee再連携(DTPとfreee)
  static const List<String> freeeNeedsReAlignmentErrorCode = [
    'W001-00005',
    'W003-00001',
    'W003-00002',
    'I003-00004',
    'I003-00006',
    'I003-00007',
    'E003-00008',
    'E003-00009',
  ];

  /// freee再連携(金融機関とfreee)
  static const String freeeReConnectFinancialInstitutionErrorCode =
      'I005-00014';

  /// 該当住所なし
  static const String notFoundAddressErrorCode = 'I005-00007';

  /// ID紐付け情報登録失敗
  static const String idLinkageInformationRegistrationErrorCode = 'E011-00001';

  /// セッションタイムアウト判断用配列
  /// セッションタイムアウトのエラーコードが追加された際にはここに追記していく
  static const List<String> sessionTimeOutCodeList = [
    ErrorInfo.noSessionErrorCode,
    ErrorInfo.sessionTimeOutErrorTTLCode,
    ErrorInfo.sessionTimeOutErrorCode,
  ];

  // エラーメッセージ
  /// 口座情報が0件
  static const String noAccountErrorMessage = '口座情報へのアクセスが許可されていません。';

  /// 共通エラー(意図しないエラー含む)
  static const String defaultErrorCode = 'I006-00035';
  static const String defaultErrorMessage =
      '処理に失敗しました。お手数ですが、しばらく時間をあけてから再度お試しください。';

  /// パスワード変更エラー
  static const String changePasswordMessage =
      'ValueDoorIDのパスワードが初期パスワードのため、ValueDoorにログインのうえパスワードを変更してください。\n\n';

  /// パスワード有効期限切れエラー
  static const String passwordExpirationNoticeMessage =
      'ValueDoorIDのパスワード有効期限を超過していますので、ValueDoorにログインのうえパスワードを変更してください。\n\n';

  /// OTP有効化（スキップ）エラー
  static const String enableOtpLaterMessage = 'ワンタイムパスワードの有効化に失敗しました。';

  /// freeeと金融機関の連携エラー
  static const String freeeReConnectFinancialInstitutionErrorMessage =
      '金融機関との連携が切れています。再連携してください。';

  /// 指定がない際のエラーダイアログタイトル
  static const String defaultErrorTitle = '処理に失敗しました';

  /// エラーダイアログタイトルを表示する場合、同様の文言は句読点を含めて削除。(ph0.5 UAT指摘対応)
  static const String deletionTargetOfDuplicationToTitle = '処理に失敗しました。';

  //エラーメッセージを取得するメソッド
  static String getErrorMessage(String errorCode) {
    final String value;
    switch (errorCode) {
      case forceUpdateConnectionErrorCode:
      case brokenResponseErrorCode:
      case forceUpdateInternalErrorCode:
        value = '処理に失敗しました。お手数ですが、しばらく時間をあけてから再度お試しください。';
        break;
      default:
        //想定外の値の場合は空白を返す
        value = '';
    }
    return value;
  }
}

/// ログイン画面に表示するエラー情報
class LoginErrorInfo {
  /// ログイン不可ユーザが、DTPIDでDTPにログインした際のエラーコード（文言はdefaultErrorMessageを表示）
  static const smbcBusinessIdLoginWithInvalidVdidErrorCode = 'I016-00126';

  /// 生体認証失敗エラー
  static const biometricsFailureErrorCode = 'I006-00017';
  static const biometricsFailureErrorMessage =
      '生体認証に失敗しました。再度認証を行うか、ID・PWでのログインをお願いします。';

  /// PW変更時のFaqテキスト
  static const String changePwFaqText = 'ValueDoorIDのパスワード変更手順（FAQ）';

  /// vdOtp認証エラー(トークン)（有効化未済）
  static const String vdOtpExpirationTokenError =
      'ワンタイムパスワードカードまたはカメラ付きトークンをご利用いただくためには、初期設定（有効化）が必要です。詳細はFAQをご確認ください。';

  /// vdOtp認証エラー（アプリ）（有効化未済）
  static const String vdOtpExpirationAppError =
      'ワンタイムパスワードをご利用いただくためには、法人用アプリの初期設定（有効化）が必要です。詳細はFAQをご確認ください。';

  /// vdOtp認証エラー（カード不着）
  static const String vdOtpCardNotReceivedError =
      'お申込みいただきましたワンタイムパスワードカードを郵送しましたが、下記理由により不着となっております。詳細はFAQをご確認ください。\n不着理由：';

  /// vdOtp認証エラー（通知書不着）
  static const String vdOtpAppNotReceivedError =
      'お申込みいただきましたワンタイムパスワードアプリの二次元コード通知書を郵送しましたが、下記理由により不着となっております。詳細はFAQをご確認ください。\n不着理由：';

  /// vdOtp認証エラー（トークン不着）
  static const String vdOtpTokenNotReceivedError =
      'お申込みいただきましたカメラ付きトークンを郵送しましたが、下記理由により不着となっております。詳細はFAQをご確認ください。\n不着理由：';
}

class OtpHuchakueRsnInfo {
  static const String addresUnknouwn = 'あて先に尋ねあたりません';
  static const String periodPasses = '留置期間経過';
  static const String noTransferRequired = '転送不要';
  static const String relocation = '転居';
  static const String incomleteDestination = 'あて先不完全';
  static const String roomNumberUnknown = '棟室番号もれ';
  static const String relocationUnknown = '転居先不明';
  static const String other = 'その他';
}

/// 振込画面に表示するエラー情報
class PaymentErrorInfo {
  /// 法人用アプリにて処理が中断された際のエラー(OTP認証)
  static const String cancelTimeErrorCode = 'I006-00018';
  static const String cancelTimeErrorMessage =
      '処理がキャンセルされました。お手数ですが、再度ワンタイムパスワード発行してください。';

  /// 法人用アプリにて処理が中断された際のエラー(トランザクション認証)
  static const String cancelSignatureErrorCode = 'I006-00019';
  static const String cancelSignatureErrorMessage =
      '処理がキャンセルされました。お手数ですが、再度取引先情報を確認してください。';

  /// 振込時/承認時のVDサーバーエラー(OTP認証)
  static const String vdServerTimeErrorCode = 'I006-00020';
  static const String vdServerTimeErrorMessage =
      '現在、ワンタイムパスワードのお取り扱いができません。再度ワンタイムパスワード発行を実行してください。';

  /// 振込時/承認時のVDサーバーエラー(トランザクション認証)
  static const String vdServerSignatureErrorCode = 'I006-00129';
  static const String vdServerSignatureErrorMessage =
      '現在、ワンタイムパスワードのお取り扱いができません。再度取引情報確認を実行してください。';

  /// 振込時のotp初期化未済エラー
  static const String web21OtpNotInitializedErrorCode = 'I006-00021';
  static const String web21OtpNotInitializedErrorMessage =
      'ワンタイムパスワードが有効化されていません。';

  /// 振込時のOTPバリデーションエラー
  static const String web21OtpValidationErrorCode = 'I006-00022';
  static const String web21OtpValidationErrorMessage =
      'ワンタイムパスワードの設定に失敗しました。パスワードをご確認の上、手動でご入力ください。';

  /// 継続可能エラー (時刻OTP認証)
  static const String continuableTimeErrorCode = 'I006-00023';
  static const String continuableTimeErrorMessage =
      '処理に失敗しました。お手数ですが、再度ワンタイムパスワード発行してください。';

  /// 継続可能エラー (トランザクション認証)
  static const String continuableSignatureErrorCode = 'I006-00024';
  static const String continuableSignatureErrorMessage =
      '処理に失敗しました。お手数ですが、再度取引先情報を確認してください。';

  /// 継続不可エラー
  static const String uncontainableErrorCode = 'I006-00128';
  static const String uncontainableErrorMessage =
      '処理に失敗しました。お手数ですが、しばらく経ってから再度操作をお願いします。';

  /// 法人用アプリ遷移後の画面遷移エラー
  static const screenChangedErrorCode = 'I006-00025';
  static const screenChangedErrorMessage =
      '法人用アプリでワンタイムパスワードの発行操作をしている間は、SMBC BUSINESSアプリを操作せずそのままの状態でお待ちください。大変お手数ですが、振込ボタンより再度操作をお願い致します。';
}

class IdentityVerificationErrorInfo {
  /// 本人情報の保存失敗時エラーダイアログ
  static const identityInfoNotSavedErrorCode = 'I006-00016';
  static const identityInfoNotSavedErrorMessage =
      '本人確認情報が保存できませんでした。お手数ですが再度お手続き番号入力から実施してください。';

  /// QRコード読み込み失敗時のエラーダイアログ(お手続番号不正)
  static const invalidReferenceNumberErrorCode = 'I006-00027';
  static const invalidReferenceNumberErrorMessage =
      'お手続き番号に誤りがあります。正しいお手続き番号をご確認の上、再度ご入力ください。';

  /// QRコード読み取りできなかった際のエラー
  static const qrCodeValidationErrorCode = 'I006-00028';
  static const qrCodeValidationErrorMessage =
      '読み取りに失敗しました。二次元コードをお確かめの上、再度読み取ってください。';

  /// カメラ権限がない際のエラー
  static const permissionDeniedErrorCode = 'I006-00030';
  static const permissionDeniedErrorMessage =
      'カメラ機能へのアクセスが許可されていません。アクセス権限を見直してください。';

  /// QR機能がサポートされていない際のエラー
  static const qrUnsupportedErrorCode = 'I006-00031';
  static const qrUnsupportedErrorMessage =
      'QRコードスキャン機能がサポートされていません。お手数ですが、テキストボックスからのお手続き番号入力をお願いします。';
}

class MyPageErrorInfo {
  /// ユーザー情報取得API失敗時メッセージ
  static const String failedToLoadUserInfoCode = 'I006-00026';
  static const String failedToLoadUserInfoMessage =
      '処理に失敗しました。お手数ですが、ログインからやり直してください。';
  static const String failedToLoadUserInfoButtonText = 'ログイン画面へ';

  /// DTPID連携失敗時メッセージ
  static const String dtpIdCreationErrorDialogCode = 'I006-00029';
  static const String dtpIdCreationErrorDialogMessage =
      '大変お手数ですが、SMBC BUSINESSのWEBにログインいただき、マイページ＞ID管理画面よりIDの新規発行を完了させてください。';
}

class HomeNavigationErrorInfo {
  static const authErrorCode = 'I006-00127';
  static const authErrorMessage = 'ご利用には権限設定が必要です。';
}

class HomeErrorInfo {
  /// web21取引口座照会権限なしエラー
  static const noInquiryAuthErrorCode = 'I006-00032';
  static const noInquiryAuthErrorMessage = 'ご利用には権限設定が必要です。';

  /// freee連携エラー
  static const freeeTokenStatusErrorCode = 'I006-00008';
}

class HomeErrorWithOutServiceInfo {
  /// web21サービス時間外や、企業コードが存在しない場合
  static const withOutServiceErrorCode = 'I005-00006';
}

// 口座情報に関するエラー(主にHOME、取引口座照会にて使用)
class BankAccountErrorInfo {
  /// 口座残高、他店内手形などの桁数超過エラー
  static const overflowDigitError = '桁数超過エラー';

  /// Web21再認可ボタン表示対象エラー(認可拒否履歴有り除く)
  /// HACK 認可拒否履歴ありの場合も同様の手法でハンドリングを実施
  static const web21ReauthorizationErrorCodes = [
    'W001-00005',
    'I002-00026',
    'I002-00027',
    'I002-00047',
    'E002-00048',
    'I002-00050',
    'E002-00051',
    'E002-00052',
    'E002-00055',
    'I002-00058',
    'I002-00060',
    'I002-00075',
  ];
}

//　口座表示設定に関するエラー
class DisplayConfigErrorInfo {
  static const otherBankAccountExceededErrorMessage = '連携口座は最大15口座選択可能です。';
  static const smbcAccountExceededErrorMessage = '三井住友銀行口座は最大5口座選択可能です。';
  static const allOtherBankAccountInvisibleErrorMessage = '連携口座を1つ以上選択してください。';
  static const allSMBCAccountInvisibleErrorMessage = '三井住友銀行の口座を1つ以上選択してください。';
}
