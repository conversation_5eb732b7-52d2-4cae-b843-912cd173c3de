class KatakanaToHepburn {
  // カタカナを変換する表
  static const Map<String, String> _hepburnMap = {
    // 外国人氏名
    'イェ': 'IE',
    'ウィ': 'UI',
    'ウェ': 'UE',
    'ウォ': 'UO',
    'ヴァ': 'BUA',
    'ヴィ': 'BUI',
    'ヴ': 'BU',
    'ヴェ': 'BUE',
    'ヴォ': 'BUO',
    'クァ': 'KUA',
    'クィ': 'KUI',
    'クェ': 'KUE',
    'クォ': 'KUO',
    'グァ': 'GUA',
    'グィ': 'GUI',
    'グェ': 'GUE',
    'グォ': 'GUO',
    'ジェ': 'JIE',
    'チェ': 'CHIE',
    'ツァ': 'TSUA',
    'ツィ': 'TSUI',
    'ツェ': 'TSUE',
    'ツォ': 'TSUO',
    'ティ': 'TEI',
    'ディ': 'DEI',
    'デュ': 'DEYU',
    'ドゥ': 'DOU',
    'ファ': 'FUA',
    'フィ': 'FUI',
    'フェ': 'FUE',
    'フォ': 'FUO',
    'フョ': 'FUYO',
    // 拗音
    'キャ': 'KYA',
    'キュ': 'KYU',
    'キョ': 'KYO',
    'シャ': 'SHA',
    'シュ': 'SHU',
    'ショ': 'SHO',
    'チャ': 'CHA',
    'チュ': 'CHU',
    'チョ': 'CHO',
    'ニャ': 'NYA',
    'ニュ': 'NYU',
    'ニョ': 'NYO',
    'ヒャ': 'HYA',
    'ヒュ': 'HYU',
    'ヒョ': 'HYO',
    'ミャ': 'MYA',
    'ミュ': 'MYU',
    'ミョ': 'MYO',
    'リャ': 'RYA',
    'リュ': 'RYU',
    'リョ': 'RYO',
    'ギャ': 'GYA',
    'ギュ': 'GYU',
    'ギョ': 'GYO',
    'ジャ': 'JA',
    'ジュ': 'JU',
    'ジョ': 'JO',
    'ビャ': 'BYA',
    'ビュ': 'BYU',
    'ビョ': 'BYO',
    'ピャ': 'PYA',
    'ピュ': 'PYU',
    'ピョ': 'PYO',
    // 通常の50音
    'ア': 'A',
    'イ': 'I',
    'ウ': 'U',
    'エ': 'E',
    'オ': 'O',
    'カ': 'KA',
    'キ': 'KI',
    'ク': 'KU',
    'ケ': 'KE',
    'コ': 'KO',
    'サ': 'SA',
    'シ': 'SHI',
    'ス': 'SU',
    'セ': 'SE',
    'ソ': 'SO',
    'タ': 'TA',
    'チ': 'CHI',
    'ツ': 'TSU',
    'テ': 'TE',
    'ト': 'TO',
    'ナ': 'NA',
    'ニ': 'NI',
    'ヌ': 'NU',
    'ネ': 'NE',
    'ノ': 'NO',
    'ハ': 'HA',
    'ヒ': 'HI',
    'フ': 'FU',
    'ヘ': 'HE',
    'ホ': 'HO',
    'マ': 'MA',
    'ミ': 'MI',
    'ム': 'MU',
    'メ': 'ME',
    'モ': 'MO',
    'ヤ': 'YA',
    'ユ': 'YU',
    'ヨ': 'YO',
    'ラ': 'RA',
    'リ': 'RI',
    'ル': 'RU',
    'レ': 'RE',
    'ロ': 'RO',
    'ワ': 'WA',
    'ヰ': 'I',
    'ヱ': 'E',
    'ヲ': 'O',
    'ン': 'NN',
    'ガ': 'GA',
    'ギ': 'GI',
    'グ': 'GU',
    'ゲ': 'GE',
    'ゴ': 'GO',
    'ザ': 'ZA',
    'ジ': 'JI',
    'ズ': 'ZU',
    'ゼ': 'ZE',
    'ゾ': 'ZO',
    'ダ': 'DA',
    'ヂ': 'JI',
    'ヅ': 'ZU',
    'デ': 'DE',
    'ド': 'DO',
    'バ': 'BA',
    'ビ': 'BI',
    'ブ': 'BU',
    'ベ': 'BE',
    'ボ': 'BO',
    'パ': 'PA',
    'ピ': 'PI',
    'プ': 'PU',
    'ペ': 'PE',
    'ポ': 'PO',
    'ァ': 'A',
    'ィ': 'I',
    'ゥ': 'U',
    'ェ': 'E',
    'ォ': 'O',
    'ッ': '',
  };

  static String convertToHepburn(String value) {
    final buffer = StringBuffer();
    for (int i = 0; i < value.length; i++) {
      final char = value[i];
      // 変換mapに存在しない場合には何もしない
      if (_hepburnMap.containsKey(char)) {
        if (char == 'ッ') {
          // 促音があった場合の処理
          if (i + 1 < value.length) {
            final nextChar = value[i + 1];
            if (nextChar == 'チ') {
              // CHが次に来る側縁はTで表現
              buffer.write('T');
              continue;
            } else if (_hepburnMap.containsKey(nextChar)) {
              buffer.write(_hepburnMap[nextChar]![0]);
              continue;
            }
          }
        }
        // ンをMに変換する必要があるかを確認
        else if (char == 'ン') {
          if (i + 1 < value.length) {
            final nextChar = value[i + 1];
            if (_charactersChangeNtoM.contains(nextChar)) {
              buffer.write('M');
              continue;
            }
          }
          buffer.write('N');
          continue;
          // 長音の対応
        } else if (_vowels.contains(char) || char == 'ー') {
          if (char == 'ー') {
            // 何もしない
            continue;
          }
          if (i > 0) {
            final charAlphabet = _hepburnMap[char];
            final lastChar = value[i - 1];
            final lastAlphabet = buffer.toString()[buffer.length - 1];
            if (charAlphabet == 'O' && i == value.length - 1) {
              // 末尾のOは省略せずに記載
              buffer.write('O');
              continue;
            } else if (lastChar == 'ツ' && charAlphabet == 'U') {
              // 長音にならない特例(長音の前にツ)
              buffer.write('U');
              continue;
            } else if (i < value.length - 1 &&
                charAlphabet == 'U' &&
                lastAlphabet == 'O') {
              final nextChar = value[i + 1];
              if (nextChar == 'チ') {
                // 長音にならない特例(OU長音の次にチ)
                buffer.write(charAlphabet);
                continue;
              }
            } else if (charAlphabet == lastAlphabet ||
                // OUは長音として扱う(例：カトウ→KATO)
                (charAlphabet == 'U' && lastAlphabet == 'O')) {
              // 何もしない
              continue;
            }
          }

          buffer.write(_hepburnMap[char]);
        } else {
          // 拗音、外国人氏名の処理
          if (i + 1 < value.length) {
            final nextChar = value[i + 1];
            if (['ァ', 'ィ', 'ゥ', 'ェ', 'ォ', 'ャ', 'ュ', 'ョ'].contains(nextChar)) {
              final combined = char + nextChar;
              if (_hepburnMap.containsKey(combined)) {
                buffer.write(_hepburnMap[combined]);
                // 次の文字も含めて処理しているため、処理を一つ飛ばす
                i++;
                continue;
              }
            }
          }
          buffer.write(_hepburnMap[char]);
        }
      }
    }
    return buffer.toString();
  }
}

final List<String> _charactersChangeNtoM = [
  'バ',
  'ビ',
  'ブ',
  'ベ',
  'ボ',
  'パ',
  'ピ',
  'プ',
  'ペ',
  'ポ',
  'マ',
  'ミ',
  'ム',
  'メ',
  'モ',
];

final List<String> _vowels = ['ウ', 'オ'];
