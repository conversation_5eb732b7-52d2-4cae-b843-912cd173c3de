/// TODO input_validator_mixin.dartとの重複を確認しinput_validator_mixin.dartに移動
extension StringValidator on String {
  /// 与えられた文字列に絵文字が含まれている場合はtrueを返す
  bool containsEmoji() {
    return _emojiRegex.hasMatch(this);
  }

  /// 与えられた文字列に半角カナが含まれている場合はtrueを返す
  bool containsHalfWidthKana() {
    return _halfWidthKanaRegex.hasMatch(this);
  }

  /// 与えられた文字列に半角カナ、半角スペース、半角記号、半角英数字以外が含まれている場合はtrueを返す
  bool containsNonHalfWidth() {
    return RegExp(r'[^\u0020-\u007E\uFF61-\uFF9F]').hasMatch(this);
  }

  /// 与えられた文字列に数字以外(カンマ除く)が含まれている場合はtrueを返す
  bool containsNonDigit() {
    return !RegExp(r'^[\d,]+$').hasMatch(this);
  }

  /// 与えられた文字列にマイナスが含まれている場合はtrueを返す
  bool containsMinus() {
    return RegExp(r'-').hasMatch(this);
  }

  /// 金額の下限が上限より大きければtrueを返す
  bool checkAmountCorrelation({
    required String smallAmount,
    required String largeAmount,
  }) {
    final smallAmountNumeric = int.parse(smallAmount.replaceAll(',', ''));
    final largeAmountNumeric = int.parse(largeAmount.replaceAll(',', ''));
    return smallAmountNumeric > largeAmountNumeric;
  }

  // 金額の下限が上限より大きければtrueを返す（マイナス判定用）
  bool checkAmountMinusCorrelation({
    required String smallAmount,
    required String largeAmount,
  }) {
    final int smallAmountInt = convertString2Integer(intStr: smallAmount);
    final int largeAmountInt = convertString2Integer(intStr: largeAmount);
    return smallAmountInt > largeAmountInt;
  }

  // カンマが含まれる整数値文字列を整数値型に変換
  int convertString2Integer({
    required String intStr,
  }) {
    final bool isMinus = intStr[0] == '-';
    final String intStrBody = isMinus ? intStr.substring(1) : intStr;
    final int targetInt = int.parse(intStrBody.replaceAll(',', ''));

    return isMinus ? targetInt * (-1) : targetInt;
  }

  /// 与えられた文字列が半角数字8桁でなければtrueを返す
  bool isNotEightDigitNumber() {
    return !RegExp(r'^\d{8}$').hasMatch(this);
  }

  /// 与えられた文字列が適切なメールアドレスで無ければtrueを返す
  bool isNotValidEmailAddress() {
    return !RegExp(r'^[^@]+@[^@.]+\.[^@]+$').hasMatch(this);
  }

  /// 与えられた文字列に全角(ひらがな、カタカナ、漢字)以外が入った場合にtrueを返す
  bool checkContainsNonFullWidthKana() {
    return !RegExp(r'^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]+$')
        .hasMatch(this);
  }

  /// 与えられた文字列のなかに全角カナ以外が含まれる場合、trueを返す
  bool checkContainsNonFullWidthKatakana() {
    return !RegExp(r'^[\u30A0-\u30FF]+$').hasMatch(this);
  }

  /// 与えられた文字列に半角英字以外が含まれる場合、tureを返す
  bool checkContainsNonHalfWidthAlphabet() {
    return !RegExp(r'^[a-zA-Z]+$').hasMatch(this);
  }

  /// 与えられた文字列に半角数字以外が含まれる場合、trueを返す
  bool checkContainsNonHalfWidthNumber() {
    return !RegExp(r'^[0-9]+$').hasMatch(this);
  }

  /// 与えられた文字列に半角英数字以外が含まれる場合、trueを返す
  bool checkContainsNonHalfWidthAlphanumeric() {
    return !RegExp(r'^[a-zA-Z0-9]+$').hasMatch(this);
  }

  // 与えられた文字列に半角マイナスは先頭以外許容しない、以降の文字に数字以外(カンマ除く)を含んでいるか判定
  bool checkContainsDigitHalfMinus() {
    return !RegExp(r'^-?(?!,)[\d,]+$').hasMatch(this);
  }

  /// 与えられた文字列に半角英数字以外が含まれる場合、trueを返す
  /// TODO 現状使用されていないが使用される場合には対象とする記号を確認
  /// MEMO現状許容されている記号は[@_.-]の４つ
  bool checkContainsNonHalfWidthAlphanumericAndSymbol() {
    return !RegExp(r'^[a-zA-Z0-9@_.-]+$').hasMatch(this);
  }

  /// 与えられた文字列が有効な月では無い場合、trueを返す
  bool checkInvalidMonth() {
    try {
      final monthInt = int.parse(this);
      if (monthInt >= 1 && monthInt <= 12) {
        return false;
      } else {
        return true;
      }
    } catch (e) {
      // 万一数値以外が入力された際の対応
      return true;
    }
  }

  /// 与えられた文字列が有効な日では無い場合、trueを返す
  bool checkInvalidDay({String? month, String? year}) {
    try {
      final to31 = [1, 3, 4, 7, 8, 10, 12];
      final yearInt = int.parse(year!);
      final monthInt = int.parse(month!);
      final dayInt = int.parse(this);
      if (to31.contains(monthInt)) {
        if (dayInt >= 1 && dayInt <= 31) {
          return false;
        } else {
          return true;
        }
      } else if (monthInt == 2) {
        int dayTo = 28;
        // 閏年の場合
        if (yearInt % 400 == 0 || !(yearInt % 4 == 0 && yearInt != 0)) {
          dayTo = 29;
        }
        if (dayInt >= 1 && dayInt <= dayTo) {
          return false;
        } else {
          return true;
        }
      } else {
        if (dayInt >= 1 && dayInt <= 30) {
          return false;
        } else {
          return true;
        }
      }
    } catch (e) {
      // 万一数値以外が入力された際の対応
      return true;
    }
  }
}

final _emojiRegex = RegExp(
  r'[\u00A9\u00AE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9-\u21AA\u231A-\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA-\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614-\u2615\u2618\u261D\u2620\u2622-\u2623\u2626\u262A\u262E-\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u2660\u2663\u2665-\u2666\u2668\u267B\u267F\u2692-\u2697\u2699\u269B-\u269C\u26A0-\u26A1\u26AA-\u26AB\u26B0-\u26B1\u26BD-\u26BE\u26C4-\u26C5\u26C8\u26CE-\u26CF\u26D1\u26D3-\u26D4\u26E9-\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733-\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763-\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934-\u2935\u2B05-\u2B07\u2B1B-\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|(?:\uD83C[\uDC04\uDCCF\uDD70-\uDD71\uDD7E-\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01-\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50-\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96-\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F-\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95-\uDD96\uDDA4-\uDDA5\uDDA8\uDDB1-\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDEE0-\uDEE5\uDEE9\uDEEB-\uDEEC\uDEF0\uDEF3-\uDEF6]|\uD83E[\uDD10-\uDD1E\uDD20-\uDD27\uDD30\uDD33-\uDD3A\uDD3C-\uDD3E\uDD40-\uDD45\uDD47-\uDD4B\uDD50-\uDD5E\uDD80-\uDD91\uDDC0])',
);

final _halfWidthKanaRegex = RegExp(r'[\uFF61-\uFF9F]');
