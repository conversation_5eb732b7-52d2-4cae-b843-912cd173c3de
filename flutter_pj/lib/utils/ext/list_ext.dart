/// Listクラスの拡張メソッド
extension ListExt<T> on List<T> {
  /// ソートしたリストを返す
  List<T> sorted(int Function(T a, T b) compare) {
    final list = List<T>.from(this);
    list.sort(compare);
    return list;
  }

  /// ふたつの項目を並び替えたリストを返す
  List<T> reordered(int oldIndex, int newIndex) {
    final list = List<T>.from(this);
    list.insert(newIndex, list.removeAt(oldIndex));
    return list;
  }

  /// インデックスつきのmapメソッド
  List<R> mapIndexed<R>(R Function(T element, int index) mapper) {
    final list = <R>[];
    for (var i = 0; i < length; ++i) {
      list.add(mapper(this[i], i));
    }
    return list;
  }
}
