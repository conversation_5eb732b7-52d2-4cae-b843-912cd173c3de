extension NullableStringExt on String? {
  bool get isNullOrEmpty {
    return this?.isEmpty ?? true;
  }

  bool get isNotNullOrEmpty {
    return !isNullOrEmpty;
  }

  /// アンダーバーより前を削除
  /// JPKIの氏名変換処理にて使用
  String get removeBeforeUnderscore {
    if (this == null) return '';
    // アンダーバーが存在する場合、 アンダーバーの後の文字列を返す
    if (this!.contains('＿')) {
      return this!.split('＿')[1];
    }
    // アンダーバーが含まれていない場合は、元の文字列を返す
    return this!;
  }

  /// 旧姓[] と 通称名()を削除
  /// JPKIの氏名変換処理にて使用
  String get removeBracketsAndContents {
    if (this == null) return '';
    return this!.replaceAll(RegExp(r'\[.*?\]|\(.*?\)'), '').trim();
  }

  /// 全角スペースを元に姓と名を区切り、半角スペースで結合
  /// JPKIの氏名変換処理にて使用
  String get parseSpace {
    if (this == null) return '';
    // 全角スペースで区切る
    List<String> parts = this!.split('　');

    parts = parts.where((part) => part.isNotEmpty).toList();

    // 姓と名を分ける
    final lastName = parts.isNotEmpty ? parts[0] : '';
    final firstName = parts.length > 1 ? parts[1] : '';

    // 姓と名を半角スペースで結合
    return '$lastName $firstName'.trim();
  }
}

extension StringExt on String {
  /// 送信値が''(空文字)の際に、'ー'(長音記号)にするメソッド
  /// ユーザープロパティ送信値の変換に使用
  String get convertToHyphen {
    if (isEmpty) {
      return 'ー';
    } else {
      return this;
    }
  }
}
