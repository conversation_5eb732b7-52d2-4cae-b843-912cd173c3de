import 'package:intl/intl.dart';

extension IntExt on int {
  /// 正負記号を取り除いたカンマ表記
  String get withCommas => NumberFormat('#,###').format(abs());

  /// 正負記号を含んだカンマ表記
  String get withCommasAndMinusSign => NumberFormat('#,###').format(this);
}

extension DpubleExt on double {
  /// 正負記号を取り除いたカンマ表記
  String get withCommas => NumberFormat('#,###').format(abs());

  /// 正負記号を含んだカンマ表記
  String get withCommasAndMinusSign => NumberFormat('#,###').format(this);
}
