import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/utils/ext/string_ext.dart';
import 'package:encrypt/encrypt.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

abstract class DtpEncrypter {
  Future<Encrypted> encrypt(final String value);

  Future<String> decrypt(final Encrypted value);
}

class DtpEncrypterImpl implements DtpEncrypter {
  final SecureStorageManager _secureStorageManager;

  DtpEncrypterImpl(
    this._secureStorageManager,
  );

  @override
  Future<Encrypted> encrypt(value) async {
    final aesKey = await _secureStorageManager.aesKey.load();
    final aesIv = await _secureStorageManager.aesIv.load();
    final key = Key.fromBase64(aesKey);
    final iv = IV.fromBase64(aesIv);
    return Encrypter(AES(key)).encrypt(value, iv: iv);
  }

  @override
  Future<String> decrypt(Encrypted value) async {
    final aesKey = await _secureStorageManager.aesKey.load();
    final aesIv = await _secureStorageManager.aesIv.load();
    if (aesKey.isNullOrEmpty || aesIv.isNullOrEmpty) return '';
    final key = Key.fromBase64(aesKey);
    final iv = IV.fromBase64(aesIv);
    return Encrypter(AES(key)).decrypt(value, iv: iv);
  }
}

final encrypterProvider = Provider<DtpEncrypter>((ref) {
  final secureStorageManager = ref.watch(secureStorageManagerProvider);
  return DtpEncrypterImpl(secureStorageManager);
});
