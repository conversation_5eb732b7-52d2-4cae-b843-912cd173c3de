import 'package:dtp_app/build_config/env/dot_env_params.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BuildConfig {
  const BuildConfig(this._envParams);

  final DotEnvParams _envParams;

  // ベースとなるURL
  String get baseUrl => _envParams.baseUrl;

  // APIバージョン
  String get apiVersion => _envParams.apiVersion;

  // iOS強制アップデートjsonURL
  String get iosForceUpdateJsonUrl => _envParams.iosForceUpdateJsonUrl;

  // Android強制アップデートjsonURL
  String get androidForceUpdateJsonUrl => _envParams.androidForceUpdateJsonUrl;

  // 認可画面URL
  String get authWebViewUrl => _envParams.authWebViewUrl;

  // WebViewホワイトリスト
  String get webViewWhiteList => _envParams.webViewWhiteList;

  // リンクIDとして定義済みのリンクにおけるベースURL
  String get linkIdBaseUrl => 'https://www.smbc.co.jp/ln/direct/LinksServlet';

  // SMBC法人用アプリ（iOS）のストアページURL
  String get corporationAppAppStorePageUrl =>
      'https://apps.apple.com/jp/app/%E6%B3%95%E4%BA%BA%E7%94%A8%E3%82%A2%E3%83%97%E3%83%AA/id1203161060';

  // SMBC法人用アプリ（Android）のストアページURL
  String get corporationAppPlayStorePageUrl =>
      'https://play.google.com/store/apps/details?id=jp.co.smbc.web21sp&pcampaignid=web_share';

  // ValueDoor接続用のURL
  String get valueDoorUrl => _envParams.valueDoorUrl;

  // マイナPocketAPIのベースとなるURL
  String get mynaPocketBaseUrl => _envParams.mynaPocketBaseUrl;

  // DTPweb版ログイン画面
  String get digitalTouchPointLogin => _envParams.digitalTouchPointLogin;

  // 法人用アプリのユニバーサルリンク
  String get web21UniversalLink => _envParams.web21UniversalLink;

  // Web21振込情報入力画面「ワンタイムパスワード発行」ボタン押下時のURL（フック対象）
  String get otpIssuanceUrl => _envParams.otpIssuanceUrl;

  // Freeeリダイレクト画面
  String get freeeRedirectScreen => _envParams.freeeRedirectScreen;

  // IDaaSのベースとなるURL
  String get idaasBaseUrl => _envParams.idaasBaseUrl;

  // eKYCからのリダイレクトURL（フック対象）
  String get ekycRedirectUrl => _envParams.ekycRedirectUrl;

  // お知らせ表示JsonURL
  String get announceJsonUrl => _envParams.announceJsonUrl;

  // http通信許容フラグ
  String get isHttpAllowed => _envParams.isHttpAllowed;
}

/// BuildConfigをDIするためのプロバイダー
final buildConfigProvider = Provider<BuildConfig>((ref) {
  // 実際にはmain関数でオーバーライドしてそれぞれのBuildConfigを注入する
  return const BuildConfig(DotEnvParams({}));
});
