/// dot_envプラグインで読み込んだ環境変数のラッパークラス
class DotEnvParams {
  const DotEnvParams(this._env);

  final Map<String, String> _env;

  // ベースとなるURL
  String get baseUrl => _env['BASE_URL'] ?? 'http://localhost:3501';

  // APIバージョン
  String get apiVersion => _env['API_VERSION'] ?? 'v1';

  // iOS強制アップデートjsonURL
  String get iosForceUpdateJsonUrl => _env['IOS_FORCE_UPDATE_JSON_URL'] ?? '';

  // Android強制アップデートjsonURL
  String get androidForceUpdateJsonUrl =>
      _env['ANDROID_FORCE_UPDATE_JSON_URL'] ?? '';

  // 認可画面URL
  String get authWebViewUrl => _env['AUTHORIZATION_URL'] ?? 'http://localhost:3501/auth_dummy.html';

  // WebViewホワイトリスト
  String get webViewWhiteList => _env['WHITELISTED_LIST'] ?? 'http://localhost:3501';

  // ValueDoor接続用のURL
  String get valueDoorUrl => _env['VALUE_DOOR_URL'] ?? '';

  // マイナPocketAPIのベースとなるURL
  String get mynaPocketBaseUrl => _env['MYNAPOCKET_BASE_URL'] ?? 'http://localhost:3501/api/v1';

  // DTPweb版ログイン画面
  String get digitalTouchPointLogin => _env['DIGITAL_TOUCH_POINT_LOGIN'] ?? '';

  // 法人用アプリのユニバーサルリンク
  String get web21UniversalLink => _env['WEB21_APP_UNIVERSAL_LINK'] ?? '';

  // Web21振込情報入力画面「ワンタイムパスワード発行」ボタン押下時のURL（フック対象）
  String get otpIssuanceUrl => _env['OTP_ISSUANCE_URL'] ?? '';

  // Freeeリダイレクト画面
  String get freeeRedirectScreen => _env['FREEE_REDIRECT_SCREEN'] ?? '';

  // IDaaSのベースとなるURL
  String get idaasBaseUrl => _env['IDAAS_BASE_URL'] ?? 'http://localhost:3501';

  // eKYCからのリダイレクトURL（フック対象）
  String get ekycRedirectUrl => _env['EKYC_REDIRECT_URL'] ?? '/webhook/ekyc_end';

  // お知らせ機能表示jsonURL
  String get announceJsonUrl => _env['ANNOUNCE_JSON_URL'] ?? '';

  // http通信許容フラグ
  String get isHttpAllowed => _env['HTTP_ALLOWED'] ?? 'YES';
}
