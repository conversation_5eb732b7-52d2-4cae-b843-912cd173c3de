import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'ekyc_webview_screen_notifier.freezed.dart';

@freezed
class EkycWebViewScreenState with _$EkycWebViewScreenState {
  const factory EkycWebViewScreenState({
    // ekycWebview内での処理が完了しフック用のURLが返却されているか
    @Default(false) bool isEkycComplete,
  }) = _EkycWebViewScreenState;
}

final ekycWebViewScreenStateScreenProvider = StateNotifierProvider.autoDispose<
    EkycWebViewScreenNotifier, EkycWebViewScreenState>((ref) {
  return EkycWebViewScreenNotifier();
});

class EkycWebViewScreenNotifier extends StateNotifier<EkycWebViewScreenState> {
  EkycWebViewScreenNotifier() : super(const EkycWebViewScreenState());

  // ekycWebview内での処理が完了しフック用のURLが返却されているか判定
  void updateIsEkycComplete() {
    state = state.copyWith(
      isEkycComplete: true,
    );
  }
}
