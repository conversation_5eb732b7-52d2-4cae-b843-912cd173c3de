// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ekyc_webview_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EkycWebViewScreenState {
// ekycWebview内での処理が完了しフック用のURLが返却されているか
  bool get isEkycComplete => throw _privateConstructorUsedError;

  /// Create a copy of EkycWebViewScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EkycWebViewScreenStateCopyWith<EkycWebViewScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EkycWebViewScreenStateCopyWith<$Res> {
  factory $EkycWebViewScreenStateCopyWith(EkycWebViewScreenState value,
          $Res Function(EkycWebViewScreenState) then) =
      _$EkycWebViewScreenStateCopyWithImpl<$Res, EkycWebViewScreenState>;
  @useResult
  $Res call({bool isEkycComplete});
}

/// @nodoc
class _$EkycWebViewScreenStateCopyWithImpl<$Res,
        $Val extends EkycWebViewScreenState>
    implements $EkycWebViewScreenStateCopyWith<$Res> {
  _$EkycWebViewScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EkycWebViewScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEkycComplete = null,
  }) {
    return _then(_value.copyWith(
      isEkycComplete: null == isEkycComplete
          ? _value.isEkycComplete
          : isEkycComplete // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EkycWebViewScreenStateImplCopyWith<$Res>
    implements $EkycWebViewScreenStateCopyWith<$Res> {
  factory _$$EkycWebViewScreenStateImplCopyWith(
          _$EkycWebViewScreenStateImpl value,
          $Res Function(_$EkycWebViewScreenStateImpl) then) =
      __$$EkycWebViewScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isEkycComplete});
}

/// @nodoc
class __$$EkycWebViewScreenStateImplCopyWithImpl<$Res>
    extends _$EkycWebViewScreenStateCopyWithImpl<$Res,
        _$EkycWebViewScreenStateImpl>
    implements _$$EkycWebViewScreenStateImplCopyWith<$Res> {
  __$$EkycWebViewScreenStateImplCopyWithImpl(
      _$EkycWebViewScreenStateImpl _value,
      $Res Function(_$EkycWebViewScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EkycWebViewScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isEkycComplete = null,
  }) {
    return _then(_$EkycWebViewScreenStateImpl(
      isEkycComplete: null == isEkycComplete
          ? _value.isEkycComplete
          : isEkycComplete // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$EkycWebViewScreenStateImpl implements _EkycWebViewScreenState {
  const _$EkycWebViewScreenStateImpl({this.isEkycComplete = false});

// ekycWebview内での処理が完了しフック用のURLが返却されているか
  @override
  @JsonKey()
  final bool isEkycComplete;

  @override
  String toString() {
    return 'EkycWebViewScreenState(isEkycComplete: $isEkycComplete)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EkycWebViewScreenStateImpl &&
            (identical(other.isEkycComplete, isEkycComplete) ||
                other.isEkycComplete == isEkycComplete));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isEkycComplete);

  /// Create a copy of EkycWebViewScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EkycWebViewScreenStateImplCopyWith<_$EkycWebViewScreenStateImpl>
      get copyWith => __$$EkycWebViewScreenStateImplCopyWithImpl<
          _$EkycWebViewScreenStateImpl>(this, _$identity);
}

abstract class _EkycWebViewScreenState implements EkycWebViewScreenState {
  const factory _EkycWebViewScreenState({final bool isEkycComplete}) =
      _$EkycWebViewScreenStateImpl;

// ekycWebview内での処理が完了しフック用のURLが返却されているか
  @override
  bool get isEkycComplete;

  /// Create a copy of EkycWebViewScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EkycWebViewScreenStateImplCopyWith<_$EkycWebViewScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
