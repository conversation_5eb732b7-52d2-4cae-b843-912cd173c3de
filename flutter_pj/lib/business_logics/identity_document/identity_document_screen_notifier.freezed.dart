// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identity_document_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IdentityDocumentState {
  String? get nextPageName => throw _privateConstructorUsedError;
  String get referenceNumber => throw _privateConstructorUsedError;
  String get userTypeNumber => throw _privateConstructorUsedError;
  ErrorPattern? get errorPattern => throw _privateConstructorUsedError;
  String get uuid => throw _privateConstructorUsedError;

  /// Create a copy of IdentityDocumentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityDocumentStateCopyWith<IdentityDocumentState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityDocumentStateCopyWith<$Res> {
  factory $IdentityDocumentStateCopyWith(IdentityDocumentState value,
          $Res Function(IdentityDocumentState) then) =
      _$IdentityDocumentStateCopyWithImpl<$Res, IdentityDocumentState>;
  @useResult
  $Res call(
      {String? nextPageName,
      String referenceNumber,
      String userTypeNumber,
      ErrorPattern? errorPattern,
      String uuid});
}

/// @nodoc
class _$IdentityDocumentStateCopyWithImpl<$Res,
        $Val extends IdentityDocumentState>
    implements $IdentityDocumentStateCopyWith<$Res> {
  _$IdentityDocumentStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityDocumentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nextPageName = freezed,
    Object? referenceNumber = null,
    Object? userTypeNumber = null,
    Object? errorPattern = freezed,
    Object? uuid = null,
  }) {
    return _then(_value.copyWith(
      nextPageName: freezed == nextPageName
          ? _value.nextPageName
          : nextPageName // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceNumber: null == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      userTypeNumber: null == userTypeNumber
          ? _value.userTypeNumber
          : userTypeNumber // ignore: cast_nullable_to_non_nullable
              as String,
      errorPattern: freezed == errorPattern
          ? _value.errorPattern
          : errorPattern // ignore: cast_nullable_to_non_nullable
              as ErrorPattern?,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$IdentityDocumentStateImplCopyWith<$Res>
    implements $IdentityDocumentStateCopyWith<$Res> {
  factory _$$IdentityDocumentStateImplCopyWith(
          _$IdentityDocumentStateImpl value,
          $Res Function(_$IdentityDocumentStateImpl) then) =
      __$$IdentityDocumentStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nextPageName,
      String referenceNumber,
      String userTypeNumber,
      ErrorPattern? errorPattern,
      String uuid});
}

/// @nodoc
class __$$IdentityDocumentStateImplCopyWithImpl<$Res>
    extends _$IdentityDocumentStateCopyWithImpl<$Res,
        _$IdentityDocumentStateImpl>
    implements _$$IdentityDocumentStateImplCopyWith<$Res> {
  __$$IdentityDocumentStateImplCopyWithImpl(_$IdentityDocumentStateImpl _value,
      $Res Function(_$IdentityDocumentStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityDocumentState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nextPageName = freezed,
    Object? referenceNumber = null,
    Object? userTypeNumber = null,
    Object? errorPattern = freezed,
    Object? uuid = null,
  }) {
    return _then(_$IdentityDocumentStateImpl(
      nextPageName: freezed == nextPageName
          ? _value.nextPageName
          : nextPageName // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceNumber: null == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      userTypeNumber: null == userTypeNumber
          ? _value.userTypeNumber
          : userTypeNumber // ignore: cast_nullable_to_non_nullable
              as String,
      errorPattern: freezed == errorPattern
          ? _value.errorPattern
          : errorPattern // ignore: cast_nullable_to_non_nullable
              as ErrorPattern?,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$IdentityDocumentStateImpl
    with DiagnosticableTreeMixin
    implements _IdentityDocumentState {
  const _$IdentityDocumentStateImpl(
      {this.nextPageName = null,
      this.referenceNumber = '',
      required this.userTypeNumber,
      this.errorPattern = null,
      this.uuid = ''});

  @override
  @JsonKey()
  final String? nextPageName;
  @override
  @JsonKey()
  final String referenceNumber;
  @override
  final String userTypeNumber;
  @override
  @JsonKey()
  final ErrorPattern? errorPattern;
  @override
  @JsonKey()
  final String uuid;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'IdentityDocumentState(nextPageName: $nextPageName, referenceNumber: $referenceNumber, userTypeNumber: $userTypeNumber, errorPattern: $errorPattern, uuid: $uuid)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'IdentityDocumentState'))
      ..add(DiagnosticsProperty('nextPageName', nextPageName))
      ..add(DiagnosticsProperty('referenceNumber', referenceNumber))
      ..add(DiagnosticsProperty('userTypeNumber', userTypeNumber))
      ..add(DiagnosticsProperty('errorPattern', errorPattern))
      ..add(DiagnosticsProperty('uuid', uuid));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityDocumentStateImpl &&
            (identical(other.nextPageName, nextPageName) ||
                other.nextPageName == nextPageName) &&
            (identical(other.referenceNumber, referenceNumber) ||
                other.referenceNumber == referenceNumber) &&
            (identical(other.userTypeNumber, userTypeNumber) ||
                other.userTypeNumber == userTypeNumber) &&
            (identical(other.errorPattern, errorPattern) ||
                other.errorPattern == errorPattern) &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nextPageName, referenceNumber,
      userTypeNumber, errorPattern, uuid);

  /// Create a copy of IdentityDocumentState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityDocumentStateImplCopyWith<_$IdentityDocumentStateImpl>
      get copyWith => __$$IdentityDocumentStateImplCopyWithImpl<
          _$IdentityDocumentStateImpl>(this, _$identity);
}

abstract class _IdentityDocumentState implements IdentityDocumentState {
  const factory _IdentityDocumentState(
      {final String? nextPageName,
      final String referenceNumber,
      required final String userTypeNumber,
      final ErrorPattern? errorPattern,
      final String uuid}) = _$IdentityDocumentStateImpl;

  @override
  String? get nextPageName;
  @override
  String get referenceNumber;
  @override
  String get userTypeNumber;
  @override
  ErrorPattern? get errorPattern;
  @override
  String get uuid;

  /// Create a copy of IdentityDocumentState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityDocumentStateImplCopyWith<_$IdentityDocumentStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
