import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/interfaces/classified_client_repository.dart';

import 'package:dtp_app/business_logics/interfaces/identity_reference_number_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_oauth_api_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_service_check_api_repository.dart';

import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';
import 'package:dtp_app/repositories/classified_client/classified_client_repository_impl.dart';

import 'package:dtp_app/repositories/identity_verification/reference_number/reference_number_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_oauth_api/jpki_oauth_api_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_service_check_api/jpki_service_check_api_repository_impl.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'identity_document_screen_notifier.freezed.dart';

@freezed
class IdentityDocumentState with _$IdentityDocumentState {
  const factory IdentityDocumentState({
    @Default(null) String? nextPageName,
    @Default('') String referenceNumber,
    required String userTypeNumber,
    @Default(null) ErrorPattern? errorPattern,
    @Default('') String uuid,
  }) = _IdentityDocumentState;
}

extension IdentityDocumentStateExt on IdentityDocumentState {}

final identityDocumentStateScreenProvider = StateNotifierProvider.autoDispose<
    IdentityDocumentScreenNotifier, IdentityDocumentState>((ref) {
  // お手続き番号を取得する
  final referenceNumber =
      ref.read(identityReferenceNumberScreenProvider).cacheReferenceNumberText;

  // UUIDを取得する
  final uuid = ref.read(identityReferenceNumberScreenProvider).cacheUuid;

  // 口座開設情報を取得する
  final openAccountAppTemp =
      ref.read(identityReferenceNumberScreenProvider).openAccountAppTemp;

  // 手続き者属性を取得する
  final userTypeNumber = openAccountAppTemp?.getUserType(referenceNumber) ?? '';

  return IdentityDocumentScreenNotifier(
    jpkiOAuthAPIRepository: ref.read(jpkiOAuthAPIRepositoryProvider),
    jpkiServiceCheckApiRepository:
        ref.read(jpkiServiceCheckApiRepositoryProvider),
    identityReferenceNumberRepository:
        ref.read(identityReferenceNumberRepositoryProvider),
    classifiedClientRepository: ref.read(classifiedClientRepositoryProvider),
    referenceNumber: referenceNumber,
    uuid: uuid,
    userTypeNumber: userTypeNumber,
  );
});

class IdentityDocumentScreenNotifier
    extends StateNotifier<IdentityDocumentState> {
  IdentityDocumentScreenNotifier({
    required this.jpkiOAuthAPIRepository,
    required this.jpkiServiceCheckApiRepository,
    required this.identityReferenceNumberRepository,
    required this.classifiedClientRepository,
    required String referenceNumber,
    required String uuid,
    required String userTypeNumber,
  }) : super(
          IdentityDocumentState(
            referenceNumber: referenceNumber,
            uuid: uuid,
            userTypeNumber: userTypeNumber,
          ),
        );

  final JpkiOAuthAPIRepository jpkiOAuthAPIRepository;
  final JpkiServiceCheckApiRepository jpkiServiceCheckApiRepository;
  final ClassifiedClientRepository classifiedClientRepository;

  final IdentityReferenceNumberRepository identityReferenceNumberRepository;

  // マイナンバーカード選択時の処理
  Future<void> onTapMyNumberCard() async {
    await runGetClassifiedClientApi();
  }

  // 「マイナンバーをお持ちでない場合」タップ時の処理
  void onTapNotHaveCard() {
    changeNextPageName(RouteNames.ekycExplanation);
  }

  // エラーパターンの変更
  void changeErrorPattern(ErrorPattern errorPattern) {
    state = state.copyWith(
      errorPattern: errorPattern,
    );
  }

  // 次に遷移するページの変更
  void changeNextPageName(String pageName) {
    state = state.copyWith(
      nextPageName: pageName,
    );
  }

  //クライアント情報取得API
  Future<void> runGetClassifiedClientApi() async {
    // clientId, clientSecretを取得する
    final classifiedClientResult =
        await classifiedClientRepository.getClassifiedClient();

    final classifiedClient = classifiedClientResult.when(
      success: (classifiedClient) => classifiedClient,
      failure: (error) {
        changeErrorPattern(
          ErrorInfoJpki.getErrorPattern(
            error.statusCode,
            ErrorInfoJpki.jpkiServiceCheckApiErrorPattern,
          ),
        );
        return null;
      },
    );
    if (classifiedClient == null) return;

    // clientId, clientSecretを保存する
    await jpkiOAuthAPIRepository.saveClientId(classifiedClient.clientId);
    await jpkiOAuthAPIRepository
        .saveClientSecret(classifiedClient.clientSecret);

    await runOAuthApi();
  }

  //CC_OAuthApi実行
  Future<void> runOAuthApi() async {
    final oauthResult = await jpkiOAuthAPIRepository.jpkiOAuthApiAccess();
    await oauthResult.when(
      success: (_) async {
        // OAuthApi実行成功の場合、トークンを取得してサービス利用情報提供APIを呼び出し
        await runServiceCheckApi();
      },
      failure: (error) {
        changeErrorPattern(
          ErrorInfoJpki.getErrorPattern(
            error.statusCode,
            ErrorInfoJpki.jpkiOAuthApiErrorPattern,
          ),
        );
      },
    );
  }

// サービス利用情報提供API実行
  Future<void> runServiceCheckApi() async {
    final token = await jpkiServiceCheckApiRepository.getJpkiAccessToken();
    final serviceCheckResult =
        await jpkiServiceCheckApiRepository.jpkiServiceCheckApiAccess(token);
    await serviceCheckResult.when(
      success: (_) async {
        // サービスチェック成功の場合、次画面へ遷移
        changeNextPageName(RouteNames.passwordInputExplanation);
      },
      failure: (error) {
        // サービスチェック失敗の場合、エラーステータスを元に、エラーダイアログを出しわける
        changeErrorPattern(
          ErrorInfoJpki.getErrorPattern(
            error.statusCode,
            ErrorInfoJpki.jpkiServiceCheckApiErrorPattern,
          ),
        );
      },
    );
  }

// 画面遷移、エラーに必要な情報のリセット
  void stateReset() {
    state = state.copyWith(
      nextPageName: null,
      errorPattern: null,
    );
  }
}
