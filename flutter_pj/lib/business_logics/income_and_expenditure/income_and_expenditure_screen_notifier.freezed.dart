// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'income_and_expenditure_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IncomeAndExpenditureScreenState {
  AccountTransactionHistory get accountTransactionHistory =>
      throw _privateConstructorUsedError;
  AccountTransactionHistory get cashTransactionHistory =>
      throw _privateConstructorUsedError;
  FreeeTransactionHistory get freeeTransactionHistory =>
      throw _privateConstructorUsedError;
  FreeeTransactionHistory get cashFreeeTransactionHistory =>
      throw _privateConstructorUsedError;
  bool get isAccordionExpanded => throw _privateConstructorUsedError;

  /// HACK　入出金明細の開始日付、終了日付に関してはprovider生成時に遅延初期化にて代入し
  /// 必須項目とする
  DateTime? get dateFrom => throw _privateConstructorUsedError;
  DateTime? get dateTo => throw _privateConstructorUsedError;
  List<DateTime> get yearMonthList => throw _privateConstructorUsedError;
  DateTime? get selectedYearMonth => throw _privateConstructorUsedError;
  int get selectedYearMonthIndex => throw _privateConstructorUsedError;
  TransactionHistoryFilterType get currentFilterType =>
      throw _privateConstructorUsedError;
  TransactionHistorySortType get currentSortType =>
      throw _privateConstructorUsedError;
  TransactionHistorySortOrder get currentSortOrder =>
      throw _privateConstructorUsedError;
  String get sortTitle => throw _privateConstructorUsedError;
  DateSegment get dateSegment => throw _privateConstructorUsedError;
  bool get isCash => throw _privateConstructorUsedError;
  bool get isPayment => throw _privateConstructorUsedError;
  bool get isExchange => throw _privateConstructorUsedError;
  bool get isTransfer => throw _privateConstructorUsedError;
  bool get isOtherBanksTicket => throw _privateConstructorUsedError;
  bool get isCorrection => throw _privateConstructorUsedError;
  bool get isDensai => throw _privateConstructorUsedError;
  bool get isContinuation => throw _privateConstructorUsedError;
  bool get isSonota => throw _privateConstructorUsedError;
  bool get isNarrowDown => throw _privateConstructorUsedError;
  DateTime? get fromFirstDay => throw _privateConstructorUsedError;
  DateTime? get fromLastDay => throw _privateConstructorUsedError;
  DateTime? get fromSelectedDay => throw _privateConstructorUsedError;
  DateTime? get toFirstDay => throw _privateConstructorUsedError;
  DateTime? get toLastDay => throw _privateConstructorUsedError;
  DateTime? get toSelectedDay => throw _privateConstructorUsedError;
  bool get isFromDialog => throw _privateConstructorUsedError;
  bool get isToDialog => throw _privateConstructorUsedError;
  List<TransactionTypeCode> get transactionCodes =>
      throw _privateConstructorUsedError; // 他行、自行を分岐するためのフラグ
  AccountApiType get apiType => throw _privateConstructorUsedError;
  String get smallAmount => throw _privateConstructorUsedError;
  String get largeAmount => throw _privateConstructorUsedError;
  bool get clientNameError => throw _privateConstructorUsedError;
  bool get digitSmallAmountError => throw _privateConstructorUsedError;
  bool get positiveSmallAmountError => throw _privateConstructorUsedError;
  bool get correlationSmallAmountError => throw _privateConstructorUsedError;
  bool get digitLargeAmountError => throw _privateConstructorUsedError;
  bool get positiveLargeAmountError => throw _privateConstructorUsedError;
  bool get correlationLargeAmountError => throw _privateConstructorUsedError;
  bool get referenceNumberError => throw _privateConstructorUsedError;
  bool get depositNumberError => throw _privateConstructorUsedError;
  AppError? get web21Error => throw _privateConstructorUsedError;
  AppError? get freeeError => throw _privateConstructorUsedError;

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IncomeAndExpenditureScreenStateCopyWith<IncomeAndExpenditureScreenState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IncomeAndExpenditureScreenStateCopyWith<$Res> {
  factory $IncomeAndExpenditureScreenStateCopyWith(
          IncomeAndExpenditureScreenState value,
          $Res Function(IncomeAndExpenditureScreenState) then) =
      _$IncomeAndExpenditureScreenStateCopyWithImpl<$Res,
          IncomeAndExpenditureScreenState>;
  @useResult
  $Res call(
      {AccountTransactionHistory accountTransactionHistory,
      AccountTransactionHistory cashTransactionHistory,
      FreeeTransactionHistory freeeTransactionHistory,
      FreeeTransactionHistory cashFreeeTransactionHistory,
      bool isAccordionExpanded,
      DateTime? dateFrom,
      DateTime? dateTo,
      List<DateTime> yearMonthList,
      DateTime? selectedYearMonth,
      int selectedYearMonthIndex,
      TransactionHistoryFilterType currentFilterType,
      TransactionHistorySortType currentSortType,
      TransactionHistorySortOrder currentSortOrder,
      String sortTitle,
      DateSegment dateSegment,
      bool isCash,
      bool isPayment,
      bool isExchange,
      bool isTransfer,
      bool isOtherBanksTicket,
      bool isCorrection,
      bool isDensai,
      bool isContinuation,
      bool isSonota,
      bool isNarrowDown,
      DateTime? fromFirstDay,
      DateTime? fromLastDay,
      DateTime? fromSelectedDay,
      DateTime? toFirstDay,
      DateTime? toLastDay,
      DateTime? toSelectedDay,
      bool isFromDialog,
      bool isToDialog,
      List<TransactionTypeCode> transactionCodes,
      AccountApiType apiType,
      String smallAmount,
      String largeAmount,
      bool clientNameError,
      bool digitSmallAmountError,
      bool positiveSmallAmountError,
      bool correlationSmallAmountError,
      bool digitLargeAmountError,
      bool positiveLargeAmountError,
      bool correlationLargeAmountError,
      bool referenceNumberError,
      bool depositNumberError,
      AppError? web21Error,
      AppError? freeeError});

  $AccountTransactionHistoryCopyWith<$Res> get accountTransactionHistory;
  $AccountTransactionHistoryCopyWith<$Res> get cashTransactionHistory;
  $FreeeTransactionHistoryCopyWith<$Res> get freeeTransactionHistory;
  $FreeeTransactionHistoryCopyWith<$Res> get cashFreeeTransactionHistory;
  $AppErrorCopyWith<$Res>? get web21Error;
  $AppErrorCopyWith<$Res>? get freeeError;
}

/// @nodoc
class _$IncomeAndExpenditureScreenStateCopyWithImpl<$Res,
        $Val extends IncomeAndExpenditureScreenState>
    implements $IncomeAndExpenditureScreenStateCopyWith<$Res> {
  _$IncomeAndExpenditureScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountTransactionHistory = null,
    Object? cashTransactionHistory = null,
    Object? freeeTransactionHistory = null,
    Object? cashFreeeTransactionHistory = null,
    Object? isAccordionExpanded = null,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? yearMonthList = null,
    Object? selectedYearMonth = freezed,
    Object? selectedYearMonthIndex = null,
    Object? currentFilterType = null,
    Object? currentSortType = null,
    Object? currentSortOrder = null,
    Object? sortTitle = null,
    Object? dateSegment = null,
    Object? isCash = null,
    Object? isPayment = null,
    Object? isExchange = null,
    Object? isTransfer = null,
    Object? isOtherBanksTicket = null,
    Object? isCorrection = null,
    Object? isDensai = null,
    Object? isContinuation = null,
    Object? isSonota = null,
    Object? isNarrowDown = null,
    Object? fromFirstDay = freezed,
    Object? fromLastDay = freezed,
    Object? fromSelectedDay = freezed,
    Object? toFirstDay = freezed,
    Object? toLastDay = freezed,
    Object? toSelectedDay = freezed,
    Object? isFromDialog = null,
    Object? isToDialog = null,
    Object? transactionCodes = null,
    Object? apiType = null,
    Object? smallAmount = null,
    Object? largeAmount = null,
    Object? clientNameError = null,
    Object? digitSmallAmountError = null,
    Object? positiveSmallAmountError = null,
    Object? correlationSmallAmountError = null,
    Object? digitLargeAmountError = null,
    Object? positiveLargeAmountError = null,
    Object? correlationLargeAmountError = null,
    Object? referenceNumberError = null,
    Object? depositNumberError = null,
    Object? web21Error = freezed,
    Object? freeeError = freezed,
  }) {
    return _then(_value.copyWith(
      accountTransactionHistory: null == accountTransactionHistory
          ? _value.accountTransactionHistory
          : accountTransactionHistory // ignore: cast_nullable_to_non_nullable
              as AccountTransactionHistory,
      cashTransactionHistory: null == cashTransactionHistory
          ? _value.cashTransactionHistory
          : cashTransactionHistory // ignore: cast_nullable_to_non_nullable
              as AccountTransactionHistory,
      freeeTransactionHistory: null == freeeTransactionHistory
          ? _value.freeeTransactionHistory
          : freeeTransactionHistory // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistory,
      cashFreeeTransactionHistory: null == cashFreeeTransactionHistory
          ? _value.cashFreeeTransactionHistory
          : cashFreeeTransactionHistory // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistory,
      isAccordionExpanded: null == isAccordionExpanded
          ? _value.isAccordionExpanded
          : isAccordionExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      yearMonthList: null == yearMonthList
          ? _value.yearMonthList
          : yearMonthList // ignore: cast_nullable_to_non_nullable
              as List<DateTime>,
      selectedYearMonth: freezed == selectedYearMonth
          ? _value.selectedYearMonth
          : selectedYearMonth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedYearMonthIndex: null == selectedYearMonthIndex
          ? _value.selectedYearMonthIndex
          : selectedYearMonthIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentFilterType: null == currentFilterType
          ? _value.currentFilterType
          : currentFilterType // ignore: cast_nullable_to_non_nullable
              as TransactionHistoryFilterType,
      currentSortType: null == currentSortType
          ? _value.currentSortType
          : currentSortType // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortType,
      currentSortOrder: null == currentSortOrder
          ? _value.currentSortOrder
          : currentSortOrder // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortOrder,
      sortTitle: null == sortTitle
          ? _value.sortTitle
          : sortTitle // ignore: cast_nullable_to_non_nullable
              as String,
      dateSegment: null == dateSegment
          ? _value.dateSegment
          : dateSegment // ignore: cast_nullable_to_non_nullable
              as DateSegment,
      isCash: null == isCash
          ? _value.isCash
          : isCash // ignore: cast_nullable_to_non_nullable
              as bool,
      isPayment: null == isPayment
          ? _value.isPayment
          : isPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      isExchange: null == isExchange
          ? _value.isExchange
          : isExchange // ignore: cast_nullable_to_non_nullable
              as bool,
      isTransfer: null == isTransfer
          ? _value.isTransfer
          : isTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      isOtherBanksTicket: null == isOtherBanksTicket
          ? _value.isOtherBanksTicket
          : isOtherBanksTicket // ignore: cast_nullable_to_non_nullable
              as bool,
      isCorrection: null == isCorrection
          ? _value.isCorrection
          : isCorrection // ignore: cast_nullable_to_non_nullable
              as bool,
      isDensai: null == isDensai
          ? _value.isDensai
          : isDensai // ignore: cast_nullable_to_non_nullable
              as bool,
      isContinuation: null == isContinuation
          ? _value.isContinuation
          : isContinuation // ignore: cast_nullable_to_non_nullable
              as bool,
      isSonota: null == isSonota
          ? _value.isSonota
          : isSonota // ignore: cast_nullable_to_non_nullable
              as bool,
      isNarrowDown: null == isNarrowDown
          ? _value.isNarrowDown
          : isNarrowDown // ignore: cast_nullable_to_non_nullable
              as bool,
      fromFirstDay: freezed == fromFirstDay
          ? _value.fromFirstDay
          : fromFirstDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      fromLastDay: freezed == fromLastDay
          ? _value.fromLastDay
          : fromLastDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      fromSelectedDay: freezed == fromSelectedDay
          ? _value.fromSelectedDay
          : fromSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toFirstDay: freezed == toFirstDay
          ? _value.toFirstDay
          : toFirstDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toLastDay: freezed == toLastDay
          ? _value.toLastDay
          : toLastDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toSelectedDay: freezed == toSelectedDay
          ? _value.toSelectedDay
          : toSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isFromDialog: null == isFromDialog
          ? _value.isFromDialog
          : isFromDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      isToDialog: null == isToDialog
          ? _value.isToDialog
          : isToDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      transactionCodes: null == transactionCodes
          ? _value.transactionCodes
          : transactionCodes // ignore: cast_nullable_to_non_nullable
              as List<TransactionTypeCode>,
      apiType: null == apiType
          ? _value.apiType
          : apiType // ignore: cast_nullable_to_non_nullable
              as AccountApiType,
      smallAmount: null == smallAmount
          ? _value.smallAmount
          : smallAmount // ignore: cast_nullable_to_non_nullable
              as String,
      largeAmount: null == largeAmount
          ? _value.largeAmount
          : largeAmount // ignore: cast_nullable_to_non_nullable
              as String,
      clientNameError: null == clientNameError
          ? _value.clientNameError
          : clientNameError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitSmallAmountError: null == digitSmallAmountError
          ? _value.digitSmallAmountError
          : digitSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveSmallAmountError: null == positiveSmallAmountError
          ? _value.positiveSmallAmountError
          : positiveSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationSmallAmountError: null == correlationSmallAmountError
          ? _value.correlationSmallAmountError
          : correlationSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitLargeAmountError: null == digitLargeAmountError
          ? _value.digitLargeAmountError
          : digitLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveLargeAmountError: null == positiveLargeAmountError
          ? _value.positiveLargeAmountError
          : positiveLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationLargeAmountError: null == correlationLargeAmountError
          ? _value.correlationLargeAmountError
          : correlationLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      referenceNumberError: null == referenceNumberError
          ? _value.referenceNumberError
          : referenceNumberError // ignore: cast_nullable_to_non_nullable
              as bool,
      depositNumberError: null == depositNumberError
          ? _value.depositNumberError
          : depositNumberError // ignore: cast_nullable_to_non_nullable
              as bool,
      web21Error: freezed == web21Error
          ? _value.web21Error
          : web21Error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      freeeError: freezed == freeeError
          ? _value.freeeError
          : freeeError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountTransactionHistoryCopyWith<$Res> get accountTransactionHistory {
    return $AccountTransactionHistoryCopyWith<$Res>(
        _value.accountTransactionHistory, (value) {
      return _then(_value.copyWith(accountTransactionHistory: value) as $Val);
    });
  }

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountTransactionHistoryCopyWith<$Res> get cashTransactionHistory {
    return $AccountTransactionHistoryCopyWith<$Res>(
        _value.cashTransactionHistory, (value) {
      return _then(_value.copyWith(cashTransactionHistory: value) as $Val);
    });
  }

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FreeeTransactionHistoryCopyWith<$Res> get freeeTransactionHistory {
    return $FreeeTransactionHistoryCopyWith<$Res>(
        _value.freeeTransactionHistory, (value) {
      return _then(_value.copyWith(freeeTransactionHistory: value) as $Val);
    });
  }

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FreeeTransactionHistoryCopyWith<$Res> get cashFreeeTransactionHistory {
    return $FreeeTransactionHistoryCopyWith<$Res>(
        _value.cashFreeeTransactionHistory, (value) {
      return _then(_value.copyWith(cashFreeeTransactionHistory: value) as $Val);
    });
  }

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get web21Error {
    if (_value.web21Error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.web21Error!, (value) {
      return _then(_value.copyWith(web21Error: value) as $Val);
    });
  }

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get freeeError {
    if (_value.freeeError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.freeeError!, (value) {
      return _then(_value.copyWith(freeeError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IncomeAndExpenditureScreenStateImplCopyWith<$Res>
    implements $IncomeAndExpenditureScreenStateCopyWith<$Res> {
  factory _$$IncomeAndExpenditureScreenStateImplCopyWith(
          _$IncomeAndExpenditureScreenStateImpl value,
          $Res Function(_$IncomeAndExpenditureScreenStateImpl) then) =
      __$$IncomeAndExpenditureScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {AccountTransactionHistory accountTransactionHistory,
      AccountTransactionHistory cashTransactionHistory,
      FreeeTransactionHistory freeeTransactionHistory,
      FreeeTransactionHistory cashFreeeTransactionHistory,
      bool isAccordionExpanded,
      DateTime? dateFrom,
      DateTime? dateTo,
      List<DateTime> yearMonthList,
      DateTime? selectedYearMonth,
      int selectedYearMonthIndex,
      TransactionHistoryFilterType currentFilterType,
      TransactionHistorySortType currentSortType,
      TransactionHistorySortOrder currentSortOrder,
      String sortTitle,
      DateSegment dateSegment,
      bool isCash,
      bool isPayment,
      bool isExchange,
      bool isTransfer,
      bool isOtherBanksTicket,
      bool isCorrection,
      bool isDensai,
      bool isContinuation,
      bool isSonota,
      bool isNarrowDown,
      DateTime? fromFirstDay,
      DateTime? fromLastDay,
      DateTime? fromSelectedDay,
      DateTime? toFirstDay,
      DateTime? toLastDay,
      DateTime? toSelectedDay,
      bool isFromDialog,
      bool isToDialog,
      List<TransactionTypeCode> transactionCodes,
      AccountApiType apiType,
      String smallAmount,
      String largeAmount,
      bool clientNameError,
      bool digitSmallAmountError,
      bool positiveSmallAmountError,
      bool correlationSmallAmountError,
      bool digitLargeAmountError,
      bool positiveLargeAmountError,
      bool correlationLargeAmountError,
      bool referenceNumberError,
      bool depositNumberError,
      AppError? web21Error,
      AppError? freeeError});

  @override
  $AccountTransactionHistoryCopyWith<$Res> get accountTransactionHistory;
  @override
  $AccountTransactionHistoryCopyWith<$Res> get cashTransactionHistory;
  @override
  $FreeeTransactionHistoryCopyWith<$Res> get freeeTransactionHistory;
  @override
  $FreeeTransactionHistoryCopyWith<$Res> get cashFreeeTransactionHistory;
  @override
  $AppErrorCopyWith<$Res>? get web21Error;
  @override
  $AppErrorCopyWith<$Res>? get freeeError;
}

/// @nodoc
class __$$IncomeAndExpenditureScreenStateImplCopyWithImpl<$Res>
    extends _$IncomeAndExpenditureScreenStateCopyWithImpl<$Res,
        _$IncomeAndExpenditureScreenStateImpl>
    implements _$$IncomeAndExpenditureScreenStateImplCopyWith<$Res> {
  __$$IncomeAndExpenditureScreenStateImplCopyWithImpl(
      _$IncomeAndExpenditureScreenStateImpl _value,
      $Res Function(_$IncomeAndExpenditureScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accountTransactionHistory = null,
    Object? cashTransactionHistory = null,
    Object? freeeTransactionHistory = null,
    Object? cashFreeeTransactionHistory = null,
    Object? isAccordionExpanded = null,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? yearMonthList = null,
    Object? selectedYearMonth = freezed,
    Object? selectedYearMonthIndex = null,
    Object? currentFilterType = null,
    Object? currentSortType = null,
    Object? currentSortOrder = null,
    Object? sortTitle = null,
    Object? dateSegment = null,
    Object? isCash = null,
    Object? isPayment = null,
    Object? isExchange = null,
    Object? isTransfer = null,
    Object? isOtherBanksTicket = null,
    Object? isCorrection = null,
    Object? isDensai = null,
    Object? isContinuation = null,
    Object? isSonota = null,
    Object? isNarrowDown = null,
    Object? fromFirstDay = freezed,
    Object? fromLastDay = freezed,
    Object? fromSelectedDay = freezed,
    Object? toFirstDay = freezed,
    Object? toLastDay = freezed,
    Object? toSelectedDay = freezed,
    Object? isFromDialog = null,
    Object? isToDialog = null,
    Object? transactionCodes = null,
    Object? apiType = null,
    Object? smallAmount = null,
    Object? largeAmount = null,
    Object? clientNameError = null,
    Object? digitSmallAmountError = null,
    Object? positiveSmallAmountError = null,
    Object? correlationSmallAmountError = null,
    Object? digitLargeAmountError = null,
    Object? positiveLargeAmountError = null,
    Object? correlationLargeAmountError = null,
    Object? referenceNumberError = null,
    Object? depositNumberError = null,
    Object? web21Error = freezed,
    Object? freeeError = freezed,
  }) {
    return _then(_$IncomeAndExpenditureScreenStateImpl(
      accountTransactionHistory: null == accountTransactionHistory
          ? _value.accountTransactionHistory
          : accountTransactionHistory // ignore: cast_nullable_to_non_nullable
              as AccountTransactionHistory,
      cashTransactionHistory: null == cashTransactionHistory
          ? _value.cashTransactionHistory
          : cashTransactionHistory // ignore: cast_nullable_to_non_nullable
              as AccountTransactionHistory,
      freeeTransactionHistory: null == freeeTransactionHistory
          ? _value.freeeTransactionHistory
          : freeeTransactionHistory // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistory,
      cashFreeeTransactionHistory: null == cashFreeeTransactionHistory
          ? _value.cashFreeeTransactionHistory
          : cashFreeeTransactionHistory // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistory,
      isAccordionExpanded: null == isAccordionExpanded
          ? _value.isAccordionExpanded
          : isAccordionExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      yearMonthList: null == yearMonthList
          ? _value._yearMonthList
          : yearMonthList // ignore: cast_nullable_to_non_nullable
              as List<DateTime>,
      selectedYearMonth: freezed == selectedYearMonth
          ? _value.selectedYearMonth
          : selectedYearMonth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedYearMonthIndex: null == selectedYearMonthIndex
          ? _value.selectedYearMonthIndex
          : selectedYearMonthIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentFilterType: null == currentFilterType
          ? _value.currentFilterType
          : currentFilterType // ignore: cast_nullable_to_non_nullable
              as TransactionHistoryFilterType,
      currentSortType: null == currentSortType
          ? _value.currentSortType
          : currentSortType // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortType,
      currentSortOrder: null == currentSortOrder
          ? _value.currentSortOrder
          : currentSortOrder // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortOrder,
      sortTitle: null == sortTitle
          ? _value.sortTitle
          : sortTitle // ignore: cast_nullable_to_non_nullable
              as String,
      dateSegment: null == dateSegment
          ? _value.dateSegment
          : dateSegment // ignore: cast_nullable_to_non_nullable
              as DateSegment,
      isCash: null == isCash
          ? _value.isCash
          : isCash // ignore: cast_nullable_to_non_nullable
              as bool,
      isPayment: null == isPayment
          ? _value.isPayment
          : isPayment // ignore: cast_nullable_to_non_nullable
              as bool,
      isExchange: null == isExchange
          ? _value.isExchange
          : isExchange // ignore: cast_nullable_to_non_nullable
              as bool,
      isTransfer: null == isTransfer
          ? _value.isTransfer
          : isTransfer // ignore: cast_nullable_to_non_nullable
              as bool,
      isOtherBanksTicket: null == isOtherBanksTicket
          ? _value.isOtherBanksTicket
          : isOtherBanksTicket // ignore: cast_nullable_to_non_nullable
              as bool,
      isCorrection: null == isCorrection
          ? _value.isCorrection
          : isCorrection // ignore: cast_nullable_to_non_nullable
              as bool,
      isDensai: null == isDensai
          ? _value.isDensai
          : isDensai // ignore: cast_nullable_to_non_nullable
              as bool,
      isContinuation: null == isContinuation
          ? _value.isContinuation
          : isContinuation // ignore: cast_nullable_to_non_nullable
              as bool,
      isSonota: null == isSonota
          ? _value.isSonota
          : isSonota // ignore: cast_nullable_to_non_nullable
              as bool,
      isNarrowDown: null == isNarrowDown
          ? _value.isNarrowDown
          : isNarrowDown // ignore: cast_nullable_to_non_nullable
              as bool,
      fromFirstDay: freezed == fromFirstDay
          ? _value.fromFirstDay
          : fromFirstDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      fromLastDay: freezed == fromLastDay
          ? _value.fromLastDay
          : fromLastDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      fromSelectedDay: freezed == fromSelectedDay
          ? _value.fromSelectedDay
          : fromSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toFirstDay: freezed == toFirstDay
          ? _value.toFirstDay
          : toFirstDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toLastDay: freezed == toLastDay
          ? _value.toLastDay
          : toLastDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toSelectedDay: freezed == toSelectedDay
          ? _value.toSelectedDay
          : toSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isFromDialog: null == isFromDialog
          ? _value.isFromDialog
          : isFromDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      isToDialog: null == isToDialog
          ? _value.isToDialog
          : isToDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      transactionCodes: null == transactionCodes
          ? _value._transactionCodes
          : transactionCodes // ignore: cast_nullable_to_non_nullable
              as List<TransactionTypeCode>,
      apiType: null == apiType
          ? _value.apiType
          : apiType // ignore: cast_nullable_to_non_nullable
              as AccountApiType,
      smallAmount: null == smallAmount
          ? _value.smallAmount
          : smallAmount // ignore: cast_nullable_to_non_nullable
              as String,
      largeAmount: null == largeAmount
          ? _value.largeAmount
          : largeAmount // ignore: cast_nullable_to_non_nullable
              as String,
      clientNameError: null == clientNameError
          ? _value.clientNameError
          : clientNameError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitSmallAmountError: null == digitSmallAmountError
          ? _value.digitSmallAmountError
          : digitSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveSmallAmountError: null == positiveSmallAmountError
          ? _value.positiveSmallAmountError
          : positiveSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationSmallAmountError: null == correlationSmallAmountError
          ? _value.correlationSmallAmountError
          : correlationSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitLargeAmountError: null == digitLargeAmountError
          ? _value.digitLargeAmountError
          : digitLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveLargeAmountError: null == positiveLargeAmountError
          ? _value.positiveLargeAmountError
          : positiveLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationLargeAmountError: null == correlationLargeAmountError
          ? _value.correlationLargeAmountError
          : correlationLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      referenceNumberError: null == referenceNumberError
          ? _value.referenceNumberError
          : referenceNumberError // ignore: cast_nullable_to_non_nullable
              as bool,
      depositNumberError: null == depositNumberError
          ? _value.depositNumberError
          : depositNumberError // ignore: cast_nullable_to_non_nullable
              as bool,
      web21Error: freezed == web21Error
          ? _value.web21Error
          : web21Error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      freeeError: freezed == freeeError
          ? _value.freeeError
          : freeeError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$IncomeAndExpenditureScreenStateImpl
    implements _IncomeAndExpenditureScreenState {
  const _$IncomeAndExpenditureScreenStateImpl(
      {this.accountTransactionHistory = const AccountTransactionHistory.empty(),
      this.cashTransactionHistory = const AccountTransactionHistory.empty(),
      this.freeeTransactionHistory = const FreeeTransactionHistory.empty(),
      this.cashFreeeTransactionHistory = const FreeeTransactionHistory.empty(),
      this.isAccordionExpanded = false,
      this.dateFrom,
      this.dateTo,
      final List<DateTime> yearMonthList = const <DateTime>[],
      this.selectedYearMonth,
      this.selectedYearMonthIndex = 0,
      this.currentFilterType = TransactionHistoryFilterType.all,
      this.currentSortType = TransactionHistorySortType.date,
      this.currentSortOrder = TransactionHistorySortOrder.descending,
      this.sortTitle = '',
      this.dateSegment = DateSegment.notSpecified,
      this.isCash = false,
      this.isPayment = false,
      this.isExchange = false,
      this.isTransfer = false,
      this.isOtherBanksTicket = false,
      this.isCorrection = false,
      this.isDensai = false,
      this.isContinuation = false,
      this.isSonota = false,
      this.isNarrowDown = false,
      this.fromFirstDay,
      this.fromLastDay,
      this.fromSelectedDay,
      this.toFirstDay,
      this.toLastDay,
      this.toSelectedDay,
      this.isFromDialog = false,
      this.isToDialog = false,
      final List<TransactionTypeCode> transactionCodes =
          const <TransactionTypeCode>[],
      this.apiType = AccountApiType.web21,
      this.smallAmount = '',
      this.largeAmount = '',
      this.clientNameError = false,
      this.digitSmallAmountError = false,
      this.positiveSmallAmountError = false,
      this.correlationSmallAmountError = false,
      this.digitLargeAmountError = false,
      this.positiveLargeAmountError = false,
      this.correlationLargeAmountError = false,
      this.referenceNumberError = false,
      this.depositNumberError = false,
      this.web21Error,
      this.freeeError})
      : _yearMonthList = yearMonthList,
        _transactionCodes = transactionCodes;

  @override
  @JsonKey()
  final AccountTransactionHistory accountTransactionHistory;
  @override
  @JsonKey()
  final AccountTransactionHistory cashTransactionHistory;
  @override
  @JsonKey()
  final FreeeTransactionHistory freeeTransactionHistory;
  @override
  @JsonKey()
  final FreeeTransactionHistory cashFreeeTransactionHistory;
  @override
  @JsonKey()
  final bool isAccordionExpanded;

  /// HACK　入出金明細の開始日付、終了日付に関してはprovider生成時に遅延初期化にて代入し
  /// 必須項目とする
  @override
  final DateTime? dateFrom;
  @override
  final DateTime? dateTo;
  final List<DateTime> _yearMonthList;
  @override
  @JsonKey()
  List<DateTime> get yearMonthList {
    if (_yearMonthList is EqualUnmodifiableListView) return _yearMonthList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_yearMonthList);
  }

  @override
  final DateTime? selectedYearMonth;
  @override
  @JsonKey()
  final int selectedYearMonthIndex;
  @override
  @JsonKey()
  final TransactionHistoryFilterType currentFilterType;
  @override
  @JsonKey()
  final TransactionHistorySortType currentSortType;
  @override
  @JsonKey()
  final TransactionHistorySortOrder currentSortOrder;
  @override
  @JsonKey()
  final String sortTitle;
  @override
  @JsonKey()
  final DateSegment dateSegment;
  @override
  @JsonKey()
  final bool isCash;
  @override
  @JsonKey()
  final bool isPayment;
  @override
  @JsonKey()
  final bool isExchange;
  @override
  @JsonKey()
  final bool isTransfer;
  @override
  @JsonKey()
  final bool isOtherBanksTicket;
  @override
  @JsonKey()
  final bool isCorrection;
  @override
  @JsonKey()
  final bool isDensai;
  @override
  @JsonKey()
  final bool isContinuation;
  @override
  @JsonKey()
  final bool isSonota;
  @override
  @JsonKey()
  final bool isNarrowDown;
  @override
  final DateTime? fromFirstDay;
  @override
  final DateTime? fromLastDay;
  @override
  final DateTime? fromSelectedDay;
  @override
  final DateTime? toFirstDay;
  @override
  final DateTime? toLastDay;
  @override
  final DateTime? toSelectedDay;
  @override
  @JsonKey()
  final bool isFromDialog;
  @override
  @JsonKey()
  final bool isToDialog;
  final List<TransactionTypeCode> _transactionCodes;
  @override
  @JsonKey()
  List<TransactionTypeCode> get transactionCodes {
    if (_transactionCodes is EqualUnmodifiableListView)
      return _transactionCodes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactionCodes);
  }

// 他行、自行を分岐するためのフラグ
  @override
  @JsonKey()
  final AccountApiType apiType;
  @override
  @JsonKey()
  final String smallAmount;
  @override
  @JsonKey()
  final String largeAmount;
  @override
  @JsonKey()
  final bool clientNameError;
  @override
  @JsonKey()
  final bool digitSmallAmountError;
  @override
  @JsonKey()
  final bool positiveSmallAmountError;
  @override
  @JsonKey()
  final bool correlationSmallAmountError;
  @override
  @JsonKey()
  final bool digitLargeAmountError;
  @override
  @JsonKey()
  final bool positiveLargeAmountError;
  @override
  @JsonKey()
  final bool correlationLargeAmountError;
  @override
  @JsonKey()
  final bool referenceNumberError;
  @override
  @JsonKey()
  final bool depositNumberError;
  @override
  final AppError? web21Error;
  @override
  final AppError? freeeError;

  @override
  String toString() {
    return 'IncomeAndExpenditureScreenState(accountTransactionHistory: $accountTransactionHistory, cashTransactionHistory: $cashTransactionHistory, freeeTransactionHistory: $freeeTransactionHistory, cashFreeeTransactionHistory: $cashFreeeTransactionHistory, isAccordionExpanded: $isAccordionExpanded, dateFrom: $dateFrom, dateTo: $dateTo, yearMonthList: $yearMonthList, selectedYearMonth: $selectedYearMonth, selectedYearMonthIndex: $selectedYearMonthIndex, currentFilterType: $currentFilterType, currentSortType: $currentSortType, currentSortOrder: $currentSortOrder, sortTitle: $sortTitle, dateSegment: $dateSegment, isCash: $isCash, isPayment: $isPayment, isExchange: $isExchange, isTransfer: $isTransfer, isOtherBanksTicket: $isOtherBanksTicket, isCorrection: $isCorrection, isDensai: $isDensai, isContinuation: $isContinuation, isSonota: $isSonota, isNarrowDown: $isNarrowDown, fromFirstDay: $fromFirstDay, fromLastDay: $fromLastDay, fromSelectedDay: $fromSelectedDay, toFirstDay: $toFirstDay, toLastDay: $toLastDay, toSelectedDay: $toSelectedDay, isFromDialog: $isFromDialog, isToDialog: $isToDialog, transactionCodes: $transactionCodes, apiType: $apiType, smallAmount: $smallAmount, largeAmount: $largeAmount, clientNameError: $clientNameError, digitSmallAmountError: $digitSmallAmountError, positiveSmallAmountError: $positiveSmallAmountError, correlationSmallAmountError: $correlationSmallAmountError, digitLargeAmountError: $digitLargeAmountError, positiveLargeAmountError: $positiveLargeAmountError, correlationLargeAmountError: $correlationLargeAmountError, referenceNumberError: $referenceNumberError, depositNumberError: $depositNumberError, web21Error: $web21Error, freeeError: $freeeError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IncomeAndExpenditureScreenStateImpl &&
            (identical(other.accountTransactionHistory, accountTransactionHistory) ||
                other.accountTransactionHistory == accountTransactionHistory) &&
            (identical(other.cashTransactionHistory, cashTransactionHistory) ||
                other.cashTransactionHistory == cashTransactionHistory) &&
            (identical(other.freeeTransactionHistory, freeeTransactionHistory) ||
                other.freeeTransactionHistory == freeeTransactionHistory) &&
            (identical(other.cashFreeeTransactionHistory, cashFreeeTransactionHistory) ||
                other.cashFreeeTransactionHistory ==
                    cashFreeeTransactionHistory) &&
            (identical(other.isAccordionExpanded, isAccordionExpanded) ||
                other.isAccordionExpanded == isAccordionExpanded) &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo) &&
            const DeepCollectionEquality()
                .equals(other._yearMonthList, _yearMonthList) &&
            (identical(other.selectedYearMonth, selectedYearMonth) ||
                other.selectedYearMonth == selectedYearMonth) &&
            (identical(other.selectedYearMonthIndex, selectedYearMonthIndex) ||
                other.selectedYearMonthIndex == selectedYearMonthIndex) &&
            (identical(other.currentFilterType, currentFilterType) ||
                other.currentFilterType == currentFilterType) &&
            (identical(other.currentSortType, currentSortType) ||
                other.currentSortType == currentSortType) &&
            (identical(other.currentSortOrder, currentSortOrder) ||
                other.currentSortOrder == currentSortOrder) &&
            (identical(other.sortTitle, sortTitle) ||
                other.sortTitle == sortTitle) &&
            (identical(other.dateSegment, dateSegment) ||
                other.dateSegment == dateSegment) &&
            (identical(other.isCash, isCash) || other.isCash == isCash) &&
            (identical(other.isPayment, isPayment) ||
                other.isPayment == isPayment) &&
            (identical(other.isExchange, isExchange) ||
                other.isExchange == isExchange) &&
            (identical(other.isTransfer, isTransfer) ||
                other.isTransfer == isTransfer) &&
            (identical(other.isOtherBanksTicket, isOtherBanksTicket) ||
                other.isOtherBanksTicket == isOtherBanksTicket) &&
            (identical(other.isCorrection, isCorrection) ||
                other.isCorrection == isCorrection) &&
            (identical(other.isDensai, isDensai) ||
                other.isDensai == isDensai) &&
            (identical(other.isContinuation, isContinuation) ||
                other.isContinuation == isContinuation) &&
            (identical(other.isSonota, isSonota) || other.isSonota == isSonota) &&
            (identical(other.isNarrowDown, isNarrowDown) || other.isNarrowDown == isNarrowDown) &&
            (identical(other.fromFirstDay, fromFirstDay) || other.fromFirstDay == fromFirstDay) &&
            (identical(other.fromLastDay, fromLastDay) || other.fromLastDay == fromLastDay) &&
            (identical(other.fromSelectedDay, fromSelectedDay) || other.fromSelectedDay == fromSelectedDay) &&
            (identical(other.toFirstDay, toFirstDay) || other.toFirstDay == toFirstDay) &&
            (identical(other.toLastDay, toLastDay) || other.toLastDay == toLastDay) &&
            (identical(other.toSelectedDay, toSelectedDay) || other.toSelectedDay == toSelectedDay) &&
            (identical(other.isFromDialog, isFromDialog) || other.isFromDialog == isFromDialog) &&
            (identical(other.isToDialog, isToDialog) || other.isToDialog == isToDialog) &&
            const DeepCollectionEquality().equals(other._transactionCodes, _transactionCodes) &&
            (identical(other.apiType, apiType) || other.apiType == apiType) &&
            (identical(other.smallAmount, smallAmount) || other.smallAmount == smallAmount) &&
            (identical(other.largeAmount, largeAmount) || other.largeAmount == largeAmount) &&
            (identical(other.clientNameError, clientNameError) || other.clientNameError == clientNameError) &&
            (identical(other.digitSmallAmountError, digitSmallAmountError) || other.digitSmallAmountError == digitSmallAmountError) &&
            (identical(other.positiveSmallAmountError, positiveSmallAmountError) || other.positiveSmallAmountError == positiveSmallAmountError) &&
            (identical(other.correlationSmallAmountError, correlationSmallAmountError) || other.correlationSmallAmountError == correlationSmallAmountError) &&
            (identical(other.digitLargeAmountError, digitLargeAmountError) || other.digitLargeAmountError == digitLargeAmountError) &&
            (identical(other.positiveLargeAmountError, positiveLargeAmountError) || other.positiveLargeAmountError == positiveLargeAmountError) &&
            (identical(other.correlationLargeAmountError, correlationLargeAmountError) || other.correlationLargeAmountError == correlationLargeAmountError) &&
            (identical(other.referenceNumberError, referenceNumberError) || other.referenceNumberError == referenceNumberError) &&
            (identical(other.depositNumberError, depositNumberError) || other.depositNumberError == depositNumberError) &&
            (identical(other.web21Error, web21Error) || other.web21Error == web21Error) &&
            (identical(other.freeeError, freeeError) || other.freeeError == freeeError));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        accountTransactionHistory,
        cashTransactionHistory,
        freeeTransactionHistory,
        cashFreeeTransactionHistory,
        isAccordionExpanded,
        dateFrom,
        dateTo,
        const DeepCollectionEquality().hash(_yearMonthList),
        selectedYearMonth,
        selectedYearMonthIndex,
        currentFilterType,
        currentSortType,
        currentSortOrder,
        sortTitle,
        dateSegment,
        isCash,
        isPayment,
        isExchange,
        isTransfer,
        isOtherBanksTicket,
        isCorrection,
        isDensai,
        isContinuation,
        isSonota,
        isNarrowDown,
        fromFirstDay,
        fromLastDay,
        fromSelectedDay,
        toFirstDay,
        toLastDay,
        toSelectedDay,
        isFromDialog,
        isToDialog,
        const DeepCollectionEquality().hash(_transactionCodes),
        apiType,
        smallAmount,
        largeAmount,
        clientNameError,
        digitSmallAmountError,
        positiveSmallAmountError,
        correlationSmallAmountError,
        digitLargeAmountError,
        positiveLargeAmountError,
        correlationLargeAmountError,
        referenceNumberError,
        depositNumberError,
        web21Error,
        freeeError
      ]);

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IncomeAndExpenditureScreenStateImplCopyWith<
          _$IncomeAndExpenditureScreenStateImpl>
      get copyWith => __$$IncomeAndExpenditureScreenStateImplCopyWithImpl<
          _$IncomeAndExpenditureScreenStateImpl>(this, _$identity);
}

abstract class _IncomeAndExpenditureScreenState
    implements IncomeAndExpenditureScreenState {
  const factory _IncomeAndExpenditureScreenState(
      {final AccountTransactionHistory accountTransactionHistory,
      final AccountTransactionHistory cashTransactionHistory,
      final FreeeTransactionHistory freeeTransactionHistory,
      final FreeeTransactionHistory cashFreeeTransactionHistory,
      final bool isAccordionExpanded,
      final DateTime? dateFrom,
      final DateTime? dateTo,
      final List<DateTime> yearMonthList,
      final DateTime? selectedYearMonth,
      final int selectedYearMonthIndex,
      final TransactionHistoryFilterType currentFilterType,
      final TransactionHistorySortType currentSortType,
      final TransactionHistorySortOrder currentSortOrder,
      final String sortTitle,
      final DateSegment dateSegment,
      final bool isCash,
      final bool isPayment,
      final bool isExchange,
      final bool isTransfer,
      final bool isOtherBanksTicket,
      final bool isCorrection,
      final bool isDensai,
      final bool isContinuation,
      final bool isSonota,
      final bool isNarrowDown,
      final DateTime? fromFirstDay,
      final DateTime? fromLastDay,
      final DateTime? fromSelectedDay,
      final DateTime? toFirstDay,
      final DateTime? toLastDay,
      final DateTime? toSelectedDay,
      final bool isFromDialog,
      final bool isToDialog,
      final List<TransactionTypeCode> transactionCodes,
      final AccountApiType apiType,
      final String smallAmount,
      final String largeAmount,
      final bool clientNameError,
      final bool digitSmallAmountError,
      final bool positiveSmallAmountError,
      final bool correlationSmallAmountError,
      final bool digitLargeAmountError,
      final bool positiveLargeAmountError,
      final bool correlationLargeAmountError,
      final bool referenceNumberError,
      final bool depositNumberError,
      final AppError? web21Error,
      final AppError? freeeError}) = _$IncomeAndExpenditureScreenStateImpl;

  @override
  AccountTransactionHistory get accountTransactionHistory;
  @override
  AccountTransactionHistory get cashTransactionHistory;
  @override
  FreeeTransactionHistory get freeeTransactionHistory;
  @override
  FreeeTransactionHistory get cashFreeeTransactionHistory;
  @override
  bool get isAccordionExpanded;

  /// HACK　入出金明細の開始日付、終了日付に関してはprovider生成時に遅延初期化にて代入し
  /// 必須項目とする
  @override
  DateTime? get dateFrom;
  @override
  DateTime? get dateTo;
  @override
  List<DateTime> get yearMonthList;
  @override
  DateTime? get selectedYearMonth;
  @override
  int get selectedYearMonthIndex;
  @override
  TransactionHistoryFilterType get currentFilterType;
  @override
  TransactionHistorySortType get currentSortType;
  @override
  TransactionHistorySortOrder get currentSortOrder;
  @override
  String get sortTitle;
  @override
  DateSegment get dateSegment;
  @override
  bool get isCash;
  @override
  bool get isPayment;
  @override
  bool get isExchange;
  @override
  bool get isTransfer;
  @override
  bool get isOtherBanksTicket;
  @override
  bool get isCorrection;
  @override
  bool get isDensai;
  @override
  bool get isContinuation;
  @override
  bool get isSonota;
  @override
  bool get isNarrowDown;
  @override
  DateTime? get fromFirstDay;
  @override
  DateTime? get fromLastDay;
  @override
  DateTime? get fromSelectedDay;
  @override
  DateTime? get toFirstDay;
  @override
  DateTime? get toLastDay;
  @override
  DateTime? get toSelectedDay;
  @override
  bool get isFromDialog;
  @override
  bool get isToDialog;
  @override
  List<TransactionTypeCode> get transactionCodes; // 他行、自行を分岐するためのフラグ
  @override
  AccountApiType get apiType;
  @override
  String get smallAmount;
  @override
  String get largeAmount;
  @override
  bool get clientNameError;
  @override
  bool get digitSmallAmountError;
  @override
  bool get positiveSmallAmountError;
  @override
  bool get correlationSmallAmountError;
  @override
  bool get digitLargeAmountError;
  @override
  bool get positiveLargeAmountError;
  @override
  bool get correlationLargeAmountError;
  @override
  bool get referenceNumberError;
  @override
  bool get depositNumberError;
  @override
  AppError? get web21Error;
  @override
  AppError? get freeeError;

  /// Create a copy of IncomeAndExpenditureScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IncomeAndExpenditureScreenStateImplCopyWith<
          _$IncomeAndExpenditureScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
