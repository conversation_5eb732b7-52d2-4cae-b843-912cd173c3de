import 'dart:async';

import 'package:dtp_app/business_logics/interfaces/account_transaction_history_repository.dart';
import 'package:dtp_app/business_logics/interfaces/freee_transaction_history_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/repositories/account_transaction_history/account_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/freee_transaction_history/freee_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

part 'income_and_expenditure_screen_notifier.freezed.dart';

enum DateSegment {
  notSpecified,
  tradingDate,
  startingDate,
}

@freezed
class IncomeAndExpenditureScreenState with _$IncomeAndExpenditureScreenState {
  const factory IncomeAndExpenditureScreenState({
    @Default(
      AccountTransactionHistory.empty(),
    )
    AccountTransactionHistory accountTransactionHistory,
    @Default(
      AccountTransactionHistory.empty(),
    )
    AccountTransactionHistory cashTransactionHistory,
    @Default(
      FreeeTransactionHistory.empty(),
    )
    FreeeTransactionHistory freeeTransactionHistory,
    @Default(
      FreeeTransactionHistory.empty(),
    )
    FreeeTransactionHistory cashFreeeTransactionHistory,
    @Default(false) bool isAccordionExpanded,

    /// HACK　入出金明細の開始日付、終了日付に関してはprovider生成時に遅延初期化にて代入し
    /// 必須項目とする
    DateTime? dateFrom,
    DateTime? dateTo,
    @Default(<DateTime>[]) List<DateTime> yearMonthList,
    DateTime? selectedYearMonth,
    @Default(0) int selectedYearMonthIndex,
    @Default(
      TransactionHistoryFilterType.all,
    )
    TransactionHistoryFilterType currentFilterType,
    @Default(
      TransactionHistorySortType.date,
    )
    TransactionHistorySortType currentSortType,
    @Default(
      TransactionHistorySortOrder.descending,
    )
    TransactionHistorySortOrder currentSortOrder,
    @Default('') String sortTitle,
    @Default(DateSegment.notSpecified) DateSegment dateSegment,
    @Default(false) bool isCash,
    @Default(false) bool isPayment,
    @Default(false) bool isExchange,
    @Default(false) bool isTransfer,
    @Default(false) bool isOtherBanksTicket,
    @Default(false) bool isCorrection,
    @Default(false) bool isDensai,
    @Default(false) bool isContinuation,
    @Default(false) bool isSonota,
    @Default(false) bool isNarrowDown,
    DateTime? fromFirstDay,
    DateTime? fromLastDay,
    DateTime? fromSelectedDay,
    DateTime? toFirstDay,
    DateTime? toLastDay,
    DateTime? toSelectedDay,
    @Default(false) bool isFromDialog,
    @Default(false) bool isToDialog,
    @Default(<TransactionTypeCode>[])
    List<TransactionTypeCode> transactionCodes,
    // 他行、自行を分岐するためのフラグ
    @Default(AccountApiType.web21) AccountApiType apiType,
    @Default('') String smallAmount,
    @Default('') String largeAmount,
    @Default(false) bool clientNameError,
    @Default(false) bool digitSmallAmountError,
    @Default(false) bool positiveSmallAmountError,
    @Default(false) bool correlationSmallAmountError,
    @Default(false) bool digitLargeAmountError,
    @Default(false) bool positiveLargeAmountError,
    @Default(false) bool correlationLargeAmountError,
    @Default(false) bool referenceNumberError,
    @Default(false) bool depositNumberError,
    AppError? web21Error,
    AppError? freeeError,
  }) = _IncomeAndExpenditureScreenState;
}

final incomeAndExpenditureScreenProvider = StateNotifierProvider.autoDispose<
    IncomeAndExpenditureScreenNotifier, IncomeAndExpenditureScreenState>(
  (ref) => IncomeAndExpenditureScreenNotifier(
    ref.read(accountTransactionHistoryRepositoryProvider),
    ref.read(loginRepositoryProvider),
    ref.read(freeeTransactionHistoryRepositoryProvider),
    ref.read(operationLogControllerProvider),
  ),
);

class IncomeAndExpenditureScreenNotifier
    extends StateNotifier<IncomeAndExpenditureScreenState> {
  IncomeAndExpenditureScreenNotifier(
    this._repository,
    this.loginRepository,
    this._freeeRepository,
    this._logController,
  ) : super(const IncomeAndExpenditureScreenState());

  final AccountTransactionHistoryRepository _repository;
  final FreeeTransactionHistoryRepository _freeeRepository;
  final OperationLogController _logController;
  final LoginRepository loginRepository;

  final clientNameController = TextEditingController();
  final smallAmountController = TextEditingController();
  final largeAmountController = TextEditingController();
  final referenceNumberController = TextEditingController();
  final depositNumberController = TextEditingController();

  // 共通エラー
  final defaultError = AppError(
    message: ErrorInfo.defaultErrorMessage,
    code: ErrorInfo.defaultErrorCode,
  );

  void clearError() {
    state = state.copyWith(web21Error: null, freeeError: null);
  }

  Future<void> loadAnser({
    required String dateTo,
    required String accountId,
  }) async {
    try {
      state = state.copyWith(
        apiType: AccountApiType.web21,
        accountTransactionHistory: const AccountTransactionHistory.empty(),
        cashTransactionHistory: const AccountTransactionHistory.empty(),
        web21Error: null,
        dateFrom: _getFirstDate(dateTo),
        dateTo: _toDateTime(dateTo),
        selectedYearMonth: _toDateTime(dateTo),
        yearMonthList: _getPreviousYearMonths(dateTo),
      );

      await _loadWeb21Transactions(accountId, dateTo);
    } on Exception catch (error) {
      state = state.copyWith(
        web21Error: error.toAppError().copyWith(
              isShowOnDialog: false,
            ),
      );
    } finally {
      unawaited(_sendOperationLogSMBC());
    }
  }

  Future<void> loadFreee({
    required String dateTo,
    required int walletableId,
    required FreeeTransactionsType walletableType,
  }) async {
    try {
      state = state.copyWith(
        apiType: AccountApiType.freee,
        freeeTransactionHistory: const FreeeTransactionHistory.empty(),
        cashFreeeTransactionHistory: const FreeeTransactionHistory.empty(),
      );

      state = state.copyWith(
        freeeError: null,
        dateFrom: _getFirstDate(dateTo),
        dateTo: _toDateTime(dateTo),
        selectedYearMonth: _toDateTime(dateTo),
        yearMonthList: _getPreviousYearMonths(dateTo),
      );

      await _loadFreeeTransactions(walletableId, dateTo, walletableType);
    } on Exception catch (error) {
      state = state.copyWith(
        freeeError: error.toAppError().copyWith(isShowOnDialog: false),
      );
    } finally {
      unawaited(_sendOperationLogOtherBank());
    }
  }

  Future<void> _loadWeb21Transactions(String accountId, String dateTo) async {
    try {
      final res = await _repository.getAccountTransactionHistory(
        accountId: accountId,
        dateFrom: _getFirstDate(dateTo),
        dateTo: _toDateTime(dateTo),
        forceRefresh: true,
      );

      res.when(
        success: (value) {
          state = state.copyWith(
            accountTransactionHistory: value,
            cashTransactionHistory: value,
          );
        },
        failure: (error) {
          Log.e(error);
          state =
              state.copyWith(web21Error: error.copyWith(isShowOnDialog: false));
        },
      );
    } on Exception catch (error) {
      state = state.copyWith(
        web21Error: error.toAppError().copyWith(isShowOnDialog: false),
      );
    }
  }

  Future<void> _loadFreeeTransactions(
    int walletableId,
    String dateTo,
    FreeeTransactionsType freeeTransactionsType,
  ) async {
    try {
      final res = await _freeeRepository.getFreeeTransactionHistory(
        walletableId: walletableId,
        forceRefresh: true,
        walletableType: freeeTransactionsType,
        startDateFrom: _getFirstDate(dateTo),
        startDateTo: _toDateTime(dateTo),
      );

      res.when(
        success: (value) {
          state = state.copyWith(
            freeeTransactionHistory: value,
            cashFreeeTransactionHistory: value,
          );
        },
        failure: (error) {
          Log.e(error);
          state =
              state.copyWith(freeeError: error.copyWith(isShowOnDialog: false));
        },
      );
    } on Exception catch (error) {
      state = state.copyWith(
        freeeError: error.toAppError().copyWith(isShowOnDialog: false),
      );
    }
  }

  /// 顧客操作ログ送信処理（自行口座入出金明細）
  Future<void> _sendOperationLogSMBC() => _logController.sendOperationLog(
        functionLog: OperationLogMessage.smbcBankTransactions,
        operationLog: OperationLogMessage.inquiry,
        resultLog: state.web21Error == null
            ? OperationLogMessage.normal
            : OperationLogMessage.abnormality,
        errorIdLog: state.web21Error?.code ?? '',
      );

  /// 顧客操作ログ送信処理（他行口座入出金明細）
  Future<void> _sendOperationLogOtherBank() => _logController.sendOperationLog(
        functionLog: OperationLogMessage.otherBankTransactions,
        operationLog: OperationLogMessage.inquiry,
        resultLog: state.freeeError == null
            ? OperationLogMessage.normal
            : OperationLogMessage.abnormality,
        errorIdLog: state.freeeError?.code ?? '',
      );

  void openAccordion() {
    state = state.copyWith(isAccordionExpanded: true);
  }

  void closeAccordion() {
    state = state.copyWith(isAccordionExpanded: false);
  }

  Future<void> getAccountTransactionHistory({
    required String accountId,
    required DateTime dateFrom,
    required DateTime dateTo,
    required DateTime yearMonth,
    required int selectedYearMonthIndex,
    required TransactionHistoryFilterType filterType,
    required TransactionHistorySortType sortType,
    required TransactionHistorySortOrder order,
    required String sortTitle,
    bool forceRefresh = false,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    List<TransactionTypeCode>? transactionTypeCode,
    String? payerName,
    int? minAmount,
    int? maxAmount,
    String? referenceNumber,
  }) async {
    // エラー情報の削除
    clearError();
    final res = await _repository.getAccountTransactionHistory(
      accountId: accountId,
      dateFrom: dateFrom,
      dateTo: dateTo,
      forceRefresh: forceRefresh,
      filterType: filterType,
      sortType: sortType,
      order: order,
      startingDateFrom: startingDateFrom,
      startingDateTo: startingDateTo,
      tradingDateFrom: tradingDateFrom,
      tradingDateTo: tradingDateTo,
      transactionTypeCode: transactionTypeCode,
      payerName: payerName,
      minAmount: minAmount,
      maxAmount: maxAmount,
      referenceNumber: referenceNumber,
    );

    res.when(
      success: (value) {
        state = state.copyWith(
          accountTransactionHistory: value,
          dateFrom: dateFrom,
          dateTo: dateTo,
          selectedYearMonth: yearMonth,
          selectedYearMonthIndex: selectedYearMonthIndex,
          currentFilterType: filterType,
          currentSortType: sortType,
          currentSortOrder: order,
          sortTitle: sortTitle,
        );
      },
      failure: (error) {
        Log.e(error);
        state = state.copyWith(
          web21Error: error.copyWith(isShowOnDialog: false),
          selectedYearMonth: yearMonth,
        );
      },
    );
  }

  Future<void> getFreeeTransactionHistory({
    required int walletableId,
    required FreeeTransactionsType walletableType,
    // 選択されている月の初日を入れる
    required DateTime dateFrom,
    // 選択されている月の最終日を入れる
    required DateTime dateTo,
    // 絞り込みの際の日時を入れる
    DateTime? tradingDateFrom,
    // 絞り込みの際の日時を入れる
    DateTime? tradingDateTo,
    required DateTime yearMonth,
    required int selectedYearMonthIndex,
    required String sortTitle,
    required TransactionHistorySortType sortType,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    double? minAmount,
    double? maxAmount,
  }) async {
    // エラー情報の削除
    clearError();
    final res = await _freeeRepository.getFreeeTransactionHistory(
      startDateFrom: dateFrom,
      startDateTo: dateTo,
      walletableId: walletableId,
      walletableType: walletableType,
      forceRefresh: forceRefresh,
      filterType: filterType,
      sortType: sortType,
      order: order,
      tradingDateFrom: tradingDateFrom,
      tradingDateTo: tradingDateTo,
      minAmount: minAmount,
      maxAmount: maxAmount,
    );

    res.when(
      success: (value) {
        state = state.copyWith(
          freeeTransactionHistory: value,
          dateFrom: dateFrom,
          dateTo: dateTo,
          selectedYearMonth: yearMonth,
          selectedYearMonthIndex: selectedYearMonthIndex,
          currentFilterType: filterType,
          currentSortType: sortType,
          currentSortOrder: order,
          sortTitle: sortTitle,
        );
      },
      failure: (error) {
        Log.e(error);
        state = state.copyWith(
          freeeError: error.copyWith(isShowOnDialog: false),
          selectedYearMonth: yearMonth,
        );
      },
    );
  }

  ///  選択した年月が前回選択時と同一の年月であるかを判定
  bool checkSameYearMonth({
    required DateTime yearMonth,
    required DateTime selectedYearMonth,
  }) {
    final base = DateTime(yearMonth.year, yearMonth.month);
    final selected = DateTime(selectedYearMonth.year, selectedYearMonth.month);
    return base == selected;
  }

  ///  選択した年月が当月であるかを判定
  bool checkCurrentMonth({
    required DateTime yearMonth,
    required String serverDate,
  }) {
    final dateTime = _toDateTime(serverDate);
    return yearMonth.year == dateTime.year && yearMonth.month == dateTime.month;
  }

  DateTime _toDateTime(String serverDate) {
    return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
  }

  DateTime _getFirstDate(String serverDate) {
    final dateTime = _toDateTime(serverDate);
    return DateTime(dateTime.year, dateTime.month, 1);
  }

  /// 当月から13ヶ月前までの年月を取得
  List<DateTime> _getPreviousYearMonths(String serverDate) {
    final baseTime = _toDateTime(serverDate);
    final List<DateTime> yearMonthList = [];

    for (int i = 0; i < 12; i++) {
      //先月の末日を取得
      final lastMonthLastDay = DateTime(baseTime.year, baseTime.month - i, 0);
      yearMonthList.add(lastMonthLastDay);
    }
    yearMonthList.insert(0, baseTime);

    return yearMonthList;
  }

  void changeDateSegment(DateSegment value) {
    state = state.copyWith(dateSegment: value);
  }

  void changeCash() {
    state = state.copyWith(isCash: !state.isCash);
  }

  void changePayment() {
    state = state.copyWith(isPayment: !state.isPayment);
  }

  void changeExchange() {
    state = state.copyWith(isExchange: !state.isExchange);
  }

  void changeTransfer() {
    state = state.copyWith(isTransfer: !state.isTransfer);
  }

  void changeOtherBanksTicket() {
    state = state.copyWith(isOtherBanksTicket: !state.isOtherBanksTicket);
  }

  void changeCorrection() {
    state = state.copyWith(isCorrection: !state.isCorrection);
  }

  void changeDensai() {
    state = state.copyWith(isDensai: !state.isDensai);
  }

  void changeContinuation() {
    state = state.copyWith(isContinuation: !state.isContinuation);
  }

  void changeSonota() {
    state = state.copyWith(isSonota: !state.isSonota);
  }

  void changeIsToDialog([bool isFlag = true]) {
    state = state.copyWith(isToDialog: isFlag);
  }

  void changeIsFromDialog([bool isFlag = true]) {
    state = state.copyWith(isFromDialog: isFlag);
  }

  void changeToSelectedDay(DateTime selectedDay) {
    state = state.copyWith(toSelectedDay: selectedDay);
  }

  void changeFromSelectedDay(DateTime selectedDay) {
    state = state.copyWith(fromSelectedDay: selectedDay);
  }

  void reset() {
    state = state.copyWith(
      dateSegment: DateSegment.notSpecified,
      isFromDialog: false,
      isToDialog: false,
      isCash: false,
      isPayment: false,
      isExchange: false,
      isTransfer: false,
      isOtherBanksTicket: false,
      isCorrection: false,
      isDensai: false,
      isSonota: false,
      transactionCodes: <TransactionTypeCode>[],
      isNarrowDown: false,
    );
    clientNameController.clear();
    depositNumberController.clear();
    referenceNumberController.clear();
    largeAmountController.clear();
    smallAmountController.clear();
  }

  void checkFirstDayAndLastDayCalendar(DateTime serverDate) {
    if (state.selectedYearMonth == null) {
      return;
    }
    final selectedMonth = state.selectedYearMonth;
    final fromSelectedDay = state.fromSelectedDay;
    final toSelectedDay = state.toSelectedDay;
    final selectedMonthFirstDate =
        DateTime(selectedMonth!.year, selectedMonth.month, 1);
    final selectedMonthEndDate =
        DateTime(selectedMonth.year, selectedMonth.month + 1, 0);
    final previousMonthFirstDate =
        DateTime(selectedMonth.year, selectedMonth.month - 1, 1);
    final selectedNextMonthEnd =
        DateTime(selectedMonth.year, selectedMonth.month + 2, 0);

    final isThisMonth = checkSameYearMonth(
      yearMonth: serverDate,
      selectedYearMonth: selectedMonth,
    );

    if (!state.isFromDialog && !state.isToDialog) {
      // 初期状態
      state = state.copyWith(
        fromFirstDay: previousMonthFirstDate,
        fromLastDay: isThisMonth ? serverDate : selectedMonthEndDate,
        toFirstDay: selectedMonthFirstDate,
        toLastDay: selectedNextMonthEnd,
        fromSelectedDay: selectedMonthFirstDate,
        toSelectedDay: isThisMonth ? serverDate : selectedMonthEndDate,
      );
    }
    if (state.isToDialog) {
      // Toダイアログ設定時
      state = state.copyWith(
        fromFirstDay: previousMonthFirstDate,
        fromLastDay: toSelectedDay,
      );
    }
    if (state.isFromDialog) {
      // Fromダイアログ設定時
      state = state.copyWith(
        toFirstDay: fromSelectedDay,
        toLastDay: selectedNextMonthEnd,
      );
    }
    if (state.isToDialog && !state.isFromDialog) {
      // Toダイアログを最初に設定した場合
      state = state.copyWith(
        toFirstDay: selectedMonthFirstDate,
        toLastDay: selectedNextMonthEnd,
      );
    }
  }

  void judgeCheckBox(TransactionTypeCode type, bool isSelected) {
    final newList = List<TransactionTypeCode>.from(state.transactionCodes);
    if (isSelected) {
      newList.remove(type);
      state = state.copyWith(transactionCodes: newList);
    } else {
      newList.add(type);
      state = state.copyWith(transactionCodes: newList);
    }
  }

  Future<void> narrowDown({
    required String accountId,
    required DateTime dateFrom,
    required DateTime dateTo,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortType sortType = TransactionHistorySortType.date,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    List<TransactionTypeCode>? transactionTypeCode,
    String? payerName,
    int? minAmount,
    int? maxAmount,
    String? referenceNumber,
    bool isNarrowDown = true,
  }) async {
    // エラー情報の削除
    clearError();
    final res = await _repository.getAccountTransactionHistory(
      accountId: accountId,
      dateFrom: dateFrom,
      dateTo: dateTo,
      forceRefresh: forceRefresh,
      filterType: filterType,
      sortType: sortType,
      order: order,
      startingDateFrom: startingDateFrom,
      startingDateTo: startingDateTo,
      tradingDateFrom: tradingDateFrom,
      tradingDateTo: tradingDateTo,
      transactionTypeCode: transactionTypeCode,
      payerName: payerName,
      minAmount: minAmount,
      maxAmount: maxAmount,
      referenceNumber: referenceNumber,
    );

    res.when(
      success: (value) {
        state = state.copyWith(
          accountTransactionHistory: value,
          isNarrowDown: isNarrowDown,
        );
      },
      failure: (error) {
        Log.e(error);
        state =
            state.copyWith(web21Error: error.copyWith(isShowOnDialog: false));
      },
    );
  }

  Future<void> narrowDownFreee({
    required DateTime dateFrom,
    required DateTime dateTo,
    required int walletableId,
    required FreeeTransactionsType walletableType,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    required DateTime tradingDateFrom,
    required DateTime tradingDateTo,
    double? minAmount,
    double? maxAmount,
    bool isNarrowDown = true,
  }) async {
    // エラー情報の削除
    clearError();
    final res = await _freeeRepository.getFreeeTransactionHistory(
      walletableId: walletableId,
      walletableType: walletableType,
      forceRefresh: forceRefresh,
      filterType: filterType,
      order: order,
      startDateFrom: dateFrom,
      startDateTo: dateTo,
      tradingDateFrom: tradingDateFrom,
      tradingDateTo: tradingDateTo,
      minAmount: minAmount,
      maxAmount: maxAmount,
    );

    res.when(
      success: (value) {
        state = state.copyWith(
          freeeTransactionHistory: value,
          isNarrowDown: isNarrowDown,
        );
      },
      failure: (error) {
        Log.e(error);
        state =
            state.copyWith(freeeError: error.copyWith(isShowOnDialog: false));
      },
    );
  }

  void validateClientName(bool value) {
    state = state.copyWith(clientNameError: value);
  }

  void validateDigitAmount({
    required bool value,
    bool isSmallAmount = false,
  }) {
    if (isSmallAmount) {
      state = state.copyWith(digitSmallAmountError: value);
    } else {
      state = state.copyWith(digitLargeAmountError: value);
    }
  }

  void validatePositiveAmount({
    required bool value,
    bool isSmallAmount = false,
  }) {
    if (isSmallAmount) {
      state = state.copyWith(positiveSmallAmountError: value);
    } else {
      state = state.copyWith(positiveLargeAmountError: value);
    }
  }

  void validateCorrelationAmount({
    required bool smallAmount,
    required bool largeAmount,
  }) {
    state = state.copyWith(
      correlationSmallAmountError: smallAmount,
      correlationLargeAmountError: largeAmount,
    );
  }

  void clearSmallAmountError() {
    state = state.copyWith(
      digitSmallAmountError: false,
      positiveSmallAmountError: false,
      correlationSmallAmountError: false,
      correlationLargeAmountError: false,
    );
  }

  void clearLargeAmountError() {
    state = state.copyWith(
      digitLargeAmountError: false,
      positiveLargeAmountError: false,
      correlationLargeAmountError: false,
      correlationSmallAmountError: false,
    );
  }

  void validateReferenceNumber(bool value) {
    state = state.copyWith(referenceNumberError: value);
  }

  void validateDepositNumber(bool value) {
    state = state.copyWith(depositNumberError: value);
  }

  bool checkInvalid() {
    final result = state.clientNameError ||
        state.digitSmallAmountError ||
        state.positiveSmallAmountError ||
        state.correlationSmallAmountError ||
        state.correlationLargeAmountError ||
        state.digitLargeAmountError ||
        state.positiveLargeAmountError ||
        state.referenceNumberError ||
        state.depositNumberError;
    return result;
  }
}
