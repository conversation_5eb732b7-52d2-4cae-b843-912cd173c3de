import 'package:dtp_app/business_logics/interfaces/business_blockage_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/repositories/business_blockage/business_blockage_repository_impl.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BusinessBlockageController {
  BusinessBlockageController({
    required this.businessBlockageRepository,
  });

  final BusinessBlockageRepository businessBlockageRepository;
  bool _isBlocked = true;
  AppError? _businessBlockageError;

  Future<void> getBlockageStatus({
    required String functionId,
  }) async {
    clearBlockageStatus();
    final result = await businessBlockageRepository.getBlockageStatus(
      functionId: functionId,
    );
    result.when(
      success: (blockageStatus) {
        // 閉塞状態が0（利用可能）である場合はfalse、それ以外はtrueを設定する
        _isBlocked = blockageStatus != '0';
      },
      failure: (error) {
        // エラー情報も保持させるが、ph05_sp6時点では利用していない
        _businessBlockageError = error;
      },
    );
  }

  void clearBlockageStatus() {
    _isBlocked = true;
    _businessBlockageError = null;
  }

  bool get isBlocked => _isBlocked;
  AppError? get businessBlockageError => _businessBlockageError;
}

final businessBlockageControllerProvider =
    Provider.autoDispose<BusinessBlockageController>((ref) {
  return BusinessBlockageController(
    businessBlockageRepository: ref.read(businessBlockageRepositoryProvider),
  );
});
