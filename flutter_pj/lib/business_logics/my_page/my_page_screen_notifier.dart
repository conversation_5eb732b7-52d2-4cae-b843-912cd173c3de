import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/operation_log_repository.dart';
import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/datas/app_information_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/user_info/user_info.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/operation_log_repository/operation_log_repository_impl.dart';
import 'package:dtp_app/repositories/user/user_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'my_page_screen_notifier.freezed.dart';

@freezed
class MyPageScreenState with _$MyPageScreenState {
  const factory MyPageScreenState({
    @Default(UserInfo()) UserInfo userInfo,
    @Default(false) bool isLoading,
    @Default('') String appName,
    @Default('') String versionName,
    AppError? error,
  }) = _MyPageScreenState;
}

final myPageScreenProvider =
    StateNotifierProvider.autoDispose<MyPageScreenNotifier, MyPageScreenState>(
  (ref) => MyPageScreenNotifier(
    userRepository: ref.read(userRepositoryProvider),
    loginRepository: ref.read(loginRepositoryProvider),
    operationLogRepository: ref.read(operationLogRepositoryProvider),
    appInformationManager: ref.read(appInformationManagerProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
    state: const MyPageScreenState(),
  ),
);

class MyPageScreenNotifier extends StateNotifier<MyPageScreenState> {
  MyPageScreenNotifier({
    required this.userRepository,
    required this.loginRepository,
    required this.operationLogRepository,
    required this.appInformationManager,
    required this.analyticsLogController,
    required MyPageScreenState state,
  }) : super(state);

  final UserRepository userRepository;
  final LoginRepository loginRepository;
  final OperationLogRepository operationLogRepository;
  final AppInformationManager appInformationManager;
  final AnalyticsLogController analyticsLogController;

  void _onLoadStarted() {
    state = state.copyWith(isLoading: true);
  }

  void _onLoadFinished() {
    state = state.copyWith(isLoading: false);
  }

  Future<void> initialize() async {
    await _getUserInfo();
    await _getAppInformation();
  }

  Future<void> _getUserInfo() async {
    _onLoadStarted();
    (await userRepository.getUserInfo()).when(
      success: (userInfo) {
        state = state.copyWith(
          userInfo: userInfo,
        );
      },
      failure: (error) {
        // 他のAPIではhandleError内でerror.codeの内容を参照してセッションタイムアウトの検知を行っているが、
        // 本APIではエラー内容に関わらずerror.codeを固定値で返却しているため、セッションタイムアウトの検知を本メソッド内で行う。
        if (error.isSessionTimeOutError) {
          state = state.copyWith(
            error: error.copyWith(clearErrorOnShowDialog: clearError),
          );
          return;
        }
        state = state.copyWith(
          error: error.copyWith(
            message: MyPageErrorInfo.failedToLoadUserInfoMessage,
            code: MyPageErrorInfo.failedToLoadUserInfoCode,
            okButtonText: MyPageErrorInfo.failedToLoadUserInfoButtonText,
            clearErrorOnShowDialog: clearError,
          ),
        );
      },
    );
    _onLoadFinished();
  }

  Future<void> logout() async {
    //顧客操作履歴の送信にはsessionIDとsessionが必要なため
    //マイページからのログアウト時には、logout処理が完了する前に顧客操作履歴を送信する。
    await _sendLogoutLog();
    await loginRepository.logout();
  }

  /// 暗号化Cookieの最新化
  Future<void> refreshEncryptedCookie() async {
    _onLoadStarted();
    final result = await loginRepository.getEncryptedCookie();
    result.when(
      success: (_) {},
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearError),
        );
      },
    );
    _onLoadFinished();
  }

  /// ログアウトする際にエラー情報をリセットするメソッド
  void clearError() {
    state = state.copyWith(error: null);
  }

  Future<void> _getAppInformation() async {
    final appInformation = await appInformationManager.load();
    state = state.copyWith(
      appName: appInformation.appName,
      versionName: appInformation.versionName,
    );
  }

  void updateDtpId(String dtpId) {
    final userInfo = state.userInfo.copyWith(dtpId: dtpId);
    state = state.copyWith(userInfo: userInfo);
  }

  /// ログアウトの顧客操作ログを送信
  Future<void> _sendLogoutLog() async {
    await operationLogRepository.sendOperationLog(
      functionLog: OperationLogMessage.logout,
      operationLog: OperationLogMessage.logout,
      resultLog: state.error == null
          ? OperationLogMessage.normal
          : OperationLogMessage.abnormality,
      errorIdLog: state.error?.code ?? '',
    );
  }
}
