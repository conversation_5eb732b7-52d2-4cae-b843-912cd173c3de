// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'my_page_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MyPageScreenState {
  UserInfo get userInfo => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String get appName => throw _privateConstructorUsedError;
  String get versionName => throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MyPageScreenStateCopyWith<MyPageScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MyPageScreenStateCopyWith<$Res> {
  factory $MyPageScreenStateCopyWith(
          MyPageScreenState value, $Res Function(MyPageScreenState) then) =
      _$MyPageScreenStateCopyWithImpl<$Res, MyPageScreenState>;
  @useResult
  $Res call(
      {UserInfo userInfo,
      bool isLoading,
      String appName,
      String versionName,
      AppError? error});

  $UserInfoCopyWith<$Res> get userInfo;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$MyPageScreenStateCopyWithImpl<$Res, $Val extends MyPageScreenState>
    implements $MyPageScreenStateCopyWith<$Res> {
  _$MyPageScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userInfo = null,
    Object? isLoading = null,
    Object? appName = null,
    Object? versionName = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      userInfo: null == userInfo
          ? _value.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as UserInfo,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      versionName: null == versionName
          ? _value.versionName
          : versionName // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<$Res> get userInfo {
    return $UserInfoCopyWith<$Res>(_value.userInfo, (value) {
      return _then(_value.copyWith(userInfo: value) as $Val);
    });
  }

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MyPageScreenStateImplCopyWith<$Res>
    implements $MyPageScreenStateCopyWith<$Res> {
  factory _$$MyPageScreenStateImplCopyWith(_$MyPageScreenStateImpl value,
          $Res Function(_$MyPageScreenStateImpl) then) =
      __$$MyPageScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UserInfo userInfo,
      bool isLoading,
      String appName,
      String versionName,
      AppError? error});

  @override
  $UserInfoCopyWith<$Res> get userInfo;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$MyPageScreenStateImplCopyWithImpl<$Res>
    extends _$MyPageScreenStateCopyWithImpl<$Res, _$MyPageScreenStateImpl>
    implements _$$MyPageScreenStateImplCopyWith<$Res> {
  __$$MyPageScreenStateImplCopyWithImpl(_$MyPageScreenStateImpl _value,
      $Res Function(_$MyPageScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userInfo = null,
    Object? isLoading = null,
    Object? appName = null,
    Object? versionName = null,
    Object? error = freezed,
  }) {
    return _then(_$MyPageScreenStateImpl(
      userInfo: null == userInfo
          ? _value.userInfo
          : userInfo // ignore: cast_nullable_to_non_nullable
              as UserInfo,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      appName: null == appName
          ? _value.appName
          : appName // ignore: cast_nullable_to_non_nullable
              as String,
      versionName: null == versionName
          ? _value.versionName
          : versionName // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$MyPageScreenStateImpl implements _MyPageScreenState {
  const _$MyPageScreenStateImpl(
      {this.userInfo = const UserInfo(),
      this.isLoading = false,
      this.appName = '',
      this.versionName = '',
      this.error});

  @override
  @JsonKey()
  final UserInfo userInfo;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final String appName;
  @override
  @JsonKey()
  final String versionName;
  @override
  final AppError? error;

  @override
  String toString() {
    return 'MyPageScreenState(userInfo: $userInfo, isLoading: $isLoading, appName: $appName, versionName: $versionName, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MyPageScreenStateImpl &&
            (identical(other.userInfo, userInfo) ||
                other.userInfo == userInfo) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.appName, appName) || other.appName == appName) &&
            (identical(other.versionName, versionName) ||
                other.versionName == versionName) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, userInfo, isLoading, appName, versionName, error);

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MyPageScreenStateImplCopyWith<_$MyPageScreenStateImpl> get copyWith =>
      __$$MyPageScreenStateImplCopyWithImpl<_$MyPageScreenStateImpl>(
          this, _$identity);
}

abstract class _MyPageScreenState implements MyPageScreenState {
  const factory _MyPageScreenState(
      {final UserInfo userInfo,
      final bool isLoading,
      final String appName,
      final String versionName,
      final AppError? error}) = _$MyPageScreenStateImpl;

  @override
  UserInfo get userInfo;
  @override
  bool get isLoading;
  @override
  String get appName;
  @override
  String get versionName;
  @override
  AppError? get error;

  /// Create a copy of MyPageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MyPageScreenStateImplCopyWith<_$MyPageScreenStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
