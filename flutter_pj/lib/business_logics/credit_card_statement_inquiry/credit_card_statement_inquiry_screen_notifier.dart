import 'package:dtp_app/business_logics/interfaces/credit_card_transaction_history_repository.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/freee_credit_card_expense/freee_credit_card_expense.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/repositories/credit_card_transaction_history/credit_card_transaction_history_repository_impl.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

part 'credit_card_statement_inquiry_screen_notifier.freezed.dart';

@freezed
class CreditCardStatementInquiryScreenState
    with _$CreditCardStatementInquiryScreenState {
  const factory CreditCardStatementInquiryScreenState({
    /// クレジットカード情報
    CreditCardExpense? creditCardExpense,

    /// クレジットカード利用明細
    @Default(
      FreeeTransactionHistory.empty(),
    )
    FreeeTransactionHistory freeeTransactionHistory,

    /// クレジットカードを取得した時刻
    DateTime? baseDateTime,

    /// 選択されている月の初日
    DateTime? dateFrom,

    /// 選択されている月の最終日(当月の場合は今日)
    DateTime? dateTo,

    /// 絞り込み中かどうかのフラグ
    @Default(false) bool isNarrowDown,

    /// 絞り込み開始日
    DateTime? fromSelectedDay,

    /// 絞り込み終了日
    DateTime? toSelectedDay,

    /// 絞り込み開始日を設定しているかのフラグ
    @Default(false) bool isFromDialog,

    /// 絞り込み終了日を設定しているかのフラグ
    @Default(false) bool isToDialog,

    /// 当月以前の13か月分の年月
    @Default(<DateTime>[]) List<DateTime> yearMonthList,

    /// プルダウンで選択した月
    DateTime? selectedYearMonth,

    /// プルダウンで当月から何ヶ月前の明細を選択したか
    @Default(0) int selectedYearMonthIndex,

    /// クレジットカード名のアコーディオンを開いているかのフラグ
    @Default(false) bool isCreditCardNameAccordionExpanded,

    /// クレジットカードインデックス
    @Default(0) int creditCardIndex,

    /// カード切替モーダルで選択されたクレジットカードインデックス
    @Default(0) int selectCreditCardIndex,

    /// 絞り込み条件(すべて、入金、出金)
    @Default(
      TransactionHistoryFilterType.all,
    )
    TransactionHistoryFilterType currentFilterType,

    /// 絞り込み条件(日時、金額)
    @Default(
      TransactionHistorySortType.date,
    )
    TransactionHistorySortType currentSortType,

    /// 絞り込み条件(昇順、降順)
    @Default(
      TransactionHistorySortOrder.descending,
    )
    TransactionHistorySortOrder currentSortOrder,

    /// 並び替え基準(新しい利用日順、古い利用日順、金額が大きい順、金額が小さい順)
    @Default('') String sortTitle,

    /// 初回表示利用額合計
    String? initialTotalExpense,

    /// 利用明細の絞り込みキーワード
    @Default('') String descriptionSearch,

    /// 絵文字が使われているかのフラグ
    @Default(false) bool usageDetailsError,

    /// 最小金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
    @Default(false) bool digitSmallAmountError,

    /// 最小金額の先頭以外にマイナスがついているかのフラグ
    @Default(false) bool positiveSmallAmountError,

    /// 最小金額が最大金額より大きいかどうかのフラグ
    @Default(false) bool correlationSmallAmountError,

    /// 最大金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
    @Default(false) bool digitLargeAmountError,

    /// 最小金額の先頭以外にマイナスがついているかのフラグ
    @Default(false) bool positiveLargeAmountError,

    /// 最大金額が最小金額より小さいかどうかのフラグ
    @Default(false) bool correlationLargeAmountError,

    /// 最小金額を入力中かどうかのフラグ
    @Default(false) bool smallAmountFocus,

    /// 最大金額を入力中かどうかのフラグ
    @Default(false) bool largeAmountFocus,

    /// freee口座明細取得エラー
    AppError? error,
  }) = _CreditCardStatementInquiryScreenState;
}

final creditCardStatementInquiryScreenProvider =
    StateNotifierProvider.autoDispose<CreditCardStatementInquiryScreenNotifier,
        CreditCardStatementInquiryScreenState>(
  (ref) => CreditCardStatementInquiryScreenNotifier(
    ref.read(creditCardTransactionHistoryRepositoryProvider),
  ),
);

extension CreditCardStatementInquiryScreenStateExt
    on CreditCardStatementInquiryScreenState {
  // クレジット利用額合計(正負記号を含んだカンマ表記)
  String? get getTotalExpense {
    return freeeTransactionHistory.map(
      empty: (_) => null,
      data: (data) => data.getTotalCreditCardExpense().withCommasAndMinusSign,
    );
  }
}

class CreditCardStatementInquiryScreenNotifier
    extends StateNotifier<CreditCardStatementInquiryScreenState> {
  CreditCardStatementInquiryScreenNotifier(
    this._creditCardTransactionHistoryRepository,
  ) : super(const CreditCardStatementInquiryScreenState());

  final CreditCardTransactionHistoryRepository
      _creditCardTransactionHistoryRepository;
  final usageDetailsController = TextEditingController();
  final smallAmountController = TextEditingController();
  final largeAmountController = TextEditingController();

  /// エラー情報をリセット
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// クレジットカードの明細を取得し、stateのクレジットカード情報も更新する
  Future<void> loadCreditCardTransactionHistory({
    CreditCardExpense? creditCardExpense,
  }) async {
    try {
      // クレジットカード情報がない場合は明細を取得しない
      if (creditCardExpense == null) {
        return;
      }

      // 初期化
      state = state.copyWith(
        creditCardExpense: creditCardExpense,
        initialTotalExpense: null,
        freeeTransactionHistory: const FreeeTransactionHistory.empty(),
      );

      final dateTo = creditCardExpense.baseDateTime;
      final walletableId = creditCardExpense.id;

      state = state.copyWith(
        error: null,
        baseDateTime: _toDateTime(dateTo),
        dateFrom: _getFirstDate(dateTo),
        dateTo: _toDateTime(dateTo),
        selectedYearMonth: _toDateTime(dateTo),
        yearMonthList: _getPreviousYearMonths(dateTo),
      );

      await _loadFreeeTransactions(walletableId, dateTo);
    } on Exception catch (error) {
      state = state.copyWith(error: error.toAppError());
    }
  }

  /// クレジットカードの明細を取得する
  Future<void> _loadFreeeTransactions(
    int walletableId,
    String dateTo,
  ) async {
    final res = await _creditCardTransactionHistoryRepository
        .getFreeeCreditCardTransactionHistory(
      walletableId: walletableId,
      forceRefresh: true,
      walletableType: FreeeTransactionsType.creditCard,
      startDateFrom: _getFirstDate(dateTo),
      startDateTo: _toDateTime(dateTo),
    );

    res.when(
      success: (value) {
        if (mounted) {
          // 非同期実行のため、すでに画面が閉じられているおそれがある
          // したがって、mountedで判定する
          state = state.copyWith(
            freeeTransactionHistory: value,
          );

          // 初回取得時(絞り込みなし)の値を保持
          state = state.copyWith(
            initialTotalExpense: state.getTotalExpense,
          );
        }
      },
      failure: (error) {
        if (mounted) {
          state = state.copyWith(error: error);
        }
      },
    );
  }

  ///　絞り込み条件を設定して、クレジットカードの情報を取得する。
  Future<void> getCreditCardTransactionHistory({
    // 選択されている月の初日を入れる
    required DateTime dateFrom,
    // 選択されている月の最終日を入れる
    required DateTime dateTo,
    // 絞り込みの際の日時を入れる
    DateTime? tradingDateFrom,
    // 絞り込みの際の日時を入れる
    DateTime? tradingDateTo,
    required DateTime yearMonth,
    required int selectedYearMonthIndex,
    required String sortTitle,
    required TransactionHistorySortType sortType,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    double? minAmountWithSign,
    double? maxAmountWithSign,
  }) async {
    // エラー情報の削除
    clearError();

    final creditCardExpense = state.creditCardExpense;

    // クレジットカード情報がない場合は明細を取得しない
    if (creditCardExpense == null) {
      return;
    }

    state = state.copyWith(
      dateFrom: dateFrom,
      dateTo: dateTo,
      selectedYearMonth: yearMonth,
      selectedYearMonthIndex: selectedYearMonthIndex,
    );

    // クレジットカードの明細を取得する
    final res = await _creditCardTransactionHistoryRepository
        .getFreeeCreditCardTransactionHistory(
      startDateFrom: dateFrom,
      startDateTo: dateTo,
      walletableId: creditCardExpense.id,
      walletableType: FreeeTransactionsType.creditCard,
      forceRefresh: forceRefresh,
      filterType: filterType,
      sortType: sortType,
      order: order,
      tradingDateFrom: tradingDateFrom,
      tradingDateTo: tradingDateTo,
      minAmountWithSign: minAmountWithSign,
      maxAmountWithSign: maxAmountWithSign,
    );

    res.when(
      success: (value) {
        if (mounted) {
          state = state.copyWith(
            freeeTransactionHistory: value,
            currentFilterType: filterType,
            currentSortType: sortType,
            currentSortOrder: order,
            sortTitle: sortTitle,
          );
        }
      },
      failure: (error) {
        if (mounted) {
          state = state.copyWith(
            error: error,
          );
        }
      },
    );
  }

  /// クレジットカード名のアコーディオンの状態フラグの更新
  void updateIsCreditCardNameAccordionExpanded(bool value) {
    state = state.copyWith(isCreditCardNameAccordionExpanded: value);
  }

  /// カード切替モーダルで選択されたクレジットカードのindexに更新する
  void updateSelectCreditCardIndex(
    int creditCardIndex,
    int selectCreditCardIndex,
  ) {
    state = state.copyWith(creditCardIndex: creditCardIndex);
    state = state.copyWith(selectCreditCardIndex: selectCreditCardIndex);
  }

  /// 初回表示時のクレジットカードのインデックスを更新
  void updateFirstCreditCardIndex(
    int creditCardIndex,
    List<CreditCardExpense> freeeCreditCardExpenses,
  ) {
    final List<String> creditCardSwitchModalList =
        getCreditCardSwitchModalList(freeeCreditCardExpenses);

    // カード切替モーダルで選択されたクレジットカードの本来のインデックスを特定し取得する
    freeeCreditCardExpenses.asMap().entries.map((entry) {
      final int freeeCreditCardListIndex = entry.key;
      final String freeeCreditCardListCardName = entry.value.creditCardName;
      for (int itemIndex = 0;
          (itemIndex < freeeCreditCardExpenses.length ||
              itemIndex < freeeCreditCardExpenses.length);
          itemIndex++) {
        if (freeeCreditCardExpenses[creditCardIndex].creditCardName ==
            freeeCreditCardListCardName) {
          state = state.copyWith(
            creditCardIndex: freeeCreditCardListIndex,
          );
        }
      }
    }).toList();

    // カード切替モーダルで選択されたインデックスを特定し取得する
    creditCardSwitchModalList.asMap().entries.map((entry) {
      final int creditCardSwitchModalListIndex = entry.key;
      final String creditCardSwitchModalListCardName = entry.value.toString();
      for (int itemIndex = 0;
          (itemIndex < freeeCreditCardExpenses.length ||
              itemIndex < freeeCreditCardExpenses.length);
          itemIndex++) {
        if (freeeCreditCardExpenses[creditCardIndex].creditCardName ==
            creditCardSwitchModalListCardName) {
          state = state.copyWith(
            selectCreditCardIndex: creditCardSwitchModalListIndex,
          );
        }
      }
    }).toList();
  }

  /// 選択した年月が前回選択時と同一の年月であるかを判定
  bool checkSameYearMonth({
    required DateTime yearMonth,
    required DateTime selectedYearMonth,
  }) {
    final base = DateTime(yearMonth.year, yearMonth.month);
    final selected = DateTime(selectedYearMonth.year, selectedYearMonth.month);
    return base == selected;
  }

  /// 選択した年月が当月であるかを判定
  bool checkCurrentMonth({
    required DateTime yearMonth,
    required DateTime dateTime,
  }) {
    return yearMonth.year == dateTime.year && yearMonth.month == dateTime.month;
  }

  /// YYYY-M-D型に変換
  DateTime _toDateTime(String serverDate) {
    return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
  }

  /// 該当月の初日を返却
  DateTime _getFirstDate(String serverDate) {
    final dateTime = _toDateTime(serverDate);
    return DateTime(dateTime.year, dateTime.month, 1);
  }

  /// 当月から13ヶ月前までの年月を取得
  List<DateTime> _getPreviousYearMonths(String serverDate) {
    final baseTime = _toDateTime(serverDate);
    final List<DateTime> yearMonthList = [];

    for (int i = 0; i < 12; i++) {
      //先月の末日を取得
      final lastMonthLastDay = DateTime(baseTime.year, baseTime.month - i, 0);
      yearMonthList.add(lastMonthLastDay);
    }
    yearMonthList.insert(0, baseTime);

    return yearMonthList;
  }

  /// 絞り込み終了日を設定しているかのフラグを更新する
  void changeIsToDialog([bool isFlag = true]) {
    state = state.copyWith(isToDialog: isFlag);
  }

  /// 絞り込み開始日を設定しているかのフラグを更新する
  void changeIsFromDialog([bool isFlag = true]) {
    state = state.copyWith(isFromDialog: isFlag);
  }

  /// 絞り込み終了日を設定する
  void changeToSelectedDay(DateTime selectedDay) {
    state = state.copyWith(toSelectedDay: selectedDay);
  }

  /// 絞り込み開始日を設定する
  void changeFromSelectedDay(DateTime selectedDay) {
    state = state.copyWith(fromSelectedDay: selectedDay);
  }

  /// 絞り込みのキーワードを保存
  void changeDescriptionSearch(String descriptionSearch) {
    state = state.copyWith(descriptionSearch: descriptionSearch);
  }

  /// 各パラメータ初期化用の関数
  void narrowDownReset() {
    state = state.copyWith(
      isFromDialog: false,
      isToDialog: false,
      isNarrowDown: false,
    );
    usageDetailsController.clear();
    largeAmountController.clear();
    smallAmountController.clear();
  }

  /// カード切り替え時に絞り込み条件をリセット
  void cardSwitchReset() {
    state = state.copyWith(
      sortTitle: '',
      currentSortOrder: TransactionHistorySortOrder.descending,
      currentSortType: TransactionHistorySortType.date,
      isCreditCardNameAccordionExpanded: false,
    );
  }

  ///　カレンダーの選択開始日と選択終了日の初期値を設定
  void checkFirstDayAndLastDayCalendar(DateTime serverDate) {
    if (state.selectedYearMonth == null) {
      return;
    }
    final selectedMonth = state.selectedYearMonth;

    final selectedMonthFirstDate =
        DateTime(selectedMonth!.year, selectedMonth.month, 1);
    final selectedMonthEndDate =
        DateTime(selectedMonth.year, selectedMonth.month + 1, 0);

    final isThisMonth = checkSameYearMonth(
      yearMonth: serverDate,
      selectedYearMonth: selectedMonth,
    );

    if (!state.isFromDialog && !state.isToDialog) {
      // 初期状態
      state = state.copyWith(
        fromSelectedDay: selectedMonthFirstDate,
        toSelectedDay: isThisMonth ? serverDate : selectedMonthEndDate,
      );
    }
  }

  /// 条件をもとに絞り込みを実施
  Future<void> narrowDownCreditCardHistory({
    required DateTime dateFrom,
    required DateTime dateTo,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    TransactionHistorySortType sortType = TransactionHistorySortType.date,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    double? minAmountWithSign,
    double? maxAmountWithSign,
    String? descriptionSearch,
  }) async {
    // エラー情報の削除
    clearError();

    final creditCardExpense = state.creditCardExpense;

    // クレジットカード情報がない場合は明細を取得しない
    if (creditCardExpense == null) {
      return;
    }

    // フィルター有効状態かの確認
    final isNarrowDownEnabled = checkNarrowDownEnabled(
      dateFrom,
      dateTo,
      tradingDateFrom,
      tradingDateTo,
      minAmountWithSign,
      maxAmountWithSign,
      descriptionSearch,
    );

    final res = await _creditCardTransactionHistoryRepository
        .getFreeeCreditCardTransactionHistory(
      walletableId: creditCardExpense.id,
      walletableType: FreeeTransactionsType.creditCard,
      forceRefresh: forceRefresh,
      filterType: filterType,
      order: order,
      sortType: sortType,
      startDateFrom: dateFrom,
      startDateTo: dateTo,
      tradingDateFrom: tradingDateFrom!,
      tradingDateTo: tradingDateTo!,
      minAmountWithSign: minAmountWithSign,
      maxAmountWithSign: maxAmountWithSign,
      descriptionSearch: descriptionSearch,
    );

    res.when(
      success: (value) {
        state = state.copyWith(
          freeeTransactionHistory: value,
          // 絞り込み条件が初期値の場合で絞り込みが実行されても、絞り込みなしと同様
          isNarrowDown: isNarrowDownEnabled,
        );
      },
      failure: (error) {
        state = state.copyWith(error: error);
      },
    );
  }

  /// 絵文字が入力されているかのフラグを更新
  void validateUsageDetails({
    required bool value,
    bool isUsageDetails = false,
  }) {
    if (isUsageDetails) {
      state = state.copyWith(usageDetailsError: value);
    } else {
      state = state.copyWith(usageDetailsError: value);
    }
  }

  /// 数字と半角マイナス以外(カンマ除く)を含んでいるかのフラグを更新
  void validateDigitAmount({
    required bool value,
    bool isSmallAmount = false,
  }) {
    if (isSmallAmount) {
      state = state.copyWith(digitSmallAmountError: value);
    } else {
      state = state.copyWith(digitLargeAmountError: value);
    }
  }

  /// 金額の先頭以外にマイナスがついているかのフラグを更新
  void validatePositiveAmount({
    required bool value,
    bool isSmallAmount = false,
  }) {
    if (isSmallAmount) {
      state = state.copyWith(positiveSmallAmountError: value);
    } else {
      state = state.copyWith(positiveLargeAmountError: value);
    }
  }

  /// 最小金額または最大金額が数値ではないときにtrueを返す
  bool hasAmountNumError() {
    final trySmallAmount =
        int.tryParse(smallAmountController.text.replaceAll(',', ''));
    final tryLargeAmount =
        int.tryParse(largeAmountController.text.replaceAll(',', ''));
    return (trySmallAmount == null) ||
        (tryLargeAmount == null) ||
        state.digitSmallAmountError ||
        state.positiveSmallAmountError ||
        state.digitLargeAmountError ||
        state.positiveLargeAmountError;
  }

  /// 最大金額が最小金額より小さいかどうかのフラグを更新
  void validateCorrelationAmount({
    required bool smallAmount,
    required bool largeAmount,
  }) {
    state = state.copyWith(
      correlationSmallAmountError: smallAmount,
      correlationLargeAmountError: largeAmount,
    );
  }

  /// 入力状態を更新
  void switchAmountFocus({
    required bool smallAmountFocus,
    required bool largeAmountFocus,
  }) {
    state = state.copyWith(
      smallAmountFocus: smallAmountFocus,
      largeAmountFocus: largeAmountFocus,
    );
  }

  /// 金額の最小値のバリデーションをリセット
  void clearSmallAmountError() {
    state = state.copyWith(
      digitSmallAmountError: false,
      positiveSmallAmountError: false,
      correlationSmallAmountError: false,
      correlationLargeAmountError: false,
    );
  }

  /// 絵文字のバリデーションをリセット
  void clearUsageDetailsError() {
    state = state.copyWith(
      usageDetailsError: false,
    );
  }

  /// 金額の最大値のバリデーションをリセット
  void clearLargeAmountError() {
    state = state.copyWith(
      digitLargeAmountError: false,
      positiveLargeAmountError: false,
      correlationLargeAmountError: false,
      correlationSmallAmountError: false,
    );
  }

  /// 絞り込み条件が有効かどうかを判定
  bool checkInvalid() {
    final result = state.usageDetailsError ||
        state.digitSmallAmountError ||
        state.positiveSmallAmountError ||
        state.correlationSmallAmountError ||
        state.correlationLargeAmountError ||
        state.digitLargeAmountError ||
        state.positiveLargeAmountError;
    return result;
  }

  /// 絞り込み有効状態かの確認
  bool checkNarrowDownEnabled(
    DateTime dateFrom,
    DateTime dateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    double? minAmount,
    double? maxAmount,
    String? descriptionSearch,
  ) {
    if (dateFrom != tradingDateFrom ||
        dateTo != tradingDateTo ||
        minAmount != null ||
        maxAmount != null ||
        descriptionSearch != '') {
      return true;
    }
    return false;
  }

  // クレジットカード利用明細のカード切替モーダルリストを作成する
  List<String> getCreditCardSwitchModalList(
    List<CreditCardExpense> freeeCreditCardExpenses,
  ) {
    // ループ上限（カード枚数最大100枚のため）
    const maxLoopLength = 100;

    final creditCardSwitchModalList =
        freeeCreditCardExpenses.map((e) => e.creditCardName).toList();

    for (int itemIndex = 0;
        (itemIndex < freeeCreditCardExpenses.length ||
            maxLoopLength < freeeCreditCardExpenses.length);
        itemIndex++) {
      if (freeeCreditCardExpenses[itemIndex]
          .isFreeeReConnectFinancialInstitution) {
        creditCardSwitchModalList.remove(
          freeeCreditCardExpenses[itemIndex].creditCardName,
        );
      }
    }

    return creditCardSwitchModalList;
  }

  // カード切替モーダル出しわけのためにクレジットカードの一覧リストを作成する
  List<String> getFreeeCreditCardList(
    List<CreditCardExpense> freeeCreditCardExpenses,
  ) {
    return freeeCreditCardExpenses.map((e) => e.creditCardName).toList();
  }
}
