// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'credit_card_statement_inquiry_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CreditCardStatementInquiryScreenState {
  /// クレジットカード情報
  CreditCardExpense? get creditCardExpense =>
      throw _privateConstructorUsedError;

  /// クレジットカード利用明細
  FreeeTransactionHistory get freeeTransactionHistory =>
      throw _privateConstructorUsedError;

  /// クレジットカードを取得した時刻
  DateTime? get baseDateTime => throw _privateConstructorUsedError;

  /// 選択されている月の初日
  DateTime? get dateFrom => throw _privateConstructorUsedError;

  /// 選択されている月の最終日(当月の場合は今日)
  DateTime? get dateTo => throw _privateConstructorUsedError;

  /// 絞り込み中かどうかのフラグ
  bool get isNarrowDown => throw _privateConstructorUsedError;

  /// 絞り込み開始日
  DateTime? get fromSelectedDay => throw _privateConstructorUsedError;

  /// 絞り込み終了日
  DateTime? get toSelectedDay => throw _privateConstructorUsedError;

  /// 絞り込み開始日を設定しているかのフラグ
  bool get isFromDialog => throw _privateConstructorUsedError;

  /// 絞り込み終了日を設定しているかのフラグ
  bool get isToDialog => throw _privateConstructorUsedError;

  /// 当月以前の13か月分の年月
  List<DateTime> get yearMonthList => throw _privateConstructorUsedError;

  /// プルダウンで選択した月
  DateTime? get selectedYearMonth => throw _privateConstructorUsedError;

  /// プルダウンで当月から何ヶ月前の明細を選択したか
  int get selectedYearMonthIndex => throw _privateConstructorUsedError;

  /// クレジットカード名のアコーディオンを開いているかのフラグ
  bool get isCreditCardNameAccordionExpanded =>
      throw _privateConstructorUsedError;

  /// クレジットカードインデックス
  int get creditCardIndex => throw _privateConstructorUsedError;

  /// カード切替モーダルで選択されたクレジットカードインデックス
  int get selectCreditCardIndex => throw _privateConstructorUsedError;

  /// 絞り込み条件(すべて、入金、出金)
  TransactionHistoryFilterType get currentFilterType =>
      throw _privateConstructorUsedError;

  /// 絞り込み条件(日時、金額)
  TransactionHistorySortType get currentSortType =>
      throw _privateConstructorUsedError;

  /// 絞り込み条件(昇順、降順)
  TransactionHistorySortOrder get currentSortOrder =>
      throw _privateConstructorUsedError;

  /// 並び替え基準(新しい利用日順、古い利用日順、金額が大きい順、金額が小さい順)
  String get sortTitle => throw _privateConstructorUsedError;

  /// 初回表示利用額合計
  String? get initialTotalExpense => throw _privateConstructorUsedError;

  /// 利用明細の絞り込みキーワード
  String get descriptionSearch => throw _privateConstructorUsedError;

  /// 絵文字が使われているかのフラグ
  bool get usageDetailsError => throw _privateConstructorUsedError;

  /// 最小金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
  bool get digitSmallAmountError => throw _privateConstructorUsedError;

  /// 最小金額の先頭以外にマイナスがついているかのフラグ
  bool get positiveSmallAmountError => throw _privateConstructorUsedError;

  /// 最小金額が最大金額より大きいかどうかのフラグ
  bool get correlationSmallAmountError => throw _privateConstructorUsedError;

  /// 最大金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
  bool get digitLargeAmountError => throw _privateConstructorUsedError;

  /// 最小金額の先頭以外にマイナスがついているかのフラグ
  bool get positiveLargeAmountError => throw _privateConstructorUsedError;

  /// 最大金額が最小金額より小さいかどうかのフラグ
  bool get correlationLargeAmountError => throw _privateConstructorUsedError;

  /// 最小金額を入力中かどうかのフラグ
  bool get smallAmountFocus => throw _privateConstructorUsedError;

  /// 最大金額を入力中かどうかのフラグ
  bool get largeAmountFocus => throw _privateConstructorUsedError;

  /// freee口座明細取得エラー
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditCardStatementInquiryScreenStateCopyWith<
          CreditCardStatementInquiryScreenState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditCardStatementInquiryScreenStateCopyWith<$Res> {
  factory $CreditCardStatementInquiryScreenStateCopyWith(
          CreditCardStatementInquiryScreenState value,
          $Res Function(CreditCardStatementInquiryScreenState) then) =
      _$CreditCardStatementInquiryScreenStateCopyWithImpl<$Res,
          CreditCardStatementInquiryScreenState>;
  @useResult
  $Res call(
      {CreditCardExpense? creditCardExpense,
      FreeeTransactionHistory freeeTransactionHistory,
      DateTime? baseDateTime,
      DateTime? dateFrom,
      DateTime? dateTo,
      bool isNarrowDown,
      DateTime? fromSelectedDay,
      DateTime? toSelectedDay,
      bool isFromDialog,
      bool isToDialog,
      List<DateTime> yearMonthList,
      DateTime? selectedYearMonth,
      int selectedYearMonthIndex,
      bool isCreditCardNameAccordionExpanded,
      int creditCardIndex,
      int selectCreditCardIndex,
      TransactionHistoryFilterType currentFilterType,
      TransactionHistorySortType currentSortType,
      TransactionHistorySortOrder currentSortOrder,
      String sortTitle,
      String? initialTotalExpense,
      String descriptionSearch,
      bool usageDetailsError,
      bool digitSmallAmountError,
      bool positiveSmallAmountError,
      bool correlationSmallAmountError,
      bool digitLargeAmountError,
      bool positiveLargeAmountError,
      bool correlationLargeAmountError,
      bool smallAmountFocus,
      bool largeAmountFocus,
      AppError? error});

  $CreditCardExpenseCopyWith<$Res>? get creditCardExpense;
  $FreeeTransactionHistoryCopyWith<$Res> get freeeTransactionHistory;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$CreditCardStatementInquiryScreenStateCopyWithImpl<$Res,
        $Val extends CreditCardStatementInquiryScreenState>
    implements $CreditCardStatementInquiryScreenStateCopyWith<$Res> {
  _$CreditCardStatementInquiryScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creditCardExpense = freezed,
    Object? freeeTransactionHistory = null,
    Object? baseDateTime = freezed,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? isNarrowDown = null,
    Object? fromSelectedDay = freezed,
    Object? toSelectedDay = freezed,
    Object? isFromDialog = null,
    Object? isToDialog = null,
    Object? yearMonthList = null,
    Object? selectedYearMonth = freezed,
    Object? selectedYearMonthIndex = null,
    Object? isCreditCardNameAccordionExpanded = null,
    Object? creditCardIndex = null,
    Object? selectCreditCardIndex = null,
    Object? currentFilterType = null,
    Object? currentSortType = null,
    Object? currentSortOrder = null,
    Object? sortTitle = null,
    Object? initialTotalExpense = freezed,
    Object? descriptionSearch = null,
    Object? usageDetailsError = null,
    Object? digitSmallAmountError = null,
    Object? positiveSmallAmountError = null,
    Object? correlationSmallAmountError = null,
    Object? digitLargeAmountError = null,
    Object? positiveLargeAmountError = null,
    Object? correlationLargeAmountError = null,
    Object? smallAmountFocus = null,
    Object? largeAmountFocus = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      creditCardExpense: freezed == creditCardExpense
          ? _value.creditCardExpense
          : creditCardExpense // ignore: cast_nullable_to_non_nullable
              as CreditCardExpense?,
      freeeTransactionHistory: null == freeeTransactionHistory
          ? _value.freeeTransactionHistory
          : freeeTransactionHistory // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistory,
      baseDateTime: freezed == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isNarrowDown: null == isNarrowDown
          ? _value.isNarrowDown
          : isNarrowDown // ignore: cast_nullable_to_non_nullable
              as bool,
      fromSelectedDay: freezed == fromSelectedDay
          ? _value.fromSelectedDay
          : fromSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toSelectedDay: freezed == toSelectedDay
          ? _value.toSelectedDay
          : toSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isFromDialog: null == isFromDialog
          ? _value.isFromDialog
          : isFromDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      isToDialog: null == isToDialog
          ? _value.isToDialog
          : isToDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      yearMonthList: null == yearMonthList
          ? _value.yearMonthList
          : yearMonthList // ignore: cast_nullable_to_non_nullable
              as List<DateTime>,
      selectedYearMonth: freezed == selectedYearMonth
          ? _value.selectedYearMonth
          : selectedYearMonth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedYearMonthIndex: null == selectedYearMonthIndex
          ? _value.selectedYearMonthIndex
          : selectedYearMonthIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isCreditCardNameAccordionExpanded: null ==
              isCreditCardNameAccordionExpanded
          ? _value.isCreditCardNameAccordionExpanded
          : isCreditCardNameAccordionExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      creditCardIndex: null == creditCardIndex
          ? _value.creditCardIndex
          : creditCardIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectCreditCardIndex: null == selectCreditCardIndex
          ? _value.selectCreditCardIndex
          : selectCreditCardIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentFilterType: null == currentFilterType
          ? _value.currentFilterType
          : currentFilterType // ignore: cast_nullable_to_non_nullable
              as TransactionHistoryFilterType,
      currentSortType: null == currentSortType
          ? _value.currentSortType
          : currentSortType // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortType,
      currentSortOrder: null == currentSortOrder
          ? _value.currentSortOrder
          : currentSortOrder // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortOrder,
      sortTitle: null == sortTitle
          ? _value.sortTitle
          : sortTitle // ignore: cast_nullable_to_non_nullable
              as String,
      initialTotalExpense: freezed == initialTotalExpense
          ? _value.initialTotalExpense
          : initialTotalExpense // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionSearch: null == descriptionSearch
          ? _value.descriptionSearch
          : descriptionSearch // ignore: cast_nullable_to_non_nullable
              as String,
      usageDetailsError: null == usageDetailsError
          ? _value.usageDetailsError
          : usageDetailsError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitSmallAmountError: null == digitSmallAmountError
          ? _value.digitSmallAmountError
          : digitSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveSmallAmountError: null == positiveSmallAmountError
          ? _value.positiveSmallAmountError
          : positiveSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationSmallAmountError: null == correlationSmallAmountError
          ? _value.correlationSmallAmountError
          : correlationSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitLargeAmountError: null == digitLargeAmountError
          ? _value.digitLargeAmountError
          : digitLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveLargeAmountError: null == positiveLargeAmountError
          ? _value.positiveLargeAmountError
          : positiveLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationLargeAmountError: null == correlationLargeAmountError
          ? _value.correlationLargeAmountError
          : correlationLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      smallAmountFocus: null == smallAmountFocus
          ? _value.smallAmountFocus
          : smallAmountFocus // ignore: cast_nullable_to_non_nullable
              as bool,
      largeAmountFocus: null == largeAmountFocus
          ? _value.largeAmountFocus
          : largeAmountFocus // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreditCardExpenseCopyWith<$Res>? get creditCardExpense {
    if (_value.creditCardExpense == null) {
      return null;
    }

    return $CreditCardExpenseCopyWith<$Res>(_value.creditCardExpense!, (value) {
      return _then(_value.copyWith(creditCardExpense: value) as $Val);
    });
  }

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FreeeTransactionHistoryCopyWith<$Res> get freeeTransactionHistory {
    return $FreeeTransactionHistoryCopyWith<$Res>(
        _value.freeeTransactionHistory, (value) {
      return _then(_value.copyWith(freeeTransactionHistory: value) as $Val);
    });
  }

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CreditCardStatementInquiryScreenStateImplCopyWith<$Res>
    implements $CreditCardStatementInquiryScreenStateCopyWith<$Res> {
  factory _$$CreditCardStatementInquiryScreenStateImplCopyWith(
          _$CreditCardStatementInquiryScreenStateImpl value,
          $Res Function(_$CreditCardStatementInquiryScreenStateImpl) then) =
      __$$CreditCardStatementInquiryScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CreditCardExpense? creditCardExpense,
      FreeeTransactionHistory freeeTransactionHistory,
      DateTime? baseDateTime,
      DateTime? dateFrom,
      DateTime? dateTo,
      bool isNarrowDown,
      DateTime? fromSelectedDay,
      DateTime? toSelectedDay,
      bool isFromDialog,
      bool isToDialog,
      List<DateTime> yearMonthList,
      DateTime? selectedYearMonth,
      int selectedYearMonthIndex,
      bool isCreditCardNameAccordionExpanded,
      int creditCardIndex,
      int selectCreditCardIndex,
      TransactionHistoryFilterType currentFilterType,
      TransactionHistorySortType currentSortType,
      TransactionHistorySortOrder currentSortOrder,
      String sortTitle,
      String? initialTotalExpense,
      String descriptionSearch,
      bool usageDetailsError,
      bool digitSmallAmountError,
      bool positiveSmallAmountError,
      bool correlationSmallAmountError,
      bool digitLargeAmountError,
      bool positiveLargeAmountError,
      bool correlationLargeAmountError,
      bool smallAmountFocus,
      bool largeAmountFocus,
      AppError? error});

  @override
  $CreditCardExpenseCopyWith<$Res>? get creditCardExpense;
  @override
  $FreeeTransactionHistoryCopyWith<$Res> get freeeTransactionHistory;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$CreditCardStatementInquiryScreenStateImplCopyWithImpl<$Res>
    extends _$CreditCardStatementInquiryScreenStateCopyWithImpl<$Res,
        _$CreditCardStatementInquiryScreenStateImpl>
    implements _$$CreditCardStatementInquiryScreenStateImplCopyWith<$Res> {
  __$$CreditCardStatementInquiryScreenStateImplCopyWithImpl(
      _$CreditCardStatementInquiryScreenStateImpl _value,
      $Res Function(_$CreditCardStatementInquiryScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creditCardExpense = freezed,
    Object? freeeTransactionHistory = null,
    Object? baseDateTime = freezed,
    Object? dateFrom = freezed,
    Object? dateTo = freezed,
    Object? isNarrowDown = null,
    Object? fromSelectedDay = freezed,
    Object? toSelectedDay = freezed,
    Object? isFromDialog = null,
    Object? isToDialog = null,
    Object? yearMonthList = null,
    Object? selectedYearMonth = freezed,
    Object? selectedYearMonthIndex = null,
    Object? isCreditCardNameAccordionExpanded = null,
    Object? creditCardIndex = null,
    Object? selectCreditCardIndex = null,
    Object? currentFilterType = null,
    Object? currentSortType = null,
    Object? currentSortOrder = null,
    Object? sortTitle = null,
    Object? initialTotalExpense = freezed,
    Object? descriptionSearch = null,
    Object? usageDetailsError = null,
    Object? digitSmallAmountError = null,
    Object? positiveSmallAmountError = null,
    Object? correlationSmallAmountError = null,
    Object? digitLargeAmountError = null,
    Object? positiveLargeAmountError = null,
    Object? correlationLargeAmountError = null,
    Object? smallAmountFocus = null,
    Object? largeAmountFocus = null,
    Object? error = freezed,
  }) {
    return _then(_$CreditCardStatementInquiryScreenStateImpl(
      creditCardExpense: freezed == creditCardExpense
          ? _value.creditCardExpense
          : creditCardExpense // ignore: cast_nullable_to_non_nullable
              as CreditCardExpense?,
      freeeTransactionHistory: null == freeeTransactionHistory
          ? _value.freeeTransactionHistory
          : freeeTransactionHistory // ignore: cast_nullable_to_non_nullable
              as FreeeTransactionHistory,
      baseDateTime: freezed == baseDateTime
          ? _value.baseDateTime
          : baseDateTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateFrom: freezed == dateFrom
          ? _value.dateFrom
          : dateFrom // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateTo: freezed == dateTo
          ? _value.dateTo
          : dateTo // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isNarrowDown: null == isNarrowDown
          ? _value.isNarrowDown
          : isNarrowDown // ignore: cast_nullable_to_non_nullable
              as bool,
      fromSelectedDay: freezed == fromSelectedDay
          ? _value.fromSelectedDay
          : fromSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      toSelectedDay: freezed == toSelectedDay
          ? _value.toSelectedDay
          : toSelectedDay // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isFromDialog: null == isFromDialog
          ? _value.isFromDialog
          : isFromDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      isToDialog: null == isToDialog
          ? _value.isToDialog
          : isToDialog // ignore: cast_nullable_to_non_nullable
              as bool,
      yearMonthList: null == yearMonthList
          ? _value._yearMonthList
          : yearMonthList // ignore: cast_nullable_to_non_nullable
              as List<DateTime>,
      selectedYearMonth: freezed == selectedYearMonth
          ? _value.selectedYearMonth
          : selectedYearMonth // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      selectedYearMonthIndex: null == selectedYearMonthIndex
          ? _value.selectedYearMonthIndex
          : selectedYearMonthIndex // ignore: cast_nullable_to_non_nullable
              as int,
      isCreditCardNameAccordionExpanded: null ==
              isCreditCardNameAccordionExpanded
          ? _value.isCreditCardNameAccordionExpanded
          : isCreditCardNameAccordionExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      creditCardIndex: null == creditCardIndex
          ? _value.creditCardIndex
          : creditCardIndex // ignore: cast_nullable_to_non_nullable
              as int,
      selectCreditCardIndex: null == selectCreditCardIndex
          ? _value.selectCreditCardIndex
          : selectCreditCardIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentFilterType: null == currentFilterType
          ? _value.currentFilterType
          : currentFilterType // ignore: cast_nullable_to_non_nullable
              as TransactionHistoryFilterType,
      currentSortType: null == currentSortType
          ? _value.currentSortType
          : currentSortType // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortType,
      currentSortOrder: null == currentSortOrder
          ? _value.currentSortOrder
          : currentSortOrder // ignore: cast_nullable_to_non_nullable
              as TransactionHistorySortOrder,
      sortTitle: null == sortTitle
          ? _value.sortTitle
          : sortTitle // ignore: cast_nullable_to_non_nullable
              as String,
      initialTotalExpense: freezed == initialTotalExpense
          ? _value.initialTotalExpense
          : initialTotalExpense // ignore: cast_nullable_to_non_nullable
              as String?,
      descriptionSearch: null == descriptionSearch
          ? _value.descriptionSearch
          : descriptionSearch // ignore: cast_nullable_to_non_nullable
              as String,
      usageDetailsError: null == usageDetailsError
          ? _value.usageDetailsError
          : usageDetailsError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitSmallAmountError: null == digitSmallAmountError
          ? _value.digitSmallAmountError
          : digitSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveSmallAmountError: null == positiveSmallAmountError
          ? _value.positiveSmallAmountError
          : positiveSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationSmallAmountError: null == correlationSmallAmountError
          ? _value.correlationSmallAmountError
          : correlationSmallAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      digitLargeAmountError: null == digitLargeAmountError
          ? _value.digitLargeAmountError
          : digitLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      positiveLargeAmountError: null == positiveLargeAmountError
          ? _value.positiveLargeAmountError
          : positiveLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      correlationLargeAmountError: null == correlationLargeAmountError
          ? _value.correlationLargeAmountError
          : correlationLargeAmountError // ignore: cast_nullable_to_non_nullable
              as bool,
      smallAmountFocus: null == smallAmountFocus
          ? _value.smallAmountFocus
          : smallAmountFocus // ignore: cast_nullable_to_non_nullable
              as bool,
      largeAmountFocus: null == largeAmountFocus
          ? _value.largeAmountFocus
          : largeAmountFocus // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$CreditCardStatementInquiryScreenStateImpl
    implements _CreditCardStatementInquiryScreenState {
  const _$CreditCardStatementInquiryScreenStateImpl(
      {this.creditCardExpense,
      this.freeeTransactionHistory = const FreeeTransactionHistory.empty(),
      this.baseDateTime,
      this.dateFrom,
      this.dateTo,
      this.isNarrowDown = false,
      this.fromSelectedDay,
      this.toSelectedDay,
      this.isFromDialog = false,
      this.isToDialog = false,
      final List<DateTime> yearMonthList = const <DateTime>[],
      this.selectedYearMonth,
      this.selectedYearMonthIndex = 0,
      this.isCreditCardNameAccordionExpanded = false,
      this.creditCardIndex = 0,
      this.selectCreditCardIndex = 0,
      this.currentFilterType = TransactionHistoryFilterType.all,
      this.currentSortType = TransactionHistorySortType.date,
      this.currentSortOrder = TransactionHistorySortOrder.descending,
      this.sortTitle = '',
      this.initialTotalExpense,
      this.descriptionSearch = '',
      this.usageDetailsError = false,
      this.digitSmallAmountError = false,
      this.positiveSmallAmountError = false,
      this.correlationSmallAmountError = false,
      this.digitLargeAmountError = false,
      this.positiveLargeAmountError = false,
      this.correlationLargeAmountError = false,
      this.smallAmountFocus = false,
      this.largeAmountFocus = false,
      this.error})
      : _yearMonthList = yearMonthList;

  /// クレジットカード情報
  @override
  final CreditCardExpense? creditCardExpense;

  /// クレジットカード利用明細
  @override
  @JsonKey()
  final FreeeTransactionHistory freeeTransactionHistory;

  /// クレジットカードを取得した時刻
  @override
  final DateTime? baseDateTime;

  /// 選択されている月の初日
  @override
  final DateTime? dateFrom;

  /// 選択されている月の最終日(当月の場合は今日)
  @override
  final DateTime? dateTo;

  /// 絞り込み中かどうかのフラグ
  @override
  @JsonKey()
  final bool isNarrowDown;

  /// 絞り込み開始日
  @override
  final DateTime? fromSelectedDay;

  /// 絞り込み終了日
  @override
  final DateTime? toSelectedDay;

  /// 絞り込み開始日を設定しているかのフラグ
  @override
  @JsonKey()
  final bool isFromDialog;

  /// 絞り込み終了日を設定しているかのフラグ
  @override
  @JsonKey()
  final bool isToDialog;

  /// 当月以前の13か月分の年月
  final List<DateTime> _yearMonthList;

  /// 当月以前の13か月分の年月
  @override
  @JsonKey()
  List<DateTime> get yearMonthList {
    if (_yearMonthList is EqualUnmodifiableListView) return _yearMonthList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_yearMonthList);
  }

  /// プルダウンで選択した月
  @override
  final DateTime? selectedYearMonth;

  /// プルダウンで当月から何ヶ月前の明細を選択したか
  @override
  @JsonKey()
  final int selectedYearMonthIndex;

  /// クレジットカード名のアコーディオンを開いているかのフラグ
  @override
  @JsonKey()
  final bool isCreditCardNameAccordionExpanded;

  /// クレジットカードインデックス
  @override
  @JsonKey()
  final int creditCardIndex;

  /// カード切替モーダルで選択されたクレジットカードインデックス
  @override
  @JsonKey()
  final int selectCreditCardIndex;

  /// 絞り込み条件(すべて、入金、出金)
  @override
  @JsonKey()
  final TransactionHistoryFilterType currentFilterType;

  /// 絞り込み条件(日時、金額)
  @override
  @JsonKey()
  final TransactionHistorySortType currentSortType;

  /// 絞り込み条件(昇順、降順)
  @override
  @JsonKey()
  final TransactionHistorySortOrder currentSortOrder;

  /// 並び替え基準(新しい利用日順、古い利用日順、金額が大きい順、金額が小さい順)
  @override
  @JsonKey()
  final String sortTitle;

  /// 初回表示利用額合計
  @override
  final String? initialTotalExpense;

  /// 利用明細の絞り込みキーワード
  @override
  @JsonKey()
  final String descriptionSearch;

  /// 絵文字が使われているかのフラグ
  @override
  @JsonKey()
  final bool usageDetailsError;

  /// 最小金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
  @override
  @JsonKey()
  final bool digitSmallAmountError;

  /// 最小金額の先頭以外にマイナスがついているかのフラグ
  @override
  @JsonKey()
  final bool positiveSmallAmountError;

  /// 最小金額が最大金額より大きいかどうかのフラグ
  @override
  @JsonKey()
  final bool correlationSmallAmountError;

  /// 最大金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
  @override
  @JsonKey()
  final bool digitLargeAmountError;

  /// 最小金額の先頭以外にマイナスがついているかのフラグ
  @override
  @JsonKey()
  final bool positiveLargeAmountError;

  /// 最大金額が最小金額より小さいかどうかのフラグ
  @override
  @JsonKey()
  final bool correlationLargeAmountError;

  /// 最小金額を入力中かどうかのフラグ
  @override
  @JsonKey()
  final bool smallAmountFocus;

  /// 最大金額を入力中かどうかのフラグ
  @override
  @JsonKey()
  final bool largeAmountFocus;

  /// freee口座明細取得エラー
  @override
  final AppError? error;

  @override
  String toString() {
    return 'CreditCardStatementInquiryScreenState(creditCardExpense: $creditCardExpense, freeeTransactionHistory: $freeeTransactionHistory, baseDateTime: $baseDateTime, dateFrom: $dateFrom, dateTo: $dateTo, isNarrowDown: $isNarrowDown, fromSelectedDay: $fromSelectedDay, toSelectedDay: $toSelectedDay, isFromDialog: $isFromDialog, isToDialog: $isToDialog, yearMonthList: $yearMonthList, selectedYearMonth: $selectedYearMonth, selectedYearMonthIndex: $selectedYearMonthIndex, isCreditCardNameAccordionExpanded: $isCreditCardNameAccordionExpanded, creditCardIndex: $creditCardIndex, selectCreditCardIndex: $selectCreditCardIndex, currentFilterType: $currentFilterType, currentSortType: $currentSortType, currentSortOrder: $currentSortOrder, sortTitle: $sortTitle, initialTotalExpense: $initialTotalExpense, descriptionSearch: $descriptionSearch, usageDetailsError: $usageDetailsError, digitSmallAmountError: $digitSmallAmountError, positiveSmallAmountError: $positiveSmallAmountError, correlationSmallAmountError: $correlationSmallAmountError, digitLargeAmountError: $digitLargeAmountError, positiveLargeAmountError: $positiveLargeAmountError, correlationLargeAmountError: $correlationLargeAmountError, smallAmountFocus: $smallAmountFocus, largeAmountFocus: $largeAmountFocus, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreditCardStatementInquiryScreenStateImpl &&
            (identical(other.creditCardExpense, creditCardExpense) ||
                other.creditCardExpense == creditCardExpense) &&
            (identical(other.freeeTransactionHistory, freeeTransactionHistory) ||
                other.freeeTransactionHistory == freeeTransactionHistory) &&
            (identical(other.baseDateTime, baseDateTime) ||
                other.baseDateTime == baseDateTime) &&
            (identical(other.dateFrom, dateFrom) ||
                other.dateFrom == dateFrom) &&
            (identical(other.dateTo, dateTo) || other.dateTo == dateTo) &&
            (identical(other.isNarrowDown, isNarrowDown) ||
                other.isNarrowDown == isNarrowDown) &&
            (identical(other.fromSelectedDay, fromSelectedDay) ||
                other.fromSelectedDay == fromSelectedDay) &&
            (identical(other.toSelectedDay, toSelectedDay) ||
                other.toSelectedDay == toSelectedDay) &&
            (identical(other.isFromDialog, isFromDialog) ||
                other.isFromDialog == isFromDialog) &&
            (identical(other.isToDialog, isToDialog) ||
                other.isToDialog == isToDialog) &&
            const DeepCollectionEquality()
                .equals(other._yearMonthList, _yearMonthList) &&
            (identical(other.selectedYearMonth, selectedYearMonth) ||
                other.selectedYearMonth == selectedYearMonth) &&
            (identical(other.selectedYearMonthIndex, selectedYearMonthIndex) ||
                other.selectedYearMonthIndex == selectedYearMonthIndex) &&
            (identical(other.isCreditCardNameAccordionExpanded, isCreditCardNameAccordionExpanded) ||
                other.isCreditCardNameAccordionExpanded ==
                    isCreditCardNameAccordionExpanded) &&
            (identical(other.creditCardIndex, creditCardIndex) ||
                other.creditCardIndex == creditCardIndex) &&
            (identical(other.selectCreditCardIndex, selectCreditCardIndex) ||
                other.selectCreditCardIndex == selectCreditCardIndex) &&
            (identical(other.currentFilterType, currentFilterType) ||
                other.currentFilterType == currentFilterType) &&
            (identical(other.currentSortType, currentSortType) ||
                other.currentSortType == currentSortType) &&
            (identical(other.currentSortOrder, currentSortOrder) ||
                other.currentSortOrder == currentSortOrder) &&
            (identical(other.sortTitle, sortTitle) ||
                other.sortTitle == sortTitle) &&
            (identical(other.initialTotalExpense, initialTotalExpense) ||
                other.initialTotalExpense == initialTotalExpense) &&
            (identical(other.descriptionSearch, descriptionSearch) ||
                other.descriptionSearch == descriptionSearch) &&
            (identical(other.usageDetailsError, usageDetailsError) ||
                other.usageDetailsError == usageDetailsError) &&
            (identical(other.digitSmallAmountError, digitSmallAmountError) ||
                other.digitSmallAmountError == digitSmallAmountError) &&
            (identical(other.positiveSmallAmountError, positiveSmallAmountError) ||
                other.positiveSmallAmountError == positiveSmallAmountError) &&
            (identical(other.correlationSmallAmountError, correlationSmallAmountError) ||
                other.correlationSmallAmountError ==
                    correlationSmallAmountError) &&
            (identical(other.digitLargeAmountError, digitLargeAmountError) ||
                other.digitLargeAmountError == digitLargeAmountError) &&
            (identical(other.positiveLargeAmountError, positiveLargeAmountError) ||
                other.positiveLargeAmountError == positiveLargeAmountError) &&
            (identical(other.correlationLargeAmountError, correlationLargeAmountError) || other.correlationLargeAmountError == correlationLargeAmountError) &&
            (identical(other.smallAmountFocus, smallAmountFocus) || other.smallAmountFocus == smallAmountFocus) &&
            (identical(other.largeAmountFocus, largeAmountFocus) || other.largeAmountFocus == largeAmountFocus) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        creditCardExpense,
        freeeTransactionHistory,
        baseDateTime,
        dateFrom,
        dateTo,
        isNarrowDown,
        fromSelectedDay,
        toSelectedDay,
        isFromDialog,
        isToDialog,
        const DeepCollectionEquality().hash(_yearMonthList),
        selectedYearMonth,
        selectedYearMonthIndex,
        isCreditCardNameAccordionExpanded,
        creditCardIndex,
        selectCreditCardIndex,
        currentFilterType,
        currentSortType,
        currentSortOrder,
        sortTitle,
        initialTotalExpense,
        descriptionSearch,
        usageDetailsError,
        digitSmallAmountError,
        positiveSmallAmountError,
        correlationSmallAmountError,
        digitLargeAmountError,
        positiveLargeAmountError,
        correlationLargeAmountError,
        smallAmountFocus,
        largeAmountFocus,
        error
      ]);

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreditCardStatementInquiryScreenStateImplCopyWith<
          _$CreditCardStatementInquiryScreenStateImpl>
      get copyWith => __$$CreditCardStatementInquiryScreenStateImplCopyWithImpl<
          _$CreditCardStatementInquiryScreenStateImpl>(this, _$identity);
}

abstract class _CreditCardStatementInquiryScreenState
    implements CreditCardStatementInquiryScreenState {
  const factory _CreditCardStatementInquiryScreenState(
      {final CreditCardExpense? creditCardExpense,
      final FreeeTransactionHistory freeeTransactionHistory,
      final DateTime? baseDateTime,
      final DateTime? dateFrom,
      final DateTime? dateTo,
      final bool isNarrowDown,
      final DateTime? fromSelectedDay,
      final DateTime? toSelectedDay,
      final bool isFromDialog,
      final bool isToDialog,
      final List<DateTime> yearMonthList,
      final DateTime? selectedYearMonth,
      final int selectedYearMonthIndex,
      final bool isCreditCardNameAccordionExpanded,
      final int creditCardIndex,
      final int selectCreditCardIndex,
      final TransactionHistoryFilterType currentFilterType,
      final TransactionHistorySortType currentSortType,
      final TransactionHistorySortOrder currentSortOrder,
      final String sortTitle,
      final String? initialTotalExpense,
      final String descriptionSearch,
      final bool usageDetailsError,
      final bool digitSmallAmountError,
      final bool positiveSmallAmountError,
      final bool correlationSmallAmountError,
      final bool digitLargeAmountError,
      final bool positiveLargeAmountError,
      final bool correlationLargeAmountError,
      final bool smallAmountFocus,
      final bool largeAmountFocus,
      final AppError? error}) = _$CreditCardStatementInquiryScreenStateImpl;

  /// クレジットカード情報
  @override
  CreditCardExpense? get creditCardExpense;

  /// クレジットカード利用明細
  @override
  FreeeTransactionHistory get freeeTransactionHistory;

  /// クレジットカードを取得した時刻
  @override
  DateTime? get baseDateTime;

  /// 選択されている月の初日
  @override
  DateTime? get dateFrom;

  /// 選択されている月の最終日(当月の場合は今日)
  @override
  DateTime? get dateTo;

  /// 絞り込み中かどうかのフラグ
  @override
  bool get isNarrowDown;

  /// 絞り込み開始日
  @override
  DateTime? get fromSelectedDay;

  /// 絞り込み終了日
  @override
  DateTime? get toSelectedDay;

  /// 絞り込み開始日を設定しているかのフラグ
  @override
  bool get isFromDialog;

  /// 絞り込み終了日を設定しているかのフラグ
  @override
  bool get isToDialog;

  /// 当月以前の13か月分の年月
  @override
  List<DateTime> get yearMonthList;

  /// プルダウンで選択した月
  @override
  DateTime? get selectedYearMonth;

  /// プルダウンで当月から何ヶ月前の明細を選択したか
  @override
  int get selectedYearMonthIndex;

  /// クレジットカード名のアコーディオンを開いているかのフラグ
  @override
  bool get isCreditCardNameAccordionExpanded;

  /// クレジットカードインデックス
  @override
  int get creditCardIndex;

  /// カード切替モーダルで選択されたクレジットカードインデックス
  @override
  int get selectCreditCardIndex;

  /// 絞り込み条件(すべて、入金、出金)
  @override
  TransactionHistoryFilterType get currentFilterType;

  /// 絞り込み条件(日時、金額)
  @override
  TransactionHistorySortType get currentSortType;

  /// 絞り込み条件(昇順、降順)
  @override
  TransactionHistorySortOrder get currentSortOrder;

  /// 並び替え基準(新しい利用日順、古い利用日順、金額が大きい順、金額が小さい順)
  @override
  String get sortTitle;

  /// 初回表示利用額合計
  @override
  String? get initialTotalExpense;

  /// 利用明細の絞り込みキーワード
  @override
  String get descriptionSearch;

  /// 絵文字が使われているかのフラグ
  @override
  bool get usageDetailsError;

  /// 最小金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
  @override
  bool get digitSmallAmountError;

  /// 最小金額の先頭以外にマイナスがついているかのフラグ
  @override
  bool get positiveSmallAmountError;

  /// 最小金額が最大金額より大きいかどうかのフラグ
  @override
  bool get correlationSmallAmountError;

  /// 最大金額に数字と半角マイナス以外(カンマ除く)を含んでいるのフラグ
  @override
  bool get digitLargeAmountError;

  /// 最小金額の先頭以外にマイナスがついているかのフラグ
  @override
  bool get positiveLargeAmountError;

  /// 最大金額が最小金額より小さいかどうかのフラグ
  @override
  bool get correlationLargeAmountError;

  /// 最小金額を入力中かどうかのフラグ
  @override
  bool get smallAmountFocus;

  /// 最大金額を入力中かどうかのフラグ
  @override
  bool get largeAmountFocus;

  /// freee口座明細取得エラー
  @override
  AppError? get error;

  /// Create a copy of CreditCardStatementInquiryScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreditCardStatementInquiryScreenStateImplCopyWith<
          _$CreditCardStatementInquiryScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
