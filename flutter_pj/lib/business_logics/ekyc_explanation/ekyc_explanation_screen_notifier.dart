import 'package:dtp_app/business_logics/identity_document/identity_document_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/interfaces/corporate_web_repository.dart';
import 'package:dtp_app/business_logics/interfaces/ekyc_url_repository.dart';
import 'package:dtp_app/business_logics/interfaces/identity_reference_number_repository.dart';
import 'package:dtp_app/business_logics/interfaces/identity_verification_repository.dart';
import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/identity_verification_info/identity_verification_info.dart';
import 'package:dtp_app/repositories/identity_verification/corporate_web/corporate_web_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/ekyc_url/ekyc_url_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/identity_verification_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/reference_number/reference_number_repository_impl.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'ekyc_explanation_screen_notifier.freezed.dart';

@freezed
class EkycExplanationState with _$EkycExplanationState {
  const factory EkycExplanationState({
    @Default(null) String? nextPageName,
    @Default('') String referenceNumber,
    required String userTypeNumber,
    @Default(null) ErrorPattern? errorPattern,
    @Default('') String uuid,
    AppError? sendIdentityInfoError, // 本人情報送信処理で発生したエラー内容
  }) = _EkycExplanationState;
}

extension EkycExplanationStateExt on EkycExplanationState {}

final ekycExplanationStateScreenProvider = StateNotifierProvider.autoDispose<
    EkycExplanationScreenNotifier, EkycExplanationState>((ref) {
  // お手続き番号を取得する
  final referenceNumber =
      ref.watch(identityReferenceNumberScreenProvider).cacheReferenceNumberText;

  // UUIDを取得する
  final uuid = ref.watch(identityReferenceNumberScreenProvider).cacheUuid;

  // 手続き者属性フラグを取得する
  final userTypeNumber =
      ref.watch(identityDocumentStateScreenProvider).userTypeNumber;

  return EkycExplanationScreenNotifier(
    corporateWebRepository: ref.watch(corporateWebRepositoryProvider),
    ekycUrlRepository: ref.watch(ekycUrlRepositoryProvider),
    identityReferenceNumberRepository:
        ref.watch(identityReferenceNumberRepositoryProvider),
    identityVerificationRepository:
        ref.watch(identityVerificationRepositoryProvider),
    referenceNumber: referenceNumber,
    uuid: uuid,
    userTypeNumber: userTypeNumber,
    secureStorageManager: ref.watch(secureStorageManagerProvider),
    memoryDataManager: ref.watch(mdManagerProvider),
  );
});

class EkycExplanationScreenNotifier
    extends StateNotifier<EkycExplanationState> {
  EkycExplanationScreenNotifier({
    required this.corporateWebRepository,
    required this.ekycUrlRepository,
    required this.identityReferenceNumberRepository,
    required this.identityVerificationRepository,
    required String referenceNumber,
    required String uuid,
    required String userTypeNumber,
    required this.secureStorageManager,
    required this.memoryDataManager,
  }) : super(
          EkycExplanationState(
            referenceNumber: referenceNumber,
            uuid: uuid,
            userTypeNumber: userTypeNumber,
          ),
        );

  final CorporateWebRepository corporateWebRepository;
  final EkycUrlRepository ekycUrlRepository;
  final IdentityReferenceNumberRepository identityReferenceNumberRepository;
  final IdentityVerificationRepository identityVerificationRepository;
  final SecureStorageManager secureStorageManager;
  final MemoryDataManager memoryDataManager;

  // エラーパターンの変更
  void _changeErrorPattern(ErrorPattern errorPattern) {
    state = state.copyWith(
      errorPattern: errorPattern,
    );
  }

  // 次に遷移するページの変更
  void changeNextPageName(String pageName) {
    state = state.copyWith(
      nextPageName: pageName,
    );
  }

  // 画面遷移、エラーに必要な情報のリセット
  void stateReset() {
    state = state.copyWith(
      nextPageName: null,
      errorPattern: null,
    );
  }

  // 法人Webの開局状況を取得する
  Future<bool> _checkCorporateWebStatus() async {
    final result = await corporateWebRepository.checkCorporateWebStatus();
    final isOpened = result.when(
      success: (isOpened) {
        return isOpened;
      },
      failure: (_) {
        return false;
      },
    );

    return isOpened;
  }

  // eKYCのWebViewURLを取得する
  Future<String> requestEkycUrl() async {
    stateReset();

    // 法人Webの開局状況を取得する
    final isOpened = await _checkCorporateWebStatus();

    if (!isOpened) {
      _changeErrorPattern(ErrorPattern.errorPattern11);
      // 閉局時は空文字を返却
      return '';
    }

    final result = await ekycUrlRepository.requestEkycUrl(
      referenceNumber: state.referenceNumber,
      uuid: state.uuid,
      userType: state.userTypeNumber,
    );
    final url = result.when(
      success: (url) {
        // eKYCのURLを返却する
        return url;
      },
      failure: (error) {
        // eKYC URL生成要求APIエラーはエラーパターン9に該当する
        _changeErrorPattern(ErrorPattern.errorPattern9);
        // エラー時は空文字を返却する
        return '';
      },
    );

    return url;
  }

  /// 本人情報送信処理を実行する
  Future<bool> sendIdentityVerificationInfo({
    required String role,
    required bool isEkyc,
  }) async {
    final res =
        await identityVerificationRepository.sendIdentityVerificationInfo(
      IdentityVerificationInfo(
        role: role,
        dataSource: isEkyc ? 'eKYC' : 'JPKI',
        referenceNumber: state.referenceNumber,
      ),
    );
    return res.when(
      success: (_) {
        return true;
      },
      failure: (error) {
        state = state.copyWith(sendIdentityInfoError: error);
        return false;
      },
    );
  }

  void clearSendIdentityInfoError() {
    state = state.copyWith(sendIdentityInfoError: null);
  }

  // 画面描画時にメモリに保持している情報を削除する
  Future<void> deleteMemoryData() async {
    await memoryDataManager.jpkiAccessToken.delete();
    await memoryDataManager.jpkiPassword.delete();
    await memoryDataManager.sigDoc.delete();
    await memoryDataManager.certForSig.delete();
  }
}
