// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ekyc_explanation_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$EkycExplanationState {
  String? get nextPageName => throw _privateConstructorUsedError;
  String get referenceNumber => throw _privateConstructorUsedError;
  String get userTypeNumber => throw _privateConstructorUsedError;
  ErrorPattern? get errorPattern => throw _privateConstructorUsedError;
  String get uuid => throw _privateConstructorUsedError;
  AppError? get sendIdentityInfoError => throw _privateConstructorUsedError;

  /// Create a copy of EkycExplanationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $EkycExplanationStateCopyWith<EkycExplanationState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EkycExplanationStateCopyWith<$Res> {
  factory $EkycExplanationStateCopyWith(EkycExplanationState value,
          $Res Function(EkycExplanationState) then) =
      _$EkycExplanationStateCopyWithImpl<$Res, EkycExplanationState>;
  @useResult
  $Res call(
      {String? nextPageName,
      String referenceNumber,
      String userTypeNumber,
      ErrorPattern? errorPattern,
      String uuid,
      AppError? sendIdentityInfoError});

  $AppErrorCopyWith<$Res>? get sendIdentityInfoError;
}

/// @nodoc
class _$EkycExplanationStateCopyWithImpl<$Res,
        $Val extends EkycExplanationState>
    implements $EkycExplanationStateCopyWith<$Res> {
  _$EkycExplanationStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of EkycExplanationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nextPageName = freezed,
    Object? referenceNumber = null,
    Object? userTypeNumber = null,
    Object? errorPattern = freezed,
    Object? uuid = null,
    Object? sendIdentityInfoError = freezed,
  }) {
    return _then(_value.copyWith(
      nextPageName: freezed == nextPageName
          ? _value.nextPageName
          : nextPageName // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceNumber: null == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      userTypeNumber: null == userTypeNumber
          ? _value.userTypeNumber
          : userTypeNumber // ignore: cast_nullable_to_non_nullable
              as String,
      errorPattern: freezed == errorPattern
          ? _value.errorPattern
          : errorPattern // ignore: cast_nullable_to_non_nullable
              as ErrorPattern?,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      sendIdentityInfoError: freezed == sendIdentityInfoError
          ? _value.sendIdentityInfoError
          : sendIdentityInfoError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of EkycExplanationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get sendIdentityInfoError {
    if (_value.sendIdentityInfoError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.sendIdentityInfoError!, (value) {
      return _then(_value.copyWith(sendIdentityInfoError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$EkycExplanationStateImplCopyWith<$Res>
    implements $EkycExplanationStateCopyWith<$Res> {
  factory _$$EkycExplanationStateImplCopyWith(_$EkycExplanationStateImpl value,
          $Res Function(_$EkycExplanationStateImpl) then) =
      __$$EkycExplanationStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? nextPageName,
      String referenceNumber,
      String userTypeNumber,
      ErrorPattern? errorPattern,
      String uuid,
      AppError? sendIdentityInfoError});

  @override
  $AppErrorCopyWith<$Res>? get sendIdentityInfoError;
}

/// @nodoc
class __$$EkycExplanationStateImplCopyWithImpl<$Res>
    extends _$EkycExplanationStateCopyWithImpl<$Res, _$EkycExplanationStateImpl>
    implements _$$EkycExplanationStateImplCopyWith<$Res> {
  __$$EkycExplanationStateImplCopyWithImpl(_$EkycExplanationStateImpl _value,
      $Res Function(_$EkycExplanationStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of EkycExplanationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? nextPageName = freezed,
    Object? referenceNumber = null,
    Object? userTypeNumber = null,
    Object? errorPattern = freezed,
    Object? uuid = null,
    Object? sendIdentityInfoError = freezed,
  }) {
    return _then(_$EkycExplanationStateImpl(
      nextPageName: freezed == nextPageName
          ? _value.nextPageName
          : nextPageName // ignore: cast_nullable_to_non_nullable
              as String?,
      referenceNumber: null == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      userTypeNumber: null == userTypeNumber
          ? _value.userTypeNumber
          : userTypeNumber // ignore: cast_nullable_to_non_nullable
              as String,
      errorPattern: freezed == errorPattern
          ? _value.errorPattern
          : errorPattern // ignore: cast_nullable_to_non_nullable
              as ErrorPattern?,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      sendIdentityInfoError: freezed == sendIdentityInfoError
          ? _value.sendIdentityInfoError
          : sendIdentityInfoError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$EkycExplanationStateImpl
    with DiagnosticableTreeMixin
    implements _EkycExplanationState {
  const _$EkycExplanationStateImpl(
      {this.nextPageName = null,
      this.referenceNumber = '',
      required this.userTypeNumber,
      this.errorPattern = null,
      this.uuid = '',
      this.sendIdentityInfoError});

  @override
  @JsonKey()
  final String? nextPageName;
  @override
  @JsonKey()
  final String referenceNumber;
  @override
  final String userTypeNumber;
  @override
  @JsonKey()
  final ErrorPattern? errorPattern;
  @override
  @JsonKey()
  final String uuid;
  @override
  final AppError? sendIdentityInfoError;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'EkycExplanationState(nextPageName: $nextPageName, referenceNumber: $referenceNumber, userTypeNumber: $userTypeNumber, errorPattern: $errorPattern, uuid: $uuid, sendIdentityInfoError: $sendIdentityInfoError)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'EkycExplanationState'))
      ..add(DiagnosticsProperty('nextPageName', nextPageName))
      ..add(DiagnosticsProperty('referenceNumber', referenceNumber))
      ..add(DiagnosticsProperty('userTypeNumber', userTypeNumber))
      ..add(DiagnosticsProperty('errorPattern', errorPattern))
      ..add(DiagnosticsProperty('uuid', uuid))
      ..add(
          DiagnosticsProperty('sendIdentityInfoError', sendIdentityInfoError));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EkycExplanationStateImpl &&
            (identical(other.nextPageName, nextPageName) ||
                other.nextPageName == nextPageName) &&
            (identical(other.referenceNumber, referenceNumber) ||
                other.referenceNumber == referenceNumber) &&
            (identical(other.userTypeNumber, userTypeNumber) ||
                other.userTypeNumber == userTypeNumber) &&
            (identical(other.errorPattern, errorPattern) ||
                other.errorPattern == errorPattern) &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.sendIdentityInfoError, sendIdentityInfoError) ||
                other.sendIdentityInfoError == sendIdentityInfoError));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nextPageName, referenceNumber,
      userTypeNumber, errorPattern, uuid, sendIdentityInfoError);

  /// Create a copy of EkycExplanationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EkycExplanationStateImplCopyWith<_$EkycExplanationStateImpl>
      get copyWith =>
          __$$EkycExplanationStateImplCopyWithImpl<_$EkycExplanationStateImpl>(
              this, _$identity);
}

abstract class _EkycExplanationState implements EkycExplanationState {
  const factory _EkycExplanationState(
      {final String? nextPageName,
      final String referenceNumber,
      required final String userTypeNumber,
      final ErrorPattern? errorPattern,
      final String uuid,
      final AppError? sendIdentityInfoError}) = _$EkycExplanationStateImpl;

  @override
  String? get nextPageName;
  @override
  String get referenceNumber;
  @override
  String get userTypeNumber;
  @override
  ErrorPattern? get errorPattern;
  @override
  String get uuid;
  @override
  AppError? get sendIdentityInfoError;

  /// Create a copy of EkycExplanationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EkycExplanationStateImplCopyWith<_$EkycExplanationStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
