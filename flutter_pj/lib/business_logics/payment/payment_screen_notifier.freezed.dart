// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentScreenState {
  SSOLoginPhases get ssoLoginPhases => throw _privateConstructorUsedError;
  String get redirectUrl => throw _privateConstructorUsedError;
  String get fromVdParameter => throw _privateConstructorUsedError;
  OtpType get otpType => throw _privateConstructorUsedError;
  InputType get inputType => throw _privateConstructorUsedError;
  String get vdID => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;

  /// HOMEに遷移する必要があるかを確認
  bool get isMovingHomeNeeded => throw _privateConstructorUsedError;

  /// otp入力画面のWebViewコントローラー
  InAppWebViewController? get web21WebViewController =>
      throw _privateConstructorUsedError;
  VdidLoginResponse? get vdidLoginResponse =>
      throw _privateConstructorUsedError;

  /// 振込、承認のどちらが表示されているかを確認するフラグ
  PaymentScreenStatus get paymentScreenStatus =>
      throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentScreenStateCopyWith<PaymentScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentScreenStateCopyWith<$Res> {
  factory $PaymentScreenStateCopyWith(
          PaymentScreenState value, $Res Function(PaymentScreenState) then) =
      _$PaymentScreenStateCopyWithImpl<$Res, PaymentScreenState>;
  @useResult
  $Res call(
      {SSOLoginPhases ssoLoginPhases,
      String redirectUrl,
      String fromVdParameter,
      OtpType otpType,
      InputType inputType,
      String vdID,
      bool isLoading,
      bool isMovingHomeNeeded,
      InAppWebViewController? web21WebViewController,
      VdidLoginResponse? vdidLoginResponse,
      PaymentScreenStatus paymentScreenStatus,
      AppError? error});

  $VdidLoginResponseCopyWith<$Res>? get vdidLoginResponse;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$PaymentScreenStateCopyWithImpl<$Res, $Val extends PaymentScreenState>
    implements $PaymentScreenStateCopyWith<$Res> {
  _$PaymentScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ssoLoginPhases = null,
    Object? redirectUrl = null,
    Object? fromVdParameter = null,
    Object? otpType = null,
    Object? inputType = null,
    Object? vdID = null,
    Object? isLoading = null,
    Object? isMovingHomeNeeded = null,
    Object? web21WebViewController = freezed,
    Object? vdidLoginResponse = freezed,
    Object? paymentScreenStatus = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      ssoLoginPhases: null == ssoLoginPhases
          ? _value.ssoLoginPhases
          : ssoLoginPhases // ignore: cast_nullable_to_non_nullable
              as SSOLoginPhases,
      redirectUrl: null == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String,
      fromVdParameter: null == fromVdParameter
          ? _value.fromVdParameter
          : fromVdParameter // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
      inputType: null == inputType
          ? _value.inputType
          : inputType // ignore: cast_nullable_to_non_nullable
              as InputType,
      vdID: null == vdID
          ? _value.vdID
          : vdID // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isMovingHomeNeeded: null == isMovingHomeNeeded
          ? _value.isMovingHomeNeeded
          : isMovingHomeNeeded // ignore: cast_nullable_to_non_nullable
              as bool,
      web21WebViewController: freezed == web21WebViewController
          ? _value.web21WebViewController
          : web21WebViewController // ignore: cast_nullable_to_non_nullable
              as InAppWebViewController?,
      vdidLoginResponse: freezed == vdidLoginResponse
          ? _value.vdidLoginResponse
          : vdidLoginResponse // ignore: cast_nullable_to_non_nullable
              as VdidLoginResponse?,
      paymentScreenStatus: null == paymentScreenStatus
          ? _value.paymentScreenStatus
          : paymentScreenStatus // ignore: cast_nullable_to_non_nullable
              as PaymentScreenStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VdidLoginResponseCopyWith<$Res>? get vdidLoginResponse {
    if (_value.vdidLoginResponse == null) {
      return null;
    }

    return $VdidLoginResponseCopyWith<$Res>(_value.vdidLoginResponse!, (value) {
      return _then(_value.copyWith(vdidLoginResponse: value) as $Val);
    });
  }

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$PaymentScreenStateImplCopyWith<$Res>
    implements $PaymentScreenStateCopyWith<$Res> {
  factory _$$PaymentScreenStateImplCopyWith(_$PaymentScreenStateImpl value,
          $Res Function(_$PaymentScreenStateImpl) then) =
      __$$PaymentScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SSOLoginPhases ssoLoginPhases,
      String redirectUrl,
      String fromVdParameter,
      OtpType otpType,
      InputType inputType,
      String vdID,
      bool isLoading,
      bool isMovingHomeNeeded,
      InAppWebViewController? web21WebViewController,
      VdidLoginResponse? vdidLoginResponse,
      PaymentScreenStatus paymentScreenStatus,
      AppError? error});

  @override
  $VdidLoginResponseCopyWith<$Res>? get vdidLoginResponse;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$PaymentScreenStateImplCopyWithImpl<$Res>
    extends _$PaymentScreenStateCopyWithImpl<$Res, _$PaymentScreenStateImpl>
    implements _$$PaymentScreenStateImplCopyWith<$Res> {
  __$$PaymentScreenStateImplCopyWithImpl(_$PaymentScreenStateImpl _value,
      $Res Function(_$PaymentScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? ssoLoginPhases = null,
    Object? redirectUrl = null,
    Object? fromVdParameter = null,
    Object? otpType = null,
    Object? inputType = null,
    Object? vdID = null,
    Object? isLoading = null,
    Object? isMovingHomeNeeded = null,
    Object? web21WebViewController = freezed,
    Object? vdidLoginResponse = freezed,
    Object? paymentScreenStatus = null,
    Object? error = freezed,
  }) {
    return _then(_$PaymentScreenStateImpl(
      ssoLoginPhases: null == ssoLoginPhases
          ? _value.ssoLoginPhases
          : ssoLoginPhases // ignore: cast_nullable_to_non_nullable
              as SSOLoginPhases,
      redirectUrl: null == redirectUrl
          ? _value.redirectUrl
          : redirectUrl // ignore: cast_nullable_to_non_nullable
              as String,
      fromVdParameter: null == fromVdParameter
          ? _value.fromVdParameter
          : fromVdParameter // ignore: cast_nullable_to_non_nullable
              as String,
      otpType: null == otpType
          ? _value.otpType
          : otpType // ignore: cast_nullable_to_non_nullable
              as OtpType,
      inputType: null == inputType
          ? _value.inputType
          : inputType // ignore: cast_nullable_to_non_nullable
              as InputType,
      vdID: null == vdID
          ? _value.vdID
          : vdID // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isMovingHomeNeeded: null == isMovingHomeNeeded
          ? _value.isMovingHomeNeeded
          : isMovingHomeNeeded // ignore: cast_nullable_to_non_nullable
              as bool,
      web21WebViewController: freezed == web21WebViewController
          ? _value.web21WebViewController
          : web21WebViewController // ignore: cast_nullable_to_non_nullable
              as InAppWebViewController?,
      vdidLoginResponse: freezed == vdidLoginResponse
          ? _value.vdidLoginResponse
          : vdidLoginResponse // ignore: cast_nullable_to_non_nullable
              as VdidLoginResponse?,
      paymentScreenStatus: null == paymentScreenStatus
          ? _value.paymentScreenStatus
          : paymentScreenStatus // ignore: cast_nullable_to_non_nullable
              as PaymentScreenStatus,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$PaymentScreenStateImpl implements _PaymentScreenState {
  const _$PaymentScreenStateImpl(
      {this.ssoLoginPhases = SSOLoginPhases.initial,
      this.redirectUrl = '',
      this.fromVdParameter = '',
      this.otpType = OtpType.none,
      this.inputType = InputType.none,
      this.vdID = '',
      this.isLoading = false,
      this.isMovingHomeNeeded = false,
      this.web21WebViewController,
      this.vdidLoginResponse,
      this.paymentScreenStatus = PaymentScreenStatus.payment,
      this.error});

  @override
  @JsonKey()
  final SSOLoginPhases ssoLoginPhases;
  @override
  @JsonKey()
  final String redirectUrl;
  @override
  @JsonKey()
  final String fromVdParameter;
  @override
  @JsonKey()
  final OtpType otpType;
  @override
  @JsonKey()
  final InputType inputType;
  @override
  @JsonKey()
  final String vdID;
  @override
  @JsonKey()
  final bool isLoading;

  /// HOMEに遷移する必要があるかを確認
  @override
  @JsonKey()
  final bool isMovingHomeNeeded;

  /// otp入力画面のWebViewコントローラー
  @override
  final InAppWebViewController? web21WebViewController;
  @override
  final VdidLoginResponse? vdidLoginResponse;

  /// 振込、承認のどちらが表示されているかを確認するフラグ
  @override
  @JsonKey()
  final PaymentScreenStatus paymentScreenStatus;
  @override
  final AppError? error;

  @override
  String toString() {
    return 'PaymentScreenState(ssoLoginPhases: $ssoLoginPhases, redirectUrl: $redirectUrl, fromVdParameter: $fromVdParameter, otpType: $otpType, inputType: $inputType, vdID: $vdID, isLoading: $isLoading, isMovingHomeNeeded: $isMovingHomeNeeded, web21WebViewController: $web21WebViewController, vdidLoginResponse: $vdidLoginResponse, paymentScreenStatus: $paymentScreenStatus, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentScreenStateImpl &&
            (identical(other.ssoLoginPhases, ssoLoginPhases) ||
                other.ssoLoginPhases == ssoLoginPhases) &&
            (identical(other.redirectUrl, redirectUrl) ||
                other.redirectUrl == redirectUrl) &&
            (identical(other.fromVdParameter, fromVdParameter) ||
                other.fromVdParameter == fromVdParameter) &&
            (identical(other.otpType, otpType) || other.otpType == otpType) &&
            (identical(other.inputType, inputType) ||
                other.inputType == inputType) &&
            (identical(other.vdID, vdID) || other.vdID == vdID) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isMovingHomeNeeded, isMovingHomeNeeded) ||
                other.isMovingHomeNeeded == isMovingHomeNeeded) &&
            (identical(other.web21WebViewController, web21WebViewController) ||
                other.web21WebViewController == web21WebViewController) &&
            (identical(other.vdidLoginResponse, vdidLoginResponse) ||
                other.vdidLoginResponse == vdidLoginResponse) &&
            (identical(other.paymentScreenStatus, paymentScreenStatus) ||
                other.paymentScreenStatus == paymentScreenStatus) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      ssoLoginPhases,
      redirectUrl,
      fromVdParameter,
      otpType,
      inputType,
      vdID,
      isLoading,
      isMovingHomeNeeded,
      web21WebViewController,
      vdidLoginResponse,
      paymentScreenStatus,
      error);

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentScreenStateImplCopyWith<_$PaymentScreenStateImpl> get copyWith =>
      __$$PaymentScreenStateImplCopyWithImpl<_$PaymentScreenStateImpl>(
          this, _$identity);
}

abstract class _PaymentScreenState implements PaymentScreenState {
  const factory _PaymentScreenState(
      {final SSOLoginPhases ssoLoginPhases,
      final String redirectUrl,
      final String fromVdParameter,
      final OtpType otpType,
      final InputType inputType,
      final String vdID,
      final bool isLoading,
      final bool isMovingHomeNeeded,
      final InAppWebViewController? web21WebViewController,
      final VdidLoginResponse? vdidLoginResponse,
      final PaymentScreenStatus paymentScreenStatus,
      final AppError? error}) = _$PaymentScreenStateImpl;

  @override
  SSOLoginPhases get ssoLoginPhases;
  @override
  String get redirectUrl;
  @override
  String get fromVdParameter;
  @override
  OtpType get otpType;
  @override
  InputType get inputType;
  @override
  String get vdID;
  @override
  bool get isLoading;

  /// HOMEに遷移する必要があるかを確認
  @override
  bool get isMovingHomeNeeded;

  /// otp入力画面のWebViewコントローラー
  @override
  InAppWebViewController? get web21WebViewController;
  @override
  VdidLoginResponse? get vdidLoginResponse;

  /// 振込、承認のどちらが表示されているかを確認するフラグ
  @override
  PaymentScreenStatus get paymentScreenStatus;
  @override
  AppError? get error;

  /// Create a copy of PaymentScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentScreenStateImplCopyWith<_$PaymentScreenStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
