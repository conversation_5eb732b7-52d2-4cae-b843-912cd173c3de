import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/non_sdk_method_channel_repository.dart';
import 'package:dtp_app/business_logics/interfaces/web21_payment_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/vdid_login/vdid_login_response.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/non_sdk_method_channel/non_sdk_method_channel_repository_impl.dart';
import 'package:dtp_app/repositories/payment/web21_payment_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'payment_screen_notifier.freezed.dart';

@freezed
class PaymentScreenState with _$PaymentScreenState {
  const factory PaymentScreenState({
    @Default(SSOLoginPhases.initial) SSOLoginPhases ssoLoginPhases,
    @Default('') String redirectUrl,
    @Default('') String fromVdParameter,
    @Default(OtpType.none) OtpType otpType,
    @Default(InputType.none) InputType inputType,
    @Default('') String vdID,
    @Default(false) bool isLoading,

    /// HOMEに遷移する必要があるかを確認
    @Default(false) bool isMovingHomeNeeded,

    /// otp入力画面のWebViewコントローラー
    InAppWebViewController? web21WebViewController,
    VdidLoginResponse? vdidLoginResponse,

    /// 振込、承認のどちらが表示されているかを確認するフラグ
    @Default(PaymentScreenStatus.payment)
    PaymentScreenStatus paymentScreenStatus,
    AppError? error,
  }) = _PaymentScreenState;
}

enum SSOLoginPhases {
  /// 初期状態
  initial,

  /// ログイン完了
  /// -> Web21アプリ（WebView）を表示
  web21,
}

/// OTP種別
enum OtpType {
  /// time：時刻OTP認証
  time,

  /// signature：トランザクション認証
  signature,

  /// 未設定（初期状態）
  none,
}

/// OTP入力方式（時刻OTP認証のみ）
enum InputType {
  /// 0：自動入力
  autoInput,

  /// 1：手動入力
  manualInput,

  /// 未設定（初期状態）
  none,
}

/// 法人用アプリの処理結果
enum Web21AppResult {
  /// 0：キャンセル(法人用アプリ上でキャンセル押下)
  cancel,

  /// 1：正常終了/確認OK
  ok,

  /// 2：VDサーバー通信エラー
  vdServerError,

  /// 3：OTP有効化未済
  otpNotEnabled,

  /// 4:継続可能エラー
  continuableError,

  /// 5:継続不可エラー
  uncontainableError,

  /// 未設定（初期状態）
  none,
}

/// 振込・承認の遷移先情報
enum PaymentScreenStatus {
  // 振込・振替
  payment,
  // 承認
  approval
}

final paymentScreenProvider = StateNotifierProvider.autoDispose<
    PaymentScreenNotifier, PaymentScreenState>(
  (ref) => PaymentScreenNotifier(
    loginRepository: ref.watch(loginRepositoryProvider),
    web21paymentRepository: ref.read(web21PaymentRepositoryProvider),
    methodChannelRepository: ref.read(methodChannelRepositoryProvider),
    state: const PaymentScreenState(),
  ),
);

class PaymentScreenNotifier extends StateNotifier<PaymentScreenState> {
  PaymentScreenNotifier({
    required this.loginRepository,
    required this.web21paymentRepository,
    required this.methodChannelRepository,
    required PaymentScreenState state,
  }) : super(state);

  final LoginRepository loginRepository;
  final Web21PaymentRepository web21paymentRepository;
  final NonSdkMethodChannelRepository methodChannelRepository;

  /// SSO共通呼び出し
  Future<void> getWeb21SsoSaml(bool isTransfer) async {
    // エラーをリセット
    // ヘルプボタンアイコン押下時のステータス変更
    state = state.copyWith(
      error: null,
      ssoLoginPhases: SSOLoginPhases.initial,
      paymentScreenStatus: isTransfer
          ? PaymentScreenStatus.payment
          : PaymentScreenStatus.approval,
    );

    // SSO用SAMLを取得
    final result = await loginRepository.getWeb21SsoSaml(isTransfer);
    await result.when(
      success: (toWeb21Param) async {
        await _navigateVdSystem(toWeb21Param);
      },
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearAppError),
          ssoLoginPhases: SSOLoginPhases.initial,
          isMovingHomeNeeded: true,
        );
      },
    );
  }

  /// SSOリクエスト
  Future<void> _navigateVdSystem(String toWeb21Param) async {
    final result = await loginRepository.navigateVdSystem(toWeb21Param);
    await result.when(
      success: (vdidLoginResponse) async {
        // OTP有効化
        if (vdidLoginResponse.isOtpEnabled) {
          state = state.copyWith(
            vdidLoginResponse: vdidLoginResponse,
          );
          await _handleOtpResponse(vdidLoginResponse);
        } else
        // ログイン成功
        if (vdidLoginResponse.isLoggedIn) {
          state = state.copyWith(
            ssoLoginPhases: SSOLoginPhases.web21,
            redirectUrl: vdidLoginResponse.redirectUrl ?? '',
            fromVdParameter: vdidLoginResponse.fromVdParameter ?? '',
          );
        } else {
          state = state.copyWith(
            error: vdidLoginResponse.toAppErrorFromVdResponse(),
            ssoLoginPhases: SSOLoginPhases.initial,
            isMovingHomeNeeded: true,
          );
        }
      },
      failure: (error) {
        state = state.copyWith(
          error: error,
          ssoLoginPhases: SSOLoginPhases.initial,
          isMovingHomeNeeded: true,
        );
      },
    );
  }

  // otp後で有効化時のハンドリング
  Future<void> _handleOtpResponse(VdidLoginResponse response) async {
    final isLateActivateEnable = response.isOtpLateActivationEnable;
    // OTPの情報が受領されている場合且つ後で有効化可能な場合、後て有効化を実施
    // しかし、ワンタイムパスワードアプリによる後で有効化は許容されていないためエラーとしてハンドリング
    if (response.isOtpReceived &&
        response.otpKind != VdOtpKind.app &&
        isLateActivateEnable) {
      await _enableOtpLater();
      return;
    }
    final otpError = state.vdidLoginResponse?.toAppErrorRelatedOtp();
    state = state.copyWith(
      error: otpError?.copyWith(clearErrorOnShowDialog: clearAppError),
      isMovingHomeNeeded: true,
    );
  }

  void setWebViewController(InAppWebViewController controller) {
    state = state.copyWith(web21WebViewController: controller);
  }

  /// OTP有効化（スキップ）
  Future<bool> _enableOtpLater() async {
    final result = await loginRepository.enableOtpLater();
    return result.when(
      success: (vdidLoginResponse) {
        if (vdidLoginResponse.isLoggedIn) {
          state = state.copyWith(
            ssoLoginPhases: SSOLoginPhases.web21,
            redirectUrl: vdidLoginResponse.redirectUrl ?? '',
            fromVdParameter: vdidLoginResponse.fromVdParameter ?? '',
          );
          return true;
        } else {
          state = state.copyWith(
            ssoLoginPhases: SSOLoginPhases.initial,
            error: AppError(
              code: vdidLoginResponse.errorMessageId,
              message: ErrorInfo.enableOtpLaterMessage,
            ),
            isMovingHomeNeeded: true,
          );
          return false;
        }
      },
      failure: (error) {
        state = state.copyWith(
          error: error,
          ssoLoginPhases: SSOLoginPhases.initial,
          isMovingHomeNeeded: true,
        );
        return false;
      },
    );
  }

  /// 法人用アプリの時刻OTP認証またはトランザクション認証から戻って来た場合
  Future<void> onReceiveUniversalLinks({
    required String userId,
    required String result,
    String? inputTimeOtp,
  }) async {
    final idCheckResult = await _checkEncryptedVdid(userId);
    // チェックに引っかかった際には早期リターン
    if (!idCheckResult) return;

    // resultについて、stringからenumに変換
    final web21AppResult = _stringToWeb21AppResult(result);
    final web21OperationResult =
        await _checkWeb21OperationResult(web21AppResult);
    // チェックに引っかかった際には早期リターン
    if (!web21OperationResult) return;

    // otp復号処理
    await _enterOtp(inputTimeOtp);
  }

  Web21AppResult _stringToWeb21AppResult(String result) {
    switch (result) {
      case '0':
        return Web21AppResult.cancel;
      case '1':
        return Web21AppResult.ok;
      case '2':
        return Web21AppResult.vdServerError;
      case '3':
        return Web21AppResult.otpNotEnabled;
      case '4':
        return Web21AppResult.continuableError;
      case '5':
        return Web21AppResult.uncontainableError;
      default:
        return Web21AppResult.none;
    }
  }

  /// VDIDの合致確認処理
  Future<bool> _checkEncryptedVdid(String vdid) async {
    final result = await web21paymentRepository.checkEncryptedVdid(vdid);
    return result.when(
      success: (_) {
        return true;
      },
      failure: (error) {
        state = state.copyWith(error: error, isMovingHomeNeeded: true);
        return false;
      },
    );
  }

  Future<bool> _checkWeb21OperationResult(Web21AppResult web21AppResult) async {
    // キャンセルエラー
    const cancelTimeError = AppError(
      message: PaymentErrorInfo.cancelTimeErrorMessage,
      code: PaymentErrorInfo.cancelTimeErrorCode,
    );
    const cancelSignatureError = AppError(
      message: PaymentErrorInfo.cancelSignatureErrorMessage,
      code: PaymentErrorInfo.cancelSignatureErrorCode,
    );

    // VDサーバー通信エラー
    const vdServerTimeError = AppError(
      message: PaymentErrorInfo.vdServerTimeErrorMessage,
      code: PaymentErrorInfo.vdServerTimeErrorCode,
    );
    const vdServerSignatureError = AppError(
      message: PaymentErrorInfo.vdServerSignatureErrorMessage,
      code: PaymentErrorInfo.vdServerSignatureErrorCode,
    );

    // OTP有効化未済エラー
    const otpNotInitializedError = AppError(
      message: PaymentErrorInfo.web21OtpNotInitializedErrorMessage,
      code: PaymentErrorInfo.web21OtpNotInitializedErrorCode,
      hasFaq: true,
    );

    // 継続可能エラー
    const continuableTimeError = AppError(
      message: PaymentErrorInfo.continuableTimeErrorMessage,
      code: PaymentErrorInfo.continuableTimeErrorCode,
    );
    const continuableSignatureError = AppError(
      message: PaymentErrorInfo.continuableSignatureErrorMessage,
      code: PaymentErrorInfo.continuableSignatureErrorCode,
    );

    // 継続不可エラー
    const uncontainableError = AppError(
      message: PaymentErrorInfo.uncontainableErrorMessage,
      code: PaymentErrorInfo.uncontainableErrorCode,
    );

    // Web21アプリから連携された処理結果によって処理分岐
    switch (web21AppResult) {
      // キャンセルエラーを設定
      case Web21AppResult.cancel:
        if (state.otpType == OtpType.time) {
          // OTP種別が時刻OTP認証の場合
          state = state.copyWith(error: cancelTimeError);
        }
        if (state.otpType == OtpType.signature) {
          // OTP種別がトランザクション認証の場合
          state = state.copyWith(error: cancelSignatureError);
        }
      case Web21AppResult.ok:
        // 後続処理へ
        return true;
      case Web21AppResult.vdServerError:
        // VDサーバー通信エラーを設定
        if (state.otpType == OtpType.time) {
          // OTP種別が時刻OTP認証の場合
          state = state.copyWith(error: vdServerTimeError);
        }
        if (state.otpType == OtpType.signature) {
          // OTP種別がトランザクション認証の場合
          state = state.copyWith(error: vdServerSignatureError);
        }
      case Web21AppResult.otpNotEnabled:
        // OTP有効化未済エラーを設定
        state = state.copyWith(error: otpNotInitializedError);
      case Web21AppResult.continuableError:
        // 継続可能エラーを設定
        if (state.otpType == OtpType.time) {
          // OTP種別が時刻OTP認証の場合
          state = state.copyWith(error: continuableTimeError);
        }
        if (state.otpType == OtpType.signature) {
          // OTP種別がトランザクション認証の場合
          state = state.copyWith(error: continuableSignatureError);
        }
      case Web21AppResult.uncontainableError:
        // 継続不可エラーを設定
        state = state.copyWith(
          error: uncontainableError,
          isMovingHomeNeeded: true,
        );
      case Web21AppResult.none:
        // その他エラーを設定（サーバーエラーにまとめる）
        if (state.otpType == OtpType.time) {
          // OTP種別が時刻OTP認証の場合
          state = state.copyWith(error: vdServerTimeError);
        }
        if (state.otpType == OtpType.signature) {
          // OTP種別がトランザクション認証の場合
          state = state.copyWith(error: vdServerSignatureError);
        }
    }
    return false;
  }

  /// Otp復号化要求およびWebView内のJS関数実行
  Future<void> _enterOtp(String? encryptedOtp) async {
    const web21OtpValidationError = AppError(
      message: PaymentErrorInfo.web21OtpValidationErrorMessage,
      code: PaymentErrorInfo.web21OtpValidationErrorCode,
    );
    // OTP種類によって処理分岐
    switch (state.otpType) {
      // 時刻OTP認証の場合
      case OtpType.time:
        // OTP入力方式が自動ではない場合、またはOTPがnull（想定外ケース）の場合には早期リターン
        if (state.inputType != InputType.autoInput || encryptedOtp == null) {
          return;
        }
        final res = await web21paymentRepository.getDecryptedOtp(encryptedOtp);
        await res.when(
          success: (res) async {
            // バグ防止のためcontrollerの存在チェックを実施
            final controller = state.web21WebViewController;
            if (controller == null) {
              state = state.copyWith(error: web21OtpValidationError);
              return;
            }
            try {
              await controller.evaluateJavascript(
                source: 'inputTimeOtp("$res")',
              );
            } catch (_) {
              state = state.copyWith(error: web21OtpValidationError);
            }
          },
          failure: (error) {
            state = state.copyWith(error: error);
          },
        );
      // トランザクション認証の場合
      case OtpType.signature:
        // バグ防止のためcontrollerの存在チェックを実施
        final controller = state.web21WebViewController;
        if (controller == null) {
          state = state.copyWith(
            error: AppError(
              message: ErrorInfo.defaultErrorMessage,
              code: ErrorInfo.defaultErrorCode,
            ),
          );
          return;
        }
        try {
          // WebView内のJS関数（approveTxOtp()）を実行して振込確認画面へ
          await controller.evaluateJavascript(source: 'approveTxOtp()');
        } catch (_) {
          state = state.copyWith(error: web21OtpValidationError);
        }
      case OtpType.none:
        // OTP種類が想定外の場合はエラーを設定
        state = state.copyWith(error: web21OtpValidationError);
    }
  }

  /// OTP発行ボタン取得値state格納
  void setOtp({
    required String otpType,
    required String inputType,
  }) {
    // otpTypeについて、stringからenumに変換
    OtpType enumOtpType;
    switch (otpType) {
      case 'time':
        enumOtpType = OtpType.time;
      case 'signature':
        enumOtpType = OtpType.signature;
      default:
        enumOtpType = OtpType.none;
    }

    // InputTypeについて、stringからenumに変換
    InputType enumInputType;
    switch (inputType) {
      case '0':
        enumInputType = InputType.autoInput;
      case '1':
        enumInputType = InputType.manualInput;
      default:
        enumInputType = InputType.none;
    }

    state = state.copyWith(
      otpType: enumOtpType,
      inputType: enumInputType,
    );
  }

  /// エラー情報をクリアする
  void clearAppError() {
    state = state.copyWith(error: null);
  }

  /// Home画面遷移フラグをリセット
  void clearMovingHomeFlag() {
    state = state.copyWith(isMovingHomeNeeded: false);
  }

  /// ssoLoginPhasesを初期状態に設定する
  void resetSSOLoginPhases() {
    state = state.copyWith(ssoLoginPhases: SSOLoginPhases.initial);
  }

  /// AES-256で暗号化されたVDIDを取得する
  Future<String?> getEncryptedVdId() async {
    final result = await web21paymentRepository.getEncryptedVdId();
    return result.when(
      success: (encryptedVdId) {
        return encryptedVdId;
      },
      failure: (error) {
        state = state.copyWith(error: error, isMovingHomeNeeded: true);
        return null;
      },
    );
  }

  Future<void> launchIntent({
    required String url,
    required String otp,
    required String userid,
    String? notInputOTP,
    String? encrypt,
  }) async {
    final result = await methodChannelRepository.launchIntent(
      launchURL: url,
      otp: otp,
      userid: userid,
      notInputOTP: notInputOTP,
      encrypt: encrypt,
    );
    result.when(
      success: (_) {},
      failure: (error) {
        state = state.copyWith(error: error);
      },
    );
  }

  Future<Map<String, String>?> getIntentExtras() async {
    final result = await methodChannelRepository.getIntentExtras();
    return result.when(
      success: (params) {
        return params;
      },
      failure: (error) {
        state = state.copyWith(error: error);
        return null;
      },
    );
  }
}
