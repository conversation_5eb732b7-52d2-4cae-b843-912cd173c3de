import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/repositories/aes_salt/aes_salt_repository_impl.dart';
import 'package:dtp_app/repositories/login/debug_login_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'debug_menu_screen_notifier.freezed.dart';

@freezed
class DebugMenuScreenState with _$DebugMenuScreenState {
  const factory DebugMenuScreenState() = _DebugMenuScreenState;
}

final debugMenuScreenProvider =
    StateNotifierProvider<DebugMenuScreenNotifier, DebugMenuScreenState>(
  (ref) => DebugMenuScreenNotifier(
    loginRepository: ref.watch(loginRepositoryProvider),
    mdManager: ref.watch(mdManagerProvider),
    aesSaltRepository: ref.read(aesSaltRepositoryProvider),
  ),
);

class DebugMenuScreenNotifier extends StateNotifier<DebugMenuScreenState> {
  DebugMenuScreenNotifier({
    required this.loginRepository,
    required this.mdManager,
    required this.aesSaltRepository,
  }) : super(const DebugMenuScreenState());

  final LoginRepository loginRepository;
  final MemoryDataManager mdManager;
  final AesSaltRepository aesSaltRepository;

  void setLoginStatus(bool isLoggedIn) {
    if (loginRepository is DebugLoginRepositoryImpl) {
      (loginRepository as DebugLoginRepositoryImpl).setLoginStatus(isLoggedIn);
    }
  }

  /// m05_011 テスト用
  Future<void> setSessionId() async {
    const sessionId = 'e077c736-0f2a-4968-a3ee-92d4674d0974';
    // const sessionId = '7bfb5408-db40-458a-98a5-0dc7af91f897';
    // const sessionId = 'e0453285-a160-4166-a2b0-82a01ffc0270';
    // const sessionId = '8fcb181e-7a16-418f-92bc-8cb0114d520e';
    // const sessionId = 'cc68a3ed-fec3-4b7e-9116-c9cde515b79b';
    // const sessionId = '3416f8f4-66ae-4e45-99fd-1eabfee5d023';
    // const sessionId = '10fd5285-40c0-483a-b803-ae7ff8d6ff8d';
    await mdManager.sessionId.save(sessionId);
    return;
  }

  Future<void> initDebugMenuScreen() async {
    await aesSaltRepository.fetchSaltAndGenerateKey();
  }
}
