// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'debug_menu_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$DebugMenuScreenState {}

/// @nodoc
abstract class $DebugMenuScreenStateCopyWith<$Res> {
  factory $DebugMenuScreenStateCopyWith(DebugMenuScreenState value,
          $Res Function(DebugMenuScreenState) then) =
      _$DebugMenuScreenStateCopyWithImpl<$Res, DebugMenuScreenState>;
}

/// @nodoc
class _$DebugMenuScreenStateCopyWithImpl<$Res,
        $Val extends DebugMenuScreenState>
    implements $DebugMenuScreenStateCopyWith<$Res> {
  _$DebugMenuScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DebugMenuScreenState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$DebugMenuScreenStateImplCopyWith<$Res> {
  factory _$$DebugMenuScreenStateImplCopyWith(_$DebugMenuScreenStateImpl value,
          $Res Function(_$DebugMenuScreenStateImpl) then) =
      __$$DebugMenuScreenStateImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$DebugMenuScreenStateImplCopyWithImpl<$Res>
    extends _$DebugMenuScreenStateCopyWithImpl<$Res, _$DebugMenuScreenStateImpl>
    implements _$$DebugMenuScreenStateImplCopyWith<$Res> {
  __$$DebugMenuScreenStateImplCopyWithImpl(_$DebugMenuScreenStateImpl _value,
      $Res Function(_$DebugMenuScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DebugMenuScreenState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$DebugMenuScreenStateImpl implements _DebugMenuScreenState {
  const _$DebugMenuScreenStateImpl();

  @override
  String toString() {
    return 'DebugMenuScreenState()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DebugMenuScreenStateImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;
}

abstract class _DebugMenuScreenState implements DebugMenuScreenState {
  const factory _DebugMenuScreenState() = _$DebugMenuScreenStateImpl;
}
