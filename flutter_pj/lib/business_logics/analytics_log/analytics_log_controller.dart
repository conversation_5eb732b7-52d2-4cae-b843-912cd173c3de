import 'dart:async';

import 'package:dtp_app/business_logics/interfaces/analytics_log_repository.dart';
import 'package:dtp_app/business_logics/interfaces/katre_sdk_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/extension/string_encryption.dart';
import 'package:dtp_app/repositories/analytics_log/analytics_log_repository_impl.dart';
import 'package:dtp_app/repositories/karte_sdk/karte_sdk_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/user/user_repository_impl.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// Analyticsログ送信クラス (Firebase・KARTE)
class AnalyticsLogController {
  const AnalyticsLogController({
    required this.firebaseAnalyticsLogRepository,
    required this.karteSdkRepository,
    required this.memoryDataManager,
    required this.userRepository,
    required this.loginRepository,
  });

  final FirebaseAnalyticsLogRepository firebaseAnalyticsLogRepository;
  final KarteSdkRepository karteSdkRepository;
  final UserRepository userRepository;
  final LoginRepository loginRepository;
  final MemoryDataManager memoryDataManager;

  /// Firebase Screenログ・KARTE Viewイベント
  void sendScreenLog({
    required String screenNumber,
    required String screenName,
  }) {
    // Firebase Analytics Screenログ
    unawaited(
      firebaseAnalyticsLogRepository.sendScreenFirebaseLog(
        screenNumber: screenNumber,
        screenName: screenName,
      ),
    );

    // Todo　送信項目確定次第、引数をモデル化（DTPO-21564）
    // HACK 画面IDのみ指定して画面名は逆算して設定される形にしたい
    // KARTE Viewイベント
    karteSdkRepository.sendView(
      screenIdNumber: screenNumber,
      screenName: screenName,
    );
  }

  /// KARTE IdentifyとAttributeデータ送信イベント
  Future<void> sendIdentifyAndAttribute({
    required bool freeeLinkStatus,
    required int anotherAccountCount,
    required int cardCount,
  }) async {
    final internalId = await memoryDataManager.internalId.load();
    final dtpId = await memoryDataManager.dtpId.load();

    final vdId = await memoryDataManager.valueDoorId.load();
    final clientId = await memoryDataManager.clientId.load();
    final userType =
        await memoryDataManager.userTypeForFireBaseAnalytics.load();
    final branchId = await memoryDataManager.representativeAccountNumber.load();
    final agreeTermsUpdatedAt =
        await memoryDataManager.agreeTermsUpdatedAt.load();

    // TODO: DTPO-22427のタスクでAPIを作成し、以下の値を取得して送信するように修正
    // final userAuths = '1:0000000';
    // final openAccountUpdateAt = '1999-12-12 00:00:00';

    /// VDID、内部ID、DTPID、お手続き番号はsha256でハッシュ化
    // 先頭から36文字
    final hashedVdId = vdId.convertToSHA256Hash(36);
    final hashedDtpId = dtpId.convertToSHA256Hash(36);
    final hashedInternalId = internalId.convertToSHA256Hash(36);
    final hashedClientId = clientId.convertToSHA256Hash(36);
    final hashedBranchId = branchId.convertToSHA256Hash(36);

    karteSdkRepository.sendIdentifyAndAttribute(
      userId: hashedInternalId,
      attribute: {
        'smbc_business_id': hashedDtpId,
        'vdid': hashedVdId,
        'client_id': hashedClientId,
        'freee_link_status': freeeLinkStatus,
        'another_account_count': anotherAccountCount,
        'card_count': cardCount,
        'user_type': userType,
        'branch_id': hashedBranchId,
        'agree_terms_updated_at': agreeTermsUpdatedAt,
      },
    );
  }

  /// KARTE Attributeデータ（お手続き番号）送信イベント
  // FIXME  DTPO-23068で対応予定　上記KARTE IdentifyとAttributeデータ送信イベントを元に作成、今後共通化検討
  Future<void> sendAttribute() async {
    final referenceNumber = await memoryDataManager.referenceNumber.load();

    /// お手続き番号をsha256でハッシュ化
    // 先頭から36文字
    final hashedReferenceNumber = referenceNumber.convertToSHA256Hash(36);

    karteSdkRepository.sendAttribute(
      attribute: {
        'reference_number': hashedReferenceNumber,
      },
    );
  }

  /// Firebase ボタン押下ログ
  void sendButtonLog({
    required String buttonName,
    required String screenName,
  }) {
    // Firebase Analytics Buttonログ
    firebaseAnalyticsLogRepository.sendButtonFirebaseLog(
      buttonName: buttonName,
      screenName: screenName,
    );
  }

  // KARTE Track送信
  void sendTrack({
    required String buttonName,
    required String screenIdNumber,
  }) {
    // KARTE Track送信
    karteSdkRepository.sendTrack(
      buttonName: buttonName,
      screenIdNumber: screenIdNumber,
    );
  }

  // firebaseの送信項目を取得するためにユーザー情報を取得し、保存
  Future<void> setUserInfo() async {
    final result = await userRepository.getUserInfo();
    result.when(
      success: (result) {
        memoryDataManager.valueDoorId.save(result.vdId);
        memoryDataManager.dtpId.save(result.dtpId);
        memoryDataManager.internalId.save(result.internalId);
        memoryDataManager.contractType.save(result.contractType);
        memoryDataManager.userAuths.save(result.userAuths);
        memoryDataManager.userTypeForFireBaseAnalytics.save(result.userType);
        memoryDataManager.representativeAccountNumber.save(result.branchCode);
      },
      failure: (_) {},
    );
  }
}

final analyticsLogControllerProvider = Provider<AnalyticsLogController>((ref) {
  return AnalyticsLogController(
    firebaseAnalyticsLogRepository: ref.read(analyticsLogRepositoryProvider),
    karteSdkRepository: ref.read(karteSdkRepositoryProvider),
    userRepository: ref.read(userRepositoryProvider),
    memoryDataManager: ref.read(mdManagerProvider),
    loginRepository: ref.read(loginRepositoryProvider),
  );
});
