import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/business_blockage/business_blockage_controller.dart';
import 'package:dtp_app/business_logics/interfaces/app_launching_repository.dart';
import 'package:dtp_app/business_logics/interfaces/fraud_alert_sdk_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/datas/firebase_manager.dart';
import 'package:dtp_app/datas/permission_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/app_launching/app_launching_repository_impl.dart';
import 'package:dtp_app/repositories/fraud_alert_sdk/fraud_alert_sdk_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/user/user_repository_impl.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'before_login_screen_notifier.freezed.dart';

@freezed
class BeforeLoginScreenState with _$BeforeLoginScreenState {
  const factory BeforeLoginScreenState(AppError? error) =
      _BeforeLoginScreenState;
}

enum LoginPhases {
  /// 初期状態（ログアウト）
  initial,

  /// 規約同意
  /// -> 規約同意画面を表示
  checkTerms,

  /// 認可前判定
  /// -> 認可前判定処理を実施
  checkBeforeAuth,

  /// 認可判定
  /// -> 認可画面（WebView）を表示
  checkAuth,

  /// ログイン完了
  /// -> ホーム画面へ遷移
  completed,
}

final beforeLoginScreenProvider = StateNotifierProvider.autoDispose<
    BeforeLoginScreenNotifier, BeforeLoginScreenState>(
  (ref) => BeforeLoginScreenNotifier(
    firebaseManager: ref.read(firebaseManagerProvider),
    permissionManager: ref.read(permissionManagerProvider),
    loginRepository: ref.watch(loginRepositoryProvider),
    appLaunchingRepository: ref.read(appLaunchingRepositoryProvider),
    userRepository: ref.read(userRepositoryProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
    fraudAlertSDKRepository: ref.read(fraudAlertSDKRepositoryProvider),
    businessBlockageController: ref.read(businessBlockageControllerProvider),
  ),
);

class BeforeLoginScreenNotifier extends StateNotifier<BeforeLoginScreenState> {
  BeforeLoginScreenNotifier({
    required this.firebaseManager,
    required this.permissionManager,
    required this.loginRepository,
    required this.appLaunchingRepository,
    required this.userRepository,
    required this.analyticsLogController,
    required this.fraudAlertSDKRepository,
    required this.businessBlockageController,
  }) : super(BeforeLoginScreenState(null));

  final FirebaseManager firebaseManager;
  final PermissionManager permissionManager;
  final LoginRepository loginRepository;
  final AppLaunchingRepository appLaunchingRepository;
  final UserRepository userRepository;
  final AnalyticsLogController analyticsLogController;
  final FraudAlertSDKRepository fraudAlertSDKRepository;
  final BusinessBlockageController businessBlockageController;

  Future<void> requestMessagingPermission() async {
    await firebaseManager.requestMessagingPermission();
  }

  Future<void> requestTrackingAuthorization() async {
    final status = await permissionManager.getTrackingAuthorizationStatus();
    if (status == TrackingStatus.notDetermined) {
      await Future.delayed(const Duration(milliseconds: 200));
      await permissionManager.requestTrackingAuthorization();
    }
  }

  Future<bool> isFirstTimeStartup() async {
    return await appLaunchingRepository.isFirstTimeStartup();
  }

  Future<bool> isFirstLogin() async {
    return await loginRepository.getFirstLoginWithIDPW();
  }

  Future<void> firstTimeStartupDone() async {
    await appLaunchingRepository.firstTimeStartupDone();
  }

  Future<bool> getBusinessBlockageStatus() async {
    // TODO: Ph0.5ではfunctionIdが1種類のため、エラーコードを表示していないが、2種類以上になったタイミングで採番が必要
    await businessBlockageController.getBlockageStatus(functionId: '1101');
    final error = businessBlockageController.businessBlockageError;
    state = state.copyWith(
      error: error?.reason is SorryScreenException ? error : null,
    );
    return error == null ? businessBlockageController.isBlocked : false;
  }
}
