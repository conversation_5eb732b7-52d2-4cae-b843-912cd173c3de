// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'before_login_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BeforeLoginScreenState {
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of BeforeLoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BeforeLoginScreenStateCopyWith<BeforeLoginScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BeforeLoginScreenStateCopyWith<$Res> {
  factory $BeforeLoginScreenStateCopyWith(BeforeLoginScreenState value,
          $Res Function(BeforeLoginScreenState) then) =
      _$BeforeLoginScreenStateCopyWithImpl<$Res, BeforeLoginScreenState>;
  @useResult
  $Res call({AppError? error});

  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$BeforeLoginScreenStateCopyWithImpl<$Res,
        $Val extends BeforeLoginScreenState>
    implements $BeforeLoginScreenStateCopyWith<$Res> {
  _$BeforeLoginScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BeforeLoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of BeforeLoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BeforeLoginScreenStateImplCopyWith<$Res>
    implements $BeforeLoginScreenStateCopyWith<$Res> {
  factory _$$BeforeLoginScreenStateImplCopyWith(
          _$BeforeLoginScreenStateImpl value,
          $Res Function(_$BeforeLoginScreenStateImpl) then) =
      __$$BeforeLoginScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AppError? error});

  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$BeforeLoginScreenStateImplCopyWithImpl<$Res>
    extends _$BeforeLoginScreenStateCopyWithImpl<$Res,
        _$BeforeLoginScreenStateImpl>
    implements _$$BeforeLoginScreenStateImplCopyWith<$Res> {
  __$$BeforeLoginScreenStateImplCopyWithImpl(
      _$BeforeLoginScreenStateImpl _value,
      $Res Function(_$BeforeLoginScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BeforeLoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_$BeforeLoginScreenStateImpl(
      freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$BeforeLoginScreenStateImpl
    with DiagnosticableTreeMixin
    implements _BeforeLoginScreenState {
  const _$BeforeLoginScreenStateImpl(this.error);

  @override
  final AppError? error;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'BeforeLoginScreenState(error: $error)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'BeforeLoginScreenState'))
      ..add(DiagnosticsProperty('error', error));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BeforeLoginScreenStateImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of BeforeLoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BeforeLoginScreenStateImplCopyWith<_$BeforeLoginScreenStateImpl>
      get copyWith => __$$BeforeLoginScreenStateImplCopyWithImpl<
          _$BeforeLoginScreenStateImpl>(this, _$identity);
}

abstract class _BeforeLoginScreenState implements BeforeLoginScreenState {
  const factory _BeforeLoginScreenState(final AppError? error) =
      _$BeforeLoginScreenStateImpl;

  @override
  AppError? get error;

  /// Create a copy of BeforeLoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BeforeLoginScreenStateImplCopyWith<_$BeforeLoginScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
