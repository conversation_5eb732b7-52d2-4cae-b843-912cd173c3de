import 'package:charset/charset.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/utils/ext/string_ext.dart';

/// バリデーションチェック用の関数を集めたmixin
mixin InputValidatorMixin {
  /// 必須入力チェック
  ValidationError checkRequired(
    Object? value, {
    RequiredStyle style = RequiredStyle.input,
  }) {
    if (value == null) {
      // nullの場合はエラー
      return ValidationError.required(style: style);
    }
    if (value is String) {
      // 半角、全角スペースを除去
      final trimmedValue = value.trim();
      if (trimmedValue.isEmpty) {
        return ValidationError.required(style: style);
      }
    }
    return const ValidationError.none();
  }

  /// 半角英大文字のみ使用しているかチェック
  ValidationError checkIsHalfWidthAlphabetUpper(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^[A-Z]+$');
    final result = _checkValidation(regExp, value);
    if (result) {
      return const ValidationError.none();
    } else {
      return const ValidationError.notHalfWidthUpper();
    }
  }

  /// 半角数字のみ使用しているかチェック
  ValidationError checkIsHalfWidthNumber(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^[0-9]+$');
    final result = _checkValidation(regExp, value);
    if (result) {
      return const ValidationError.none();
    }
    // 生年月日のバリデーションチェック時のみ、文言を変更
    return const ValidationError.notDigit();
  }

  /// 有効な年かチェック(生年月日入力画面,年)
  /// ここでは、全角半角数字が入力されているかどうかのみを確認
  ValidationError checkIsValidYear(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'[0-9\uFF10-\uFF19]+');
    final result = _checkValidation(regExp, value);
    if (result) {
      return const ValidationError.none();
    }
    return const ValidationError.invalidDate();
  }

  /// 半角英数記号のみ使用しているかチェック
  ValidationError checkIsHalfWidthSymbol(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^[\u0020-\u007e]+$');
    final result = _checkValidation(regExp, value);
    if (result) {
      return const ValidationError.none();
    }
    return const ValidationError.notHalfWidthSymbol();
  }

  /// 全角カタカナのみ使用しているかチェック
  ValidationError checkIsZenkakuKatakana(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp('^[\\u3000\\u30a1-\\u30f6\\u30fc\\uff10-\\uff19]');
    final result = _checkValidation(regExp, value);
    if (result) {
      return const ValidationError.none();
    }
    return const ValidationError.notZenkakuKatakana();
  }

  /// 全角のみ使用しているかチェック
  ValidationError checkIsZenkaku(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^[^ -~｡-ﾟ]+$');
    final result = _checkValidation(regExp, value);
    if (result) {
      return const ValidationError.none();
    }
    return const ValidationError.notZenkaku();
  }

  /// メールアドレスの形式をチェック
  ValidationError checkEmailFormat(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (regExp.hasMatch(value)) {
      return const ValidationError.none();
    }
    return const ValidationError.invalidEmailFormat();
  }

  /// メールアドレスの相関（確認用メールアドレスと一致しているか）をチェック
  ValidationError checkEmailCorrelation(String? value, String? confirmValue) {
    if (value == null ||
        value.isEmpty ||
        confirmValue == null ||
        confirmValue.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    if (value == confirmValue) {
      return const ValidationError.none();
    }
    return const ValidationError.invalidEmailCorrelation();
  }

  ///携帯電話番号の形式をチェック
  ValidationError checkPhoneNumberFormat(String? value) {
    final stringValue = value.toString();
    if (value == null || stringValue.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^0[789]0');

    if (regExp.hasMatch(stringValue)) {
      return const ValidationError.none();
    }
    return const ValidationError.invalidMobileNumberFormat();
  }

  /// 最大桁数のチェック
  ValidationError checkMaxLength(String? value, int maxLength) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    if (value.length <= maxLength) {
      return const ValidationError.none();
    }
    return ValidationError.maxLength(maxLength);
  }

  /// 最大桁数のチェック(住所の合計桁数)
  ValidationError checkSumMaxLength(String? value, int maxLength) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    if (value.length <= maxLength) {
      return const ValidationError.none();
    }
    return ValidationError.sumMaxLength(maxLength);
  }

  /// 固定桁数のチェック
  ValidationError checkLengthEquals(String? value, int length) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    if (value.length == length) {
      return const ValidationError.none();
    }
    return ValidationError.lengthEquals(length);
  }

  /// 半角数字・固定桁数のチェック（お手続き番号）
  ValidationError checkReferenceNumberLengthEquals(
    String? value,
    int minLength,
    int maxLength,
  ) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 半角数字
    final regExp = RegExp(r'^[0-9]+$');
    final isValidNumber = _checkValidation(regExp, value);
    // 固定桁数
    final isValidLength =
        value.length == minLength || value.length == maxLength;

    if (isValidNumber && isValidLength) {
      return const ValidationError.none();
    } else {
      return ValidationError.referenceNumberLengthEquals(
        minLength,
        maxLength,
      );
    }
  }

  /// 半角数字・固定桁数のチェック（loginID_VDID）
  ValidationError checkVdIdFormatAndLength(
    String? value,
    int maxLength,
  ) {
    if (value == null || value.isEmpty) {
      return const ValidationError.noneVdIdError();
    }
    // 半角数字
    final regExp = RegExp(r'^[0-9]+$');
    final isValidNumber = _checkValidation(regExp, value);
    if (!isValidNumber) return ValidationError.isVdTypeError();
    // 固定桁数
    final isValidLength = value.length == maxLength;
    if (!isValidLength) return ValidationError.isVdTypeError();
    //バリデーションエラー無し
    return const ValidationError.none();
  }

  /// 半角英数字・固定桁数のチェック（loginPassword_VDID）
  ValidationError checkVdPasswordFormatAndLength(
    String? value,
    int minLength,
    int maxLength,
  ) {
    if (value == null || value.isEmpty) {
      return const ValidationError.nonePasswordError();
    }
    // 半角英数字
    final regExp = RegExp(r'^[0-9a-zA-Z]+$');
    final isValidNumber = _checkValidation(regExp, value);
    if (!isValidNumber) return ValidationError.isVdTypeError();
    // 固定桁数
    final isValidLength =
        value.length >= minLength && value.length <= maxLength;
    if (!isValidLength) return ValidationError.isVdTypeError();
    //バリデーションエラー無し
    return const ValidationError.none();
  }

  /// 半角英数記号・固定桁数のチェック（DTPloginID）
  ValidationError checkDtpIdFormatAndLength(
    String? value,
    int minLength,
    int maxLength,
  ) {
    if (value == null || value.isEmpty) {
      return const ValidationError.noneDtpIDError();
    }
    // 半角英数記号　+ .の使用位置(DTP-IDの場合のみ)
    final regExp = RegExp(r'^(?!\.)+(?![!-~|a-zA-Z0-9]*\.@)[!-~|a-zA-Z0-9]*');
    final isValidNumber = regExp.hasMatch(value);
    if (!isValidNumber) return ValidationError.isDtpTypeError();
    // 固定桁数
    final isValidLength =
        value.length >= minLength && value.length <= maxLength;
    if (!isValidLength) return ValidationError.isDtpTypeError();
    //バリデーションエラー無し
    return const ValidationError.none();
  }

  /// 半角英数記号・固定桁数のチェック（Password_DTPID）
  ValidationError checkDtpPWFormatAndLength(
    String? value,
    int minLength,
    int maxLength,
  ) {
    if (value == null || value.isEmpty) {
      return const ValidationError.nonePasswordError();
    }
    // 半角英数記号　+ .の使用位置(DTP-IDの場合のみ)
    final regExp = RegExp(r'^[!-~]+$');
    final isValidNumber = _checkValidation(regExp, value);
    if (!isValidNumber) return ValidationError.isDtpTypeError();
    // 固定桁数
    final isValidLength =
        value.length >= minLength && value.length <= maxLength;
    if (!isValidLength) return ValidationError.isDtpTypeError();
    //バリデーションエラー無し
    return const ValidationError.none();
  }

  /// 有効な月であるかチェック
  ValidationError checkInvalidMonth(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    try {
      final monthInt = int.parse(value);
      if (monthInt >= 1 && monthInt <= 12) {
        // 有効な月
        return const ValidationError.none();
      }
      // 無効な月
      return const ValidationError.invalidDate();
    } catch (e) {
      // 万一数値以外が入力された際の対応
      return const ValidationError.invalidDate();
    }
  }

  /// 日付が存在するかどうかチェック
  ValidationError checkDateExist({
    required String? year,
    required String? month,
    required String? day,
  }) {
    if (year == null || month == null || day == null) {
      // 必須チェックは別におこなう前提のため、いずれかがnullならばエラーなしとする
      return const ValidationError.none();
    }

    try {
      // DateTimeオブジェクトを生成して検証
      final date = DateTime(
        int.parse(year),
        int.parse(month),
        int.parse(day),
      );

      if (date.year == int.parse(year) &&
          date.month == int.parse(month) &&
          date.day == int.parse(day)) {
        // 有効な日付
        return const ValidationError.none();
      }
      // 日付が一致しない
      return const ValidationError.invalidDate();
    } catch (_) {
      // DateTime生成に失敗した場合
      return const ValidationError.invalidDate();
    }
  }

  /// 未来日かどうかをチェックする
  ValidationError checkIsFutureDate({
    required String? year,
    required String? month,
    required String? day,
  }) {
    if (year == null || month == null || day == null) {
      // 必須チェックは別におこなう前提のため、いずれかがnullならばエラーなしとする
      return const ValidationError.none();
    }

    try {
      // DateTimeオブジェクトを生成して検証
      final date = DateTime(
        int.parse(year),
        int.parse(month),
        int.parse(day),
      );

      // 今日の日付を取得（時刻情報を除外）
      final today = DateTime.now();
      final todayDateOnly = DateTime(today.year, today.month, today.day);

      // 未来日ならエラー
      if (date.isAfter(todayDateOnly)) {
        return const ValidationError.futureDate();
      }

      return const ValidationError.none();
    } catch (_) {
      // DateTime生成に失敗した場合
      return const ValidationError.invalidDate();
    }
  }

  /// 郵便番号が存在するかどうかチェック
  ValidationError checkIsValidPostcode(
    String? postcode,
    String? cachedPostcode,
    bool isValidPostcode,
  ) {
    // フラグがtrueかつキャッシュが存在しない（該当住所なしエラー未発生時）
    if (isValidPostcode && cachedPostcode == null) {
      return const ValidationError.none();
    }
    // フラグがtrueかつ、一つ前に入力された該当住所なしエラー発生時の郵便番号と異なる
    if (isValidPostcode && postcode != cachedPostcode) {
      return const ValidationError.none();
    }
    return const ValidationError.invalidPostalCodeFormat();
  }

  /// 半角英数字が混在しているかチェック
  ValidationError checkIsMixedHalfWidthAlphabetAndNumber(String? value) {
    if (value == null || value.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    // 正規表現でチェック
    final regExp = RegExp(r'^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]+$');
    if (regExp.hasMatch(value)) {
      return const ValidationError.none();
    }
    return const ValidationError.notMixedHalfWidthAlphabetAndNumber();
  }

  /// パスワードの相関チェック
  ValidationError checkPasswordEquals(
    String? newPassword,
    String? confirmPassword,
  ) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    if (newPassword == confirmPassword) {
      return const ValidationError.none();
    }
    return const ValidationError.passwordEquals();
  }

  /// 一文字づつバリデーションチェックを実施するメソッド
  bool _checkValidation(RegExp regExp, String value) {
    for (final char in value.split('')) {
      if (!regExp.hasMatch(char)) {
        return false;
      }
    }
    return true;
  }

  // BEに倣い、法人Webへの全角送信項目に関しては正規表現、文字コードチェックの両方を実施する
  ValidationError checkIsValidHoujinWebZenkaku(String? value) {
    if (value.isNullOrEmpty) {
      // 必須チェックは別におこなう前提のため、nullもしくは空ならばエラーなしとする
      return const ValidationError.none();
    }
    for (final char in value!.split('')) {
      final byteArray = shiftJis.encode(char);
      // バイナリへの変換後、数値として扱う為の処理
      final result = byteArray.length < 2
          ? byteArray[0]
          : (byteArray[0] << 8) | byteArray[1];
      bool hasError = false;
      for (final range in _hojinWebFullJp) {
        if (result >= range[0] && result <= range[1]) {
          hasError = false;
          break;
        }
        hasError = true;
      }
      if (hasError) return const ValidationError.notZenkaku();
    }
    return ValidationError.none();
  }
}

const _hojinWebFullJp = [
  // 全角文字
  [0x8140, 0x81AC], // [　]～[〓]
  [0x81B8, 0x81BF], // [∈]～[∩]
  [0x81C8, 0x81CE], // [∧]～[∃]
  [0x81DA, 0x81E8], // [∠]～[∬]
  [0x81F0, 0x81F7], // [Å]～[¶]
  [0x81FC, 0x81FC], // [◯]～[◯]
  [0x824F, 0x8258], // [０]～[９]
  [0x8260, 0x8279], // [Ａ]～[Ｚ]
  [0x8281, 0x829A], // [ａ]～[ｚ]
  [0x829F, 0x82F1], // [ぁ]～[ん]
  [0x8340, 0x837E], // [ァ]～[ミ]
  [0x8380, 0x8396], // [ム]～[ヶ]
  [0x839F, 0x83B6], // [Α]～[Ω]
  [0x83BF, 0x83D6], // [α]～[ω]
  [0x8440, 0x8460], // [А]～[Я]
  [0x8470, 0x847E], // [а]～[н]
  [0x8480, 0x8491], // [о]～[я]
  [0x849F, 0x84BE], // [─]～[╂]
  [0x8754, 0x875D], // [Ⅰ]～[Ⅹ] ※機種依存
  [0x8782, 0x8782], // [№]～[№] ※機種依存
  [0x8784, 0x8784], // [℡]～[℡] ※機種依存
  [0x878A, 0x878A], // [㈱]～[㈱] ※機種依存
  [0x889F, 0x88FC], // [亜]～[蔭]
  [0x8940, 0x897E], // [院]～[円]
  [0x8980, 0x89FC], // [園]～[改]
  [0x8A40, 0x8A7E], // [魁]～[樫]
  [0x8A80, 0x8AFC], // [橿]～[棄]
  [0x8B40, 0x8B7E], // [機]～[救]
  [0x8B80, 0x8BFC], // [朽]～[屈]
  [0x8C40, 0x8C7E], // [掘]～[鯨]
  [0x8C80, 0x8CFC], // [劇]～[向]
  [0x8D40, 0x8D7E], // [后]～[降]
  [0x8D80, 0x8DFC], // [項]～[刷]
  [0x8E40, 0x8E7E], // [察]～[止]
  [0x8E80, 0x8EFC], // [死]～[周]
  [0x8F40, 0x8F7E], // [宗]～[淳]
  [0x8F80, 0x8FFC], // [準]～[飾]
  [0x9040, 0x907E], // [拭]～[厨]
  [0x9080, 0x90FC], // [逗]～[線]
  [0x9140, 0x917E], // [繊]～[掻]
  [0x9180, 0x91FC], // [操]～[只]
  [0x9240, 0x927E], // [叩]～[蓄]
  [0x9280, 0x92FC], // [逐]～[逓]
  [0x9340, 0x937E], // [邸]～[冬]
  [0x9380, 0x93FC], // [凍]～[入]
  [0x9440, 0x947E], // [如]～[梅]
  [0x9480, 0x94FC], // [楳]～[美]
  [0x9540, 0x957E], // [鼻]～[敷]
  [0x9580, 0x95FC], // [斧]～[朋]
  [0x9640, 0x967E], // [法]～[盆]
  [0x9680, 0x96FC], // [摩]～[癒]
  [0x9740, 0x977E], // [諭]～[欲]
  [0x9780, 0x97FC], // [沃]～[聯]
  [0x9840, 0x9872], // [蓮]～[腕]
  [0x989F, 0x98FC], // [弌]～[傲]
  [0x9940, 0x997E], // [僉]～[凭]
  [0x9980, 0x99FC], // [凰]～[咨]
  [0x9A40, 0x9A7E], // [咫]～[嘸]
  [0x9A80, 0x9AFC], // [噫]～[奩]
  [0x9B40, 0x9B7E], // [奸]～[宀]
  [0x9B80, 0x9BFC], // [它]～[廏]
  [0x9C40, 0x9C7E], // [廖]～[恠]
  [0x9C80, 0x9CFC], // [怙]～[戛]
  [0x9D40, 0x9D7E], // [戞]～[捫]
  [0x9D80, 0x9DFC], // [捩]～[暼]
  [0x9E40, 0x9E7E], // [曄]～[桎]
  [0x9E80, 0x9EFC], // [梳]～[檣]
  [0x9F40, 0x9F7E], // [檗]～[毯]
  [0x9F80, 0x9FFC], // [麾]～[滌]
  [0xE040, 0xE07E], // [漾]～[烝]
  [0xE080, 0xE0FC], // [烙]～[珱]
  [0xE140, 0xE17E], // [瓠]～[痿]
  [0xE180, 0xE1FC], // [痼]～[磬]
  [0xE240, 0xE27E], // [磧]～[窰]
  [0xE280, 0xE2FC], // [窶]～[紆]
  [0xE340, 0xE37E], // [紂]～[縷]
  [0xE380, 0xE3FC], // [縲]～[腋]
  [0xE440, 0xE47E], // [隋]～[艤]
  [0xE480, 0xE4FC], // [艢]～[蕈]
  [0xE540, 0xE57E], // [蕁]～[蛬]
  [0xE580, 0xE5FC], // [蛟]～[襞]
  [0xE640, 0xE67E], // [襦]～[諧]
  [0xE680, 0xE6FC], // [諤]～[蹊]
  [0xE740, 0xE77E], // [蹇]～[轜]
  [0xE780, 0xE7FC], // [轢]～[錮]
  [0xE840, 0xE87E], // [錙]～[閙]
  [0xE880, 0xE8FC], // [閠]～[顰]
  [0xE940, 0xE97E], // [顱]～[驃]
  [0xE980, 0xE9FC], // [騾]～[鵈]
  [0xEA40, 0xEA7E], // [鵝]～[黯]
  [0xEA80, 0xEAA4], // [黴]～[熙]
  [0xED40, 0xED7E], // [纊]～[﨏] ※機種依存
  [0xED80, 0xEDFC], // [塚]～[犱] ※機種依存
  [0xEE40, 0xEE7E], // [犾]～[蕙] ※機種依存
  [0xEE80, 0xEEEC], // [蕫]～[黑] ※機種依存
  [0xEEFA, 0xEEFC], // [￤]～[＂] ※機種依存
  [0xFA40, 0xFA49], // [ⅰ]～[ⅹ] ※機種依存
  [0xFA55, 0xFA57], // [￤]～[＂] ※機種依存
  [0xFA5C, 0xFA7E], // [纊]～[兊] ※機種依存
  [0xFA80, 0xFAFC], // [兤]～[浯] ※機種依存
  [0xFB40, 0xFB7E], // [涖]～[神] ※機種依存
  [0xFB80, 0xFBFC], // [祥]～[髙] ※機種依存
  [0xFC40, 0xFC4B], // [髜]～[黑] ※機種依存
];
