import 'package:dtp_app/business_logics/interfaces/app_launching_repository.dart';
import 'package:dtp_app/datas/app_information_manager.dart';
import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';
import 'package:dtp_app/repositories/aes_salt/aes_salt_repository_impl.dart';
import 'package:dtp_app/repositories/app_launching/app_launching_repository_impl.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'splash_screen_notifier.freezed.dart';

@freezed
class SplashScreenState with _$SplashScreenState {
  const factory SplashScreenState({
    @Default(LaunchingPhases.initial) LaunchingPhases launchingPhase,
    ForceUpdateConfig? forceUpdateConfig,
  }) = _SplashScreenState;
}

enum LaunchingPhases {
  /// 初期状態
  initial,

  /// 強制アップデートが必要（エラー時も含む）
  /// -> 強制アップデート画面に遷移
  needUpdate,

  /// SALT取得エラー
  /// -> 共通ダイアログ9を表示
  errorSalt,

  /// 強制アップデートが不要
  /// -> ホーム画面へ遷移
  completed,
}

final splashScreenProvider =
    StateNotifierProvider<SplashScreenNotifier, SplashScreenState>(
  (ref) => SplashScreenNotifier(
    appLaunchingRepository: ref.watch(appLaunchingRepositoryProvider),
    appInformationManager: ref.read(appInformationManagerProvider),
    aesSaltRepository: ref.read(aesSaltRepositoryProvider),
    secureStorageManager: ref.watch(secureStorageManagerProvider),
    state: const SplashScreenState(),
  ),
);

class SplashScreenNotifier extends StateNotifier<SplashScreenState> {
  SplashScreenNotifier({
    required this.appLaunchingRepository,
    required this.appInformationManager,
    required this.aesSaltRepository,
    required this.secureStorageManager,
    required SplashScreenState state,
  }) : super(state);

  final AppLaunchingRepository appLaunchingRepository;
  final AppInformationManager appInformationManager;
  final AesSaltRepository aesSaltRepository;
  final SecureStorageManager secureStorageManager;

  //HACKME 初期化処理を全体的にリファクタリングする
  Future<void> initializer() async {
    // 各OSの端末情報を取得
    final appInformation = await appInformationManager.load();
    // SecureStorage内情報削除
    await _deleteIvAndKey();

    // アップデート情報・SALT値ともにアプリに必要な情報なので、どの動線でも必ず非同期的に両情報とも取得する
    final results = await Future.wait([
      appLaunchingRepository.checkForceUpdate(
        appInformation: appInformation,
      ),
      aesSaltRepository.fetchSaltAndGenerateKey(),
    ]);
    final forceUpdateResult = results[0] as ForceUpdateConfig,
        fetchSaltResult = results[1] as AppResult<void>;

    // アップデート情報を優先して処理する
    if (forceUpdateResult.updateType != UpdateType.none) {
      state = state.copyWith(
        forceUpdateConfig: forceUpdateResult,
        launchingPhase: LaunchingPhases.needUpdate,
      );
      return;
    }
    fetchSaltResult.when(
      success: (_) {
        state = state.copyWith(launchingPhase: LaunchingPhases.completed);
      },
      failure: (_) {
        state = state.copyWith(launchingPhase: LaunchingPhases.errorSalt);
      },
    );
  }

  Future<void> fetchSaltAndGenerateKey() async {
    final result = await aesSaltRepository.fetchSaltAndGenerateKey();
    result.when(
      success: (_) {
        state = state.copyWith(
          launchingPhase: LaunchingPhases.completed,
        );
      },
      failure: (error) {
        state = state.copyWith(
          launchingPhase: LaunchingPhases.errorSalt,
        );
      },
    );
  }

  Future<void> _deleteIvAndKey() async {
    // 画面描画時にセキュアストレージの情報を削除
    await secureStorageManager.aesIv.delete();
    await secureStorageManager.aesKey.delete();
  }
}
