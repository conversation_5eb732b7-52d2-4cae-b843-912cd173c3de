// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'splash_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SplashScreenState {
  LaunchingPhases get launchingPhase => throw _privateConstructorUsedError;
  ForceUpdateConfig? get forceUpdateConfig =>
      throw _privateConstructorUsedError;

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SplashScreenStateCopyWith<SplashScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SplashScreenStateCopyWith<$Res> {
  factory $SplashScreenStateCopyWith(
          SplashScreenState value, $Res Function(SplashScreenState) then) =
      _$SplashScreenStateCopyWithImpl<$Res, SplashScreenState>;
  @useResult
  $Res call(
      {LaunchingPhases launchingPhase, ForceUpdateConfig? forceUpdateConfig});

  $ForceUpdateConfigCopyWith<$Res>? get forceUpdateConfig;
}

/// @nodoc
class _$SplashScreenStateCopyWithImpl<$Res, $Val extends SplashScreenState>
    implements $SplashScreenStateCopyWith<$Res> {
  _$SplashScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? launchingPhase = null,
    Object? forceUpdateConfig = freezed,
  }) {
    return _then(_value.copyWith(
      launchingPhase: null == launchingPhase
          ? _value.launchingPhase
          : launchingPhase // ignore: cast_nullable_to_non_nullable
              as LaunchingPhases,
      forceUpdateConfig: freezed == forceUpdateConfig
          ? _value.forceUpdateConfig
          : forceUpdateConfig // ignore: cast_nullable_to_non_nullable
              as ForceUpdateConfig?,
    ) as $Val);
  }

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ForceUpdateConfigCopyWith<$Res>? get forceUpdateConfig {
    if (_value.forceUpdateConfig == null) {
      return null;
    }

    return $ForceUpdateConfigCopyWith<$Res>(_value.forceUpdateConfig!, (value) {
      return _then(_value.copyWith(forceUpdateConfig: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SplashScreenStateImplCopyWith<$Res>
    implements $SplashScreenStateCopyWith<$Res> {
  factory _$$SplashScreenStateImplCopyWith(_$SplashScreenStateImpl value,
          $Res Function(_$SplashScreenStateImpl) then) =
      __$$SplashScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LaunchingPhases launchingPhase, ForceUpdateConfig? forceUpdateConfig});

  @override
  $ForceUpdateConfigCopyWith<$Res>? get forceUpdateConfig;
}

/// @nodoc
class __$$SplashScreenStateImplCopyWithImpl<$Res>
    extends _$SplashScreenStateCopyWithImpl<$Res, _$SplashScreenStateImpl>
    implements _$$SplashScreenStateImplCopyWith<$Res> {
  __$$SplashScreenStateImplCopyWithImpl(_$SplashScreenStateImpl _value,
      $Res Function(_$SplashScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? launchingPhase = null,
    Object? forceUpdateConfig = freezed,
  }) {
    return _then(_$SplashScreenStateImpl(
      launchingPhase: null == launchingPhase
          ? _value.launchingPhase
          : launchingPhase // ignore: cast_nullable_to_non_nullable
              as LaunchingPhases,
      forceUpdateConfig: freezed == forceUpdateConfig
          ? _value.forceUpdateConfig
          : forceUpdateConfig // ignore: cast_nullable_to_non_nullable
              as ForceUpdateConfig?,
    ));
  }
}

/// @nodoc

class _$SplashScreenStateImpl implements _SplashScreenState {
  const _$SplashScreenStateImpl(
      {this.launchingPhase = LaunchingPhases.initial, this.forceUpdateConfig});

  @override
  @JsonKey()
  final LaunchingPhases launchingPhase;
  @override
  final ForceUpdateConfig? forceUpdateConfig;

  @override
  String toString() {
    return 'SplashScreenState(launchingPhase: $launchingPhase, forceUpdateConfig: $forceUpdateConfig)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SplashScreenStateImpl &&
            (identical(other.launchingPhase, launchingPhase) ||
                other.launchingPhase == launchingPhase) &&
            (identical(other.forceUpdateConfig, forceUpdateConfig) ||
                other.forceUpdateConfig == forceUpdateConfig));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, launchingPhase, forceUpdateConfig);

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SplashScreenStateImplCopyWith<_$SplashScreenStateImpl> get copyWith =>
      __$$SplashScreenStateImplCopyWithImpl<_$SplashScreenStateImpl>(
          this, _$identity);
}

abstract class _SplashScreenState implements SplashScreenState {
  const factory _SplashScreenState(
      {final LaunchingPhases launchingPhase,
      final ForceUpdateConfig? forceUpdateConfig}) = _$SplashScreenStateImpl;

  @override
  LaunchingPhases get launchingPhase;
  @override
  ForceUpdateConfig? get forceUpdateConfig;

  /// Create a copy of SplashScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SplashScreenStateImplCopyWith<_$SplashScreenStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
