// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'password_input_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PasswordInputScreenState {
// パスワードの表示・非表示を管理する状態
  bool get isObscure =>
      throw _privateConstructorUsedError; // 初回入力時のバリデーションチェック判定用
  bool get isFirstValidation =>
      throw _privateConstructorUsedError; // バリデーションチェック結果
  ValidationResult get validationResult => throw _privateConstructorUsedError;

  /// Create a copy of PasswordInputScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PasswordInputScreenStateCopyWith<PasswordInputScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PasswordInputScreenStateCopyWith<$Res> {
  factory $PasswordInputScreenStateCopyWith(PasswordInputScreenState value,
          $Res Function(PasswordInputScreenState) then) =
      _$PasswordInputScreenStateCopyWithImpl<$Res, PasswordInputScreenState>;
  @useResult
  $Res call(
      {bool isObscure,
      bool isFirstValidation,
      ValidationResult validationResult});
}

/// @nodoc
class _$PasswordInputScreenStateCopyWithImpl<$Res,
        $Val extends PasswordInputScreenState>
    implements $PasswordInputScreenStateCopyWith<$Res> {
  _$PasswordInputScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PasswordInputScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isObscure = null,
    Object? isFirstValidation = null,
    Object? validationResult = null,
  }) {
    return _then(_value.copyWith(
      isObscure: null == isObscure
          ? _value.isObscure
          : isObscure // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstValidation: null == isFirstValidation
          ? _value.isFirstValidation
          : isFirstValidation // ignore: cast_nullable_to_non_nullable
              as bool,
      validationResult: null == validationResult
          ? _value.validationResult
          : validationResult // ignore: cast_nullable_to_non_nullable
              as ValidationResult,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PasswordInputScreenStateImplCopyWith<$Res>
    implements $PasswordInputScreenStateCopyWith<$Res> {
  factory _$$PasswordInputScreenStateImplCopyWith(
          _$PasswordInputScreenStateImpl value,
          $Res Function(_$PasswordInputScreenStateImpl) then) =
      __$$PasswordInputScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isObscure,
      bool isFirstValidation,
      ValidationResult validationResult});
}

/// @nodoc
class __$$PasswordInputScreenStateImplCopyWithImpl<$Res>
    extends _$PasswordInputScreenStateCopyWithImpl<$Res,
        _$PasswordInputScreenStateImpl>
    implements _$$PasswordInputScreenStateImplCopyWith<$Res> {
  __$$PasswordInputScreenStateImplCopyWithImpl(
      _$PasswordInputScreenStateImpl _value,
      $Res Function(_$PasswordInputScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of PasswordInputScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isObscure = null,
    Object? isFirstValidation = null,
    Object? validationResult = null,
  }) {
    return _then(_$PasswordInputScreenStateImpl(
      isObscure: null == isObscure
          ? _value.isObscure
          : isObscure // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstValidation: null == isFirstValidation
          ? _value.isFirstValidation
          : isFirstValidation // ignore: cast_nullable_to_non_nullable
              as bool,
      validationResult: null == validationResult
          ? _value.validationResult
          : validationResult // ignore: cast_nullable_to_non_nullable
              as ValidationResult,
    ));
  }
}

/// @nodoc

class _$PasswordInputScreenStateImpl implements _PasswordInputScreenState {
  const _$PasswordInputScreenStateImpl(
      {this.isObscure = true,
      this.isFirstValidation = true,
      this.validationResult = ValidationResult.none});

// パスワードの表示・非表示を管理する状態
  @override
  @JsonKey()
  final bool isObscure;
// 初回入力時のバリデーションチェック判定用
  @override
  @JsonKey()
  final bool isFirstValidation;
// バリデーションチェック結果
  @override
  @JsonKey()
  final ValidationResult validationResult;

  @override
  String toString() {
    return 'PasswordInputScreenState(isObscure: $isObscure, isFirstValidation: $isFirstValidation, validationResult: $validationResult)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PasswordInputScreenStateImpl &&
            (identical(other.isObscure, isObscure) ||
                other.isObscure == isObscure) &&
            (identical(other.isFirstValidation, isFirstValidation) ||
                other.isFirstValidation == isFirstValidation) &&
            (identical(other.validationResult, validationResult) ||
                other.validationResult == validationResult));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isObscure, isFirstValidation, validationResult);

  /// Create a copy of PasswordInputScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PasswordInputScreenStateImplCopyWith<_$PasswordInputScreenStateImpl>
      get copyWith => __$$PasswordInputScreenStateImplCopyWithImpl<
          _$PasswordInputScreenStateImpl>(this, _$identity);
}

abstract class _PasswordInputScreenState implements PasswordInputScreenState {
  const factory _PasswordInputScreenState(
          {final bool isObscure,
          final bool isFirstValidation,
          final ValidationResult validationResult}) =
      _$PasswordInputScreenStateImpl;

// パスワードの表示・非表示を管理する状態
  @override
  bool get isObscure; // 初回入力時のバリデーションチェック判定用
  @override
  bool get isFirstValidation; // バリデーションチェック結果
  @override
  ValidationResult get validationResult;

  /// Create a copy of PasswordInputScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PasswordInputScreenStateImplCopyWith<_$PasswordInputScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
