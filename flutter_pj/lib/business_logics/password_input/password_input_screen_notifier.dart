import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/repositories/jpki_sdk/jpki_sdk_repository_impl.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'password_input_screen_notifier.freezed.dart';

@freezed
class PasswordInputScreenState with _$PasswordInputScreenState {
  const factory PasswordInputScreenState({
    // パスワードの表示・非表示を管理する状態
    @Default(true) bool isObscure,
    // 初回入力時のバリデーションチェック判定用
    @Default(true) bool isFirstValidation,

    // バリデーションチェック結果
    @Default(ValidationResult.none) ValidationResult validationResult,
  }) = _PasswordInputScreenState;
}

final passwordInputScreenProvider = StateNotifierProvider.autoDispose<
    PasswordInputScreenNotifier, PasswordInputScreenState>(
  (ref) => PasswordInputScreenNotifier(
    jpkiSdkRepository: ref.watch(jpkiSdkRepositoryProvider),
  ),
);

// バリデーションチェック結果
enum ValidationResult {
  // バリデーションエラー
  error,
  // 完了済み(チェック結果でエラーなし)
  complete,
  // 未チェック
  none,
}

class PasswordInputScreenNotifier
    extends StateNotifier<PasswordInputScreenState> {
  PasswordInputScreenNotifier({
    required this.jpkiSdkRepository,
  }) : super(const PasswordInputScreenState());

  final JpkiSdkRepository jpkiSdkRepository;

  // JPKIのpassword ローカル保存
  Future<void> setJpkiPassword({
    required String password,
  }) async {
    await jpkiSdkRepository.setJpkiPassword(password);
  }

  // バリデーションエラー状態かを取得
  bool isValidationError() {
    return state.validationResult == ValidationResult.error;
  }

  // バリデーションチェック完了かを取得
  bool isValidationComplete() {
    return state.validationResult == ValidationResult.complete;
  }

  // パスワードの表示・非表示状態を切り替える
  void setIsObscure(bool value) {
    state = state.copyWith(
      isObscure: value,
    );
  }

  // 初回入力時のバリデーションチェックを判定する
  void setIsFirstValidation(bool value) {
    state = state.copyWith(
      isFirstValidation: value,
    );
  }

  // バリデーションチェック結果の変更
  void setIsValidationResult(ValidationResult value) {
    state = state.copyWith(
      validationResult: value,
    );
  }

  // パスワードをバリデートする関数
  ValidationResult validatePassword(String password) {
    if (password.isEmpty) {
      return ValidationResult.none;
    }

    // 「6桁以上16桁以内で、大文字英語と数字のみで、両方が含まれている文字列」の正規表現
    final bool match =
        RegExp(r'^(?=.*[A-Z])(?=.*\d)[A-Z\d]{6,16}$').hasMatch(password);

    if (match) {
      return ValidationResult.complete;
    } else {
      return ValidationResult.error;
    }
  }
}
