// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identity_verification_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IdentityVerificationInputState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() sendSuccess,
    required TResult Function(AppError error) sendFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? sendSuccess,
    TResult? Function(AppError error)? sendFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? sendSuccess,
    TResult Function(AppError error)? sendFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SendSuccess value) sendSuccess,
    required TResult Function(_SendFailure value) sendFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SendSuccess value)? sendSuccess,
    TResult? Function(_SendFailure value)? sendFailure,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SendSuccess value)? sendSuccess,
    TResult Function(_SendFailure value)? sendFailure,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityVerificationInputStateCopyWith<$Res> {
  factory $IdentityVerificationInputStateCopyWith(
          IdentityVerificationInputState value,
          $Res Function(IdentityVerificationInputState) then) =
      _$IdentityVerificationInputStateCopyWithImpl<$Res,
          IdentityVerificationInputState>;
}

/// @nodoc
class _$IdentityVerificationInputStateCopyWithImpl<$Res,
        $Val extends IdentityVerificationInputState>
    implements $IdentityVerificationInputStateCopyWith<$Res> {
  _$IdentityVerificationInputStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$IdentityVerificationInputStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'IdentityVerificationInputState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() sendSuccess,
    required TResult Function(AppError error) sendFailure,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? sendSuccess,
    TResult? Function(AppError error)? sendFailure,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? sendSuccess,
    TResult Function(AppError error)? sendFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SendSuccess value) sendSuccess,
    required TResult Function(_SendFailure value) sendFailure,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SendSuccess value)? sendSuccess,
    TResult? Function(_SendFailure value)? sendFailure,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SendSuccess value)? sendSuccess,
    TResult Function(_SendFailure value)? sendFailure,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements IdentityVerificationInputState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$IdentityVerificationInputStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'IdentityVerificationInputState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() sendSuccess,
    required TResult Function(AppError error) sendFailure,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? sendSuccess,
    TResult? Function(AppError error)? sendFailure,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? sendSuccess,
    TResult Function(AppError error)? sendFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SendSuccess value) sendSuccess,
    required TResult Function(_SendFailure value) sendFailure,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SendSuccess value)? sendSuccess,
    TResult? Function(_SendFailure value)? sendFailure,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SendSuccess value)? sendSuccess,
    TResult Function(_SendFailure value)? sendFailure,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements IdentityVerificationInputState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$SendSuccessImplCopyWith<$Res> {
  factory _$$SendSuccessImplCopyWith(
          _$SendSuccessImpl value, $Res Function(_$SendSuccessImpl) then) =
      __$$SendSuccessImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SendSuccessImplCopyWithImpl<$Res>
    extends _$IdentityVerificationInputStateCopyWithImpl<$Res,
        _$SendSuccessImpl> implements _$$SendSuccessImplCopyWith<$Res> {
  __$$SendSuccessImplCopyWithImpl(
      _$SendSuccessImpl _value, $Res Function(_$SendSuccessImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SendSuccessImpl implements _SendSuccess {
  const _$SendSuccessImpl();

  @override
  String toString() {
    return 'IdentityVerificationInputState.sendSuccess()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SendSuccessImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() sendSuccess,
    required TResult Function(AppError error) sendFailure,
  }) {
    return sendSuccess();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? sendSuccess,
    TResult? Function(AppError error)? sendFailure,
  }) {
    return sendSuccess?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? sendSuccess,
    TResult Function(AppError error)? sendFailure,
    required TResult orElse(),
  }) {
    if (sendSuccess != null) {
      return sendSuccess();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SendSuccess value) sendSuccess,
    required TResult Function(_SendFailure value) sendFailure,
  }) {
    return sendSuccess(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SendSuccess value)? sendSuccess,
    TResult? Function(_SendFailure value)? sendFailure,
  }) {
    return sendSuccess?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SendSuccess value)? sendSuccess,
    TResult Function(_SendFailure value)? sendFailure,
    required TResult orElse(),
  }) {
    if (sendSuccess != null) {
      return sendSuccess(this);
    }
    return orElse();
  }
}

abstract class _SendSuccess implements IdentityVerificationInputState {
  const factory _SendSuccess() = _$SendSuccessImpl;
}

/// @nodoc
abstract class _$$SendFailureImplCopyWith<$Res> {
  factory _$$SendFailureImplCopyWith(
          _$SendFailureImpl value, $Res Function(_$SendFailureImpl) then) =
      __$$SendFailureImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$SendFailureImplCopyWithImpl<$Res>
    extends _$IdentityVerificationInputStateCopyWithImpl<$Res,
        _$SendFailureImpl> implements _$$SendFailureImplCopyWith<$Res> {
  __$$SendFailureImplCopyWithImpl(
      _$SendFailureImpl _value, $Res Function(_$SendFailureImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$SendFailureImpl(
      null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
    ));
  }

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$SendFailureImpl implements _SendFailure {
  const _$SendFailureImpl(this.error);

  @override
  final AppError error;

  @override
  String toString() {
    return 'IdentityVerificationInputState.sendFailure(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendFailureImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SendFailureImplCopyWith<_$SendFailureImpl> get copyWith =>
      __$$SendFailureImplCopyWithImpl<_$SendFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() sendSuccess,
    required TResult Function(AppError error) sendFailure,
  }) {
    return sendFailure(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? sendSuccess,
    TResult? Function(AppError error)? sendFailure,
  }) {
    return sendFailure?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? sendSuccess,
    TResult Function(AppError error)? sendFailure,
    required TResult orElse(),
  }) {
    if (sendFailure != null) {
      return sendFailure(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_SendSuccess value) sendSuccess,
    required TResult Function(_SendFailure value) sendFailure,
  }) {
    return sendFailure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_SendSuccess value)? sendSuccess,
    TResult? Function(_SendFailure value)? sendFailure,
  }) {
    return sendFailure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_SendSuccess value)? sendSuccess,
    TResult Function(_SendFailure value)? sendFailure,
    required TResult orElse(),
  }) {
    if (sendFailure != null) {
      return sendFailure(this);
    }
    return orElse();
  }
}

abstract class _SendFailure implements IdentityVerificationInputState {
  const factory _SendFailure(final AppError error) = _$SendFailureImpl;

  AppError get error;

  /// Create a copy of IdentityVerificationInputState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SendFailureImplCopyWith<_$SendFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$IdentityVerificationScreenState {
  IdentityVerificationInputState get inputState =>
      throw _privateConstructorUsedError;

  /// 項目編集時にバリデーションチェックを実行するかどうか
  bool get isRealTimeValidationMode => throw _privateConstructorUsedError;

  /// 代表者確認
  bool get isRepresentative => throw _privateConstructorUsedError;

  /// eKYCか否か
  bool get isEkyc => throw _privateConstructorUsedError;

  /// role(代表者/代理人/実質的支配者)
  String get role => throw _privateConstructorUsedError;

  /// 氏名-姓
  String? get familyName => throw _privateConstructorUsedError;
  ValidationError get familyNameValidationError =>
      throw _privateConstructorUsedError;

  /// 氏名-名
  String? get givenName => throw _privateConstructorUsedError;
  ValidationError get givenNameValidationError =>
      throw _privateConstructorUsedError;

  /// 氏名-セイ
  String? get familyNameKana => throw _privateConstructorUsedError;
  ValidationError get familyNameKanaValidationError =>
      throw _privateConstructorUsedError;

  /// 氏名-メイ
  String? get givenNameKana => throw _privateConstructorUsedError;
  ValidationError get givenNameKanaValidationError =>
      throw _privateConstructorUsedError;

  /// 英字の名前が編集可能であるか否か
  bool get isNameEditable => throw _privateConstructorUsedError;

  /// 氏名-Last Name
  String? get familyNameAlphabetic => throw _privateConstructorUsedError;
  ValidationError get familyNameAlphabeticValidationError =>
      throw _privateConstructorUsedError;

  /// 氏名-First Name
  String? get givenNameAlphabetic => throw _privateConstructorUsedError;
  ValidationError get givenNameAlphabeticValidationError =>
      throw _privateConstructorUsedError;

  /// 役職名
  String? get position => throw _privateConstructorUsedError;
  ValidationError get positionValidationError =>
      throw _privateConstructorUsedError;

  /// 生年月日（年）
  String? get birthDateYear => throw _privateConstructorUsedError;
  ValidationError get birthDateYearValidationError =>
      throw _privateConstructorUsedError;

  /// 生年月日（月）
  String? get birthDateMonth => throw _privateConstructorUsedError;
  ValidationError get birthDateMonthValidationError =>
      throw _privateConstructorUsedError;

  /// 生年月日（日）
  String? get birthDateDay => throw _privateConstructorUsedError;
  ValidationError get birthDateDayValidationError =>
      throw _privateConstructorUsedError;

  /// 郵便番号
  String? get postcode => throw _privateConstructorUsedError;
  ValidationError get postcodeValidationError =>
      throw _privateConstructorUsedError;

  /// 郵便番号が有効であるか否か
  bool get isValidPostcode => throw _privateConstructorUsedError;

  /// 住所-都道府県
  String? get prefecture => throw _privateConstructorUsedError;
  ValidationError get prefectureCityValidationError =>
      throw _privateConstructorUsedError;

  /// 住所-市区町村
  String? get city => throw _privateConstructorUsedError;
  ValidationError get cityNameValidationError =>
      throw _privateConstructorUsedError;

  /// 住所-町名・番地・号・建物名
  String? get sectionNumberAndBuildingName =>
      throw _privateConstructorUsedError;
  ValidationError get streetAddressValidationError =>
      throw _privateConstructorUsedError;
  AppError? get fetchAddressError => throw _privateConstructorUsedError;

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityVerificationScreenStateCopyWith<IdentityVerificationScreenState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityVerificationScreenStateCopyWith<$Res> {
  factory $IdentityVerificationScreenStateCopyWith(
          IdentityVerificationScreenState value,
          $Res Function(IdentityVerificationScreenState) then) =
      _$IdentityVerificationScreenStateCopyWithImpl<$Res,
          IdentityVerificationScreenState>;
  @useResult
  $Res call(
      {IdentityVerificationInputState inputState,
      bool isRealTimeValidationMode,
      bool isRepresentative,
      bool isEkyc,
      String role,
      String? familyName,
      ValidationError familyNameValidationError,
      String? givenName,
      ValidationError givenNameValidationError,
      String? familyNameKana,
      ValidationError familyNameKanaValidationError,
      String? givenNameKana,
      ValidationError givenNameKanaValidationError,
      bool isNameEditable,
      String? familyNameAlphabetic,
      ValidationError familyNameAlphabeticValidationError,
      String? givenNameAlphabetic,
      ValidationError givenNameAlphabeticValidationError,
      String? position,
      ValidationError positionValidationError,
      String? birthDateYear,
      ValidationError birthDateYearValidationError,
      String? birthDateMonth,
      ValidationError birthDateMonthValidationError,
      String? birthDateDay,
      ValidationError birthDateDayValidationError,
      String? postcode,
      ValidationError postcodeValidationError,
      bool isValidPostcode,
      String? prefecture,
      ValidationError prefectureCityValidationError,
      String? city,
      ValidationError cityNameValidationError,
      String? sectionNumberAndBuildingName,
      ValidationError streetAddressValidationError,
      AppError? fetchAddressError});

  $IdentityVerificationInputStateCopyWith<$Res> get inputState;
  $ValidationErrorCopyWith<$Res> get familyNameValidationError;
  $ValidationErrorCopyWith<$Res> get givenNameValidationError;
  $ValidationErrorCopyWith<$Res> get familyNameKanaValidationError;
  $ValidationErrorCopyWith<$Res> get givenNameKanaValidationError;
  $ValidationErrorCopyWith<$Res> get familyNameAlphabeticValidationError;
  $ValidationErrorCopyWith<$Res> get givenNameAlphabeticValidationError;
  $ValidationErrorCopyWith<$Res> get positionValidationError;
  $ValidationErrorCopyWith<$Res> get birthDateYearValidationError;
  $ValidationErrorCopyWith<$Res> get birthDateMonthValidationError;
  $ValidationErrorCopyWith<$Res> get birthDateDayValidationError;
  $ValidationErrorCopyWith<$Res> get postcodeValidationError;
  $ValidationErrorCopyWith<$Res> get prefectureCityValidationError;
  $ValidationErrorCopyWith<$Res> get cityNameValidationError;
  $ValidationErrorCopyWith<$Res> get streetAddressValidationError;
  $AppErrorCopyWith<$Res>? get fetchAddressError;
}

/// @nodoc
class _$IdentityVerificationScreenStateCopyWithImpl<$Res,
        $Val extends IdentityVerificationScreenState>
    implements $IdentityVerificationScreenStateCopyWith<$Res> {
  _$IdentityVerificationScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inputState = null,
    Object? isRealTimeValidationMode = null,
    Object? isRepresentative = null,
    Object? isEkyc = null,
    Object? role = null,
    Object? familyName = freezed,
    Object? familyNameValidationError = null,
    Object? givenName = freezed,
    Object? givenNameValidationError = null,
    Object? familyNameKana = freezed,
    Object? familyNameKanaValidationError = null,
    Object? givenNameKana = freezed,
    Object? givenNameKanaValidationError = null,
    Object? isNameEditable = null,
    Object? familyNameAlphabetic = freezed,
    Object? familyNameAlphabeticValidationError = null,
    Object? givenNameAlphabetic = freezed,
    Object? givenNameAlphabeticValidationError = null,
    Object? position = freezed,
    Object? positionValidationError = null,
    Object? birthDateYear = freezed,
    Object? birthDateYearValidationError = null,
    Object? birthDateMonth = freezed,
    Object? birthDateMonthValidationError = null,
    Object? birthDateDay = freezed,
    Object? birthDateDayValidationError = null,
    Object? postcode = freezed,
    Object? postcodeValidationError = null,
    Object? isValidPostcode = null,
    Object? prefecture = freezed,
    Object? prefectureCityValidationError = null,
    Object? city = freezed,
    Object? cityNameValidationError = null,
    Object? sectionNumberAndBuildingName = freezed,
    Object? streetAddressValidationError = null,
    Object? fetchAddressError = freezed,
  }) {
    return _then(_value.copyWith(
      inputState: null == inputState
          ? _value.inputState
          : inputState // ignore: cast_nullable_to_non_nullable
              as IdentityVerificationInputState,
      isRealTimeValidationMode: null == isRealTimeValidationMode
          ? _value.isRealTimeValidationMode
          : isRealTimeValidationMode // ignore: cast_nullable_to_non_nullable
              as bool,
      isRepresentative: null == isRepresentative
          ? _value.isRepresentative
          : isRepresentative // ignore: cast_nullable_to_non_nullable
              as bool,
      isEkyc: null == isEkyc
          ? _value.isEkyc
          : isEkyc // ignore: cast_nullable_to_non_nullable
              as bool,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameValidationError: null == familyNameValidationError
          ? _value.familyNameValidationError
          : familyNameValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      givenName: freezed == givenName
          ? _value.givenName
          : givenName // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameValidationError: null == givenNameValidationError
          ? _value.givenNameValidationError
          : givenNameValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      familyNameKana: freezed == familyNameKana
          ? _value.familyNameKana
          : familyNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameKanaValidationError: null == familyNameKanaValidationError
          ? _value.familyNameKanaValidationError
          : familyNameKanaValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      givenNameKana: freezed == givenNameKana
          ? _value.givenNameKana
          : givenNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameKanaValidationError: null == givenNameKanaValidationError
          ? _value.givenNameKanaValidationError
          : givenNameKanaValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      isNameEditable: null == isNameEditable
          ? _value.isNameEditable
          : isNameEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      familyNameAlphabetic: freezed == familyNameAlphabetic
          ? _value.familyNameAlphabetic
          : familyNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameAlphabeticValidationError: null ==
              familyNameAlphabeticValidationError
          ? _value.familyNameAlphabeticValidationError
          : familyNameAlphabeticValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      givenNameAlphabetic: freezed == givenNameAlphabetic
          ? _value.givenNameAlphabetic
          : givenNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameAlphabeticValidationError: null ==
              givenNameAlphabeticValidationError
          ? _value.givenNameAlphabeticValidationError
          : givenNameAlphabeticValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as String?,
      positionValidationError: null == positionValidationError
          ? _value.positionValidationError
          : positionValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      birthDateYear: freezed == birthDateYear
          ? _value.birthDateYear
          : birthDateYear // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateYearValidationError: null == birthDateYearValidationError
          ? _value.birthDateYearValidationError
          : birthDateYearValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      birthDateMonth: freezed == birthDateMonth
          ? _value.birthDateMonth
          : birthDateMonth // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateMonthValidationError: null == birthDateMonthValidationError
          ? _value.birthDateMonthValidationError
          : birthDateMonthValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      birthDateDay: freezed == birthDateDay
          ? _value.birthDateDay
          : birthDateDay // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateDayValidationError: null == birthDateDayValidationError
          ? _value.birthDateDayValidationError
          : birthDateDayValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      postcode: freezed == postcode
          ? _value.postcode
          : postcode // ignore: cast_nullable_to_non_nullable
              as String?,
      postcodeValidationError: null == postcodeValidationError
          ? _value.postcodeValidationError
          : postcodeValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      isValidPostcode: null == isValidPostcode
          ? _value.isValidPostcode
          : isValidPostcode // ignore: cast_nullable_to_non_nullable
              as bool,
      prefecture: freezed == prefecture
          ? _value.prefecture
          : prefecture // ignore: cast_nullable_to_non_nullable
              as String?,
      prefectureCityValidationError: null == prefectureCityValidationError
          ? _value.prefectureCityValidationError
          : prefectureCityValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      cityNameValidationError: null == cityNameValidationError
          ? _value.cityNameValidationError
          : cityNameValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      sectionNumberAndBuildingName: freezed == sectionNumberAndBuildingName
          ? _value.sectionNumberAndBuildingName
          : sectionNumberAndBuildingName // ignore: cast_nullable_to_non_nullable
              as String?,
      streetAddressValidationError: null == streetAddressValidationError
          ? _value.streetAddressValidationError
          : streetAddressValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      fetchAddressError: freezed == fetchAddressError
          ? _value.fetchAddressError
          : fetchAddressError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $IdentityVerificationInputStateCopyWith<$Res> get inputState {
    return $IdentityVerificationInputStateCopyWith<$Res>(_value.inputState,
        (value) {
      return _then(_value.copyWith(inputState: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get familyNameValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.familyNameValidationError,
        (value) {
      return _then(_value.copyWith(familyNameValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get givenNameValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.givenNameValidationError,
        (value) {
      return _then(_value.copyWith(givenNameValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get familyNameKanaValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.familyNameKanaValidationError,
        (value) {
      return _then(
          _value.copyWith(familyNameKanaValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get givenNameKanaValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.givenNameKanaValidationError,
        (value) {
      return _then(
          _value.copyWith(givenNameKanaValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get familyNameAlphabeticValidationError {
    return $ValidationErrorCopyWith<$Res>(
        _value.familyNameAlphabeticValidationError, (value) {
      return _then(
          _value.copyWith(familyNameAlphabeticValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get givenNameAlphabeticValidationError {
    return $ValidationErrorCopyWith<$Res>(
        _value.givenNameAlphabeticValidationError, (value) {
      return _then(
          _value.copyWith(givenNameAlphabeticValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get positionValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.positionValidationError,
        (value) {
      return _then(_value.copyWith(positionValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get birthDateYearValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.birthDateYearValidationError,
        (value) {
      return _then(
          _value.copyWith(birthDateYearValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get birthDateMonthValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.birthDateMonthValidationError,
        (value) {
      return _then(
          _value.copyWith(birthDateMonthValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get birthDateDayValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.birthDateDayValidationError,
        (value) {
      return _then(_value.copyWith(birthDateDayValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get postcodeValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.postcodeValidationError,
        (value) {
      return _then(_value.copyWith(postcodeValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get prefectureCityValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.prefectureCityValidationError,
        (value) {
      return _then(
          _value.copyWith(prefectureCityValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get cityNameValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.cityNameValidationError,
        (value) {
      return _then(_value.copyWith(cityNameValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get streetAddressValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.streetAddressValidationError,
        (value) {
      return _then(
          _value.copyWith(streetAddressValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get fetchAddressError {
    if (_value.fetchAddressError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.fetchAddressError!, (value) {
      return _then(_value.copyWith(fetchAddressError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdentityVerificationScreenStateImplCopyWith<$Res>
    implements $IdentityVerificationScreenStateCopyWith<$Res> {
  factory _$$IdentityVerificationScreenStateImplCopyWith(
          _$IdentityVerificationScreenStateImpl value,
          $Res Function(_$IdentityVerificationScreenStateImpl) then) =
      __$$IdentityVerificationScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {IdentityVerificationInputState inputState,
      bool isRealTimeValidationMode,
      bool isRepresentative,
      bool isEkyc,
      String role,
      String? familyName,
      ValidationError familyNameValidationError,
      String? givenName,
      ValidationError givenNameValidationError,
      String? familyNameKana,
      ValidationError familyNameKanaValidationError,
      String? givenNameKana,
      ValidationError givenNameKanaValidationError,
      bool isNameEditable,
      String? familyNameAlphabetic,
      ValidationError familyNameAlphabeticValidationError,
      String? givenNameAlphabetic,
      ValidationError givenNameAlphabeticValidationError,
      String? position,
      ValidationError positionValidationError,
      String? birthDateYear,
      ValidationError birthDateYearValidationError,
      String? birthDateMonth,
      ValidationError birthDateMonthValidationError,
      String? birthDateDay,
      ValidationError birthDateDayValidationError,
      String? postcode,
      ValidationError postcodeValidationError,
      bool isValidPostcode,
      String? prefecture,
      ValidationError prefectureCityValidationError,
      String? city,
      ValidationError cityNameValidationError,
      String? sectionNumberAndBuildingName,
      ValidationError streetAddressValidationError,
      AppError? fetchAddressError});

  @override
  $IdentityVerificationInputStateCopyWith<$Res> get inputState;
  @override
  $ValidationErrorCopyWith<$Res> get familyNameValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get givenNameValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get familyNameKanaValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get givenNameKanaValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get familyNameAlphabeticValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get givenNameAlphabeticValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get positionValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get birthDateYearValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get birthDateMonthValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get birthDateDayValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get postcodeValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get prefectureCityValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get cityNameValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get streetAddressValidationError;
  @override
  $AppErrorCopyWith<$Res>? get fetchAddressError;
}

/// @nodoc
class __$$IdentityVerificationScreenStateImplCopyWithImpl<$Res>
    extends _$IdentityVerificationScreenStateCopyWithImpl<$Res,
        _$IdentityVerificationScreenStateImpl>
    implements _$$IdentityVerificationScreenStateImplCopyWith<$Res> {
  __$$IdentityVerificationScreenStateImplCopyWithImpl(
      _$IdentityVerificationScreenStateImpl _value,
      $Res Function(_$IdentityVerificationScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inputState = null,
    Object? isRealTimeValidationMode = null,
    Object? isRepresentative = null,
    Object? isEkyc = null,
    Object? role = null,
    Object? familyName = freezed,
    Object? familyNameValidationError = null,
    Object? givenName = freezed,
    Object? givenNameValidationError = null,
    Object? familyNameKana = freezed,
    Object? familyNameKanaValidationError = null,
    Object? givenNameKana = freezed,
    Object? givenNameKanaValidationError = null,
    Object? isNameEditable = null,
    Object? familyNameAlphabetic = freezed,
    Object? familyNameAlphabeticValidationError = null,
    Object? givenNameAlphabetic = freezed,
    Object? givenNameAlphabeticValidationError = null,
    Object? position = freezed,
    Object? positionValidationError = null,
    Object? birthDateYear = freezed,
    Object? birthDateYearValidationError = null,
    Object? birthDateMonth = freezed,
    Object? birthDateMonthValidationError = null,
    Object? birthDateDay = freezed,
    Object? birthDateDayValidationError = null,
    Object? postcode = freezed,
    Object? postcodeValidationError = null,
    Object? isValidPostcode = null,
    Object? prefecture = freezed,
    Object? prefectureCityValidationError = null,
    Object? city = freezed,
    Object? cityNameValidationError = null,
    Object? sectionNumberAndBuildingName = freezed,
    Object? streetAddressValidationError = null,
    Object? fetchAddressError = freezed,
  }) {
    return _then(_$IdentityVerificationScreenStateImpl(
      inputState: null == inputState
          ? _value.inputState
          : inputState // ignore: cast_nullable_to_non_nullable
              as IdentityVerificationInputState,
      isRealTimeValidationMode: null == isRealTimeValidationMode
          ? _value.isRealTimeValidationMode
          : isRealTimeValidationMode // ignore: cast_nullable_to_non_nullable
              as bool,
      isRepresentative: null == isRepresentative
          ? _value.isRepresentative
          : isRepresentative // ignore: cast_nullable_to_non_nullable
              as bool,
      isEkyc: null == isEkyc
          ? _value.isEkyc
          : isEkyc // ignore: cast_nullable_to_non_nullable
              as bool,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String,
      familyName: freezed == familyName
          ? _value.familyName
          : familyName // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameValidationError: null == familyNameValidationError
          ? _value.familyNameValidationError
          : familyNameValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      givenName: freezed == givenName
          ? _value.givenName
          : givenName // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameValidationError: null == givenNameValidationError
          ? _value.givenNameValidationError
          : givenNameValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      familyNameKana: freezed == familyNameKana
          ? _value.familyNameKana
          : familyNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameKanaValidationError: null == familyNameKanaValidationError
          ? _value.familyNameKanaValidationError
          : familyNameKanaValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      givenNameKana: freezed == givenNameKana
          ? _value.givenNameKana
          : givenNameKana // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameKanaValidationError: null == givenNameKanaValidationError
          ? _value.givenNameKanaValidationError
          : givenNameKanaValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      isNameEditable: null == isNameEditable
          ? _value.isNameEditable
          : isNameEditable // ignore: cast_nullable_to_non_nullable
              as bool,
      familyNameAlphabetic: freezed == familyNameAlphabetic
          ? _value.familyNameAlphabetic
          : familyNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      familyNameAlphabeticValidationError: null ==
              familyNameAlphabeticValidationError
          ? _value.familyNameAlphabeticValidationError
          : familyNameAlphabeticValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      givenNameAlphabetic: freezed == givenNameAlphabetic
          ? _value.givenNameAlphabetic
          : givenNameAlphabetic // ignore: cast_nullable_to_non_nullable
              as String?,
      givenNameAlphabeticValidationError: null ==
              givenNameAlphabeticValidationError
          ? _value.givenNameAlphabeticValidationError
          : givenNameAlphabeticValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      position: freezed == position
          ? _value.position
          : position // ignore: cast_nullable_to_non_nullable
              as String?,
      positionValidationError: null == positionValidationError
          ? _value.positionValidationError
          : positionValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      birthDateYear: freezed == birthDateYear
          ? _value.birthDateYear
          : birthDateYear // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateYearValidationError: null == birthDateYearValidationError
          ? _value.birthDateYearValidationError
          : birthDateYearValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      birthDateMonth: freezed == birthDateMonth
          ? _value.birthDateMonth
          : birthDateMonth // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateMonthValidationError: null == birthDateMonthValidationError
          ? _value.birthDateMonthValidationError
          : birthDateMonthValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      birthDateDay: freezed == birthDateDay
          ? _value.birthDateDay
          : birthDateDay // ignore: cast_nullable_to_non_nullable
              as String?,
      birthDateDayValidationError: null == birthDateDayValidationError
          ? _value.birthDateDayValidationError
          : birthDateDayValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      postcode: freezed == postcode
          ? _value.postcode
          : postcode // ignore: cast_nullable_to_non_nullable
              as String?,
      postcodeValidationError: null == postcodeValidationError
          ? _value.postcodeValidationError
          : postcodeValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      isValidPostcode: null == isValidPostcode
          ? _value.isValidPostcode
          : isValidPostcode // ignore: cast_nullable_to_non_nullable
              as bool,
      prefecture: freezed == prefecture
          ? _value.prefecture
          : prefecture // ignore: cast_nullable_to_non_nullable
              as String?,
      prefectureCityValidationError: null == prefectureCityValidationError
          ? _value.prefectureCityValidationError
          : prefectureCityValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      city: freezed == city
          ? _value.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      cityNameValidationError: null == cityNameValidationError
          ? _value.cityNameValidationError
          : cityNameValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      sectionNumberAndBuildingName: freezed == sectionNumberAndBuildingName
          ? _value.sectionNumberAndBuildingName
          : sectionNumberAndBuildingName // ignore: cast_nullable_to_non_nullable
              as String?,
      streetAddressValidationError: null == streetAddressValidationError
          ? _value.streetAddressValidationError
          : streetAddressValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      fetchAddressError: freezed == fetchAddressError
          ? _value.fetchAddressError
          : fetchAddressError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$IdentityVerificationScreenStateImpl
    implements _IdentityVerificationScreenState {
  const _$IdentityVerificationScreenStateImpl(
      {this.inputState = const IdentityVerificationInputState.initial(),
      this.isRealTimeValidationMode = false,
      required this.isRepresentative,
      this.isEkyc = false,
      this.role = '',
      this.familyName,
      this.familyNameValidationError = const ValidationError.none(),
      this.givenName,
      this.givenNameValidationError = const ValidationError.none(),
      this.familyNameKana,
      this.familyNameKanaValidationError = const ValidationError.none(),
      this.givenNameKana,
      this.givenNameKanaValidationError = const ValidationError.none(),
      this.isNameEditable = false,
      this.familyNameAlphabetic,
      this.familyNameAlphabeticValidationError = const ValidationError.none(),
      this.givenNameAlphabetic,
      this.givenNameAlphabeticValidationError = const ValidationError.none(),
      this.position,
      this.positionValidationError = const ValidationError.none(),
      this.birthDateYear,
      this.birthDateYearValidationError = const ValidationError.none(),
      this.birthDateMonth,
      this.birthDateMonthValidationError = const ValidationError.none(),
      this.birthDateDay,
      this.birthDateDayValidationError = const ValidationError.none(),
      this.postcode,
      this.postcodeValidationError = const ValidationError.none(),
      this.isValidPostcode = false,
      this.prefecture,
      this.prefectureCityValidationError = const ValidationError.none(),
      this.city,
      this.cityNameValidationError = const ValidationError.none(),
      this.sectionNumberAndBuildingName,
      this.streetAddressValidationError = const ValidationError.none(),
      this.fetchAddressError});

  @override
  @JsonKey()
  final IdentityVerificationInputState inputState;

  /// 項目編集時にバリデーションチェックを実行するかどうか
  @override
  @JsonKey()
  final bool isRealTimeValidationMode;

  /// 代表者確認
  @override
  final bool isRepresentative;

  /// eKYCか否か
  @override
  @JsonKey()
  final bool isEkyc;

  /// role(代表者/代理人/実質的支配者)
  @override
  @JsonKey()
  final String role;

  /// 氏名-姓
  @override
  final String? familyName;
  @override
  @JsonKey()
  final ValidationError familyNameValidationError;

  /// 氏名-名
  @override
  final String? givenName;
  @override
  @JsonKey()
  final ValidationError givenNameValidationError;

  /// 氏名-セイ
  @override
  final String? familyNameKana;
  @override
  @JsonKey()
  final ValidationError familyNameKanaValidationError;

  /// 氏名-メイ
  @override
  final String? givenNameKana;
  @override
  @JsonKey()
  final ValidationError givenNameKanaValidationError;

  /// 英字の名前が編集可能であるか否か
  @override
  @JsonKey()
  final bool isNameEditable;

  /// 氏名-Last Name
  @override
  final String? familyNameAlphabetic;
  @override
  @JsonKey()
  final ValidationError familyNameAlphabeticValidationError;

  /// 氏名-First Name
  @override
  final String? givenNameAlphabetic;
  @override
  @JsonKey()
  final ValidationError givenNameAlphabeticValidationError;

  /// 役職名
  @override
  final String? position;
  @override
  @JsonKey()
  final ValidationError positionValidationError;

  /// 生年月日（年）
  @override
  final String? birthDateYear;
  @override
  @JsonKey()
  final ValidationError birthDateYearValidationError;

  /// 生年月日（月）
  @override
  final String? birthDateMonth;
  @override
  @JsonKey()
  final ValidationError birthDateMonthValidationError;

  /// 生年月日（日）
  @override
  final String? birthDateDay;
  @override
  @JsonKey()
  final ValidationError birthDateDayValidationError;

  /// 郵便番号
  @override
  final String? postcode;
  @override
  @JsonKey()
  final ValidationError postcodeValidationError;

  /// 郵便番号が有効であるか否か
  @override
  @JsonKey()
  final bool isValidPostcode;

  /// 住所-都道府県
  @override
  final String? prefecture;
  @override
  @JsonKey()
  final ValidationError prefectureCityValidationError;

  /// 住所-市区町村
  @override
  final String? city;
  @override
  @JsonKey()
  final ValidationError cityNameValidationError;

  /// 住所-町名・番地・号・建物名
  @override
  final String? sectionNumberAndBuildingName;
  @override
  @JsonKey()
  final ValidationError streetAddressValidationError;
  @override
  final AppError? fetchAddressError;

  @override
  String toString() {
    return 'IdentityVerificationScreenState(inputState: $inputState, isRealTimeValidationMode: $isRealTimeValidationMode, isRepresentative: $isRepresentative, isEkyc: $isEkyc, role: $role, familyName: $familyName, familyNameValidationError: $familyNameValidationError, givenName: $givenName, givenNameValidationError: $givenNameValidationError, familyNameKana: $familyNameKana, familyNameKanaValidationError: $familyNameKanaValidationError, givenNameKana: $givenNameKana, givenNameKanaValidationError: $givenNameKanaValidationError, isNameEditable: $isNameEditable, familyNameAlphabetic: $familyNameAlphabetic, familyNameAlphabeticValidationError: $familyNameAlphabeticValidationError, givenNameAlphabetic: $givenNameAlphabetic, givenNameAlphabeticValidationError: $givenNameAlphabeticValidationError, position: $position, positionValidationError: $positionValidationError, birthDateYear: $birthDateYear, birthDateYearValidationError: $birthDateYearValidationError, birthDateMonth: $birthDateMonth, birthDateMonthValidationError: $birthDateMonthValidationError, birthDateDay: $birthDateDay, birthDateDayValidationError: $birthDateDayValidationError, postcode: $postcode, postcodeValidationError: $postcodeValidationError, isValidPostcode: $isValidPostcode, prefecture: $prefecture, prefectureCityValidationError: $prefectureCityValidationError, city: $city, cityNameValidationError: $cityNameValidationError, sectionNumberAndBuildingName: $sectionNumberAndBuildingName, streetAddressValidationError: $streetAddressValidationError, fetchAddressError: $fetchAddressError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityVerificationScreenStateImpl &&
            (identical(other.inputState, inputState) ||
                other.inputState == inputState) &&
            (identical(other.isRealTimeValidationMode, isRealTimeValidationMode) ||
                other.isRealTimeValidationMode == isRealTimeValidationMode) &&
            (identical(other.isRepresentative, isRepresentative) ||
                other.isRepresentative == isRepresentative) &&
            (identical(other.isEkyc, isEkyc) || other.isEkyc == isEkyc) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.familyName, familyName) ||
                other.familyName == familyName) &&
            (identical(other.familyNameValidationError, familyNameValidationError) ||
                other.familyNameValidationError == familyNameValidationError) &&
            (identical(other.givenName, givenName) ||
                other.givenName == givenName) &&
            (identical(other.givenNameValidationError, givenNameValidationError) ||
                other.givenNameValidationError == givenNameValidationError) &&
            (identical(other.familyNameKana, familyNameKana) ||
                other.familyNameKana == familyNameKana) &&
            (identical(other.familyNameKanaValidationError, familyNameKanaValidationError) ||
                other.familyNameKanaValidationError ==
                    familyNameKanaValidationError) &&
            (identical(other.givenNameKana, givenNameKana) ||
                other.givenNameKana == givenNameKana) &&
            (identical(other.givenNameKanaValidationError, givenNameKanaValidationError) ||
                other.givenNameKanaValidationError ==
                    givenNameKanaValidationError) &&
            (identical(other.isNameEditable, isNameEditable) ||
                other.isNameEditable == isNameEditable) &&
            (identical(other.familyNameAlphabetic, familyNameAlphabetic) ||
                other.familyNameAlphabetic == familyNameAlphabetic) &&
            (identical(other.familyNameAlphabeticValidationError, familyNameAlphabeticValidationError) ||
                other.familyNameAlphabeticValidationError ==
                    familyNameAlphabeticValidationError) &&
            (identical(other.givenNameAlphabetic, givenNameAlphabetic) ||
                other.givenNameAlphabetic == givenNameAlphabetic) &&
            (identical(other.givenNameAlphabeticValidationError, givenNameAlphabeticValidationError) ||
                other.givenNameAlphabeticValidationError ==
                    givenNameAlphabeticValidationError) &&
            (identical(other.position, position) ||
                other.position == position) &&
            (identical(other.positionValidationError, positionValidationError) ||
                other.positionValidationError == positionValidationError) &&
            (identical(other.birthDateYear, birthDateYear) ||
                other.birthDateYear == birthDateYear) &&
            (identical(other.birthDateYearValidationError, birthDateYearValidationError) || other.birthDateYearValidationError == birthDateYearValidationError) &&
            (identical(other.birthDateMonth, birthDateMonth) || other.birthDateMonth == birthDateMonth) &&
            (identical(other.birthDateMonthValidationError, birthDateMonthValidationError) || other.birthDateMonthValidationError == birthDateMonthValidationError) &&
            (identical(other.birthDateDay, birthDateDay) || other.birthDateDay == birthDateDay) &&
            (identical(other.birthDateDayValidationError, birthDateDayValidationError) || other.birthDateDayValidationError == birthDateDayValidationError) &&
            (identical(other.postcode, postcode) || other.postcode == postcode) &&
            (identical(other.postcodeValidationError, postcodeValidationError) || other.postcodeValidationError == postcodeValidationError) &&
            (identical(other.isValidPostcode, isValidPostcode) || other.isValidPostcode == isValidPostcode) &&
            (identical(other.prefecture, prefecture) || other.prefecture == prefecture) &&
            (identical(other.prefectureCityValidationError, prefectureCityValidationError) || other.prefectureCityValidationError == prefectureCityValidationError) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.cityNameValidationError, cityNameValidationError) || other.cityNameValidationError == cityNameValidationError) &&
            (identical(other.sectionNumberAndBuildingName, sectionNumberAndBuildingName) || other.sectionNumberAndBuildingName == sectionNumberAndBuildingName) &&
            (identical(other.streetAddressValidationError, streetAddressValidationError) || other.streetAddressValidationError == streetAddressValidationError) &&
            (identical(other.fetchAddressError, fetchAddressError) || other.fetchAddressError == fetchAddressError));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        inputState,
        isRealTimeValidationMode,
        isRepresentative,
        isEkyc,
        role,
        familyName,
        familyNameValidationError,
        givenName,
        givenNameValidationError,
        familyNameKana,
        familyNameKanaValidationError,
        givenNameKana,
        givenNameKanaValidationError,
        isNameEditable,
        familyNameAlphabetic,
        familyNameAlphabeticValidationError,
        givenNameAlphabetic,
        givenNameAlphabeticValidationError,
        position,
        positionValidationError,
        birthDateYear,
        birthDateYearValidationError,
        birthDateMonth,
        birthDateMonthValidationError,
        birthDateDay,
        birthDateDayValidationError,
        postcode,
        postcodeValidationError,
        isValidPostcode,
        prefecture,
        prefectureCityValidationError,
        city,
        cityNameValidationError,
        sectionNumberAndBuildingName,
        streetAddressValidationError,
        fetchAddressError
      ]);

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityVerificationScreenStateImplCopyWith<
          _$IdentityVerificationScreenStateImpl>
      get copyWith => __$$IdentityVerificationScreenStateImplCopyWithImpl<
          _$IdentityVerificationScreenStateImpl>(this, _$identity);
}

abstract class _IdentityVerificationScreenState
    implements IdentityVerificationScreenState {
  const factory _IdentityVerificationScreenState(
          {final IdentityVerificationInputState inputState,
          final bool isRealTimeValidationMode,
          required final bool isRepresentative,
          final bool isEkyc,
          final String role,
          final String? familyName,
          final ValidationError familyNameValidationError,
          final String? givenName,
          final ValidationError givenNameValidationError,
          final String? familyNameKana,
          final ValidationError familyNameKanaValidationError,
          final String? givenNameKana,
          final ValidationError givenNameKanaValidationError,
          final bool isNameEditable,
          final String? familyNameAlphabetic,
          final ValidationError familyNameAlphabeticValidationError,
          final String? givenNameAlphabetic,
          final ValidationError givenNameAlphabeticValidationError,
          final String? position,
          final ValidationError positionValidationError,
          final String? birthDateYear,
          final ValidationError birthDateYearValidationError,
          final String? birthDateMonth,
          final ValidationError birthDateMonthValidationError,
          final String? birthDateDay,
          final ValidationError birthDateDayValidationError,
          final String? postcode,
          final ValidationError postcodeValidationError,
          final bool isValidPostcode,
          final String? prefecture,
          final ValidationError prefectureCityValidationError,
          final String? city,
          final ValidationError cityNameValidationError,
          final String? sectionNumberAndBuildingName,
          final ValidationError streetAddressValidationError,
          final AppError? fetchAddressError}) =
      _$IdentityVerificationScreenStateImpl;

  @override
  IdentityVerificationInputState get inputState;

  /// 項目編集時にバリデーションチェックを実行するかどうか
  @override
  bool get isRealTimeValidationMode;

  /// 代表者確認
  @override
  bool get isRepresentative;

  /// eKYCか否か
  @override
  bool get isEkyc;

  /// role(代表者/代理人/実質的支配者)
  @override
  String get role;

  /// 氏名-姓
  @override
  String? get familyName;
  @override
  ValidationError get familyNameValidationError;

  /// 氏名-名
  @override
  String? get givenName;
  @override
  ValidationError get givenNameValidationError;

  /// 氏名-セイ
  @override
  String? get familyNameKana;
  @override
  ValidationError get familyNameKanaValidationError;

  /// 氏名-メイ
  @override
  String? get givenNameKana;
  @override
  ValidationError get givenNameKanaValidationError;

  /// 英字の名前が編集可能であるか否か
  @override
  bool get isNameEditable;

  /// 氏名-Last Name
  @override
  String? get familyNameAlphabetic;
  @override
  ValidationError get familyNameAlphabeticValidationError;

  /// 氏名-First Name
  @override
  String? get givenNameAlphabetic;
  @override
  ValidationError get givenNameAlphabeticValidationError;

  /// 役職名
  @override
  String? get position;
  @override
  ValidationError get positionValidationError;

  /// 生年月日（年）
  @override
  String? get birthDateYear;
  @override
  ValidationError get birthDateYearValidationError;

  /// 生年月日（月）
  @override
  String? get birthDateMonth;
  @override
  ValidationError get birthDateMonthValidationError;

  /// 生年月日（日）
  @override
  String? get birthDateDay;
  @override
  ValidationError get birthDateDayValidationError;

  /// 郵便番号
  @override
  String? get postcode;
  @override
  ValidationError get postcodeValidationError;

  /// 郵便番号が有効であるか否か
  @override
  bool get isValidPostcode;

  /// 住所-都道府県
  @override
  String? get prefecture;
  @override
  ValidationError get prefectureCityValidationError;

  /// 住所-市区町村
  @override
  String? get city;
  @override
  ValidationError get cityNameValidationError;

  /// 住所-町名・番地・号・建物名
  @override
  String? get sectionNumberAndBuildingName;
  @override
  ValidationError get streetAddressValidationError;
  @override
  AppError? get fetchAddressError;

  /// Create a copy of IdentityVerificationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityVerificationScreenStateImplCopyWith<
          _$IdentityVerificationScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
