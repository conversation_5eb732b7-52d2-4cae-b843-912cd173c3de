import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/interfaces/identity_verification_repository.dart';
import 'package:dtp_app/business_logics/mixin/input_validator_mixin.dart';
import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/models/identity_verification_address/identity_verification_address.dart';
import 'package:dtp_app/models/identity_verification_info/identity_verification_info.dart';
import 'package:dtp_app/repositories/identity_verification/identity_verification_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/string_ext.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'identity_verification_screen_notifier.freezed.dart';

@freezed
sealed class IdentityVerificationInputState
    with _$IdentityVerificationInputState {
  const factory IdentityVerificationInputState.initial() = _Initial;

  const factory IdentityVerificationInputState.loading() = _Loading;

  const factory IdentityVerificationInputState.sendSuccess() = _SendSuccess;

  const factory IdentityVerificationInputState.sendFailure(AppError error) =
      _SendFailure;
}

@freezed
sealed class IdentityVerificationScreenState
    with _$IdentityVerificationScreenState {
  const factory IdentityVerificationScreenState({
    @Default(
      IdentityVerificationInputState.initial(),
    )
    IdentityVerificationInputState inputState,

    /// 項目編集時にバリデーションチェックを実行するかどうか
    @Default(false) bool isRealTimeValidationMode,

    /// 代表者確認
    required bool isRepresentative,

    /// eKYCか否か
    @Default(false) bool isEkyc,

    /// role(代表者/代理人/実質的支配者)
    @Default('') String role,

    /// 氏名-姓
    String? familyName,
    @Default(
      ValidationError.none(),
    )
    ValidationError familyNameValidationError,

    /// 氏名-名
    String? givenName,
    @Default(
      ValidationError.none(),
    )
    ValidationError givenNameValidationError,

    /// 氏名-セイ
    String? familyNameKana,
    @Default(
      ValidationError.none(),
    )
    ValidationError familyNameKanaValidationError,

    /// 氏名-メイ
    String? givenNameKana,
    @Default(
      ValidationError.none(),
    )
    ValidationError givenNameKanaValidationError,

    /// 英字の名前が編集可能であるか否か
    @Default(false) bool isNameEditable,

    /// 氏名-Last Name
    String? familyNameAlphabetic,
    @Default(ValidationError.none())
    ValidationError familyNameAlphabeticValidationError,

    /// 氏名-First Name
    String? givenNameAlphabetic,
    @Default(ValidationError.none())
    ValidationError givenNameAlphabeticValidationError,

    /// 役職名
    String? position,
    @Default(ValidationError.none()) ValidationError positionValidationError,

    /// 生年月日（年）
    String? birthDateYear,
    @Default(ValidationError.none())
    ValidationError birthDateYearValidationError,

    /// 生年月日（月）
    String? birthDateMonth,
    @Default(ValidationError.none())
    ValidationError birthDateMonthValidationError,

    /// 生年月日（日）
    String? birthDateDay,
    @Default(ValidationError.none())
    ValidationError birthDateDayValidationError,

    /// 郵便番号
    String? postcode,
    @Default(ValidationError.none()) ValidationError postcodeValidationError,

    /// 郵便番号が有効であるか否か
    @Default(false) bool isValidPostcode,

    /// 住所-都道府県
    String? prefecture,
    @Default(
      ValidationError.none(),
    )
    ValidationError prefectureCityValidationError,

    /// 住所-市区町村
    String? city,
    @Default(ValidationError.none()) ValidationError cityNameValidationError,

    /// 住所-町名・番地・号・建物名
    String? sectionNumberAndBuildingName,
    @Default(ValidationError.none())
    ValidationError streetAddressValidationError,
    AppError? fetchAddressError,
  }) = _IdentityVerificationScreenState;
}

extension IdentityVerificationScreenStateExt
    on IdentityVerificationScreenState {
  /// 名前入力画面の必須項目がすべて入力済みであればtrueを返す
  bool get requiredNameFormIsNotNullOrEmpty {
    return isEkyc
        ? familyName.isNotNullOrEmpty &&
            givenName.isNotNullOrEmpty &&
            familyNameKana.isNotNullOrEmpty &&
            givenNameKana.isNotNullOrEmpty
        : familyNameKana.isNotNullOrEmpty && givenNameKana.isNotNullOrEmpty;
  }

  /// 名前入力画面のバリデーションチェック
  bool get hasNameValidationError {
    // エラーがある場合には次へボタンを実行しない
    return isEkyc
        ? familyNameValidationError.hasError ||
            givenNameValidationError.hasError ||
            familyNameKanaValidationError.hasError ||
            givenNameKanaValidationError.hasError ||
            familyNameAlphabeticValidationError.hasError ||
            givenNameAlphabeticValidationError.hasError
        : familyNameKanaValidationError.hasError ||
            givenNameKanaValidationError.hasError ||
            familyNameAlphabeticValidationError.hasError ||
            givenNameAlphabeticValidationError.hasError;
  }

  /// 役職入力画面の必須項目がすべて入力済みであればtrueを返す
  bool get requiredPositionIsNotNullOrEmpty {
    return position.isNotNullOrEmpty;
  }

  /// 役職入力画面のバリデーションチェック
  bool get hasPositionValidationError {
    // エラーがある場合には次へボタンを実行しない
    return positionValidationError.hasError;
  }

  /// 生年月日入力画面の必須項目がすべて入力済みであればtrueを返す
  bool get requiredBirthDateIsNotNullOrEmpty {
    return birthDateYear.isNotNullOrEmpty &&
        birthDateMonth.isNotNullOrEmpty &&
        birthDateDay.isNotNullOrEmpty;
  }

  /// 生年月日入力画面のバリデーションチェック
  bool get hasBirthDateValidationError {
    // エラーがある場合には次へボタンを実行しない
    return birthDateYearValidationError.hasError ||
        birthDateMonthValidationError.hasError ||
        birthDateDayValidationError.hasError;
  }

  /// 自宅住所画面の必須項目がすべて入力済みであればtrueを返す
  bool get requiredAddressIsNotNullOrEmpty {
    return postcode.isNotNullOrEmpty &&
        prefecture.isNotNullOrEmpty &&
        city.isNotNullOrEmpty &&
        sectionNumberAndBuildingName.isNotNullOrEmpty;
  }

  /// 郵便番号入力画面の必須項目がすべて入力済みであればtrueを返す
  bool get requiredPostCodeIsNotNullOrEmpty {
    return postcode.isNotNullOrEmpty;
  }

  /// 自宅住所入力画面のバリデーションチェック
  bool get hasAddressValidationError {
    // エラーがある場合には次へボタンを実行しない
    return postcodeValidationError.hasError ||
        prefectureCityValidationError.hasError ||
        cityNameValidationError.hasError ||
        streetAddressValidationError.hasError;
  }
}

final identityVerificationScreenProvider = StateNotifierProvider.autoDispose<
    IdentityVerificationScreenNotifier, IdentityVerificationScreenState>((ref) {
  // 代表者かどうか
  final isRepresentativeSelected = ref
          .read(identityReferenceNumberScreenProvider)
          .openAccountAppTemp
          ?.userType ==
      'REPRESENTATIVE';

  return IdentityVerificationScreenNotifier(
    isRepresentativeSelected,
    ref.read(identityVerificationRepositoryProvider),
    ref.read(secureStorageManagerProvider),
    ref.read(mdManagerProvider),
  );
});

class IdentityVerificationScreenNotifier
    extends StateNotifier<IdentityVerificationScreenState>
    with InputValidatorMixin {
  IdentityVerificationScreenNotifier(
    bool isRepresentative,
    this._repository,
    this.secureStorageManager,
    this.memoryDataManager,
  ) : super(
          IdentityVerificationScreenState(
            isRepresentative: isRepresentative,
          ),
        );

  final IdentityVerificationRepository _repository;
  final SecureStorageManager secureStorageManager;
  final MemoryDataManager memoryDataManager;
  final maxLengthAlphabeticName = 34;
  final maxLengthPosition = 30;
  String? _cachedPostcode;

  /// リアルタイムバリデーション状態かどうかでバリデーションチェックの挙動を変えるための関数
  ValidationError _realTimeValidation(ValidationError validationError) {
    if (state.isRealTimeValidationMode) {
      return validationError.maybeWhen(
        // リアルタイムバリデーションでは必須入力エラーを判断しない
        required: (_) => const ValidationError.none(),
        orElse: () => validationError,
      );
    } else {
      // リアルタイムバリデーション状態でないならば、エラー表示しない
      return const ValidationError.none();
    }
  }

  /// 氏名-姓を更新する
  void updateFamilyName(String? familyName) {
    state = state.copyWith(
      familyName: familyName,
      familyNameValidationError:
          _realTimeValidation(validateFamilyName(familyName)),
    );
  }

  /// 氏名-名を更新する
  void updateGivenName(String? givenName) {
    state = state.copyWith(
      givenName: givenName,
      givenNameValidationError:
          _realTimeValidation(validateGivenName(givenName)),
    );
  }

  /// 氏名-セイを更新する
  void updateFamilyNameKana(String? familyNameKana) {
    state = state.copyWith(
      familyNameKana: familyNameKana,
      familyNameKanaValidationError:
          _realTimeValidation(validateFamilyNameKana(familyNameKana)),
    );
  }

  /// 氏名-メイを更新する
  void updateGivenNameKana(String? givenNameKana) {
    state = state.copyWith(
      givenNameKana: givenNameKana,
      givenNameKanaValidationError:
          _realTimeValidation(validateGivenNameKana(givenNameKana)),
    );
  }

  /// 英字名編集チェックボックスを更新する
  void updateToggleEditable() {
    state = state.copyWith(
      isNameEditable: !state.isNameEditable,
    );
  }

  /// 氏名-Last Nameを更新する
  void updateFamilyNameAlphabetic(String? familyNameAlphabetic) {
    state = state.copyWith(
      familyNameAlphabetic: familyNameAlphabetic,
      familyNameAlphabeticValidationError: _realTimeValidation(
        validateFamilyNameAlphabetic(familyNameAlphabetic),
      ),
    );
  }

  /// 氏名-First Nameを更新する
  void updateGivenNameAlphabetic(String? givenNameAlphabetic) {
    state = state.copyWith(
      givenNameAlphabetic: givenNameAlphabetic,
      givenNameAlphabeticValidationError:
          _realTimeValidation(validateGivenNameAlphabetic(givenNameAlphabetic)),
    );
  }

  /// 役職名を更新する
  void updatePosition(String? position) {
    state = state.copyWith(
      position: position,
      positionValidationError: _realTimeValidation(validatePosition(position)),
    );
  }

  /// 生年月日（年）を更新する
  void updateBirthDateYear(String? birthDateYear) {
    state = state.copyWith(
      birthDateYear: birthDateYear,
      birthDateYearValidationError:
          _realTimeValidation(validateBirthDateYear(birthDateYear)),
    );
  }

  /// 生年月日（月）を更新する
  void updateBirthDateMonth(String? birthDateMonth) {
    state = state.copyWith(
      birthDateMonth: birthDateMonth,
      birthDateMonthValidationError:
          _realTimeValidation(validateBirthDateMonth(birthDateMonth)),
    );
  }

  /// 生年月日（日）を更新する
  void updateBirthDateDay(String? birthDateDay) {
    state = state.copyWith(
      birthDateDay: birthDateDay,
      birthDateDayValidationError:
          _realTimeValidation(validateBirthDateDay(birthDateDay)),
    );
  }

  /// 郵便番号を更新する
  void updatePostcode(String? postcode, bool isValidPostcode) {
    state = state.copyWith(
      postcode: postcode,
      isValidPostcode: isValidPostcode,
      postcodeValidationError: _realTimeValidation(
        validatePostcode(postcode, _cachedPostcode, isValidPostcode),
      ),
    );
  }

  /// 住所-都道府県を更新する
  void updatePrefecture(String? prefecture) {
    state = state.copyWith(
      prefecture: prefecture,
      prefectureCityValidationError:
          _realTimeValidation(validatePrefecture(prefecture)),
    );
  }

  /// 住所-市区町村を更新する
  void updateCityName(String? city) {
    state = state.copyWith(
      city: city,
      cityNameValidationError: _realTimeValidation(
        validateCityName(state.prefecture, city),
      ),
    );
  }

  /// 住所-町名・番地・号・建物名を更新する
  void updateStreetAddress(String? sectionNumberAndBuildingName) {
    state = state.copyWith(
      sectionNumberAndBuildingName: sectionNumberAndBuildingName,
      streetAddressValidationError: _realTimeValidation(
        validateStreetAddress(
          state.prefecture,
          state.city,
          sectionNumberAndBuildingName,
        ),
      ),
    );
  }

  /// 画面POP時にstateを初期状態に変更(名前入力画面)
  void onPopNameScreen() {
    state = state.copyWith(
      isRealTimeValidationMode: false,
      isNameEditable: false,
      givenName: null,
      familyName: null,
      givenNameKana: null,
      familyNameKana: null,
      familyNameAlphabetic: null,
      givenNameAlphabetic: null,
      givenNameValidationError: const ValidationError.none(),
      familyNameValidationError: const ValidationError.none(),
      familyNameAlphabeticValidationError: const ValidationError.none(),
      givenNameAlphabeticValidationError: const ValidationError.none(),
      givenNameKanaValidationError: const ValidationError.none(),
      familyNameKanaValidationError: const ValidationError.none(),
    );
  }

  /// 画面POP時にstateを初期状態に変更(役職名入力画面)
  void onPopPositionScreen() {
    state = state.copyWith(
      isRealTimeValidationMode: false,
      position: null,
      positionValidationError: const ValidationError.none(),
    );
  }

  /// 画面POP時にstateを初期状態に変更(生年月日入力画面)
  void onPopDateOfBirthScreen() {
    state = state.copyWith(
      isRealTimeValidationMode: false,
      birthDateYear: null,
      birthDateMonth: null,
      birthDateDay: null,
      birthDateYearValidationError: const ValidationError.none(),
      birthDateMonthValidationError: const ValidationError.none(),
      birthDateDayValidationError: const ValidationError.none(),
    );
  }

  /// 画面POP時にstateを初期状態に変更(住所入力画面)
  void onPopAddressScreen() {
    state = state.copyWith(
      isRealTimeValidationMode: false,
      postcode: null,
      isValidPostcode: false,
      prefecture: null,
      city: null,
      sectionNumberAndBuildingName: null,
      postcodeValidationError: const ValidationError.none(),
      prefectureCityValidationError: const ValidationError.none(),
      cityNameValidationError: const ValidationError.none(),
      streetAddressValidationError: const ValidationError.none(),
    );
  }

  /// 画面POP時にstateを初期状態に変更(住所入力画面)
  void onPopPostCodeScreen() {
    state = state.copyWith(
      isRealTimeValidationMode: false,
      postcode: null,
      postcodeValidationError: const ValidationError.none(),
    );
  }

  /// 氏名-姓のバリデーションチェック
  ValidationError validateFamilyName(String? familyName) {
    return [
      checkRequired(familyName),
      checkIsZenkaku(familyName),
      checkIsValidHoujinWebZenkaku(familyName),
      checkMaxLength(familyName, 17),
    ].firstError;
  }

  /// 氏名-名のバリデーションチェック
  ValidationError validateGivenName(String? givenName) {
    return [
      checkRequired(givenName),
      checkIsZenkaku(givenName),
      checkIsValidHoujinWebZenkaku(givenName),
      checkMaxLength(givenName, 17),
    ].firstError;
  }

  /// 氏名-セイのバリデーションチェック
  ValidationError validateFamilyNameKana(String? familyNameKana) {
    return [
      checkRequired(familyNameKana),
      checkIsZenkakuKatakana(familyNameKana),
      checkMaxLength(familyNameKana, 19),
    ].firstError;
  }

  /// 氏名-メイのバリデーションチェック
  ValidationError validateGivenNameKana(String? givenNameKana) {
    return [
      checkRequired(givenNameKana),
      checkIsZenkakuKatakana(givenNameKana),
      checkMaxLength(givenNameKana, 19),
    ].firstError;
  }

  /// 氏名-FamilyNameAlphabeticのバリデーションチェック
  ValidationError validateFamilyNameAlphabetic(String? familyName) {
    if (familyName?.isEmpty ?? true) {
      if (state.isRepresentative) {
        // 英字名の入力が必須であれば必須エラー
        return const ValidationError.required();
      }
    }
    return [
      checkIsHalfWidthAlphabetUpper(familyName),
      checkMaxLength(familyName, maxLengthAlphabeticName),
    ].firstError;
  }

  /// 氏名-GivenNameAlphabeticのバリデーションチェック
  ValidationError validateGivenNameAlphabetic(String? givenName) {
    if (givenName?.isEmpty ?? true) {
      if (state.isRepresentative) {
        // 英字名の入力が必須であれば必須エラー
        return const ValidationError.required();
      }
    }
    return [
      checkIsHalfWidthAlphabetUpper(givenName),
      checkMaxLength(givenName, maxLengthAlphabeticName),
    ].firstError;
  }

  /// 役職名のバリデーションチェック
  ValidationError validatePosition(String? position) {
    return [
      checkRequired(position),
      checkIsZenkaku(position),
      checkIsValidHoujinWebZenkaku(position),
      checkMaxLength(position, maxLengthPosition),
    ].firstError;
  }

  /// 生年月日（年）のバリデーションチェック
  ValidationError validateBirthDateYear(String? birthDateYear) {
    return [
      checkRequired(birthDateYear),
      // メッセージ出しわけのため、ここでは数字が入力されているかを確認
      checkIsValidYear(birthDateYear),
      // メッセージ出しわけのため、ここでは半角数字が入力されているかを確認
      checkIsHalfWidthNumber(birthDateYear),
      checkLengthEquals(birthDateYear, 4),
    ].firstError;
  }

  /// 生年月日（月）のバリデーションチェック
  ValidationError validateBirthDateMonth(String? birthDateMonth) {
    return [
      checkRequired(birthDateMonth),
      checkInvalidMonth(birthDateMonth),
      checkIsHalfWidthNumber(birthDateMonth),
      checkMaxLength(birthDateMonth, 2),
    ].firstError;
  }

  /// 生年月日（日）のバリデーションチェック
  ValidationError validateBirthDateDay(String? birthDateDay) {
    return [
      checkRequired(birthDateDay),
      checkDateExist(
        year: state.birthDateYear.toString(),
        month: state.birthDateMonth.toString(),
        day: birthDateDay,
      ),
      checkIsHalfWidthNumber(birthDateDay),
      checkMaxLength(birthDateDay, 2),
      checkIsFutureDate(
        year: state.birthDateYear.toString(),
        month: state.birthDateMonth.toString(),
        day: birthDateDay,
      ),
    ].firstError;
  }

  /// 郵便番号のバリデーションチェック(isValidPostcodeは該当なしエラー発生時のみfalseを設定)
  ValidationError validatePostcode(
    String? postcode,
    String? cachedPostcode,
    bool isValidPostcode,
  ) {
    return [
      checkRequired(postcode),
      checkIsHalfWidthNumber(postcode),
      checkLengthEquals(postcode, 7),
      checkIsValidPostcode(postcode, cachedPostcode, isValidPostcode),
    ].firstError;
  }

  /// 住所-都道府県・市区町村のバリデーションチェック
  ValidationError validatePrefecture(String? prefectureCity) {
    return [
      checkRequired(prefectureCity),
      checkIsZenkaku(prefectureCity),
      checkIsValidHoujinWebZenkaku(prefectureCity),
      checkMaxLength(prefectureCity, 10),
    ].firstError;
  }

  /// 住所-番地・号のバリデーションチェック
  ValidationError validateCityName(
    String? prefectureCity,
    String? cityName,
  ) {
    return [
      checkRequired(cityName),
      checkIsZenkaku(cityName),
      checkIsValidHoujinWebZenkaku(cityName),
      checkMaxLength(cityName, 20),
      checkSumMaxLength('$prefectureCity$cityName', 100),
    ].firstError;
  }

  /// 住所-マンション名のバリデーションチェック
  ValidationError validateStreetAddress(
    String? prefectureCity,
    String? cityName,
    String? streetAddress,
  ) {
    return [
      checkRequired(streetAddress),
      checkIsZenkaku(streetAddress),
      checkIsValidHoujinWebZenkaku(streetAddress),
      checkMaxLength(streetAddress, 300),
      checkSumMaxLength('$prefectureCity$cityName$streetAddress', 100),
    ].firstError;
  }

  /// 姓名の入力項目のバリデーションチェックを実行する
  /// すべての項目でエラーがなければtrueを返す
  bool validateNameForm() {
    if (state.isEkyc) {
      state = state.copyWith(
        familyNameValidationError: validateFamilyName(state.familyName),
        givenNameValidationError: validateGivenName(state.givenName),
      );
    }
    state = state.copyWith(
      familyNameKanaValidationError:
          validateFamilyNameKana(state.familyNameKana),
      givenNameKanaValidationError: validateGivenNameKana(state.givenNameKana),
      familyNameAlphabeticValidationError:
          validateFamilyNameAlphabetic(state.familyNameAlphabetic),
      givenNameAlphabeticValidationError:
          validateGivenNameAlphabetic(state.givenNameAlphabetic),
    );
    final hasError = state.hasNameValidationError;
    // エラーがある場合、リアルタイムバリデーションモードに移行
    state = state.copyWith(
      isRealTimeValidationMode: hasError,
    );
    return !hasError;
  }

  /// 役職名の入力項目のバリデーションチェックを実行する
  /// すべての項目でエラーがなければtrueを返す
  bool validatePositionForm() {
    state = state.copyWith(
      positionValidationError: validatePosition(state.position),
    );
    final hasError = state.hasPositionValidationError;
    // エラーがある場合、リアルタイムバリデーションモードに移行
    state = state.copyWith(
      isRealTimeValidationMode: hasError,
    );
    return !hasError;
  }

  /// 生年月日の入力項目のバリデーションチェックを実行する
  /// すべての項目でエラーがなければtrueを返す
  bool validateBirthDateForm() {
    state = state.copyWith(
      birthDateYearValidationError: validateBirthDateYear(state.birthDateYear),
      birthDateMonthValidationError:
          validateBirthDateMonth(state.birthDateMonth),
      birthDateDayValidationError: validateBirthDateDay(state.birthDateDay),
    );
    final hasError = state.hasBirthDateValidationError;
    // エラーがある場合、リアルタイムバリデーションモードに移行
    state = state.copyWith(
      isRealTimeValidationMode: hasError,
    );
    return !hasError;
  }

  /// 郵便番号のバリデーションチェックを実行する
  bool validatePostcodeForm() {
    state = state.copyWith(
      postcodeValidationError: validatePostcode(
        state.postcode,
        _cachedPostcode,
        state.isValidPostcode,
      ),
    );
    final hasError = state.postcodeValidationError.hasError;
    // エラーがある場合、リアルタイムバリデーションモードに移行
    state = state.copyWith(
      isRealTimeValidationMode: hasError,
    );
    return hasError;
  }

  /// 住所画面の入力項目のバリデーションチェックを実行する
  bool validateAddressForm() {
    state = state.copyWith(
      postcodeValidationError: validatePostcode(
        state.postcode,
        _cachedPostcode,
        state.isValidPostcode,
      ),
      prefectureCityValidationError: validatePrefecture(state.prefecture),
      cityNameValidationError: validateCityName(
        state.prefecture,
        state.city,
      ),
      streetAddressValidationError: validateStreetAddress(
        state.prefecture,
        state.city,
        state.sectionNumberAndBuildingName,
      ),
    );
    final hasError = state.hasAddressValidationError;
    // エラーがある場合、リアルタイムバリデーションモードに移行
    state = state.copyWith(isRealTimeValidationMode: hasError);
    return hasError;
  }

  void resetInputState() {
    state = state.copyWith(
      inputState: const IdentityVerificationInputState.initial(),
    );
  }

  /// 本人情報送信処理を実行する
  Future<void> sendIdentityVerificationInfo({
    required String role,
    required String referenceNumber,
    required bool isEkyc,
  }) async {
    state = state.copyWith(
      inputState: const IdentityVerificationInputState.loading(),
    );

    roleForBackEnd(role: role);

    final res = await _repository.sendIdentityVerificationInfo(
      IdentityVerificationInfo(
        referenceNumber: referenceNumber,
        role: state.role,
        dataSource: isEkyc ? 'eKYC' : 'JPKI',
        familyName: state.familyName,
        givenName: state.givenName,
        familyNameKana: state.familyNameKana,
        givenNameKana: state.givenNameKana,
        familyNameAlphabetic: state.familyNameAlphabetic,
        givenNameAlphabetic: state.givenNameAlphabetic,
        hepburnStyle: state.isNameEditable,
        position: state.position,
        birthYear:
            state.birthDateYear != null && state.birthDateYear!.isNotEmpty
                ? int.parse(state.birthDateYear!)
                : null,
        birthMonth:
            state.birthDateMonth != null && state.birthDateMonth!.isNotEmpty
                ? int.parse(state.birthDateMonth!)
                : null,
        birthDay: state.birthDateDay != null && state.birthDateDay!.isNotEmpty
            ? int.parse(state.birthDateDay!)
            : null,
        postCode: state.postcode,
        prefecture: state.prefecture,
        city: state.city,
        sectionNumberAndBuildingName: state.sectionNumberAndBuildingName,
      ),
    );
    res.when(
      success: (_) {
        state = state.copyWith(
          inputState: const IdentityVerificationInputState.sendSuccess(),
        );
      },
      failure: (error) {
        state = state.copyWith(
          inputState: IdentityVerificationInputState.sendFailure(error),
        );
      },
    );
  }

  /// roleをバックエンド送信用に変換する
  void roleForBackEnd({
    required String role,
  }) {
    String convertRoleForBackEnd;
    switch (role) {
      case '01':
        convertRoleForBackEnd = 'REPRESENTATIVE';
      case '02':
        convertRoleForBackEnd = 'AGENT';
      case '03':
        convertRoleForBackEnd = 'BENEFICIARY1';
      case '04':
        convertRoleForBackEnd = 'BENEFICIARY2';
      case '05':
        convertRoleForBackEnd = 'BENEFICIARY3';
      case '06':
        convertRoleForBackEnd = 'BENEFICIARY4';
      default:
        convertRoleForBackEnd = '';
    }
    state = state.copyWith(role: convertRoleForBackEnd);
  }

  /// 住所検索APIを実行する
  Future<void> fetchAddress({
    String? postcode,
    bool fromSendIdentityVerificationInfo = false,
  }) async {
    // エラー情報をリセット
    clearFetchAddressError();

    final res = await _repository.fetchAddress(postcode);
    await res.when(
      success: (address) async {
        if (fromSendIdentityVerificationInfo) {
          // 送信ボタン押下時に呼び出された場合は何もしない
          return;
        } else {
          // 自動入力ボタン押下時は取得した値を都道府県欄に設定
          final formattedAddress = _formatAddressFromApiResponse(address);
          final addressList = formattedAddress.split(',');
          if (formattedAddress.isNotEmpty) {
            updatePrefecture(addressList[0]);
            updateCityName(addressList[1]);
            updateStreetAddress(addressList[2]);
          }
        }
      },
      failure: (error) {
        // 該当住所なしのエラー返却時は、バリデーションエラーとして表示するため分岐
        if (error.code == ErrorInfo.notFoundAddressErrorCode) {
          // 該当住所なしのバリデーションエラーを設定
          state = state.copyWith(
            postcodeValidationError:
                const ValidationError.invalidPostalCodeFormat(),
          );
          // エラー返却時の郵便番号を保存
          _cachedPostcode = postcode;
          state = state.copyWith(isValidPostcode: false);

          // 自動入力ボタン押下した場合
          if (!fromSendIdentityVerificationInfo) {
            // 都道府県をnullに更新
            updatePrefecture(null);
            updateCityName(null);
            updateStreetAddress(null);
          }
        } else {
          state = state.copyWith(fetchAddressError: error);
        }
      },
    );
  }

  /// 住所検索APIにて取得した情報を画面に表示する形式に変換する
  String _formatAddressFromApiResponse(Address address) {
    final prefecture = address.prefecture ?? '';
    final city = address.city ?? '';
    final street = address.street ?? '';

    if (prefecture.isEmpty || city.isEmpty || street.isEmpty) {
      return '';
    }

    return '$prefecture,$city,$street';
  }

  /// 住所検索APIエラー情報の削除
  void clearFetchAddressError() {
    state = state.copyWith(fetchAddressError: null);
  }

  /// 都道府県・市区町村エラー情報の削除
  void clearPrefectureCityError() {
    state = state.copyWith(
      prefectureCityValidationError: const ValidationError.none(),
    );
  }

  /// JPKIを選択
  void switchToJPKI() {
    state = state.copyWith(isEkyc: false);
  }

  /// eKYCを選択
  void switchToEkyc() {
    state = state.copyWith(isEkyc: true);
  }

  // 画面描画時にメモリ・セキュアストレージに保持している情報を削除する
  Future<void> deleteMemoryAndSecureData() async {
    await memoryDataManager.jpkiAccessToken.delete();
    await memoryDataManager.jpkiPassword.delete();
    await memoryDataManager.sigDoc.delete();
    await memoryDataManager.certForSig.delete();
    await secureStorageManager.aesIv.delete();
    await secureStorageManager.aesKey.delete();
  }
}
