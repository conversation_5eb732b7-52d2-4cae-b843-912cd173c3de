// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'force_update_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ForceUpdateScreenState {
  ForceUpdateStatus get forceUpdateStatus => throw _privateConstructorUsedError;
  bool get isInitialized => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String get errorCode => throw _privateConstructorUsedError;
  String get buttonLink => throw _privateConstructorUsedError;
  bool get isAndroid12 => throw _privateConstructorUsedError;

  /// Create a copy of ForceUpdateScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ForceUpdateScreenStateCopyWith<ForceUpdateScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForceUpdateScreenStateCopyWith<$Res> {
  factory $ForceUpdateScreenStateCopyWith(ForceUpdateScreenState value,
          $Res Function(ForceUpdateScreenState) then) =
      _$ForceUpdateScreenStateCopyWithImpl<$Res, ForceUpdateScreenState>;
  @useResult
  $Res call(
      {ForceUpdateStatus forceUpdateStatus,
      bool isInitialized,
      String? message,
      String errorCode,
      String buttonLink,
      bool isAndroid12});
}

/// @nodoc
class _$ForceUpdateScreenStateCopyWithImpl<$Res,
        $Val extends ForceUpdateScreenState>
    implements $ForceUpdateScreenStateCopyWith<$Res> {
  _$ForceUpdateScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ForceUpdateScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? forceUpdateStatus = null,
    Object? isInitialized = null,
    Object? message = freezed,
    Object? errorCode = null,
    Object? buttonLink = null,
    Object? isAndroid12 = null,
  }) {
    return _then(_value.copyWith(
      forceUpdateStatus: null == forceUpdateStatus
          ? _value.forceUpdateStatus
          : forceUpdateStatus // ignore: cast_nullable_to_non_nullable
              as ForceUpdateStatus,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      errorCode: null == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String,
      buttonLink: null == buttonLink
          ? _value.buttonLink
          : buttonLink // ignore: cast_nullable_to_non_nullable
              as String,
      isAndroid12: null == isAndroid12
          ? _value.isAndroid12
          : isAndroid12 // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ForceUpdateScreenStateImplCopyWith<$Res>
    implements $ForceUpdateScreenStateCopyWith<$Res> {
  factory _$$ForceUpdateScreenStateImplCopyWith(
          _$ForceUpdateScreenStateImpl value,
          $Res Function(_$ForceUpdateScreenStateImpl) then) =
      __$$ForceUpdateScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ForceUpdateStatus forceUpdateStatus,
      bool isInitialized,
      String? message,
      String errorCode,
      String buttonLink,
      bool isAndroid12});
}

/// @nodoc
class __$$ForceUpdateScreenStateImplCopyWithImpl<$Res>
    extends _$ForceUpdateScreenStateCopyWithImpl<$Res,
        _$ForceUpdateScreenStateImpl>
    implements _$$ForceUpdateScreenStateImplCopyWith<$Res> {
  __$$ForceUpdateScreenStateImplCopyWithImpl(
      _$ForceUpdateScreenStateImpl _value,
      $Res Function(_$ForceUpdateScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ForceUpdateScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? forceUpdateStatus = null,
    Object? isInitialized = null,
    Object? message = freezed,
    Object? errorCode = null,
    Object? buttonLink = null,
    Object? isAndroid12 = null,
  }) {
    return _then(_$ForceUpdateScreenStateImpl(
      forceUpdateStatus: null == forceUpdateStatus
          ? _value.forceUpdateStatus
          : forceUpdateStatus // ignore: cast_nullable_to_non_nullable
              as ForceUpdateStatus,
      isInitialized: null == isInitialized
          ? _value.isInitialized
          : isInitialized // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      errorCode: null == errorCode
          ? _value.errorCode
          : errorCode // ignore: cast_nullable_to_non_nullable
              as String,
      buttonLink: null == buttonLink
          ? _value.buttonLink
          : buttonLink // ignore: cast_nullable_to_non_nullable
              as String,
      isAndroid12: null == isAndroid12
          ? _value.isAndroid12
          : isAndroid12 // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ForceUpdateScreenStateImpl implements _ForceUpdateScreenState {
  const _$ForceUpdateScreenStateImpl(
      {this.forceUpdateStatus = ForceUpdateStatus.initial,
      this.isInitialized = false,
      this.message,
      this.errorCode = '',
      this.buttonLink = '',
      this.isAndroid12 = false});

  @override
  @JsonKey()
  final ForceUpdateStatus forceUpdateStatus;
  @override
  @JsonKey()
  final bool isInitialized;
  @override
  final String? message;
  @override
  @JsonKey()
  final String errorCode;
  @override
  @JsonKey()
  final String buttonLink;
  @override
  @JsonKey()
  final bool isAndroid12;

  @override
  String toString() {
    return 'ForceUpdateScreenState(forceUpdateStatus: $forceUpdateStatus, isInitialized: $isInitialized, message: $message, errorCode: $errorCode, buttonLink: $buttonLink, isAndroid12: $isAndroid12)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForceUpdateScreenStateImpl &&
            (identical(other.forceUpdateStatus, forceUpdateStatus) ||
                other.forceUpdateStatus == forceUpdateStatus) &&
            (identical(other.isInitialized, isInitialized) ||
                other.isInitialized == isInitialized) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.errorCode, errorCode) ||
                other.errorCode == errorCode) &&
            (identical(other.buttonLink, buttonLink) ||
                other.buttonLink == buttonLink) &&
            (identical(other.isAndroid12, isAndroid12) ||
                other.isAndroid12 == isAndroid12));
  }

  @override
  int get hashCode => Object.hash(runtimeType, forceUpdateStatus, isInitialized,
      message, errorCode, buttonLink, isAndroid12);

  /// Create a copy of ForceUpdateScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForceUpdateScreenStateImplCopyWith<_$ForceUpdateScreenStateImpl>
      get copyWith => __$$ForceUpdateScreenStateImplCopyWithImpl<
          _$ForceUpdateScreenStateImpl>(this, _$identity);
}

abstract class _ForceUpdateScreenState implements ForceUpdateScreenState {
  const factory _ForceUpdateScreenState(
      {final ForceUpdateStatus forceUpdateStatus,
      final bool isInitialized,
      final String? message,
      final String errorCode,
      final String buttonLink,
      final bool isAndroid12}) = _$ForceUpdateScreenStateImpl;

  @override
  ForceUpdateStatus get forceUpdateStatus;
  @override
  bool get isInitialized;
  @override
  String? get message;
  @override
  String get errorCode;
  @override
  String get buttonLink;
  @override
  bool get isAndroid12;

  /// Create a copy of ForceUpdateScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForceUpdateScreenStateImplCopyWith<_$ForceUpdateScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
