import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dtp_app/business_logics/interfaces/app_launching_repository.dart';
import 'package:dtp_app/datas/app_information_manager.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';
import 'package:dtp_app/repositories/aes_salt/aes_salt_repository_impl.dart';
import 'package:dtp_app/repositories/app_launching/app_launching_repository_impl.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'force_update_screen_notifier.freezed.dart';

enum ForceUpdateStatus {
  /// 初期状態
  initial,

  /// 強制アップデート
  forcedUpdate,

  /// 任意アップデート
  optionalUpdate,

  /// 任意アップデートダイアログではいボタンを選択/アップデート不要
  completed,

  /// エラー
  error,

  /// SALT取得エラー
  /// -> 共通ダイアログ9を表示
  errorSalt,
}

@freezed
class ForceUpdateScreenState with _$ForceUpdateScreenState {
  const factory ForceUpdateScreenState({
    @Default(ForceUpdateStatus.initial) ForceUpdateStatus forceUpdateStatus,
    @Default(false) bool isInitialized,
    String? message,
    @Default('') String errorCode,
    @Default('') String buttonLink,
    @Default(false) bool isAndroid12,
  }) = _ForceUpdateScreenState;
}

final forceUpdateScreenProvider =
    StateNotifierProvider<ForceUpdateScreenNotifier, ForceUpdateScreenState>(
  (ref) => ForceUpdateScreenNotifier(
    appLaunchingRepository: ref.watch(appLaunchingRepositoryProvider),
    appInformationManager: ref.read(appInformationManagerProvider),
    aesSaltRepository: ref.read(aesSaltRepositoryProvider),
    state: const ForceUpdateScreenState(),
  ),
);

class ForceUpdateScreenNotifier extends StateNotifier<ForceUpdateScreenState> {
  ForceUpdateScreenNotifier({
    required this.appLaunchingRepository,
    required this.appInformationManager,
    required this.aesSaltRepository,
    required ForceUpdateScreenState state,
  }) : super(state);

  final AppLaunchingRepository appLaunchingRepository;
  final AppInformationManager appInformationManager;
  final AesSaltRepository aesSaltRepository;

  Future<void> initialize({
    required ForceUpdateConfig forceUpdateConfig,
  }) async {
    await _setStatus(forceUpdateConfig: forceUpdateConfig);
  }

  Future<void> checkForceUpdate() async {
    final appInformation = await appInformationManager.load();
    final forceUpdateConfig = await appLaunchingRepository.checkForceUpdate(
      appInformation: appInformation,
    );
    await _setStatus(forceUpdateConfig: forceUpdateConfig);
  }

  Future<void> fetchSaltAndGenerateKey() async {
    final result = await aesSaltRepository.fetchSaltAndGenerateKey();
    result.when(
      success: (_) {
        goToHome();
      },
      failure: (error) {
        state = state.copyWith(
          forceUpdateStatus: ForceUpdateStatus.errorSalt,
        );
      },
    );
  }

  void goToHome() {
    state = state.copyWith(
      forceUpdateStatus: ForceUpdateStatus.completed,
    );
  }

  Future<void> _setStatus({
    required ForceUpdateConfig forceUpdateConfig,
  }) async {
    var status = ForceUpdateStatus.completed;
    String? message;
    var buttonLink = '';
    var errorCode = '';
    var isAndroid12 = false;
    switch (forceUpdateConfig.updateType) {
      case UpdateType.force:
        // 強制アップデート
        status = ForceUpdateStatus.forcedUpdate;
        final force =
            forceUpdateConfig.forceUpdateConfigResponse?.updateAlert.force;
        message = force!.message;
        buttonLink = force.buttonLink;
        break;
      case UpdateType.optional:
        // 任意アップデート
        status = ForceUpdateStatus.optionalUpdate;
        final optional =
            forceUpdateConfig.forceUpdateConfigResponse?.updateAlert.optional;
        message = optional!.message;
        buttonLink = optional.buttonLink;
        break;
      case UpdateType.error:
        // エラー
        status = ForceUpdateStatus.error;
        final updateError = forceUpdateConfig.updateError!;
        errorCode = updateError.errorCode;
        message = updateError.message;
        isAndroid12 = await _isAndroid12();
        break;
      case UpdateType.none:
        // アップデート不要
        break;
    }
    state = state.copyWith(
      forceUpdateStatus: status,
      isInitialized: true,
      message: message,
      buttonLink: buttonLink,
      errorCode: errorCode,
      isAndroid12: isAndroid12,
    );
  }

  // Android12以上か否かを取得（仮実装のためテストには含めず）
  Future<bool> _isAndroid12() async {
    if (Platform.isAndroid) {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final androidInfo = await deviceInfoPlugin.androidInfo;
      return androidInfo.version.sdkInt > 30;
    }
    return false;
  }
}
