import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/interfaces/account_transaction_daily_repository.dart';
import 'package:dtp_app/business_logics/interfaces/announcement_info.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_display_config_repository.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_repository.dart';
import 'package:dtp_app/business_logics/interfaces/credit_card_transaction_history_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/datas/date_time_manager.dart';
import 'package:dtp_app/datas/login_flag_provider.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/bank_account_base_info/bank_account_base_info.dart';
import 'package:dtp_app/models/freee_credit_card_expense/freee_credit_card_expense.dart';
import 'package:dtp_app/models/freee_signup_params/freee_signup_params.dart';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/models/session/session.dart';
import 'package:dtp_app/models/session_permission_status/session_permission_status.dart';
import 'package:dtp_app/models/transaction_daily_total/transaction_daily_total.dart';
import 'package:dtp_app/models/users/users.dart';
import 'package:dtp_app/repositories/account_transaction_daily_repository/account_transaction_daily_repository_impl.dart';
import 'package:dtp_app/repositories/announcement_info/announcement_info_repository_impl.dart';
import 'package:dtp_app/repositories/bank_account/bank_account_repository_impl.dart';
import 'package:dtp_app/repositories/bank_account_display_config/bank_account_display_config_repository_impl.dart';
import 'package:dtp_app/repositories/credit_card_transaction_history/credit_card_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/user/user_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'home_screen_notifier.freezed.dart';

/// freeeアクセストークン、リフレッシュトークンの取得状況
enum FreeeTokenStatus {
  // 処理不要
  noActionRequired,

  // 取得エラー
  error,

  // freee初回完了
  firstLinkageCompleted,
}

enum FreeeSSOTypeFromHome {
  // 初回連携
  firstTime,

  // 口座追加
  bankAccount,

  // クレジットカード追加
  creditCard,

  // 金融機関連携期限切れ
  expirationOfFinanceLinkage,
}

extension FreeeTokenStatusExt on FreeeTokenStatus {
  bool get isFreeeTokenError => this == FreeeTokenStatus.error;
}

@freezed
class HomeScreenState with _$HomeScreenState {
  /// hack: 変数が多すぎて管理できていないため、要リファクタリング
  const factory HomeScreenState({
    /// ローディング管理フラグ(全体)
    @Default(false) bool isLoading,

    /// ローディング管理フラグ(他行口座)
    @Default(false) bool isLoadingOtherBanks,

    /// 口座情報の非同期読み込みフラグ
    @Default(false) bool isAccountBalanceAsyncLoading,

    /// 自行の口座情報
    @Default(<BankAccount>[]) List<BankAccount> bankAccounts,

    /// 他行の口座情報
    @Default(<BankAccountWithBalanceDetail>[])
    List<BankAccountWithBalanceDetail> otherBankAccounts,

    /// クレジットカード利用情報
    @Default(<CreditCardExpense>[])
    List<CreditCardExpense> freeeCreditCardExpenses,

    /// 入出金日時合計額情報
    @Default(<TransactionDailyTotal>[])
    List<TransactionDailyTotal> transactionDailyTotal,

    /// 情報取得日付
    @Default('') String serverDate,

    /// 残高公開フラグ
    @Default(true) bool isBalanceVisible,

    /// クレジットカード利用金額公開フラグ
    @Default(true) bool isCreditCardExpenseVisible,

    /// freee連携フラグ
    @Default(false) bool isFreeeLinked,

    /// freee連携期限切れフラグ
    /// HACK コチラのフラグはVMの処理に影響を与えるものではない為、フラグをViewSettingsに移管させる
    @Default(false) bool freeeExpirationFlag,

    /// web21再認可フラグ
    @Default(false) bool reAuthorizationFlag,

    /// 利用者情報0件エラー
    @Default(false) bool isNoAccountError,

    /// freee再連携中かを表現するフラグ
    @Default(false) bool isOnFreeeReAlignment,

    /// 取引先ID(clientID)に紐づくデータ有無フラグ 紐づくデータがある場合はtrue,ない場合はfalse
    @Default(false) bool exists,

    /// freeeのアクセストークンの状態
    @Default(
      FreeeTokenStatus.noActionRequired,
    )
    FreeeTokenStatus freeeTokenStatus,

    /// 選択したクレジットカードのindex
    int? selectCreditCardExpenseIndex,

    /// お知らせ情報
    String? announcementInfo,

    /// 利用者情報エラー
    AppError? userAccountError,

    /// 入出金日時取得エラー
    AppError? transactionDailyError,

    /// freee連携リンク取得エラー
    AppError? getUrlToFreeeSsoParamsError,

    /// freee連携エラー
    AppError? freeeLinkError,

    /// freee他行口座エラー
    AppError? freeeBankAccountError,

    /// 口座表示設定エラー
    AppError? accountSettingsError,

    /// アクセストークンエラー
    AppError? tokenError,

    /// web21認可エラー
    AppError? authError,

    /// ダイアログ上に表示するエラー
    AppError? onDialogError,

    /// 取引先ID紐付け確認エラー
    AppError? checkFreeeLinksByClientIdError,
  }) = _HomeScreenState;
}

extension HomeScreenStateEx on HomeScreenState {
  /// 口座残高合計額を取得する
  int? get totalBalance {
    final balanceAccountsIncludingOtherBanks = [
      ...bankAccounts.onlyVisible(),
      ...otherBankAccounts,
    ];
    return balanceAccountsIncludingOtherBanks
        .toBalanceDetailList()
        .getTotalBalance();
  }

  /// クレジットカード利用金額合計を取得する
  int? get totalCreditExpense {
    return freeeCreditCardExpenses.getTotalCreditCardExpense();
  }

  /// 支払い可能残高を取得する
  int? get totalWithdrawableBalance {
    final allBankAccounts = [
      ...bankAccounts,
      ...otherBankAccounts,
    ];
    return allBankAccounts.toBalanceDetailList().getTotalWithdrawableBalance();
  }

  int? get totalChecksIssuedByOtherBanksAmount => bankAccounts
      .toBalanceDetailList()
      .getTotalChecksIssuedByOtherBanksAmount();

  int? get totalOverdraftLimit =>
      bankAccounts.toBalanceDetailList().getTotalOverdraftLimit();

  /// 共通エラーを検知するためにエラーのリストを取得
  /// 新規にAPI情報を追加するときはhasErrorListとclearErrorメソッドも更新すること
  List<AppError> get hasErrorList {
    final errorList = [
      userAccountError,
      ...bankAccounts.errorList,
      transactionDailyError,
      freeeLinkError,
      freeeBankAccountError,
      accountSettingsError,
      tokenError,
      authError,
      ...freeeCreditCardExpenses.map((e) => e.freeeCreditCardError),
    ];
    return errorList.whereType<AppError>().toList();
  }

  String? get otherBankBaseDateTime =>
      otherBankAccounts.firstOrNull?.balanceDetail.baseDateTime;

  String? get smbcBaseTime =>
      bankAccounts.onlyHasBalanceDetail().firstOrNull?.baseTime;

  //　クレジットのローディング状態は全てのカードがロード中であるかどうかで判断
  bool get isCreditCardInRefresh =>
      freeeCreditCardExpenses.any((e) => e.isLoading);
}

final homeScreenProvider =
    StateNotifierProvider<HomeScreenNotifier, HomeScreenState>((ref) {
  // ログイン状態の変化に合わせて再生成するため、watchメソッドを呼んでおく
  ref.watch(loginFlagProvider);

  return HomeScreenNotifier(
    bankAccountRepository: ref.read(bankAccountRepositoryProvider),
    creditCardTransactionHistoryRepository:
        ref.read(creditCardTransactionHistoryRepositoryProvider),
    userRepository: ref.read(userRepositoryProvider),
    accountTransactionDailyRepository:
        ref.read(accountTransactionDailyRepositoryProvider),
    bankAccountDisplayConfigRepository:
        ref.read(bankAccountDisplayConfigRepositoryProvider),
    dateTimeManager: ref.read(dateTimeManagerProvider),
    loginRepository: ref.read(loginRepositoryProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
    announcementInfoRepository: ref.read(announcementInfoRepositoryProvider),
  );
});

class HomeScreenNotifier extends StateNotifier<HomeScreenState> {
  HomeScreenNotifier({
    required this.bankAccountRepository,
    required this.creditCardTransactionHistoryRepository,
    required this.userRepository,
    required this.accountTransactionDailyRepository,
    required this.dateTimeManager,
    required this.bankAccountDisplayConfigRepository,
    required this.loginRepository,
    required this.analyticsLogController,
    required this.announcementInfoRepository,
  }) : super(const HomeScreenState());

  // HOME初期化フラグ
  // 基本的にはログイン後のみで初期化するという要件実現のため
  bool _isInitialized = false;

  final BankAccountRepository bankAccountRepository;
  final CreditCardTransactionHistoryRepository
      creditCardTransactionHistoryRepository;
  final UserRepository userRepository;
  final AccountTransactionDailyRepository accountTransactionDailyRepository;
  final BankAccountDisplayConfigRepository bankAccountDisplayConfigRepository;
  final DateTimeManager dateTimeManager;
  final LoginRepository loginRepository;
  final AnalyticsLogController analyticsLogController;
  final AnnouncementInfoRepository announcementInfoRepository;

  void _onLoadStarted() {
    state = state.copyWith(isLoading: true);
  }

  void _onLoadFinished() {
    if (mounted) {
      // 終了前に画面が閉じられた場合にそなえてmountedを判定
      state = state.copyWith(isLoading: false);
    }
  }

  void onLoadOtherBanksStarted() {
    state = state.copyWith(isLoadingOtherBanks: true);
  }

  void _onLoadOtherBanksFinished() {
    if (mounted) {
      // 終了前に画面が閉じられた場合にそなえてmountedを判定
      state = state.copyWith(isLoadingOtherBanks: false);
    }
  }

  // 明細ボタン押下時にクレジットカード情報参照先を更新する
  void updateItemIndex(int? itemIndex) {
    state = state.copyWith(selectCreditCardExpenseIndex: itemIndex);
  }

  /// ログアウトする際にエラー情報をリセットするメソッド
  Future<void> clearError() async {
    state = state.copyWith(
      userAccountError: null,
      transactionDailyError: null,
      freeeLinkError: null,
      freeeBankAccountError: null,
      accountSettingsError: null,
      tokenError: null,
      authError: null,
    );

    // 残高情報を削除することでエラー情報も削除
    state = state.copyWith(
      bankAccounts: [],
      freeeCreditCardExpenses: [],
    );

    // 再認可ボタン表示フラグをリセット
    state = state.copyWith(reAuthorizationFlag: false);
  }

  /// 初期化処理（ホーム画面初期化処理の実行要否判定）
  Future<void> initialize({
    required bool hasInquiryAuth,
    bool forceRefresh = false,
  }) async {
    // 初期化済みの場合何もしない
    if (_isInitialized && !forceRefresh) return;

    // HOMEのuseEffectが二度実行されているため、初期化が二度実施されないように
    // フラグをすぐに更新している。
    /// HACK Login後にScreenのuseEffecttが二度実行されない形に改修
    _isInitialized = true;
    // 非同期でお知らせ情報を取得する
    unawaited(_getAnnouncementInfo());

    /// HACK アクセストークンエラー、認可エラーのハンドリング方法に関しては
    /// かなりヤヤコしい仕組みになってしまっている為、引数渡しのよりシンプルな形にリファクタリング
    // 認可エラーがある際には処理を中断
    final hasAuthError = _handleAuthError(hasInquiryAuth);
    if (hasAuthError) return;

    _onLoadStarted();
    onLoadOtherBanksStarted();

    // ログイン状態および、セッションID・セッション情報を取得する
    final fetchLoginStatusResult = await loginRepository.fetchLoginStatus();
    await fetchLoginStatusResult.when(
      success: (loginStatus) async {
        // アクセストークンエラー情報リセット
        state = state.copyWith(tokenError: null);
        // LoginStatusLogoutが返却されキャスト例外が発生する可能性を考慮し、条件判定
        if (loginStatus is LoginStatusLogin) {
          // アクセストークン取得エラーの場合、エラー情報をセットしホーム画面初期化処理をスキップする
          if (loginStatus.session.permissionStatus ==
              SessionPermissionStatus.denied) {
            state = state.copyWith(
              tokenError: (loginStatus.session as SessionIdentified)
                  .accessTokenError!
                  .copyWith(isShowOnDialog: false),
            );
            _onLoadFinished();
            _onLoadOtherBanksFinished();
            return;
          }

          // ホーム画面初期化処理を実行
          await _initializeHomeScreen();
        }
        _onLoadFinished();
        _onLoadOtherBanksFinished();
      },
      failure: (AppError error) {
        state =
            state.copyWith(tokenError: error.copyWith(isShowOnDialog: false));
        _onLoadFinished();
        _onLoadOtherBanksFinished();
      },
    );

    await analyticsLogController.sendIdentifyAndAttribute(
      freeeLinkStatus: state.isFreeeLinked,
      anotherAccountCount: state.otherBankAccounts.length,
      cardCount: state.freeeCreditCardExpenses.length,
    );
  }

  /// 認可権限エラーのハンドリング(エラーがある場合にtrueを返却)
  bool _handleAuthError(bool hasInquiryAuth) {
    final authError = loginRepository.getAuthError();
    // エラーがある場合のハンドリング
    if (authError != null) {
      switch (authError.code) {
        // 企業コードがない場合
        case HomeErrorWithOutServiceInfo.withOutServiceErrorCode:
          final noAuthError = AppError(
            code: authError.code,
            message: authError.message,
            isShowOnDialog: false,
            hasFaq: false,
          );
          // 再認可ボタンは表示させない
          state = state.copyWith(
            authError: noAuthError,
            reAuthorizationFlag: false,
          );
          // 企業コードなし、且つ口座照会権限を保有している場合freeeのみを初期化
          if (hasInquiryAuth) initializeFreeeInfo();
          return true;
        // 認可拒否履歴ありの場合、口座照会権限なしとなるため先にハンドリングを実施
        case ErrorInfo.notAuthenticatedErrorCode:
          // 認可拒否履歴ありの場合は、再認可ボタン表示フラグをtrueに
          state = state.copyWith(
            authError: authError.copyWith(isShowOnDialog: false),
            reAuthorizationFlag: true,
          );
          return true;
      }
    }
    // 口座照会権限がない場合のハンドリング
    if (!hasInquiryAuth) {
      final noAuthError = AppError(
        code: HomeErrorInfo.noInquiryAuthErrorCode,
        message: HomeErrorInfo.noInquiryAuthErrorMessage,
        isShowOnDialog: false,
        hasFaq: true,
      );
      // 再認可ボタンは表示させない
      state =
          state.copyWith(authError: noAuthError, reAuthorizationFlag: false);
      return true;
    }
    // 再認可用にエラーがなかった際には初期化
    state = state.copyWith(authError: null);
    return false;
  }

  /// 口座情報（自行口座ならびにFreee連携他行口座）を読み込む
  /// 主に口座照会画面表示時の再読み込み処理として実行する
  Future<void> loadBankAccountsForInquiryScreen() async {
    _onLoadStarted();

    // serverDateを取得
    state = state.copyWith(
      serverDate: userRepository.getServerDate(),
    );

    // Freee連携情報を再取得
    await _loadFreeeLinkStatus();

    // 自行口座の口座残高情報を読み込み
    final bankAccountFuture = _loadAccountBalance(
      forceRefresh: true,
    );

    // 他行口座の口座残高情報を読み込み
    final otherBankAccountFuture = _loadOtherBankAccountBalance();

    // それぞれの初期化処理を待ち合わせ
    await Future.wait([bankAccountFuture, otherBankAccountFuture]);
    _onLoadFinished();
  }

  /// ホーム画面初期化処理
  Future<void> _initializeHomeScreen() async {
    // エラー情報をリセット
    state = state.copyWith(
      userAccountError: null,
      // 別タブから戻りホーム画面を再描画するとき用
      accountSettingsError: null,
    );

    // Anserから取得する情報
    final anserFuture = _initializeAnserInfo();
    // freeeから取得する情報
    final freeeFuture = initializeFreeeInfo();

    // それぞれの初期化処理を待ち合わせ
    await Future.wait([anserFuture, freeeFuture]);

    await _loadBalanceVisibility();

    await _loadCreditCardVisibility();

    _onLoadFinished();
  }

  // 画面初期化時に叩くAnserBizSolから情報取得するメソッド
  Future<void> _initializeAnserInfo() async {
    // 利用者情報を取得
    final result = await userRepository.getUsers();
    await result.when(
      success: (users) async {
        // 利用者が0件だった場合、
        if (users.accountList.isEmpty) {
          // エラーカードに表示するため、エラー内部に情報を埋め込む
          // 実際には発生しないケースのため、共通エラーにまとめる
          const noAccountInfo = AppError(
            code: ErrorInfo.defaultErrorCode,
            message: ErrorInfo.defaultErrorMessage,
            isShowOnDialog: false,
          );
          state = state.copyWith(
            userAccountError: noAccountInfo,
            isNoAccountError: true,
            serverDate: users.serverDate,
          );
        } else {
          // ユーザー保有口座リストを更新
          state = state.copyWith(
            bankAccounts: users.accountList.toBankAccountList(),
            serverDate: users.serverDate,
          );
          // 各情報取得処理
          await _loadAccountBalance();
          await _loadTransactionDailyTotal();
        }
      },
      failure: (error) {
        state = state.copyWith(
          userAccountError: error.copyWith(isShowOnDialog: false),

          /// WARNING: 通信エラーなどserver日時が取得できない場合もある為注意
          /// より堅牢な作りにするためにはfreee連携の際にもサーバー日付を更新する
          serverDate: error.baseDate ?? '',
        );
      },
    );
  }

  /// 画面初期化時に叩く、freee情報を取得するメソッド
  Future<void> initializeFreeeInfo() async {
    // エラー情報をリセット
    state = state.copyWith(freeeLinkError: null);

    await _loadFreeeLinkStatus();

    // 他行口座
    final freeeBanksFuture = _loadOtherBankAccountBalance();
    // クレジットカード
    final freeeCreditCardsFuture = _loadCreditCardExpense();
    await Future.wait([freeeBanksFuture, freeeCreditCardsFuture]);

    _onLoadOtherBanksFinished();
  }

  /// Freeeから他行口座の情報を取得
  Future<void> _loadOtherBankAccountBalance() async {
    if (state.isFreeeLinked) {
      await getFreeeAccountBalance(InquiryCategory.bankAccount);
    }
  }

  /// Freeeからクレジットカードの情報を取得
  Future<void> _loadCreditCardExpense() async {
    if (state.isFreeeLinked) {
      await getFreeeAccountBalance(InquiryCategory.creditCard);
    }
  }

  /// 口座表示設定情報を取得
  Future<void> _loadAccountSettings() async {
    // エラー情報をリセット
    state = state.copyWith(accountSettingsError: null);

    final result =
        await bankAccountDisplayConfigRepository.getAccountSettings();

    result.when(
      success: (accountSettings) {
        final mergedBankAccounts =
            state.bankAccounts.overwriteDisplayConfig(accountSettings);
        state = state.copyWith(bankAccounts: mergedBankAccounts);
      },
      failure: (error) {
        state = state.copyWith(
          accountSettingsError: error.copyWith(isShowOnDialog: false),
        );
      },
    );
  }

  /// 口座残高情報を取得
  /// [forceRefresh] trueの場合、すべての口座詳細情報を強制的に再取得する
  Future<void> _loadAccountBalance({
    bool forceRefresh = false,
  }) async {
    // エラー情報をリセット
    state = state.copyWith(bankAccounts: state.bankAccounts.resetError());

    // 口座IDのみ抽出
    final accountIds =
        state.bankAccounts.onlyVisible().map((e) => e.accountId).toList();

    // 新たに詳細取得対象となる口座のIDを洗い出す関数
    // すでに詳細取得済みの口座ID（oldAccountIds）をのぞいた口座IDを返す
    // ただし、forceRefreshがtrueの場合のみ、それに関係なくすべての口座IDを取得対象として返す
    List<String?> getAccountIdsWithoutBalanceDetails() {
      if (forceRefresh) {
        return accountIds;
      }

      final oldAccountIds = List.of(
        state.bankAccounts
            .onlyHasBalanceDetail()
            .map((e) => e.baseInfo.accountId),
      );
      final newAccountIds = List.of(accountIds);
      newAccountIds.removeWhere((e) => oldAccountIds.contains(e));

      return newAccountIds;
    }

    // 上記の関数を用いて取得対象となるIDを洗い出す
    final accountIdsToLoad = getAccountIdsWithoutBalanceDetails();

    if (accountIdsToLoad.isNotEmpty) {
      // 指定口座情報を1口座ずつ非同期で取得
      await Future.forEach(accountIdsToLoad, (accountId) async {
        if (mounted) {
          if (!state.isAccountBalanceAsyncLoading) {
            // 非同期読み込み中フラグをONにする
            state = state.copyWith(
              isAccountBalanceAsyncLoading: true,
            );
          }
          await _singleLoadAccountBalance(accountId);
        }
      }).then((_) {
        if (mounted) {
          // 非同期読み込み中フラグをOFFにする
          state = state.copyWith(
            isAccountBalanceAsyncLoading: false,
          );
        }
      });
    } else {
      // 表示設定の反映のみ
      // 口座表示設定画面にて設定された順序でホーム画面の口座残高カードを並び替えてリストに格納する
      state = state.copyWith(
        bankAccounts: state.bankAccounts.sortedByDisplayOrder(),
      );
    }
  }

  /// 日次入出金合計を取得する
  Future<void> _loadTransactionDailyTotal() async {
    if (state.bankAccounts.isEmpty ||
        state.bankAccounts.sortedByDisplayOrder().onlyVisible().isEmpty) {
      return;
    }

    // エラー情報をリセット
    state = state.copyWith(
      transactionDailyError: null,
    );

    final accountId = state.bankAccounts
        .sortedByDisplayOrder()
        .onlyVisible()
        .first
        .accountId
        .toString();

    final result = await accountTransactionDailyRepository.getWeeklyTotal(
      accountId: accountId,
      endDate: dateTimeManager.now,
    );

    result.when(
      success: (transactionDailyTotal) {
        state = state.copyWith(
          transactionDailyTotal: transactionDailyTotal,
        );
      },
      failure: (error) {
        state = state.copyWith(
          transactionDailyError: error.copyWith(
            isShowOnDialog: false,
          ),
        );
      },
    );
  }

  Future<void> _singleLoadAccountBalance(String? accountId) async {
    // 口座残高カードをロード中に
    final loadingBankAccounts =
        state.bankAccounts.overwriteLoading(accountId ?? '');
    state = state.copyWith(bankAccounts: loadingBankAccounts);

    final accountBalance = await bankAccountRepository.getAccountBalance(
      accountId: accountId,
      date: state.serverDate,
    );

    accountBalance.when(
      success: (data) {
        if (mounted) {
          // 非同期実行のため、すでに画面が閉じられているおそれがある
          // したがって、mountedで判定する
          final newAccounts = state.bankAccounts
              .overwriteBalanceDetails(data.accounts)
              .sortedByDisplayOrder();

          state = state.copyWith(bankAccounts: newAccounts);
        }
      },
      failure: (error) {
        if (mounted) {
          final newAccounts = state.bankAccounts
              .overwriteError(accountId, error)
              .sortedByDisplayOrder();

          state = state.copyWith(bankAccounts: newAccounts);
        }
      },
    );
  }

  // 表示・非表示を切り替える
  Future<void> toggleBalanceVisibility() async {
    state = state.copyWith(isBalanceVisible: !state.isBalanceVisible);
    await bankAccountRepository.setBalanceVisible(state.isBalanceVisible);
  }

  // 表示・非表示のFlag読み込み
  Future<void> _loadBalanceVisibility() async {
    final isBalanceVisible = await bankAccountRepository.getBalanceVisible();
    state = state.copyWith(isBalanceVisible: isBalanceVisible);
  }

  // クレジットカードの表示・非表示を切り替える
  Future<void> toggleCreditCardVisibility() async {
    state = state.copyWith(
      isCreditCardExpenseVisible: !state.isCreditCardExpenseVisible,
    );
    await creditCardTransactionHistoryRepository
        .setCreditCardExpenseVisible(state.isCreditCardExpenseVisible);
  }

  // クレジットカードの表示・非表示のFlag読み込み
  Future<void> _loadCreditCardVisibility() async {
    final isCreditCardExpenseVisible =
        await creditCardTransactionHistoryRepository
            .getCreditCardExpenseVisible();
    state =
        state.copyWith(isCreditCardExpenseVisible: isCreditCardExpenseVisible);
  }

  /// freeeSSO中継用のURLを取得
  Future<String?> getUrlToFreeeSSOPage(
    String baseRedirectUrl,
    FreeeSSOTypeFromHome ssoType,
  ) async {
    // エラー情報をリセット
    state = state.copyWith(getUrlToFreeeSsoParamsError: null);
    final result = await loginRepository.getEncryptedCookie();
    return await result.when(
      success: (_) async {
        final redirectUrl = await _getSsoParams(baseRedirectUrl, ssoType);
        return redirectUrl;
      },
      failure: (error) {
        state = state.copyWith(
          getUrlToFreeeSsoParamsError: error.copyWith(
            clearErrorOnShowDialog: _clearGetFreeeSsoParamsError,
          ),
        );
        return null;
      },
    );
  }

  Future<String> _getSsoParams(
    String baseRedirectUrl,
    FreeeSSOTypeFromHome ssoType,
  ) async {
    late final String redirectUrl;
    final result = await bankAccountRepository.getFreeeSsoParams();
    result.when(
      success: (params) {
        final urlParams = _convertSsoTypeToUrlParams(
          ssoType,
          params,
        );
        redirectUrl = baseRedirectUrl + urlParams;
      },
      failure: (error) {
        state = state.copyWith(
          getUrlToFreeeSsoParamsError: error.copyWith(
            clearErrorOnShowDialog: _clearGetFreeeSsoParamsError,
          ),
        );
        redirectUrl = baseRedirectUrl;
      },
    );
    return redirectUrl;
  }

  /// freeeにSSOする為のパラメーターをクエリパラメータに変換する処理
  String _convertSsoTypeToUrlParams(
    // 暗号化Cookieに関してはURLLauncherのオプションにて付与
    FreeeSSOTypeFromHome ssoType,
    FreeeSsoParams params,
  ) {
    late final String loginType;
    late final String urlParams;
    switch (ssoType) {
      // partnerIDありの場合→従業員初回ログイン
      case FreeeSSOTypeFromHome.firstTime:
        loginType =
            params.isPartnerIdExists ? 'firstEmployeeLogin' : 'firstLogin';
        urlParams =
            '?loginType=$loginType&state=${params.state}&partnerId=${params.partnerId}';
      case FreeeSSOTypeFromHome.bankAccount:
        loginType = 'addBankAccountLogin';
        urlParams = '?loginType=$loginType';
      case FreeeSSOTypeFromHome.creditCard:
        loginType = 'addCreditCardLogin';
        urlParams = '?loginType=$loginType';

      // 金融機関連携期限切れの際にはクレカ・口座を区別しない
      case FreeeSSOTypeFromHome.expirationOfFinanceLinkage:
        loginType = 'expirationOfFinanceLinkage';
        urlParams = '?loginType=$loginType';
    }
    return urlParams;
  }

  /// freee再連携用のURLを取得
  Future<String?> getUrlToFreeeReLinkPage() async {
    // エラー情報をリセット
    state = state.copyWith(
      getUrlToFreeeSsoParamsError: null,
      isOnFreeeReAlignment: true,
    );
    // 暗号化Cookieの最新化
    final result = await loginRepository.getEncryptedCookie();
    return await result.when(
      success: (_) async {
        final reAlignmentUrl = await _getFreeeRealignmentUrl();
        return reAlignmentUrl;
      },
      failure: (error) {
        state = state.copyWith(
          getUrlToFreeeSsoParamsError: error.copyWith(
            clearErrorOnShowDialog: _clearGetFreeeSsoParamsError,
          ),
        );
        return null;
      },
    );
  }

  Future<String?> _getFreeeRealignmentUrl() async {
    final result = await bankAccountRepository.getUrlToFreeeReLinkPage();
    return result.when(
      success: (redirectUrl) {
        return redirectUrl;
      },
      failure: (error) {
        state = state.copyWith(
          getUrlToFreeeSsoParamsError: error.copyWith(
            clearErrorOnShowDialog: _clearGetFreeeSsoParamsError,
          ),
        );
        return null;
      },
    );
  }

  // freee連携の有無を取得
  Future<void> _loadFreeeLinkStatus() async {
    // エラー情報をリセット
    state = state.copyWith(freeeLinkError: null);

    final freeeLinkStatus = await bankAccountRepository.getFreeeLinkStatus();
    await freeeLinkStatus.when(
      success: (isLinked) async {
        state = state.copyWith(isFreeeLinked: isLinked);
      },
      failure: (error) {
        state = state.copyWith(
          freeeLinkError: error.copyWith(
            isShowOnDialog: false,
          ),
        );
      },
    );
  }

  /// freee口座情報を取得する処理
  Future<void> getFreeeAccountBalance(InquiryCategory inquiryCategory) async {
    // 口座情報取得の場合、フラグを変更
    if (inquiryCategory == InquiryCategory.bankAccount) {
      onLoadOtherBanksStarted();
    }
    // クレジットカード情報取得の際、フラグを変更
    if (inquiryCategory == InquiryCategory.creditCard) {
      final loadingCreditCardExpenses = state.freeeCreditCardExpenses
          .map((e) => e.copyWith(isLoading: true))
          .toList();
      state =
          state.copyWith(freeeCreditCardExpenses: loadingCreditCardExpenses);
    }
    // エラー情報をリセット
    state = state.copyWith(
      freeeBankAccountError: null,
      freeeExpirationFlag: false,
    );

    final freeeWalletables =
        await bankAccountRepository.getFreeeAccountBalance(inquiryCategory);

    await freeeWalletables.when(
      success: (freeeWalletables) async {
        // クレジットカードと他行連携口座を分岐して配列に格納
        if (inquiryCategory == InquiryCategory.bankAccount) {
          //freeeから取得した口座情報をSMBC口座情報の保持形式に変換する
          final balanceAccountsOfOtherBanks =
              freeeWalletables.walletables.convertToBankAccount();
          state =
              state.copyWith(otherBankAccounts: balanceAccountsOfOtherBanks);
          return;
        }
        if (inquiryCategory == InquiryCategory.creditCard) {
          // クレジットカード情報一覧をCreditCardExpense型に変換
          final creditCardExpenses =
              freeeWalletables.walletables.convertToFreeeCreditCardExpense();

          state = state.copyWith(freeeCreditCardExpenses: creditCardExpenses);
          // 不足している情報を取得
          await _getCreditCardExpenses(creditCardExpenses);
          return;
        }
      },
      failure: (error) {
        final isErrorInfoForReAlignment =
            ErrorInfo.freeeNeedsReAlignmentErrorCode.contains(error.code);
        if (isErrorInfoForReAlignment) {
          state = state.copyWith(freeeExpirationFlag: true);
        }
        state = state.copyWith(
          freeeBankAccountError: error.copyWith(isShowOnDialog: false),
          // エラー情報のカードを表示するためリストにlength1を持たせる
          otherBankAccounts: [
            const BankAccount.withBalanceDetail(
              baseInfo: BankAccountBaseInfo(),
              displayConfig: DisplayConfig(
                accountId: '',
                displayOrder: 1,
                isHidden: false,
                accountApiType: AccountApiType.freee,
              ),
              balanceDetail: BankAccountBalanceDetail(),
            ) as BankAccountWithBalanceDetail,
          ],
          //エラーを明示的にするため、idには存在しない値を使用
          freeeCreditCardExpenses: [
            CreditCardExpense(
              id: -0,
              baseDateTime: '',
              creditCardName: '',
              isLoading: false,
            ),
          ],
        );
      },
    );
    // 口座情報取得の場合、ロードを中止
    if (inquiryCategory == InquiryCategory.bankAccount) {
      _onLoadOtherBanksFinished();
    }
  }

  /// リフレッシュボタン押下時に利用する、口座残高取得処理
  Future<void> refresh(String? accountId) async {
    await _singleLoadAccountBalance(accountId);
  }

  /// 口座表示設定画面から戻った時に呼び出す(表示設定が変更可能なのは口座のみであるため、クレジットカードは更新対象外)
  void refreshFromDisplayConfig() {
    _updateAnserInfo();
    _loadOtherBankAccountBalance();
  }

  Future<void> _updateAnserInfo() async {
    await _loadAccountSettings();
    await _loadAccountBalance();
    await _loadTransactionDailyTotal();
  }

  /// クレジットカード情報を受け取り、該当するクレジットカードの使用額取得情報を付与
  Future<void> _getCreditCardExpenses(
    List<CreditCardExpense> creditCardExpense,
  ) async {
    for (int i = 0; i < creditCardExpense.length; i++) {
      // すでに画面が閉じられている場合は終了
      if (!mounted) break;

      // 性能向上のため、クレジットカード使用額の読み込みは非同期にて実施
      unawaited(_updateSingleCreditCardExpense(i, creditCardExpense[i]));
    }
  }

  /// クレジットカード使用額更新用メソッド
  Future<void> _updateSingleCreditCardExpense(
    int elementNumber,
    CreditCardExpense creditCardExpense,
  ) async {
    // 現在のリストをコピーし、該当カードをロード状態に
    final freeeCreditCardExpenses =
        List<CreditCardExpense>.from(state.freeeCreditCardExpenses);

    freeeCreditCardExpenses[elementNumber] =
        creditCardExpense.copyWith(isLoading: true);
    state = state.copyWith(
      freeeCreditCardExpenses: freeeCreditCardExpenses,
    );

    final freeeTransactionTotalResponse =
        await bankAccountRepository.getFreeeTotalExpense(creditCardExpense.id);

    // APIから取得した情報の格納
    freeeTransactionTotalResponse.when(
      success: (freeeTransactionTotalResponse) {
        if (mounted) {
          // 非同期実行のため、画面インスタンスが失われているおそれがある
          // したがって、mountedフラグで判定する
          final updatedFreeeCreditCardExpenses =
              List<CreditCardExpense>.from(state.freeeCreditCardExpenses);

          //引数で受け取ったカードの状態を更新
          //配列に格納
          updatedFreeeCreditCardExpenses[elementNumber] =
              creditCardExpense.copyWith(
            startDate: freeeTransactionTotalResponse.startDate,
            endDate: freeeTransactionTotalResponse.endDate,
            totalExpense: freeeTransactionTotalResponse.totalExpense,
            baseDateTime: freeeTransactionTotalResponse.serverDateTime ?? 'ー',
            isLoading: false,
          );

          state = state.copyWith(
            freeeCreditCardExpenses: updatedFreeeCreditCardExpenses,
          );
        }
      },
      failure: (error) {
        if (creditCardExpense.isFreeeReConnectFinancialInstitution) {
          final updatedFreeeCreditCardExpenses =
              List<CreditCardExpense>.from(state.freeeCreditCardExpenses);

          //引数で受け取ったカードの状態を更新
          //配列に格納
          //金融機関再連携エラーの際はbaseDateTimeが取得できないため、baseDateTimeは'ー'にする
          updatedFreeeCreditCardExpenses[elementNumber] =
              creditCardExpense.copyWith(
            totalExpense: null,
            baseDateTime: 'ー',
            isLoading: false,
          );

          state = state.copyWith(
            freeeCreditCardExpenses: updatedFreeeCreditCardExpenses,
          );
          return;
        }

        final errorFreeeCreditCardExpenses =
            List<CreditCardExpense>.from(state.freeeCreditCardExpenses);

        //引数で受け取ったカードの状態を更新
        //配列に格納
        errorFreeeCreditCardExpenses[elementNumber] =
            creditCardExpense.copyWith(
          freeeCreditCardError: error.copyWith(isShowOnDialog: false),
          isLoading: false,
        );

        state = state.copyWith(
          freeeCreditCardExpenses: errorFreeeCreditCardExpenses,
        );
      },
    );
  }

  /// 更新ボタン押下時のメソッド(クレジットカードエラー時)
  Future<void> refreshAllFreeCreditCardWalletables() async {
    await getFreeeAccountBalance(InquiryCategory.creditCard);
  }

  /// 更新ボタン押下時のメソッド(他行口座残高更新時)
  Future<void> refreshAllFreeBankAccount() async {
    await getFreeeAccountBalance(InquiryCategory.bankAccount);
  }

  /// 更新ボタン押下時のメソッド(クレジットカード正常系時)
  Future<void> refreshCreditCardExpense(
    int elementNumber,
    CreditCardExpense creditCardExpense,
  ) async {
    await _updateSingleCreditCardExpense(elementNumber, creditCardExpense);
  }

  Future<String?> createAuthScreenInfo() async {
    // 認可画面呼び出し情報生成
    final result = await loginRepository.createAuthScreenInfo();
    return result.when(
      success: (fromEbizParam) => fromEbizParam,
      failure: (error) {
        state = state.copyWith(
          onDialogError: error,
        );
        return null;
      },
    );
  }

  void _clearGetFreeeSsoParamsError() {
    state = state.copyWith(getUrlToFreeeSsoParamsError: null);
  }

  /// freee連携APIの実行
  Future<void> linkToFreee(
    String code,
    String stateFromFreee, [
    bool isFirstLinkage = true,
  ]) async {
    final res = await bankAccountRepository.linkToFreee(
      code: code,
      stateFromFreee: stateFromFreee,
    );
    res.when(
      success: (_) {
        // freeeトークン取得状況を種別ごとの連携完了に
        state = state.copyWith(
          freeeTokenStatus: isFirstLinkage
              ? FreeeTokenStatus.firstLinkageCompleted
              : FreeeTokenStatus.noActionRequired,
          // 異常系表示の場合、エラー券面表示の為にダミー情報を格納するため、券面情報を空にする
          otherBankAccounts: [],
          freeeCreditCardExpenses: [],
        );
      },
      failure: (error) {
        // freeeトークン取得状況を「取得エラー」に更新
        state = state.copyWith(freeeTokenStatus: FreeeTokenStatus.error);
      },
    );
  }

  /// 取引先ID紐付け確認APIの実行
  Future<void> checkFreeeLinksByClientId() async {
    final res = await bankAccountRepository.checkFreeeLinksByClientId();
    res.when(
      success: (response) {
        state = state.copyWith(exists: response.exists);
      },
      failure: (error) {
        state = state.copyWith(checkFreeeLinksByClientIdError: error);
      },
    );
  }

  Future<void> clearFreeeLinksByClientIdError() async {
    state = state.copyWith(
      checkFreeeLinksByClientIdError: null,
    );
  }

  /// トークン取得状況を「処理不要」に更新するメソッド
  void updateFreeeTokenStatusToNoActionRequired() {
    state = state.copyWith(
      freeeTokenStatus: FreeeTokenStatus.noActionRequired,
      isOnFreeeReAlignment: false,
    );
  }

  /// お知らせ情報を取得する
  Future<void> _getAnnouncementInfo() async {
    final res = await announcementInfoRepository.getAnnouncementInfo();

    res.when(
      success: (content) {
        state = state.copyWith(announcementInfo: content);
      },
      failure: (_) {
        // エラー時はお知らせ情報にnullをセットする
        state = state.copyWith(announcementInfo: null);
      },
    );
  }

  // クレジットカード利用明細を表示するクレジットカード情報を判定する
  int getCreditCardStatementInquiryIndex() {
    // ループ上限（カード枚数最大100枚のため）
    const maxLoopLength = 100;
    // クレジットカード情報のインデックス（初期値は1枚目を指定）
    const creditCardStatementInquiryIndex = 0;
    // クレジットカード情報が全くない場合に返すインデックス
    const allErrorIndex = -1;

    if (state.freeeCreditCardExpenses[creditCardStatementInquiryIndex]
        .isFreeeReConnectFinancialInstitution) {
      for (int itemIndex = 0;
          (itemIndex < state.freeeCreditCardExpenses.length ||
              maxLoopLength < state.freeeCreditCardExpenses.length);
          itemIndex++) {
        if (!state.freeeCreditCardExpenses[itemIndex]
            .isFreeeReConnectFinancialInstitution) {
          return creditCardStatementInquiryIndex + itemIndex;
        }
      }

      return allErrorIndex;
    }

    return creditCardStatementInquiryIndex;
  }
}
