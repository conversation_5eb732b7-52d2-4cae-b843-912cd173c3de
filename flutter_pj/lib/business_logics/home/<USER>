// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeScreenState {
  /// ローディング管理フラグ(全体)
  bool get isLoading => throw _privateConstructorUsedError;

  /// ローディング管理フラグ(他行口座)
  bool get isLoadingOtherBanks => throw _privateConstructorUsedError;

  /// 口座情報の非同期読み込みフラグ
  bool get isAccountBalanceAsyncLoading => throw _privateConstructorUsedError;

  /// 自行の口座情報
  List<BankAccount> get bankAccounts => throw _privateConstructorUsedError;

  /// 他行の口座情報
  List<BankAccountWithBalanceDetail> get otherBankAccounts =>
      throw _privateConstructorUsedError;

  /// クレジットカード利用情報
  List<CreditCardExpense> get freeeCreditCardExpenses =>
      throw _privateConstructorUsedError;

  /// 入出金日時合計額情報
  List<TransactionDailyTotal> get transactionDailyTotal =>
      throw _privateConstructorUsedError;

  /// 情報取得日付
  String get serverDate => throw _privateConstructorUsedError;

  /// 残高公開フラグ
  bool get isBalanceVisible => throw _privateConstructorUsedError;

  /// クレジットカード利用金額公開フラグ
  bool get isCreditCardExpenseVisible => throw _privateConstructorUsedError;

  /// freee連携フラグ
  bool get isFreeeLinked => throw _privateConstructorUsedError;

  /// freee連携期限切れフラグ
  /// HACK コチラのフラグはVMの処理に影響を与えるものではない為、フラグをViewSettingsに移管させる
  bool get freeeExpirationFlag => throw _privateConstructorUsedError;

  /// web21再認可フラグ
  bool get reAuthorizationFlag => throw _privateConstructorUsedError;

  /// 利用者情報0件エラー
  bool get isNoAccountError => throw _privateConstructorUsedError;

  /// freee再連携中かを表現するフラグ
  bool get isOnFreeeReAlignment => throw _privateConstructorUsedError;

  /// 取引先ID(clientID)に紐づくデータ有無フラグ 紐づくデータがある場合はtrue,ない場合はfalse
  bool get exists => throw _privateConstructorUsedError;

  /// freeeのアクセストークンの状態
  FreeeTokenStatus get freeeTokenStatus => throw _privateConstructorUsedError;

  /// 選択したクレジットカードのindex
  int? get selectCreditCardExpenseIndex => throw _privateConstructorUsedError;

  /// お知らせ情報
  String? get announcementInfo => throw _privateConstructorUsedError;

  /// 利用者情報エラー
  AppError? get userAccountError => throw _privateConstructorUsedError;

  /// 入出金日時取得エラー
  AppError? get transactionDailyError => throw _privateConstructorUsedError;

  /// freee連携リンク取得エラー
  AppError? get getUrlToFreeeSsoParamsError =>
      throw _privateConstructorUsedError;

  /// freee連携エラー
  AppError? get freeeLinkError => throw _privateConstructorUsedError;

  /// freee他行口座エラー
  AppError? get freeeBankAccountError => throw _privateConstructorUsedError;

  /// 口座表示設定エラー
  AppError? get accountSettingsError => throw _privateConstructorUsedError;

  /// アクセストークンエラー
  AppError? get tokenError => throw _privateConstructorUsedError;

  /// web21認可エラー
  AppError? get authError => throw _privateConstructorUsedError;

  /// ダイアログ上に表示するエラー
  AppError? get onDialogError => throw _privateConstructorUsedError;

  /// 取引先ID紐付け確認エラー
  AppError? get checkFreeeLinksByClientIdError =>
      throw _privateConstructorUsedError;

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeScreenStateCopyWith<HomeScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeScreenStateCopyWith<$Res> {
  factory $HomeScreenStateCopyWith(
          HomeScreenState value, $Res Function(HomeScreenState) then) =
      _$HomeScreenStateCopyWithImpl<$Res, HomeScreenState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isLoadingOtherBanks,
      bool isAccountBalanceAsyncLoading,
      List<BankAccount> bankAccounts,
      List<BankAccountWithBalanceDetail> otherBankAccounts,
      List<CreditCardExpense> freeeCreditCardExpenses,
      List<TransactionDailyTotal> transactionDailyTotal,
      String serverDate,
      bool isBalanceVisible,
      bool isCreditCardExpenseVisible,
      bool isFreeeLinked,
      bool freeeExpirationFlag,
      bool reAuthorizationFlag,
      bool isNoAccountError,
      bool isOnFreeeReAlignment,
      bool exists,
      FreeeTokenStatus freeeTokenStatus,
      int? selectCreditCardExpenseIndex,
      String? announcementInfo,
      AppError? userAccountError,
      AppError? transactionDailyError,
      AppError? getUrlToFreeeSsoParamsError,
      AppError? freeeLinkError,
      AppError? freeeBankAccountError,
      AppError? accountSettingsError,
      AppError? tokenError,
      AppError? authError,
      AppError? onDialogError,
      AppError? checkFreeeLinksByClientIdError});

  $AppErrorCopyWith<$Res>? get userAccountError;
  $AppErrorCopyWith<$Res>? get transactionDailyError;
  $AppErrorCopyWith<$Res>? get getUrlToFreeeSsoParamsError;
  $AppErrorCopyWith<$Res>? get freeeLinkError;
  $AppErrorCopyWith<$Res>? get freeeBankAccountError;
  $AppErrorCopyWith<$Res>? get accountSettingsError;
  $AppErrorCopyWith<$Res>? get tokenError;
  $AppErrorCopyWith<$Res>? get authError;
  $AppErrorCopyWith<$Res>? get onDialogError;
  $AppErrorCopyWith<$Res>? get checkFreeeLinksByClientIdError;
}

/// @nodoc
class _$HomeScreenStateCopyWithImpl<$Res, $Val extends HomeScreenState>
    implements $HomeScreenStateCopyWith<$Res> {
  _$HomeScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoadingOtherBanks = null,
    Object? isAccountBalanceAsyncLoading = null,
    Object? bankAccounts = null,
    Object? otherBankAccounts = null,
    Object? freeeCreditCardExpenses = null,
    Object? transactionDailyTotal = null,
    Object? serverDate = null,
    Object? isBalanceVisible = null,
    Object? isCreditCardExpenseVisible = null,
    Object? isFreeeLinked = null,
    Object? freeeExpirationFlag = null,
    Object? reAuthorizationFlag = null,
    Object? isNoAccountError = null,
    Object? isOnFreeeReAlignment = null,
    Object? exists = null,
    Object? freeeTokenStatus = null,
    Object? selectCreditCardExpenseIndex = freezed,
    Object? announcementInfo = freezed,
    Object? userAccountError = freezed,
    Object? transactionDailyError = freezed,
    Object? getUrlToFreeeSsoParamsError = freezed,
    Object? freeeLinkError = freezed,
    Object? freeeBankAccountError = freezed,
    Object? accountSettingsError = freezed,
    Object? tokenError = freezed,
    Object? authError = freezed,
    Object? onDialogError = freezed,
    Object? checkFreeeLinksByClientIdError = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingOtherBanks: null == isLoadingOtherBanks
          ? _value.isLoadingOtherBanks
          : isLoadingOtherBanks // ignore: cast_nullable_to_non_nullable
              as bool,
      isAccountBalanceAsyncLoading: null == isAccountBalanceAsyncLoading
          ? _value.isAccountBalanceAsyncLoading
          : isAccountBalanceAsyncLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      bankAccounts: null == bankAccounts
          ? _value.bankAccounts
          : bankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      otherBankAccounts: null == otherBankAccounts
          ? _value.otherBankAccounts
          : otherBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccountWithBalanceDetail>,
      freeeCreditCardExpenses: null == freeeCreditCardExpenses
          ? _value.freeeCreditCardExpenses
          : freeeCreditCardExpenses // ignore: cast_nullable_to_non_nullable
              as List<CreditCardExpense>,
      transactionDailyTotal: null == transactionDailyTotal
          ? _value.transactionDailyTotal
          : transactionDailyTotal // ignore: cast_nullable_to_non_nullable
              as List<TransactionDailyTotal>,
      serverDate: null == serverDate
          ? _value.serverDate
          : serverDate // ignore: cast_nullable_to_non_nullable
              as String,
      isBalanceVisible: null == isBalanceVisible
          ? _value.isBalanceVisible
          : isBalanceVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreditCardExpenseVisible: null == isCreditCardExpenseVisible
          ? _value.isCreditCardExpenseVisible
          : isCreditCardExpenseVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      isFreeeLinked: null == isFreeeLinked
          ? _value.isFreeeLinked
          : isFreeeLinked // ignore: cast_nullable_to_non_nullable
              as bool,
      freeeExpirationFlag: null == freeeExpirationFlag
          ? _value.freeeExpirationFlag
          : freeeExpirationFlag // ignore: cast_nullable_to_non_nullable
              as bool,
      reAuthorizationFlag: null == reAuthorizationFlag
          ? _value.reAuthorizationFlag
          : reAuthorizationFlag // ignore: cast_nullable_to_non_nullable
              as bool,
      isNoAccountError: null == isNoAccountError
          ? _value.isNoAccountError
          : isNoAccountError // ignore: cast_nullable_to_non_nullable
              as bool,
      isOnFreeeReAlignment: null == isOnFreeeReAlignment
          ? _value.isOnFreeeReAlignment
          : isOnFreeeReAlignment // ignore: cast_nullable_to_non_nullable
              as bool,
      exists: null == exists
          ? _value.exists
          : exists // ignore: cast_nullable_to_non_nullable
              as bool,
      freeeTokenStatus: null == freeeTokenStatus
          ? _value.freeeTokenStatus
          : freeeTokenStatus // ignore: cast_nullable_to_non_nullable
              as FreeeTokenStatus,
      selectCreditCardExpenseIndex: freezed == selectCreditCardExpenseIndex
          ? _value.selectCreditCardExpenseIndex
          : selectCreditCardExpenseIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      announcementInfo: freezed == announcementInfo
          ? _value.announcementInfo
          : announcementInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      userAccountError: freezed == userAccountError
          ? _value.userAccountError
          : userAccountError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      transactionDailyError: freezed == transactionDailyError
          ? _value.transactionDailyError
          : transactionDailyError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      getUrlToFreeeSsoParamsError: freezed == getUrlToFreeeSsoParamsError
          ? _value.getUrlToFreeeSsoParamsError
          : getUrlToFreeeSsoParamsError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      freeeLinkError: freezed == freeeLinkError
          ? _value.freeeLinkError
          : freeeLinkError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      freeeBankAccountError: freezed == freeeBankAccountError
          ? _value.freeeBankAccountError
          : freeeBankAccountError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      accountSettingsError: freezed == accountSettingsError
          ? _value.accountSettingsError
          : accountSettingsError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      tokenError: freezed == tokenError
          ? _value.tokenError
          : tokenError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      authError: freezed == authError
          ? _value.authError
          : authError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      onDialogError: freezed == onDialogError
          ? _value.onDialogError
          : onDialogError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      checkFreeeLinksByClientIdError: freezed == checkFreeeLinksByClientIdError
          ? _value.checkFreeeLinksByClientIdError
          : checkFreeeLinksByClientIdError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get userAccountError {
    if (_value.userAccountError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.userAccountError!, (value) {
      return _then(_value.copyWith(userAccountError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get transactionDailyError {
    if (_value.transactionDailyError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.transactionDailyError!, (value) {
      return _then(_value.copyWith(transactionDailyError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get getUrlToFreeeSsoParamsError {
    if (_value.getUrlToFreeeSsoParamsError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.getUrlToFreeeSsoParamsError!,
        (value) {
      return _then(_value.copyWith(getUrlToFreeeSsoParamsError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get freeeLinkError {
    if (_value.freeeLinkError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.freeeLinkError!, (value) {
      return _then(_value.copyWith(freeeLinkError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get freeeBankAccountError {
    if (_value.freeeBankAccountError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.freeeBankAccountError!, (value) {
      return _then(_value.copyWith(freeeBankAccountError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get accountSettingsError {
    if (_value.accountSettingsError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.accountSettingsError!, (value) {
      return _then(_value.copyWith(accountSettingsError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get tokenError {
    if (_value.tokenError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.tokenError!, (value) {
      return _then(_value.copyWith(tokenError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get authError {
    if (_value.authError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.authError!, (value) {
      return _then(_value.copyWith(authError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get onDialogError {
    if (_value.onDialogError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.onDialogError!, (value) {
      return _then(_value.copyWith(onDialogError: value) as $Val);
    });
  }

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get checkFreeeLinksByClientIdError {
    if (_value.checkFreeeLinksByClientIdError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.checkFreeeLinksByClientIdError!,
        (value) {
      return _then(
          _value.copyWith(checkFreeeLinksByClientIdError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HomeScreenStateImplCopyWith<$Res>
    implements $HomeScreenStateCopyWith<$Res> {
  factory _$$HomeScreenStateImplCopyWith(_$HomeScreenStateImpl value,
          $Res Function(_$HomeScreenStateImpl) then) =
      __$$HomeScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isLoadingOtherBanks,
      bool isAccountBalanceAsyncLoading,
      List<BankAccount> bankAccounts,
      List<BankAccountWithBalanceDetail> otherBankAccounts,
      List<CreditCardExpense> freeeCreditCardExpenses,
      List<TransactionDailyTotal> transactionDailyTotal,
      String serverDate,
      bool isBalanceVisible,
      bool isCreditCardExpenseVisible,
      bool isFreeeLinked,
      bool freeeExpirationFlag,
      bool reAuthorizationFlag,
      bool isNoAccountError,
      bool isOnFreeeReAlignment,
      bool exists,
      FreeeTokenStatus freeeTokenStatus,
      int? selectCreditCardExpenseIndex,
      String? announcementInfo,
      AppError? userAccountError,
      AppError? transactionDailyError,
      AppError? getUrlToFreeeSsoParamsError,
      AppError? freeeLinkError,
      AppError? freeeBankAccountError,
      AppError? accountSettingsError,
      AppError? tokenError,
      AppError? authError,
      AppError? onDialogError,
      AppError? checkFreeeLinksByClientIdError});

  @override
  $AppErrorCopyWith<$Res>? get userAccountError;
  @override
  $AppErrorCopyWith<$Res>? get transactionDailyError;
  @override
  $AppErrorCopyWith<$Res>? get getUrlToFreeeSsoParamsError;
  @override
  $AppErrorCopyWith<$Res>? get freeeLinkError;
  @override
  $AppErrorCopyWith<$Res>? get freeeBankAccountError;
  @override
  $AppErrorCopyWith<$Res>? get accountSettingsError;
  @override
  $AppErrorCopyWith<$Res>? get tokenError;
  @override
  $AppErrorCopyWith<$Res>? get authError;
  @override
  $AppErrorCopyWith<$Res>? get onDialogError;
  @override
  $AppErrorCopyWith<$Res>? get checkFreeeLinksByClientIdError;
}

/// @nodoc
class __$$HomeScreenStateImplCopyWithImpl<$Res>
    extends _$HomeScreenStateCopyWithImpl<$Res, _$HomeScreenStateImpl>
    implements _$$HomeScreenStateImplCopyWith<$Res> {
  __$$HomeScreenStateImplCopyWithImpl(
      _$HomeScreenStateImpl _value, $Res Function(_$HomeScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isLoadingOtherBanks = null,
    Object? isAccountBalanceAsyncLoading = null,
    Object? bankAccounts = null,
    Object? otherBankAccounts = null,
    Object? freeeCreditCardExpenses = null,
    Object? transactionDailyTotal = null,
    Object? serverDate = null,
    Object? isBalanceVisible = null,
    Object? isCreditCardExpenseVisible = null,
    Object? isFreeeLinked = null,
    Object? freeeExpirationFlag = null,
    Object? reAuthorizationFlag = null,
    Object? isNoAccountError = null,
    Object? isOnFreeeReAlignment = null,
    Object? exists = null,
    Object? freeeTokenStatus = null,
    Object? selectCreditCardExpenseIndex = freezed,
    Object? announcementInfo = freezed,
    Object? userAccountError = freezed,
    Object? transactionDailyError = freezed,
    Object? getUrlToFreeeSsoParamsError = freezed,
    Object? freeeLinkError = freezed,
    Object? freeeBankAccountError = freezed,
    Object? accountSettingsError = freezed,
    Object? tokenError = freezed,
    Object? authError = freezed,
    Object? onDialogError = freezed,
    Object? checkFreeeLinksByClientIdError = freezed,
  }) {
    return _then(_$HomeScreenStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoadingOtherBanks: null == isLoadingOtherBanks
          ? _value.isLoadingOtherBanks
          : isLoadingOtherBanks // ignore: cast_nullable_to_non_nullable
              as bool,
      isAccountBalanceAsyncLoading: null == isAccountBalanceAsyncLoading
          ? _value.isAccountBalanceAsyncLoading
          : isAccountBalanceAsyncLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      bankAccounts: null == bankAccounts
          ? _value._bankAccounts
          : bankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      otherBankAccounts: null == otherBankAccounts
          ? _value._otherBankAccounts
          : otherBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccountWithBalanceDetail>,
      freeeCreditCardExpenses: null == freeeCreditCardExpenses
          ? _value._freeeCreditCardExpenses
          : freeeCreditCardExpenses // ignore: cast_nullable_to_non_nullable
              as List<CreditCardExpense>,
      transactionDailyTotal: null == transactionDailyTotal
          ? _value._transactionDailyTotal
          : transactionDailyTotal // ignore: cast_nullable_to_non_nullable
              as List<TransactionDailyTotal>,
      serverDate: null == serverDate
          ? _value.serverDate
          : serverDate // ignore: cast_nullable_to_non_nullable
              as String,
      isBalanceVisible: null == isBalanceVisible
          ? _value.isBalanceVisible
          : isBalanceVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreditCardExpenseVisible: null == isCreditCardExpenseVisible
          ? _value.isCreditCardExpenseVisible
          : isCreditCardExpenseVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      isFreeeLinked: null == isFreeeLinked
          ? _value.isFreeeLinked
          : isFreeeLinked // ignore: cast_nullable_to_non_nullable
              as bool,
      freeeExpirationFlag: null == freeeExpirationFlag
          ? _value.freeeExpirationFlag
          : freeeExpirationFlag // ignore: cast_nullable_to_non_nullable
              as bool,
      reAuthorizationFlag: null == reAuthorizationFlag
          ? _value.reAuthorizationFlag
          : reAuthorizationFlag // ignore: cast_nullable_to_non_nullable
              as bool,
      isNoAccountError: null == isNoAccountError
          ? _value.isNoAccountError
          : isNoAccountError // ignore: cast_nullable_to_non_nullable
              as bool,
      isOnFreeeReAlignment: null == isOnFreeeReAlignment
          ? _value.isOnFreeeReAlignment
          : isOnFreeeReAlignment // ignore: cast_nullable_to_non_nullable
              as bool,
      exists: null == exists
          ? _value.exists
          : exists // ignore: cast_nullable_to_non_nullable
              as bool,
      freeeTokenStatus: null == freeeTokenStatus
          ? _value.freeeTokenStatus
          : freeeTokenStatus // ignore: cast_nullable_to_non_nullable
              as FreeeTokenStatus,
      selectCreditCardExpenseIndex: freezed == selectCreditCardExpenseIndex
          ? _value.selectCreditCardExpenseIndex
          : selectCreditCardExpenseIndex // ignore: cast_nullable_to_non_nullable
              as int?,
      announcementInfo: freezed == announcementInfo
          ? _value.announcementInfo
          : announcementInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      userAccountError: freezed == userAccountError
          ? _value.userAccountError
          : userAccountError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      transactionDailyError: freezed == transactionDailyError
          ? _value.transactionDailyError
          : transactionDailyError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      getUrlToFreeeSsoParamsError: freezed == getUrlToFreeeSsoParamsError
          ? _value.getUrlToFreeeSsoParamsError
          : getUrlToFreeeSsoParamsError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      freeeLinkError: freezed == freeeLinkError
          ? _value.freeeLinkError
          : freeeLinkError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      freeeBankAccountError: freezed == freeeBankAccountError
          ? _value.freeeBankAccountError
          : freeeBankAccountError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      accountSettingsError: freezed == accountSettingsError
          ? _value.accountSettingsError
          : accountSettingsError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      tokenError: freezed == tokenError
          ? _value.tokenError
          : tokenError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      authError: freezed == authError
          ? _value.authError
          : authError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      onDialogError: freezed == onDialogError
          ? _value.onDialogError
          : onDialogError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      checkFreeeLinksByClientIdError: freezed == checkFreeeLinksByClientIdError
          ? _value.checkFreeeLinksByClientIdError
          : checkFreeeLinksByClientIdError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$HomeScreenStateImpl implements _HomeScreenState {
  const _$HomeScreenStateImpl(
      {this.isLoading = false,
      this.isLoadingOtherBanks = false,
      this.isAccountBalanceAsyncLoading = false,
      final List<BankAccount> bankAccounts = const <BankAccount>[],
      final List<BankAccountWithBalanceDetail> otherBankAccounts =
          const <BankAccountWithBalanceDetail>[],
      final List<CreditCardExpense> freeeCreditCardExpenses =
          const <CreditCardExpense>[],
      final List<TransactionDailyTotal> transactionDailyTotal =
          const <TransactionDailyTotal>[],
      this.serverDate = '',
      this.isBalanceVisible = true,
      this.isCreditCardExpenseVisible = true,
      this.isFreeeLinked = false,
      this.freeeExpirationFlag = false,
      this.reAuthorizationFlag = false,
      this.isNoAccountError = false,
      this.isOnFreeeReAlignment = false,
      this.exists = false,
      this.freeeTokenStatus = FreeeTokenStatus.noActionRequired,
      this.selectCreditCardExpenseIndex,
      this.announcementInfo,
      this.userAccountError,
      this.transactionDailyError,
      this.getUrlToFreeeSsoParamsError,
      this.freeeLinkError,
      this.freeeBankAccountError,
      this.accountSettingsError,
      this.tokenError,
      this.authError,
      this.onDialogError,
      this.checkFreeeLinksByClientIdError})
      : _bankAccounts = bankAccounts,
        _otherBankAccounts = otherBankAccounts,
        _freeeCreditCardExpenses = freeeCreditCardExpenses,
        _transactionDailyTotal = transactionDailyTotal;

  /// ローディング管理フラグ(全体)
  @override
  @JsonKey()
  final bool isLoading;

  /// ローディング管理フラグ(他行口座)
  @override
  @JsonKey()
  final bool isLoadingOtherBanks;

  /// 口座情報の非同期読み込みフラグ
  @override
  @JsonKey()
  final bool isAccountBalanceAsyncLoading;

  /// 自行の口座情報
  final List<BankAccount> _bankAccounts;

  /// 自行の口座情報
  @override
  @JsonKey()
  List<BankAccount> get bankAccounts {
    if (_bankAccounts is EqualUnmodifiableListView) return _bankAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_bankAccounts);
  }

  /// 他行の口座情報
  final List<BankAccountWithBalanceDetail> _otherBankAccounts;

  /// 他行の口座情報
  @override
  @JsonKey()
  List<BankAccountWithBalanceDetail> get otherBankAccounts {
    if (_otherBankAccounts is EqualUnmodifiableListView)
      return _otherBankAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_otherBankAccounts);
  }

  /// クレジットカード利用情報
  final List<CreditCardExpense> _freeeCreditCardExpenses;

  /// クレジットカード利用情報
  @override
  @JsonKey()
  List<CreditCardExpense> get freeeCreditCardExpenses {
    if (_freeeCreditCardExpenses is EqualUnmodifiableListView)
      return _freeeCreditCardExpenses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_freeeCreditCardExpenses);
  }

  /// 入出金日時合計額情報
  final List<TransactionDailyTotal> _transactionDailyTotal;

  /// 入出金日時合計額情報
  @override
  @JsonKey()
  List<TransactionDailyTotal> get transactionDailyTotal {
    if (_transactionDailyTotal is EqualUnmodifiableListView)
      return _transactionDailyTotal;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_transactionDailyTotal);
  }

  /// 情報取得日付
  @override
  @JsonKey()
  final String serverDate;

  /// 残高公開フラグ
  @override
  @JsonKey()
  final bool isBalanceVisible;

  /// クレジットカード利用金額公開フラグ
  @override
  @JsonKey()
  final bool isCreditCardExpenseVisible;

  /// freee連携フラグ
  @override
  @JsonKey()
  final bool isFreeeLinked;

  /// freee連携期限切れフラグ
  /// HACK コチラのフラグはVMの処理に影響を与えるものではない為、フラグをViewSettingsに移管させる
  @override
  @JsonKey()
  final bool freeeExpirationFlag;

  /// web21再認可フラグ
  @override
  @JsonKey()
  final bool reAuthorizationFlag;

  /// 利用者情報0件エラー
  @override
  @JsonKey()
  final bool isNoAccountError;

  /// freee再連携中かを表現するフラグ
  @override
  @JsonKey()
  final bool isOnFreeeReAlignment;

  /// 取引先ID(clientID)に紐づくデータ有無フラグ 紐づくデータがある場合はtrue,ない場合はfalse
  @override
  @JsonKey()
  final bool exists;

  /// freeeのアクセストークンの状態
  @override
  @JsonKey()
  final FreeeTokenStatus freeeTokenStatus;

  /// 選択したクレジットカードのindex
  @override
  final int? selectCreditCardExpenseIndex;

  /// お知らせ情報
  @override
  final String? announcementInfo;

  /// 利用者情報エラー
  @override
  final AppError? userAccountError;

  /// 入出金日時取得エラー
  @override
  final AppError? transactionDailyError;

  /// freee連携リンク取得エラー
  @override
  final AppError? getUrlToFreeeSsoParamsError;

  /// freee連携エラー
  @override
  final AppError? freeeLinkError;

  /// freee他行口座エラー
  @override
  final AppError? freeeBankAccountError;

  /// 口座表示設定エラー
  @override
  final AppError? accountSettingsError;

  /// アクセストークンエラー
  @override
  final AppError? tokenError;

  /// web21認可エラー
  @override
  final AppError? authError;

  /// ダイアログ上に表示するエラー
  @override
  final AppError? onDialogError;

  /// 取引先ID紐付け確認エラー
  @override
  final AppError? checkFreeeLinksByClientIdError;

  @override
  String toString() {
    return 'HomeScreenState(isLoading: $isLoading, isLoadingOtherBanks: $isLoadingOtherBanks, isAccountBalanceAsyncLoading: $isAccountBalanceAsyncLoading, bankAccounts: $bankAccounts, otherBankAccounts: $otherBankAccounts, freeeCreditCardExpenses: $freeeCreditCardExpenses, transactionDailyTotal: $transactionDailyTotal, serverDate: $serverDate, isBalanceVisible: $isBalanceVisible, isCreditCardExpenseVisible: $isCreditCardExpenseVisible, isFreeeLinked: $isFreeeLinked, freeeExpirationFlag: $freeeExpirationFlag, reAuthorizationFlag: $reAuthorizationFlag, isNoAccountError: $isNoAccountError, isOnFreeeReAlignment: $isOnFreeeReAlignment, exists: $exists, freeeTokenStatus: $freeeTokenStatus, selectCreditCardExpenseIndex: $selectCreditCardExpenseIndex, announcementInfo: $announcementInfo, userAccountError: $userAccountError, transactionDailyError: $transactionDailyError, getUrlToFreeeSsoParamsError: $getUrlToFreeeSsoParamsError, freeeLinkError: $freeeLinkError, freeeBankAccountError: $freeeBankAccountError, accountSettingsError: $accountSettingsError, tokenError: $tokenError, authError: $authError, onDialogError: $onDialogError, checkFreeeLinksByClientIdError: $checkFreeeLinksByClientIdError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeScreenStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isLoadingOtherBanks, isLoadingOtherBanks) ||
                other.isLoadingOtherBanks == isLoadingOtherBanks) &&
            (identical(other.isAccountBalanceAsyncLoading, isAccountBalanceAsyncLoading) ||
                other.isAccountBalanceAsyncLoading ==
                    isAccountBalanceAsyncLoading) &&
            const DeepCollectionEquality()
                .equals(other._bankAccounts, _bankAccounts) &&
            const DeepCollectionEquality()
                .equals(other._otherBankAccounts, _otherBankAccounts) &&
            const DeepCollectionEquality().equals(
                other._freeeCreditCardExpenses, _freeeCreditCardExpenses) &&
            const DeepCollectionEquality()
                .equals(other._transactionDailyTotal, _transactionDailyTotal) &&
            (identical(other.serverDate, serverDate) ||
                other.serverDate == serverDate) &&
            (identical(other.isBalanceVisible, isBalanceVisible) ||
                other.isBalanceVisible == isBalanceVisible) &&
            (identical(other.isCreditCardExpenseVisible, isCreditCardExpenseVisible) ||
                other.isCreditCardExpenseVisible ==
                    isCreditCardExpenseVisible) &&
            (identical(other.isFreeeLinked, isFreeeLinked) ||
                other.isFreeeLinked == isFreeeLinked) &&
            (identical(other.freeeExpirationFlag, freeeExpirationFlag) ||
                other.freeeExpirationFlag == freeeExpirationFlag) &&
            (identical(other.reAuthorizationFlag, reAuthorizationFlag) ||
                other.reAuthorizationFlag == reAuthorizationFlag) &&
            (identical(other.isNoAccountError, isNoAccountError) ||
                other.isNoAccountError == isNoAccountError) &&
            (identical(other.isOnFreeeReAlignment, isOnFreeeReAlignment) ||
                other.isOnFreeeReAlignment == isOnFreeeReAlignment) &&
            (identical(other.exists, exists) || other.exists == exists) &&
            (identical(other.freeeTokenStatus, freeeTokenStatus) ||
                other.freeeTokenStatus == freeeTokenStatus) &&
            (identical(other.selectCreditCardExpenseIndex, selectCreditCardExpenseIndex) ||
                other.selectCreditCardExpenseIndex ==
                    selectCreditCardExpenseIndex) &&
            (identical(other.announcementInfo, announcementInfo) ||
                other.announcementInfo == announcementInfo) &&
            (identical(other.userAccountError, userAccountError) ||
                other.userAccountError == userAccountError) &&
            (identical(other.transactionDailyError, transactionDailyError) ||
                other.transactionDailyError == transactionDailyError) &&
            (identical(other.getUrlToFreeeSsoParamsError, getUrlToFreeeSsoParamsError) ||
                other.getUrlToFreeeSsoParamsError ==
                    getUrlToFreeeSsoParamsError) &&
            (identical(other.freeeLinkError, freeeLinkError) ||
                other.freeeLinkError == freeeLinkError) &&
            (identical(other.freeeBankAccountError, freeeBankAccountError) ||
                other.freeeBankAccountError == freeeBankAccountError) &&
            (identical(other.accountSettingsError, accountSettingsError) ||
                other.accountSettingsError == accountSettingsError) &&
            (identical(other.tokenError, tokenError) ||
                other.tokenError == tokenError) &&
            (identical(other.authError, authError) ||
                other.authError == authError) &&
            (identical(other.onDialogError, onDialogError) || other.onDialogError == onDialogError) &&
            (identical(other.checkFreeeLinksByClientIdError, checkFreeeLinksByClientIdError) || other.checkFreeeLinksByClientIdError == checkFreeeLinksByClientIdError));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        isLoading,
        isLoadingOtherBanks,
        isAccountBalanceAsyncLoading,
        const DeepCollectionEquality().hash(_bankAccounts),
        const DeepCollectionEquality().hash(_otherBankAccounts),
        const DeepCollectionEquality().hash(_freeeCreditCardExpenses),
        const DeepCollectionEquality().hash(_transactionDailyTotal),
        serverDate,
        isBalanceVisible,
        isCreditCardExpenseVisible,
        isFreeeLinked,
        freeeExpirationFlag,
        reAuthorizationFlag,
        isNoAccountError,
        isOnFreeeReAlignment,
        exists,
        freeeTokenStatus,
        selectCreditCardExpenseIndex,
        announcementInfo,
        userAccountError,
        transactionDailyError,
        getUrlToFreeeSsoParamsError,
        freeeLinkError,
        freeeBankAccountError,
        accountSettingsError,
        tokenError,
        authError,
        onDialogError,
        checkFreeeLinksByClientIdError
      ]);

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeScreenStateImplCopyWith<_$HomeScreenStateImpl> get copyWith =>
      __$$HomeScreenStateImplCopyWithImpl<_$HomeScreenStateImpl>(
          this, _$identity);
}

abstract class _HomeScreenState implements HomeScreenState {
  const factory _HomeScreenState(
      {final bool isLoading,
      final bool isLoadingOtherBanks,
      final bool isAccountBalanceAsyncLoading,
      final List<BankAccount> bankAccounts,
      final List<BankAccountWithBalanceDetail> otherBankAccounts,
      final List<CreditCardExpense> freeeCreditCardExpenses,
      final List<TransactionDailyTotal> transactionDailyTotal,
      final String serverDate,
      final bool isBalanceVisible,
      final bool isCreditCardExpenseVisible,
      final bool isFreeeLinked,
      final bool freeeExpirationFlag,
      final bool reAuthorizationFlag,
      final bool isNoAccountError,
      final bool isOnFreeeReAlignment,
      final bool exists,
      final FreeeTokenStatus freeeTokenStatus,
      final int? selectCreditCardExpenseIndex,
      final String? announcementInfo,
      final AppError? userAccountError,
      final AppError? transactionDailyError,
      final AppError? getUrlToFreeeSsoParamsError,
      final AppError? freeeLinkError,
      final AppError? freeeBankAccountError,
      final AppError? accountSettingsError,
      final AppError? tokenError,
      final AppError? authError,
      final AppError? onDialogError,
      final AppError? checkFreeeLinksByClientIdError}) = _$HomeScreenStateImpl;

  /// ローディング管理フラグ(全体)
  @override
  bool get isLoading;

  /// ローディング管理フラグ(他行口座)
  @override
  bool get isLoadingOtherBanks;

  /// 口座情報の非同期読み込みフラグ
  @override
  bool get isAccountBalanceAsyncLoading;

  /// 自行の口座情報
  @override
  List<BankAccount> get bankAccounts;

  /// 他行の口座情報
  @override
  List<BankAccountWithBalanceDetail> get otherBankAccounts;

  /// クレジットカード利用情報
  @override
  List<CreditCardExpense> get freeeCreditCardExpenses;

  /// 入出金日時合計額情報
  @override
  List<TransactionDailyTotal> get transactionDailyTotal;

  /// 情報取得日付
  @override
  String get serverDate;

  /// 残高公開フラグ
  @override
  bool get isBalanceVisible;

  /// クレジットカード利用金額公開フラグ
  @override
  bool get isCreditCardExpenseVisible;

  /// freee連携フラグ
  @override
  bool get isFreeeLinked;

  /// freee連携期限切れフラグ
  /// HACK コチラのフラグはVMの処理に影響を与えるものではない為、フラグをViewSettingsに移管させる
  @override
  bool get freeeExpirationFlag;

  /// web21再認可フラグ
  @override
  bool get reAuthorizationFlag;

  /// 利用者情報0件エラー
  @override
  bool get isNoAccountError;

  /// freee再連携中かを表現するフラグ
  @override
  bool get isOnFreeeReAlignment;

  /// 取引先ID(clientID)に紐づくデータ有無フラグ 紐づくデータがある場合はtrue,ない場合はfalse
  @override
  bool get exists;

  /// freeeのアクセストークンの状態
  @override
  FreeeTokenStatus get freeeTokenStatus;

  /// 選択したクレジットカードのindex
  @override
  int? get selectCreditCardExpenseIndex;

  /// お知らせ情報
  @override
  String? get announcementInfo;

  /// 利用者情報エラー
  @override
  AppError? get userAccountError;

  /// 入出金日時取得エラー
  @override
  AppError? get transactionDailyError;

  /// freee連携リンク取得エラー
  @override
  AppError? get getUrlToFreeeSsoParamsError;

  /// freee連携エラー
  @override
  AppError? get freeeLinkError;

  /// freee他行口座エラー
  @override
  AppError? get freeeBankAccountError;

  /// 口座表示設定エラー
  @override
  AppError? get accountSettingsError;

  /// アクセストークンエラー
  @override
  AppError? get tokenError;

  /// web21認可エラー
  @override
  AppError? get authError;

  /// ダイアログ上に表示するエラー
  @override
  AppError? get onDialogError;

  /// 取引先ID紐付け確認エラー
  @override
  AppError? get checkFreeeLinksByClientIdError;

  /// Create a copy of HomeScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeScreenStateImplCopyWith<_$HomeScreenStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
