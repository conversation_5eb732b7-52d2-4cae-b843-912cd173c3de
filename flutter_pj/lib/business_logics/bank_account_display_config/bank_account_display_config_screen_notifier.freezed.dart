// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account_display_config_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BankAccountDisplayConfigState {
  List<BankAccount> get bankAccounts => throw _privateConstructorUsedError;
  List<BankAccount> get loadedBankAccounts =>
      throw _privateConstructorUsedError;
  List<BankAccount> get otherBankAccounts => throw _privateConstructorUsedError;
  List<BankAccount> get loadedOtherBankAccounts =>
      throw _privateConstructorUsedError;
  List<String?> get validationErrors => throw _privateConstructorUsedError;
  bool get isFreeeLinked => throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of BankAccountDisplayConfigState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BankAccountDisplayConfigStateCopyWith<BankAccountDisplayConfigState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountDisplayConfigStateCopyWith<$Res> {
  factory $BankAccountDisplayConfigStateCopyWith(
          BankAccountDisplayConfigState value,
          $Res Function(BankAccountDisplayConfigState) then) =
      _$BankAccountDisplayConfigStateCopyWithImpl<$Res,
          BankAccountDisplayConfigState>;
  @useResult
  $Res call(
      {List<BankAccount> bankAccounts,
      List<BankAccount> loadedBankAccounts,
      List<BankAccount> otherBankAccounts,
      List<BankAccount> loadedOtherBankAccounts,
      List<String?> validationErrors,
      bool isFreeeLinked,
      AppError? error});

  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$BankAccountDisplayConfigStateCopyWithImpl<$Res,
        $Val extends BankAccountDisplayConfigState>
    implements $BankAccountDisplayConfigStateCopyWith<$Res> {
  _$BankAccountDisplayConfigStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccountDisplayConfigState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccounts = null,
    Object? loadedBankAccounts = null,
    Object? otherBankAccounts = null,
    Object? loadedOtherBankAccounts = null,
    Object? validationErrors = null,
    Object? isFreeeLinked = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      bankAccounts: null == bankAccounts
          ? _value.bankAccounts
          : bankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      loadedBankAccounts: null == loadedBankAccounts
          ? _value.loadedBankAccounts
          : loadedBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      otherBankAccounts: null == otherBankAccounts
          ? _value.otherBankAccounts
          : otherBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      loadedOtherBankAccounts: null == loadedOtherBankAccounts
          ? _value.loadedOtherBankAccounts
          : loadedOtherBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      validationErrors: null == validationErrors
          ? _value.validationErrors
          : validationErrors // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      isFreeeLinked: null == isFreeeLinked
          ? _value.isFreeeLinked
          : isFreeeLinked // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of BankAccountDisplayConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BankAccountDisplayConfigStateImplCopyWith<$Res>
    implements $BankAccountDisplayConfigStateCopyWith<$Res> {
  factory _$$BankAccountDisplayConfigStateImplCopyWith(
          _$BankAccountDisplayConfigStateImpl value,
          $Res Function(_$BankAccountDisplayConfigStateImpl) then) =
      __$$BankAccountDisplayConfigStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<BankAccount> bankAccounts,
      List<BankAccount> loadedBankAccounts,
      List<BankAccount> otherBankAccounts,
      List<BankAccount> loadedOtherBankAccounts,
      List<String?> validationErrors,
      bool isFreeeLinked,
      AppError? error});

  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$BankAccountDisplayConfigStateImplCopyWithImpl<$Res>
    extends _$BankAccountDisplayConfigStateCopyWithImpl<$Res,
        _$BankAccountDisplayConfigStateImpl>
    implements _$$BankAccountDisplayConfigStateImplCopyWith<$Res> {
  __$$BankAccountDisplayConfigStateImplCopyWithImpl(
      _$BankAccountDisplayConfigStateImpl _value,
      $Res Function(_$BankAccountDisplayConfigStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountDisplayConfigState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccounts = null,
    Object? loadedBankAccounts = null,
    Object? otherBankAccounts = null,
    Object? loadedOtherBankAccounts = null,
    Object? validationErrors = null,
    Object? isFreeeLinked = null,
    Object? error = freezed,
  }) {
    return _then(_$BankAccountDisplayConfigStateImpl(
      bankAccounts: null == bankAccounts
          ? _value._bankAccounts
          : bankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      loadedBankAccounts: null == loadedBankAccounts
          ? _value._loadedBankAccounts
          : loadedBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      otherBankAccounts: null == otherBankAccounts
          ? _value._otherBankAccounts
          : otherBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      loadedOtherBankAccounts: null == loadedOtherBankAccounts
          ? _value._loadedOtherBankAccounts
          : loadedOtherBankAccounts // ignore: cast_nullable_to_non_nullable
              as List<BankAccount>,
      validationErrors: null == validationErrors
          ? _value._validationErrors
          : validationErrors // ignore: cast_nullable_to_non_nullable
              as List<String?>,
      isFreeeLinked: null == isFreeeLinked
          ? _value.isFreeeLinked
          : isFreeeLinked // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$BankAccountDisplayConfigStateImpl
    with DiagnosticableTreeMixin
    implements _BankAccountDisplayConfigState {
  const _$BankAccountDisplayConfigStateImpl(
      {final List<BankAccount> bankAccounts = const [],
      final List<BankAccount> loadedBankAccounts = const [],
      final List<BankAccount> otherBankAccounts = const [],
      final List<BankAccount> loadedOtherBankAccounts = const [],
      final List<String?> validationErrors = const [],
      this.isFreeeLinked = false,
      this.error})
      : _bankAccounts = bankAccounts,
        _loadedBankAccounts = loadedBankAccounts,
        _otherBankAccounts = otherBankAccounts,
        _loadedOtherBankAccounts = loadedOtherBankAccounts,
        _validationErrors = validationErrors;

  final List<BankAccount> _bankAccounts;
  @override
  @JsonKey()
  List<BankAccount> get bankAccounts {
    if (_bankAccounts is EqualUnmodifiableListView) return _bankAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_bankAccounts);
  }

  final List<BankAccount> _loadedBankAccounts;
  @override
  @JsonKey()
  List<BankAccount> get loadedBankAccounts {
    if (_loadedBankAccounts is EqualUnmodifiableListView)
      return _loadedBankAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_loadedBankAccounts);
  }

  final List<BankAccount> _otherBankAccounts;
  @override
  @JsonKey()
  List<BankAccount> get otherBankAccounts {
    if (_otherBankAccounts is EqualUnmodifiableListView)
      return _otherBankAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_otherBankAccounts);
  }

  final List<BankAccount> _loadedOtherBankAccounts;
  @override
  @JsonKey()
  List<BankAccount> get loadedOtherBankAccounts {
    if (_loadedOtherBankAccounts is EqualUnmodifiableListView)
      return _loadedOtherBankAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_loadedOtherBankAccounts);
  }

  final List<String?> _validationErrors;
  @override
  @JsonKey()
  List<String?> get validationErrors {
    if (_validationErrors is EqualUnmodifiableListView)
      return _validationErrors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_validationErrors);
  }

  @override
  @JsonKey()
  final bool isFreeeLinked;
  @override
  final AppError? error;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'BankAccountDisplayConfigState(bankAccounts: $bankAccounts, loadedBankAccounts: $loadedBankAccounts, otherBankAccounts: $otherBankAccounts, loadedOtherBankAccounts: $loadedOtherBankAccounts, validationErrors: $validationErrors, isFreeeLinked: $isFreeeLinked, error: $error)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'BankAccountDisplayConfigState'))
      ..add(DiagnosticsProperty('bankAccounts', bankAccounts))
      ..add(DiagnosticsProperty('loadedBankAccounts', loadedBankAccounts))
      ..add(DiagnosticsProperty('otherBankAccounts', otherBankAccounts))
      ..add(DiagnosticsProperty(
          'loadedOtherBankAccounts', loadedOtherBankAccounts))
      ..add(DiagnosticsProperty('validationErrors', validationErrors))
      ..add(DiagnosticsProperty('isFreeeLinked', isFreeeLinked))
      ..add(DiagnosticsProperty('error', error));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountDisplayConfigStateImpl &&
            const DeepCollectionEquality()
                .equals(other._bankAccounts, _bankAccounts) &&
            const DeepCollectionEquality()
                .equals(other._loadedBankAccounts, _loadedBankAccounts) &&
            const DeepCollectionEquality()
                .equals(other._otherBankAccounts, _otherBankAccounts) &&
            const DeepCollectionEquality().equals(
                other._loadedOtherBankAccounts, _loadedOtherBankAccounts) &&
            const DeepCollectionEquality()
                .equals(other._validationErrors, _validationErrors) &&
            (identical(other.isFreeeLinked, isFreeeLinked) ||
                other.isFreeeLinked == isFreeeLinked) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_bankAccounts),
      const DeepCollectionEquality().hash(_loadedBankAccounts),
      const DeepCollectionEquality().hash(_otherBankAccounts),
      const DeepCollectionEquality().hash(_loadedOtherBankAccounts),
      const DeepCollectionEquality().hash(_validationErrors),
      isFreeeLinked,
      error);

  /// Create a copy of BankAccountDisplayConfigState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountDisplayConfigStateImplCopyWith<
          _$BankAccountDisplayConfigStateImpl>
      get copyWith => __$$BankAccountDisplayConfigStateImplCopyWithImpl<
          _$BankAccountDisplayConfigStateImpl>(this, _$identity);
}

abstract class _BankAccountDisplayConfigState
    implements BankAccountDisplayConfigState {
  const factory _BankAccountDisplayConfigState(
      {final List<BankAccount> bankAccounts,
      final List<BankAccount> loadedBankAccounts,
      final List<BankAccount> otherBankAccounts,
      final List<BankAccount> loadedOtherBankAccounts,
      final List<String?> validationErrors,
      final bool isFreeeLinked,
      final AppError? error}) = _$BankAccountDisplayConfigStateImpl;

  @override
  List<BankAccount> get bankAccounts;
  @override
  List<BankAccount> get loadedBankAccounts;
  @override
  List<BankAccount> get otherBankAccounts;
  @override
  List<BankAccount> get loadedOtherBankAccounts;
  @override
  List<String?> get validationErrors;
  @override
  bool get isFreeeLinked;
  @override
  AppError? get error;

  /// Create a copy of BankAccountDisplayConfigState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountDisplayConfigStateImplCopyWith<
          _$BankAccountDisplayConfigStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
