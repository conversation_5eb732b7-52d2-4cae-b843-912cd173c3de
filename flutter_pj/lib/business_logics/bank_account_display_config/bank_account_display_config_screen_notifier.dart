import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_display_config_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/repositories/bank_account_display_config/bank_account_display_config_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'bank_account_display_config_screen_notifier.freezed.dart';

@freezed
class BankAccountDisplayConfigState with _$BankAccountDisplayConfigState {
  const factory BankAccountDisplayConfigState({
    @Default([]) List<BankAccount> bankAccounts,
    @Default([]) List<BankAccount> loadedBankAccounts,
    @Default([]) List<BankAccount> otherBankAccounts,
    @Default([]) List<BankAccount> loadedOtherBankAccounts,
    @Default([]) List<String?> validationErrors,
    @Default(false) bool isFreeeLinked,
    AppError? error,
  }) = _BankAccountDisplayConfigState;

  static const smbcMaxVisibleCount = 5;
  static const otherBankMaxVisibleCount = 15;
}

extension BankAccountDisplayConfigStateExt on BankAccountDisplayConfigState {
  /// 最初に読み込んだリストと現在のリストを比較して、差異があればtrueを返す
  bool get hasChanged {
    final configsBefore = [
      ...bankAccounts,
      ...otherBankAccounts,
    ];
    final configsAfter = [
      ...loadedBankAccounts,
      ...loadedOtherBankAccounts,
    ];

    return !listEquals(configsBefore, configsAfter);
  }

  /// バリデーションエラーがある場合はtrueを返す
  bool get hasValidationError => validationErrors.any((error) => error != null);

  /// すべてのSMBC口座が非表示に設定されている場合はtrueを返す
  /// 利用者情報取得などで口座情報が取得できない際にはfalseを返す
  bool get isAllSMBCAccountInvisible {
    if (bankAccounts.isEmpty) return false;
    return bankAccounts.every((account) => account.displayConfig.isHidden);
  }

  /// 且つすべての他行口座が非表示に設定されている場合はtrueを返す
  /// freee連携未済や口座取得に失敗した際にはfalseを返却
  bool get isAllOtherBankAccountInvisible {
    if (otherBankAccounts.isEmpty) return false;
    return otherBankAccounts.every((account) => account.displayConfig.isHidden);
  }

  /// 表示設定されたSMBC口座の数が指定された数を超えている場合はtrueを返す
  bool get isSMBCDisplayLimitOver {
    final visibleAccounts =
        bankAccounts.where((account) => !account.displayConfig.isHidden);
    return visibleAccounts.length >
        BankAccountDisplayConfigState.smbcMaxVisibleCount;
  }

  /// 表示設定された他行口座の数が指定された数を超えている場合はtrueを返す
  bool get isOtherBankDisplayLimitOver {
    final visibleAccounts =
        otherBankAccounts.where((account) => !account.displayConfig.isHidden);
    return visibleAccounts.length >
        BankAccountDisplayConfigState.otherBankMaxVisibleCount;
  }

  /// freee連携済み且つfreee連携口座が存在しない場合、trueを返す
  bool get isFreeeAccountsEmpty {
    return isFreeeLinked && otherBankAccounts.isEmpty;
  }
}

final bankAccountDisplayConfigScreenProvider =
    StateNotifierProvider.autoDispose<BankAccountDisplayConfigScreenNotifier,
        BankAccountDisplayConfigState>((ref) {
  return BankAccountDisplayConfigScreenNotifier(
    repository: ref.read(bankAccountDisplayConfigRepositoryProvider),
    loginRepository: ref.read(loginRepositoryProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
  );
});

class BankAccountDisplayConfigScreenNotifier
    extends StateNotifier<BankAccountDisplayConfigState> {
  BankAccountDisplayConfigScreenNotifier({
    required this.repository,
    required this.loginRepository,
    required this.analyticsLogController,
  }) : super(const BankAccountDisplayConfigState());

  final BankAccountDisplayConfigRepository repository;
  final LoginRepository loginRepository;
  final AnalyticsLogController analyticsLogController;

  // エラー情報をリセットするメソッド
  void _clearError() {
    state = state.copyWith(
      error: null,
    );
  }

  /// 設定読み込み処理
  Future<void> load(
    List<BankAccount> bankAccounts,
    List<BankAccount> otherBankAccounts,
    bool isFreeeLinked,
  ) async {
    // 並び順でソートする
    final sortedBankAccounts = bankAccounts.sortedByDisplayOrder();
    final sortedOtherBankAccounts = otherBankAccounts.sortedByDisplayOrder();
    state = state.copyWith(
      bankAccounts: sortedBankAccounts,
      loadedBankAccounts: sortedBankAccounts,
      validationErrors: []..length = sortedBankAccounts.length,
      otherBankAccounts: sortedOtherBankAccounts,
      loadedOtherBankAccounts: sortedOtherBankAccounts,
      isFreeeLinked: isFreeeLinked,
    );
  }

  /// 設定保存処理
  Future<bool> save() async {
    // SMBC口座の並び順を更新する
    final editedAccounts = state.bankAccounts
        .mapIndexed(
          (e, index) => e.copyWith(
            displayConfig: e.displayConfig.copyWith(displayOrder: index),
          ),
        )
        .toList();

    // freee口座の並び順を変更
    final editedFreeeAccounts = state.otherBankAccounts
        .mapIndexed(
          (e, index) => e.copyWith(
            displayConfig: e.displayConfig.copyWith(displayOrder: index),
          ),
        )
        .toList();

    // それぞれの口座情報をマージし、表示設定のみを抽出
    final newConfigs = [
      ...editedAccounts,
      ...editedFreeeAccounts,
    ].map((e) => e.displayConfig).toList();

    // 保存する
    final result = await repository.save(newConfigs);
    return result.when(
      success: (_) {
        state = state.copyWith(
          bankAccounts: editedAccounts,
          otherBankAccounts: editedFreeeAccounts,
        );
        return true;
      },
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: _clearError),
        );
        return false;
      },
    );
  }

  /// 自行：並び順を変更したときの処理
  void onReordered(int oldIndex, int newIndex) {
    state = state.copyWith(
      bankAccounts: state.bankAccounts.reordered(oldIndex, newIndex),
    );
  }

  /// 他行：並び順を変更したときの処理
  void onFreeeReordered(int oldIndex, int newIndex) {
    state = state.copyWith(
      otherBankAccounts: state.otherBankAccounts.reordered(oldIndex, newIndex),
    );
  }

  /// 表示名を変更したときの処理
  void onChangeDisplayName(int displayOrder, String newName, String? error) {
    // 表示名を更新する
    state = state.copyWith(
      bankAccounts: state.bankAccounts.map((account) {
        if (account.displayConfig.displayOrder == displayOrder) {
          return account.copyWith(
            displayConfig: account.displayConfig.copyWith(displayName: newName),
          );
        }
        return account;
      }).toList(),
      validationErrors: state.validationErrors.mapIndexed((e, index) {
        if (index == displayOrder) {
          return error;
        }
        return e;
      }).toList(),
    );
  }

  /// 自行：表示フラグを変更したときの処理
  void onChangeDisplayFlag(int displayOrder, bool newFlag) {
    state = state.copyWith(
      bankAccounts: state.bankAccounts.map((account) {
        if (account.displayConfig.displayOrder == displayOrder) {
          return account.copyWith(
            displayConfig: account.displayConfig.copyWith(isHidden: newFlag),
          );
        }
        return account;
      }).toList(),
    );
  }

  /// 他行：表示フラグを変更したときの処理
  void onChangeFreeeDisplayFlag(int displayOrder, bool newFlag) {
    state = state.copyWith(
      otherBankAccounts: state.otherBankAccounts.map((account) {
        if (account.displayConfig.displayOrder == displayOrder) {
          return account.copyWith(
            displayConfig: account.displayConfig.copyWith(isHidden: newFlag),
          );
        }
        return account;
      }).toList(),
    );
  }

  /// 保存ボタン押下時のバリデーションメッセージを取得(※個々のテキストフィールドエラーに関しては別途実装)
  /// バリデーションエラーがない際にはから文字を返却
  String getValidationErrorMessageOnSave() {
    if (state.isAllSMBCAccountInvisible) {
      return DisplayConfigErrorInfo.allSMBCAccountInvisibleErrorMessage;
    }
    if (state.isAllOtherBankAccountInvisible) {
      return DisplayConfigErrorInfo.allOtherBankAccountInvisibleErrorMessage;
    }
    if (state.isSMBCDisplayLimitOver) {
      return DisplayConfigErrorInfo.smbcAccountExceededErrorMessage;
    }
    if (state.isOtherBankDisplayLimitOver) {
      return DisplayConfigErrorInfo.otherBankAccountExceededErrorMessage;
    }
    return '';
  }
}
