// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_navigation_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$HomeNavigationScreenState {
  /// ログイン状態
  LoginStatus? get loginStatus => throw _privateConstructorUsedError;

  /// 現在表示中の画面
  int get currentIndex => throw _privateConstructorUsedError;

  /// 照会ボタン押下時の遷移先
  InquiryType get inquiryType => throw _privateConstructorUsedError;

  /// ユニバーサルリンクの受付状態
  UniversalLinkStatus get universalLinkStatus =>
      throw _privateConstructorUsedError;

  /// 初回ログインフラグ
  /// 起動後の遷移先の決定に使用
  bool get isFirstLogin => throw _privateConstructorUsedError;

  /// ID連携解除後フラグ
  /// ログイン画面の表示制御に使用
  bool get isIdUnlinked => throw _privateConstructorUsedError;

  /// ログアウト処理実施後のログアウト完了フラグ
  /// ログイン画面の生体認証制御に使用
  bool get logoutCompleted => throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;

  /// 現在表示中のモーダル
  ModalType get modalType => throw _privateConstructorUsedError;

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeNavigationScreenStateCopyWith<HomeNavigationScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeNavigationScreenStateCopyWith<$Res> {
  factory $HomeNavigationScreenStateCopyWith(HomeNavigationScreenState value,
          $Res Function(HomeNavigationScreenState) then) =
      _$HomeNavigationScreenStateCopyWithImpl<$Res, HomeNavigationScreenState>;
  @useResult
  $Res call(
      {LoginStatus? loginStatus,
      int currentIndex,
      InquiryType inquiryType,
      UniversalLinkStatus universalLinkStatus,
      bool isFirstLogin,
      bool isIdUnlinked,
      bool logoutCompleted,
      AppError? error,
      ModalType modalType});

  $LoginStatusCopyWith<$Res>? get loginStatus;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$HomeNavigationScreenStateCopyWithImpl<$Res,
        $Val extends HomeNavigationScreenState>
    implements $HomeNavigationScreenStateCopyWith<$Res> {
  _$HomeNavigationScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginStatus = freezed,
    Object? currentIndex = null,
    Object? inquiryType = null,
    Object? universalLinkStatus = null,
    Object? isFirstLogin = null,
    Object? isIdUnlinked = null,
    Object? logoutCompleted = null,
    Object? error = freezed,
    Object? modalType = null,
  }) {
    return _then(_value.copyWith(
      loginStatus: freezed == loginStatus
          ? _value.loginStatus
          : loginStatus // ignore: cast_nullable_to_non_nullable
              as LoginStatus?,
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      inquiryType: null == inquiryType
          ? _value.inquiryType
          : inquiryType // ignore: cast_nullable_to_non_nullable
              as InquiryType,
      universalLinkStatus: null == universalLinkStatus
          ? _value.universalLinkStatus
          : universalLinkStatus // ignore: cast_nullable_to_non_nullable
              as UniversalLinkStatus,
      isFirstLogin: null == isFirstLogin
          ? _value.isFirstLogin
          : isFirstLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isIdUnlinked: null == isIdUnlinked
          ? _value.isIdUnlinked
          : isIdUnlinked // ignore: cast_nullable_to_non_nullable
              as bool,
      logoutCompleted: null == logoutCompleted
          ? _value.logoutCompleted
          : logoutCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      modalType: null == modalType
          ? _value.modalType
          : modalType // ignore: cast_nullable_to_non_nullable
              as ModalType,
    ) as $Val);
  }

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LoginStatusCopyWith<$Res>? get loginStatus {
    if (_value.loginStatus == null) {
      return null;
    }

    return $LoginStatusCopyWith<$Res>(_value.loginStatus!, (value) {
      return _then(_value.copyWith(loginStatus: value) as $Val);
    });
  }

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HomeNavigationScreenStateImplCopyWith<$Res>
    implements $HomeNavigationScreenStateCopyWith<$Res> {
  factory _$$HomeNavigationScreenStateImplCopyWith(
          _$HomeNavigationScreenStateImpl value,
          $Res Function(_$HomeNavigationScreenStateImpl) then) =
      __$$HomeNavigationScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoginStatus? loginStatus,
      int currentIndex,
      InquiryType inquiryType,
      UniversalLinkStatus universalLinkStatus,
      bool isFirstLogin,
      bool isIdUnlinked,
      bool logoutCompleted,
      AppError? error,
      ModalType modalType});

  @override
  $LoginStatusCopyWith<$Res>? get loginStatus;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$HomeNavigationScreenStateImplCopyWithImpl<$Res>
    extends _$HomeNavigationScreenStateCopyWithImpl<$Res,
        _$HomeNavigationScreenStateImpl>
    implements _$$HomeNavigationScreenStateImplCopyWith<$Res> {
  __$$HomeNavigationScreenStateImplCopyWithImpl(
      _$HomeNavigationScreenStateImpl _value,
      $Res Function(_$HomeNavigationScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginStatus = freezed,
    Object? currentIndex = null,
    Object? inquiryType = null,
    Object? universalLinkStatus = null,
    Object? isFirstLogin = null,
    Object? isIdUnlinked = null,
    Object? logoutCompleted = null,
    Object? error = freezed,
    Object? modalType = null,
  }) {
    return _then(_$HomeNavigationScreenStateImpl(
      loginStatus: freezed == loginStatus
          ? _value.loginStatus
          : loginStatus // ignore: cast_nullable_to_non_nullable
              as LoginStatus?,
      currentIndex: null == currentIndex
          ? _value.currentIndex
          : currentIndex // ignore: cast_nullable_to_non_nullable
              as int,
      inquiryType: null == inquiryType
          ? _value.inquiryType
          : inquiryType // ignore: cast_nullable_to_non_nullable
              as InquiryType,
      universalLinkStatus: null == universalLinkStatus
          ? _value.universalLinkStatus
          : universalLinkStatus // ignore: cast_nullable_to_non_nullable
              as UniversalLinkStatus,
      isFirstLogin: null == isFirstLogin
          ? _value.isFirstLogin
          : isFirstLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isIdUnlinked: null == isIdUnlinked
          ? _value.isIdUnlinked
          : isIdUnlinked // ignore: cast_nullable_to_non_nullable
              as bool,
      logoutCompleted: null == logoutCompleted
          ? _value.logoutCompleted
          : logoutCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      modalType: null == modalType
          ? _value.modalType
          : modalType // ignore: cast_nullable_to_non_nullable
              as ModalType,
    ));
  }
}

/// @nodoc

class _$HomeNavigationScreenStateImpl implements _HomeNavigationScreenState {
  const _$HomeNavigationScreenStateImpl(
      {this.loginStatus = null,
      this.currentIndex = 0,
      this.inquiryType = InquiryType.accountInquiry,
      this.universalLinkStatus = UniversalLinkStatus.notAcceptable,
      this.isFirstLogin = true,
      this.isIdUnlinked = false,
      this.logoutCompleted = false,
      this.error,
      this.modalType = ModalType.none});

  /// ログイン状態
  @override
  @JsonKey()
  final LoginStatus? loginStatus;

  /// 現在表示中の画面
  @override
  @JsonKey()
  final int currentIndex;

  /// 照会ボタン押下時の遷移先
  @override
  @JsonKey()
  final InquiryType inquiryType;

  /// ユニバーサルリンクの受付状態
  @override
  @JsonKey()
  final UniversalLinkStatus universalLinkStatus;

  /// 初回ログインフラグ
  /// 起動後の遷移先の決定に使用
  @override
  @JsonKey()
  final bool isFirstLogin;

  /// ID連携解除後フラグ
  /// ログイン画面の表示制御に使用
  @override
  @JsonKey()
  final bool isIdUnlinked;

  /// ログアウト処理実施後のログアウト完了フラグ
  /// ログイン画面の生体認証制御に使用
  @override
  @JsonKey()
  final bool logoutCompleted;
  @override
  final AppError? error;

  /// 現在表示中のモーダル
  @override
  @JsonKey()
  final ModalType modalType;

  @override
  String toString() {
    return 'HomeNavigationScreenState(loginStatus: $loginStatus, currentIndex: $currentIndex, inquiryType: $inquiryType, universalLinkStatus: $universalLinkStatus, isFirstLogin: $isFirstLogin, isIdUnlinked: $isIdUnlinked, logoutCompleted: $logoutCompleted, error: $error, modalType: $modalType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeNavigationScreenStateImpl &&
            (identical(other.loginStatus, loginStatus) ||
                other.loginStatus == loginStatus) &&
            (identical(other.currentIndex, currentIndex) ||
                other.currentIndex == currentIndex) &&
            (identical(other.inquiryType, inquiryType) ||
                other.inquiryType == inquiryType) &&
            (identical(other.universalLinkStatus, universalLinkStatus) ||
                other.universalLinkStatus == universalLinkStatus) &&
            (identical(other.isFirstLogin, isFirstLogin) ||
                other.isFirstLogin == isFirstLogin) &&
            (identical(other.isIdUnlinked, isIdUnlinked) ||
                other.isIdUnlinked == isIdUnlinked) &&
            (identical(other.logoutCompleted, logoutCompleted) ||
                other.logoutCompleted == logoutCompleted) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.modalType, modalType) ||
                other.modalType == modalType));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      loginStatus,
      currentIndex,
      inquiryType,
      universalLinkStatus,
      isFirstLogin,
      isIdUnlinked,
      logoutCompleted,
      error,
      modalType);

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeNavigationScreenStateImplCopyWith<_$HomeNavigationScreenStateImpl>
      get copyWith => __$$HomeNavigationScreenStateImplCopyWithImpl<
          _$HomeNavigationScreenStateImpl>(this, _$identity);
}

abstract class _HomeNavigationScreenState implements HomeNavigationScreenState {
  const factory _HomeNavigationScreenState(
      {final LoginStatus? loginStatus,
      final int currentIndex,
      final InquiryType inquiryType,
      final UniversalLinkStatus universalLinkStatus,
      final bool isFirstLogin,
      final bool isIdUnlinked,
      final bool logoutCompleted,
      final AppError? error,
      final ModalType modalType}) = _$HomeNavigationScreenStateImpl;

  /// ログイン状態
  @override
  LoginStatus? get loginStatus;

  /// 現在表示中の画面
  @override
  int get currentIndex;

  /// 照会ボタン押下時の遷移先
  @override
  InquiryType get inquiryType;

  /// ユニバーサルリンクの受付状態
  @override
  UniversalLinkStatus get universalLinkStatus;

  /// 初回ログインフラグ
  /// 起動後の遷移先の決定に使用
  @override
  bool get isFirstLogin;

  /// ID連携解除後フラグ
  /// ログイン画面の表示制御に使用
  @override
  bool get isIdUnlinked;

  /// ログアウト処理実施後のログアウト完了フラグ
  /// ログイン画面の生体認証制御に使用
  @override
  bool get logoutCompleted;
  @override
  AppError? get error;

  /// 現在表示中のモーダル
  @override
  ModalType get modalType;

  /// Create a copy of HomeNavigationScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeNavigationScreenStateImplCopyWith<_$HomeNavigationScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
