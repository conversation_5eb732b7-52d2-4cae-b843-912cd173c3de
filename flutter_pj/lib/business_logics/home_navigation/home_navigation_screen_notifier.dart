import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/interfaces/fraud_alert_sdk_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/payment/payment_screen_notifier.dart';
import 'package:dtp_app/datas/firebase_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/repositories/fraud_alert_sdk/fraud_alert_sdk_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'home_navigation_screen_notifier.freezed.dart';

@freezed
class HomeNavigationScreenState with _$HomeNavigationScreenState {
  const factory HomeNavigationScreenState({
    /// ログイン状態
    @Default(null) LoginStatus? loginStatus,

    /// 現在表示中の画面
    @Default(0) int currentIndex,

    /// 照会ボタン押下時の遷移先
    @Default(InquiryType.accountInquiry) InquiryType inquiryType,

    /// ユニバーサルリンクの受付状態
    @Default(UniversalLinkStatus.notAcceptable)
    UniversalLinkStatus universalLinkStatus,

    /// 初回ログインフラグ
    /// 起動後の遷移先の決定に使用
    @Default(true) bool isFirstLogin,

    /// ID連携解除後フラグ
    /// ログイン画面の表示制御に使用
    @Default(false) bool isIdUnlinked,

    /// ログアウト処理実施後のログアウト完了フラグ
    /// ログイン画面の生体認証制御に使用
    @Default(false) bool logoutCompleted,
    AppError? error,

    /// 現在表示中のモーダル
    @Default(ModalType.none) ModalType modalType,
  }) = _HomeNavigationScreenState;
}

enum ModalType {
  none,
  inquiry,
  payment,
}

/// ユニバーサルリンク受付可否の状況
enum UniversalLinkStatus {
  /// 受付可能
  acceptable,

  /// 受付不可
  notAcceptable,

  /// 処理中（何もしない）
  inProgress,
}

final homeNavigationScreenProvider = StateNotifierProvider.autoDispose<
    HomeNavigationScreenNotifier, HomeNavigationScreenState>(
  (ref) => HomeNavigationScreenNotifier(
    firebaseManager: ref.read(firebaseManagerProvider),
    loginRepository: ref.read(loginRepositoryProvider),
    fraudAlertSDKRepository: ref.read(fraudAlertSDKRepositoryProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
  ),
);

class HomeNavigationScreenNotifier
    extends StateNotifier<HomeNavigationScreenState> {
  HomeNavigationScreenNotifier({
    required this.firebaseManager,
    required this.loginRepository,
    required this.fraudAlertSDKRepository,
    required this.analyticsLogController,
  }) : super(const HomeNavigationScreenState());

  final FirebaseManager firebaseManager;
  final LoginRepository loginRepository;
  final FraudAlertSDKRepository fraudAlertSDKRepository;
  final AnalyticsLogController analyticsLogController;

  /// ログイン状態を取得する
  Future<void> fetchLoginStatus() async {
    final result = await loginRepository.fetchLoginStatus();
    await result.when(
      success: (loginStatus) async {
        await loginStatus.map(
          login: (loggedIn) async {
            // ログイン済みの場合
            state = state.copyWith(
              loginStatus: loggedIn,
              isFirstLogin: false,
            );
          },
          logout: (_) async {
            // 未ログインの場合
            state = state.copyWith(
              loginStatus: const LoginStatus.logout(),
            );
          },
        );
      },
      failure: (error) async {
        // エラー時の処理
        state = state.copyWith(loginStatus: null);
      },
    );
  }

  Future<void> initialize() async {
    // 初回ログイン状況取得
    final result = await loginRepository.getFirstLoginWithIDPW();
    state = state.copyWith(isFirstLogin: result);
    // ログイン状況取得
    await fetchLoginStatus();
    // 怪しさ判定初期化
    await _fraudAlertSdkInit();
  }

  Future<void> logout() async {
    await loginRepository.logout();
  }

  void onTapAccountInquiry() {
    state = state.copyWith(inquiryType: InquiryType.accountInquiry);
  }

  void onTapCreditCardStatementInquiry() {
    state = state.copyWith(inquiryType: InquiryType.creditCardStatementInquiry);
  }

  void changeCurrentIndex(int index) {
    state = state.copyWith(
      currentIndex: index,
      // 振込処理中にWebViewから移動された際はユニバーサルリンクの処理を実施しない
      universalLinkStatus: UniversalLinkStatus.notAcceptable,
      modalType: ModalType.none,
    );
  }

  //モーダル表示するためのModalTypeの切り替え
  void changeModalType(ModalType modalType) {
    state = state.copyWith(
      modalType: modalType,
    );
  }

  //モーダル表示するためのModalTypeのリセット
  void resetModalType() {
    state = state.copyWith(
      modalType: ModalType.none,
    );
  }

  // ユニバーサルリンク処理継続可能確認フラグを更新
  void setUniversalLinkAcceptable(UniversalLinkStatus universalLinkStatus) {
    state = state.copyWith(universalLinkStatus: universalLinkStatus);
  }

  /// Fraud Alert SDK初期化処理
  Future<void> _fraudAlertSdkInit() async {
    await fraudAlertSDKRepository.initialize();
  }

  void setIdUnlinked(bool isUnlinked) {
    state = state.copyWith(isIdUnlinked: isUnlinked);
  }

  void setLogoutComplete(bool logoutCompleted) {
    state = state.copyWith(logoutCompleted: logoutCompleted);
  }

  String getScreenIdNumber(PaymentScreenStatus paymentScreenStatus) {
    switch (state.currentIndex) {
      case 0:
        return ScreenIdNumber.homeScreenId;
      case 1:
        if (state.inquiryType == InquiryType.accountInquiry) {
          return ScreenIdNumber.accountInquiryScreenId;
        }
        return ScreenIdNumber.cardDetailScreenId;
      case 2:
        if (paymentScreenStatus == PaymentScreenStatus.payment) {
          return ScreenIdNumber.paymentScreenId;
        }
        return ScreenIdNumber.approvalScreenId;
      case 3:
        return ScreenIdNumber.myPageScreenId;
    }
    return ScreenIdNumber.homeScreenId;
  }
}

enum InquiryType {
  // 口座照会
  accountInquiry,
  // クレジットカード利用明細照会
  creditCardStatementInquiry,
}
