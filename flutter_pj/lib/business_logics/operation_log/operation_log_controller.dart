import 'package:dtp_app/business_logics/interfaces/operation_log_repository.dart';
import 'package:dtp_app/repositories/operation_log_repository/operation_log_repository_impl.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class OperationLogController {
  OperationLogController({
    required this.operationLogRepository,
  });

  final OperationLogRepository operationLogRepository;

  // HACKME 各画面のnotifierでsendOperationLogを呼び出しているため
  // operation_log_controller.dartに定義して、それを呼び出す方針でまとめたい
  Future<void> sendOperationLog({
    required String? functionLog,
    required String? operationLog,
    required String? resultLog,
    required String? errorIdLog,
  }) async {
    await operationLogRepository.sendOperationLog(
      functionLog: functionLog ?? '',
      operationLog: operationLog ?? '',
      resultLog: resultLog ?? '',
      errorIdLog: errorIdLog ?? '',
    );
  }
}

final operationLogControllerProvider =
    Provider.autoDispose<OperationLogController>((ref) {
  return OperationLogController(
    operationLogRepository: ref.read(operationLogRepositoryProvider),
  );
});
