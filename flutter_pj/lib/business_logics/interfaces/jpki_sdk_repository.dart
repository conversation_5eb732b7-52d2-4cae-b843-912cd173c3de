import 'package:dtp_app/models/app_result/app_result.dart';

abstract class JpkiSdkRepository {
  Future<String> getJpkiPassword();

  Future<void> setJpkiPassword(String value);

  Future<String> getCertForSig();

  Future<void> setCertForSig(String value);

  Future<String> getSigDoc();

  Future<void> setSigDoc(String value);

  // F_SLI_CMN_01 ICカード通信開始
  Future<AppResult<String>> startNfcSession(String statusMessage);

  // F_SLI_CMN_02 ICカード通信正常終了
  Future<AppResult<String>> finishNfcSessionSuccess(String statusMessage);

  // F_SLI_CMN_03 ICカード通信エラー終了
  Future<AppResult<String>> finishNfcSessionError(String statusMessage);

  // F_SLI_CMN_04 ICカードスキャン文言変更
  Future<AppResult<String>> setCardScanStatusMessage(String message);

  // F_SLI_CMN_05 カード種別判定
  // Future<AppResult<String>> getCardKind();

  // F_SLI_JPK_01 署名用パスワード照合
  Future<AppResult<String>> verifyPasswordForSign();

  // F_SLI_JPK_02 署名用電子証明書取得
  Future<AppResult<void>> getCertificateForSign();

  // F_SLI_JPK_03 署名書電子署名作成(生データより作成)
  Future<AppResult<void>> makeSignatureForSign();

  // F_SLI_JPK_05 署名用パスワード照合残試行可能回数取得
  Future<AppResult<int>> getRetryCounterForSign();

  // ここからAndroidのSDKのメソッドを記載
  // F_JLA_01 NFCリーダーモード設定
  Future<AppResult<String>> setEnableNfcCardReader(int timeout);

  // F_JLA_02 NFC標準モード設定
  Future<AppResult<String>> setDisableNfcCardReader();

  // F_JLA_03 残試行回数取得
  Future<AppResult<int>> getAttemptRemain(int type);

  // F_JLA_JPK_01 署名用電子証明書取得
  Future<AppResult<void>> getSignatureCert();

  // F_JLA_JPK_02 署名用電子署名作成
  Future<AppResult<void>> makeSignatureCertDigitalSignature();

  // F_JLA_JPK_07 署名用電子証明書AP認証確認
  Future<AppResult<String>> verifySignatureCertPin();

  // AndroidのNFC状態取得
  Future<AppResult<AndroidNfcStatus>> getAndroidNfcStatus();
}

enum AndroidNfcStatus {
  // 非対応
  notSupported,
  // 無効化
  disabled,
  // 有効化
  enabled,
}
