import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';

abstract class CreditCardTransactionHistoryRepository {
  Future<AppResult<FreeeTransactionHistory>>
      getFreeeCreditCardTransactionHistory({
    required int walletableId,
    required FreeeTransactionsType walletableType,
    // 選択された月の初日を設定
    required DateTime startDateFrom,
    // 選択された月の最終日を設定
    required DateTime startDateTo,
    bool forceRefresh,
    TransactionHistoryFilterType filterType,
    TransactionHistorySortType sortType,
    TransactionHistorySortOrder order,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    double? minAmountWithSign,
    double? maxAmountWithSign,
    String? descriptionSearch,
  });

  /// 口座情報の表示/非表示状態を取得
  Future<bool> getCreditCardExpenseVisible();

  /// 口座情報の表示/非表示状態を設定
  Future<bool> setCreditCardExpenseVisible(bool value);
}
