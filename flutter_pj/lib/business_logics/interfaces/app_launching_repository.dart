import 'package:dtp_app/models/app_information/app_information.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';

/// アプリ起動時の初期化処理などを管理するリポジトリ
abstract class AppLaunchingRepository {
  Future<ForceUpdateConfig> checkForceUpdate({
    required AppInformation appInformation,
  });
  // 初回起動か否かを返す
  Future<bool> isFirstTimeStartup();
  // 初回起動完了
  Future<bool> firstTimeStartupDone();
  // セキュアデータを全削除する
  Future<void> deleteAllSecureData();
}
