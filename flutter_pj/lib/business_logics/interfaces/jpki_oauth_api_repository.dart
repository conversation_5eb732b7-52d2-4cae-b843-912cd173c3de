import 'package:dtp_app/models/app_result/app_result.dart';

abstract class JpkiOAuthAPIRepository {
  // ClientIdを設定する
  Future<void> saveClientId(String clientId);

  // ClientSecretを設定する
  Future<void> saveClientSecret(String clientSecret);

  // OAuthAPIから取得したアクセストークンを取得する
  Future<String> getJpkiAccessToken();

  // OAuthAPIから取得したアクセストークンを設定する
  Future<void> setJpkiAccessToken(String accessToken);

  // CC_OAuthAPI呼び出し
  Future<AppResult<void>> jpkiOAuthApiAccess();
}
