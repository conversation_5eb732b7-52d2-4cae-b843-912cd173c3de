import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';

abstract class FreeeTransactionHistoryRepository {
  Future<AppResult<FreeeTransactionHistory>> getFreeeTransactionHistory({
    required int walletableId,
    required FreeeTransactionsType walletableType,
    // 選択された月の初日を設定
    required DateTime startDateFrom,
    // 選択された月の最終日を設定
    required DateTime startDateTo,
    bool forceRefresh,
    TransactionHistoryFilterType filterType,
    TransactionHistorySortType sortType,
    TransactionHistorySortOrder order,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    double? minAmount,
    double? maxAmount,
  });
}
