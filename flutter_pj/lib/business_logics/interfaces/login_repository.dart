import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/authorization_status/authorization_status.dart';
import 'package:dtp_app/models/consent_status/consent_status.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/models/session_id_or_high_risk_user_id/session_id_or_high_risk_user_id.dart';
import 'package:dtp_app/models/vdid_login/vdid_login_response.dart';

abstract class LoginRepository {
  Future<AppResult<LoginStatus>> fetchLoginStatus();

  Future<AppResult<SessionIdOrHighRiskUserId>> verifySamlResponse(
    String samlResponse,
    String caulisSessionId,
  );

  Future<AppResult<ConsentStatus>> checkConsentStatus();

  Future<AppResult<void>> registerConsentStatus(
    List<String> agreeTerms,
  );

  Future<AppResult<AuthorizationStatus>> checkAuthorization();

  Future<AppResult<String>> createAuthScreenInfo();

  Future<AppResult<void>> getToken(
    String code,
    String state,
  );

  Future<bool> getUsingBiometrics();

  Future<bool> getFirstLoginWithIDPW();

  Future<bool> getDtpLoginFlag();

  Future<String> getLoginId();

  Future<String> getPassword();

  Future<void> setBiometricsLoginInfo({
    required String id,
    required String password,
    required bool isUsingBiometrics,
    required bool isFirstLoginWithIDPW,
    required bool isDtpIdLogin,
  });

  AppError? getAuthError();

  Future<void> clearAuthError();

  Future<AppResult<void>> logout();

  Future<AppResult<VdidLoginResponse>> login(
    String vdId,
    String password,
    String caulisSessionId,
  );

  Future<AppResult<SessionIdOrHighRiskUserId>> loginDtpId(
    String vdId,
    String password,
    String caulisSessionId,
  );

  Future<void> loginSuspiciousDetection(
    String vdId,
    String caulisSessionId,
    String errorMessageId,
  );

  Future<AppResult<void>> checkGetsAuthorization();

  /// Gets権限の有無をキャッシュから取得（FirebaseAnalytics送信用）
  /// '0':連携なし（紐付けている口座0件）
  /// '1':連携あり（紐付けている口座1件以上）
  /// '':値が設定できない場合は空文字（初期値）
  String getGetsAuthStatus();

  Future<AppResult<String>> getWeb21SsoSaml(bool isTransfer);

  Future<AppResult<VdidLoginResponse>> navigateVdSystem(String toWeb21Param);

  Future<AppResult<VdidLoginResponse>> enableOtpLater();

  /// 高リスクユーザーIDとIDaaSから取得した暗号化Cookieのticketをもとにして、セッションIDを取得する
  Future<AppResult<void>> getSessionId({
    required String highRiskUserId,
    required String idaasTicket,
  });

  /// DTPログイン時のIDをメモリから取得
  Future<String> loadDtpIdCache();

  /// DTPログイン時のIDをメモリに書き込む
  Future<void> saveDtpIdCache(String dtpId);

  /// VDログイン時のIDをメモリから取得
  Future<String> loadVdIdCache();

  /// VDログイン時のIDをメモリに書き込む
  Future<void> saveVdIdCache(String vdId);

  /// IDaaSのSSOに使用する暗号化Cookieの取得
  Future<AppResult<void>> getEncryptedCookie();

  /// 認可拒否履歴登録
  Future<AppResult<void>> registerRefusalFlag();

  /// 認可拒否履歴削除
  Future<AppResult<void>> deleteRefusalFlag();

  /// 揮発性情報の削除
  Future<void> deleteVolatileInfo();
}
