import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/identity_verification_address/identity_verification_address.dart';
import 'package:dtp_app/models/identity_verification_info/identity_verification_info.dart';

/// 本人確認用リポジトリ
abstract interface class IdentityVerificationRepository {
  /// 本人確認情報登録
  Future<AppResult<void>> sendIdentityVerificationInfo(
    IdentityVerificationInfo info,
  );

  /// 郵便番号から住所検索
  Future<AppResult<Address>> fetchAddress(String? postcode);
}
