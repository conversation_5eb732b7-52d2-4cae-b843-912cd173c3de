import 'package:dtp_app/models/app_result/app_result.dart';

abstract class KarteSdkRepository {
  /// KARTEのViewイベント送信用メソッド
  AppResult<void> sendView({
    required String screenIdNumber,
    required String screenName,
  });

  AppResult<void> sendTrack({
    required String buttonName,
    required String screenIdNumber,
  });

  /// KARTEのidentifyとAttributeイベント送信用メソッド
  AppResult<void> sendIdentifyAndAttribute({
    required String userId,
    required Map<String, dynamic> attribute,
  });

  /// KARTEのAttributeイベント(お手続き番号)送信用メソッド
  AppResult<void> sendAttribute({
    required Map<String, String> attribute,
  });
}
