import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';

abstract class AccountTransactionHistoryRepository {
  Future<AppResult<AccountTransactionHistory>> getAccountTransactionHistory({
    required String accountId,
    required DateTime dateFrom,
    required DateTime dateTo,
    bool forceRefresh,
    TransactionHistoryFilterType filterType,
    TransactionHistorySortType sortType,
    TransactionHistorySortOrder order,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    List<TransactionTypeCode>? transactionTypeCode,
    String? payerName,
    int? minAmount,
    int? maxAmount,
    String? referenceNumber,
  });
}
