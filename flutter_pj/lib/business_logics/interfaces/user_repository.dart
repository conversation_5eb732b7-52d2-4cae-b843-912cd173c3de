import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/user_info/user_info.dart';
import 'package:dtp_app/models/users/users.dart';

abstract class UserRepository {
  Future<AppResult<Users>> getUsers();

  Future<AppResult<UserInfo>> getUserInfo();

  Future<AppResult<void>> updateDtpId();

  Future<AppResult<String>> issueAndLinkDtpId(EncryptedCookie encryptedCookie);

  Future<AppResult<void>> deleteDtpIdLink();

  List<AccountList> getUserAccounts();

  String getServerDate();

  AppError? getError();
}
