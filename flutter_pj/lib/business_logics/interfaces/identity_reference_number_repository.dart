import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/identity_verification_reference_number_info/identity_verification_reference_number_info.dart';
import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';

/// 本人確認お手続き番号リポジトリのインターフェース
abstract class IdentityReferenceNumberRepository {
  /// 口座開設申込情報一時保存情報を取得
  Future<AppResult<OpenAccountAppTemp>> fetchOpenAccountAppTemp({
    required String referenceNumber,
  });

  /// 口座開設行内審査ステータスを取得
  Future<AppResult<ReferenceNumberInfo>> checkReferenceNumberStatus({
    required String referenceNumber,
  });

  /// UUIDを取得
  Future<AppResult<String>> getUuid({
    required String referenceNumber,
  });
}
