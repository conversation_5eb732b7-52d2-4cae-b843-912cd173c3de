import 'package:dtp_app/models/account_settings/account_settings.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

/// 口座表示設定リポジトリ
abstract class BankAccountDisplayConfigRepository {
  /// 口座表示設定を保存する
  Future<AppResult<void>> save(List<DisplayConfig> configs);

  /// 口座表示設定を取得する
  Future<AppResult<AccountSettings>> getAccountSettings();
}
