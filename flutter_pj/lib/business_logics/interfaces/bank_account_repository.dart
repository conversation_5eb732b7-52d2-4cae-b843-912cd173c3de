import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/freee_links_client_id_response/freee_links_client_id_response.dart';
import 'package:dtp_app/models/freee_signup_params/freee_signup_params.dart';
import 'package:dtp_app/models/freee_transaction_total_response/freee_transaction_total_response.dart';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';

/// 口座情報リポジトリのインターフェース
abstract class BankAccountRepository {
  /// 指定口座残高情報を取得
  Future<AppResult<BankAccountBalance>> getAccountBalance({
    required String? accountId,
    required String date,
  });

  /// 口座情報の表示/非表示状態を取得
  Future<bool> getBalanceVisible();

  /// 口座情報の表示/非表示状態を設定
  Future<bool> setBalanceVisible(bool value);

  /// freee連携の有無をキャッシュから取得
  bool getIsFreeeLinked();

  /// 他行口座連携の有無をキャッシュから取得（FirebaseAnalytics送信用）
  /// '0':連携なし（紐付けている口座0件）
  /// '1':連携あり（紐付けている口座1件以上）
  /// '':値が設定できない場合は空文字（初期値）
  String getOtherBanksLinkage();

  /// 他社クレジットカード連携の有無をキャッシュから取得（FirebaseAnalytics送信用）
  /// '0':連携なし（紐付けているクレジットカード0枚）
  /// '1':連携あり（紐付けているクレジットカード1枚以上）
  /// '':値が設定できない場合は空文字（初期値）
  String getOtherCompanyCardsLinkage();

  /// 他行口座連携の有無と他社クレジットカード連携の有無をリセット
  void resetOtherBanksLinkageAndOtherCompanyCardsLinkage();

  /// freeeSSO用のパラメータを取得
  Future<AppResult<FreeeSsoParams>> getFreeeSsoParams();

  /// freee再連携画面のURLを取得
  Future<AppResult<String>> getUrlToFreeeReLinkPage();

  /// freee連携の有無を取得
  Future<AppResult<bool>> getFreeeLinkStatus();

  /// freee口座一覧を取得
  Future<AppResult<FreeeWalletables>> getFreeeAccountBalance(
    InquiryCategory inquiryCategory,
  );

  /// freeeクレジットカード利用額期間合計を取得
  Future<AppResult<FreeeTransactionTotalResponse>> getFreeeTotalExpense(
    int walletableId,
  );

  /// freee連携（アクセストークン・リフレッシュトークン取得、保存）
  Future<AppResult<void>> linkToFreee({
    required String code,
    required String stateFromFreee,
  });

  /// 取引先ID紐付け確認
  Future<AppResult<FreeeLinksClientIdResponse>> checkFreeeLinksByClientId();
}
