// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'identity_reference_number_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IdentityReferenceNumberState {
  ReferenceNumberStatus get referenceNumberStatus =>
      throw _privateConstructorUsedError;
  ReferenceNumberInfo? get referenceNumberInfo =>
      throw _privateConstructorUsedError;
  OpenAccountAppTemp? get openAccountAppTemp =>
      throw _privateConstructorUsedError;
  IdentityVerificationPhases? get identityVerificationPhases =>
      throw _privateConstructorUsedError;
  String get referenceNumberText =>
      throw _privateConstructorUsedError; // 本人情報送信時に使用
  String get cacheReferenceNumberText => throw _privateConstructorUsedError;
  String get cacheUuid => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  XFile? get image => throw _privateConstructorUsedError;
  ValidationError get referenceNumberValidationError =>
      throw _privateConstructorUsedError;

  /// 項目編集時にバリデーションチェックを実行するかどうか
  bool get isRealTimeValidationMode => throw _privateConstructorUsedError;

  /// QRコード読み込みを実施した場合
  /// 一度POP処理にてお手続番号入力画面遷移後後続画面に遷移
  /// 現状QR画面からPUSH遷移することがない為、フラグのリセットはPOP時にのみ実施する形になっている
  bool get isOnQrScreen => throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;
  AppError? get pickImageError => throw _privateConstructorUsedError;

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdentityReferenceNumberStateCopyWith<IdentityReferenceNumberState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdentityReferenceNumberStateCopyWith<$Res> {
  factory $IdentityReferenceNumberStateCopyWith(
          IdentityReferenceNumberState value,
          $Res Function(IdentityReferenceNumberState) then) =
      _$IdentityReferenceNumberStateCopyWithImpl<$Res,
          IdentityReferenceNumberState>;
  @useResult
  $Res call(
      {ReferenceNumberStatus referenceNumberStatus,
      ReferenceNumberInfo? referenceNumberInfo,
      OpenAccountAppTemp? openAccountAppTemp,
      IdentityVerificationPhases? identityVerificationPhases,
      String referenceNumberText,
      String cacheReferenceNumberText,
      String cacheUuid,
      bool isLoading,
      XFile? image,
      ValidationError referenceNumberValidationError,
      bool isRealTimeValidationMode,
      bool isOnQrScreen,
      AppError? error,
      AppError? pickImageError});

  $ReferenceNumberInfoCopyWith<$Res>? get referenceNumberInfo;
  $OpenAccountAppTempCopyWith<$Res>? get openAccountAppTemp;
  $ValidationErrorCopyWith<$Res> get referenceNumberValidationError;
  $AppErrorCopyWith<$Res>? get error;
  $AppErrorCopyWith<$Res>? get pickImageError;
}

/// @nodoc
class _$IdentityReferenceNumberStateCopyWithImpl<$Res,
        $Val extends IdentityReferenceNumberState>
    implements $IdentityReferenceNumberStateCopyWith<$Res> {
  _$IdentityReferenceNumberStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referenceNumberStatus = null,
    Object? referenceNumberInfo = freezed,
    Object? openAccountAppTemp = freezed,
    Object? identityVerificationPhases = freezed,
    Object? referenceNumberText = null,
    Object? cacheReferenceNumberText = null,
    Object? cacheUuid = null,
    Object? isLoading = null,
    Object? image = freezed,
    Object? referenceNumberValidationError = null,
    Object? isRealTimeValidationMode = null,
    Object? isOnQrScreen = null,
    Object? error = freezed,
    Object? pickImageError = freezed,
  }) {
    return _then(_value.copyWith(
      referenceNumberStatus: null == referenceNumberStatus
          ? _value.referenceNumberStatus
          : referenceNumberStatus // ignore: cast_nullable_to_non_nullable
              as ReferenceNumberStatus,
      referenceNumberInfo: freezed == referenceNumberInfo
          ? _value.referenceNumberInfo
          : referenceNumberInfo // ignore: cast_nullable_to_non_nullable
              as ReferenceNumberInfo?,
      openAccountAppTemp: freezed == openAccountAppTemp
          ? _value.openAccountAppTemp
          : openAccountAppTemp // ignore: cast_nullable_to_non_nullable
              as OpenAccountAppTemp?,
      identityVerificationPhases: freezed == identityVerificationPhases
          ? _value.identityVerificationPhases
          : identityVerificationPhases // ignore: cast_nullable_to_non_nullable
              as IdentityVerificationPhases?,
      referenceNumberText: null == referenceNumberText
          ? _value.referenceNumberText
          : referenceNumberText // ignore: cast_nullable_to_non_nullable
              as String,
      cacheReferenceNumberText: null == cacheReferenceNumberText
          ? _value.cacheReferenceNumberText
          : cacheReferenceNumberText // ignore: cast_nullable_to_non_nullable
              as String,
      cacheUuid: null == cacheUuid
          ? _value.cacheUuid
          : cacheUuid // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as XFile?,
      referenceNumberValidationError: null == referenceNumberValidationError
          ? _value.referenceNumberValidationError
          : referenceNumberValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      isRealTimeValidationMode: null == isRealTimeValidationMode
          ? _value.isRealTimeValidationMode
          : isRealTimeValidationMode // ignore: cast_nullable_to_non_nullable
              as bool,
      isOnQrScreen: null == isOnQrScreen
          ? _value.isOnQrScreen
          : isOnQrScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      pickImageError: freezed == pickImageError
          ? _value.pickImageError
          : pickImageError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ReferenceNumberInfoCopyWith<$Res>? get referenceNumberInfo {
    if (_value.referenceNumberInfo == null) {
      return null;
    }

    return $ReferenceNumberInfoCopyWith<$Res>(_value.referenceNumberInfo!,
        (value) {
      return _then(_value.copyWith(referenceNumberInfo: value) as $Val);
    });
  }

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OpenAccountAppTempCopyWith<$Res>? get openAccountAppTemp {
    if (_value.openAccountAppTemp == null) {
      return null;
    }

    return $OpenAccountAppTempCopyWith<$Res>(_value.openAccountAppTemp!,
        (value) {
      return _then(_value.copyWith(openAccountAppTemp: value) as $Val);
    });
  }

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get referenceNumberValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.referenceNumberValidationError,
        (value) {
      return _then(
          _value.copyWith(referenceNumberValidationError: value) as $Val);
    });
  }

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get pickImageError {
    if (_value.pickImageError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.pickImageError!, (value) {
      return _then(_value.copyWith(pickImageError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdentityReferenceNumberStateImplCopyWith<$Res>
    implements $IdentityReferenceNumberStateCopyWith<$Res> {
  factory _$$IdentityReferenceNumberStateImplCopyWith(
          _$IdentityReferenceNumberStateImpl value,
          $Res Function(_$IdentityReferenceNumberStateImpl) then) =
      __$$IdentityReferenceNumberStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ReferenceNumberStatus referenceNumberStatus,
      ReferenceNumberInfo? referenceNumberInfo,
      OpenAccountAppTemp? openAccountAppTemp,
      IdentityVerificationPhases? identityVerificationPhases,
      String referenceNumberText,
      String cacheReferenceNumberText,
      String cacheUuid,
      bool isLoading,
      XFile? image,
      ValidationError referenceNumberValidationError,
      bool isRealTimeValidationMode,
      bool isOnQrScreen,
      AppError? error,
      AppError? pickImageError});

  @override
  $ReferenceNumberInfoCopyWith<$Res>? get referenceNumberInfo;
  @override
  $OpenAccountAppTempCopyWith<$Res>? get openAccountAppTemp;
  @override
  $ValidationErrorCopyWith<$Res> get referenceNumberValidationError;
  @override
  $AppErrorCopyWith<$Res>? get error;
  @override
  $AppErrorCopyWith<$Res>? get pickImageError;
}

/// @nodoc
class __$$IdentityReferenceNumberStateImplCopyWithImpl<$Res>
    extends _$IdentityReferenceNumberStateCopyWithImpl<$Res,
        _$IdentityReferenceNumberStateImpl>
    implements _$$IdentityReferenceNumberStateImplCopyWith<$Res> {
  __$$IdentityReferenceNumberStateImplCopyWithImpl(
      _$IdentityReferenceNumberStateImpl _value,
      $Res Function(_$IdentityReferenceNumberStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? referenceNumberStatus = null,
    Object? referenceNumberInfo = freezed,
    Object? openAccountAppTemp = freezed,
    Object? identityVerificationPhases = freezed,
    Object? referenceNumberText = null,
    Object? cacheReferenceNumberText = null,
    Object? cacheUuid = null,
    Object? isLoading = null,
    Object? image = freezed,
    Object? referenceNumberValidationError = null,
    Object? isRealTimeValidationMode = null,
    Object? isOnQrScreen = null,
    Object? error = freezed,
    Object? pickImageError = freezed,
  }) {
    return _then(_$IdentityReferenceNumberStateImpl(
      referenceNumberStatus: null == referenceNumberStatus
          ? _value.referenceNumberStatus
          : referenceNumberStatus // ignore: cast_nullable_to_non_nullable
              as ReferenceNumberStatus,
      referenceNumberInfo: freezed == referenceNumberInfo
          ? _value.referenceNumberInfo
          : referenceNumberInfo // ignore: cast_nullable_to_non_nullable
              as ReferenceNumberInfo?,
      openAccountAppTemp: freezed == openAccountAppTemp
          ? _value.openAccountAppTemp
          : openAccountAppTemp // ignore: cast_nullable_to_non_nullable
              as OpenAccountAppTemp?,
      identityVerificationPhases: freezed == identityVerificationPhases
          ? _value.identityVerificationPhases
          : identityVerificationPhases // ignore: cast_nullable_to_non_nullable
              as IdentityVerificationPhases?,
      referenceNumberText: null == referenceNumberText
          ? _value.referenceNumberText
          : referenceNumberText // ignore: cast_nullable_to_non_nullable
              as String,
      cacheReferenceNumberText: null == cacheReferenceNumberText
          ? _value.cacheReferenceNumberText
          : cacheReferenceNumberText // ignore: cast_nullable_to_non_nullable
              as String,
      cacheUuid: null == cacheUuid
          ? _value.cacheUuid
          : cacheUuid // ignore: cast_nullable_to_non_nullable
              as String,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as XFile?,
      referenceNumberValidationError: null == referenceNumberValidationError
          ? _value.referenceNumberValidationError
          : referenceNumberValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      isRealTimeValidationMode: null == isRealTimeValidationMode
          ? _value.isRealTimeValidationMode
          : isRealTimeValidationMode // ignore: cast_nullable_to_non_nullable
              as bool,
      isOnQrScreen: null == isOnQrScreen
          ? _value.isOnQrScreen
          : isOnQrScreen // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
      pickImageError: freezed == pickImageError
          ? _value.pickImageError
          : pickImageError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$IdentityReferenceNumberStateImpl
    with DiagnosticableTreeMixin
    implements _IdentityReferenceNumberState {
  const _$IdentityReferenceNumberStateImpl(
      {this.referenceNumberStatus = ReferenceNumberStatus.unchecked,
      this.referenceNumberInfo,
      this.openAccountAppTemp,
      this.identityVerificationPhases,
      this.referenceNumberText = '',
      this.cacheReferenceNumberText = '',
      this.cacheUuid = '',
      this.isLoading = false,
      this.image,
      this.referenceNumberValidationError = const ValidationError.none(),
      this.isRealTimeValidationMode = false,
      this.isOnQrScreen = false,
      this.error,
      this.pickImageError});

  @override
  @JsonKey()
  final ReferenceNumberStatus referenceNumberStatus;
  @override
  final ReferenceNumberInfo? referenceNumberInfo;
  @override
  final OpenAccountAppTemp? openAccountAppTemp;
  @override
  final IdentityVerificationPhases? identityVerificationPhases;
  @override
  @JsonKey()
  final String referenceNumberText;
// 本人情報送信時に使用
  @override
  @JsonKey()
  final String cacheReferenceNumberText;
  @override
  @JsonKey()
  final String cacheUuid;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final XFile? image;
  @override
  @JsonKey()
  final ValidationError referenceNumberValidationError;

  /// 項目編集時にバリデーションチェックを実行するかどうか
  @override
  @JsonKey()
  final bool isRealTimeValidationMode;

  /// QRコード読み込みを実施した場合
  /// 一度POP処理にてお手続番号入力画面遷移後後続画面に遷移
  /// 現状QR画面からPUSH遷移することがない為、フラグのリセットはPOP時にのみ実施する形になっている
  @override
  @JsonKey()
  final bool isOnQrScreen;
  @override
  final AppError? error;
  @override
  final AppError? pickImageError;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'IdentityReferenceNumberState(referenceNumberStatus: $referenceNumberStatus, referenceNumberInfo: $referenceNumberInfo, openAccountAppTemp: $openAccountAppTemp, identityVerificationPhases: $identityVerificationPhases, referenceNumberText: $referenceNumberText, cacheReferenceNumberText: $cacheReferenceNumberText, cacheUuid: $cacheUuid, isLoading: $isLoading, image: $image, referenceNumberValidationError: $referenceNumberValidationError, isRealTimeValidationMode: $isRealTimeValidationMode, isOnQrScreen: $isOnQrScreen, error: $error, pickImageError: $pickImageError)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'IdentityReferenceNumberState'))
      ..add(DiagnosticsProperty('referenceNumberStatus', referenceNumberStatus))
      ..add(DiagnosticsProperty('referenceNumberInfo', referenceNumberInfo))
      ..add(DiagnosticsProperty('openAccountAppTemp', openAccountAppTemp))
      ..add(DiagnosticsProperty(
          'identityVerificationPhases', identityVerificationPhases))
      ..add(DiagnosticsProperty('referenceNumberText', referenceNumberText))
      ..add(DiagnosticsProperty(
          'cacheReferenceNumberText', cacheReferenceNumberText))
      ..add(DiagnosticsProperty('cacheUuid', cacheUuid))
      ..add(DiagnosticsProperty('isLoading', isLoading))
      ..add(DiagnosticsProperty('image', image))
      ..add(DiagnosticsProperty(
          'referenceNumberValidationError', referenceNumberValidationError))
      ..add(DiagnosticsProperty(
          'isRealTimeValidationMode', isRealTimeValidationMode))
      ..add(DiagnosticsProperty('isOnQrScreen', isOnQrScreen))
      ..add(DiagnosticsProperty('error', error))
      ..add(DiagnosticsProperty('pickImageError', pickImageError));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdentityReferenceNumberStateImpl &&
            (identical(other.referenceNumberStatus, referenceNumberStatus) ||
                other.referenceNumberStatus == referenceNumberStatus) &&
            (identical(other.referenceNumberInfo, referenceNumberInfo) ||
                other.referenceNumberInfo == referenceNumberInfo) &&
            (identical(other.openAccountAppTemp, openAccountAppTemp) ||
                other.openAccountAppTemp == openAccountAppTemp) &&
            (identical(other.identityVerificationPhases,
                    identityVerificationPhases) ||
                other.identityVerificationPhases ==
                    identityVerificationPhases) &&
            (identical(other.referenceNumberText, referenceNumberText) ||
                other.referenceNumberText == referenceNumberText) &&
            (identical(
                    other.cacheReferenceNumberText, cacheReferenceNumberText) ||
                other.cacheReferenceNumberText == cacheReferenceNumberText) &&
            (identical(other.cacheUuid, cacheUuid) ||
                other.cacheUuid == cacheUuid) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.referenceNumberValidationError,
                    referenceNumberValidationError) ||
                other.referenceNumberValidationError ==
                    referenceNumberValidationError) &&
            (identical(
                    other.isRealTimeValidationMode, isRealTimeValidationMode) ||
                other.isRealTimeValidationMode == isRealTimeValidationMode) &&
            (identical(other.isOnQrScreen, isOnQrScreen) ||
                other.isOnQrScreen == isOnQrScreen) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.pickImageError, pickImageError) ||
                other.pickImageError == pickImageError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      referenceNumberStatus,
      referenceNumberInfo,
      openAccountAppTemp,
      identityVerificationPhases,
      referenceNumberText,
      cacheReferenceNumberText,
      cacheUuid,
      isLoading,
      image,
      referenceNumberValidationError,
      isRealTimeValidationMode,
      isOnQrScreen,
      error,
      pickImageError);

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdentityReferenceNumberStateImplCopyWith<
          _$IdentityReferenceNumberStateImpl>
      get copyWith => __$$IdentityReferenceNumberStateImplCopyWithImpl<
          _$IdentityReferenceNumberStateImpl>(this, _$identity);
}

abstract class _IdentityReferenceNumberState
    implements IdentityReferenceNumberState {
  const factory _IdentityReferenceNumberState(
      {final ReferenceNumberStatus referenceNumberStatus,
      final ReferenceNumberInfo? referenceNumberInfo,
      final OpenAccountAppTemp? openAccountAppTemp,
      final IdentityVerificationPhases? identityVerificationPhases,
      final String referenceNumberText,
      final String cacheReferenceNumberText,
      final String cacheUuid,
      final bool isLoading,
      final XFile? image,
      final ValidationError referenceNumberValidationError,
      final bool isRealTimeValidationMode,
      final bool isOnQrScreen,
      final AppError? error,
      final AppError? pickImageError}) = _$IdentityReferenceNumberStateImpl;

  @override
  ReferenceNumberStatus get referenceNumberStatus;
  @override
  ReferenceNumberInfo? get referenceNumberInfo;
  @override
  OpenAccountAppTemp? get openAccountAppTemp;
  @override
  IdentityVerificationPhases? get identityVerificationPhases;
  @override
  String get referenceNumberText; // 本人情報送信時に使用
  @override
  String get cacheReferenceNumberText;
  @override
  String get cacheUuid;
  @override
  bool get isLoading;
  @override
  XFile? get image;
  @override
  ValidationError get referenceNumberValidationError;

  /// 項目編集時にバリデーションチェックを実行するかどうか
  @override
  bool get isRealTimeValidationMode;

  /// QRコード読み込みを実施した場合
  /// 一度POP処理にてお手続番号入力画面遷移後後続画面に遷移
  /// 現状QR画面からPUSH遷移することがない為、フラグのリセットはPOP時にのみ実施する形になっている
  @override
  bool get isOnQrScreen;
  @override
  AppError? get error;
  @override
  AppError? get pickImageError;

  /// Create a copy of IdentityReferenceNumberState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdentityReferenceNumberStateImplCopyWith<
          _$IdentityReferenceNumberStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
