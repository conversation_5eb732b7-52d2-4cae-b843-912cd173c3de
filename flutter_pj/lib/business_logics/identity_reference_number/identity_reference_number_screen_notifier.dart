import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/interfaces/identity_reference_number_repository.dart';
import 'package:dtp_app/business_logics/interfaces/image_picker_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/business_logics/mixin/input_validator_mixin.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/models/identity_verification_reference_number_info/identity_verification_reference_number_info.dart';
import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';
import 'package:dtp_app/repositories/identity_verification/reference_number/reference_number_repository_impl.dart';
import 'package:dtp_app/repositories/image_picker/image_picker_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_sdk/jpki_sdk_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

part 'identity_reference_number_screen_notifier.freezed.dart';

enum ReferenceNumberStatus {
  // 未確認状態
  unchecked,
  // true
  passed,
  //確認失敗
  checkFailed,
}

@freezed
class IdentityReferenceNumberState with _$IdentityReferenceNumberState {
  const factory IdentityReferenceNumberState({
    @Default(ReferenceNumberStatus.unchecked)
    ReferenceNumberStatus referenceNumberStatus,
    ReferenceNumberInfo? referenceNumberInfo,
    OpenAccountAppTemp? openAccountAppTemp,
    IdentityVerificationPhases? identityVerificationPhases,
    @Default('') String referenceNumberText,
    // 本人情報送信時に使用
    @Default('') String cacheReferenceNumberText,
    @Default('') String cacheUuid,
    @Default(false) bool isLoading,
    XFile? image,
    @Default(ValidationError.none())
    ValidationError referenceNumberValidationError,

    /// 項目編集時にバリデーションチェックを実行するかどうか
    @Default(false) bool isRealTimeValidationMode,

    /// QRコード読み込みを実施した場合
    /// 一度POP処理にてお手続番号入力画面遷移後後続画面に遷移
    /// 現状QR画面からPUSH遷移することがない為、フラグのリセットはPOP時にのみ実施する形になっている
    @Default(false) bool isOnQrScreen,
    AppError? error,
    AppError? pickImageError,
  }) = _IdentityReferenceNumberState;
}

extension IdentityReferenceNumberStateExt on IdentityReferenceNumberState {
  // MobileScanner上に表示するエラーメッセージを取得
  String get mobileScannerErrorMessage {
    if (referenceNumberValidationError.hasError) {
      final qrCodeValidationError =
          '${IdentityVerificationErrorInfo.qrCodeValidationErrorCode}\n${IdentityVerificationErrorInfo.qrCodeValidationErrorMessage}';
      return qrCodeValidationError;
    } else {
      final invalidReferenceNumberError =
          '${IdentityVerificationErrorInfo.invalidReferenceNumberErrorCode}\n${IdentityVerificationErrorInfo.invalidReferenceNumberErrorMessage}';
      return invalidReferenceNumberError;
    }
  }
}

final identityReferenceNumberScreenProvider = StateNotifierProvider.autoDispose<
    IdentityReferenceNumberScreenNotifier, IdentityReferenceNumberState>(
  (ref) => IdentityReferenceNumberScreenNotifier(
    identityReferenceNumberRepository:
        ref.read(identityReferenceNumberRepositoryProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
    imagePickerRepository: ref.read(imagePickerRepositoryProvider),
    jpkiSdkRepository: ref.read(jpkiSdkRepositoryProvider),
    mdManager: ref.read(mdManagerProvider),
  ),
);

class IdentityReferenceNumberScreenNotifier
    extends StateNotifier<IdentityReferenceNumberState>
    with InputValidatorMixin {
  IdentityReferenceNumberScreenNotifier({
    required this.identityReferenceNumberRepository,
    required this.analyticsLogController,
    required this.imagePickerRepository,
    required this.jpkiSdkRepository,
    required this.mdManager,
  }) : super(const IdentityReferenceNumberState());

  final IdentityReferenceNumberRepository identityReferenceNumberRepository;
  final AnalyticsLogController analyticsLogController;
  final ImagePickerRepository imagePickerRepository;
  final JpkiSdkRepository jpkiSdkRepository;
  final MemoryDataManager mdManager;

  void setReferenceNumber({required String referenceNumber}) {
    state = state.copyWith(
      referenceNumberText: referenceNumber,
    );
    // 一度バリデーションチェックに抵触した後は、リアルタイムバリデーションを実施
    if (state.isRealTimeValidationMode) {
      state = state.copyWith(
        referenceNumberValidationError:
            validateReferenceNumber(referenceNumber),
      );
    }
  }

  String setQrErrorMessage(MobileScannerException error) {
    String errorMessage = ErrorInfo.defaultErrorMessage;
    String errorCode = ErrorInfo.defaultErrorCode;

    switch (error.errorCode) {
      case MobileScannerErrorCode.controllerUninitialized:
        errorCode = IdentityVerificationErrorInfo.qrCodeValidationErrorCode;
        errorMessage =
            IdentityVerificationErrorInfo.qrCodeValidationErrorMessage;
      case MobileScannerErrorCode.permissionDenied:
        errorCode = IdentityVerificationErrorInfo.permissionDeniedErrorCode;
        errorMessage =
            IdentityVerificationErrorInfo.permissionDeniedErrorMessage;
      case MobileScannerErrorCode.unsupported:
        errorCode = IdentityVerificationErrorInfo.qrUnsupportedErrorCode;
        errorMessage = IdentityVerificationErrorInfo.qrUnsupportedErrorMessage;
      default:
        break;
    }
    return '$errorCode\n$errorMessage';
  }

  Future<XFile?> pickImage() async {
    final res = await imagePickerRepository.pickImage();
    return await res.when(
      success: (image) async {
        state = state.copyWith(image: image);
        return image;
      },
      failure: (error) async {
        state = state.copyWith(pickImageError: error);
        return null;
      },
    );
  }

  void setImageErrorMassage(String errorMessage) {
    state = state.copyWith(pickImageError: AppError(message: errorMessage));
  }

  void clearImageErrorMassage() {
    state = state.copyWith(pickImageError: null);
  }

  void clearReferenceNumberValidationError() {
    state = state.copyWith(
      referenceNumberValidationError: ValidationError.none(),
    );
  }

  void _loadingNow() {
    state = state.copyWith(isLoading: true);
  }

  void _loadingFinish() {
    state = state.copyWith(isLoading: false);
  }

  void onPopQrScreen() {
    state = state.copyWith(isOnQrScreen: false);
  }

  void initializeQrScreen(MobileScannerController qrController) {
    qrController.stop();
    state = state.copyWith(isOnQrScreen: true);
  }

  void resetReferenceNumberStatus({bool isFailed = false}) {
    //誤ったお手続番号取得時にはreferenceNumberStatusをuncheckedではなくcheckfailedに設定する。
    final isReferenceNumberStatus = isFailed
        ? ReferenceNumberStatus.checkFailed
        : ReferenceNumberStatus.unchecked;
    state = state.copyWith(
      referenceNumberStatus: isReferenceNumberStatus,
      identityVerificationPhases: null,
      isLoading: false,
    );
  }

  /// 口座開設申込情報一時保存情報を取得
  Future<void> fetchOpenAccountAppTemp({
    required String referenceNumber,
  }) async {
    // エラー情報をリセット
    state = state.copyWith(error: null);

    final res = await identityReferenceNumberRepository.fetchOpenAccountAppTemp(
      referenceNumber: referenceNumber,
    );
    await res.when(
      success: (openAccountAppTemp) async {
        // JPKI・eKYC読み取り完了後、本人確認情報保存APIで使用するため、mdManagerに保存
        await mdManager.userType.save(openAccountAppTemp.userType);

        // FireBaseのユーザープロパティとして送信するためにお手続き番号mdManagerに保存
        await mdManager.referenceNumber.save(referenceNumber);

        state = state.copyWith(
          openAccountAppTemp: openAccountAppTemp,
          identityVerificationPhases: openAccountAppTemp
              .getIdentityVerificationPhases(state.referenceNumberInfo),
        );
      },
      failure: (error) {
        state = state.copyWith(error: error);
      },
    );
  }

  /// お手続き番号のバリデーションチェック
  ValidationError validateReferenceNumber(String? referenceNumber) {
    const minLength = 13;
    const maxLength = 14;
    final result = checkReferenceNumberLengthEquals(
      referenceNumber,
      minLength,
      maxLength,
    );

    state = state.copyWith(referenceNumberValidationError: result);

    // 一度バリデーションチェックに抵触した場合は、リアルタイムバリデーションモードに移行
    if (result.hasError) {
      state = state.copyWith(isRealTimeValidationMode: true);
    }
    return result;
  }

  /// お手続き番号の有無および、口座開設申込ステータスを取得
  Future<void> checkReferenceNumberStatus({
    required String referenceNumber,
  }) async {
    // エラー情報をリセット
    state = state.copyWith(error: null);

    final res = await identityReferenceNumberRepository
        .checkReferenceNumberStatus(referenceNumber: referenceNumber);
    await res.when(
      success: (referenceNumberInfo) async {
        if (referenceNumberInfo.isAccountOpeningRequestSubmitted) {
          state = state.copyWith(
            referenceNumberStatus: ReferenceNumberStatus.passed,
          );
          _loadingFinish();
          // FireBaseのユーザープロパティとして送信するためにお手続き番号mdManagerに保存
          await mdManager.referenceNumber.save(referenceNumber);
          return;
        }
        state = state.copyWith(
          referenceNumberInfo: referenceNumberInfo,
          cacheReferenceNumberText: referenceNumber,
        );
        await fetchOpenAccountAppTemp(
          referenceNumber: referenceNumber,
        );
      },
      failure: (error) {
        state = state.copyWith(error: error);
      },
    );
  }

  /// エラー情報をリセット
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// QRコード検知
  Future<void> qrCodeDetect(BarcodeCapture barcodes) async {
    // 複数検知した場合は最初のQRコードを利用
    final firstBarcode = barcodes.barcodes.firstOrNull;
    if (firstBarcode != null) {
      // お手続番号を取得
      final referenceNumber = firstBarcode.displayValue ?? '';
      final result = validateReferenceNumber(referenceNumber);

      // QRから読み込んだ値をstateに格納
      setReferenceNumber(
        referenceNumber: referenceNumber,
      );
      // エラー時には処理を中断し、ダイアログを表示
      if (result.hasError) {
        state = state.copyWith(
          error: AppError(
            message: IdentityVerificationErrorInfo
                .invalidReferenceNumberErrorMessage,
            code: IdentityVerificationErrorInfo.invalidReferenceNumberErrorCode,
            clearErrorOnShowDialog: () => state = state.copyWith(
              error: null,
              referenceNumberValidationError: ValidationError.none(),
            ),
          ),
        );
        resetReferenceNumberStatus(isFailed: true);
        return;
      }
      // APIを叩く
      await getUuid();
    }
  }

  // 後続画面で使用するUUIDを取得
  Future<void> getUuid() async {
    _loadingNow();
    final result = await identityReferenceNumberRepository.getUuid(
      referenceNumber: state.referenceNumberText,
    );
    await result.when(
      success: (result) async {
        state = state.copyWith(cacheUuid: result);
        await checkReferenceNumberStatus(
          referenceNumber: state.referenceNumberText,
        );
      },
      failure: (error) {
        resetReferenceNumberStatus(isFailed: true);
        state = state.copyWith(
          error: AppError(
            code: IdentityVerificationErrorInfo.invalidReferenceNumberErrorCode,
            message: IdentityVerificationErrorInfo
                .invalidReferenceNumberErrorMessage,
          ),
        );
      },
    );
    // 誤った挙動を防ぐため、全ての処理が完了したのちloading状態を更新
    _loadingFinish();

    await analyticsLogController.sendAttribute();
  }

  // AndroidのNFC状態取得
  // AndroidのみNFCをON/OFF設定変更できるためAndroidのNFC状態取得し、
  // 後続処理にてNFC対象端末 かつ　NFC設定OFFの場合(Androidのみ)は証明書選択画面へ遷移させる
  // なお、NFC対象端末かの判定（Android/iOS）はreference_number_screen.dart内nfc_managerを用いて実施
  Future<AndroidNfcStatus> getAndroidNfcStatus() async {
    final result = await jpkiSdkRepository.getAndroidNfcStatus();
    AndroidNfcStatus status = AndroidNfcStatus.disabled;

    result.when(
      success: (result) {
        status = result;
      },
      failure: (_) {
        status = AndroidNfcStatus.notSupported;
      },
    );
    return status;
  }
}
