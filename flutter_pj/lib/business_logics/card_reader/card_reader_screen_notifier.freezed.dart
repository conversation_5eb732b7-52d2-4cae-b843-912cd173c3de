// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card_reader_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CardReaderScreenState {
  int? get retryCounter => throw _privateConstructorUsedError; // 残試行回数
  bool get isSdkReading =>
      throw _privateConstructorUsedError; // SDKでカード読み取り実行中か
  bool get isComplete => throw _privateConstructorUsedError; // 本人確認まで完了したか
  bool get isActive => throw _privateConstructorUsedError; // 自画面が最前面か
  bool get cardReadingIosCompleted =>
      throw _privateConstructorUsedError; // iOSでのカード読み取り完了フラグ
  bool get cardReadingAndroidCompleted =>
      throw _privateConstructorUsedError; // Androidでのカード読み取り完了フラグ
  bool get cardReadingAndroidCanceled =>
      throw _privateConstructorUsedError; // Androidでのカード読み取りキャンセルフラグ
  String get nameBp =>
      throw _privateConstructorUsedError; // 署名用証明書の氏名(本人確認APIのレスポンスを格納)
  String get addressBp =>
      throw _privateConstructorUsedError; //署名用証明書の住所（(本人確認APIのレスポンスを格納)
  String get referenceNumber =>
      throw _privateConstructorUsedError; // お手続き番号(前画面から取得)
  String get uuid => throw _privateConstructorUsedError; // uuid(前画面から取得)
  String get userTypeNumber => throw _privateConstructorUsedError; // 手続き者属性フラグ
  ErrorPattern? get errorPattern =>
      throw _privateConstructorUsedError; // エラーパターン
  AppError? get sdkError =>
      throw _privateConstructorUsedError; // SDK内で発生したエラー内容
  AppError? get identificationError =>
      throw _privateConstructorUsedError; // 本人確認APIで発生したエラー内容
  AppError? get sendIdentityInfoError => throw _privateConstructorUsedError;

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CardReaderScreenStateCopyWith<CardReaderScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CardReaderScreenStateCopyWith<$Res> {
  factory $CardReaderScreenStateCopyWith(CardReaderScreenState value,
          $Res Function(CardReaderScreenState) then) =
      _$CardReaderScreenStateCopyWithImpl<$Res, CardReaderScreenState>;
  @useResult
  $Res call(
      {int? retryCounter,
      bool isSdkReading,
      bool isComplete,
      bool isActive,
      bool cardReadingIosCompleted,
      bool cardReadingAndroidCompleted,
      bool cardReadingAndroidCanceled,
      String nameBp,
      String addressBp,
      String referenceNumber,
      String uuid,
      String userTypeNumber,
      ErrorPattern? errorPattern,
      AppError? sdkError,
      AppError? identificationError,
      AppError? sendIdentityInfoError});

  $AppErrorCopyWith<$Res>? get sdkError;
  $AppErrorCopyWith<$Res>? get identificationError;
  $AppErrorCopyWith<$Res>? get sendIdentityInfoError;
}

/// @nodoc
class _$CardReaderScreenStateCopyWithImpl<$Res,
        $Val extends CardReaderScreenState>
    implements $CardReaderScreenStateCopyWith<$Res> {
  _$CardReaderScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? retryCounter = freezed,
    Object? isSdkReading = null,
    Object? isComplete = null,
    Object? isActive = null,
    Object? cardReadingIosCompleted = null,
    Object? cardReadingAndroidCompleted = null,
    Object? cardReadingAndroidCanceled = null,
    Object? nameBp = null,
    Object? addressBp = null,
    Object? referenceNumber = null,
    Object? uuid = null,
    Object? userTypeNumber = null,
    Object? errorPattern = freezed,
    Object? sdkError = freezed,
    Object? identificationError = freezed,
    Object? sendIdentityInfoError = freezed,
  }) {
    return _then(_value.copyWith(
      retryCounter: freezed == retryCounter
          ? _value.retryCounter
          : retryCounter // ignore: cast_nullable_to_non_nullable
              as int?,
      isSdkReading: null == isSdkReading
          ? _value.isSdkReading
          : isSdkReading // ignore: cast_nullable_to_non_nullable
              as bool,
      isComplete: null == isComplete
          ? _value.isComplete
          : isComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      cardReadingIosCompleted: null == cardReadingIosCompleted
          ? _value.cardReadingIosCompleted
          : cardReadingIosCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      cardReadingAndroidCompleted: null == cardReadingAndroidCompleted
          ? _value.cardReadingAndroidCompleted
          : cardReadingAndroidCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      cardReadingAndroidCanceled: null == cardReadingAndroidCanceled
          ? _value.cardReadingAndroidCanceled
          : cardReadingAndroidCanceled // ignore: cast_nullable_to_non_nullable
              as bool,
      nameBp: null == nameBp
          ? _value.nameBp
          : nameBp // ignore: cast_nullable_to_non_nullable
              as String,
      addressBp: null == addressBp
          ? _value.addressBp
          : addressBp // ignore: cast_nullable_to_non_nullable
              as String,
      referenceNumber: null == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      userTypeNumber: null == userTypeNumber
          ? _value.userTypeNumber
          : userTypeNumber // ignore: cast_nullable_to_non_nullable
              as String,
      errorPattern: freezed == errorPattern
          ? _value.errorPattern
          : errorPattern // ignore: cast_nullable_to_non_nullable
              as ErrorPattern?,
      sdkError: freezed == sdkError
          ? _value.sdkError
          : sdkError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      identificationError: freezed == identificationError
          ? _value.identificationError
          : identificationError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      sendIdentityInfoError: freezed == sendIdentityInfoError
          ? _value.sendIdentityInfoError
          : sendIdentityInfoError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get sdkError {
    if (_value.sdkError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.sdkError!, (value) {
      return _then(_value.copyWith(sdkError: value) as $Val);
    });
  }

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get identificationError {
    if (_value.identificationError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.identificationError!, (value) {
      return _then(_value.copyWith(identificationError: value) as $Val);
    });
  }

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get sendIdentityInfoError {
    if (_value.sendIdentityInfoError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.sendIdentityInfoError!, (value) {
      return _then(_value.copyWith(sendIdentityInfoError: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CardReaderScreenStateImplCopyWith<$Res>
    implements $CardReaderScreenStateCopyWith<$Res> {
  factory _$$CardReaderScreenStateImplCopyWith(
          _$CardReaderScreenStateImpl value,
          $Res Function(_$CardReaderScreenStateImpl) then) =
      __$$CardReaderScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? retryCounter,
      bool isSdkReading,
      bool isComplete,
      bool isActive,
      bool cardReadingIosCompleted,
      bool cardReadingAndroidCompleted,
      bool cardReadingAndroidCanceled,
      String nameBp,
      String addressBp,
      String referenceNumber,
      String uuid,
      String userTypeNumber,
      ErrorPattern? errorPattern,
      AppError? sdkError,
      AppError? identificationError,
      AppError? sendIdentityInfoError});

  @override
  $AppErrorCopyWith<$Res>? get sdkError;
  @override
  $AppErrorCopyWith<$Res>? get identificationError;
  @override
  $AppErrorCopyWith<$Res>? get sendIdentityInfoError;
}

/// @nodoc
class __$$CardReaderScreenStateImplCopyWithImpl<$Res>
    extends _$CardReaderScreenStateCopyWithImpl<$Res,
        _$CardReaderScreenStateImpl>
    implements _$$CardReaderScreenStateImplCopyWith<$Res> {
  __$$CardReaderScreenStateImplCopyWithImpl(_$CardReaderScreenStateImpl _value,
      $Res Function(_$CardReaderScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? retryCounter = freezed,
    Object? isSdkReading = null,
    Object? isComplete = null,
    Object? isActive = null,
    Object? cardReadingIosCompleted = null,
    Object? cardReadingAndroidCompleted = null,
    Object? cardReadingAndroidCanceled = null,
    Object? nameBp = null,
    Object? addressBp = null,
    Object? referenceNumber = null,
    Object? uuid = null,
    Object? userTypeNumber = null,
    Object? errorPattern = freezed,
    Object? sdkError = freezed,
    Object? identificationError = freezed,
    Object? sendIdentityInfoError = freezed,
  }) {
    return _then(_$CardReaderScreenStateImpl(
      retryCounter: freezed == retryCounter
          ? _value.retryCounter
          : retryCounter // ignore: cast_nullable_to_non_nullable
              as int?,
      isSdkReading: null == isSdkReading
          ? _value.isSdkReading
          : isSdkReading // ignore: cast_nullable_to_non_nullable
              as bool,
      isComplete: null == isComplete
          ? _value.isComplete
          : isComplete // ignore: cast_nullable_to_non_nullable
              as bool,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      cardReadingIosCompleted: null == cardReadingIosCompleted
          ? _value.cardReadingIosCompleted
          : cardReadingIosCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      cardReadingAndroidCompleted: null == cardReadingAndroidCompleted
          ? _value.cardReadingAndroidCompleted
          : cardReadingAndroidCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      cardReadingAndroidCanceled: null == cardReadingAndroidCanceled
          ? _value.cardReadingAndroidCanceled
          : cardReadingAndroidCanceled // ignore: cast_nullable_to_non_nullable
              as bool,
      nameBp: null == nameBp
          ? _value.nameBp
          : nameBp // ignore: cast_nullable_to_non_nullable
              as String,
      addressBp: null == addressBp
          ? _value.addressBp
          : addressBp // ignore: cast_nullable_to_non_nullable
              as String,
      referenceNumber: null == referenceNumber
          ? _value.referenceNumber
          : referenceNumber // ignore: cast_nullable_to_non_nullable
              as String,
      uuid: null == uuid
          ? _value.uuid
          : uuid // ignore: cast_nullable_to_non_nullable
              as String,
      userTypeNumber: null == userTypeNumber
          ? _value.userTypeNumber
          : userTypeNumber // ignore: cast_nullable_to_non_nullable
              as String,
      errorPattern: freezed == errorPattern
          ? _value.errorPattern
          : errorPattern // ignore: cast_nullable_to_non_nullable
              as ErrorPattern?,
      sdkError: freezed == sdkError
          ? _value.sdkError
          : sdkError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      identificationError: freezed == identificationError
          ? _value.identificationError
          : identificationError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      sendIdentityInfoError: freezed == sendIdentityInfoError
          ? _value.sendIdentityInfoError
          : sendIdentityInfoError // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$CardReaderScreenStateImpl implements _CardReaderScreenState {
  const _$CardReaderScreenStateImpl(
      {this.retryCounter,
      this.isSdkReading = false,
      this.isComplete = false,
      this.isActive = true,
      this.cardReadingIosCompleted = false,
      this.cardReadingAndroidCompleted = false,
      this.cardReadingAndroidCanceled = false,
      this.nameBp = '',
      this.addressBp = '',
      this.referenceNumber = '',
      this.uuid = '',
      this.userTypeNumber = '',
      this.errorPattern = null,
      this.sdkError,
      this.identificationError,
      this.sendIdentityInfoError});

  @override
  final int? retryCounter;
// 残試行回数
  @override
  @JsonKey()
  final bool isSdkReading;
// SDKでカード読み取り実行中か
  @override
  @JsonKey()
  final bool isComplete;
// 本人確認まで完了したか
  @override
  @JsonKey()
  final bool isActive;
// 自画面が最前面か
  @override
  @JsonKey()
  final bool cardReadingIosCompleted;
// iOSでのカード読み取り完了フラグ
  @override
  @JsonKey()
  final bool cardReadingAndroidCompleted;
// Androidでのカード読み取り完了フラグ
  @override
  @JsonKey()
  final bool cardReadingAndroidCanceled;
// Androidでのカード読み取りキャンセルフラグ
  @override
  @JsonKey()
  final String nameBp;
// 署名用証明書の氏名(本人確認APIのレスポンスを格納)
  @override
  @JsonKey()
  final String addressBp;
//署名用証明書の住所（(本人確認APIのレスポンスを格納)
  @override
  @JsonKey()
  final String referenceNumber;
// お手続き番号(前画面から取得)
  @override
  @JsonKey()
  final String uuid;
// uuid(前画面から取得)
  @override
  @JsonKey()
  final String userTypeNumber;
// 手続き者属性フラグ
  @override
  @JsonKey()
  final ErrorPattern? errorPattern;
// エラーパターン
  @override
  final AppError? sdkError;
// SDK内で発生したエラー内容
  @override
  final AppError? identificationError;
// 本人確認APIで発生したエラー内容
  @override
  final AppError? sendIdentityInfoError;

  @override
  String toString() {
    return 'CardReaderScreenState(retryCounter: $retryCounter, isSdkReading: $isSdkReading, isComplete: $isComplete, isActive: $isActive, cardReadingIosCompleted: $cardReadingIosCompleted, cardReadingAndroidCompleted: $cardReadingAndroidCompleted, cardReadingAndroidCanceled: $cardReadingAndroidCanceled, nameBp: $nameBp, addressBp: $addressBp, referenceNumber: $referenceNumber, uuid: $uuid, userTypeNumber: $userTypeNumber, errorPattern: $errorPattern, sdkError: $sdkError, identificationError: $identificationError, sendIdentityInfoError: $sendIdentityInfoError)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CardReaderScreenStateImpl &&
            (identical(other.retryCounter, retryCounter) ||
                other.retryCounter == retryCounter) &&
            (identical(other.isSdkReading, isSdkReading) ||
                other.isSdkReading == isSdkReading) &&
            (identical(other.isComplete, isComplete) ||
                other.isComplete == isComplete) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(
                    other.cardReadingIosCompleted, cardReadingIosCompleted) ||
                other.cardReadingIosCompleted == cardReadingIosCompleted) &&
            (identical(other.cardReadingAndroidCompleted,
                    cardReadingAndroidCompleted) ||
                other.cardReadingAndroidCompleted ==
                    cardReadingAndroidCompleted) &&
            (identical(other.cardReadingAndroidCanceled,
                    cardReadingAndroidCanceled) ||
                other.cardReadingAndroidCanceled ==
                    cardReadingAndroidCanceled) &&
            (identical(other.nameBp, nameBp) || other.nameBp == nameBp) &&
            (identical(other.addressBp, addressBp) ||
                other.addressBp == addressBp) &&
            (identical(other.referenceNumber, referenceNumber) ||
                other.referenceNumber == referenceNumber) &&
            (identical(other.uuid, uuid) || other.uuid == uuid) &&
            (identical(other.userTypeNumber, userTypeNumber) ||
                other.userTypeNumber == userTypeNumber) &&
            (identical(other.errorPattern, errorPattern) ||
                other.errorPattern == errorPattern) &&
            (identical(other.sdkError, sdkError) ||
                other.sdkError == sdkError) &&
            (identical(other.identificationError, identificationError) ||
                other.identificationError == identificationError) &&
            (identical(other.sendIdentityInfoError, sendIdentityInfoError) ||
                other.sendIdentityInfoError == sendIdentityInfoError));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      retryCounter,
      isSdkReading,
      isComplete,
      isActive,
      cardReadingIosCompleted,
      cardReadingAndroidCompleted,
      cardReadingAndroidCanceled,
      nameBp,
      addressBp,
      referenceNumber,
      uuid,
      userTypeNumber,
      errorPattern,
      sdkError,
      identificationError,
      sendIdentityInfoError);

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CardReaderScreenStateImplCopyWith<_$CardReaderScreenStateImpl>
      get copyWith => __$$CardReaderScreenStateImplCopyWithImpl<
          _$CardReaderScreenStateImpl>(this, _$identity);
}

abstract class _CardReaderScreenState implements CardReaderScreenState {
  const factory _CardReaderScreenState(
      {final int? retryCounter,
      final bool isSdkReading,
      final bool isComplete,
      final bool isActive,
      final bool cardReadingIosCompleted,
      final bool cardReadingAndroidCompleted,
      final bool cardReadingAndroidCanceled,
      final String nameBp,
      final String addressBp,
      final String referenceNumber,
      final String uuid,
      final String userTypeNumber,
      final ErrorPattern? errorPattern,
      final AppError? sdkError,
      final AppError? identificationError,
      final AppError? sendIdentityInfoError}) = _$CardReaderScreenStateImpl;

  @override
  int? get retryCounter; // 残試行回数
  @override
  bool get isSdkReading; // SDKでカード読み取り実行中か
  @override
  bool get isComplete; // 本人確認まで完了したか
  @override
  bool get isActive; // 自画面が最前面か
  @override
  bool get cardReadingIosCompleted; // iOSでのカード読み取り完了フラグ
  @override
  bool get cardReadingAndroidCompleted; // Androidでのカード読み取り完了フラグ
  @override
  bool get cardReadingAndroidCanceled; // Androidでのカード読み取りキャンセルフラグ
  @override
  String get nameBp; // 署名用証明書の氏名(本人確認APIのレスポンスを格納)
  @override
  String get addressBp; //署名用証明書の住所（(本人確認APIのレスポンスを格納)
  @override
  String get referenceNumber; // お手続き番号(前画面から取得)
  @override
  String get uuid; // uuid(前画面から取得)
  @override
  String get userTypeNumber; // 手続き者属性フラグ
  @override
  ErrorPattern? get errorPattern; // エラーパターン
  @override
  AppError? get sdkError; // SDK内で発生したエラー内容
  @override
  AppError? get identificationError; // 本人確認APIで発生したエラー内容
  @override
  AppError? get sendIdentityInfoError;

  /// Create a copy of CardReaderScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CardReaderScreenStateImplCopyWith<_$CardReaderScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
