import 'package:dtp_app/business_logics/identity_document/identity_document_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/interfaces/identity_verification_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_identification_api_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/identity_verification_info/identity_verification_info.dart';
import 'package:dtp_app/repositories/identity_verification/identity_verification_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_identification_api/jpki_identification_api_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_sdk/jpki_sdk_repository_impl.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/sdk_parameter.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'card_reader_screen_notifier.freezed.dart';

@freezed
class CardReaderScreenState with _$CardReaderScreenState {
  const factory CardReaderScreenState({
    int? retryCounter, // 残試行回数
    @Default(false) bool isSdkReading, // SDKでカード読み取り実行中か
    @Default(false) bool isComplete, // 本人確認まで完了したか
    @Default(true) bool isActive, // 自画面が最前面か
    @Default(false) bool cardReadingIosCompleted, // iOSでのカード読み取り完了フラグ
    @Default(false) bool cardReadingAndroidCompleted, // Androidでのカード読み取り完了フラグ
    @Default(false) bool cardReadingAndroidCanceled, // Androidでのカード読み取りキャンセルフラグ
    @Default('') String nameBp, // 署名用証明書の氏名(本人確認APIのレスポンスを格納)
    @Default('') String addressBp, //署名用証明書の住所（(本人確認APIのレスポンスを格納)
    @Default('') String referenceNumber, // お手続き番号(前画面から取得)
    @Default('') String uuid, // uuid(前画面から取得)
    @Default('') String userTypeNumber, // 手続き者属性フラグ
    @Default(null) ErrorPattern? errorPattern, // エラーパターン
    AppError? sdkError, // SDK内で発生したエラー内容
    AppError? identificationError, // 本人確認APIで発生したエラー内容
    AppError? sendIdentityInfoError, // 本人情報送信処理で発生したエラー内容
  }) = _CardReaderScreenState;
}

final cardReaderScreenProvider = StateNotifierProvider.autoDispose<
    CardReaderScreenNotifier, CardReaderScreenState>((ref) {
  // お手続き番号、uuidを取得する
  final referenceNumber =
      ref.watch(identityReferenceNumberScreenProvider).cacheReferenceNumberText;
  final uuid = ref.watch(identityReferenceNumberScreenProvider).cacheUuid;
  final userTypeNumber =
      ref.watch(identityDocumentStateScreenProvider).userTypeNumber;
  return CardReaderScreenNotifier(
    jpkiSdkRepository: ref.watch(jpkiSdkRepositoryProvider),
    jpkiIdentificationApiRepository:
        ref.watch(jpkiIdentificationApiRepositoryProvider),
    identityVerificationRepository:
        ref.watch(identityVerificationRepositoryProvider),
    referenceNumber: referenceNumber,
    uuid: uuid,
    userTypeNumber: userTypeNumber,
  );
});

class CardReaderScreenNotifier extends StateNotifier<CardReaderScreenState> {
  CardReaderScreenNotifier({
    required this.jpkiSdkRepository,
    required this.jpkiIdentificationApiRepository,
    required this.identityVerificationRepository,
    required String referenceNumber,
    required String uuid,
    required String userTypeNumber,
  }) : super(
          CardReaderScreenState(
            referenceNumber: referenceNumber,
            uuid: uuid,
            userTypeNumber: userTypeNumber,
          ),
        );

  final JpkiSdkRepository jpkiSdkRepository;
  final JpkiIdentificationApiRepository jpkiIdentificationApiRepository;
  final IdentityVerificationRepository identityVerificationRepository;

  // マイナンバカードを読み取る(iOS)
  Future<void> startNfcSession() async {
    //残試行回数とエラー情報をリセット
    state = state.copyWith(
      retryCounter: null,
      sdkError: null,
      errorPattern: null,
    );
    // NFCリーダーモード設定の呼び出し判定のリセット
    state = state.copyWith(isSdkReading: false);

    final result =
        await jpkiSdkRepository.startNfcSession(SdkParameter.statusMessage);
    await result.when(
      success: (isSdkReading) async {
        state = state.copyWith(isSdkReading: true);
        // NFCセッション開始の次にスキャン文言の変更を行う。
        await _setCardScanStatusMessage();
      },
      failure: (error) {
        state = state.copyWith(sdkError: error);
      },
    );
  }

  // ICカード通信正常終了(iOS)
  Future<void> _finishNfcSessionSuccess() async {
    final result = await jpkiSdkRepository
        .finishNfcSessionSuccess(SdkParameter.successStatusMessage);
    await result.when(
      success: (_) async {
        state = state.copyWith(cardReadingIosCompleted: true);
      },
      failure: (error) {
        state = state.copyWith(sdkError: error);
      },
    );
  }

  // ICカード通信エラー終了(iOS)
  Future<void> _finishNfcSessionError() async {
    final result = await jpkiSdkRepository
        .finishNfcSessionError(SdkParameter.errorStatusMessage);

    // エラー情報を元にエラーパターンを判定
    updateErrorPattern(
      ErrorInfoJpkiSdkIos.getErrorPattern(
        errorCode: state.sdkError?.code,
        details: state.sdkError?.details,
      ),
    );

    result.when(
      success: (_) {},
      failure: (error) {
        state = state.copyWith(sdkError: error);
      },
    );
  }

  // スキャン文言変更(iOS)
  Future<void> _setCardScanStatusMessage() async {
    final result = await jpkiSdkRepository
        .setCardScanStatusMessage(SdkParameter.readingMessage);
    await result.when(
      success: (_) async {
        // スキャン文言変更に成功した際はパスワード照合を行う
        await _verifyPasswordForSign();
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        // スキャン文言変更に失敗した際はエラー終了
        await _finishNfcSessionError();
      },
    );
  }

  // パスワード照合(iOS)
  Future<void> _verifyPasswordForSign() async {
    final result = await jpkiSdkRepository.verifyPasswordForSign();
    await result.when(
      success: (_) async {
        // パスワード照合に成功した際は署名用電子証明書を取得
        await _getCertificateForSign();
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        // パスワード照合に失敗した際は残試行回数を取得
        await _getRetryCounterForSign();
      },
    );
  }

  // 署名用電子証明書取得(iOS)
  Future<void> _getCertificateForSign() async {
    final result = await jpkiSdkRepository.getCertificateForSign();
    await result.when(
      success: (_) async {
        // 署名用電子証明書取得に成功した際は署名用電子署名を生成
        await _makeSignatureForSign();
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        // 署名用電子証明書取得に失敗した際はエラー終了
        await _finishNfcSessionError();
      },
    );
  }

  // 署名用電子署名作成(iOS)
  Future<void> _makeSignatureForSign() async {
    final result = await jpkiSdkRepository.makeSignatureForSign();
    await result.when(
      success: (_) async {
        // 署名用電子署名作成に成功した際は正常終了
        await _finishNfcSessionSuccess();
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        // 署名用電子署名作成に失敗した際はエラー終了
        await _finishNfcSessionError();
      },
    );
  }

  // 残試行回数取得(iOS)
  Future<void> _getRetryCounterForSign() async {
    final result = await jpkiSdkRepository.getRetryCounterForSign();
    await result.when(
      success: (retryCounter) async {
        state = state.copyWith(retryCounter: retryCounter);
        // パスワード照合に失敗しているのでエラー終了
        await _finishNfcSessionError();
      },
      failure: (error) async {
        // すでにパスワード照合失敗のエラー情報が格納されているのでエラー情報はstateに格納しない
        // パスワード照合に失敗しているのでエラー終了
        await _finishNfcSessionError();
      },
    );
  }

  // ここからAndroidの処理を記載

  // F_JLA_01 NFCリーダーモード設定
  // マイナンバーカード読み取り開始
  Future<bool> setEnableNfcCardReader() async {
    // NFCリーダーモード設定の呼び出し判定のリセット
    state = state.copyWith(isSdkReading: false);
    // 残試行回数のリセット
    state = state.copyWith(retryCounter: null);

    final result =
        await jpkiSdkRepository.setEnableNfcCardReader(SdkParameter.timeout);

    await result.when(
      success: (isSdkReading) async {
        state = state.copyWith(isSdkReading: true);
        // NFCリーダーモード設定の呼び出し成功後にパスワード照合を行う
        await _verifySignatureCertPin();
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
      },
    );
    return state.isSdkReading;
  }

  // F_JLA_02 NFC標準モード設定
  // マイナンバーカード読み取り終了
  Future<void> setDisableNfcCardReader() async {
    Log.d('F_JLA_02 NFC標準モード設定呼び出し');
    // repository呼び出し
    final result = await jpkiSdkRepository.setDisableNfcCardReader();

    result.when(
      success: (_) {
        state = state.copyWith(isSdkReading: true);
      },
      failure: (error) {
        state = state.copyWith(isSdkReading: false);
      },
    );
  }

  // F_JLA_03 残試行回数取得
  Future<void> getAttemptRemain() async {
    // repository呼び出し
    final result = await jpkiSdkRepository.getAttemptRemain(SdkParameter.type);

    await result.when(
      success: (retryCounter) async {
        state = state.copyWith(retryCounter: retryCounter);
        // パスワード照合に失敗しているのでNFCリーダモード終了
        await setDisableNfcCardReader();
      },
      failure: (error) async {
        ////残試行回数取得に失敗した場合はNFCリーダモード終了
        await setDisableNfcCardReader();
      },
    );
  }

  // F_JLA_07 署名用電子証明書AP認証取得
  Future<void> _verifySignatureCertPin() async {
    // repository呼び出し
    final result = await jpkiSdkRepository.verifySignatureCertPin();

    await result.when(
      success: (_) async {
        //署名用電子証明書AP認証取得に成功した場合、電子証明書取得をする
        await _getSignatureCert();
        // Log.d('署名用電子証明書AP認証取得に成功した場合、電子証明書取得をする');
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        //署名用電子証明書AP認証取得に失敗した場合は残試行回数を取得
        await getAttemptRemain();
      },
    );
  }

  // F_JLA_01 署名用電子証明書取得
  Future<void> _getSignatureCert() async {
    final result = await jpkiSdkRepository.getSignatureCert();

    // repository呼び出し
    await result.when(
      success: (_) async {
        //署名用電子証明書取得が成功した場合、電子署名を作成する
        // Log.d('署名用電子証明書取得が成功した場合、電子署名を作成する');
        await _makeSignatureCertDigitalSignature();
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        //署名用電子証明書取得が失敗した場合はNFCリーダモード終了
        // Log.d('署名用電子証明書取得が失敗した場合はNFCリーダモード終了');
        await setDisableNfcCardReader();
      },
    );
  }

  // F_JLA_02 署名用電子署名作成
  Future<void> _makeSignatureCertDigitalSignature() async {
    // repository呼び出し
    Log.d('makeSignatureCertDigitalSignature呼び出し');
    final result = await jpkiSdkRepository.makeSignatureCertDigitalSignature();

    await result.when(
      success: (_) async {
        //電子署名作成が成功した場合、NFCリーダモード終了
        await setDisableNfcCardReader();
        // Log.d('電子署名作成が成功した場合、NFCリーダモード終了');
      },
      failure: (error) async {
        state = state.copyWith(sdkError: error);
        //電子証明書作成が失敗した場合もNFCリーダモード終了
        // Log.d('電子署名作成が失敗した場合もNFCリーダモード終了');
        await setDisableNfcCardReader();
      },
    );
    // Log.d('(どちらにも入らなかった)電子署名作成が成功した場合、NFCリーダモード終了');
    await setDisableNfcCardReader();
  }

  // エラーパターンの変更
  void updateErrorPattern(ErrorPattern errorPattern) {
    state = state.copyWith(
      errorPattern: errorPattern,
    );
  }

  // 完了フラグ（スキャン読み取り〜本人確認APIまで）
  void updateIsComplete(bool isComplete) {
    state = state.copyWith(isComplete: isComplete);
  }

  // iOSスキャン完了フラグの更新
  void updateCardReadingIosCompleted(bool cardReadingCompleted) {
    state = state.copyWith(cardReadingIosCompleted: cardReadingCompleted);
  }

  // Androidスキャン完了フラグの更新
  void updateCardReadingAndroidCompleted(bool cardReadingCompleted) {
    state = state.copyWith(cardReadingAndroidCompleted: cardReadingCompleted);
  }

  // Androidスキャンキャンセルフラグの更新
  void updateIsCanceled(bool cardReadingCanceled) {
    state = state.copyWith(cardReadingAndroidCanceled: cardReadingCanceled);
  }

  // 読み取り完了モーダルが閉じたことを検知する
  void updateIsActive(bool isActive) {
    state = state.copyWith(isActive: isActive);
  }

  // 署名用証明書の氏名(本人確認APIのレスポンス)の格納
  void _updateNameBp(String? nameBp) {
    state = state.copyWith(nameBp: nameBp ?? '');
  }

  // 署名用証明書の住所(本人確認APIのレスポンス)の格納
  void _updateAddressBp(String? addressBp) {
    state = state.copyWith(addressBp: addressBp ?? '');
  }

  // エラー、画面遷移に必要な情報のリセット
  void errorReset() {
    state = state.copyWith(
      errorPattern: null,
      sdkError: null,
      identificationError: null,
    );
  }

  // Android読み取り中モーダルにて、キャンセルされた時のリセット
  void androidCanceledReset() {
    state = state.copyWith(
      isSdkReading: false,
      cardReadingAndroidCanceled: false,
      cardReadingAndroidCompleted: false,
      errorPattern: null,
      sdkError: null,
      identificationError: null,
    );
  }

  // 本人確認APIを実行する
  Future<bool> getJpkiIdentification() async {
    // 本人確認結果を取得
    final result =
        await jpkiIdentificationApiRepository.jpkiIdentificationApiAccess(
      // 一度本人確認をしたreferenceNumber+手続き番号の組み合わせはバックエンド側で
      // open_account_application_jpki_results(本人確認結果保存テーブル)に保存され、同じ番号では打鍵不可のため
      // 手続き者属性は01〜06の間で変更する。（06まで行ったらDBレコード削除）
      // お手続き番号が14桁の時は末尾1桁をトリミングして、13桁に加工する
      referenceNumber: state.referenceNumber.substring(0, 13),
      uuid: state.uuid,
      userTypeNumber: state.userTypeNumber,
    );
    return result.when(
      success: (response) {
        // 署名用証明書の氏名(本人確認APIのレスポンス)の格納
        _updateNameBp(response.nameBp);
        // 署名用証明書の住所(本人確認APIのレスポンス)の格納
        _updateAddressBp(response.addressBp);
        updateIsComplete(true);
        return true;
      },
      failure: (error) {
        state = state.copyWith(identificationError: error);
        updateErrorPattern(
          ErrorInfoJpki.getErrorPattern(
            error.statusCode,
            ErrorInfoJpki.jpkiIdentificationApiErrorPattern,
          ),
        );
        return false;
      },
    );
  }

  /// 本人情報送信処理を実行する
  Future<bool> sendIdentityVerificationInfo({
    required String role,
    required bool isEkyc,
  }) async {
    final res =
        await identityVerificationRepository.sendIdentityVerificationInfo(
      IdentityVerificationInfo(
        role: role,
        dataSource: isEkyc ? 'eKYC' : 'JPKI',
        referenceNumber: state.referenceNumber,
      ),
    );
    return res.when(
      success: (_) {
        return true;
      },
      failure: (error) {
        state = state.copyWith(sendIdentityInfoError: error);
        return false;
      },
    );
  }

  void clearSendIdentityInfoError() {
    state = state.copyWith(sendIdentityInfoError: null);
  }
}
