import 'package:dtp_app/datas/jaibreak_detection_manager.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'jailbreak_detection_screen_notifier.freezed.dart';

enum JailbreakType {
  // なし
  none,
  // root化
  root,
  // jailbreak
  jailbreak,
}

@freezed
class JailbreakDetectionScreenState with _$JailbreakDetectionScreenState {
  const factory JailbreakDetectionScreenState({
    JailbreakType? jailbreakType,
  }) = _JailbreakDetectionScreenState;
}

class JailbreakDetectionScreenNotifier
    extends StateNotifier<JailbreakDetectionScreenState> {
  JailbreakDetectionScreenNotifier({
    required this.jailbreakDetectionManager,
  }) : super(const JailbreakDetectionScreenState());

  final JailbreakDetectionManager jailbreakDetectionManager;

  Future<void> initialize() async {
    final jailbreakType = await jailbreakDetectionManager.initPlatformState();
    state = state.copyWith(
      jailbreakType: jailbreakType,
    );
  }
}

final jailbreakDetectionScreenProvider = StateNotifierProvider.autoDispose<
    JailbreakDetectionScreenNotifier, JailbreakDetectionScreenState>(
  (ref) => JailbreakDetectionScreenNotifier(
    jailbreakDetectionManager: ref.watch(jailbreakDetectionManagerProvider),
  ),
);
