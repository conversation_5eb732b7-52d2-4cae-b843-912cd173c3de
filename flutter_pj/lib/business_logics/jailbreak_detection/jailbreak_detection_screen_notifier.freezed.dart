// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jailbreak_detection_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$JailbreakDetectionScreenState {
  JailbreakType? get jailbreakType => throw _privateConstructorUsedError;

  /// Create a copy of JailbreakDetectionScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JailbreakDetectionScreenStateCopyWith<JailbreakDetectionScreenState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JailbreakDetectionScreenStateCopyWith<$Res> {
  factory $JailbreakDetectionScreenStateCopyWith(
          JailbreakDetectionScreenState value,
          $Res Function(JailbreakDetectionScreenState) then) =
      _$JailbreakDetectionScreenStateCopyWithImpl<$Res,
          JailbreakDetectionScreenState>;
  @useResult
  $Res call({JailbreakType? jailbreakType});
}

/// @nodoc
class _$JailbreakDetectionScreenStateCopyWithImpl<$Res,
        $Val extends JailbreakDetectionScreenState>
    implements $JailbreakDetectionScreenStateCopyWith<$Res> {
  _$JailbreakDetectionScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JailbreakDetectionScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jailbreakType = freezed,
  }) {
    return _then(_value.copyWith(
      jailbreakType: freezed == jailbreakType
          ? _value.jailbreakType
          : jailbreakType // ignore: cast_nullable_to_non_nullable
              as JailbreakType?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JailbreakDetectionScreenStateImplCopyWith<$Res>
    implements $JailbreakDetectionScreenStateCopyWith<$Res> {
  factory _$$JailbreakDetectionScreenStateImplCopyWith(
          _$JailbreakDetectionScreenStateImpl value,
          $Res Function(_$JailbreakDetectionScreenStateImpl) then) =
      __$$JailbreakDetectionScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({JailbreakType? jailbreakType});
}

/// @nodoc
class __$$JailbreakDetectionScreenStateImplCopyWithImpl<$Res>
    extends _$JailbreakDetectionScreenStateCopyWithImpl<$Res,
        _$JailbreakDetectionScreenStateImpl>
    implements _$$JailbreakDetectionScreenStateImplCopyWith<$Res> {
  __$$JailbreakDetectionScreenStateImplCopyWithImpl(
      _$JailbreakDetectionScreenStateImpl _value,
      $Res Function(_$JailbreakDetectionScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of JailbreakDetectionScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jailbreakType = freezed,
  }) {
    return _then(_$JailbreakDetectionScreenStateImpl(
      jailbreakType: freezed == jailbreakType
          ? _value.jailbreakType
          : jailbreakType // ignore: cast_nullable_to_non_nullable
              as JailbreakType?,
    ));
  }
}

/// @nodoc

class _$JailbreakDetectionScreenStateImpl
    implements _JailbreakDetectionScreenState {
  const _$JailbreakDetectionScreenStateImpl({this.jailbreakType});

  @override
  final JailbreakType? jailbreakType;

  @override
  String toString() {
    return 'JailbreakDetectionScreenState(jailbreakType: $jailbreakType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JailbreakDetectionScreenStateImpl &&
            (identical(other.jailbreakType, jailbreakType) ||
                other.jailbreakType == jailbreakType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, jailbreakType);

  /// Create a copy of JailbreakDetectionScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JailbreakDetectionScreenStateImplCopyWith<
          _$JailbreakDetectionScreenStateImpl>
      get copyWith => __$$JailbreakDetectionScreenStateImplCopyWithImpl<
          _$JailbreakDetectionScreenStateImpl>(this, _$identity);
}

abstract class _JailbreakDetectionScreenState
    implements JailbreakDetectionScreenState {
  const factory _JailbreakDetectionScreenState(
          {final JailbreakType? jailbreakType}) =
      _$JailbreakDetectionScreenStateImpl;

  @override
  JailbreakType? get jailbreakType;

  /// Create a copy of JailbreakDetectionScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JailbreakDetectionScreenStateImplCopyWith<
          _$JailbreakDetectionScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
