// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'login_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$LoginScreenState {
  /// 次に表示すべき画面
  LoginNextScreens get loginPhases => throw _privateConstructorUsedError;

  /// DBに保存する同意済みの利用規定
  List<String> get agreeTerms => throw _privateConstructorUsedError;

  /// DBに保存しない同意済みの利用規定
  List<String> get unsavedAgreeTerms => throw _privateConstructorUsedError;

  /// 認可画面生成のための情報
  String? get fromEbizParam => throw _privateConstructorUsedError;

  /// 共通エラー処理完了時のフラグ
  bool get commonErrorLogoutCompleted => throw _privateConstructorUsedError;

  /// ViewのPW表示管理フラグ
  bool get isPasswordVisible => throw _privateConstructorUsedError;

  /// vdId/dtpIds識別フラグ(画面表示に使用)
  bool get isDtpLogin => throw _privateConstructorUsedError;

  /// PW変更完了トースト表示用フラグ
  bool get passwordChangedSuccessfully => throw _privateConstructorUsedError;

  /// 生体認証利用判断フラグ
  bool get isUsingBiometrics => throw _privateConstructorUsedError;

  /// 初回ログインフラグ
  bool get isFirstTimeLogin => throw _privateConstructorUsedError;

  /// 生体認証利用可否フラグ
  bool get isBiometricsAvailable => throw _privateConstructorUsedError;

  /// ローディングフラグ
  bool get isLoading => throw _privateConstructorUsedError; // 認可拒否履歴フラグ
  bool get isRefusalFlag => throw _privateConstructorUsedError;

  /// 端末に保存されているPWフラグ
  SavedPasswordStatus get savedPwStatus => throw _privateConstructorUsedError;

  /// VD宛のAPI取得時のレスポンスを格納
  VdidLoginResponse? get vdidLoginResponse =>
      throw _privateConstructorUsedError;

  /// Web21権限現情報
  AuthorizationStatus? get authorizationStatus =>
      throw _privateConstructorUsedError;

  /// otp要認証時のhighRiskUserID
  String? get highRiskUserId => throw _privateConstructorUsedError;

  /// PW変更時の新しいPW
  String? get newPassword => throw _privateConstructorUsedError;

  /// お知らせ情報
  String? get announcementInfo => throw _privateConstructorUsedError;
  ValidationError get loginValidationError =>
      throw _privateConstructorUsedError;

  ///login時のバリデーション結果
  ValidationError get changedPasswordValidationError =>
      throw _privateConstructorUsedError;

  /// エラー
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LoginScreenStateCopyWith<LoginScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LoginScreenStateCopyWith<$Res> {
  factory $LoginScreenStateCopyWith(
          LoginScreenState value, $Res Function(LoginScreenState) then) =
      _$LoginScreenStateCopyWithImpl<$Res, LoginScreenState>;
  @useResult
  $Res call(
      {LoginNextScreens loginPhases,
      List<String> agreeTerms,
      List<String> unsavedAgreeTerms,
      String? fromEbizParam,
      bool commonErrorLogoutCompleted,
      bool isPasswordVisible,
      bool isDtpLogin,
      bool passwordChangedSuccessfully,
      bool isUsingBiometrics,
      bool isFirstTimeLogin,
      bool isBiometricsAvailable,
      bool isLoading,
      bool isRefusalFlag,
      SavedPasswordStatus savedPwStatus,
      VdidLoginResponse? vdidLoginResponse,
      AuthorizationStatus? authorizationStatus,
      String? highRiskUserId,
      String? newPassword,
      String? announcementInfo,
      ValidationError loginValidationError,
      ValidationError changedPasswordValidationError,
      AppError? error});

  $VdidLoginResponseCopyWith<$Res>? get vdidLoginResponse;
  $AuthorizationStatusCopyWith<$Res>? get authorizationStatus;
  $ValidationErrorCopyWith<$Res> get loginValidationError;
  $ValidationErrorCopyWith<$Res> get changedPasswordValidationError;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$LoginScreenStateCopyWithImpl<$Res, $Val extends LoginScreenState>
    implements $LoginScreenStateCopyWith<$Res> {
  _$LoginScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginPhases = null,
    Object? agreeTerms = null,
    Object? unsavedAgreeTerms = null,
    Object? fromEbizParam = freezed,
    Object? commonErrorLogoutCompleted = null,
    Object? isPasswordVisible = null,
    Object? isDtpLogin = null,
    Object? passwordChangedSuccessfully = null,
    Object? isUsingBiometrics = null,
    Object? isFirstTimeLogin = null,
    Object? isBiometricsAvailable = null,
    Object? isLoading = null,
    Object? isRefusalFlag = null,
    Object? savedPwStatus = null,
    Object? vdidLoginResponse = freezed,
    Object? authorizationStatus = freezed,
    Object? highRiskUserId = freezed,
    Object? newPassword = freezed,
    Object? announcementInfo = freezed,
    Object? loginValidationError = null,
    Object? changedPasswordValidationError = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      loginPhases: null == loginPhases
          ? _value.loginPhases
          : loginPhases // ignore: cast_nullable_to_non_nullable
              as LoginNextScreens,
      agreeTerms: null == agreeTerms
          ? _value.agreeTerms
          : agreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
      unsavedAgreeTerms: null == unsavedAgreeTerms
          ? _value.unsavedAgreeTerms
          : unsavedAgreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fromEbizParam: freezed == fromEbizParam
          ? _value.fromEbizParam
          : fromEbizParam // ignore: cast_nullable_to_non_nullable
              as String?,
      commonErrorLogoutCompleted: null == commonErrorLogoutCompleted
          ? _value.commonErrorLogoutCompleted
          : commonErrorLogoutCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordVisible: null == isPasswordVisible
          ? _value.isPasswordVisible
          : isPasswordVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      isDtpLogin: null == isDtpLogin
          ? _value.isDtpLogin
          : isDtpLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      passwordChangedSuccessfully: null == passwordChangedSuccessfully
          ? _value.passwordChangedSuccessfully
          : passwordChangedSuccessfully // ignore: cast_nullable_to_non_nullable
              as bool,
      isUsingBiometrics: null == isUsingBiometrics
          ? _value.isUsingBiometrics
          : isUsingBiometrics // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstTimeLogin: null == isFirstTimeLogin
          ? _value.isFirstTimeLogin
          : isFirstTimeLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isBiometricsAvailable: null == isBiometricsAvailable
          ? _value.isBiometricsAvailable
          : isBiometricsAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefusalFlag: null == isRefusalFlag
          ? _value.isRefusalFlag
          : isRefusalFlag // ignore: cast_nullable_to_non_nullable
              as bool,
      savedPwStatus: null == savedPwStatus
          ? _value.savedPwStatus
          : savedPwStatus // ignore: cast_nullable_to_non_nullable
              as SavedPasswordStatus,
      vdidLoginResponse: freezed == vdidLoginResponse
          ? _value.vdidLoginResponse
          : vdidLoginResponse // ignore: cast_nullable_to_non_nullable
              as VdidLoginResponse?,
      authorizationStatus: freezed == authorizationStatus
          ? _value.authorizationStatus
          : authorizationStatus // ignore: cast_nullable_to_non_nullable
              as AuthorizationStatus?,
      highRiskUserId: freezed == highRiskUserId
          ? _value.highRiskUserId
          : highRiskUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      newPassword: freezed == newPassword
          ? _value.newPassword
          : newPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      announcementInfo: freezed == announcementInfo
          ? _value.announcementInfo
          : announcementInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      loginValidationError: null == loginValidationError
          ? _value.loginValidationError
          : loginValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      changedPasswordValidationError: null == changedPasswordValidationError
          ? _value.changedPasswordValidationError
          : changedPasswordValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VdidLoginResponseCopyWith<$Res>? get vdidLoginResponse {
    if (_value.vdidLoginResponse == null) {
      return null;
    }

    return $VdidLoginResponseCopyWith<$Res>(_value.vdidLoginResponse!, (value) {
      return _then(_value.copyWith(vdidLoginResponse: value) as $Val);
    });
  }

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AuthorizationStatusCopyWith<$Res>? get authorizationStatus {
    if (_value.authorizationStatus == null) {
      return null;
    }

    return $AuthorizationStatusCopyWith<$Res>(_value.authorizationStatus!,
        (value) {
      return _then(_value.copyWith(authorizationStatus: value) as $Val);
    });
  }

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get loginValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.loginValidationError, (value) {
      return _then(_value.copyWith(loginValidationError: value) as $Val);
    });
  }

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ValidationErrorCopyWith<$Res> get changedPasswordValidationError {
    return $ValidationErrorCopyWith<$Res>(_value.changedPasswordValidationError,
        (value) {
      return _then(
          _value.copyWith(changedPasswordValidationError: value) as $Val);
    });
  }

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LoginScreenStateImplCopyWith<$Res>
    implements $LoginScreenStateCopyWith<$Res> {
  factory _$$LoginScreenStateImplCopyWith(_$LoginScreenStateImpl value,
          $Res Function(_$LoginScreenStateImpl) then) =
      __$$LoginScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {LoginNextScreens loginPhases,
      List<String> agreeTerms,
      List<String> unsavedAgreeTerms,
      String? fromEbizParam,
      bool commonErrorLogoutCompleted,
      bool isPasswordVisible,
      bool isDtpLogin,
      bool passwordChangedSuccessfully,
      bool isUsingBiometrics,
      bool isFirstTimeLogin,
      bool isBiometricsAvailable,
      bool isLoading,
      bool isRefusalFlag,
      SavedPasswordStatus savedPwStatus,
      VdidLoginResponse? vdidLoginResponse,
      AuthorizationStatus? authorizationStatus,
      String? highRiskUserId,
      String? newPassword,
      String? announcementInfo,
      ValidationError loginValidationError,
      ValidationError changedPasswordValidationError,
      AppError? error});

  @override
  $VdidLoginResponseCopyWith<$Res>? get vdidLoginResponse;
  @override
  $AuthorizationStatusCopyWith<$Res>? get authorizationStatus;
  @override
  $ValidationErrorCopyWith<$Res> get loginValidationError;
  @override
  $ValidationErrorCopyWith<$Res> get changedPasswordValidationError;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$LoginScreenStateImplCopyWithImpl<$Res>
    extends _$LoginScreenStateCopyWithImpl<$Res, _$LoginScreenStateImpl>
    implements _$$LoginScreenStateImplCopyWith<$Res> {
  __$$LoginScreenStateImplCopyWithImpl(_$LoginScreenStateImpl _value,
      $Res Function(_$LoginScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginPhases = null,
    Object? agreeTerms = null,
    Object? unsavedAgreeTerms = null,
    Object? fromEbizParam = freezed,
    Object? commonErrorLogoutCompleted = null,
    Object? isPasswordVisible = null,
    Object? isDtpLogin = null,
    Object? passwordChangedSuccessfully = null,
    Object? isUsingBiometrics = null,
    Object? isFirstTimeLogin = null,
    Object? isBiometricsAvailable = null,
    Object? isLoading = null,
    Object? isRefusalFlag = null,
    Object? savedPwStatus = null,
    Object? vdidLoginResponse = freezed,
    Object? authorizationStatus = freezed,
    Object? highRiskUserId = freezed,
    Object? newPassword = freezed,
    Object? announcementInfo = freezed,
    Object? loginValidationError = null,
    Object? changedPasswordValidationError = null,
    Object? error = freezed,
  }) {
    return _then(_$LoginScreenStateImpl(
      loginPhases: null == loginPhases
          ? _value.loginPhases
          : loginPhases // ignore: cast_nullable_to_non_nullable
              as LoginNextScreens,
      agreeTerms: null == agreeTerms
          ? _value._agreeTerms
          : agreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
      unsavedAgreeTerms: null == unsavedAgreeTerms
          ? _value._unsavedAgreeTerms
          : unsavedAgreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fromEbizParam: freezed == fromEbizParam
          ? _value.fromEbizParam
          : fromEbizParam // ignore: cast_nullable_to_non_nullable
              as String?,
      commonErrorLogoutCompleted: null == commonErrorLogoutCompleted
          ? _value.commonErrorLogoutCompleted
          : commonErrorLogoutCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isPasswordVisible: null == isPasswordVisible
          ? _value.isPasswordVisible
          : isPasswordVisible // ignore: cast_nullable_to_non_nullable
              as bool,
      isDtpLogin: null == isDtpLogin
          ? _value.isDtpLogin
          : isDtpLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      passwordChangedSuccessfully: null == passwordChangedSuccessfully
          ? _value.passwordChangedSuccessfully
          : passwordChangedSuccessfully // ignore: cast_nullable_to_non_nullable
              as bool,
      isUsingBiometrics: null == isUsingBiometrics
          ? _value.isUsingBiometrics
          : isUsingBiometrics // ignore: cast_nullable_to_non_nullable
              as bool,
      isFirstTimeLogin: null == isFirstTimeLogin
          ? _value.isFirstTimeLogin
          : isFirstTimeLogin // ignore: cast_nullable_to_non_nullable
              as bool,
      isBiometricsAvailable: null == isBiometricsAvailable
          ? _value.isBiometricsAvailable
          : isBiometricsAvailable // ignore: cast_nullable_to_non_nullable
              as bool,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefusalFlag: null == isRefusalFlag
          ? _value.isRefusalFlag
          : isRefusalFlag // ignore: cast_nullable_to_non_nullable
              as bool,
      savedPwStatus: null == savedPwStatus
          ? _value.savedPwStatus
          : savedPwStatus // ignore: cast_nullable_to_non_nullable
              as SavedPasswordStatus,
      vdidLoginResponse: freezed == vdidLoginResponse
          ? _value.vdidLoginResponse
          : vdidLoginResponse // ignore: cast_nullable_to_non_nullable
              as VdidLoginResponse?,
      authorizationStatus: freezed == authorizationStatus
          ? _value.authorizationStatus
          : authorizationStatus // ignore: cast_nullable_to_non_nullable
              as AuthorizationStatus?,
      highRiskUserId: freezed == highRiskUserId
          ? _value.highRiskUserId
          : highRiskUserId // ignore: cast_nullable_to_non_nullable
              as String?,
      newPassword: freezed == newPassword
          ? _value.newPassword
          : newPassword // ignore: cast_nullable_to_non_nullable
              as String?,
      announcementInfo: freezed == announcementInfo
          ? _value.announcementInfo
          : announcementInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      loginValidationError: null == loginValidationError
          ? _value.loginValidationError
          : loginValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      changedPasswordValidationError: null == changedPasswordValidationError
          ? _value.changedPasswordValidationError
          : changedPasswordValidationError // ignore: cast_nullable_to_non_nullable
              as ValidationError,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$LoginScreenStateImpl implements _LoginScreenState {
  const _$LoginScreenStateImpl(
      {this.loginPhases = LoginNextScreens.initial,
      final List<String> agreeTerms = const <String>[],
      final List<String> unsavedAgreeTerms = const <String>[],
      this.fromEbizParam = null,
      this.commonErrorLogoutCompleted = false,
      this.isPasswordVisible = false,
      this.isDtpLogin = true,
      this.passwordChangedSuccessfully = false,
      this.isUsingBiometrics = false,
      this.isFirstTimeLogin = false,
      this.isBiometricsAvailable = false,
      this.isLoading = false,
      this.isRefusalFlag = false,
      this.savedPwStatus = SavedPasswordStatus.none,
      this.vdidLoginResponse,
      this.authorizationStatus,
      this.highRiskUserId,
      this.newPassword,
      this.announcementInfo,
      this.loginValidationError = const ValidationError.none(),
      this.changedPasswordValidationError = const ValidationError.none(),
      this.error})
      : _agreeTerms = agreeTerms,
        _unsavedAgreeTerms = unsavedAgreeTerms;

  /// 次に表示すべき画面
  @override
  @JsonKey()
  final LoginNextScreens loginPhases;

  /// DBに保存する同意済みの利用規定
  final List<String> _agreeTerms;

  /// DBに保存する同意済みの利用規定
  @override
  @JsonKey()
  List<String> get agreeTerms {
    if (_agreeTerms is EqualUnmodifiableListView) return _agreeTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_agreeTerms);
  }

  /// DBに保存しない同意済みの利用規定
  final List<String> _unsavedAgreeTerms;

  /// DBに保存しない同意済みの利用規定
  @override
  @JsonKey()
  List<String> get unsavedAgreeTerms {
    if (_unsavedAgreeTerms is EqualUnmodifiableListView)
      return _unsavedAgreeTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_unsavedAgreeTerms);
  }

  /// 認可画面生成のための情報
  @override
  @JsonKey()
  final String? fromEbizParam;

  /// 共通エラー処理完了時のフラグ
  @override
  @JsonKey()
  final bool commonErrorLogoutCompleted;

  /// ViewのPW表示管理フラグ
  @override
  @JsonKey()
  final bool isPasswordVisible;

  /// vdId/dtpIds識別フラグ(画面表示に使用)
  @override
  @JsonKey()
  final bool isDtpLogin;

  /// PW変更完了トースト表示用フラグ
  @override
  @JsonKey()
  final bool passwordChangedSuccessfully;

  /// 生体認証利用判断フラグ
  @override
  @JsonKey()
  final bool isUsingBiometrics;

  /// 初回ログインフラグ
  @override
  @JsonKey()
  final bool isFirstTimeLogin;

  /// 生体認証利用可否フラグ
  @override
  @JsonKey()
  final bool isBiometricsAvailable;

  /// ローディングフラグ
  @override
  @JsonKey()
  final bool isLoading;
// 認可拒否履歴フラグ
  @override
  @JsonKey()
  final bool isRefusalFlag;

  /// 端末に保存されているPWフラグ
  @override
  @JsonKey()
  final SavedPasswordStatus savedPwStatus;

  /// VD宛のAPI取得時のレスポンスを格納
  @override
  final VdidLoginResponse? vdidLoginResponse;

  /// Web21権限現情報
  @override
  final AuthorizationStatus? authorizationStatus;

  /// otp要認証時のhighRiskUserID
  @override
  final String? highRiskUserId;

  /// PW変更時の新しいPW
  @override
  final String? newPassword;

  /// お知らせ情報
  @override
  final String? announcementInfo;
  @override
  @JsonKey()
  final ValidationError loginValidationError;

  ///login時のバリデーション結果
  @override
  @JsonKey()
  final ValidationError changedPasswordValidationError;

  /// エラー
  @override
  final AppError? error;

  @override
  String toString() {
    return 'LoginScreenState(loginPhases: $loginPhases, agreeTerms: $agreeTerms, unsavedAgreeTerms: $unsavedAgreeTerms, fromEbizParam: $fromEbizParam, commonErrorLogoutCompleted: $commonErrorLogoutCompleted, isPasswordVisible: $isPasswordVisible, isDtpLogin: $isDtpLogin, passwordChangedSuccessfully: $passwordChangedSuccessfully, isUsingBiometrics: $isUsingBiometrics, isFirstTimeLogin: $isFirstTimeLogin, isBiometricsAvailable: $isBiometricsAvailable, isLoading: $isLoading, isRefusalFlag: $isRefusalFlag, savedPwStatus: $savedPwStatus, vdidLoginResponse: $vdidLoginResponse, authorizationStatus: $authorizationStatus, highRiskUserId: $highRiskUserId, newPassword: $newPassword, announcementInfo: $announcementInfo, loginValidationError: $loginValidationError, changedPasswordValidationError: $changedPasswordValidationError, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoginScreenStateImpl &&
            (identical(other.loginPhases, loginPhases) ||
                other.loginPhases == loginPhases) &&
            const DeepCollectionEquality()
                .equals(other._agreeTerms, _agreeTerms) &&
            const DeepCollectionEquality()
                .equals(other._unsavedAgreeTerms, _unsavedAgreeTerms) &&
            (identical(other.fromEbizParam, fromEbizParam) ||
                other.fromEbizParam == fromEbizParam) &&
            (identical(other.commonErrorLogoutCompleted, commonErrorLogoutCompleted) ||
                other.commonErrorLogoutCompleted ==
                    commonErrorLogoutCompleted) &&
            (identical(other.isPasswordVisible, isPasswordVisible) ||
                other.isPasswordVisible == isPasswordVisible) &&
            (identical(other.isDtpLogin, isDtpLogin) ||
                other.isDtpLogin == isDtpLogin) &&
            (identical(other.passwordChangedSuccessfully,
                    passwordChangedSuccessfully) ||
                other.passwordChangedSuccessfully ==
                    passwordChangedSuccessfully) &&
            (identical(other.isUsingBiometrics, isUsingBiometrics) ||
                other.isUsingBiometrics == isUsingBiometrics) &&
            (identical(other.isFirstTimeLogin, isFirstTimeLogin) ||
                other.isFirstTimeLogin == isFirstTimeLogin) &&
            (identical(other.isBiometricsAvailable, isBiometricsAvailable) ||
                other.isBiometricsAvailable == isBiometricsAvailable) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRefusalFlag, isRefusalFlag) ||
                other.isRefusalFlag == isRefusalFlag) &&
            (identical(other.savedPwStatus, savedPwStatus) ||
                other.savedPwStatus == savedPwStatus) &&
            (identical(other.vdidLoginResponse, vdidLoginResponse) ||
                other.vdidLoginResponse == vdidLoginResponse) &&
            (identical(other.authorizationStatus, authorizationStatus) ||
                other.authorizationStatus == authorizationStatus) &&
            (identical(other.highRiskUserId, highRiskUserId) ||
                other.highRiskUserId == highRiskUserId) &&
            (identical(other.newPassword, newPassword) ||
                other.newPassword == newPassword) &&
            (identical(other.announcementInfo, announcementInfo) ||
                other.announcementInfo == announcementInfo) &&
            (identical(other.loginValidationError, loginValidationError) ||
                other.loginValidationError == loginValidationError) &&
            (identical(other.changedPasswordValidationError,
                    changedPasswordValidationError) ||
                other.changedPasswordValidationError ==
                    changedPasswordValidationError) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        loginPhases,
        const DeepCollectionEquality().hash(_agreeTerms),
        const DeepCollectionEquality().hash(_unsavedAgreeTerms),
        fromEbizParam,
        commonErrorLogoutCompleted,
        isPasswordVisible,
        isDtpLogin,
        passwordChangedSuccessfully,
        isUsingBiometrics,
        isFirstTimeLogin,
        isBiometricsAvailable,
        isLoading,
        isRefusalFlag,
        savedPwStatus,
        vdidLoginResponse,
        authorizationStatus,
        highRiskUserId,
        newPassword,
        announcementInfo,
        loginValidationError,
        changedPasswordValidationError,
        error
      ]);

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoginScreenStateImplCopyWith<_$LoginScreenStateImpl> get copyWith =>
      __$$LoginScreenStateImplCopyWithImpl<_$LoginScreenStateImpl>(
          this, _$identity);
}

abstract class _LoginScreenState implements LoginScreenState {
  const factory _LoginScreenState(
      {final LoginNextScreens loginPhases,
      final List<String> agreeTerms,
      final List<String> unsavedAgreeTerms,
      final String? fromEbizParam,
      final bool commonErrorLogoutCompleted,
      final bool isPasswordVisible,
      final bool isDtpLogin,
      final bool passwordChangedSuccessfully,
      final bool isUsingBiometrics,
      final bool isFirstTimeLogin,
      final bool isBiometricsAvailable,
      final bool isLoading,
      final bool isRefusalFlag,
      final SavedPasswordStatus savedPwStatus,
      final VdidLoginResponse? vdidLoginResponse,
      final AuthorizationStatus? authorizationStatus,
      final String? highRiskUserId,
      final String? newPassword,
      final String? announcementInfo,
      final ValidationError loginValidationError,
      final ValidationError changedPasswordValidationError,
      final AppError? error}) = _$LoginScreenStateImpl;

  /// 次に表示すべき画面
  @override
  LoginNextScreens get loginPhases;

  /// DBに保存する同意済みの利用規定
  @override
  List<String> get agreeTerms;

  /// DBに保存しない同意済みの利用規定
  @override
  List<String> get unsavedAgreeTerms;

  /// 認可画面生成のための情報
  @override
  String? get fromEbizParam;

  /// 共通エラー処理完了時のフラグ
  @override
  bool get commonErrorLogoutCompleted;

  /// ViewのPW表示管理フラグ
  @override
  bool get isPasswordVisible;

  /// vdId/dtpIds識別フラグ(画面表示に使用)
  @override
  bool get isDtpLogin;

  /// PW変更完了トースト表示用フラグ
  @override
  bool get passwordChangedSuccessfully;

  /// 生体認証利用判断フラグ
  @override
  bool get isUsingBiometrics;

  /// 初回ログインフラグ
  @override
  bool get isFirstTimeLogin;

  /// 生体認証利用可否フラグ
  @override
  bool get isBiometricsAvailable;

  /// ローディングフラグ
  @override
  bool get isLoading; // 認可拒否履歴フラグ
  @override
  bool get isRefusalFlag;

  /// 端末に保存されているPWフラグ
  @override
  SavedPasswordStatus get savedPwStatus;

  /// VD宛のAPI取得時のレスポンスを格納
  @override
  VdidLoginResponse? get vdidLoginResponse;

  /// Web21権限現情報
  @override
  AuthorizationStatus? get authorizationStatus;

  /// otp要認証時のhighRiskUserID
  @override
  String? get highRiskUserId;

  /// PW変更時の新しいPW
  @override
  String? get newPassword;

  /// お知らせ情報
  @override
  String? get announcementInfo;
  @override
  ValidationError get loginValidationError;

  ///login時のバリデーション結果
  @override
  ValidationError get changedPasswordValidationError;

  /// エラー
  @override
  AppError? get error;

  /// Create a copy of LoginScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoginScreenStateImplCopyWith<_$LoginScreenStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
