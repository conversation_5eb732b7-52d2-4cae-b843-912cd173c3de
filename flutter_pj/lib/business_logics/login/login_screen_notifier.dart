import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/business_blockage/business_blockage_controller.dart';
import 'package:dtp_app/business_logics/interfaces/announcement_info.dart';
import 'package:dtp_app/business_logics/interfaces/app_launching_repository.dart';
import 'package:dtp_app/business_logics/interfaces/fraud_alert_sdk_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/operation_log_repository.dart';
import 'package:dtp_app/business_logics/mixin/input_validator_mixin.dart';
import 'package:dtp_app/datas/biometric_manager.dart';
import 'package:dtp_app/datas/firebase_manager.dart';
import 'package:dtp_app/datas/permission_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/models/authorization_status/authorization_status.dart';
import 'package:dtp_app/models/consent_status/consent_status.dart';
import 'package:dtp_app/models/session_id_or_high_risk_user_id/session_id_or_high_risk_user_id.dart';
import 'package:dtp_app/models/vdid_login/vdid_login_response.dart';
import 'package:dtp_app/repositories/announcement_info/announcement_info_repository_impl.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/app_launching/app_launching_repository_impl.dart';
import 'package:dtp_app/repositories/fraud_alert_sdk/fraud_alert_sdk_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/operation_log_repository/operation_log_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/views/screens/login/login_next_screens.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'login_screen_notifier.freezed.dart';

@freezed
class LoginScreenState with _$LoginScreenState {
  const factory LoginScreenState({
    /// 次に表示すべき画面
    @Default(LoginNextScreens.initial) LoginNextScreens loginPhases,

    /// DBに保存する同意済みの利用規定
    @Default(<String>[]) List<String> agreeTerms,

    /// DBに保存しない同意済みの利用規定
    @Default(<String>[]) List<String> unsavedAgreeTerms,

    /// 認可画面生成のための情報
    @Default(null) String? fromEbizParam,

    /// 共通エラー処理完了時のフラグ
    @Default(false) bool commonErrorLogoutCompleted,

    /// ViewのPW表示管理フラグ
    @Default(false) bool isPasswordVisible,

    /// vdId/dtpIds識別フラグ(画面表示に使用)
    @Default(true) bool isDtpLogin,

    /// PW変更完了トースト表示用フラグ
    @Default(false) bool passwordChangedSuccessfully,

    /// 生体認証利用判断フラグ
    @Default(false) bool isUsingBiometrics,

    /// 初回ログインフラグ
    @Default(false) bool isFirstTimeLogin,

    /// 生体認証利用可否フラグ
    @Default(false) bool isBiometricsAvailable,

    /// ローディングフラグ
    @Default(false) bool isLoading,

    // 認可拒否履歴フラグ
    @Default(false) bool isRefusalFlag,

    /// 端末に保存されているPWフラグ
    @Default(SavedPasswordStatus.none) SavedPasswordStatus savedPwStatus,

    /// VD宛のAPI取得時のレスポンスを格納
    VdidLoginResponse? vdidLoginResponse,

    /// Web21権限現情報
    AuthorizationStatus? authorizationStatus,

    /// otp要認証時のhighRiskUserID
    String? highRiskUserId,

    /// PW変更時の新しいPW
    String? newPassword,

    /// お知らせ情報
    String? announcementInfo,
    @Default(
      ValidationError.none(),
    )
    ValidationError loginValidationError,

    ///login時のバリデーション結果
    @Default(
      ValidationError.none(),
    )
    ValidationError changedPasswordValidationError,

    /// エラー
    AppError? error,
  }) = _LoginScreenState;
}

enum SavedPasswordStatus {
  /// 保存なし
  none,

  /// vdIdPw保持
  vdId,

  /// dtpIdPw保持
  dtpId,
}

final loginScreenProvider =
    StateNotifierProvider.autoDispose<LoginScreenNotifier, LoginScreenState>(
  (ref) => LoginScreenNotifier(
    firebaseManager: ref.read(firebaseManagerProvider),
    permissionManager: ref.read(permissionManagerProvider),
    loginRepository: ref.watch(loginRepositoryProvider),
    appLaunchingRepository: ref.watch(appLaunchingRepositoryProvider),
    biometricManager: ref.read(biometricProvider),
    operationLogRepository: ref.read(operationLogRepositoryProvider),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
    fraudAlertSDKRepository: ref.read(fraudAlertSDKRepositoryProvider),
    announcementInfoRepository: ref.watch(
      announcementInfoRepositoryProvider,
    ),
    businessBlockageController: ref.watch(
      businessBlockageControllerProvider,
    ),
  ),
);

class LoginScreenNotifier extends StateNotifier<LoginScreenState>
    with InputValidatorMixin {
  LoginScreenNotifier({
    required this.firebaseManager,
    required this.permissionManager,
    required this.loginRepository,
    required this.appLaunchingRepository,
    required this.biometricManager,
    required this.operationLogRepository,
    required this.analyticsLogController,
    required this.fraudAlertSDKRepository,
    required this.announcementInfoRepository,
    required this.businessBlockageController,
  }) : super(const LoginScreenState());

  final FirebaseManager firebaseManager;
  final PermissionManager permissionManager;
  final LoginRepository loginRepository;
  final AppLaunchingRepository appLaunchingRepository;
  final BiometricManager biometricManager;
  final OperationLogRepository operationLogRepository;
  final AnalyticsLogController analyticsLogController;
  final FraudAlertSDKRepository fraudAlertSDKRepository;
  final AnnouncementInfoRepository announcementInfoRepository;
  final BusinessBlockageController businessBlockageController;

  /// 下記変数は処理の都合上StateNotifier内部にミュータブルな変数で定義している。
  /// しかし、こちらの管理方法はバグを生み出すため、
  /// メソッド間でやり取りする情報に関しては可能な限り引数で渡すこと。
  String _password = '';
  String _code = '';
  String _state = '';
  SessionIdOrHighRiskUserId? _sessionIdOrHighRiskUserId;
  String _saml = '';

  /// ログイン時のDTPのバリデーションチェック
  void dtpInputs(
    String? dtpId,
    String? password,
  ) {
    const minLengthId = 1;
    const maxLengthId = 128;
    const minLengthPassword = 4;
    const maxLengthPassword = 8;

    final idError = checkDtpIdFormatAndLength(
      dtpId,
      minLengthId,
      maxLengthId,
    );
    final passwordError = checkDtpPWFormatAndLength(
      password,
      minLengthPassword,
      maxLengthPassword,
    );
    //以下エラーの表示は優先度の高いものから記載している。
    //エラー無し
    if (idError == ValidationError.none() &&
        passwordError == ValidationError.none()) {
      state = state.copyWith(loginValidationError: ValidationError.none());
      return;
    }
    // 型エラー判断
    else if (idError == ValidationError.isDtpTypeError() ||
        passwordError == ValidationError.isDtpTypeError()) {
      state = state.copyWith(
        loginValidationError: ValidationError.isDtpTypeError(),
      );
      return;
    }
    // 両方空欄
    else if (idError == ValidationError.noneDtpIDError() &&
        passwordError == ValidationError.nonePasswordError()) {
      state = state.copyWith(
        loginValidationError: ValidationError.isDtpIdAndPasswordBrank(),
      );
      return;
    }
    // 片方空欄
    else {
      state = state.copyWith(
        loginValidationError:
            idError != ValidationError.none() ? idError : passwordError,
      );
    }
  }

  // ログイン時のVDのバリデーションチェック
  void vdInputs(
    String? vdId,
    String? password,
  ) {
    const maxLengthId = 10;
    const minLengthPassword = 4;
    const maxLengthPassword = 8;

    final passwordError = checkVdPasswordFormatAndLength(
      password,
      minLengthPassword,
      maxLengthPassword,
    );
    final idError = checkVdIdFormatAndLength(
      vdId,
      maxLengthId,
    );
    //以下エラーの表示は優先度の高いものから記載している。
    //エラー無し
    if (idError == ValidationError.none() &&
        passwordError == ValidationError.none()) {
      state = state.copyWith(loginValidationError: ValidationError.none());
      return;
    }
    // 型エラー判断
    else if (idError == ValidationError.isVdTypeError() ||
        passwordError == ValidationError.isVdTypeError()) {
      state =
          state.copyWith(loginValidationError: ValidationError.isVdTypeError());
      return;
    }
    // 両方空欄
    else if (idError == ValidationError.noneVdIdError() &&
        passwordError == ValidationError.nonePasswordError()) {
      state = state.copyWith(
        loginValidationError: ValidationError.isVdIdAndPasswordBrank(),
      );
      return;
    }
    // 片方空欄
    else {
      state = state.copyWith(
        loginValidationError:
            idError != ValidationError.none() ? idError : passwordError,
      );
    }
  }

  // View側にて生体認証利用選択がされたことを通知
  final _setBiometricInfoCompleter = Completer();

  Future<void> initialize(
    bool isIdUnlinked,
    bool logoutCompleted,
  ) async {
    // 非同期でお知らせ情報を取得する
    unawaited(_getAnnouncementInfo());
    await _getIsFirstLoginWithIdPw();
    await _getBiometricsAvailable();
    await _loadUsingBiometrics();
    await _loadDtpIdFlag();
    await _loadLoginPasswordType();

    /// ID紐付け解除後には生体認証は実施せず
    /// VDログイン画面を表示
    if (isIdUnlinked) {
      /// DTPIDの認証情報が保存されている際には情報を削除
      if (state.savedPwStatus == SavedPasswordStatus.dtpId &&
          state.isUsingBiometrics) {
        await cancelUsingBiometrics();
      }
      state = state.copyWith(isDtpLogin: false);
      return;
    }
    // 生体認証を利用する、かつログアウト完了フラグがfalseの場合に生体認証を実施する
    if (state.isUsingBiometrics &&
        state.isBiometricsAvailable &&
        !logoutCompleted) {
      await getBusinessBlockageStatus();
      if (state.loginPhases != LoginNextScreens.workBlockage) {
        await biometricsLogin();
      }
    }
  }

  /// ログインエラーが発生した場合の共通処理
  void notifyLoginError(AppError error) {
    state = state.copyWith(
      loginPhases: LoginNextScreens.initial,
      error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
    );
  }

  /// 業務閉塞か業務閉塞エラーが発生した場合の処理
  void _notifyBusinessBlockage() {
    state = state.copyWith(loginPhases: LoginNextScreens.workBlockage);
  }

  /// 初回ログインフラグの取得
  Future<void> _getIsFirstLoginWithIdPw() async {
    final isFirstTimeLoginWithIdPw =
        await loginRepository.getFirstLoginWithIDPW();
    state = state.copyWith(isFirstTimeLogin: isFirstTimeLoginWithIdPw);
  }

  /// 生体認証利用可否フラグの取得
  Future<void> _getBiometricsAvailable() async {
    final result = await biometricManager.canCheckBiometrics;
    state = state.copyWith(isBiometricsAvailable: result);
    if (!result) {
      // 生体認証利用なしの場合、保持していたフラグやID/PWを削除
      await cancelUsingBiometrics();
    }
  }

  /// 次回以降の生体認証利用フラグ読み込み
  Future<void> _loadUsingBiometrics() async {
    final isUsingBiometrics = await loginRepository.getUsingBiometrics();
    state = state.copyWith(isUsingBiometrics: isUsingBiometrics);
  }

  /// 保存されているID/PWの種類（VD,DTPID）
  Future<void> _loadDtpIdFlag() async {
    final isUsingDtpId = await loginRepository.getDtpLoginFlag();
    state = state.copyWith(isDtpLogin: isUsingDtpId);
  }

  /// 保存されているPW種別の取得
  Future<void> _loadLoginPasswordType() async {
    // 生体認証を使用していない場合、PW種別の取得は不要
    if (!state.isUsingBiometrics) {
      state = state.copyWith(savedPwStatus: SavedPasswordStatus.none);
      return;
    }
    final loginPassword = await loginRepository.getPassword();
    if (loginPassword.isEmpty) {
      state = state.copyWith(savedPwStatus: SavedPasswordStatus.none);
      return;
    }
    state = state.copyWith(
      savedPwStatus: state.isDtpLogin
          ? SavedPasswordStatus.dtpId
          : SavedPasswordStatus.vdId,
    );
  }

  /// お知らせ情報を取得する
  Future<void> _getAnnouncementInfo() async {
    final res = await announcementInfoRepository.getAnnouncementInfo();

    res.when(
      success: (content) {
        state = state.copyWith(announcementInfo: content);
      },
      failure: (_) {
        // エラー時はお知らせ情報にnullをセットする
        state = state.copyWith(announcementInfo: null);
      },
    );
  }

  /// 生体認証ログイン
  Future<void> biometricsLogin() async {
    _isLoadingStarted();
    final result = await biometricManager.didAuthenticate();
    await result.when(
      success: (isCompleted) async {
        if (!isCompleted) {
          // 生体認証ログインに失敗した場合は早期リターン。Viewにて失敗ダイアログ表示
          state = state.copyWith(
            loginPhases: LoginNextScreens.initial,
            error: AppError(
              message: LoginErrorInfo.biometricsFailureErrorMessage,
              code: LoginErrorInfo.biometricsFailureErrorCode,
            ).copyWith(clearErrorOnShowDialog: clearLoginError),
          );
          return;
        }
        // 生体認証ログインの場合にはログイン処理に飛ばす
        final id = await loginRepository.getLoginId();
        final password = await loginRepository.getPassword();

        state.isDtpLogin
            ? await loginDtpId(id, password, false)
            : await login(id, password, false);
      },
      failure: (AppError error) async {
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: AppError(
            message: LoginErrorInfo.biometricsFailureErrorMessage,
            code: LoginErrorInfo.biometricsFailureErrorCode,
          ).copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
    _isLoadingFinished();
  }

  Future<void> cancelUsingBiometrics() async {
    state = state.copyWith(
      isUsingBiometrics: false,
      savedPwStatus: SavedPasswordStatus.none,
    );
    await loginRepository.setBiometricsLoginInfo(
      id: '',
      password: '',
      isUsingBiometrics: false,
      isFirstLoginWithIDPW: false,
      isDtpIdLogin: true,
    );
  }

  void setUsingBiometrics() {
    state = state.copyWith(isUsingBiometrics: true);
  }

  /// DBに保存する利用規定同意状況の更新
  void updateConsentStatus(List<String> newCheckedTerms) {
    state = state.copyWith(agreeTerms: newCheckedTerms);
  }

  /// DBに保存しない利用規定同意状況の更新
  void updateUnsavedConsentStatus(List<String> newCheckedTerms) {
    state = state.copyWith(unsavedAgreeTerms: newCheckedTerms);
  }

  /// 生体認証設定トグル後の処理
  void postBiometricsToggle(bool isUsingBiometrics) {
    if (isUsingBiometrics) {
      setNextScreen(LoginNextScreens.biometricsConfirmationDialog);
    } else {
      setNextScreen(LoginNextScreens.biometricsCancellationDialog);
    }
  }

  /// メモリにキャッシュされているID情報を取得する
  Future<String> _loadIdCache() => state.isDtpLogin
      ? loginRepository.loadDtpIdCache()
      : loginRepository.loadVdIdCache();

  /// 生体認証ログイン情報のローカル保存
  Future<void> setBiometricsLoginInfoWithoutAuth(bool arrowOverwrite) async {
    state = state.copyWith(isUsingBiometrics: true);
    if (arrowOverwrite) {
      await loginRepository.setBiometricsLoginInfo(
        id: await _loadIdCache(),
        password: _password,
        isUsingBiometrics: state.isUsingBiometrics,
        isFirstLoginWithIDPW: false,
        isDtpIdLogin: state.isDtpLogin,
      );
    }
    // PWがProviderに保持されないよう削除
    _password = '';

    if (state.isDtpLogin) {
      await _postLoginProcess(_sessionIdOrHighRiskUserId!);
      // キャッシュクリア
      _sessionIdOrHighRiskUserId = null;
    } else {
      await _verifySamlResponse(
        vdId: await _loadIdCache(),
        saml: _saml,
      );
      // キャッシュクリア
      _saml = '';
    }
  }

  /// 生体認証ログイン情報のローカル保存
  Future<void> setBiometricsLoginInfo(bool isUsingBiometrics) async {
    state = state.copyWith(isUsingBiometrics: isUsingBiometrics);
    final canCheckBiometrics = await biometricManager.canCheckBiometrics;
    // 生体認証利用不可の場合にはIDPWは必ず保存しない
    bool needToSaveIdPw = isUsingBiometrics && canCheckBiometrics;
    // 次回以降の生体認証利用を選択した時に生体認証を実施
    // 生体認証が成功した場合のみID,PWを保存
    if (isUsingBiometrics && canCheckBiometrics) {
      final result = await biometricManager.didAuthenticate();
      result.when(
        success: (bioResult) {
          needToSaveIdPw = bioResult;
          state = state.copyWith(isUsingBiometrics: bioResult);
        },
        failure: (error) {
          needToSaveIdPw = false;
          state = state.copyWith(isUsingBiometrics: false);
        },
      );
    }
    // 生体認証機能が失敗/生体認証を利用しない場合、VDID、PWは保存しない
    if (!needToSaveIdPw) {
      state = state.copyWith(savedPwStatus: SavedPasswordStatus.none);
      _password = '';
    }
    await loginRepository.setBiometricsLoginInfo(
      id: await _loadIdCache(),
      password: _password,
      isUsingBiometrics: state.isUsingBiometrics,
      isFirstLoginWithIDPW: false,
      isDtpIdLogin: state.isDtpLogin,
    );
    // PWがProviderに保持されないよう削除
    _password = '';
    // 呼び出しもとの処理(ログイン処理)に処理の完了を通知
    if (!_setBiometricInfoCompleter.isCompleted) {
      _setBiometricInfoCompleter.complete();
    }
  }

  /// ログイン処理
  Future<void> login(
    String vdId,
    String password,
    bool isManualLogin,
  ) async {
    _isLoadingStarted();
    final caulisSessionId = await _getSessionId();
    Log.d('fraudAlertSDK SessionId = $caulisSessionId');
    final result = await loginRepository.login(
      vdId,
      password,
      caulisSessionId,
    );
    await result.when(
      success: (vdidLoginResponse) async {
        await loginRepository.saveVdIdCache(vdId);
        _password = password;
        final isSavedPwStatusNone =
            state.savedPwStatus == SavedPasswordStatus.none;
        Log.d('samlレスポンス: ${vdidLoginResponse.fromVdParameter}');
        await _handleValueDoorResult(vdidLoginResponse);
        // ログイン成功
        if (vdidLoginResponse.isLoggedIn) {
          if (state.isUsingBiometrics && isSavedPwStatusNone) {
            // PWが保存されていない場合、生体認証なしで生体認証情報のローカル保存
            _saml = vdidLoginResponse.fromVdParameter ?? '';
            await setBiometricsLoginInfoWithoutAuth(true);
            return;
          }
          if (state.isUsingBiometrics &&
              !isSavedPwStatusNone &&
              isManualLogin &&
              state.isBiometricsAvailable) {
            // PWがすでに保存されている、かつ手動入力でのログインの場合、ViewにてID/PW上書き確認ダイアログ表示
            // その後、生体認証なしで生体認証情報のローカル保存
            state = state.copyWith(
              loginPhases: LoginNextScreens.checkOverwriteIdPwDialog,
            );
            _saml = vdidLoginResponse.fromVdParameter ?? '';
            return;
          }
          await _checkUsingBiometrics();
          //初回ログイン時に生体認証利用確認を行ったら、初回ログインフラグをfalseにする。
          //初回ログイン時の生体認証利用確認ダイアログの表示条件に「state.isFirstTimeLogin==true」があるため_checkUsingBiometrics()後に実施。
          if (state.isFirstTimeLogin) {
            state = state.copyWith(isFirstTimeLogin: false);
          }
          await _verifySamlResponse(
            vdId: vdId,
            saml: vdidLoginResponse.fromVdParameter ?? '',
          );
        }
      },
      failure: (AppError error) async {
        notifyLoginError(error);
        unawaited(_sendLoginLog());
      },
    );
    _isLoadingFinished();
  }

  /// 正常系以外のVDからの返却値を処理
  Future<void> _handleValueDoorResult(VdidLoginResponse response) async {
    // 正常系の際には何もしない
    if (response.isLoggedIn) return;
    // OTP有効化
    if (response.isOtpEnabled) {
      state = state.copyWith(vdidLoginResponse: response);
      await _handleOtpResponse(response);
    } else {
      // OTP有効化以外のレスポンスをハンドリング
      state = state.copyWith(
        loginPhases: LoginNextScreens.initial,
        error: response
            .toAppErrorFromVdResponse()
            .copyWith(clearErrorOnShowDialog: clearLoginError),
      );
    }
  }

  /// OTP認証画面から受け取った高リスクユーザーIDを検証する処理
  Future<void> verifyHighRiskUserId({
    required String highRiskUserId,
    required String idaasTicket,
  }) async {
    Log.d('highRiskUserId = $highRiskUserId');
    final result = await loginRepository.getSessionId(
      highRiskUserId: highRiskUserId,
      idaasTicket: idaasTicket,
    );
    await result.when(
      success: (_) async {
        // 高リスクユーザーIDの検証後は後続処理を呼び出す
        // パフォーマンス向上のため、ユーザー情報取得処理と後続処理を並列で走らせる
        await Future.wait(
          [analyticsLogController.setUserInfo(), _checkConsentStatus()],
        );
        // Gets権限の有無を確認
        await _checkGetsAuthorization();
        unawaited(_sendLoginLog());
      },
      failure: (error) async {
        // エラーが発生した場合
        notifyLoginError(error);
        unawaited(_sendLoginLog());
      },
    );
  }

  // loginNextScreensの初期化
  // バグ回避のためstateが不要な状態を保持し続けないよう初期化する
  void resetNextScreen() {
    state = state.copyWith(loginPhases: LoginNextScreens.initial);
  }

  ValidationError validateChangedPassword(
    String? newPassword,
    String? confirmPassword,
  ) {
    return [
      checkRequired(newPassword),
      checkIsMixedHalfWidthAlphabetAndNumber(newPassword),
      checkLengthEquals(newPassword, 8),
      checkPasswordEquals(newPassword, confirmPassword),
    ].firstError;
  }

  void _isLoadingStarted() {
    state = state.copyWith(isLoading: true);
  }

  void _isLoadingFinished() {
    state = state.copyWith(isLoading: false);
  }

  /// DTPIDログイン処理
  Future<void> loginDtpId(
    String dtpId,
    String password,
    bool isManualLogin,
  ) async {
    _isLoadingStarted();
    await loginRepository.saveDtpIdCache(dtpId);
    _password = password;
    final caulisSessionId = await _getSessionId();
    Log.d('fraudAlertSDK SessionId = $caulisSessionId');
    final result =
        await loginRepository.loginDtpId(dtpId, password, caulisSessionId);

    await result.when(
      success: (sessionIdOrHighRiskUserId) async {
        if (sessionIdOrHighRiskUserId.isSessionId) {
          // sessionIdの場合のみ顧客操作保存ログを送信
          unawaited(_sendLoginLog());
        }
        final isSavedPwStatusNone =
            state.savedPwStatus == SavedPasswordStatus.none;
        if (state.isUsingBiometrics && isSavedPwStatusNone) {
          // PWが保存されていない場合、生体認証なしで生体認証情報のローカル保存
          _sessionIdOrHighRiskUserId = sessionIdOrHighRiskUserId;
          await setBiometricsLoginInfoWithoutAuth(true);
          return;
        }
        if (state.isUsingBiometrics &&
            !isSavedPwStatusNone &&
            isManualLogin &&
            isManualLogin &&
            state.isBiometricsAvailable) {
          // PWがすでに保存されている、かつ手動入力でのログインの場合、ViewにてID/PW上書き確認ダイアログ表示
          // その後、生体認証なしで生体認証情報のローカル保存
          state = state.copyWith(
            loginPhases: LoginNextScreens.checkOverwriteIdPwDialog,
          );
          _sessionIdOrHighRiskUserId = sessionIdOrHighRiskUserId;
          return;
        }
        // 生体認証利用確認
        await _checkUsingBiometrics();
        //初回ログイン時に生体認証利用確認を行ったら、初回ログインフラグをfalseにする。
        //初回ログイン時の生体認証利用確認ダイアログの表示条件に「state.isFirstTimeLogin==true」があるため_checkUsingBiometrics()後に実施。
        if (state.isFirstTimeLogin) {
          state = state.copyWith(isFirstTimeLogin: false);
        }
        // 成功した場合には利用規定同意画面へ
        await _postLoginProcess(sessionIdOrHighRiskUserId);
      },
      failure: (AppError error) async {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
          loginPhases: LoginNextScreens.initial,
        );
        unawaited(_sendLoginLog());
      },
    );
    _isLoadingFinished();
  }

  /// 初回ログイン時の生体認証利用確認
  Future<void> _checkUsingBiometrics() async {
    final canCheckBiometric = await biometricManager.canCheckBiometrics;

    // ID/PWでの初回ログイン且つ生体認証利用可能な場合、生体認証利用確認ダイアログを表示
    if (state.isFirstTimeLogin && canCheckBiometric) {
      state = state.copyWith(
        loginPhases: LoginNextScreens.checkFirstTimeBiometricsDialog,
      );
      // 生体認証利用確認ダイアログ処理が完了するまで待つ
      await _setBiometricInfoCompleter.future;
    }
  }

  /// ログイン処理後、セッションIDまたは高リスクユーザーIDが返却された際の共通処理
  Future<void> _postLoginProcess(
    SessionIdOrHighRiskUserId sessionIdOrHighRiskUserId,
  ) async {
    _isLoadingStarted();
    await sessionIdOrHighRiskUserId.when(
      sessionId: (_) async {
        // セッションIDが返却された場合は利用規定同意状況確認へ
        // パフォーマンス向上の為、ユーザー情報取得と後続処理を並列で実施。
        await Future.wait(
          [analyticsLogController.setUserInfo(), _checkConsentStatus()],
        );
        await _checkGetsAuthorization();
      },
      highRiskUserId: (highRiskUserId) {
        // 高リスクユーザーIDが返却された場合はOTP認証処理へ進む
        state = state.copyWith(
          loginPhases: LoginNextScreens.iDaaSOtpScreen,
          highRiskUserId: highRiskUserId,
        );
      },
    );
    _isLoadingFinished();
  }

  /// OTP有効化（スキップ）
  Future<void> _enableOtpLater() async {
    final result = await loginRepository.enableOtpLater();
    await result.when(
      success: (vdidLoginResponse) async {
        if (vdidLoginResponse.isLoggedIn) {
          await _verifySamlResponse(
            vdId: await _loadIdCache(),
            saml: vdidLoginResponse.fromVdParameter ?? '',
          );
        } else {
          // TODO apNextScreenIdが異なる場合のエラー定義でありメッセージは仮置き
          state = state.copyWith(
            loginPhases: LoginNextScreens.initial,
            error: AppError(
              message: ErrorInfo.enableOtpLaterMessage,
              code: vdidLoginResponse.errorMessageId,
            ).copyWith(clearErrorOnShowDialog: clearLoginError),
          );
        }
      },
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
          loginPhases: LoginNextScreens.initial,
        );
      },
    );
  }

  /// 顧客操作ログ送信
  Future<void> _sendLoginLog() async {
    await operationLogRepository.sendOperationLog(
      functionLog: OperationLogMessage.login,
      operationLog: state.isDtpLogin
          ? OperationLogMessage.dtpId
          : OperationLogMessage.vdId,
      resultLog: state.error == null
          ? OperationLogMessage.normal
          : OperationLogMessage.abnormality,
      errorIdLog: state.error?.code ?? '',
    );
  }

  /// SAMLレスポンスの検証を行う
  Future<void> _verifySamlResponse({
    required String vdId,
    required String saml,
  }) async {
    Log.d('_samlResponse = $saml');
    final caulisSessionId = await _getSessionId();
    Log.d('fraudAlertSDK SessionId = $caulisSessionId');
    final res = await loginRepository.verifySamlResponse(
      saml,
      caulisSessionId,
    );

    await res.when(
      success: (sessionIdOrHighRiskUserId) async {
        if (sessionIdOrHighRiskUserId.isSessionId) {
          // sessionIdの場合のみ顧客操作保存ログを送信
          unawaited(_sendLoginLog());
        }
        await _postLoginProcess(sessionIdOrHighRiskUserId);
      },
      failure: (error) {
        // SAMLレスポンス検証エラーの時、失敗時の怪しさ判定リクエストを実行する
        // エラーメッセージIDは固定でシステムエラーの場合を入れる
        unawaited(
          loginRepository.loginSuspiciousDetection(
            vdId,
            caulisSessionId,
            'C0026',
          ),
        );

        // SAMLレスポンス検証エラー
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
  }

  /// 利用規定同意状況確認
  /// 本メソッド呼び出し時にはsetVdIdを同時に呼び出す。(Firebaseログ送信の為)
  Future<void> _checkConsentStatus() async {
    _isLoadingStarted();
    final res = await loginRepository.checkConsentStatus();
    await res.when(
      success: (consentStatus) async {
        if (consentStatus is ConsentStatusRequired) {
          // 利用規定同意が必要な場合
          state = state.copyWith(
            loginPhases: LoginNextScreens.checkTermsScreen,
          );
        } else {
          // 利用規定同意が不要の場合は認可前判定処理へ
          await checkBeforeAuth();
        }
      },
      failure: (error) {
        // エラー時の処理
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
    // ローディング起因のバグ防止のため、ここでローディングのstateを更新する
    _isLoadingFinished();
  }

  /// Gets権限有無確認
  Future<void> _checkGetsAuthorization() async {
    final res = await loginRepository.checkGetsAuthorization();
    res.when(
      success: (_) {
        // 何もしない
      },
      failure: (error) {
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
  }

  /// 利用規定同意状況登録
  Future<void> registerConsentStatus(List<String> agreeTerms) async {
    _isLoadingStarted();
    final result = await loginRepository.registerConsentStatus(agreeTerms);
    await result.when(
      success: (_) async {
        // 認可前判定処理へ
        await checkBeforeAuth();
      },
      failure: (error) {
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
    _isLoadingFinished();
  }

  /// 認可前判定処理
  Future<void> checkBeforeAuth([bool isFromHome = false]) async {
    state = state.copyWith(error: null);
    final result = await loginRepository.checkAuthorization();
    await result.when(
      success: (authorizationStatus) async {
        state = state.copyWith(authorizationStatus: authorizationStatus);
        // 再認可の場合、権限情報のみを更新し処理を終了
        if (isFromHome) return;
        // 照会権限、企業コードが無い際にはホーム画面に遷移
        if (!authorizationStatus.hasInquiryAuth ||
            !authorizationStatus.hasKigyoCd) {
          state = state.copyWith(
            loginPhases: LoginNextScreens.homeScreen,
          );
          return;
        }
        switch (authorizationStatus.authorizationStatus) {
          case '01':
            // 要認可
            // 認可画面呼び出し情報生成へ
            await createAuthScreenInfo();
            break;
          case '02':
            // 認可済み
            // ログイン完了へ
            state = state.copyWith(
              loginPhases: LoginNextScreens.homeScreen,
            );
            break;
          default:
            state = state.copyWith(
              error: AppError(
                message: ErrorInfo.defaultErrorMessage,
                code: ErrorInfo.defaultErrorCode,
              ).copyWith(clearErrorOnShowDialog: clearLoginError),
              loginPhases: LoginNextScreens.initial,
            );
        }
      },
      failure: (error) async {
        // 企業コード存在なしエラーか、認可拒否履歴ありエラーの場合、Homeへ遷移
        if (error.code == HomeErrorWithOutServiceInfo.withOutServiceErrorCode ||
            error.code == ErrorInfo.notAuthenticatedErrorCode) {
          state = state.copyWith(
            loginPhases: LoginNextScreens.homeScreen,
          );
          return;
        }
        state = state.copyWith(
          error: error
              .toAppError()
              .copyWith(clearErrorOnShowDialog: clearLoginError),
          loginPhases: LoginNextScreens.initial,
        );
      },
    );
  }

  /// 認可画面呼び出し情報生成
  Future<void> createAuthScreenInfo() async {
    final result = await loginRepository.createAuthScreenInfo();
    result.when(
      success: (fromEbizParam) {
        state = state.copyWith(
          loginPhases: LoginNextScreens.authScreen,
          fromEbizParam: fromEbizParam,
        );
      },
      failure: (error) {
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
  }

  void setAuthCode(String code, String state) {
    _code = code;
    _state = state;
  }

  /// トークン要求、再認可時の場合は引数にtrueを渡す
  Future<bool> getToken(bool isReauthorization) async {
    if (_code.isEmpty && _state.isEmpty) {
      // 再認可中or認可拒否の際の場合はログアウトしない（ホーム画面に戻る）
      if (!isReauthorization && !state.isRefusalFlag) await logout();
      return false;
    }

    // トークン要求まで来た場合は認可拒否フラグはfalseにする
    state = state.copyWith(isRefusalFlag: false);
    _isLoadingStarted();
    final res = await loginRepository.getToken(_code, _state);
    _code = '';
    _state = '';
    // APIの結果に依らず、HOME画面へ遷移
    // (トークン取得に失敗した場合には、HOME画面のgetUsersのエラーを元に再認可の導線表示)
    state = state.copyWith(loginPhases: LoginNextScreens.homeScreen);
    // when式をreturnする為、コチラでローディングを終了させる
    // 後続処理に非同期処理は存在しないため、本タイミングでのLoading終了による不整合はない
    _isLoadingFinished();
    return res.when(
      success: (_) => true,
      failure: (_) => false,
    );
  }

  /// ログアウト
  Future<void> logout() async {
    (await loginRepository.logout()).when(
      success: (_) {
        // 初期状態に戻す
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
        );
      },
      failure: (error) {
        // ログアウトでエラーが発生してもエラーは通知しない
        state = state.copyWith(
          loginPhases: LoginNextScreens.initial,
          error: null,
        );
      },
    );
  }

  /// 認可に関するエラー情報を削除する
  Future<void> clearAuthError() async {
    await loginRepository.clearAuthError();
  }

  /// ログインエラー情報をリセット
  void clearLoginError() {
    state = state.copyWith(
      error: null,
      changedPasswordValidationError: const ValidationError.none(),
    );
  }

  /// ログインバリデーションエラー情報をリセット
  void clearLoginValidationError() {
    state = state.copyWith(
      loginValidationError: const ValidationError.none(),
    );
  }

  void setNextScreen(LoginNextScreens next) {
    state = state.copyWith(loginPhases: next);
  }

  /// 端末情報送信処理
  Future<void> terminalInformationTransmission() async {
    await fraudAlertSDKRepository.sendData();
  }

  /// 表示・非表示を切り替える
  void togglePassWordVisibility() {
    state = state.copyWith(isPasswordVisible: !state.isPasswordVisible);
  }

  /// DTP・VDログインを切り替える
  void switchLoginScreen() {
    state = state.copyWith(isDtpLogin: !state.isDtpLogin);
    //login画面の切り替え時にバリデーションエラーをクリアする。
    state = state.copyWith(loginValidationError: ValidationError.none());
  }

  /// カウリスSDKのSessionID取得処理
  Future<String> _getSessionId() async {
    final result = await fraudAlertSDKRepository.getSessionId();
    return result.when(
      success: (caulisSessionId) {
        return caulisSessionId;
      },
      failure: (error) {
        return 'getSessionId_function_failed';
      },
    );
  }

  // otp後で有効化時のハンドリング
  Future<void> _handleOtpResponse(VdidLoginResponse response) async {
    final isLateActivateEnable = response.isOtpLateActivationEnable;
    // OTPの情報が受領されている場合且つ後で有効化可能な場合、後て有効化を実施
    // しかし、ワンタイムパスワードアプリによる後で有効化は許容されていないためエラーとしてハンドリング
    if (response.isOtpReceived &&
        response.otpKind != VdOtpKind.app &&
        isLateActivateEnable) {
      await _enableOtpLater();
      return;
    }
    final otpError = state.vdidLoginResponse?.toAppErrorRelatedOtp();
    state = state.copyWith(
      error: otpError?.copyWith(clearErrorOnShowDialog: clearLoginError),
    );
  }

  ///　業務閉塞
  Future<void> getBusinessBlockageStatus() async {
    _isLoadingStarted();
    // TODO: Ph0.5ではfunctionIdが1種類のため、エラーコードを表示していないが、2種類以上になったタイミングで採番が必要
    await businessBlockageController.getBlockageStatus(functionId: '1101');
    final error = businessBlockageController.businessBlockageError;
    final isBlocked =
        error == null ? businessBlockageController.isBlocked : false;
    if (error?.reason is SorryScreenException || isBlocked) {
      _notifyBusinessBlockage();
    }
    _isLoadingFinished();
  }

  /// 認可拒否履歴登録
  Future<void> registerRefusalFlag() async {
    final result = await loginRepository.registerRefusalFlag();
    await result.when(
      success: (_) async {
        state = state.copyWith(
          isRefusalFlag: true,
        );
        await checkBeforeAuth();
      },
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
  }

  /// 認可拒否履歴削除
  Future<void> deleteRefusalFlag() async {
    final result = await loginRepository.deleteRefusalFlag();
    result.when(
      success: (_) {
        // 何もしない
      },
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearLoginError),
        );
      },
    );
  }

  Future<void> deleteVolatileInfo() async {
    await loginRepository.deleteVolatileInfo();
  }
}
