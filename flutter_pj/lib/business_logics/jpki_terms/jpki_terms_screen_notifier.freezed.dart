// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'jpki_terms_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$JpkiTermsScreenState {
  List<String> get jpkiAgreeTerms => throw _privateConstructorUsedError;

  /// Create a copy of JpkiTermsScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $JpkiTermsScreenStateCopyWith<JpkiTermsScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $JpkiTermsScreenStateCopyWith<$Res> {
  factory $JpkiTermsScreenStateCopyWith(JpkiTermsScreenState value,
          $Res Function(JpkiTermsScreenState) then) =
      _$JpkiTermsScreenStateCopyWithImpl<$Res, JpkiTermsScreenState>;
  @useResult
  $Res call({List<String> jpkiAgreeTerms});
}

/// @nodoc
class _$JpkiTermsScreenStateCopyWithImpl<$Res,
        $Val extends JpkiTermsScreenState>
    implements $JpkiTermsScreenStateCopyWith<$Res> {
  _$JpkiTermsScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of JpkiTermsScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jpkiAgreeTerms = null,
  }) {
    return _then(_value.copyWith(
      jpkiAgreeTerms: null == jpkiAgreeTerms
          ? _value.jpkiAgreeTerms
          : jpkiAgreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$JpkiTermsScreenStateImplCopyWith<$Res>
    implements $JpkiTermsScreenStateCopyWith<$Res> {
  factory _$$JpkiTermsScreenStateImplCopyWith(_$JpkiTermsScreenStateImpl value,
          $Res Function(_$JpkiTermsScreenStateImpl) then) =
      __$$JpkiTermsScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<String> jpkiAgreeTerms});
}

/// @nodoc
class __$$JpkiTermsScreenStateImplCopyWithImpl<$Res>
    extends _$JpkiTermsScreenStateCopyWithImpl<$Res, _$JpkiTermsScreenStateImpl>
    implements _$$JpkiTermsScreenStateImplCopyWith<$Res> {
  __$$JpkiTermsScreenStateImplCopyWithImpl(_$JpkiTermsScreenStateImpl _value,
      $Res Function(_$JpkiTermsScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of JpkiTermsScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jpkiAgreeTerms = null,
  }) {
    return _then(_$JpkiTermsScreenStateImpl(
      jpkiAgreeTerms: null == jpkiAgreeTerms
          ? _value._jpkiAgreeTerms
          : jpkiAgreeTerms // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$JpkiTermsScreenStateImpl implements _JpkiTermsScreenState {
  const _$JpkiTermsScreenStateImpl(
      {final List<String> jpkiAgreeTerms = const <String>[]})
      : _jpkiAgreeTerms = jpkiAgreeTerms;

  final List<String> _jpkiAgreeTerms;
  @override
  @JsonKey()
  List<String> get jpkiAgreeTerms {
    if (_jpkiAgreeTerms is EqualUnmodifiableListView) return _jpkiAgreeTerms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_jpkiAgreeTerms);
  }

  @override
  String toString() {
    return 'JpkiTermsScreenState(jpkiAgreeTerms: $jpkiAgreeTerms)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$JpkiTermsScreenStateImpl &&
            const DeepCollectionEquality()
                .equals(other._jpkiAgreeTerms, _jpkiAgreeTerms));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_jpkiAgreeTerms));

  /// Create a copy of JpkiTermsScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$JpkiTermsScreenStateImplCopyWith<_$JpkiTermsScreenStateImpl>
      get copyWith =>
          __$$JpkiTermsScreenStateImplCopyWithImpl<_$JpkiTermsScreenStateImpl>(
              this, _$identity);
}

abstract class _JpkiTermsScreenState implements JpkiTermsScreenState {
  const factory _JpkiTermsScreenState({final List<String> jpkiAgreeTerms}) =
      _$JpkiTermsScreenStateImpl;

  @override
  List<String> get jpkiAgreeTerms;

  /// Create a copy of JpkiTermsScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$JpkiTermsScreenStateImplCopyWith<_$JpkiTermsScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
