import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'jpki_terms_screen_notifier.freezed.dart';

@freezed
class JpkiTermsScreenState with _$JpkiTermsScreenState {
  const factory JpkiTermsScreenState({
    @Default(<String>[]) List<String> jpkiAgreeTerms,
  }) = _JpkiTermsScreenState;
}

final jpkiTermsScreenProvider = StateNotifierProvider.autoDispose<
    JpkiTermsScreenNotifier, JpkiTermsScreenState>(
  (ref) => JpkiTermsScreenNotifier(
    state: const JpkiTermsScreenState(),
    analyticsLogController: ref.read(analyticsLogControllerProvider),
  ),
);

class JpkiTermsScreenNotifier extends StateNotifier<JpkiTermsScreenState> {
  JpkiTermsScreenNotifier({
    required JpkiTermsScreenState state,
    required this.analyticsLogController,
  }) : super(const JpkiTermsScreenState());

  final AnalyticsLogController analyticsLogController;

  /// 利用規定同意状況の更新
  void updateJpkiConsentStatus(List<String> newCheckedTerms) {
    state = state.copyWith(jpkiAgreeTerms: newCheckedTerms);
  }
}
