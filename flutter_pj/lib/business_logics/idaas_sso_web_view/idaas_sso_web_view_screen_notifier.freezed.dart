// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'idaas_sso_web_view_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IdaasSsoScreenState {
  AppError? get loginError => throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdaasSsoScreenStateCopyWith<IdaasSsoScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdaasSsoScreenStateCopyWith<$Res> {
  factory $IdaasSsoScreenStateCopyWith(
          IdaasSsoScreenState value, $Res Function(IdaasSsoScreenState) then) =
      _$IdaasSsoScreenStateCopyWithImpl<$Res, IdaasSsoScreenState>;
  @useResult
  $Res call({AppError? loginError, AppError? error});

  $AppErrorCopyWith<$Res>? get loginError;
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$IdaasSsoScreenStateCopyWithImpl<$Res, $Val extends IdaasSsoScreenState>
    implements $IdaasSsoScreenStateCopyWith<$Res> {
  _$IdaasSsoScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginError = freezed,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      loginError: freezed == loginError
          ? _value.loginError
          : loginError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get loginError {
    if (_value.loginError == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.loginError!, (value) {
      return _then(_value.copyWith(loginError: value) as $Val);
    });
  }

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdaasSsoScreenStateImplCopyWith<$Res>
    implements $IdaasSsoScreenStateCopyWith<$Res> {
  factory _$$IdaasSsoScreenStateImplCopyWith(_$IdaasSsoScreenStateImpl value,
          $Res Function(_$IdaasSsoScreenStateImpl) then) =
      __$$IdaasSsoScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({AppError? loginError, AppError? error});

  @override
  $AppErrorCopyWith<$Res>? get loginError;
  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$IdaasSsoScreenStateImplCopyWithImpl<$Res>
    extends _$IdaasSsoScreenStateCopyWithImpl<$Res, _$IdaasSsoScreenStateImpl>
    implements _$$IdaasSsoScreenStateImplCopyWith<$Res> {
  __$$IdaasSsoScreenStateImplCopyWithImpl(_$IdaasSsoScreenStateImpl _value,
      $Res Function(_$IdaasSsoScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loginError = freezed,
    Object? error = freezed,
  }) {
    return _then(_$IdaasSsoScreenStateImpl(
      loginError: freezed == loginError
          ? _value.loginError
          : loginError // ignore: cast_nullable_to_non_nullable
              as AppError?,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$IdaasSsoScreenStateImpl
    with DiagnosticableTreeMixin
    implements _IdaasSsoScreenState {
  const _$IdaasSsoScreenStateImpl({this.loginError, this.error});

  @override
  final AppError? loginError;
  @override
  final AppError? error;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'IdaasSsoScreenState(loginError: $loginError, error: $error)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'IdaasSsoScreenState'))
      ..add(DiagnosticsProperty('loginError', loginError))
      ..add(DiagnosticsProperty('error', error));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdaasSsoScreenStateImpl &&
            (identical(other.loginError, loginError) ||
                other.loginError == loginError) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loginError, error);

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdaasSsoScreenStateImplCopyWith<_$IdaasSsoScreenStateImpl> get copyWith =>
      __$$IdaasSsoScreenStateImplCopyWithImpl<_$IdaasSsoScreenStateImpl>(
          this, _$identity);
}

abstract class _IdaasSsoScreenState implements IdaasSsoScreenState {
  const factory _IdaasSsoScreenState(
      {final AppError? loginError,
      final AppError? error}) = _$IdaasSsoScreenStateImpl;

  @override
  AppError? get loginError;
  @override
  AppError? get error;

  /// Create a copy of IdaasSsoScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdaasSsoScreenStateImplCopyWith<_$IdaasSsoScreenStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
