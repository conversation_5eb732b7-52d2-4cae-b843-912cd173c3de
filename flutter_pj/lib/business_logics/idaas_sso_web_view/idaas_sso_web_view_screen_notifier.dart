import 'dart:async';

import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';

part 'idaas_sso_web_view_screen_notifier.freezed.dart';

@freezed
class IdaasSsoScreenState with _$IdaasSsoScreenState {
  const factory IdaasSsoScreenState({
    AppError? loginError,
    AppError? error,
  }) = _IdaasSsoScreenState;
}

final idaasSsoScreenProvider = StateNotifierProvider.autoDispose<
    IdaasSsoScreenNotifier, IdaasSsoScreenState>(
  (ref) => IdaasSsoScreenNotifier(
    loginRepository: ref.read(loginRepositoryProvider),
    mdManager: ref.read(mdManagerProvider),
  ),
);

class IdaasSsoScreenNotifier extends StateNotifier<IdaasSsoScreenState> {
  IdaasSsoScreenNotifier({
    required this.loginRepository,
    required this.mdManager,
  }) : super(const IdaasSsoScreenState());

  final LoginRepository loginRepository;
  final MemoryDataManager mdManager;

  // DTPIDログイン処理(暗号化Cookie生成)
  Future<void> generateEncryptedCookie(
    String dtpId,
    String password,
    String caulisSessionId,
  ) async {
    final result =
        await loginRepository.loginDtpId(dtpId, password, caulisSessionId);
    result.when(
      success: (_) {
        final String encryptedCookie = result.toString();
        return encryptedCookie;
      },
      failure: (_) {},
    );
  }
}
