// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'id_linkage_screen_notifier.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$IdLinkageScreenState {
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isCompleteUnlinking => throw _privateConstructorUsedError;
  String get dtpId => throw _privateConstructorUsedError;
  String get vdId => throw _privateConstructorUsedError;
  bool get isCreated => throw _privateConstructorUsedError;
  AppError? get error => throw _privateConstructorUsedError;

  /// Create a copy of IdLinkageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $IdLinkageScreenStateCopyWith<IdLinkageScreenState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $IdLinkageScreenStateCopyWith<$Res> {
  factory $IdLinkageScreenStateCopyWith(IdLinkageScreenState value,
          $Res Function(IdLinkageScreenState) then) =
      _$IdLinkageScreenStateCopyWithImpl<$Res, IdLinkageScreenState>;
  @useResult
  $Res call(
      {bool isLoading,
      bool isCompleteUnlinking,
      String dtpId,
      String vdId,
      bool isCreated,
      AppError? error});

  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$IdLinkageScreenStateCopyWithImpl<$Res,
        $Val extends IdLinkageScreenState>
    implements $IdLinkageScreenStateCopyWith<$Res> {
  _$IdLinkageScreenStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of IdLinkageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isCompleteUnlinking = null,
    Object? dtpId = null,
    Object? vdId = null,
    Object? isCreated = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCompleteUnlinking: null == isCompleteUnlinking
          ? _value.isCompleteUnlinking
          : isCompleteUnlinking // ignore: cast_nullable_to_non_nullable
              as bool,
      dtpId: null == dtpId
          ? _value.dtpId
          : dtpId // ignore: cast_nullable_to_non_nullable
              as String,
      vdId: null == vdId
          ? _value.vdId
          : vdId // ignore: cast_nullable_to_non_nullable
              as String,
      isCreated: null == isCreated
          ? _value.isCreated
          : isCreated // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ) as $Val);
  }

  /// Create a copy of IdLinkageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res>? get error {
    if (_value.error == null) {
      return null;
    }

    return $AppErrorCopyWith<$Res>(_value.error!, (value) {
      return _then(_value.copyWith(error: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$IdLinkageScreenStateImplCopyWith<$Res>
    implements $IdLinkageScreenStateCopyWith<$Res> {
  factory _$$IdLinkageScreenStateImplCopyWith(_$IdLinkageScreenStateImpl value,
          $Res Function(_$IdLinkageScreenStateImpl) then) =
      __$$IdLinkageScreenStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      bool isCompleteUnlinking,
      String dtpId,
      String vdId,
      bool isCreated,
      AppError? error});

  @override
  $AppErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$$IdLinkageScreenStateImplCopyWithImpl<$Res>
    extends _$IdLinkageScreenStateCopyWithImpl<$Res, _$IdLinkageScreenStateImpl>
    implements _$$IdLinkageScreenStateImplCopyWith<$Res> {
  __$$IdLinkageScreenStateImplCopyWithImpl(_$IdLinkageScreenStateImpl _value,
      $Res Function(_$IdLinkageScreenStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of IdLinkageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? isCompleteUnlinking = null,
    Object? dtpId = null,
    Object? vdId = null,
    Object? isCreated = null,
    Object? error = freezed,
  }) {
    return _then(_$IdLinkageScreenStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCompleteUnlinking: null == isCompleteUnlinking
          ? _value.isCompleteUnlinking
          : isCompleteUnlinking // ignore: cast_nullable_to_non_nullable
              as bool,
      dtpId: null == dtpId
          ? _value.dtpId
          : dtpId // ignore: cast_nullable_to_non_nullable
              as String,
      vdId: null == vdId
          ? _value.vdId
          : vdId // ignore: cast_nullable_to_non_nullable
              as String,
      isCreated: null == isCreated
          ? _value.isCreated
          : isCreated // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError?,
    ));
  }
}

/// @nodoc

class _$IdLinkageScreenStateImpl implements _IdLinkageScreenState {
  const _$IdLinkageScreenStateImpl(
      {this.isLoading = false,
      this.isCompleteUnlinking = false,
      this.dtpId = '',
      this.vdId = '',
      this.isCreated = false,
      this.error});

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isCompleteUnlinking;
  @override
  @JsonKey()
  final String dtpId;
  @override
  @JsonKey()
  final String vdId;
  @override
  @JsonKey()
  final bool isCreated;
  @override
  final AppError? error;

  @override
  String toString() {
    return 'IdLinkageScreenState(isLoading: $isLoading, isCompleteUnlinking: $isCompleteUnlinking, dtpId: $dtpId, vdId: $vdId, isCreated: $isCreated, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$IdLinkageScreenStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isCompleteUnlinking, isCompleteUnlinking) ||
                other.isCompleteUnlinking == isCompleteUnlinking) &&
            (identical(other.dtpId, dtpId) || other.dtpId == dtpId) &&
            (identical(other.vdId, vdId) || other.vdId == vdId) &&
            (identical(other.isCreated, isCreated) ||
                other.isCreated == isCreated) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isLoading, isCompleteUnlinking,
      dtpId, vdId, isCreated, error);

  /// Create a copy of IdLinkageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$IdLinkageScreenStateImplCopyWith<_$IdLinkageScreenStateImpl>
      get copyWith =>
          __$$IdLinkageScreenStateImplCopyWithImpl<_$IdLinkageScreenStateImpl>(
              this, _$identity);
}

abstract class _IdLinkageScreenState implements IdLinkageScreenState {
  const factory _IdLinkageScreenState(
      {final bool isLoading,
      final bool isCompleteUnlinking,
      final String dtpId,
      final String vdId,
      final bool isCreated,
      final AppError? error}) = _$IdLinkageScreenStateImpl;

  @override
  bool get isLoading;
  @override
  bool get isCompleteUnlinking;
  @override
  String get dtpId;
  @override
  String get vdId;
  @override
  bool get isCreated;
  @override
  AppError? get error;

  /// Create a copy of IdLinkageScreenState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$IdLinkageScreenStateImplCopyWith<_$IdLinkageScreenStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
