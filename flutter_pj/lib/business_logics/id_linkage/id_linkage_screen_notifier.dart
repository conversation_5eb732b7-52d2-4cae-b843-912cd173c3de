import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/business_logics/interfaces/operation_log_repository.dart';
import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/operation_log_repository/operation_log_repository_impl.dart';
import 'package:dtp_app/repositories/user/user_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dtp_app/utils/operation_log_message.dart';

part 'id_linkage_screen_notifier.freezed.dart';

@freezed
class IdLinkageScreenState with _$IdLinkageScreenState {
  const factory IdLinkageScreenState({
    @Default(false) bool isLoading,
    @Default(false) bool isCompleteUnlinking,
    @Default('') String dtpId,
    @Default('') String vdId,
    @Default(false) bool isCreated,
    AppError? error,
  }) = _IdLinkageScreenState;
}

final idLinkageScreenProvider = StateNotifierProvider.autoDispose<
    IdLinkageScreenNotifier, IdLinkageScreenState>(
  (ref) => IdLinkageScreenNotifier(
    analyticsLogController: ref.read(analyticsLogControllerProvider),
    userRepository: ref.read(userRepositoryProvider),
    loginRepository: ref.read(loginRepositoryProvider),
    operationLogRepository: ref.read(operationLogRepositoryProvider),
  ),
);

class IdLinkageScreenNotifier extends StateNotifier<IdLinkageScreenState> {
  IdLinkageScreenNotifier({
    required this.analyticsLogController,
    required this.userRepository,
    required this.loginRepository,
    required this.operationLogRepository,
  }) : super(IdLinkageScreenState());

  final AnalyticsLogController analyticsLogController;
  final UserRepository userRepository;
  final LoginRepository loginRepository;
  final OperationLogRepository operationLogRepository;

  Future<void> setArguments({
    required String dtpId,
    required String vdId,
  }) async {
    state = state.copyWith(
      dtpId: dtpId,
      vdId: vdId,
    );
  }

  void _onLoadStarted() {
    state = state.copyWith(isLoading: true);
  }

  void _onLoadFinished() {
    state = state.copyWith(isLoading: false);
  }

  /// ID連携解除
  Future<void> idUnlinking() async {
    _onLoadStarted();
    final res = await userRepository.deleteDtpIdLink();
    res.when(
      success: (result) {
        state = state.copyWith(isCompleteUnlinking: true);
        // 顧客操作ログ送信
        _sendLinkageLog(false);
      },
      failure: (error) {
        state = state.copyWith(error: error);
        // 顧客操作ログ送信
        _sendLinkageLog(false);
      },
    );
    _onLoadFinished();
  }

  /// 完了ダイアログフラグのリセット
  void resetCompleteFlag() {
    state = state.copyWith(isCompleteUnlinking: false);
  }

  /// エラークリア
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// ログアウト
  Future<void> logout() async {
    await loginRepository.logout();
  }

  /// DTPID更新
  Future<void> updateDtpId() async {
    _onLoadStarted();
    final updateDtpIdResponse = await userRepository.updateDtpId();
    await updateDtpIdResponse.when(
      success: (_) async {
        final getUserInfoResponse = await userRepository.getUserInfo();
        getUserInfoResponse.when(
          success: (userInfo) {
            state = state.copyWith(dtpId: userInfo.dtpId);
          },
          failure: (error) {
            state = state.copyWith(error: error);
          },
        );
      },
      failure: (error) {
        state = state.copyWith(error: error);
      },
    );
    _onLoadFinished();
  }

  /// 暗号化Cookie更新
  Future<void> refreshEncryptedCookie() async {
    _onLoadStarted();
    final result = await loginRepository.getEncryptedCookie();
    result.when(
      success: (_) {},
      failure: (error) {
        state = state.copyWith(
          error: error.copyWith(clearErrorOnShowDialog: clearError),
        );
      },
    );
    _onLoadFinished();
  }

  /// ID紐付け情報登録
  Future<void> issueAndLinkDtpId(EncryptedCookie encryptedCookie) async {
    _onLoadStarted();
    final res = await userRepository.issueAndLinkDtpId(encryptedCookie);
    res.when(
      success: (dtpId) {
        // ID紐付け正常完了
        state = state.copyWith(dtpId: dtpId);
        _sendLinkageLog(true);
      },
      failure: (error) {
        state = state.copyWith(error: error);
        if (error.code != ErrorInfo.idLinkageInformationRegistrationErrorCode) {
          // ID紐付け情報登録リクエストのみ成功
          // ID紐付け情報リクエスト、セッション情報更新失敗
          state = state.copyWith(isCreated: true);
          _sendLinkageLog(true);
        }
      },
    );
    _onLoadFinished();
  }

  /// ID連携/連携解除の顧客操作ログを送信
  Future<void> _sendLinkageLog(bool isLinked) async {
    await operationLogRepository.sendOperationLog(
      functionLog: OperationLogMessage.idLinkage,
      operationLog:
          isLinked ? OperationLogMessage.link : OperationLogMessage.unlink,
      resultLog: state.error == null
          ? OperationLogMessage.normal
          : OperationLogMessage.abnormality,
      errorIdLog: state.error?.code ?? '',
    );
  }
}
