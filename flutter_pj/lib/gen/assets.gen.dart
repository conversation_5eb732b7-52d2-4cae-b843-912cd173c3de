/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// Directory path: assets/images/png
  $AssetsImagesPngGen get png => const $AssetsImagesPngGen();

  /// Directory path: assets/images/svg
  $AssetsImagesSvgGen get svg => const $AssetsImagesSvgGen();
}

class $AssetsImagesPngGen {
  const $AssetsImagesPngGen();

  /// File path: assets/images/png/add_credit_card.png
  AssetGenImage get addCreditCard =>
      const AssetGenImage('assets/images/png/add_credit_card.png');

  /// File path: assets/images/png/add_credit_card_frame.png
  AssetGenImage get addCreditCardFrame =>
      const AssetGenImage('assets/images/png/add_credit_card_frame.png');

  /// File path: assets/images/png/add_other_bank_card_frame.png
  AssetGenImage get addOtherBankCardFrame =>
      const AssetGenImage('assets/images/png/add_other_bank_card_frame.png');

  /// File path: assets/images/png/bg_wave.png
  AssetGenImage get bgWave =>
      const AssetGenImage('assets/images/png/bg_wave.png');

  /// File path: assets/images/png/bg_wave_before_login.png
  AssetGenImage get bgWaveBeforeLogin =>
      const AssetGenImage('assets/images/png/bg_wave_before_login.png');

  /// File path: assets/images/png/bg_wave_grey.png
  AssetGenImage get bgWaveGrey =>
      const AssetGenImage('assets/images/png/bg_wave_grey.png');

  /// File path: assets/images/png/bg_wave_grey_half.png
  AssetGenImage get bgWaveGreyHalf =>
      const AssetGenImage('assets/images/png/bg_wave_grey_half.png');

  /// File path: assets/images/png/bg_wave_home.png
  AssetGenImage get bgWaveHome =>
      const AssetGenImage('assets/images/png/bg_wave_home.png');

  /// File path: assets/images/png/card_reader_reading.png
  AssetGenImage get cardReaderReading =>
      const AssetGenImage('assets/images/png/card_reader_reading.png');

  /// File path: assets/images/png/card_reader_setup.png
  AssetGenImage get cardReaderSetup =>
      const AssetGenImage('assets/images/png/card_reader_setup.png');

  /// File path: assets/images/png/card_reader_success.png
  AssetGenImage get cardReaderSuccess =>
      const AssetGenImage('assets/images/png/card_reader_success.png');

  /// File path: assets/images/png/continue_on_web_example.png
  AssetGenImage get continueOnWebExample =>
      const AssetGenImage('assets/images/png/continue_on_web_example.png');

  /// File path: assets/images/png/credit_card_rectangle.png
  AssetGenImage get creditCardRectangle =>
      const AssetGenImage('assets/images/png/credit_card_rectangle.png');

  /// File path: assets/images/png/credit_setting_card_rectangle.png
  AssetGenImage get creditSettingCardRectangle => const AssetGenImage(
      'assets/images/png/credit_setting_card_rectangle.png');

  /// File path: assets/images/png/driving_licenses.png
  AssetGenImage get drivingLicenses =>
      const AssetGenImage('assets/images/png/driving_licenses.png');

  /// File path: assets/images/png/driving_licenses_selected.png
  AssetGenImage get drivingLicensesSelected =>
      const AssetGenImage('assets/images/png/driving_licenses_selected.png');

  /// File path: assets/images/png/dtpid_alignment.png
  AssetGenImage get dtpidAlignment =>
      const AssetGenImage('assets/images/png/dtpid_alignment.png');

  /// File path: assets/images/png/ekyc_explanation.png
  AssetGenImage get ekycExplanation =>
      const AssetGenImage('assets/images/png/ekyc_explanation.png');

  /// File path: assets/images/png/freee_connect.png
  AssetGenImage get freeeConnect =>
      const AssetGenImage('assets/images/png/freee_connect.png');

  /// File path: assets/images/png/freee_connect_background.png
  AssetGenImage get freeeConnectBackground =>
      const AssetGenImage('assets/images/png/freee_connect_background.png');

  /// File path: assets/images/png/freee_link_card.png
  AssetGenImage get freeeLinkCard =>
      const AssetGenImage('assets/images/png/freee_link_card.png');

  /// File path: assets/images/png/freee_linkage_completion.png
  AssetGenImage get freeeLinkageCompletion =>
      const AssetGenImage('assets/images/png/freee_linkage_completion.png');

  /// File path: assets/images/png/freee_linkage_flow_image.png
  AssetGenImage get freeeLinkageFlowImage =>
      const AssetGenImage('assets/images/png/freee_linkage_flow_image.png');

  /// File path: assets/images/png/freee_linkage_flow_image_employee.png
  AssetGenImage get freeeLinkageFlowImageEmployee => const AssetGenImage(
      'assets/images/png/freee_linkage_flow_image_employee.png');

  /// File path: assets/images/png/home_light_bg.png
  AssetGenImage get homeLightBg =>
      const AssetGenImage('assets/images/png/home_light_bg.png');

  /// File path: assets/images/png/login_footer.png
  AssetGenImage get loginFooter =>
      const AssetGenImage('assets/images/png/login_footer.png');

  /// File path: assets/images/png/my_number_card.png
  AssetGenImage get myNumberCard =>
      const AssetGenImage('assets/images/png/my_number_card.png');

  /// File path: assets/images/png/my_number_card_catchphrase.png
  AssetGenImage get myNumberCardCatchphrase =>
      const AssetGenImage('assets/images/png/my_number_card_catchphrase.png');

  /// File path: assets/images/png/my_number_card_new.png
  AssetGenImage get myNumberCardNew =>
      const AssetGenImage('assets/images/png/my_number_card_new.png');

  /// File path: assets/images/png/my_number_scan_image.png
  AssetGenImage get myNumberScanImage =>
      const AssetGenImage('assets/images/png/my_number_scan_image.png');

  /// File path: assets/images/png/password_eye.png
  AssetGenImage get passwordEye =>
      const AssetGenImage('assets/images/png/password_eye.png');

  /// File path: assets/images/png/password_input_explanation.png
  AssetGenImage get passwordInputExplanation =>
      const AssetGenImage('assets/images/png/password_input_explanation.png');

  /// File path: assets/images/png/plari_town.png
  AssetGenImage get plariTown =>
      const AssetGenImage('assets/images/png/plari_town.png');

  /// File path: assets/images/png/reference_number_process.png
  AssetGenImage get referenceNumberProcess =>
      const AssetGenImage('assets/images/png/reference_number_process.png');

  /// File path: assets/images/png/related_information_01.png
  AssetGenImage get relatedInformation01 =>
      const AssetGenImage('assets/images/png/related_information_01.png');

  /// File path: assets/images/png/related_information_02.png
  AssetGenImage get relatedInformation02 =>
      const AssetGenImage('assets/images/png/related_information_02.png');

  /// File path: assets/images/png/related_information_03.png
  AssetGenImage get relatedInformation03 =>
      const AssetGenImage('assets/images/png/related_information_03.png');

  /// File path: assets/images/png/related_information_04.png
  AssetGenImage get relatedInformation04 =>
      const AssetGenImage('assets/images/png/related_information_04.png');

  /// File path: assets/images/png/related_information_05.png
  AssetGenImage get relatedInformation05 =>
      const AssetGenImage('assets/images/png/related_information_05.png');

  /// File path: assets/images/png/smbc_business_background.png
  AssetGenImage get smbcBusinessBackground =>
      const AssetGenImage('assets/images/png/smbc_business_background.png');

  /// File path: assets/images/png/smbc_business_card.png
  AssetGenImage get smbcBusinessCard =>
      const AssetGenImage('assets/images/png/smbc_business_card.png');

  /// File path: assets/images/png/smbc_business_icon_large.png
  AssetGenImage get smbcBusinessIconLarge =>
      const AssetGenImage('assets/images/png/smbc_business_icon_large.png');

  /// File path: assets/images/png/smbc_logo_tag.png
  AssetGenImage get smbcLogoTag =>
      const AssetGenImage('assets/images/png/smbc_logo_tag.png');

  /// File path: assets/images/png/smbc_logo_tagS.png
  AssetGenImage get smbcLogoTagS =>
      const AssetGenImage('assets/images/png/smbc_logo_tagS.png');

  /// File path: assets/images/png/splash_logo_demo.png
  AssetGenImage get splashLogoDemo =>
      const AssetGenImage('assets/images/png/splash_logo_demo.png');

  /// File path: assets/images/png/tradgreen_card.png
  AssetGenImage get tradgreenCard =>
      const AssetGenImage('assets/images/png/tradgreen_card.png');

  /// File path: assets/images/png/white_card.png
  AssetGenImage get whiteCard =>
      const AssetGenImage('assets/images/png/white_card.png');

  /// File path: assets/images/png/white_card_bar.png
  AssetGenImage get whiteCardBar =>
      const AssetGenImage('assets/images/png/white_card_bar.png');

  /// List of all assets
  List<AssetGenImage> get values => [
        addCreditCard,
        addCreditCardFrame,
        addOtherBankCardFrame,
        bgWave,
        bgWaveBeforeLogin,
        bgWaveGrey,
        bgWaveGreyHalf,
        bgWaveHome,
        cardReaderReading,
        cardReaderSetup,
        cardReaderSuccess,
        continueOnWebExample,
        creditCardRectangle,
        creditSettingCardRectangle,
        drivingLicenses,
        drivingLicensesSelected,
        dtpidAlignment,
        ekycExplanation,
        freeeConnect,
        freeeConnectBackground,
        freeeLinkCard,
        freeeLinkageCompletion,
        freeeLinkageFlowImage,
        freeeLinkageFlowImageEmployee,
        homeLightBg,
        loginFooter,
        myNumberCard,
        myNumberCardCatchphrase,
        myNumberCardNew,
        myNumberScanImage,
        passwordEye,
        passwordInputExplanation,
        plariTown,
        referenceNumberProcess,
        relatedInformation01,
        relatedInformation02,
        relatedInformation03,
        relatedInformation04,
        relatedInformation05,
        smbcBusinessBackground,
        smbcBusinessCard,
        smbcBusinessIconLarge,
        smbcLogoTag,
        smbcLogoTagS,
        splashLogoDemo,
        tradgreenCard,
        whiteCard,
        whiteCardBar
      ];
}

class $AssetsImagesSvgGen {
  const $AssetsImagesSvgGen();

  /// File path: assets/images/svg/Icon_Account_Balance.svg
  String get iconAccountBalance => 'assets/images/svg/Icon_Account_Balance.svg';

  /// File path: assets/images/svg/Icon_Balance.svg
  String get iconBalance => 'assets/images/svg/Icon_Balance.svg';

  /// File path: assets/images/svg/Icon_Beginner.svg
  String get iconBeginner => 'assets/images/svg/Icon_Beginner.svg';

  /// File path: assets/images/svg/Icon_Bell.svg
  String get iconBell => 'assets/images/svg/Icon_Bell.svg';

  /// File path: assets/images/svg/Icon_Button_Close.svg
  String get iconButtonClose => 'assets/images/svg/Icon_Button_Close.svg';

  /// File path: assets/images/svg/Icon_Calendar_Gray.svg
  String get iconCalendarGray => 'assets/images/svg/Icon_Calendar_Gray.svg';

  /// File path: assets/images/svg/Icon_Calendar_Green.svg
  String get iconCalendarGreen => 'assets/images/svg/Icon_Calendar_Green.svg';

  /// File path: assets/images/svg/Icon_Card_Link.svg
  String get iconCardLink => 'assets/images/svg/Icon_Card_Link.svg';

  /// File path: assets/images/svg/Icon_Chart.svg
  String get iconChart => 'assets/images/svg/Icon_Chart.svg';

  /// File path: assets/images/svg/Icon_Check.svg
  String get iconCheck => 'assets/images/svg/Icon_Check.svg';

  /// File path: assets/images/svg/Icon_Check_Gray.svg
  String get iconCheckGray => 'assets/images/svg/Icon_Check_Gray.svg';

  /// File path: assets/images/svg/Icon_Check_Green.svg
  String get iconCheckGreen => 'assets/images/svg/Icon_Check_Green.svg';

  /// File path: assets/images/svg/Icon_Check_Green2.svg
  String get iconCheckGreen2 => 'assets/images/svg/Icon_Check_Green2.svg';

  /// File path: assets/images/svg/Icon_Check_Red.svg
  String get iconCheckRed => 'assets/images/svg/Icon_Check_Red.svg';

  /// File path: assets/images/svg/Icon_Circle.svg
  String get iconCircle => 'assets/images/svg/Icon_Circle.svg';

  /// File path: assets/images/svg/Icon_Credit_Card.svg
  String get iconCreditCard => 'assets/images/svg/Icon_Credit_Card.svg';

  /// File path: assets/images/svg/Icon_Filled_Arrow_Bottom.svg
  String get iconFilledArrowBottom =>
      'assets/images/svg/Icon_Filled_Arrow_Bottom.svg';

  /// File path: assets/images/svg/Icon_Filter.svg
  String get iconFilter => 'assets/images/svg/Icon_Filter.svg';

  /// File path: assets/images/svg/Icon_Grey_Circcle.svg
  String get iconGreyCirccle => 'assets/images/svg/Icon_Grey_Circcle.svg';

  /// File path: assets/images/svg/Icon_Help_White.svg
  String get iconHelpWhite => 'assets/images/svg/Icon_Help_White.svg';

  /// File path: assets/images/svg/Icon_Home.svg
  String get iconHome => 'assets/images/svg/Icon_Home.svg';

  /// File path: assets/images/svg/Icon_Human.svg
  String get iconHuman => 'assets/images/svg/Icon_Human.svg';

  /// File path: assets/images/svg/Icon_Id_Management.svg
  String get iconIdManagement => 'assets/images/svg/Icon_Id_Management.svg';

  /// File path: assets/images/svg/Icon_Inactive_Arrow_Right.svg
  String get iconInactiveArrowRight =>
      'assets/images/svg/Icon_Inactive_Arrow_Right.svg';

  /// File path: assets/images/svg/Icon_Invisible.svg
  String get iconInvisible => 'assets/images/svg/Icon_Invisible.svg';

  /// File path: assets/images/svg/Icon_Link.svg
  String get iconLink => 'assets/images/svg/Icon_Link.svg';

  /// File path: assets/images/svg/Icon_Middle_Dot.svg
  String get iconMiddleDot => 'assets/images/svg/Icon_Middle_Dot.svg';

  /// File path: assets/images/svg/Icon_MyPage.svg
  String get iconMyPage => 'assets/images/svg/Icon_MyPage.svg';

  /// File path: assets/images/svg/Icon_MyPage_Variation.svg
  String get iconMyPageVariation =>
      'assets/images/svg/Icon_MyPage_Variation.svg';

  /// File path: assets/images/svg/Icon_Objects_Variation.svg
  String get iconObjectsVariation =>
      'assets/images/svg/Icon_Objects_Variation.svg';

  /// File path: assets/images/svg/Icon_Payment.svg
  String get iconPayment => 'assets/images/svg/Icon_Payment.svg';

  /// File path: assets/images/svg/Icon_Pulldown_Arrow.svg
  String get iconPulldownArrow => 'assets/images/svg/Icon_Pulldown_Arrow.svg';

  /// File path: assets/images/svg/Icon_QA.svg
  String get iconQA => 'assets/images/svg/Icon_QA.svg';

  /// File path: assets/images/svg/Icon_QrCode.svg
  String get iconQrCode => 'assets/images/svg/Icon_QrCode.svg';

  /// File path: assets/images/svg/Icon_Question.svg
  String get iconQuestion => 'assets/images/svg/Icon_Question.svg';

  /// File path: assets/images/svg/Icon_Refresh.svg
  String get iconRefresh => 'assets/images/svg/Icon_Refresh.svg';

  /// File path: assets/images/svg/Icon_Selected_Circle.svg
  String get iconSelectedCircle => 'assets/images/svg/Icon_Selected_Circle.svg';

  /// File path: assets/images/svg/Icon_Setting.svg
  String get iconSetting => 'assets/images/svg/Icon_Setting.svg';

  /// File path: assets/images/svg/Icon_Setting_Green.svg
  String get iconSettingGreen => 'assets/images/svg/Icon_Setting_Green.svg';

  /// File path: assets/images/svg/Icon_Step1.svg
  String get iconStep1 => 'assets/images/svg/Icon_Step1.svg';

  /// File path: assets/images/svg/Icon_Step2.svg
  String get iconStep2 => 'assets/images/svg/Icon_Step2.svg';

  /// File path: assets/images/svg/Icon_Task.svg
  String get iconTask => 'assets/images/svg/Icon_Task.svg';

  /// File path: assets/images/svg/Icon_Tilde.svg
  String get iconTilde => 'assets/images/svg/Icon_Tilde.svg';

  /// File path: assets/images/svg/Icon_Trash.svg
  String get iconTrash => 'assets/images/svg/Icon_Trash.svg';

  /// File path: assets/images/svg/Icon_Unlink.svg
  String get iconUnlink => 'assets/images/svg/Icon_Unlink.svg';

  /// File path: assets/images/svg/Icon_Visible.svg
  String get iconVisible => 'assets/images/svg/Icon_Visible.svg';

  /// File path: assets/images/svg/Icon_accountTotal.svg
  String get iconAccountTotal => 'assets/images/svg/Icon_accountTotal.svg';

  /// File path: assets/images/svg/Icon_action.svg
  String get iconAction => 'assets/images/svg/Icon_action.svg';

  /// File path: assets/images/svg/Icon_action_white.svg
  String get iconActionWhite => 'assets/images/svg/Icon_action_white.svg';

  /// File path: assets/images/svg/Icon_add_credit_card.svg
  String get iconAddCreditCard => 'assets/images/svg/Icon_add_credit_card.svg';

  /// File path: assets/images/svg/Icon_alert.svg
  String get iconAlert => 'assets/images/svg/Icon_alert.svg';

  /// File path: assets/images/svg/Icon_alert_home.svg
  String get iconAlertHome => 'assets/images/svg/Icon_alert_home.svg';

  /// File path: assets/images/svg/Icon_arrow_down.svg
  String get iconArrowDown => 'assets/images/svg/Icon_arrow_down.svg';

  /// File path: assets/images/svg/Icon_arrow_left.svg
  String get iconArrowLeft => 'assets/images/svg/Icon_arrow_left.svg';

  /// File path: assets/images/svg/Icon_arrow_right.svg
  String get iconArrowRight => 'assets/images/svg/Icon_arrow_right.svg';

  /// File path: assets/images/svg/Icon_arrow_up.svg
  String get iconArrowUp => 'assets/images/svg/Icon_arrow_up.svg';

  /// File path: assets/images/svg/Icon_button_arrow_down.svg
  String get iconButtonArrowDown =>
      'assets/images/svg/Icon_button_arrow_down.svg';

  /// File path: assets/images/svg/Icon_caption_list_error.svg
  String get iconCaptionListError =>
      'assets/images/svg/Icon_caption_list_error.svg';

  /// File path: assets/images/svg/Icon_close.svg
  String get iconClose => 'assets/images/svg/Icon_close.svg';

  /// File path: assets/images/svg/Icon_finished.svg
  String get iconFinished => 'assets/images/svg/Icon_finished.svg';

  /// File path: assets/images/svg/Icon_information.svg
  String get iconInformation => 'assets/images/svg/Icon_information.svg';

  /// File path: assets/images/svg/Icon_information2.svg
  String get iconInformation2 => 'assets/images/svg/Icon_information2.svg';

  /// File path: assets/images/svg/Icon_loginVaridation.svg
  String get iconLoginVaridation =>
      'assets/images/svg/Icon_loginVaridation.svg';

  /// File path: assets/images/svg/Icon_logout.svg
  String get iconLogout => 'assets/images/svg/Icon_logout.svg';

  /// File path: assets/images/svg/Icon_navigation.svg
  String get iconNavigation => 'assets/images/svg/Icon_navigation.svg';

  /// File path: assets/images/svg/Icon_navigation2.svg
  String get iconNavigation2 => 'assets/images/svg/Icon_navigation2.svg';

  /// File path: assets/images/svg/Icon_related.svg
  String get iconRelated => 'assets/images/svg/Icon_related.svg';

  /// File path: assets/images/svg/Icon_representative.svg
  String get iconRepresentative => 'assets/images/svg/Icon_representative.svg';

  /// File path: assets/images/svg/Icon_status.svg
  String get iconStatus => 'assets/images/svg/Icon_status.svg';

  /// File path: assets/images/svg/Icon_sync_alt.svg
  String get iconSyncAlt => 'assets/images/svg/Icon_sync_alt.svg';

  /// List of all assets
  List<String> get values => [
        iconAccountBalance,
        iconBalance,
        iconBeginner,
        iconBell,
        iconButtonClose,
        iconCalendarGray,
        iconCalendarGreen,
        iconCardLink,
        iconChart,
        iconCheck,
        iconCheckGray,
        iconCheckGreen,
        iconCheckGreen2,
        iconCheckRed,
        iconCircle,
        iconCreditCard,
        iconFilledArrowBottom,
        iconFilter,
        iconGreyCirccle,
        iconHelpWhite,
        iconHome,
        iconHuman,
        iconIdManagement,
        iconInactiveArrowRight,
        iconInvisible,
        iconLink,
        iconMiddleDot,
        iconMyPage,
        iconMyPageVariation,
        iconObjectsVariation,
        iconPayment,
        iconPulldownArrow,
        iconQA,
        iconQrCode,
        iconQuestion,
        iconRefresh,
        iconSelectedCircle,
        iconSetting,
        iconSettingGreen,
        iconStep1,
        iconStep2,
        iconTask,
        iconTilde,
        iconTrash,
        iconUnlink,
        iconVisible,
        iconAccountTotal,
        iconAction,
        iconActionWhite,
        iconAddCreditCard,
        iconAlert,
        iconAlertHome,
        iconArrowDown,
        iconArrowLeft,
        iconArrowRight,
        iconArrowUp,
        iconButtonArrowDown,
        iconCaptionListError,
        iconClose,
        iconFinished,
        iconInformation,
        iconInformation2,
        iconLoginVaridation,
        iconLogout,
        iconNavigation,
        iconNavigation2,
        iconRelated,
        iconRepresentative,
        iconStatus,
        iconSyncAlt
      ];
}

class Assets {
  Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const String dev = 'dev.env';
  static const String pro = 'pro.env';
  static const String stg = 'stg.env';

  /// List of all assets
  static List<String> get values => [dev, pro, stg];
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
