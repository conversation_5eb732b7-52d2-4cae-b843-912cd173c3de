/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

class FontFamily {
  FontFamily._();

  /// Font family: Noto_Sans_JP
  static const String notoSansJP = 'Noto_Sans_JP';

  /// Font family: Roboto_Condensed
  static const String robotoCondensed = 'Roboto_Condensed';
}
