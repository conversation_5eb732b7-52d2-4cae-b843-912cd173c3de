class UserCerEntity {
  const UserCerEntity({
    required this.cert,
    required this.serialNo,
    required this.exDate,
  });

  factory UserCerEntity.map(Map<String, dynamic> map) {
    return UserCerEntity(
      cert: map['cert'] as List<int>,
      serialNo: map['serialNo'] as int,
      exDate: DateTime.parse(map['exDate'] as String),
    );
  }

  final List<int> cert;
  final int serialNo;
  final DateTime exDate;
}
