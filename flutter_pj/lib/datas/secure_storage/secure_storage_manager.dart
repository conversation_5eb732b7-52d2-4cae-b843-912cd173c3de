import 'package:dtp_app/datas/shared_preferences/shared_preferences_manager.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SecureStorageManager {
  /// ここに保存したい項目を追加
  final DataAccessor<bool> isUsingBiometrics =
      _BooleanSecureData('isUsingBiometrics', defVal: false); // 生体認証を利用するか否か
  final DataAccessor<bool> isFirstLoginWithIDPW = _BooleanSecureData(
    'isFirstLoginWithIDPW',
    defVal: true,
  ); // ID/PWでの初回ログインであるか否か
  final DataAccessor<bool> isDtpIdLogin =
      _BooleanSecureData('isDtpIdLogin', defVal: true); // 保存されているID,PWの種類
  final DataAccessor<String> loginId =
      _StringSecureData('loginId'); // セキュアな環境に保存するVDID/DTPID
  final DataAccessor<String> loginPassword =
      _StringSecureData('loginPassword'); // セキュアな環境に保存するVDパスワード
  /// AESのKEY
  final DataAccessor<String> aesKey = _StringSecureData(
    'aesKey',
  );

  /// AESのIV 初期化ベクトル
  final DataAccessor<String> aesIv = _StringSecureData(
    'aesIv',
  );

  /// セキュアデータの全削除
  Future<void> deleteAllData() async {
    final storage = FlutterSecureStorage(aOptions: _getAndroidOptions());
    await storage.deleteAll();
  }
}

final secureStorageManagerProvider = Provider<SecureStorageManager>((ref) {
  return SecureStorageManager();
});

/// Bool型のセキュアなデータ
class _BooleanSecureData implements DataAccessor<bool> {
  final String _key;
  final bool defVal;
  late final FlutterSecureStorage _storage;

  _BooleanSecureData(this._key, {this.defVal = false}) {
    _storage = FlutterSecureStorage(aOptions: _getAndroidOptions());
  }

  @override
  Future<bool> load() async {
    final result = await _storage.read(key: _key);
    // read()の返り値はString?のため、もしnullの場合はdefVal（初期値）を返す。
    // nullでない場合は、最終的にStringからboolに変換して返す必要があるため、文字列比較をする。
    // もしresultが'true'という文字列であればtrueを返す。そうでなければfalseを返す。
    return result == null ? defVal : result == 'true';
  }

  @override
  Future<bool> save(value) async {
    await _storage.write(
      key: _key,
      value: value.toString(),
    );
    return true;
  }

  @override
  Future<bool> delete() async {
    await _storage.delete(key: _key);
    return true;
  }
}

/// String型のセキュアなデータ
class _StringSecureData implements DataAccessor<String> {
  final String _key;
  late final FlutterSecureStorage _storage;

  _StringSecureData(this._key) {
    _storage = FlutterSecureStorage(aOptions: _getAndroidOptions());
  }

  @override
  Future<String> load() async {
    return await _storage.read(key: _key) ?? '';
  }

  @override
  Future<bool> save(value) async {
    await _storage.write(
      key: _key,
      value: value,
    );
    return true;
  }

  @override
  Future<bool> delete() async {
    await _storage.delete(key: _key);
    return true;
  }
}

AndroidOptions _getAndroidOptions() => const AndroidOptions(
      // encryptedSharedPreferencesを利用するとクラッシュの可能性がある為、使用しない
      encryptedSharedPreferences: false,
    );
