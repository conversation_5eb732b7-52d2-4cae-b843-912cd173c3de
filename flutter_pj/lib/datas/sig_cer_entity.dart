import 'dart:typed_data';

class SigCerEntity {
  const SigCerEntity({
    required this.cert,
    required this.serialNo,
    required this.exDate,
  });

  factory SigCerEntity.map(Map<String, dynamic> map) {
    return SigCerEntity(
      cert: map['cert'] as Uint8List,
      serialNo: map['serialNo'] as String,
      exDate: map['exDate'] as String,
    );
  }

  final Uint8List cert;
  final String serialNo;
  final String exDate;
}
