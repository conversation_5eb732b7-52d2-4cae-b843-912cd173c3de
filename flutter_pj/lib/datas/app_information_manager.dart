import 'dart:io';

import 'package:dtp_app/models/app_information/app_information.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppInformationManager {
  Future<AppInformation> load() async {
    final packageInfo = await PackageInfo.fromPlatform();
    if (Platform.isIOS) {
      return AppInformation.ios(
        appName: packageInfo.appName,
        versionName: packageInfo.version,
        versionInt: int.parse(packageInfo.buildNumber),
      );
    }
    if (Platform.isAndroid) {
      return AppInformation.android(
        appName: packageInfo.appName,
        versionName: packageInfo.version,
        versionInt: int.parse(packageInfo.buildNumber),
      );
    }
    return AppInformation.undefined(
      appName: packageInfo.appName,
      versionName: packageInfo.version,
      versionInt: int.parse(packageInfo.buildNumber),
    );
  }

  Future<String> getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }
}

final appInformationManagerProvider = Provider<AppInformationManager>((ref) {
  return AppInformationManager();
});
