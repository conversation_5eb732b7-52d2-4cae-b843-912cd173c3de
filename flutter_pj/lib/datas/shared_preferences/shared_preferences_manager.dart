import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesManager {
  /// ここに保存したい項目を追加
  final DataAccessor<bool> isBalanceVisible =
      _BooleanData('isBalanceVisible', defVal: true);
  final DataAccessor<bool> isCreditCardExpenseVisible =
      _BooleanData('isCreditCardExpenseVisible', defVal: true);
  final DataAccessor<bool> isFirstTimeStartup =
      _BooleanData('isFirstTimeStartup', defVal: true); // 初回起動か否か
}

final spManagerProvider = Provider<SharedPreferencesManager>((ref) {
  return SharedPreferencesManager();
});

abstract class DataAccessor<T> {
  Future<T> load();

  Future<bool> save(T value);

  Future<bool> delete();
}

/// String型のデータ ※現在未使用のためコメントアウト
// class _StringData implements DataAccessor<String> {
//   final String _key;
//
//   _StringData(this._key);
//
//   @override
//   Future<String> load() async {
//     final prefs = await SharedPreferences.getInstance();
//     return prefs.getString(_key) ?? '';
//   }
//
//   @override
//   Future<bool> save(final String value) async {
//     final prefs = await SharedPreferences.getInstance();
//     return prefs.setString(_key, value);
//   }
//
//   @override
//   Future<bool> delete() async {
//     final prefs = await SharedPreferences.getInstance();
//     return prefs.remove(_key);
//   }
// }

/// Bool型のデータ
class _BooleanData implements DataAccessor<bool> {
  final String _key;
  final bool defVal;

  _BooleanData(this._key, {this.defVal = false});

  @override
  Future<bool> load() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_key) ?? defVal;
  }

  @override
  Future<bool> save(final bool value) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.setBool(_key, value);
  }

  @override
  Future<bool> delete() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.remove(_key);
  }
}
