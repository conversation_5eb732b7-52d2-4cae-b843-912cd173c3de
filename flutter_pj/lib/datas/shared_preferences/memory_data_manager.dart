import 'package:dtp_app/datas/shared_preferences/shared_preferences_manager.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/session/session.dart';
import 'package:dtp_app/utils/dtp_encrypter.dart';
import 'package:encrypt/encrypt.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MemoryDataManager {
  /// ここに保存したい項目の宣言を追加
  late final DataAccessor<String> sessionId;
  late final DataAccessor<EncryptedCookie> encryptedCookie;
  late final DataAccessor<Session> session;
  late final DataAccessor<String> valueDoorId;
  late final DataAccessor<String> dtpId;
  late final DataAccessor<String> vdId;
  late final DataAccessor<String> internalId;
  late final DataAccessor<String> contractType;
  late final DataAccessor<String> userType;
  late final DataAccessor<String> userAuths;
  late final DataAccessor<String> userTypeForFireBaseAnalytics;
  late final DataAccessor<String> clientId;
  late final DataAccessor<String> clientSecret;
  late final DataAccessor<String> jpkiAccessToken;
  late final DataAccessor<String> jpkiPassword;
  late final DataAccessor<String> sigDoc;
  late final DataAccessor<String> certForSig;
  late final DataAccessor<String> referenceNumber;
  late final DataAccessor<String> representativeAccountNumber;
  late final DataAccessor<String> agreeTermsUpdatedAt;

  MemoryDataManager(DtpEncrypter dtpEncrypter) {
    final memoryDataMap = <String, dynamic>{};

    // ここで初期化する
    sessionId = _StringMemoryData('sessionId', memoryDataMap, dtpEncrypter);
    encryptedCookie =
        _EncryptedCookieMemoryData('encryptedCookie', memoryDataMap);
    session = _SessionMemoryData('session', memoryDataMap);
    valueDoorId = _StringMemoryData('valueDoorId', memoryDataMap, dtpEncrypter);
    dtpId = _StringMemoryData('dtpId', memoryDataMap, dtpEncrypter);
    vdId = _StringMemoryData('vdId', memoryDataMap, dtpEncrypter);
    internalId = _StringMemoryData('internalId', memoryDataMap, dtpEncrypter);
    contractType =
        _StringMemoryData('contractType', memoryDataMap, dtpEncrypter);
    userType = _StringMemoryData('userType', memoryDataMap, dtpEncrypter);
    userAuths = _StringMemoryData('userAuths', memoryDataMap, dtpEncrypter);
    userTypeForFireBaseAnalytics = _StringMemoryData(
      'userTypeForFireBaseAnalytics',
      memoryDataMap,
      dtpEncrypter,
    );
    clientId = _StringMemoryData('clientId', memoryDataMap, dtpEncrypter);
    clientSecret =
        _StringMemoryData('clientSecret', memoryDataMap, dtpEncrypter);
    jpkiAccessToken = _StringMemoryData(
      'jpkiAccessToken',
      memoryDataMap,
      dtpEncrypter,
    ); // OAuthAPIから取得したjpkiのアクセストークン
    jpkiPassword = _StringMemoryData(
      'jpkiPassword',
      memoryDataMap,
      dtpEncrypter,
    ); // jpkiのパスワード
    sigDoc = _StringMemoryData(
      'sigDoc',
      memoryDataMap,
      dtpEncrypter,
    ); // 署名済みドキュメント(電子証明書)
    certForSig = _StringMemoryData(
      'certForSig',
      memoryDataMap,
      dtpEncrypter,
    ); // 署名用電子証明書
    referenceNumber = _StringMemoryData(
      'referenceNumber',
      memoryDataMap,
      dtpEncrypter,
    ); // お手続き番号
    representativeAccountNumber = _StringMemoryData(
      'representativeAccountNumber',
      memoryDataMap,
      dtpEncrypter,
    ); // 勘定店番号
    agreeTermsUpdatedAt = _StringMemoryData(
      'representativeAccountNumber',
      memoryDataMap,
      dtpEncrypter,
    ); // 利用規定同意日
  }
}

final mdManagerProvider = Provider<MemoryDataManager>((ref) {
  final dtpEncrypter = ref.watch(encrypterProvider);
  return MemoryDataManager(dtpEncrypter);
});

/// String型のメモリデータ（アプリ終了後に破棄される）
class _StringMemoryData implements DataAccessor<String> {
  final String _key;
  final Map<String, dynamic> _map;
  final DtpEncrypter encrypter;

  _StringMemoryData(this._key, this._map, this.encrypter);

  @override
  Future<String> load() async {
    final value = _map[_key];
    if (value is Encrypted) {
      // 複合化
      final decrypt = await encrypter.decrypt(value);
      return decrypt;
    } else {
      return '';
    }
  }

  @override
  Future<bool> save(final String value) async {
    // 暗号化
    if (value.isEmpty) {
      // 空文字の場合は暗号化不要
      _map[_key] = value;
      return true;
    }

    final encrypted = await encrypter.encrypt(value);
    _map[_key] = encrypted;
    return true;
  }

  @override
  Future<bool> delete() async {
    _map[_key] = null;
    return true;
  }
}

/// Session型のメモリデータ（アプリ終了後に破棄される）
class _SessionMemoryData implements DataAccessor<Session> {
  final String _key;
  final Map<String, dynamic> _map;

  _SessionMemoryData(this._key, this._map);

  @override
  Future<Session> load() async {
    final value = _map[_key];
    // 値が存在しない場合はSessionNoneを返す
    return value is Session ? value : const SessionNone();
  }

  @override
  Future<bool> save(final Session value) async {
    _map[_key] = value;
    return true;
  }

  @override
  Future<bool> delete() async {
    _map[_key] = null;
    return true;
  }
}

/// Session型のメモリデータ（アプリ終了後に破棄される）
class _EncryptedCookieMemoryData implements DataAccessor<EncryptedCookie> {
  final String _key;
  final Map<String, dynamic> _map;

  _EncryptedCookieMemoryData(this._key, this._map);

  @override
  Future<EncryptedCookie> load() async {
    final value = _map[_key];
    // 値が存在しない場合はEncryptedCookieEmptyを返す
    return value is EncryptedCookie ? value : const EncryptedCookie.empty();
  }

  @override
  Future<bool> save(final EncryptedCookie value) async {
    _map[_key] = value;
    return true;
  }

  @override
  Future<bool> delete() async {
    _map[_key] = null;
    return true;
  }
}
