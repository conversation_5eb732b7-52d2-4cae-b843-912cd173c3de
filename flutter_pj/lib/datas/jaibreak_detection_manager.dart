import 'dart:async';
import 'dart:io';
import 'package:dtp_app/business_logics/jailbreak_detection/jailbreak_detection_screen_notifier.dart';
import 'package:flutter/services.dart';
import 'package:flutter_jailbreak_detection/flutter_jailbreak_detection.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JailbreakDetectionManager {
  JailbreakDetectionManager();

  Future<JailbreakType> initPlatformState() async {
    try {
      final jailbroken = await FlutterJailbreakDetection.jailbroken;

      if (jailbroken && Platform.isIOS) {
        return JailbreakType.jailbreak;
      }
      if (jailbroken && Platform.isAndroid) {
        return JailbreakType.root;
      }
      return JailbreakType.none;
    } on PlatformException {
      return JailbreakType.jailbreak;
    }
  }
}

final jailbreakDetectionManagerProvider = Provider<JailbreakDetectionManager>(
  (ref) => JailbreakDetectionManager(),
);
