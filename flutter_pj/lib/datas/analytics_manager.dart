import 'package:dtp_app/utils/ext/string_ext.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final analyticsManagerProvider = Provider<AnalyticsManager>(
  (ref) => AnalyticsManager(),
);

class AnalyticsManager {
  AnalyticsManager();

  // Firebase初期化をスキップし、固定値を返す
  // final firebaseAnalytics = FirebaseAnalytics.instance;

  /// 画面名ログ送信メソッド
  Future<void> sendScreenLog({
    required String screenNumber,
    required String screenName,
  }) async {
    // Firebase初期化をスキップし、固定値を返す
    // await firebaseAnalytics.logEvent(
    //   name: 'screen_name',
    //   parameters: {
    //     'screen_name': '${screenNumber}_$screenName',
    //   },
    // );
    print('Analytics screen log skipped: ${screenNumber}_$screenName');
  }

  /// ボタン名ログ送信メソッド
  Future<void> sendButtonLog({
    required String buttonName,
    required String screenName,
  }) async {
    // Firebase初期化をスキップし、固定値を返す
    // await firebaseAnalytics.logEvent(
    //   name: 'button_name',
    //   parameters: {
    //     'button_name': '${buttonName}_$screenName',
    //   },
    // );
    print('Analytics button log skipped: ${buttonName}_$screenName');
  }

  /// ユーザプロパティログ送信メソッド
  Future<void> sendUserProperty({
    required String vdId,
    required String getsAuthority,
    required String otherBanksLinkage,
    required String otherCompanyCardsLinkage,
    required String dtpId,
    required String internalId,
    required String contractType,
    required String userAuths,
    required String userType,
    required String referenceNumber,
  }) async {
    // Firebase初期化をスキップし、固定値を返す
    print('Analytics user property log skipped');
    // await firebaseAnalytics.setUserProperty(
    //   name: 'vdid',
    //   value: vdId.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'gets_authority',
    //   value: getsAuthority.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'otherbanks_linkage',
    //   value: otherBanksLinkage.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'othercompanycard_linkage',
    //   value: otherCompanyCardsLinkage.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'dtpid',
    //   value: dtpId.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'internal_id',
    //   value: internalId.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'contract_type',
    //   value: contractType.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'user_auths',
    //   value: userAuths.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'user_type',
    //   value: userType.convertToHyphen,
    // );
    // await firebaseAnalytics.setUserProperty(
    //   name: 'reference_number',
    //   value: referenceNumber.convertToHyphen,
    // );
  }
}
