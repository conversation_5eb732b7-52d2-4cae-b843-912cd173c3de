import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:local_auth/local_auth.dart';

class BiometricManager {
  BiometricManager({
    required this.localAuth,
  });

  final LocalAuthentication localAuth;

  // 端末に利用可能な生体認証が搭載されているかどうかを確認する (端末に生体認証機能がない場合)
  Future<bool> get canCheckBiometrics async =>
      await localAuth.canCheckBiometrics && await localAuth.isDeviceSupported();

  // 生体認証実施
  Future<AppResult<bool>> didAuthenticate() async {
    try {
      bool result = false;

      result = await localAuth.authenticate(
        localizedReason: '生体認証を使用してアクセスします。',
        options: const AuthenticationOptions(
          // 生体認証を登録していない端末上で生体認証を利用しようとした際
          // 独自で設定したエラーダイアログを表示するため、falseに変更。
          useErrorDialogs: false,
          stickyAuth: true,
          // Android10で生体認証利用時にPIN入力するとバグが発生してしまうためPINの使用を禁止する。
          biometricOnly: true,
        ),
      );

      return AppResult.success(result);
    } on PlatformException catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final biometricProvider = Provider<BiometricManager>(
  (ref) => BiometricManager(localAuth: ref.read(localAuthenticationProvider)),
);

final localAuthenticationProvider = Provider<LocalAuthentication>(
  (ref) => LocalAuthentication(),
);
