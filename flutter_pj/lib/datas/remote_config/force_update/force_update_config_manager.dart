import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/datas/remote_config/force_update/response/force_update_config_response.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 強制アップデートの有無を判定するクラス
/// サーバにデプロイされているJSONファイルを読みに行く
class ForceUpdateConfigManager {
  ForceUpdateConfigManager({
    required this.apiClient,
    required this.iosJsonUrl,
    required this.androidJsonUrl,
  });

  final ApiClient apiClient;
  final String iosJsonUrl;
  final String androidJsonUrl;

  // iOS用
  Future<ForceUpdateConfigResponse> loadIOS() {
    return _load(url: iosJsonUrl);
  }

  // Android用
  Future<ForceUpdateConfigResponse> loadAndroid() {
    return _load(url: androidJsonUrl);
  }

  Future<ForceUpdateConfigResponse> _load({required String url}) async {
    try {
      final response = await apiClient.get(url: url);
      final json = parseJsonMap(utf8.decode(response.body.runes.toList()));
      return ForceUpdateConfigResponse.fromJson(json);
    } catch (e) {
      rethrow;
    }
  }
}

final forceUpdateConfigProvider = Provider<ForceUpdateConfigManager>((ref) {
  final bc = ref.watch(buildConfigProvider);
  return ForceUpdateConfigManager(
    apiClient: ref.watch(apiClientProvider),
    iosJsonUrl: bc.iosForceUpdateJsonUrl,
    androidJsonUrl: bc.androidForceUpdateJsonUrl,
  );
});
