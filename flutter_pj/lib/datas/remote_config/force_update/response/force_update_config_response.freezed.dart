// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'force_update_config_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ForceUpdateConfigResponse _$ForceUpdateConfigResponseFromJson(
    Map<String, dynamic> json) {
  return _ForceUpdateConfigResponse.fromJson(json);
}

/// @nodoc
mixin _$ForceUpdateConfigResponse {
  UpdateAlert get updateAlert => throw _privateConstructorUsedError;

  /// Serializes this ForceUpdateConfigResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ForceUpdateConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ForceUpdateConfigResponseCopyWith<ForceUpdateConfigResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForceUpdateConfigResponseCopyWith<$Res> {
  factory $ForceUpdateConfigResponseCopyWith(ForceUpdateConfigResponse value,
          $Res Function(ForceUpdateConfigResponse) then) =
      _$ForceUpdateConfigResponseCopyWithImpl<$Res, ForceUpdateConfigResponse>;
  @useResult
  $Res call({UpdateAlert updateAlert});

  $UpdateAlertCopyWith<$Res> get updateAlert;
}

/// @nodoc
class _$ForceUpdateConfigResponseCopyWithImpl<$Res,
        $Val extends ForceUpdateConfigResponse>
    implements $ForceUpdateConfigResponseCopyWith<$Res> {
  _$ForceUpdateConfigResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ForceUpdateConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updateAlert = null,
  }) {
    return _then(_value.copyWith(
      updateAlert: null == updateAlert
          ? _value.updateAlert
          : updateAlert // ignore: cast_nullable_to_non_nullable
              as UpdateAlert,
    ) as $Val);
  }

  /// Create a copy of ForceUpdateConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UpdateAlertCopyWith<$Res> get updateAlert {
    return $UpdateAlertCopyWith<$Res>(_value.updateAlert, (value) {
      return _then(_value.copyWith(updateAlert: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ForceUpdateConfigResponseImplCopyWith<$Res>
    implements $ForceUpdateConfigResponseCopyWith<$Res> {
  factory _$$ForceUpdateConfigResponseImplCopyWith(
          _$ForceUpdateConfigResponseImpl value,
          $Res Function(_$ForceUpdateConfigResponseImpl) then) =
      __$$ForceUpdateConfigResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({UpdateAlert updateAlert});

  @override
  $UpdateAlertCopyWith<$Res> get updateAlert;
}

/// @nodoc
class __$$ForceUpdateConfigResponseImplCopyWithImpl<$Res>
    extends _$ForceUpdateConfigResponseCopyWithImpl<$Res,
        _$ForceUpdateConfigResponseImpl>
    implements _$$ForceUpdateConfigResponseImplCopyWith<$Res> {
  __$$ForceUpdateConfigResponseImplCopyWithImpl(
      _$ForceUpdateConfigResponseImpl _value,
      $Res Function(_$ForceUpdateConfigResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of ForceUpdateConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? updateAlert = null,
  }) {
    return _then(_$ForceUpdateConfigResponseImpl(
      updateAlert: null == updateAlert
          ? _value.updateAlert
          : updateAlert // ignore: cast_nullable_to_non_nullable
              as UpdateAlert,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ForceUpdateConfigResponseImpl implements _ForceUpdateConfigResponse {
  const _$ForceUpdateConfigResponseImpl({required this.updateAlert});

  factory _$ForceUpdateConfigResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$ForceUpdateConfigResponseImplFromJson(json);

  @override
  final UpdateAlert updateAlert;

  @override
  String toString() {
    return 'ForceUpdateConfigResponse(updateAlert: $updateAlert)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForceUpdateConfigResponseImpl &&
            (identical(other.updateAlert, updateAlert) ||
                other.updateAlert == updateAlert));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, updateAlert);

  /// Create a copy of ForceUpdateConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForceUpdateConfigResponseImplCopyWith<_$ForceUpdateConfigResponseImpl>
      get copyWith => __$$ForceUpdateConfigResponseImplCopyWithImpl<
          _$ForceUpdateConfigResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ForceUpdateConfigResponseImplToJson(
      this,
    );
  }
}

abstract class _ForceUpdateConfigResponse implements ForceUpdateConfigResponse {
  const factory _ForceUpdateConfigResponse(
          {required final UpdateAlert updateAlert}) =
      _$ForceUpdateConfigResponseImpl;

  factory _ForceUpdateConfigResponse.fromJson(Map<String, dynamic> json) =
      _$ForceUpdateConfigResponseImpl.fromJson;

  @override
  UpdateAlert get updateAlert;

  /// Create a copy of ForceUpdateConfigResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForceUpdateConfigResponseImplCopyWith<_$ForceUpdateConfigResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}

UpdateAlert _$UpdateAlertFromJson(Map<String, dynamic> json) {
  return _UpdateAlert.fromJson(json);
}

/// @nodoc
mixin _$UpdateAlert {
  Force? get force => throw _privateConstructorUsedError;
  Optional? get optional => throw _privateConstructorUsedError;

  /// Serializes this UpdateAlert to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateAlertCopyWith<UpdateAlert> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateAlertCopyWith<$Res> {
  factory $UpdateAlertCopyWith(
          UpdateAlert value, $Res Function(UpdateAlert) then) =
      _$UpdateAlertCopyWithImpl<$Res, UpdateAlert>;
  @useResult
  $Res call({Force? force, Optional? optional});

  $ForceCopyWith<$Res>? get force;
  $OptionalCopyWith<$Res>? get optional;
}

/// @nodoc
class _$UpdateAlertCopyWithImpl<$Res, $Val extends UpdateAlert>
    implements $UpdateAlertCopyWith<$Res> {
  _$UpdateAlertCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? force = freezed,
    Object? optional = freezed,
  }) {
    return _then(_value.copyWith(
      force: freezed == force
          ? _value.force
          : force // ignore: cast_nullable_to_non_nullable
              as Force?,
      optional: freezed == optional
          ? _value.optional
          : optional // ignore: cast_nullable_to_non_nullable
              as Optional?,
    ) as $Val);
  }

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ForceCopyWith<$Res>? get force {
    if (_value.force == null) {
      return null;
    }

    return $ForceCopyWith<$Res>(_value.force!, (value) {
      return _then(_value.copyWith(force: value) as $Val);
    });
  }

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OptionalCopyWith<$Res>? get optional {
    if (_value.optional == null) {
      return null;
    }

    return $OptionalCopyWith<$Res>(_value.optional!, (value) {
      return _then(_value.copyWith(optional: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UpdateAlertImplCopyWith<$Res>
    implements $UpdateAlertCopyWith<$Res> {
  factory _$$UpdateAlertImplCopyWith(
          _$UpdateAlertImpl value, $Res Function(_$UpdateAlertImpl) then) =
      __$$UpdateAlertImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Force? force, Optional? optional});

  @override
  $ForceCopyWith<$Res>? get force;
  @override
  $OptionalCopyWith<$Res>? get optional;
}

/// @nodoc
class __$$UpdateAlertImplCopyWithImpl<$Res>
    extends _$UpdateAlertCopyWithImpl<$Res, _$UpdateAlertImpl>
    implements _$$UpdateAlertImplCopyWith<$Res> {
  __$$UpdateAlertImplCopyWithImpl(
      _$UpdateAlertImpl _value, $Res Function(_$UpdateAlertImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? force = freezed,
    Object? optional = freezed,
  }) {
    return _then(_$UpdateAlertImpl(
      force: freezed == force
          ? _value.force
          : force // ignore: cast_nullable_to_non_nullable
              as Force?,
      optional: freezed == optional
          ? _value.optional
          : optional // ignore: cast_nullable_to_non_nullable
              as Optional?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UpdateAlertImpl implements _UpdateAlert {
  const _$UpdateAlertImpl({required this.force, required this.optional});

  factory _$UpdateAlertImpl.fromJson(Map<String, dynamic> json) =>
      _$$UpdateAlertImplFromJson(json);

  @override
  final Force? force;
  @override
  final Optional? optional;

  @override
  String toString() {
    return 'UpdateAlert(force: $force, optional: $optional)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAlertImpl &&
            (identical(other.force, force) || other.force == force) &&
            (identical(other.optional, optional) ||
                other.optional == optional));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, force, optional);

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAlertImplCopyWith<_$UpdateAlertImpl> get copyWith =>
      __$$UpdateAlertImplCopyWithImpl<_$UpdateAlertImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateAlertImplToJson(
      this,
    );
  }
}

abstract class _UpdateAlert implements UpdateAlert {
  const factory _UpdateAlert(
      {required final Force? force,
      required final Optional? optional}) = _$UpdateAlertImpl;

  factory _UpdateAlert.fromJson(Map<String, dynamic> json) =
      _$UpdateAlertImpl.fromJson;

  @override
  Force? get force;
  @override
  Optional? get optional;

  /// Create a copy of UpdateAlert
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAlertImplCopyWith<_$UpdateAlertImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Force _$ForceFromJson(Map<String, dynamic> json) {
  return _Force.fromJson(json);
}

/// @nodoc
mixin _$Force {
  String? get minVersion => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String get buttonLink => throw _privateConstructorUsedError;

  /// Serializes this Force to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Force
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ForceCopyWith<Force> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ForceCopyWith<$Res> {
  factory $ForceCopyWith(Force value, $Res Function(Force) then) =
      _$ForceCopyWithImpl<$Res, Force>;
  @useResult
  $Res call({String? minVersion, String? message, String buttonLink});
}

/// @nodoc
class _$ForceCopyWithImpl<$Res, $Val extends Force>
    implements $ForceCopyWith<$Res> {
  _$ForceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Force
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minVersion = freezed,
    Object? message = freezed,
    Object? buttonLink = null,
  }) {
    return _then(_value.copyWith(
      minVersion: freezed == minVersion
          ? _value.minVersion
          : minVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonLink: null == buttonLink
          ? _value.buttonLink
          : buttonLink // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ForceImplCopyWith<$Res> implements $ForceCopyWith<$Res> {
  factory _$$ForceImplCopyWith(
          _$ForceImpl value, $Res Function(_$ForceImpl) then) =
      __$$ForceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? minVersion, String? message, String buttonLink});
}

/// @nodoc
class __$$ForceImplCopyWithImpl<$Res>
    extends _$ForceCopyWithImpl<$Res, _$ForceImpl>
    implements _$$ForceImplCopyWith<$Res> {
  __$$ForceImplCopyWithImpl(
      _$ForceImpl _value, $Res Function(_$ForceImpl) _then)
      : super(_value, _then);

  /// Create a copy of Force
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? minVersion = freezed,
    Object? message = freezed,
    Object? buttonLink = null,
  }) {
    return _then(_$ForceImpl(
      minVersion: freezed == minVersion
          ? _value.minVersion
          : minVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonLink: null == buttonLink
          ? _value.buttonLink
          : buttonLink // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ForceImpl implements _Force {
  const _$ForceImpl(
      {required this.minVersion,
      required this.message,
      required this.buttonLink});

  factory _$ForceImpl.fromJson(Map<String, dynamic> json) =>
      _$$ForceImplFromJson(json);

  @override
  final String? minVersion;
  @override
  final String? message;
  @override
  final String buttonLink;

  @override
  String toString() {
    return 'Force(minVersion: $minVersion, message: $message, buttonLink: $buttonLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ForceImpl &&
            (identical(other.minVersion, minVersion) ||
                other.minVersion == minVersion) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.buttonLink, buttonLink) ||
                other.buttonLink == buttonLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, minVersion, message, buttonLink);

  /// Create a copy of Force
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ForceImplCopyWith<_$ForceImpl> get copyWith =>
      __$$ForceImplCopyWithImpl<_$ForceImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ForceImplToJson(
      this,
    );
  }
}

abstract class _Force implements Force {
  const factory _Force(
      {required final String? minVersion,
      required final String? message,
      required final String buttonLink}) = _$ForceImpl;

  factory _Force.fromJson(Map<String, dynamic> json) = _$ForceImpl.fromJson;

  @override
  String? get minVersion;
  @override
  String? get message;
  @override
  String get buttonLink;

  /// Create a copy of Force
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ForceImplCopyWith<_$ForceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Optional _$OptionalFromJson(Map<String, dynamic> json) {
  return _Optional.fromJson(json);
}

/// @nodoc
mixin _$Optional {
  String? get newVersion => throw _privateConstructorUsedError;
  String? get message => throw _privateConstructorUsedError;
  String get buttonLink => throw _privateConstructorUsedError;

  /// Serializes this Optional to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Optional
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OptionalCopyWith<Optional> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OptionalCopyWith<$Res> {
  factory $OptionalCopyWith(Optional value, $Res Function(Optional) then) =
      _$OptionalCopyWithImpl<$Res, Optional>;
  @useResult
  $Res call({String? newVersion, String? message, String buttonLink});
}

/// @nodoc
class _$OptionalCopyWithImpl<$Res, $Val extends Optional>
    implements $OptionalCopyWith<$Res> {
  _$OptionalCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Optional
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newVersion = freezed,
    Object? message = freezed,
    Object? buttonLink = null,
  }) {
    return _then(_value.copyWith(
      newVersion: freezed == newVersion
          ? _value.newVersion
          : newVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonLink: null == buttonLink
          ? _value.buttonLink
          : buttonLink // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OptionalImplCopyWith<$Res>
    implements $OptionalCopyWith<$Res> {
  factory _$$OptionalImplCopyWith(
          _$OptionalImpl value, $Res Function(_$OptionalImpl) then) =
      __$$OptionalImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? newVersion, String? message, String buttonLink});
}

/// @nodoc
class __$$OptionalImplCopyWithImpl<$Res>
    extends _$OptionalCopyWithImpl<$Res, _$OptionalImpl>
    implements _$$OptionalImplCopyWith<$Res> {
  __$$OptionalImplCopyWithImpl(
      _$OptionalImpl _value, $Res Function(_$OptionalImpl) _then)
      : super(_value, _then);

  /// Create a copy of Optional
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newVersion = freezed,
    Object? message = freezed,
    Object? buttonLink = null,
  }) {
    return _then(_$OptionalImpl(
      newVersion: freezed == newVersion
          ? _value.newVersion
          : newVersion // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      buttonLink: null == buttonLink
          ? _value.buttonLink
          : buttonLink // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OptionalImpl implements _Optional {
  const _$OptionalImpl(
      {required this.newVersion,
      required this.message,
      required this.buttonLink});

  factory _$OptionalImpl.fromJson(Map<String, dynamic> json) =>
      _$$OptionalImplFromJson(json);

  @override
  final String? newVersion;
  @override
  final String? message;
  @override
  final String buttonLink;

  @override
  String toString() {
    return 'Optional(newVersion: $newVersion, message: $message, buttonLink: $buttonLink)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OptionalImpl &&
            (identical(other.newVersion, newVersion) ||
                other.newVersion == newVersion) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.buttonLink, buttonLink) ||
                other.buttonLink == buttonLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, newVersion, message, buttonLink);

  /// Create a copy of Optional
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OptionalImplCopyWith<_$OptionalImpl> get copyWith =>
      __$$OptionalImplCopyWithImpl<_$OptionalImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OptionalImplToJson(
      this,
    );
  }
}

abstract class _Optional implements Optional {
  const factory _Optional(
      {required final String? newVersion,
      required final String? message,
      required final String buttonLink}) = _$OptionalImpl;

  factory _Optional.fromJson(Map<String, dynamic> json) =
      _$OptionalImpl.fromJson;

  @override
  String? get newVersion;
  @override
  String? get message;
  @override
  String get buttonLink;

  /// Create a copy of Optional
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OptionalImplCopyWith<_$OptionalImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
