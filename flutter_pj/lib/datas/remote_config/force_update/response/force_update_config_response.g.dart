// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'force_update_config_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ForceUpdateConfigResponseImpl _$$ForceUpdateConfigResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$ForceUpdateConfigResponseImpl(
      updateAlert:
          UpdateAlert.fromJson(json['updateAlert'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ForceUpdateConfigResponseImplToJson(
        _$ForceUpdateConfigResponseImpl instance) =>
    <String, dynamic>{
      'updateAlert': instance.updateAlert,
    };

_$UpdateAlertImpl _$$UpdateAlertImplFromJson(Map<String, dynamic> json) =>
    _$UpdateAlertImpl(
      force: json['force'] == null
          ? null
          : Force.fromJson(json['force'] as Map<String, dynamic>),
      optional: json['optional'] == null
          ? null
          : Optional.fromJson(json['optional'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$UpdateAlertImplToJson(_$UpdateAlertImpl instance) =>
    <String, dynamic>{
      'force': instance.force,
      'optional': instance.optional,
    };

_$ForceImpl _$$ForceImplFromJson(Map<String, dynamic> json) => _$ForceImpl(
      minVersion: json['minVersion'] as String?,
      message: json['message'] as String?,
      buttonLink: json['buttonLink'] as String,
    );

Map<String, dynamic> _$$ForceImplToJson(_$ForceImpl instance) =>
    <String, dynamic>{
      'minVersion': instance.minVersion,
      'message': instance.message,
      'buttonLink': instance.buttonLink,
    };

_$OptionalImpl _$$OptionalImplFromJson(Map<String, dynamic> json) =>
    _$OptionalImpl(
      newVersion: json['newVersion'] as String?,
      message: json['message'] as String?,
      buttonLink: json['buttonLink'] as String,
    );

Map<String, dynamic> _$$OptionalImplToJson(_$OptionalImpl instance) =>
    <String, dynamic>{
      'newVersion': instance.newVersion,
      'message': instance.message,
      'buttonLink': instance.buttonLink,
    };
