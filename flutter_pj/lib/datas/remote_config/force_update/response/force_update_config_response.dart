import 'package:freezed_annotation/freezed_annotation.dart';

part 'force_update_config_response.freezed.dart';
part 'force_update_config_response.g.dart';

@freezed
class ForceUpdateConfigResponse with _$ForceUpdateConfigResponse {
  const factory ForceUpdateConfigResponse({
    required UpdateAlert updateAlert,
  }) = _ForceUpdateConfigResponse;

  factory ForceUpdateConfigResponse.fromJson(Map<String, dynamic> json) =>
      _$ForceUpdateConfigResponseFromJson(json);
}

@freezed
class UpdateAlert with _$UpdateAlert {
  const factory UpdateAlert({
    required Force? force,
    required Optional? optional,
  }) = _UpdateAlert;

  factory UpdateAlert.fromJson(Map<String, dynamic> json) =>
      _$UpdateAlertFromJson(json);
}

@freezed
class Force with _$Force {
  const factory Force({
    required String? minVersion,
    required String? message,
    required String buttonLink,
  }) = _Force;

  factory Force.fromJson(Map<String, dynamic> json) => _$Force<PERSON>son(json);
}

@freezed
class Optional with _$Optional {
  const factory Optional({
    required String? newVersion,
    required String? message,
    required String buttonLink,
  }) = _Optional;

  factory Optional.fromJson(Map<String, dynamic> json) =>
      _$OptionalFromJson(json);
}
