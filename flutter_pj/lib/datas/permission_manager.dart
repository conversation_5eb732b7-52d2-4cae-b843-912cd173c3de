import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final permissionManagerProvider = Provider<PermissionManager>(
  (ref) => PermissionManager(),
);

class PermissionManager {
  PermissionManager();

  Future<TrackingStatus> requestTrackingAuthorization() async {
    return await AppTrackingTransparency.requestTrackingAuthorization();
  }

  Future<TrackingStatus> getTrackingAuthorizationStatus() async {
    return await AppTrackingTransparency.trackingAuthorizationStatus;
  }
}
