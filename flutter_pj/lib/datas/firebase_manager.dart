// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

final firebaseManagerProvider = Provider<FirebaseManager>(
  (ref) => FirebaseManager(),
);

class FirebaseManager {
  FirebaseManager();

  // Firebase初期化をスキップし、固定値を返す
  // final messaging = FirebaseMessaging.instance;

  Future<void> requestMessagingPermission() async {
    // Firebase初期化をスキップし、固定値を返す
    // await messaging.requestPermission();
    print('Firebase messaging permission request skipped.');
  }
}
