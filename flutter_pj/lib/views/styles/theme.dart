import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';

/// アプリ全体のテーマ設定
final theme = ThemeData(
  // デザインの自動変更を防ぐためのオプション
  useMaterial3: false,
  colorScheme: const ColorScheme.light(
    primary: AppColors.primary,
    surface: AppColors.basicBackgroundLightGray,
  ),
  fontFamily: FontFamily.notoSansJP,
  textTheme: const TextTheme(
    bodySmall: TextStyle(
      fontFamily: FontFamily.notoSansJP,
      fontSize: 14,
      color: AppColors.textBlack,
      fontWeight: FontWeight.w400,
    ),
    bodyMedium: TextStyle(
      fontFamily: FontFamily.notoSansJP,
      fontSize: 16,
      color: AppColors.textBlack,
      fontWeight: FontWeight.w400,
    ),
    bodyLarge: TextStyle(
      fontFamily: FontFamily.notoSansJP,
      fontSize: 18,
      color: AppColors.textBlack,
      fontWeight: FontWeight.w700,
    ),
    titleLarge: TextStyle(
      fontFamily: FontFamily.notoSansJP,
      fontSize: 24,
      color: AppColors.textBlack,
      fontWeight: FontWeight.w700,
    ),
  ),
);
