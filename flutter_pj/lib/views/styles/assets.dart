/// アプリ内で使用する画像パスの定義
class Assets {
  const Assets._();

  static const _basePath = 'assets/images';

  static const _pngPath = '$_basePath/png';
  static const _svgPath = '$_basePath/svg';

//アプリヘッダー
  static const bellIcon = '$_svgPath/Icon_Bell.svg';
  static const qaIcon = '$_svgPath/Icon_QA.svg';
  static const helpWhiteIcon = '$_svgPath/Icon_Help_White.svg';

//アプリフッター
  static const homeIcon = '$_svgPath/Icon_Home.svg';
  static const balanceIcon = '$_svgPath/Icon_Balance.svg';
  static const paymentIcon = '$_svgPath/Icon_Payment.svg';
  static const myPageIcon = '$_svgPath/Icon_MyPage.svg';

// ログイン前
  static const bgWaveBeforeLogin = '$_pngPath/bg_wave_before_login.png';
  static const cardLinkIcon = '$_svgPath/Icon_Card_Link.svg';
  static const relatedIcon = '$_svgPath/Icon_related.svg';
  static const relatedInformation01 = '$_pngPath/related_information_01.png';
  static const relatedInformation02 = '$_pngPath/related_information_02.png';
  static const relatedInformation03 = '$_pngPath/related_information_03.png';
  static const relatedInformation04 = '$_pngPath/related_information_04.png';
  static const relatedInformation05 = '$_pngPath/related_information_05.png';
  static const bgWaveGreyHalf = '$_pngPath/bg_wave_grey_half.png';
  static const smbcBusinessBackground =
      '$_pngPath/smbc_business_background.png';
  static const smbcBusinessCard = '$_pngPath/smbc_business_card.png';

// ログイン
  static const visibleIcon = '$_svgPath/Icon_Visible.svg';
  static const invisibleIcon = '$_svgPath/Icon_Invisible.svg';
  static const loginFooter = '$_pngPath/login_footer.png';

// ホーム
  static const bgWaveHome = '$_pngPath/bg_wave_home.png';
  static const accountBalanceIcon = '$_svgPath/Icon_Account_Balance.svg';
  static const settingIcon = '$_svgPath/Icon_Setting.svg';
  static const refreshIcon = '$_svgPath/Icon_Refresh.svg';
  static const tradGreenCard = '$_pngPath/tradgreen_card.png';
  static const whiteCard = '$_pngPath/white_card.png';
  static const whiteCardBar = '$_pngPath/white_card_bar.png';
  static const smbcLogo = '$_pngPath/smbc_logo_tag.png';
  static const plariTown = '$_pngPath/plari_town.png';
  static const incomeAndExpenditureChart = '$_svgPath/Icon_Chart.svg';
  static const creditCardIcon = '$_svgPath/Icon_Credit_Card.svg';
  static const creditCardRectangle = '$_pngPath/credit_card_rectangle.png';
  static const creditSettingCardRectangle =
      '$_pngPath/credit_setting_card_rectangle.png';
  static const addCreditCardIcon = '$_svgPath/Icon_add_credit_card.svg';
  static const addFreeeLinkIcon = '$_svgPath/Icon_Link.svg';
  static const navigationIcon2 = '$_svgPath/Icon_navigation2.svg';
  static const addCreditCard = '$_pngPath/add_credit_card.png';
  static const freeeLinkCard = '$_pngPath/freee_link_card.png';
  static const addCreditCardFrame = '$_pngPath/add_credit_card_frame.png';
  static const addOtherBankCardFrame =
      '$_pngPath/add_other_bank_card_frame.png';
  static const freeeLinkageCompletion =
      '$_pngPath/freee_linkage_completion.png';
  static const freeeLinkageFlowImage = '$_pngPath/freee_linkage_flow_image.png';
  static const freeeLinkageFlowImageEmployee =
      '$_pngPath/freee_linkage_flow_image_employee.png';
  static const step1Icon = '$_svgPath/Icon_Step1.svg';
  static const step2Icon = '$_svgPath/Icon_Step2.svg';
  static const beginnerIcon = '$_svgPath/Icon_Beginner.svg';
  static const humanIcon = '$_svgPath/Icon_Human.svg';

// 口座表示設定
  static const checkRedIcon = '$_svgPath/Icon_Check_Red.svg';
  static const navigationIcon = '$_svgPath/Icon_navigation.svg';
  static const freeeConnect = '$_pngPath/freee_connect.png';
  static const freeeConnectBackground =
      '$_pngPath/freee_connect_background.png';

//口座照会
  static const accountTotalIcon = '$_svgPath/Icon_accountTotal.svg';
  static const informationIcon2 = '$_svgPath/Icon_information2.svg';
  static const smbcLogoTagS = '$_pngPath/smbc_logo_tagS.png';
  static const actionIconWhite = '$_svgPath/Icon_action_white.svg';

//入出金明細
  static const calendarGrayIcon = '$_svgPath/Icon_Calendar_Gray.svg';
  static const calendarGreenIcon = '$_svgPath/Icon_Calendar_Green.svg';
  static const arrowUpIcon = '$_svgPath/Icon_arrow_up.svg';
  static const arrowDownIcon = '$_svgPath/Icon_arrow_down.svg';
  static const pulldownArrowIcon = '$_svgPath/Icon_Pulldown_Arrow.svg';
  static const filledArrowBottomIcon = '$_svgPath/Icon_Filled_Arrow_Bottom.svg';
  static const arrowDownBottomIcon = '$_svgPath/Icon_button_arrow_down.svg';
  static const arrowLeftIcon = '$_svgPath/Icon_arrow_left.svg';
  static const tildeIcon = '$_svgPath/Icon_Tilde.svg';
  static const filterIcon = '$_svgPath/Icon_Filter.svg';
  static const checkGrayIcon = '$_svgPath/Icon_Check_Gray.svg';
  static const checkGreenIcon = '$_svgPath/Icon_Check_Green.svg';
  static const circleIcon = '$_svgPath/Icon_Circle.svg';
  static const selectedCircleIcon = '$_svgPath/Icon_Selected_Circle.svg';
  static const captionListErrorIcon = '$_svgPath/Icon_caption_list_error.svg';

//利用明細
  static const syncAltIcon = '$_svgPath/Icon_sync_alt.svg';

//マイページ
  static const actionIcon = '$_svgPath/Icon_action.svg';
  static const informationIcon = '$_svgPath/Icon_information.svg';
  static const objectsVariationIcon = '$_svgPath/Icon_Objects_Variation.svg';
  static const myPageVariationIcon = '$_svgPath/Icon_MyPage_Variation.svg';
  static const logoutIcon = '$_svgPath/Icon_logout.svg';
  static const representativeIcon = '$_svgPath/Icon_representative.svg';
  static const idManagementIcon = '$_svgPath/Icon_Id_Management.svg';
  static const arrowRightIcon = '$_svgPath/Icon_arrow_right.svg';
  static const inactiveArrowRightIcon =
      '$_svgPath/Icon_Inactive_Arrow_Right.svg';
  static const questionIcon = '$_svgPath/Icon_Question.svg';
  static const bgWave = '$_pngPath/bg_wave.png';
  static const bgWaveGrey = '$_pngPath/bg_wave_grey.png';
  static const settingIconGreen = '$_svgPath/Icon_Setting_Green.svg';
  static const trashIcon = '$_svgPath/Icon_Trash.svg';

  static const buttonClose = '$_svgPath/Icon_Button_Close.svg';

//利用規定
  static const checkGreen2Icon = '$_svgPath/Icon_Check_Green2.svg';
  static const checkIcon = '$_svgPath/Icon_Check.svg';

//エラー関連
  static const alertIcon = '$_svgPath/Icon_alert.svg';
  static const alertIconHome = '$_svgPath/Icon_alert_home.svg';
  static const statusIcon = '$_svgPath/Icon_status.svg';

// 証明書選択画面
  static const myNumberCard = '$_basePath/png/my_number_card_new.png';
  static const myNumberCardCatchPhrase =
      '$_basePath/png/my_number_card_catchphrase.png';
  static const myNumberCardSelected =
      '$_basePath/png/my_number_card_selected.png';
  static const drivingLicenses = '$_basePath/png/driving_licenses.png';
  static const drivingLicensesSelected =
      '$_basePath/png/driving_licenses_selected.png';

  // マイナンバーカード読み取りイメージ
  static const myNumberScanImage = '$_basePath/png/my_number_scan_image.png';

  // マイナンバーカード読み取り準備中モーダル用
  static const cardReaderSetupImage = '$_basePath/png/card_reader_setup.png';

  // マイナンバーカード読み取り中モーダル用
  static const cardReaderReadingImage =
      '$_basePath/png/card_reader_reading.png';

  // マイナンバーカード読み取り完了モーダル用
  static const cardReaderSuccessImage =
      '$_basePath/png/card_reader_success.png';

  // 本人確認お手続き番号確認画面
  static const arrowLeftWhiteIcon = '$_svgPath/Icon_arrow_left.svg';
  static const qrCodeIcon = '$_svgPath/Icon_QrCode.svg';
  static const referenceNumberProcess =
      '$_pngPath/reference_number_process.png';

  // 署名用パスワード入力説明画面
  static const passwordInputExplanationImage =
      '$_pngPath/password_input_explanation.png';

  //eKYC概要説明画面
  static const ekycExplanationImage = '$_pngPath/ekyc_explanation.png';

  //eKYCWebViewヘッダー
  static const closeIcon = '$_svgPath/Icon_close.svg';

  // 中黒
  static const middleDot = '$_svgPath/Icon_Middle_Dot.svg';

  // 本人確認
  static const homeLightBackground = '$_pngPath/home_light_bg.png';
  static const finishedIcon = '$_svgPath/Icon_finished.svg';
  static const continueOnWeb = '$_pngPath/continue_on_web_example.png';
  static const greyCircle = '$_svgPath/Icon_Grey_Circcle.svg';

  //ログイン
  static const loginVaridation = '$_svgPath/Icon_loginVaridation.svg';

  // ID管理
  static const passwordEye = '$_pngPath/password_eye.png';
  static const unLink = '$_svgPath/Icon_Unlink.svg';
  static const dtpidAlignment = '$_pngPath/dtpid_alignment.png';
}
