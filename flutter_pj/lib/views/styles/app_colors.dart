import 'package:flutter/material.dart';

/// アプリ内で使用する色の定義
class AppColors {
  const AppColors._();

  static const primary = Color(0xff004831);

  static const white = Color(0xffffffff);
  static const black = Color(0xff000000);
  static const textBlack = Color(0xff141d1d);
  static const background = Color(0xffc4c4c4);
  static const blue = Color(0xff0070f4);
  static const darkBlue1 = Color(0xff14283A);
  static const darkBlue2 = Color(0xff131e26);
  static const darkBlue3 = Color(0xff04121F);
  static const tradGreen = Color(0xff004831);
  static const freshGreen = Color(0xffC4D700);
  static const freshGreenDark = Color(0xFFA0C81E);
  static const grey100 = Color(0xffF5F5F5);
  static const grey200 = Color(0xffD5DEDB);
  static const grey300 = Color(0xffE0E0E0);
  static const greyText = Color(0xFF999999);
  static const lightBlue = Color(0xFF90BFFF);
  static const skyBlue = Color(0xFF66B4EA);
  static const skyBlueDark = Color(0xFF49A4E3);
  static const lightGreen1 = Color(0xfff3f8f6);
  static const lightGreen2 = Color(0xffE2EFBC);
  static const functionalBlue = Color(0xFFB3DAF5);
  static const functionalFreshGreen = Color(0xFFC6DE78);
  static const lightGreen = Color(0xfff3f8f6);
  static const grayGreen = Color(0xff56635f);
  static const borderGrayGreen = Color(0xffd5dedb);
  static const textGrayGreen = Color(0xff56635F);
  static const basicBackgroundLightGray = Color(0xfff5f5f5);
  static const shadowGray = Color(0x66B3C8C1);
  static const backgroundGreen = Color(0xFFE5EDEA);
  static const narrowDownShadow = Color(0x26003927);
  static const creditNarrowDownShadow = Color(0x26003927);
  static const bottomNavigationBarShadow = Color(0x26000000);
  static const red = Color(0xffC83030);
  static const lightRed = Color(0xffFFEBEB);
  static const orange = Color(0xffff5f04);
  static const inactiveText = Color(0x7888838C);
  static const buttonInactiveText = Color(0xff788883);
  static const cardShadow = Color(0xff003927);
  static const backgroundCard = Color(0xff017A4F);
  static const cardGradation = Color(0xff004831);
  static const transparent = Colors.transparent;
  static const basicBackgroundColor = Color(0xFFB3C8C1);
  static const incomeAndExpenditureBackgroundColor =
      Color.fromRGBO(179, 200, 193, 0.4);
  static const creditCardShadow = Color(0x66003927);
  static const infoScreenDivider = Color(0xFFDDDDDD);
  static const infoScreenHintColor = Color(0xFF666666);
  static const lightGrayishGreen = Color(0xffD2DAD6);
  static const nextButtonShadow = Color(0x26003927);
  static const onPressedColor = Color(0xffe2efbc);
  static const loginScreenButton = Color(0xff1C4733);
}
