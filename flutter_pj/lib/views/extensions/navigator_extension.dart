import 'package:flutter/material.dart';

extension NavigatorExtension on BuildContext {
  void push({required Widget view}) {
    Navigator.push<void>(
      this,
      MaterialPageRoute(
        builder: (context) => view,
      ),
    );
  }

  void pushReplacement({required Widget view}) {
    Navigator.pushReplacement<void, void>(
      this,
      MaterialPageRoute<void>(
        builder: (BuildContext context) => view,
      ),
    );
  }
}
