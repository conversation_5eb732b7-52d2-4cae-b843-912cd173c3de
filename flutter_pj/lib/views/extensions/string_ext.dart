import 'package:flutter/material.dart';

extension StringExt on String {
// 最大文字数以上の場合は末尾を「...」で表示
  String ellipsis({required int maxLength}) {
    if (length < maxLength) {
      return this;
    } else {
      return '${substring(0, maxLength - 1)}...';
    }
  }

  // 画面幅に収まる最大文字数を返却
  // 画面幅とテキストスタイルを指定する
  int getMaxTextLength({
    required TextStyle textStyle,
    required double maxWidth,
  }) {
    int maxLength = length;
    while (maxLength > 0) {
      final String visibleText = substring(0, maxLength);
      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: visibleText, style: textStyle),
        textDirection: TextDirection.ltr,
      )..layout();

      if (textPainter.width > maxWidth) {
        maxLength--;
      } else {
        break;
      }
    }
    return maxLength;
  }
}
