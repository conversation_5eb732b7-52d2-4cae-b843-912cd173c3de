import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';
import 'package:dtp_app/views/routers/route_generator.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/account_inquiry/account_inquiry_screen.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/credit_card_statement_inquiry_screen.dart';
import 'package:dtp_app/views/screens/debug/debug_menu_screen.dart';
import 'package:dtp_app/views/screens/force_update/force_update_screen.dart';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/home_navigation/home_navigation_screen.dart';
import 'package:dtp_app/views/screens/id_linkage/id_linkage_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/card_reader/card_reader_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/ekyc_explanation/ekyc_explanation_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_document/identity_document_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/entering_address_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/entering_date_of_birth_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/entering_name_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/entering_position_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/entering_postcode_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/info_finished_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/password_input_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input_explanation/password_input_explanation_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/reference_number/reference_number_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/verified_user/verified_user_screen.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/income_and_expenditure_screen.dart';
import 'package:dtp_app/views/screens/jailbreak_detection/jailbreak_detection_screen.dart';
import 'package:dtp_app/views/screens/jpki_terms/jpki_terms_screen.dart';
import 'package:dtp_app/views/screens/login/login_screen.dart';
import 'package:dtp_app/views/screens/my_page/my_page_screen.dart';
import 'package:dtp_app/views/screens/splash/splash_screen.dart';
import 'package:dtp_app/views/screens/terms/terms_screen.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

enum _Mode { general, debug }

/// アプリ内のルーティングを定義する
class AppRouter {
  const AppRouter() : _mode = _Mode.general;

  const AppRouter.debug() : _mode = _Mode.debug;

  final _Mode _mode;

  /// 初期ルート
  /// 通常：スプラッシュ画面
  /// デバッグ：デバッグメニュー画面
  String get initialRoute {
    switch (_mode) {
      case _Mode.general:
        return RouteNames.splash;
      case _Mode.debug:
        return RouteNames.debugMenu;
    }
  }

  /// 各画面へのルーティングの設定
  Route<dynamic>? onGenerateRoute(RouteSettings settings) =>
      RouteGenerator(settings)
          .addRoute(
            name: RouteNames.debugMenu,
            builder: (_) {
              if (_mode == _Mode.debug) {
                return const DebugMenuScreen();
              } else {
                // デバッグメニューが許可されていない場合はホーム画面に遷移
                return const HomeNavigationScreen();
              }
            },
          )
          .addRoute(
            name: RouteNames.splash,
            builder: (_) => const SplashScreen(),
          )
          .addRoute(
            name: RouteNames.jailbreakDetection,
            builder: (_) => const JailbreakDetectionScreen(),
          )
          .addRoute(
            name: RouteNames.homeNavigation,
            builder: (_) => const HomeNavigationScreen(),
          )
          .addRoute(
            name: RouteNames.terms,
            builder: (_) {
              return const TermsScreen();
            },
          )
          .addRoute(
            name: RouteNames.home,
            builder: (_) => const HomeScreen(),
          )
          .addRoute(
            name: RouteNames.accountInquiry,
            builder: (_) => const AccountInquiryScreen(),
          )
          .addRoute(
            name: RouteNames.forceUpdate,
            builder: (_) {
              final config = settings.arguments as ForceUpdateConfig;
              return ForceUpdateScreen(
                forceUpdateConfig: config,
              );
            },
          )
          .addRoute(
            name: RouteNames.incomeAndExpenditure,
            builder: (_) {
              final arguments = settings.arguments as Map;
              return IncomeAndExpenditureScreen(
                bankAccount:
                    arguments['bankAccount'] as BankAccountWithBalanceDetail,
                serverDate: (arguments['serverDate']) as String,
              );
            },
          )
          .addRoute(
            name: RouteNames.myPage,
            builder: (_) => const MyPageScreen(),
          )
          .addRoute(
            name: RouteNames.identityReferenceNumber,
            builder: (_) => const IdentityReferenceNumberScreen(),
          )
          .addRoute(
            name: RouteNames.jpkiTerms,
            builder: (_) => const JpkiTermsScreen(),
          )
          .addRoute(
            name: RouteNames.identityDocument,
            builder: (_) => const IdentityDocumentScreen(),
          )
          .addRoute(
            name: RouteNames.passwordInput,
            builder: (_) => const PasswordInputScreen(),
          )
          .addRoute(
            name: RouteNames.cardReader,
            builder: (_) => const CardReaderScreen(),
          )
          .addRoute(
            name: RouteNames.ekycExplanation,
            builder: (_) => const EkycExplanationScreen(),
          )
          .addRoute(
            name: RouteNames.passwordInputExplanation,
            builder: (_) => const PasswordInputExplanationScreen(),
          )
          .addRoute(
            name: RouteNames.enteringPosition,
            builder: (_) {
              return const PositionScreen();
            },
          )
          .addRoute(
            name: RouteNames.enteringDateOfBirth,
            builder: (_) => const DateOfBirthScreen(),
          )
          .addRoute(
            name: RouteNames.enteringAddress,
            builder: (_) {
              return const AddressScreen();
            },
          )
          .addRoute(
            name: RouteNames.registerFinished,
            builder: (_) => const InfoEnteringFinishedScreen(),
          )
          .addRoute(
            name: RouteNames.idLinkage,
            builder: (_) {
              final arguments = settings.arguments as Map;
              return IdLinkageScreen(
                dtpId: arguments['dtpId'] as String,
                vdId: arguments['vdId'] as String,
              );
            },
          )
          .addRoute(
            name: RouteNames.verifiedUser,
            builder: (_) => const VerifiedUserScreen(),
          )
          .addRoute(
            name: RouteNames.creditCardStatementInquiry,
            builder: (_) => const CreditCardStatementInquiryScreen(),
          )
          .addRoute(
            name: RouteNames.enteringName,
            builder: (_) => const NameScreen(),
          )
          .addRoute(
            name: RouteNames.enteringPostCode,
            builder: (_) => const PostCodeScreen(),
          )
          .addRoute(name: RouteNames.login, builder: (_) => const LoginScreen())
          .generate();
}

final appRouterProvider = Provider<AppRouter>((ref) {
  return const AppRouter();
});
