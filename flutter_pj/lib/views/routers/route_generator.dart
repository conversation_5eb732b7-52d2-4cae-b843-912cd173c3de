import 'package:flutter/material.dart';

/// GenerateRouteの処理を簡略化するためのクラス
class RouteGenerator {
  RouteGenerator(this._settings) : _route = null;
  RouteGenerator._inner(this._settings, this._route);

  final RouteSettings _settings;
  final Route<dynamic>? _route;

  RouteGenerator addRoute({
    required String name,
    required WidgetBuilder builder,
    bool fullscreenDialog = false,
  }) {
    if (_settings.name == name) {
      return RouteGenerator._inner(
        _settings,
        MaterialPageRoute(
          builder: builder,
          settings: _settings,
          fullscreenDialog: fullscreenDialog,
        ),
      );
    } else {
      return this;
    }
  }

  Route<dynamic>? generate() => _route;
}
