import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/jailbreak_detection/jailbreak_detection_screen_notifier.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:dtp_app/views/screens/home_navigation/home_navigation_screen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';

class JailbreakDetectionScreen extends HookConsumerWidget {
  const JailbreakDetectionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(jailbreakDetectionScreenProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          await ref
              .watch(jailbreakDetectionScreenProvider.notifier)
              .initialize();
        });
        return null;
      },
      [],
    );

    if (state.jailbreakType == JailbreakType.jailbreak) {
      return const _JailbreakDialog();
    }

    if (state.jailbreakType == JailbreakType.root) {
      return const _RootDialog();
    }

    if (state.jailbreakType == JailbreakType.none) {
      return const HomeNavigationScreen();
    }

    return const _Loading();
  }
}

// TODO: ローディングのデザインはSP11以降で対応 (DTPO-5809にて対応予定)
class _Loading extends StatelessWidget {
  const _Loading();

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        color: AppColors.tradGreen,
      ),
    );
  }
}

class _JailbreakDialog extends HookConsumerWidget {
  const _JailbreakDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    // スプラッシュ画面を終了させる
    FlutterNativeSplash.remove();

    return CommonDialog.ok(
      message: AppLocalizations.of(context)!.jailbreakDetection,
      okButtonText: AppLocalizations.of(context)!.yes,
      onOkPressed: () async {
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.yes,
          screenName: AppLocalizations.of(context)!.jailbreakDetectionDialog,
        );
        exit(0);
      },
      isPopAfterOkButtonPress: false,
    );
  }
}

class _RootDialog extends HookConsumerWidget {
  const _RootDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    // スプラッシュ画面を終了させる
    FlutterNativeSplash.remove();

    return CommonDialog.ok(
      message: AppLocalizations.of(context)!.jailbreakDetection,
      okButtonText: AppLocalizations.of(context)!.yes,
      onOkPressed: () async {
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.yes,
          screenName: AppLocalizations.of(context)!.rootDetectionDialog,
        );
        await Future.delayed(Duration(milliseconds: 500));
        exit(0);
      },
      isPopAfterOkButtonPress: false,
    );
  }
}
