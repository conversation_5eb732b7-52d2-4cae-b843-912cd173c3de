import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/id_linkage/id_linkage_screen_notifier.dart';
import 'package:dtp_app/business_logics/my_page/my_page_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/idaas_path.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/web_view/customized/idaas_mail_address_change_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/customized/idaas_password_change_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/customized/idaas_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

class IdLinkageScreen extends HookConsumerWidget with CommonErrorHandlerMixin {
  const IdLinkageScreen({
    super.key,
    required this.dtpId,
    required this.vdId,
  });

  final String dtpId;
  final String vdId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(idLinkageScreenProvider);
    final controller = ref.watch(idLinkageScreenProvider.notifier);

    /// 共通エラーを検知した際はエラーダイアログを表示する
    /// 特殊な処理が必要な箇所があるため、本画面では共通処理としてダイアログは表示せず
    /// 画面内にダイアログ表示処理を入れる
    handleError(
      context,
      ref,
      state.error?.copyWith(isShowOnDialog: false),
      onOkTap: () {
        // セッションタイムアウトエラーの際
        // homeNavigation上の画面に帰ってくる為の処理
        if (state.error?.isSessionTimeOutError ?? false) {
          Navigator.of(context).popUntil(
            (router) => router.settings.name == RouteNames.homeNavigation,
          );
        }
      },
      screenId: ScreenIdNumber.idLinkageScreenId,
      screenName: AppLocalizations.of(context)!.idLinkage,
    );

    useEffect(
      () {
        // 初期化処理
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.setArguments(dtpId: dtpId, vdId: vdId);
        });
        return null;
      },
      const [],
    );

    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: AppColors.tradGreen,
      ),
    );
    return Container(
      color: AppColors.tradGreen,
      child: SafeArea(
        bottom: false,
        child: Scaffold(
          backgroundColor: AppColors.basicBackgroundLightGray,
          appBar: const _AppBar(),
          body: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Container(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      alignment: Alignment.bottomCenter,
                      image: AssetImage(
                        Assets.bgWaveGrey,
                      ),
                      fit: BoxFit.fitWidth,
                    ),
                  ),
                  child: Column(
                    children: [
                      _Body(),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class _AppBar extends HookConsumerWidget implements PreferredSizeWidget {
  const _AppBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return AppBar(
      backgroundColor: AppColors.white,
      elevation: 0,
      shape: const Border(
        bottom: BorderSide(
          color: AppColors.borderGrayGreen,
        ),
      ),
      leadingWidth: 90,
      leading: GestureDetector(
        onTap: () {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.back,
            screenName: AppLocalizations.of(context)!.idLinkageSettings,
          );
          Navigator.pop(context);
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                Assets.arrowLeftIcon,
                width: 16,
                height: 16,
              ),
              const SizedBox(width: 4),
              Text(
                strutStyle: StrutStyle(
                  leadingDistribution: TextLeadingDistribution.even,
                  height: 1.5,
                  fontSize: 16,
                ),
                AppLocalizations.of(context)!.back,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                      height: 1.5,
                    ),
              ),
            ],
          ),
        ),
      ),
      title: Text(
        AppLocalizations.of(context)!.idManagement,
        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontSize: 16,
              color: AppColors.tradGreen,
            ),
      ),
      centerTitle: true,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}

class _Body extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(idLinkageScreenProvider);
    final controller = ref.watch(idLinkageScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final homeNavigationScreenController =
        ref.watch(homeNavigationScreenProvider.notifier);

    useEffect(
      () {
        // 初回読み込み
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.idLinkageScreenId,
            screenName: AppLocalizations.of(context)!.idLinkageSettings,
          );
        });
        return null;
      },
      const [],
    );

    // エラーがあればダイアログでエラーメッセージを表示する
    if (state.error.isNotNull) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.microtask(() {
          // ID新規発行に失敗した場合、専用のダイアログを表示
          if (state.dtpId.isEmpty && !state.isCreated) {
            final digitalTouchPointLogin =
                ref.watch(buildConfigProvider).digitalTouchPointLogin;
            if (!context.mounted) return;
            showDialog(
              context: context,
              builder: (context) => CommonDialog.okCancel(
                title: ErrorInfo.defaultErrorTitle,
                message: MyPageErrorInfo.dtpIdCreationErrorDialogMessage,
                code: MyPageErrorInfo.dtpIdCreationErrorDialogCode,
                messageTextStyle: Theme.of(context).textTheme.bodySmall,
                okButtonText: AppLocalizations.of(context)!
                    .dtpIdCreationErrorDialogTitlePositiveButton,
                onOkPressed: () async {
                  // デジタルタッチポイントweb版へ
                  unawaited(
                    launchUrl(
                      Uri.parse(digitalTouchPointLogin),
                      mode: LaunchMode.externalApplication,
                    ),
                  );

                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!
                        .dtpIdCreationErrorDialogPositive,
                    screenName: AppLocalizations.of(context)!.idLinkageSettings,
                  );

                  // ログイアウトしてログイン画面へ遷移する
                  if (context.mounted) {
                    await _logout(
                      context,
                      controller,
                      homeNavigationScreenController,
                    );
                  }
                },
                cancelButtonText: AppLocalizations.of(context)!
                    .dtpIdCreationErrorDialogTitleNegativeButton,
                onCancelPressed: () async {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!
                        .dtpIdCreationErrorDialogNegative,
                    screenName: AppLocalizations.of(context)!.idLinkageSettings,
                  );

                  // ログアウトしてログイン画面へ遷移する
                  if (context.mounted) {
                    await _logout(
                      context,
                      controller,
                      homeNavigationScreenController,
                    );
                  }
                },
              ),
            );
          } else {
            if (!context.mounted) return;
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) => CommonDialog.ok(
                okButtonText: AppLocalizations.of(context)!.close,
                message: state.error?.message ?? '',
                isPopAfterOkButtonPress: true,
                onOkPressed: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.closeErrorDialog,
                    screenName: AppLocalizations.of(context)!.idLinkageSettings,
                  );
                  controller.clearError();
                },
              ),
            );
          }
        });
      });
    }
    // ID連携解除完了フラグがtrueの時にID連携完了ダイアログを表示する
    if (state.isCompleteUnlinking) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!context.mounted) return;
        final appLocalizations = AppLocalizations.of(context)!;
        controller.resetCompleteFlag();
        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (context) => CommonDialog.ok(
            message: appLocalizations.unlinkedCompleteDialogMessage,
            messageTextStyle: Theme.of(context).textTheme.bodySmall,
            okButtonText: appLocalizations.logout,
            onOkPressed: () async {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: appLocalizations.logoutDialog,
                screenName: appLocalizations.idLinkageSettings,
              );
              await controller.logout();
              // ID連携解除後はVDIDログイン画面へ遷移させ、生体認証を起動させない
              homeNavigationScreenController.setIdUnlinked(true);
              // Navigationのログイン状態を更新
              await homeNavigationScreenController.fetchLoginStatus();
              homeNavigationScreenController.changeCurrentIndex(0);
              // プッシュしている画面であるため、HomeNavigation管理下画面に戻る
              if (context.mounted) Navigator.pop(context);
            },
          ),
        );
      });
    }
    return SafeArea(
      child: Column(
        children: [
          ScreenNumber(
            screenNumber: ScreenIdNumber.idLinkageScreenId,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16),
            child: Column(
              children: [
                const SizedBox(height: 16),
                _DigitalTouchPointIdCard(),
                const SizedBox(height: 8),
                _ValueDoorIdCard(vdId: state.vdId),
                const SizedBox(height: 80),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _logout(
    BuildContext context,
    IdLinkageScreenNotifier idLinkageController,
    HomeNavigationScreenNotifier homeNavigationController,
  ) async {
    //ログアウト処理の実行（セッションID削除）
    await idLinkageController.logout();

    // ホーム画面に遷移する
    homeNavigationController.changeCurrentIndex(0);
    // ホーム画面のログアウト完了フラグの更新
    homeNavigationController.setLogoutComplete(true);
    // ホーム画面のログイン状況をログインからログアウトに更新
    await homeNavigationController.fetchLoginStatus();

    // ログイン画面に遷移する
    if (context.mounted) {
      await Navigator.of(context).pushNamedAndRemoveUntil(
        RouteNames.homeNavigation,
        (route) => false,
      );
    }
  }
}

class _DigitalTouchPointIdCard extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(idLinkageScreenProvider);

    return Container(
      decoration: ShapeDecoration(
        color: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        shadows: const [
          BoxShadow(
            color: AppColors.shadowGray,
            blurRadius: 4,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context)!.digitalTouchPoint,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                        fontSize: 20,
                        height: 1.5,
                        fontFamily: FontFamily.robotoCondensed,
                        fontWeight: FontWeight.bold,
                        color: AppColors.tradGreen,
                      ),
                ),
                // 紐付け解除後のfreee再連携エラー抑止のため、連携解除ボタンを非表示にする（Ph0.5/SP26/DTPO-21780）
                // 見た目上のViewではボタンは非表示で対応するが、コード（ロジック）は将来的に利用するため残したままにする（Ph0.5/SP26/DTPO-21780）
                // dtpId連携ありの場合、連携解除ボタンを表示
                // Visibility(
                //   visible: state.dtpId.isNotEmpty,
                //   child: _UnLinkButton(),
                // ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)!.digitalTouchPointIDMessage,
              style: Theme.of(context).textTheme.bodySmall,
            ),

            const SizedBox(height: 16),
            // dtpIdが存在すれば紐付けありの状態で、空であれば紐付けなしの状態で表示
            state.dtpId.isNotEmpty
                ? _DtpIdLinkArea(dtpId: state.dtpId)
                : _DtpIdNotLinkArea(),
          ],
        ),
      ),
    );
  }
}

class _ValueDoorIdCard extends StatelessWidget {
  const _ValueDoorIdCard({
    required this.vdId,
  });

  final String vdId;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: ShapeDecoration(
        color: AppColors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        shadows: const [
          BoxShadow(
            color: AppColors.shadowGray,
            blurRadius: 4,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 24),
            Text(
              AppLocalizations.of(context)!.valueDoor,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    fontSize: 20,
                    height: 1.5,
                    color: AppColors.primary,
                    fontFamily: FontFamily.robotoCondensed,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)!.valueDoorIDMessage,
              style: Theme.of(context).textTheme.bodySmall!,
            ),
            const SizedBox(height: 16),
            _VdIdArea(vdId: vdId),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class _DtpIdLinkArea extends StatelessWidget {
  const _DtpIdLinkArea({
    required this.dtpId,
  });

  final String dtpId;

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    return Column(
      children: [
        _IdInformation(
          title: appLocalizations.idMailAddress,
          content: dtpId,
          idaasType: _IdaasType.mailAddress,
        ),
        const SizedBox(height: 8),
        _IdInformation(
          title: appLocalizations.password,
          content: appLocalizations.asterisks8,
          idaasType: _IdaasType.password,
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}

class _DtpIdNotLinkArea extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(idLinkageScreenProvider.notifier);
    final myPageController = ref.watch(myPageScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.doNotHaveAnId,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
        ),
        SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.lightGreen1,
              borderRadius: BorderRadius.circular(4),
            ),
            padding:
                const EdgeInsets.only(top: 24, right: 35, bottom: 24, left: 35),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.dtpCreationRequired,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                SizedBox(height: 32),
                SizedBox(
                  height: 100,
                  width: 157,
                  child: Image.asset(
                    Assets.dtpidAlignment,
                    fit: BoxFit.cover,
                  ),
                ),
                SizedBox(height: 28),
                Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: SizedBox(
                        width: 241,
                        height: 48,
                        child: AppRectangleButton(
                          label: AppLocalizations.of(context)!.createNewDtpId,
                          textColor: AppColors.white,
                          buttonColor: AppColors.tradGreen,
                          onPressed: () async {
                            // FirebaseAnalyticsログ送信
                            analyticsLogController.sendButtonLog(
                              buttonName:
                                  AppLocalizations.of(context)!.createNewDtpId,
                              screenName: AppLocalizations.of(context)!
                                  .idLinkageSettings,
                            );
                            await controller.refreshEncryptedCookie();
                            // 暗号化Cookieの取得に失敗した際には処理を中断
                            if (ref
                                .read(idLinkageScreenProvider)
                                .error
                                .isNotNull) {
                              return;
                            }
                            // IDaaS連携画面をWebViewで表示
                            final idaasAccountUrl =
                                '${ref.watch(buildConfigProvider).idaasBaseUrl}${IdaasPath.signUpInputEmailPath}';
                            if (!context.mounted) return;
                            await ref.watch(idaasWebViewLauncherProvider).call(
                                  context,
                                  WebViewRequest.launchUrl(
                                    initialUrl: idaasAccountUrl,
                                  ),
                                  AppLocalizations.of(context)!.createNewDtpId,
                                  ScreenIdNumber.idLinkageScreenId,
                                );
                            final dtpId = ref.read(
                              idLinkageScreenProvider
                                  .select((value) => value.dtpId),
                            );
                            if (dtpId.isNotEmpty) {
                              // マイページ画面のDTPID情報を更新（同期）する
                              myPageController.updateDtpId(dtpId);
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _VdIdArea extends StatelessWidget {
  const _VdIdArea({
    required this.vdId,
  });

  final String vdId;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _IdInformation(
          title: AppLocalizations.of(context)!.id,
          content: vdId,
          idaasType: _IdaasType.none,
        ),
        SizedBox(
          height: 8,
        ),
        _IdInformation(
          title: AppLocalizations.of(context)!.authenticationMethod,
          content: AppLocalizations.of(context)!.passwordNinshou,
          idaasType: _IdaasType.none,
        ),
      ],
    );
  }
}

class _IdInformation extends HookConsumerWidget {
  const _IdInformation({
    required this.title,
    required this.content,
    required this.idaasType,
  });

  final String title;
  final String content;
  final _IdaasType idaasType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final idLinkageController = ref.watch(idLinkageScreenProvider.notifier);
    final myPageController = ref.watch(myPageScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    return Center(
      child: Center(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Container(
              decoration: BoxDecoration(
                color: AppColors.lightGreen1,
                borderRadius: BorderRadius.circular(4),
              ),
              constraints: BoxConstraints(minHeight: constraints.minHeight),
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          title,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.grayGreen,
                                  ),
                        ),
                        Visibility(
                          visible: idaasType != _IdaasType.none,
                          child: GestureDetector(
                            onTap: () async {
                              // FirebaseAnalyticsログ送信
                              analyticsLogController.sendButtonLog(
                                buttonName: idaasType.isMailAddress
                                    ? AppLocalizations.of(context)!
                                        .changeIdAndMailAddress
                                    : AppLocalizations.of(context)!
                                        .changePassword,
                                screenName: AppLocalizations.of(context)!
                                    .idLinkageSettings,
                              );
                              await idLinkageController
                                  .refreshEncryptedCookie();
                              // 暗号化Cookie再取得時にエラーが発生した際には処理を中断
                              if (ref
                                  .read(idLinkageScreenProvider)
                                  .error
                                  .isNotNull) {
                                return;
                              }
                              // 【IDaaS】メールアドレス/PW変更画面の表示
                              final idaasBaseUrl =
                                  ref.watch(buildConfigProvider).idaasBaseUrl;
                              final initialUrl = idaasType.isMailAddress
                                  ? '$idaasBaseUrl${IdaasPath.changeEmailInputPath}'
                                  : '$idaasBaseUrl${IdaasPath.changePasswordInputPath}';
                              if (!context.mounted) return;
                              await ref
                                  .watch(
                                    idaasType.isMailAddress
                                        ? idaasMailAddressChangeWebViewLauncherProvider
                                        : idaasPasswordChangeWebViewLauncherProvider,
                                  )
                                  .call(
                                    context,
                                    WebViewRequest.launchUrl(
                                      initialUrl: initialUrl,
                                    ),
                                    idaasType.isMailAddress
                                        ? AppLocalizations.of(context)!
                                            .changeIdAndMailAddress
                                        : AppLocalizations.of(context)!
                                            .changePassword,
                                    ScreenIdNumber.idLinkageScreenId,
                                  );
                              await idLinkageController.updateDtpId();
                              await myPageController.initialize();
                            },
                            child: Text(
                              AppLocalizations.of(context)!.change,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(
                                    fontFamily: FontFamily.notoSansJP,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.tradGreen,
                                  ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    EmptyHyphenText(
                      data: content,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontWeight: FontWeight.bold,
                            color: AppColors.black,
                          ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

//紐付け解除後のfreee再連携エラー抑止のため、連携解除ボタンを非表示にする（Ph0.5/SP26/DTPO-21780）
//見た目上のViewではボタンは非表示で対応するが、コード（ロジック）は将来的に利用するため残したままにする（Ph0.5/SP26/DTPO-21780）
// class _UnLinkButton extends HookConsumerWidget {
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final idLinkageController = ref.watch(idLinkageScreenProvider.notifier);
//     return GestureDetector(
//       onTap: () async {
//         // FirebaseAnalyticsログ送信
//         unawaited(
//           idLinkageController.sendButtonLog(
//             buttonName: AppLocalizations.of(context)!.unlinked,
//             screenName: AppLocalizations.of(context)!.idLinkageSettings,
//           ),
//         );
//         // 確認ダイアログの表示
//         await showConfirmDialog(context, ref);
//       },
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.center,
//         children: [
//           SvgPicture.asset(
//             Assets.unLink,
//             width: 24,
//             height: 24,
//           ),
//           Padding(
//             padding: const EdgeInsets.only(right: 4),
//           ),
//           Text(
//             strutStyle: StrutStyle(
//               leadingDistribution: TextLeadingDistribution.even,
//               fontSize: 16,
//               height: 1.5,
//             ),
//             AppLocalizations.of(context)!.unlinked,
//             style: Theme.of(context).textTheme.bodyMedium!.copyWith(
//                   height: 1.5,
//                   fontWeight: FontWeight.w700,
//                   color: AppColors.tradGreen,
//                 ),
//           ),
//         ],
//       ),
//     );
//   }
// }

Future<void> showConfirmDialog(BuildContext context, WidgetRef ref) async {
  final idLinkageController = ref.watch(idLinkageScreenProvider.notifier);
  final analyticsLogController = ref.read(analyticsLogControllerProvider);
  final appLocalizations = AppLocalizations.of(context)!;
  await showDialog(
    barrierDismissible: false,
    context: context,
    builder: (context) => CommonDialog.okCancel(
      message: appLocalizations.unlinkedConfirmDialogMessage,
      messageTextStyle: Theme.of(context).textTheme.bodySmall,
      okButtonText: appLocalizations.cancel,
      cancelButtonText: appLocalizations.unlinked,
      onOkPressed: () async {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: appLocalizations.cancel,
          screenName: appLocalizations.idLinkageSettings,
        );
      },
      onCancelPressed: () async {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: appLocalizations.unlinkedDialog,
          screenName: appLocalizations.idLinkageSettings,
        );

        // ID連携解除APIの実行
        await idLinkageController.idUnlinking();
      },
    ),
  );
}

enum _IdaasType {
  // メールアドレス
  mailAddress,
  // パスワード
  password,
  // なし
  none,
}

extension _IdaasTypeExt on _IdaasType {
  bool get isMailAddress => this == _IdaasType.mailAddress;
}
