import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/bank_account_display_config/bank_account_display_config_screen_notifier.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/screens/bank_account_display_config/widget/reorderable_config_list_view.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 口座表示設定画面
class BankAccountDisplayConfigScreen extends HookConsumerWidget
    with CommonErrorHandlerMixin {
  const BankAccountDisplayConfigScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(bankAccountDisplayConfigScreenProvider);
    final homeScreenState = ref.watch(homeScreenProvider);
    final controller =
        ref.watch(bankAccountDisplayConfigScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final tabController = useTabController(initialLength: 2);
    final index = useState(0);

    final isSaveButtonEnabled = state.hasChanged &&
        !state.hasValidationError &&
        !homeScreenState.isLoading;

    final tabTextStyle = Theme.of(context)
        .textTheme
        .bodyMedium!
        .copyWith(fontWeight: FontWeight.w700);

    useEffect(
      () {
        // 初回読み込み
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.load(
            homeScreenState.bankAccounts,
            homeScreenState.otherBankAccounts,
            homeScreenState.isFreeeLinked,
          );
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.bankAccountDisplayConfigScreenId,
            screenName: AppLocalizations.of(context)!.bankAccountDisplayConfig,
          );
          return;
        });
        return null;
      },
      const [],
    );

    // エラーを検知した際には共通エラーハンドリングへ
    handleError(
      context,
      ref,
      state.error,
      onOkTap: () {
        Navigator.pop(context);
        // セッションタイムアウトの際にはダイアログを閉じた後、HOMEに遷移させる
        if (state.error?.isSessionTimeOutError ?? false) Navigator.pop(context);
      },
      okButtonName: AppLocalizations.of(context)!.sessionTimeoutErrorButton,
      screenId: ScreenIdNumber.bankAccountDisplayConfigScreenId,
      screenName: AppLocalizations.of(context)!.bankAccountDisplayConfig,
    );

    tabController.addListener(() {
      index.value = tabController.index;
    });

    return Scaffold(
      backgroundColor: AppColors.tradGreen,
      body: Padding(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
        ),
        child: Container(
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            color: AppColors.white,
          ),
          child: SafeArea(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 8, right: 8),
                      child: IconButton(
                        onPressed: () {
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName: AppLocalizations.of(context)!.cancel,
                            screenName: AppLocalizations.of(context)!
                                .bankAccountDisplayConfig,
                          );
                          Navigator.of(context).pop();
                        },
                        icon: SvgPicture.asset(
                          Assets.buttonClose,
                          width: 32,
                          height: 32,
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: Text(
                        AppLocalizations.of(context)!.bankAccountDisplayConfig,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontSize: 20,
                              color: AppColors.tradGreen,
                            ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: DefaultTabController(
                    length: 2,
                    child: Column(
                      children: [
                        TabBar(
                          controller: tabController,
                          labelColor: AppColors.tradGreen,
                          unselectedLabelColor: AppColors.grayGreen,
                          indicator: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: AppColors.tradGreen,
                                width: 4,
                              ),
                            ),
                          ),
                          tabs: [
                            Tab(
                              child: Text(
                                AppLocalizations.of(context)!.smbcBankAccount,
                                style: tabTextStyle.copyWith(
                                  color: index.value == 0
                                      ? AppColors.tradGreen
                                      : AppColors.textGrayGreen,
                                ),
                              ),
                            ),
                            Tab(
                              child: Text(
                                AppLocalizations.of(context)!.otherBankAccount,
                                style: tabTextStyle.copyWith(
                                  color: index.value == 1
                                      ? AppColors.tradGreen
                                      : AppColors.textGrayGreen,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Container(height: 1, color: AppColors.borderGrayGreen),
                        Expanded(
                          child: Stack(
                            children: [
                              TabBarView(
                                controller: tabController,
                                children: [
                                  _SMBCAccountList(
                                    bankAccounts: state.bankAccounts,
                                    onReordered: controller.onReordered,
                                    onChangeDisplayName:
                                        controller.onChangeDisplayName,
                                    onChangeDisplayFlag:
                                        controller.onChangeDisplayFlag,
                                  ),
                                  _OtherBankAccountList(
                                    bankAccounts: state.otherBankAccounts,
                                    onReordered: controller.onFreeeReordered,
                                    onChangeDisplayFlag:
                                        controller.onChangeFreeeDisplayFlag,
                                    isFreeeAccountsEmpty:
                                        state.isFreeeAccountsEmpty,
                                    isFreeeLinked: state.isFreeeLinked,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Center(
                    child: SizedBox(
                      width: 240,
                      height: 48,
                      child: ElevatedButton(
                        // 変更箇所があり、かつバリデーションエラーがない場合のみ活性化
                        onPressed: isSaveButtonEnabled
                            ? () async {
                                // FirebaseAnalyticsログ送信
                                analyticsLogController.sendButtonLog(
                                  buttonName:
                                      AppLocalizations.of(context)!.save,
                                  screenName: AppLocalizations.of(context)!
                                      .bankAccountDisplayConfig,
                                );
                                final validationErrorMessage = controller
                                    .getValidationErrorMessageOnSave();
                                final hasValidationError =
                                    validationErrorMessage.isNotEmpty;

                                // 口座表示数に関してバリデーションがあった場合には処理を中断
                                if (hasValidationError) {
                                  await showDialog(
                                    context: context,
                                    builder: (context) => CommonDialog.ok(
                                      message: validationErrorMessage,
                                    ),
                                  );
                                  return;
                                }

                                // ローディング開始
                                LoadingDialog.loading(context);
                                // 保存処理
                                final result = await controller.save();
                                // 保存処理に失敗している際には処理を中断
                                if (!context.mounted || !result) return;
                                LoadingDialog.loadingEnd(context);
                                Navigator.of(context).pop(true);
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          backgroundColor: AppColors.tradGreen,
                          disabledBackgroundColor: AppColors.backgroundGreen,
                          foregroundColor: AppColors.white,
                          disabledForegroundColor:
                              AppColors.buttonInactiveText.withOpacity(0.55),
                          elevation: 0,
                          textStyle:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                        ),
                        child: Text(
                          AppLocalizations.of(context)!.save,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _SMBCAccountList extends StatelessWidget {
  const _SMBCAccountList({
    required this.bankAccounts,
    required this.onReordered,
    required this.onChangeDisplayName,
    required this.onChangeDisplayFlag,
  });

  final List<BankAccount> bankAccounts;
  final void Function(int oldIndex, int newIndex) onReordered;
  final void Function(int index, bool value) onChangeDisplayFlag;
  final void Function(int index, String value, String? error)
      onChangeDisplayName;

  @override
  Widget build(BuildContext context) {
    final scrollController = ScrollController();

    return RawScrollbar(
      thumbColor: AppColors.basicBackgroundColor,
      thumbVisibility: true,
      thickness: 6,
      radius: const Radius.circular(3.0),
      controller: scrollController,
      child: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16, top: 24, right: 16),
              child: Text(
                AppLocalizations.of(context)!.smbcBankAccountDescription,
              ),
            ),
            ReorderableConfigListView(
              bankType: BankType.smbc,
              bankAccounts: bankAccounts,
              scrollController: scrollController,
              onReordered: onReordered,
              onChangeDisplayName: onChangeDisplayName,
              onChangeDisplayFlag: onChangeDisplayFlag,
            ),
          ],
        ),
      ),
    );
  }
}

class _OtherBankAccountList extends StatelessWidget {
  const _OtherBankAccountList({
    required this.bankAccounts,
    required this.onReordered,
    required this.onChangeDisplayFlag,
    required this.isFreeeAccountsEmpty,
    required this.isFreeeLinked,
  });

  final List<BankAccount> bankAccounts;
  final void Function(int oldIndex, int newIndex) onReordered;
  final void Function(int index, bool value) onChangeDisplayFlag;
  final bool isFreeeAccountsEmpty;
  final bool isFreeeLinked;

  @override
  Widget build(BuildContext context) {
    final scrollController = ScrollController();

    String freeeDescription;
    if (!isFreeeLinked) {
      freeeDescription =
          AppLocalizations.of(context)!.otherBankAccountLinkedDescription;
    } else if (isFreeeAccountsEmpty) {
      freeeDescription =
          AppLocalizations.of(context)!.otherBankAccountsEmptyDescription;
    } else {
      freeeDescription =
          AppLocalizations.of(context)!.otherBankAccountDescription;
    }

    return RawScrollbar(
      thumbColor: AppColors.basicBackgroundColor,
      thumbVisibility: true,
      thickness: 6,
      radius: const Radius.circular(3.0),
      controller: scrollController,
      child: SingleChildScrollView(
        controller: scrollController,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16, top: 24, right: 16),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(freeeDescription),
              ),
            ),
            isFreeeAccountsEmpty
                ? const _EmptyFreeeAccounts()
                : ReorderableConfigListView(
                    bankType: BankType.otherBank,
                    bankAccounts: bankAccounts,
                    scrollController: scrollController,
                    onReordered: onReordered,
                    onChangeDisplayFlag: onChangeDisplayFlag,
                  ),
          ],
        ),
      ),
    );
  }
}

class _EmptyFreeeAccounts extends StatelessWidget {
  const _EmptyFreeeAccounts();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 24),
        Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Image.asset(Assets.freeeConnectBackground),
            Image.asset(
              Assets.freeeConnect,
              height: 140,
              width: 129,
            ),
          ],
        ),
      ],
    );
  }
}
