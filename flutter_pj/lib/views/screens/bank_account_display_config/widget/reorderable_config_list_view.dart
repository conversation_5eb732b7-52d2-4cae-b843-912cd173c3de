import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/bank_account_display_config/bank_account_display_config_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/ext/string_validator.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/screens/bank_account_display_config/widget/border_line.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:dtp_app/views/components/common_text_from_field.dart';

class ReorderableConfigListView extends HookConsumerWidget {
  const ReorderableConfigListView({
    super.key,
    required this.bankType,
    required this.bankAccounts,
    required this.scrollController,
    required this.onReordered,
    required this.onChangeDisplayFlag,
    this.onChangeDisplayName,
  });

  final BankType bankType;
  final List<BankAccount> bankAccounts;
  final ScrollController scrollController;
  final void Function(int oldIndex, int newIndex) onReordered;
  final void Function(int index, bool value) onChangeDisplayFlag;
  final void Function(int index, String value, String? error)?
      onChangeDisplayName;

  /// リストの表示項目を生成する関数
  IndexedWidgetBuilder get _itemBuilder => (context, index) => _ListItem(
        key: Key('${bankAccounts[index].displayConfig.displayOrder}'),
        bankType: bankType,
        index: index,
        bankAccount: bankAccounts[index],
        onChangedFlag: onChangeDisplayFlag,
        onChangedName: onChangeDisplayName,
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // 並び替え可能なリストビュー
    return ReorderableListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      buildDefaultDragHandles: false,
      itemBuilder: _itemBuilder,
      itemCount: bankAccounts.length,
      onReorder: (oldIndex, newIndex) {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.threeLinesIcon,
          screenName: AppLocalizations.of(context)!.bankAccountDisplayConfig,
        );
        onReordered(
          oldIndex,
          newIndex > oldIndex ? newIndex - 1 : newIndex,
        );
      },
    );
  }
}

class _ListItem extends StatelessWidget {
  const _ListItem({
    super.key,
    required this.bankType,
    required this.index,
    required this.bankAccount,
    required this.onChangedFlag,
    this.onChangedName,
  });

  final BankType bankType;
  final int index;
  final BankAccount bankAccount;
  final void Function(int, bool) onChangedFlag;
  final void Function(int, String, String?)? onChangedName;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                bankType.isSmbc
                    ? _SMBCListItem(
                        bankType: bankType,
                        index: index,
                        bankAccount: bankAccount,
                        onChangedFlag: onChangedFlag,
                        onChangedName: onChangedName!,
                      )
                    : _OtherBankListItem(
                        bankType: bankType,
                        index: index,
                        bankAccount: bankAccount,
                        onChangedFlag: onChangedFlag,
                      ),
                _ReorderableIcon(index: index),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
        const Row(
          children: [
            Expanded(child: BorderLine()),
          ],
        ),
      ],
    );
  }
}

class _SMBCListItem extends HookConsumerWidget {
  const _SMBCListItem({
    required this.bankType,
    required this.index,
    required this.bankAccount,
    required this.onChangedFlag,
    required this.onChangedName,
  });

  final BankType bankType;
  final int index;
  final BankAccount bankAccount;
  final void Function(int, bool) onChangedFlag;
  final void Function(int, String, String?) onChangedName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasErrorTextNotifier = useState(false);
    final isAllSMBCAccountInvisible = ref.watch(
      bankAccountDisplayConfigScreenProvider.select(
        (value) => value.isAllSMBCAccountInvisible,
      ),
    );

    return Expanded(
      child: Row(
        children: [
          _CheckBox(
            bankType: bankType,
            bankAccount: bankAccount,
            isInvisible: isAllSMBCAccountInvisible,
            hasErrorTextNotifier: hasErrorTextNotifier,
            onChangedFlag: onChangedFlag,
          ),
          const SizedBox(width: 8),
          _SMBCInfo(
            bankAccount: bankAccount,
            onChangedName: onChangedName,
            hasErrorTextNotifier: hasErrorTextNotifier,
          ),
        ],
      ),
    );
  }
}

class _OtherBankListItem extends HookConsumerWidget {
  const _OtherBankListItem({
    required this.bankType,
    required this.index,
    required this.bankAccount,
    required this.onChangedFlag,
  });

  final BankType bankType;
  final int index;
  final BankAccount bankAccount;
  final void Function(int, bool) onChangedFlag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAllOtherBankAccountInvisible = ref.watch(
      bankAccountDisplayConfigScreenProvider.select(
        (value) => value.isAllOtherBankAccountInvisible,
      ),
    );

    return Expanded(
      child: Row(
        children: [
          _CheckBox(
            bankType: bankType,
            bankAccount: bankAccount,
            isInvisible: isAllOtherBankAccountInvisible,
            onChangedFlag: onChangedFlag,
          ),
          const SizedBox(width: 8),
          _OtherBankInfo(bankAccount: bankAccount),
        ],
      ),
    );
  }
}

class _CheckBox extends HookConsumerWidget {
  const _CheckBox({
    required this.bankType,
    required this.bankAccount,
    required this.isInvisible,
    this.hasErrorTextNotifier,
    required this.onChangedFlag,
  });

  final BankType bankType;
  final BankAccount bankAccount;
  final bool isInvisible;
  final ValueNotifier<bool>? hasErrorTextNotifier;
  final void Function(int, bool) onChangedFlag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseController = ref.read(analyticsLogControllerProvider);
    String getAssetName() {
      if (bankType.isSmbc && hasErrorTextNotifier!.value) {
        return Assets.checkRedIcon;
      }
      if (isInvisible) {
        return Assets.checkRedIcon;
      }
      if (bankAccount.displayConfig.isHidden) {
        return Assets.checkGrayIcon;
      }
      if (!bankAccount.displayConfig.isHidden) {
        return Assets.checkGreenIcon;
      }
      return '';
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        bankType.isSmbc ? const SizedBox(height: 6) : const SizedBox.shrink(),
        GestureDetector(
          onTap: () {
            // FirebaseAnalyticsログ送信
            firebaseController.sendButtonLog(
              buttonName:
                  AppLocalizations.of(context)!.switchAccountDisplayFlag,
              screenName:
                  AppLocalizations.of(context)!.bankAccountDisplayConfig,
            );
            onChangedFlag(
              bankAccount.displayConfig.displayOrder,
              bankAccount.displayConfig.isHidden ? false : true,
            );
          },
          child: SizedBox(
            width: 24,
            height: 24,
            child: SvgPicture.asset(getAssetName()),
          ),
        ),
      ],
    );
  }
}

class _SMBCInfo extends HookConsumerWidget {
  const _SMBCInfo({
    required this.bankAccount,
    required this.hasErrorTextNotifier,
    required this.onChangedName,
  });

  final BankAccount bankAccount;
  final ValueNotifier<bool> hasErrorTextNotifier;
  final void Function(int, String, String?) onChangedName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    TextEditingController? textEditingController;
    final errorTextNotifier = useState<String?>(null);
    final isBranchNameKanjiNull = bankAccount.baseInfo.branchNameKanji == null;
    final style = Theme.of(context).textTheme.bodySmall!.copyWith(
          fontSize: 12,
          height: 1.5,
        );

    useEffect(
      () {
        // 初回読み込み
        textEditingController = TextEditingController(
          text: bankAccount.displayConfig.displayName,
        );
        return null;
      },
      const [],
    );

    String limitString(String text, int maxLength) {
      if (text.length <= maxLength) {
        return text;
      }
      return text.substring(0, maxLength);
    }

    /// 入力内容のバリデーションチェック
    String? validateDisplayName(String? value) {
      if (value != null && value.isNotEmpty) {
        if (value.containsEmoji()) {
          // 絵文字は設定不可
          return AppLocalizations.of(context)!.validationErrorEmoji;
        }
        if (value.length > 15) {
          // 15文字以上は設定不可
          return AppLocalizations.of(context)!.validationErrorLength15;
        }
      }
      return null;
    }

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            height: 36,
            decoration: BoxDecoration(
              color: hasErrorTextNotifier.value
                  ? AppColors.lightRed
                  : AppColors.grey100,
              border: Border.all(
                color: hasErrorTextNotifier.value
                    ? AppColors.red
                    : AppColors.borderGrayGreen,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: NotCopyTextFormField(
              controller: textEditingController,
              style: TextStyle(
                color: hasErrorTextNotifier.value
                    ? AppColors.red
                    : AppColors.textBlack,
                fontSize: 16,
              ),
              onChanged: (text) {
                errorTextNotifier.value = validateDisplayName(text);
                hasErrorTextNotifier.value = errorTextNotifier.value != null;
                onChangedName(
                  bankAccount.displayConfig.displayOrder,
                  text,
                  errorTextNotifier.value,
                );
              },
              onTapOutside: (_) => FocusScope.of(context).unfocus(),
              autovalidateMode: AutovalidateMode.always,
              decoration: InputDecoration(
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                filled: true,
                contentPadding: const EdgeInsets.all(8),
                isDense: true,
                hintText: AppLocalizations.of(context)!.displayNameUndefined,
                hintStyle: const TextStyle(
                  color: AppColors.greyText,
                ),
                errorStyle: const TextStyle(
                  color: AppColors.red,
                ),
              ),
            ),
          ),
          if (errorTextNotifier.value != null &&
              errorTextNotifier.value!.isNotEmpty)
            Column(
              children: [
                const SizedBox(height: 8),
                Row(
                  children: [
                    SvgPicture.asset(
                      Assets.captionListErrorIcon,
                      width: 16,
                      height: 16,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        errorTextNotifier.value!,
                        softWrap: true,
                        overflow: TextOverflow.visible,
                        maxLines: null,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall!
                            .copyWith(color: AppColors.red),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          const SizedBox(height: 8),
          Row(
            children: [
              NullableHyphenText(
                data: bankAccount.baseInfo.bankName,
                style: style,
              ),
              const SizedBox(width: 4),
              isBranchNameKanjiNull
                  ? NullableHyphenText(style: style)
                  : Expanded(
                      child: Text(
                        limitString(
                          bankAccount.baseInfo.branchNameKanji!,
                          15,
                        ),
                        style: style,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            textBaseline: TextBaseline.alphabetic,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            children: [
              NullableHyphenText(
                data: bankAccount.baseInfo.accountType,
                style: style,
              ),
              const SizedBox(width: 4),
              NullableHyphenText(
                data: bankAccount.baseInfo.accountNumber,
                style: style.copyWith(fontFamily: FontFamily.robotoCondensed),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  // 振込依頼人名 nullの場合は空文字で表示（webと表示を統一）
                  bankAccount.baseInfo.remitterName ?? '',
                  style: style,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _OtherBankInfo extends StatelessWidget {
  const _OtherBankInfo({required this.bankAccount});

  final BankAccount bankAccount;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            bankAccount.displayConfig.displayName,
            style: Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(fontWeight: FontWeight.w700),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            bankAccount.baseInfo.bankName ?? '',
            style:
                Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 12),
          ),
        ],
      ),
    );
  }
}

class _ReorderableIcon extends StatelessWidget {
  const _ReorderableIcon({required this.index});

  final int index;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ReorderableDragStartListener(
          index: index,
          child: Padding(
            padding: const EdgeInsets.only(left: 16),
            child: SvgPicture.asset(Assets.navigationIcon),
          ),
        ),
      ],
    );
  }
}

enum BankType {
  smbc,
  otherBank,
}

extension BankTypeExt on BankType {
  bool get isSmbc => this == BankType.smbc;

  bool get isOtherBank => this == BankType.otherBank;
}
