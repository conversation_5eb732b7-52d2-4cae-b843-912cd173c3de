import 'package:dtp_app/business_logics/card_reader/card_reader_screen_notifier.dart';
import 'package:dtp_app/views/screens/splash/widget/splash_view.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BackgroundImageScreen extends StatefulHookConsumerWidget {
  const BackgroundImageScreen({super.key, required this.child});

  final Widget child;

  @override
  BackgroundImageScreenState createState() => BackgroundImageScreenState();
}

class BackgroundImageScreenState extends ConsumerState<BackgroundImageScreen> {
  late final AppLifecycleListener _listener;
  bool _blockScreen = false;

  @override
  void initState() {
    super.initState();

    _listener = AppLifecycleListener(
      onStateChange: (AppLifecycleState state) {
        switch (state) {
          //アプリがフォアグランドの場合はフラグをfalseに
          case AppLifecycleState.resumed:
            setState(() {
              _blockScreen = false;
              ref.watch(cardReaderScreenProvider.notifier).updateIsActive(true);
            });
            break;
          // アプリがバックグラウンド状態の場合はフラグをtrueに
          case AppLifecycleState.inactive:
          case AppLifecycleState.hidden:
          case AppLifecycleState.paused:
          case AppLifecycleState.detached:
            setState(() {
              _blockScreen = true;
              ref
                  .watch(cardReaderScreenProvider.notifier)
                  .updateIsActive(false);
            });
            break;
        }
      },
    );
  }

  @override
  void dispose() {
    _listener.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (_blockScreen) const SplashView(hiddenImage: false),
      ],
    );
  }
}
