import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/before_login/before_login_screen_notifier.dart';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/models/consent_status/consent_status.dart';
import 'package:dtp_app/repositories/operation_log_repository/operation_log_repository_impl.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/my_app_bar.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/components/web_view/web_view_screen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class TermsScreen extends HookConsumerWidget {
  const TermsScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.termsScreenId,
            screenName: AppLocalizations.of(context)!.term,
          );
        });
        return null;
      },
      const [],
    );

    return PopScope(
      // 戻るボタンの無効化
      canPop: false,
      child: ColoredBox(
        color: AppColors.white,
        child: SafeArea(
          top: false,
          child: Scaffold(
            appBar: _AppBar(),
            body: const _Body(),
          ),
        ),
      ),
    );
  }
}

class _AppBar extends HookConsumerWidget implements PreferredSizeWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final actions = [
      GestureDetector(
        onTap: () {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.helpIcon,
            screenName: AppLocalizations.of(context)!.header,
          );
          ref.watch(urlLauncherProvider).launchLinkId(
                LinkIds.faq,
                AppLocalizations.of(context)!.helpIcon,
                ScreenIdNumber.termsScreenId,
              );
        },
        child: SvgPicture.asset(Assets.helpWhiteIcon),
      ),
      const SizedBox(width: 13),
    ];

    return MyAppBar(
      actions: actions,
      // 戻るボタンを非表示
      automaticallyImplyLeading: false,
      screenId: ScreenIdNumber.termsScreenId,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(56);
}

class _Body extends HookWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final deviceSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        Image.asset(
          Assets.bgWaveGrey,
          height: deviceSize.height,
          width: deviceSize.width,
          fit: BoxFit.cover,
        ),
        Column(
          children: [
            ScreenNumber(
              screenNumber: ScreenIdNumber.termsScreenId,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  const _CheckList(),
                  const SizedBox(height: 40),
                  const _AgreeButton(),
                  const SizedBox(height: 16),
                  const _DisagreeButton(),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _CheckList extends HookConsumerWidget {
  const _CheckList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final termOfServiceWebViewUrl =
        ref.read(urlLauncherProvider).getLinkIdUrl(LinkIds.smbcBusinessTerm);
    final aboutPersonalInfoWebViewUrl =
        ref.read(urlLauncherProvider).getLinkIdUrl(LinkIds.aboutPersonalInfo);
    const termOfServiceName = 'DTP';
    const termOfPersonalInfo = 'personalInfo';
    final state = ref.watch(loginScreenProvider);
    final controller = ref.watch(loginScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGray.withOpacity(0.4),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context)!.checkList,
            style: Theme.of(context)
                .textTheme
                .bodyLarge!
                .copyWith(fontSize: 20, color: AppColors.tradGreen),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.contentAgreementMessage,
            style: Theme.of(context).textTheme.bodyMedium!,
          ),
          const SizedBox(height: 24),
          const _Divider(),
          const SizedBox(height: 24),
          _CheckListItem(
            text: AppLocalizations.of(context)!.termOfService,
            url: termOfServiceWebViewUrl,
            isChecked: state.agreeTerms.contains(termOfServiceName),
            consentType: termOfServiceName,
            onLaunchedUrl: () {
              if (state.agreeTerms.contains(termOfServiceName)) {
                return;
              }
              // リンクが参照されたとして対象のチェックマークをONにする
              final checkedTerms = [
                ...state.agreeTerms,
                termOfServiceName,
              ];
              controller.updateConsentStatus(checkedTerms);

              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.termOfService,
                screenName: AppLocalizations.of(context)!.term,
              );
            },
          ),
          _TermMessage(),
          const SizedBox(height: 16),
          _CheckListItem(
            text: AppLocalizations.of(context)!.aboutPersonalInformation,
            url: aboutPersonalInfoWebViewUrl,
            isChecked: state.unsavedAgreeTerms.contains(termOfPersonalInfo),
            consentType: termOfPersonalInfo,
            onLaunchedUrl: () {
              if (state.unsavedAgreeTerms.contains(termOfPersonalInfo)) {
                return;
              }
              // リンクが参照されたとして対象のチェックマークをONにする
              final checkedTerms = [
                ...state.unsavedAgreeTerms,
                termOfPersonalInfo,
              ];
              controller.updateUnsavedConsentStatus(checkedTerms);

              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName:
                    AppLocalizations.of(context)!.aboutPersonalInformation,
                screenName: AppLocalizations.of(context)!.term,
              );
            },
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}

class _CheckListItem extends HookConsumerWidget {
  const _CheckListItem({
    required this.text,
    required this.url,
    required this.isChecked,
    required this.consentType,
    required this.onLaunchedUrl,
  });

  final String text;
  final String url;
  final bool isChecked;
  final String consentType;
  final Function onLaunchedUrl;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SvgPicture.asset(
          isChecked ? Assets.checkGreen2Icon : Assets.checkIcon,
        ),
        const SizedBox(width: 16),
        SizedBox(
          child: GestureDetector(
            onTap: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.pushTermWebViewScreen,
                screenName: AppLocalizations.of(context)!.term,
              );
              // 引数のurlを元にWebViewを開く
              final completer = ref
                  .watch(
                    webViewLauncherProvider,
                  )
                  .call(
                    context,
                    WebViewRequest.launchUrl(
                      initialUrl: url,
                    ),
                    AppLocalizations.of(context)!.term,
                    ScreenIdNumber.termsScreenId,
                  );
              completer.then(
                (_) {
                  onLaunchedUrl();
                },
              );
            },
            child: Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium!
                  .copyWith(color: AppColors.tradGreen),
            ),
          ),
        ),
        const SizedBox(width: 4),
        Padding(
          padding: const EdgeInsets.only(top: 4),
          child: SvgPicture.asset(
            Assets.arrowRightIcon,
            width: 16,
            height: 16,
          ),
        ),
      ],
    );
  }
}

class _TermMessage extends HookConsumerWidget {
  const _TermMessage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 8, left: 40),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: AppLocalizations.of(context)!.explainTerm,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.textBlack,
                      height: 1.5,
                    ),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.unsubscribeHere,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.tradGreen,
                      decoration: TextDecoration.underline,
                      decorationColor: AppColors.primary,
                      height: 1.5,
                    ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    ref.read(urlLauncherProvider).launchLinkId(
                          LinkIds.unsubscribeHere,
                          AppLocalizations.of(context)!.unsubscribeHere,
                          ScreenIdNumber.termsScreenId,
                        );

                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!.unsubscribeHere,
                      screenName: AppLocalizations.of(context)!.term,
                    );
                  },
              ),
              WidgetSpan(
                child: Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: SvgPicture.asset(
                    Assets.cardLinkIcon,
                    width: 16,
                    height: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AgreeButton extends HookConsumerWidget {
  const _AgreeButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(loginScreenProvider);
    final isAgreed = state.agreeTerms.length == agreeTermsNumber &&
        state.unsavedAgreeTerms.length == unsavedAgreeTermsNumber;
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return SizedBox(
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: AppColors.tradGreen,
          disabledBackgroundColor: AppColors.backgroundGreen,
          foregroundColor: AppColors.white,
          disabledForegroundColor:
              AppColors.buttonInactiveText.withOpacity(0.55),
          textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: FontWeight.w700,
              ),
          side: BorderSide(
            color: Colors.transparent,
          ),
        ),
        onPressed: isAgreed
            ? () {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.agree,
                  screenName: AppLocalizations.of(context)!.term,
                );
                // 画面を戻る際に引数を渡す
                Navigator.of(context).pop(state.agreeTerms);
              }
            : null,
        child: Center(
          child: Text(
            AppLocalizations.of(context)!.agree,
          ),
        ),
      ),
    );
  }
}

class _DisagreeButton extends HookConsumerWidget {
  const _DisagreeButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(beforeLoginScreenProvider);
    final loginConroller = ref.watch(loginScreenProvider.notifier);
    final operationLogRepository = ref.watch(
      operationLogRepositoryProvider,
    );
    final operationLogController =
        OperationLogController(operationLogRepository: operationLogRepository);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return GestureDetector(
      onTap: () {
        // 顧客操作ログ送信
        operationLogController.sendOperationLog(
          functionLog: OperationLogMessage.termsOfUse,
          operationLog: OperationLogMessage.disagree,
          resultLog: state.error == null
              ? OperationLogMessage.normal
              : OperationLogMessage.abnormality,
          errorIdLog: state.error?.code ?? '',
        );
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.disagree,
          screenName: AppLocalizations.of(context)!.term,
        );
        // 同意しない押下でチェックを外す
        loginConroller.updateConsentStatus([]);
        loginConroller.updateUnsavedConsentStatus([]);
        // 画面を戻る際に引数は渡さない
        Navigator.of(context).pop();
      },
      child: Text(
        AppLocalizations.of(context)!.disagree,
        style: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: AppColors.tradGreen),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.grey200,
      height: 1,
    );
  }
}
