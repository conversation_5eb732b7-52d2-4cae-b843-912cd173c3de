import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/income_and_expenditure/income_and_expenditure_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/account_transaction_history/transactions.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/ext/deposit_type_ext.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/ext/history_type_ext.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/auto_fold_text.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/income_and_expenditure_detail_modal.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/label_container.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/narrow_down_freee_modal.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/narrow_down_modal.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// ignore: library_prefixes
import 'package:intl/intl.dart' as dateFormat;

// HACK カプセル化を実施しすぎているため、大きなコンポネントごとにまとめるリファクタリングを実施
// HACK 可読性向上のためfreee用、自行用にてそれぞれ入出金明細画面として別のものを用意する
// refactor/narrow_down にてリファクタリングを途中まで実施しているため、リファクタリングの際にはご参考までに
class IncomeAndExpenditureScreen extends HookConsumerWidget
    with CommonErrorHandlerMixin {
  const IncomeAndExpenditureScreen({
    super.key,
    required this.bankAccount,
    required this.serverDate,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final String serverDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final DepositType depositType;
    final appLocalizations = AppLocalizations.of(context)!;
    final balanceDetail = bankAccount.balanceDetail;
    final accountApiType = bankAccount.displayConfig.accountApiType;

    if (balanceDetail.accountType == '普通' ||
        balanceDetail.accountType == '当座') {
      depositType = DepositType.liquidityDeposit;
    } else {
      depositType = DepositType.fixedDeposit;
    }
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          switch (accountApiType) {
            case AccountApiType.web21:
              // 初回読み込み
              controller.loadAnser(
                dateTo: serverDate,
                accountId: balanceDetail.accountId ?? '',
              );
              // Firebase Screenログ・KARTE Viewイベント送信
              analyticsLogController.sendScreenLog(
                screenNumber: ScreenIdNumber.incomeAndExpenditureScreenId,
                screenName: appLocalizations.incomeAndExpenditure,
              );
              break;
            case AccountApiType.freee:
              // 初回読み込み
              controller.loadFreee(
                dateTo: serverDate,
                walletableId: int.parse(balanceDetail.accountId ?? '0'),
                walletableType: FreeeTransactionsType.freeeBankAccount,
              );
              // Firebase Screenログ・KARTE Viewイベント送信
              analyticsLogController.sendScreenLog(
                screenNumber: ScreenIdNumber.incomeAndExpenditureFreeeScreenId,
                screenName: appLocalizations.incomeAndExpenditureLinkedAccount,
              );
              break;
          }
        });
        return null;
      },
      const [],
    );

    // 共通エラーハンドリング
    final List<AppError?> errorList = [state.web21Error, state.freeeError];
    for (final AppError? error in errorList) {
      // 画面の描画ごとに共通エラーが発生していないかを確認
      handleError(
        context,
        ref,
        error,
        screenId: accountApiType == AccountApiType.web21
            ? ScreenIdNumber.incomeAndExpenditureScreenId
            : ScreenIdNumber.incomeAndExpenditureFreeeScreenId,
        screenName: AppLocalizations.of(context)!.incomeAndExpenditure,
        onOkTap: () {
          // セッションタイムアウト用
          // HomeNavigation配下の画面に帰ってくる
          Navigator.pop(context);
        },
      ).then((hasCommonError) {
        if (hasCommonError) {
          // 共通エラーだった場合、ハンドリング後にクリアする
          controller.clearError();
        }
      });
    }

    return Container(
      color: AppColors.tradGreen,
      child: SafeArea(
        bottom: false,
        child: Scaffold(
          backgroundColor: AppColors.basicBackgroundLightGray,
          appBar: _AppBar(accountApiType: accountApiType),
          body: _Body(
            bankAccount: bankAccount,
            serverDate: serverDate,
            accountApiType: accountApiType,
            depositType: depositType,
          ),
        ),
      ),
    );
  }
}

class _AppBar extends HookConsumerWidget implements PreferredSizeWidget {
  const _AppBar({required this.accountApiType});

  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return AppBar(
      backgroundColor: AppColors.white,
      elevation: 0,
      shape: const Border(
        bottom: BorderSide(
          color: AppColors.borderGrayGreen,
        ),
      ),
      leadingWidth: 90,
      leading: GestureDetector(
        onTap: () {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.back,
            screenName: accountApiType == AccountApiType.web21
                ? AppLocalizations.of(context)!.incomeAndExpenditure
                : AppLocalizations.of(context)!
                    .incomeAndExpenditureLinkedAccount,
          );

          Navigator.pop(context);
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                Assets.arrowLeftIcon,
                width: 16,
                height: 16,
              ),
              const SizedBox(width: 4),
              Text(
                strutStyle: StrutStyle(
                  leadingDistribution: TextLeadingDistribution.even,
                  height: 1.5,
                  fontSize: 16,
                ),
                AppLocalizations.of(context)!.back,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                      height: 1.5,
                    ),
              ),
            ],
          ),
        ),
      ),
      title: Text(
        AppLocalizations.of(context)!.incomeAndExpenditure,
        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontSize: 16,
              color: AppColors.tradGreen,
            ),
      ),
      centerTitle: true,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}

class _IsLoading extends StatelessWidget {
  const _IsLoading();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body({
    required this.bankAccount,
    required this.serverDate,
    required this.accountApiType,
    required this.depositType,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final String serverDate;
  final AccountApiType accountApiType;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);

    if (state.web21Error != null) {
      return _Data(
        bankAccount: bankAccount,
        serverDate: serverDate,
        accountApiType: accountApiType,
        error: state.web21Error,
        depositType: depositType,
      );
    }
    if (state.freeeError != null) {
      return _Data(
        bankAccount: bankAccount,
        serverDate: serverDate,
        accountApiType: accountApiType,
        depositType: depositType,
        error: state.freeeError,
      );
    }
    if (accountApiType == AccountApiType.web21) {
      final transactionList = state.accountTransactionHistory.map(
        empty: (_) => const _IsLoading(),
        data: (data) => _Data(
          data: Transactions(
            web21Transactions: data,
          ),
          bankAccount: bankAccount,
          serverDate: serverDate,
          accountApiType: accountApiType,
          depositType: depositType,
          error: state.web21Error,
        ),
      );
      return SafeArea(
        child: Column(
          children: [
            Expanded(
              child: transactionList,
            ),
          ],
        ),
      );
    } else {
      final freeeTransactionList = state.freeeTransactionHistory.map(
        empty: (_) => const _IsLoading(),
        data: (data) => _Data(
          data: Transactions(
            freeeTransactions: data,
          ),
          bankAccount: bankAccount,
          serverDate: serverDate,
          accountApiType: accountApiType,
          error: state.freeeError,
          depositType: DepositType.fixedDeposit,
        ),
      );
      return SafeArea(
        child: Column(
          children: [
            Expanded(
              child: freeeTransactionList,
            ),
          ],
        ),
      );
    }
  }
}

class _Data extends HookConsumerWidget {
  const _Data({
    this.data,
    required this.bankAccount,
    required this.serverDate,
    required this.accountApiType,
    required this.depositType,
    this.error,
  });

  final Transactions? data;
  final BankAccountWithBalanceDetail bankAccount;
  final String serverDate;
  final AppError? error;
  final AccountApiType accountApiType;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // TODO 要件を確認し、上限が不要であれば削除
    const maxDisplayCount = 100;
    final isNoResult = accountApiType == AccountApiType.web21
        ? data?.web21Transactions?.items.isEmpty ?? false
        : data?.freeeTransactions?.items.isEmpty ?? false;
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final BankAccountBalanceDetail balanceDetail = bankAccount.balanceDetail;

    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        children: [
          ColoredBox(
            color: AppColors.white,
            child: ScreenNumber(
              screenNumber: accountApiType == AccountApiType.web21
                  ? ScreenIdNumber.incomeAndExpenditureScreenId
                  : ScreenIdNumber.incomeAndExpenditureFreeeScreenId,
              color: AppColors.textBlack,
            ),
          ),
          _FetchedDateTime(
            value: accountApiType == AccountApiType.web21
                ? error == null
                    ?
                    // SMBCの入出金明細の正常系の際はdata内部の日時を使用
                    data?.web21Transactions?.baseDateTime
                    // SMBCの入出金明細の異常系の際はAppError内の日時を使用
                    : error!.baseDateTime
                // freeeの場合は最終同期日時を表示
                : bankAccount.balanceDetail.lastSyncedAt,
          ),
          _AccountInformationDetail(
            bankAccount: bankAccount,
            accountApiType: accountApiType,
          ),
          _TradingDate(
            accountId: balanceDetail.accountId ?? '',
            serverDate: serverDate,
            apiType: accountApiType,
          ),
          const SizedBox(height: 24),
          error == null
              // 正常系の場合は通常表示
              ? Column(
                  children: [
                    _DepositCreditType(
                      apiType: accountApiType,
                      accountId: balanceDetail.accountId,
                      serverDate: serverDate,
                    ),
                    const SizedBox(height: 24),
                    accountApiType == AccountApiType.web21
                        ? _NarrowDownButton(
                            serverDate: serverDate,
                            items: data!.web21Transactions!.items
                                .take(maxDisplayCount)
                                .toList(),
                            freeeItems: const [],
                            accountId: balanceDetail.accountId,
                            walletableId: 0,
                            accountApiType: accountApiType,
                            depositType: depositType,
                          )
                        : _NarrowDownButton(
                            serverDate: serverDate,
                            items: const [],
                            freeeItems: data?.freeeTransactions?.items
                                    .take(maxDisplayCount)
                                    .toList() ??
                                [],
                            accountId: '',
                            walletableId: int.parse(
                              balanceDetail.accountId ?? '0',
                            ),
                            accountApiType: accountApiType,
                            depositType: DepositType.fixedDeposit,
                          ),
                    const SizedBox(height: 20),
                    _BalanceSummary(
                      income: accountApiType == AccountApiType.web21
                          ? data!.web21Transactions!.totalIncome?.withCommas ??
                              AppLocalizations.of(context)!.fullWidthHyphen

                          ///【暫定対応】freeeに関しては入出金合計に-が表示されることはない
                          : data!.freeeTransactions!
                              .getTotalIncome()
                              .withCommas,
                      expenditure: accountApiType == AccountApiType.web21
                          ? data!.web21Transactions!.totalExpenditure
                                  ?.withCommas ??
                              AppLocalizations.of(context)!.fullWidthHyphen
                          : data!.freeeTransactions!
                              .getTotalExpenditure()
                              .withCommas,
                      items: data!,
                      accountApiType: accountApiType,
                    ),
                    const SizedBox(height: 22),
                    _SearchArea(
                      apiType: accountApiType,
                      accountId: balanceDetail.accountId,
                      serverDate: serverDate,
                    ),
                    const SizedBox(height: 22),
                    accountApiType == AccountApiType.web21
                        ? _IncomeAndExpenditureList(
                            accountApiType: accountApiType,
                            onWeb21DetailClicked: (item) {
                              // FirebaseAnalyticsログ送信
                              analyticsLogController.sendButtonLog(
                                buttonName:
                                    AppLocalizations.of(context)!.detail,
                                screenName: AppLocalizations.of(context)!
                                    .incomeAndExpenditure,
                              );

                              // 詳細表示
                              showModalBottomSheet(
                                isDismissible: false,
                                enableDrag: false,
                                isScrollControlled: true,
                                useSafeArea: true,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(8),
                                  ),
                                ),
                                context: context,
                                builder: (BuildContext context) {
                                  return IncomeAndExpenditureDetailModal(
                                    item: item,
                                    bankAccount: bankAccount,
                                  );
                                },
                              );
                            },
                            items: data!,
                          )
                        : _IncomeAndExpenditureList(
                            accountApiType: accountApiType,
                            items: data!,
                            onWeb21DetailClicked: (_) {
                              // FirebaseAnalyticsログ送信
                              analyticsLogController.sendButtonLog(
                                buttonName:
                                    AppLocalizations.of(context)!.detail,
                                screenName: AppLocalizations.of(context)!
                                    .incomeAndExpenditureLinkedAccount,
                              );
                            },
                          ),
                  ],
                )
              // 異常系
              : accountApiType == AccountApiType.web21
                  ? _ErrorDisplayArea(
                      errorCode: error!.code ?? '',
                      errorMessage: error!.message ?? '',
                      hasFaq: error!.hasFaq,
                    )
                  : _FreeeErrorDisplayArea(error: error!),
          if (isNoResult) const _NoResultBody(),
        ],
      ),
    );
  }
}

class _FetchedDateTime extends StatelessWidget {
  const _FetchedDateTime({
    this.value,
  });

  final String? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.white,
      padding: const EdgeInsets.only(left: 16, top: 8),
      child: Row(
        children: [
          Text(
            AppLocalizations.of(context)!.fetchedDateTime,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: AppColors.textGrayGreen,
                ),
          ),
          const SizedBox(width: 8),
          NullableHyphenText(
            data: value,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.textGrayGreen,
                ),
          ),
        ],
      ),
    );
  }
}

class _DepositCreditType extends StatelessWidget {
  const _DepositCreditType({
    required this.accountId,
    required this.serverDate,
    required this.apiType,
  });

  final String? accountId;
  final String serverDate;
  final AccountApiType apiType;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36,
      width: double.infinity,
      margin: const EdgeInsets.only(left: 16, right: 17),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.grey200,
        ),
        borderRadius: const BorderRadius.all(
          Radius.circular(4),
        ),
      ),
      child: Row(
        children: [
          _CreditType(
            apiType: apiType,
            accountId: accountId,
            serverDate: serverDate,
            index: 0,
            filterType: TransactionHistoryFilterType.all,
            title: AppLocalizations.of(context)!.all,
          ),
          const _CreditTypeDivider(),
          _CreditType(
            apiType: apiType,
            accountId: accountId,
            serverDate: serverDate,
            index: 1,
            filterType: TransactionHistoryFilterType.income,
            title: AppLocalizations.of(context)!.income,
          ),
          const _CreditTypeDivider(),
          _CreditType(
            apiType: apiType,
            accountId: accountId,
            serverDate: serverDate,
            index: 2,
            filterType: TransactionHistoryFilterType.expenditure,
            title: AppLocalizations.of(context)!.expenditure,
          ),
        ],
      ),
    );
  }
}

/// 入金・出金・全てのフィルター
class _CreditType extends HookConsumerWidget {
  const _CreditType({
    required this.accountId,
    required this.serverDate,
    required this.index,
    required this.filterType,
    required this.title,
    required this.apiType,
  });

  final String? accountId;
  final String serverDate;
  final int index;
  final TransactionHistoryFilterType filterType;
  final String title;
  final AccountApiType apiType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final largeAmountText =
        state.isNarrowDown ? controller.largeAmountController.text : '';
    final smallAmountText =
        state.isNarrowDown ? controller.smallAmountController.text : '';
    final referenceNumberText =
        state.isNarrowDown ? controller.referenceNumberController.text : '';
    final clientNameText =
        state.isNarrowDown ? controller.clientNameController.text : '';

    int? toNumber(String text) {
      if (text.isNotEmpty && int.tryParse(text) != null) {
        // 数値に変換できる場合の処理
        return int.parse(text);
      } else {
        return null;
      }
    }

    double? toNumberFreee(String text) {
      if (text.isNotEmpty && int.tryParse(text) != null) {
        // 数値に変換できる場合の処理
        return double.parse(text);
      } else {
        return null;
      }
    }

    return Expanded(
      child: GestureDetector(
        onTap: () {
          if (apiType == AccountApiType.web21) {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName:
                  AppLocalizations.of(context)!.incomeAndExpenditureRefinement,
              screenName: AppLocalizations.of(context)!.incomeAndExpenditure,
            );

            controller.getAccountTransactionHistory(
              accountId: accountId ?? '',
              dateFrom: state.dateFrom!,
              dateTo: state.dateTo!,
              yearMonth: state.selectedYearMonth!,
              selectedYearMonthIndex: state.selectedYearMonthIndex,
              filterType: filterType,
              sortType: state.currentSortType,
              order: state.currentSortOrder,
              sortTitle: state.sortTitle,
              startingDateFrom: state.dateSegment == DateSegment.startingDate &&
                      state.isNarrowDown
                  ? state.fromSelectedDay
                  : null,
              startingDateTo: state.dateSegment == DateSegment.startingDate &&
                      state.isNarrowDown
                  ? state.toSelectedDay
                  : null,
              tradingDateFrom: state.dateSegment == DateSegment.tradingDate &&
                      state.isNarrowDown
                  ? state.fromSelectedDay
                  : null,
              tradingDateTo: state.dateSegment == DateSegment.tradingDate &&
                      state.isNarrowDown
                  ? state.toSelectedDay
                  : null,
              transactionTypeCode:
                  state.transactionCodes.isNotEmpty && state.isNarrowDown
                      ? state.transactionCodes
                      : null,
              payerName: clientNameText.isNotEmpty && state.isNarrowDown
                  ? clientNameText
                  : null,
              minAmount: toNumber(smallAmountText.replaceAll(',', '')),
              maxAmount: toNumber(largeAmountText.replaceAll(',', '')),
              referenceNumber:
                  referenceNumberText.isNotEmpty && state.isNarrowDown
                      ? referenceNumberText
                      : null,
            );
          } else {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName:
                  AppLocalizations.of(context)!.incomeAndExpenditureRefinement,
              screenName: AppLocalizations.of(context)!
                  .incomeAndExpenditureLinkedAccount,
            );

            controller.getFreeeTransactionHistory(
              walletableId: int.parse(accountId ?? '0'),
              walletableType: FreeeTransactionsType.freeeBankAccount,
              filterType: filterType,
              dateFrom: state.dateFrom!,
              dateTo: state.dateTo!,
              tradingDateFrom: state.fromSelectedDay,
              tradingDateTo: state.toSelectedDay,
              yearMonth: state.selectedYearMonth!,
              selectedYearMonthIndex: state.selectedYearMonthIndex,
              sortTitle: state.sortTitle,
              order: state.currentSortOrder,
              sortType: state.currentSortType,
              minAmount: toNumberFreee(smallAmountText.replaceAll(',', '')),
              maxAmount: toNumberFreee(largeAmountText.replaceAll(',', '')),
            );
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: state.currentFilterType == filterType
                ? AppColors.tradGreen
                : AppColors.white,
            borderRadius: BorderRadius.only(
              topLeft: index == 0 ? const Radius.circular(4) : Radius.zero,
              bottomLeft: index == 0 ? const Radius.circular(4) : Radius.zero,
              topRight: index == 2 ? const Radius.circular(4) : Radius.zero,
              bottomRight: index == 2 ? const Radius.circular(4) : Radius.zero,
            ),
          ),
          child: Center(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w700,
                    color: state.currentFilterType == filterType
                        ? AppColors.white
                        : AppColors.textGrayGreen,
                  ),
            ),
          ),
        ),
      ),
    );
  }
}

class _CreditTypeDivider extends StatelessWidget {
  const _CreditTypeDivider();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      width: 1,
      color: AppColors.grey200,
    );
  }
}

class _NarrowDownButton extends HookConsumerWidget {
  const _NarrowDownButton({
    required this.serverDate,
    required this.items,
    required this.freeeItems,
    required this.accountId,
    required this.walletableId,
    required this.accountApiType,
    required this.depositType,
  });

  final String serverDate;
  final List<FreeeTransactionHistoryDetail> freeeItems;
  final List<AccountTransactionHistoryDetail> items;
  final String? accountId;
  final int walletableId;
  final AccountApiType accountApiType;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 17),
        child: Container(
          width: 124,
          height: 36,
          color: AppColors.white,
          child: OutlinedButton(
            onPressed: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.narrowDown,
                screenName: accountApiType == AccountApiType.web21
                    ? AppLocalizations.of(context)!.incomeAndExpenditure
                    : AppLocalizations.of(context)!
                        .incomeAndExpenditureLinkedAccount,
              );

              showModalBottomSheet(
                isDismissible: false,
                enableDrag: false,
                isScrollControlled: true,
                useSafeArea: true,
                backgroundColor: AppColors.white,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(8),
                  ),
                ),
                context: context,
                builder: (BuildContext context) {
                  return accountApiType == AccountApiType.web21
                      ? NarrowDownModal(
                          serverDate: serverDate,
                          items: items,
                          accountId: accountId,
                          accountApiType: accountApiType,
                          depositType: depositType,
                        )
                      : NarrowDownFreeeModal(
                          serverDate: serverDate,
                          items: freeeItems,
                          walletableId: walletableId,
                          accountApiType: accountApiType,
                        );
                },
              );
            },
            style: OutlinedButton.styleFrom(
              side: const BorderSide(
                color: AppColors.tradGreen,
              ),
            ),
            child: Row(
              children: [
                SizedBox(
                  width: 24,
                  height: 24,
                  child: SvgPicture.asset(Assets.filterIcon),
                ),
                SizedBox(
                  width: 64,
                  height: 24,
                  child: Text(
                    AppLocalizations.of(context)!.narrowDown,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 16,
                          color: AppColors.tradGreen,
                          fontWeight: FontWeight.w700,
                        ),
                  ),
                ),
                Align(
                  alignment: const Alignment(1, -0.44),
                  child: state.isNarrowDown
                      ? Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: AppColors.orange,
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _BalanceSummary extends StatelessWidget {
  const _BalanceSummary({
    required this.income,
    required this.expenditure,
    required this.items,
    required this.accountApiType,
  });

  final String income;
  final String expenditure;
  final Transactions items;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context) {
    final itemCount = accountApiType == AccountApiType.web21
        ? items.web21Transactions!.items.length.toString()
        : items.freeeTransactions!.items.length.toString();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                AppLocalizations.of(context)!.periodTotal,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.textGrayGreen,
                      fontWeight: FontWeight.w700,
                    ),
              ),
              const SizedBox(width: 8),
              Row(
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: itemCount,
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                    fontSize: 24,
                                    fontWeight: FontWeight.w700,
                                  ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 4),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: AppLocalizations.of(context)!.itemCountUnit,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          Column(
            children: [
              SizedBox(height: 4),
              Table(
                defaultColumnWidth: const IntrinsicColumnWidth(),
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: [
                  TableRow(
                    children: [
                      Text(
                        AppLocalizations.of(context)!.income,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        income,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                            ),
                        maxLines: 1,
                        textAlign: TextAlign.end,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context)!.yenJp,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontSize: 12,
                            ),
                      ),
                    ],
                  ),
                  TableRow(
                    children: [
                      Text(
                        AppLocalizations.of(context)!.expenditure,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        expenditure,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                            ),
                        maxLines: 1,
                        textAlign: TextAlign.end,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context)!.yenJp,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontSize: 12,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _TradingDate extends HookConsumerWidget {
  const _TradingDate({
    required this.accountId,
    required this.serverDate,
    required this.apiType,
  });

  final String accountId;
  final String serverDate;
  final AccountApiType apiType;

  String _format(DateTime? dateTime) =>
      dateTime != null ? dateFormat.DateFormat('yyyy年M月').format(dateTime) : '';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final scrollController = useScrollController();
    final itemCount = state.yearMonthList.length;
    final lastIndex = itemCount - 1;
    final deviceHeight = MediaQuery.of(context).size.height;

    return Row(
      children: [
        GestureDetector(
          onTap: () async {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.selectYearMonth,
              screenName: apiType == AccountApiType.web21
                  ? AppLocalizations.of(context)!.incomeAndExpenditure
                  : AppLocalizations.of(context)!
                      .incomeAndExpenditureLinkedAccount,
            );
            await showDialog(
              context: context,
              builder: (_) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  // 選択した年月がダイアログのスクロール領域に隠れないように制御
                  if (state.selectedYearMonthIndex >= 7) {
                    scrollController
                        .jumpTo(scrollController.position.maxScrollExtent);
                  }
                });

                return AlertDialog(
                  insetPadding: const EdgeInsets.symmetric(horizontal: 16),
                  contentPadding: const EdgeInsets.symmetric(vertical: 0),
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(8),
                    ),
                  ),
                  content: Container(
                    // 高解像度の端末（基準高さ932）は表示期間の幅を広げる
                    height: deviceHeight >= 932 ? 580 : 375,
                    width: 343,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: ListView.builder(
                        controller: scrollController,
                        itemCount: itemCount,
                        itemBuilder: (context, index) {
                          final yearMonth = state.yearMonthList[index];
                          return Column(
                            children: [
                              if (index == 0) const SizedBox(height: 12),
                              _YearMonthItem(
                                accountId: accountId,
                                yearMonth: yearMonth,
                                index: index,
                                serverDate: serverDate,
                                apiType: apiType,
                              ),
                              if (index == lastIndex)
                                const SizedBox(height: 12),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            );
          },
          child: Padding(
            padding: const EdgeInsets.only(left: 16, top: 32),
            child: Row(
              children: [
                Text(
                  _format(state.selectedYearMonth),
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        fontSize: 24,
                        color: AppColors.tradGreen,
                      ),
                ),
                const SizedBox(width: 8),
                SvgPicture.asset(
                  height: 24,
                  width: 24,
                  Assets.arrowDownBottomIcon,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _YearMonthItem extends HookConsumerWidget {
  const _YearMonthItem({
    required this.accountId,
    required this.yearMonth,
    required this.index,
    required this.serverDate,
    required this.apiType,
  });

  final String accountId;
  final DateTime yearMonth;
  final int index;
  final String serverDate;
  final AccountApiType apiType;

  String _format(DateTime? dateTime) =>
      dateTime != null ? dateFormat.DateFormat('yyyy年M月').format(dateTime) : '';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isSelected = state.selectedYearMonth == yearMonth;

    return GestureDetector(
      onTap: () async {
        final isSame = controller.checkSameYearMonth(
          yearMonth: yearMonth,
          selectedYearMonth: state.selectedYearMonth!,
        );
        if (isSame) {
          Navigator.pop(context);
          return;
        }
        controller.changeIsFromDialog(false);
        controller.changeIsToDialog(false);
        controller.reset();

        final isCurrentMonth = controller.checkCurrentMonth(
          yearMonth: yearMonth,
          serverDate: serverDate,
        );

        final dateFrom = DateTime(yearMonth.year, yearMonth.month, 1);
        DateTime dateTo;
        bool forceRefresh;
        if (isCurrentMonth) {
          dateTo = dateFormat.DateFormat('yyyy-M-d')
              .parse(serverDate.replaceAll('/', '-'));
          forceRefresh = true;
        } else {
          dateTo = DateTime(yearMonth.year, yearMonth.month + 1, 0);
          forceRefresh = false;
        }

        LoadingDialog.loading(context);
        if (apiType == AccountApiType.web21) {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.selectYearMonthList,
            screenName: AppLocalizations.of(context)!.incomeAndExpenditure,
          );

          await controller.getAccountTransactionHistory(
            accountId: accountId,
            dateFrom: dateFrom,
            dateTo: dateTo,
            yearMonth: yearMonth,
            selectedYearMonthIndex: index,
            filterType: state.currentFilterType,
            sortType: state.currentSortType,
            order: state.currentSortOrder,
            sortTitle: state.sortTitle,
            forceRefresh: forceRefresh,
          );
        } else {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.selectYearMonthList,
            screenName:
                AppLocalizations.of(context)!.incomeAndExpenditureLinkedAccount,
          );

          await controller.getFreeeTransactionHistory(
            sortType: state.currentSortType,
            walletableId: int.parse(accountId),
            // 現状クレジットカードなどは表示していないため、他行口座にて固定
            walletableType: FreeeTransactionsType.freeeBankAccount,
            dateFrom: dateFrom,
            dateTo: dateTo,
            yearMonth: yearMonth,
            selectedYearMonthIndex: index,
            sortTitle: state.sortTitle,
          );
        }
        if (!context.mounted) return;
        LoadingDialog.loadingEnd(context);
        Navigator.pop(context);
      },
      child: Container(
        height: 48,
        width: double.infinity,
        padding: const EdgeInsets.only(left: 16),
        alignment: Alignment.centerLeft,
        color: isSelected ? AppColors.lightGreen2 : AppColors.white,
        child: Text(
          _format(yearMonth),
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                color: isSelected ? AppColors.tradGreen : AppColors.textBlack,
              ),
        ),
      ),
    );
  }
}

class _SearchArea extends StatelessWidget {
  const _SearchArea({
    required this.accountId,
    required this.serverDate,
    required this.apiType,
  });

  final String? accountId;
  final String serverDate;
  final AccountApiType apiType;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _SortType(
          apiType: apiType,
          accountId: accountId,
          serverDate: serverDate,
        ),
        const SizedBox(width: 16),
      ],
    );
  }
}

class _SortType extends HookConsumerWidget {
  const _SortType({
    required this.accountId,
    required this.serverDate,
    required this.apiType,
  });

  final String? accountId;
  final String serverDate;
  final AccountApiType apiType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sortTitle = ref.watch(
      incomeAndExpenditureScreenProvider.select((value) => value.sortTitle),
    );
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return GestureDetector(
      onTap: () async {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.selectSortType,
          screenName: apiType == AccountApiType.web21
              ? AppLocalizations.of(context)!.incomeAndExpenditure
              : AppLocalizations.of(context)!.incomeAndExpenditureLinkedAccount,
        );
        await showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
              insetPadding: const EdgeInsets.symmetric(horizontal: 16),
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(8),
                ),
              ),
              content: SizedBox(
                width: 343,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _SortItem(
                      apiType: apiType,
                      accountId: accountId,
                      serverDate: serverDate,
                      title: AppLocalizations.of(context)!
                          .orderByNewTransactionDate,
                      sortType: TransactionHistorySortType.date,
                      sortOrder: TransactionHistorySortOrder.descending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByNewTransactionDate,
                    ),
                    _SortItem(
                      apiType: apiType,
                      accountId: accountId,
                      serverDate: serverDate,
                      title: AppLocalizations.of(context)!
                          .orderByOldTransactionDate,
                      sortType: TransactionHistorySortType.date,
                      sortOrder: TransactionHistorySortOrder.ascending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByOldTransactionDate,
                    ),
                    _SortItem(
                      apiType: apiType,
                      accountId: accountId,
                      serverDate: serverDate,
                      title:
                          AppLocalizations.of(context)!.orderByDescendingAmount,
                      sortType: TransactionHistorySortType.amount,
                      sortOrder: TransactionHistorySortOrder.descending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByDescendingAmount,
                    ),
                    _SortItem(
                      apiType: apiType,
                      accountId: accountId,
                      serverDate: serverDate,
                      title:
                          AppLocalizations.of(context)!.orderByAscendingAmount,
                      sortType: TransactionHistorySortType.amount,
                      sortOrder: TransactionHistorySortOrder.ascending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByAscendingAmount,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
      child: Row(
        children: [
          SvgPicture.asset(Assets.pulldownArrowIcon),
          const SizedBox(width: 4),
          Text(
            sortTitle.isEmpty
                ? AppLocalizations.of(context)!.orderByNewTransactionDate
                : sortTitle,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.tradGreen,
                ),
          ),
        ],
      ),
    );
  }
}

class _SortItem extends HookConsumerWidget {
  const _SortItem({
    required this.accountId,
    required this.serverDate,
    required this.title,
    required this.sortType,
    required this.sortOrder,
    required this.apiType,
    required this.buttonName,
  });

  final String? accountId;
  final String serverDate;
  final String title;
  final TransactionHistorySortType sortType;
  final TransactionHistorySortOrder sortOrder;
  final AccountApiType apiType;
  final String buttonName;

  int? toNumber(String text) {
    if (text.isNotEmpty && int.tryParse(text) != null) {
      // 数値に変換できる場合の処理
      return int.parse(text);
    } else {
      return null;
    }
  }

  double? toNumberFreee(String text) {
    if (text.isNotEmpty && int.tryParse(text) != null) {
      // 数値に変換できる場合の処理
      return double.parse(text);
    } else {
      return null;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isSelected = state.currentSortType == sortType &&
        state.currentSortOrder == sortOrder;
    final clientNameText = controller.clientNameController.text;
    final largeAmountText = controller.largeAmountController.text;
    final smallAmountText = controller.smallAmountController.text;
    final referenceNumberText = controller.referenceNumberController.text;

    return GestureDetector(
      onTap: () {
        if (apiType == AccountApiType.web21) {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: buttonName,
            screenName: AppLocalizations.of(context)!.incomeAndExpenditure,
          );

          controller.getAccountTransactionHistory(
            accountId: accountId ?? '',
            dateFrom: state.dateFrom!,
            dateTo: state.dateTo!,
            yearMonth: state.selectedYearMonth!,
            selectedYearMonthIndex: state.selectedYearMonthIndex,
            filterType: state.currentFilterType,
            sortType: sortType,
            order: sortOrder,
            sortTitle: title,
            startingDateFrom: state.dateSegment == DateSegment.startingDate
                ? state.fromSelectedDay
                : null,
            startingDateTo: state.dateSegment == DateSegment.startingDate
                ? state.toSelectedDay
                : null,
            tradingDateFrom: state.dateSegment == DateSegment.tradingDate
                ? state.fromSelectedDay
                : null,
            tradingDateTo: state.dateSegment == DateSegment.tradingDate
                ? state.toSelectedDay
                : null,
            transactionTypeCode: state.transactionCodes.isNotEmpty
                ? state.transactionCodes
                : null,
            payerName: clientNameText.isNotEmpty ? clientNameText : null,
            minAmount: toNumber(smallAmountText.replaceAll(',', '')),
            maxAmount: toNumber(largeAmountText.replaceAll(',', '')),
            referenceNumber:
                referenceNumberText.isNotEmpty ? referenceNumberText : null,
          );
        } else {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: buttonName,
            screenName:
                AppLocalizations.of(context)!.incomeAndExpenditureLinkedAccount,
          );
          controller.getFreeeTransactionHistory(
            walletableId: int.parse(accountId ?? '0'),
            walletableType: FreeeTransactionsType.freeeBankAccount,
            filterType: state.currentFilterType,
            dateFrom: state.dateFrom!,
            dateTo: state.dateTo!,
            tradingDateFrom: state.fromSelectedDay ?? state.dateFrom,
            tradingDateTo: state.toSelectedDay ?? state.dateTo,
            yearMonth: state.selectedYearMonth!,
            selectedYearMonthIndex: state.selectedYearMonthIndex,
            sortTitle: title,
            order: sortOrder,
            sortType: sortType,
            minAmount: toNumberFreee(smallAmountText.replaceAll(',', '')),
            maxAmount: toNumberFreee(largeAmountText.replaceAll(',', '')),
          );
        }
        Navigator.pop(context);
      },
      child: Container(
        height: 48,
        width: double.infinity,
        padding: const EdgeInsets.only(left: 16),
        alignment: Alignment.centerLeft,
        color: isSelected ? AppColors.lightGreen2 : AppColors.white,
        child: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                color: isSelected ? AppColors.tradGreen : AppColors.textBlack,
              ),
        ),
      ),
    );
  }
}

class _IncomeAndExpenditureList extends StatelessWidget {
  const _IncomeAndExpenditureList({
    this.onWeb21DetailClicked,
    required this.items,
    required this.accountApiType,
  });

  final Transactions items;
  final void Function(AccountTransactionHistoryDetail item)?
      onWeb21DetailClicked;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context) {
    List<AccountTransactionHistoryDetail> web21Details = [];
    List<FreeeTransactionHistoryDetail> freeeDetails = [];
    int itemCount = 0;
    if (accountApiType == AccountApiType.web21) {
      web21Details = items.web21Transactions!.items;
      itemCount = web21Details.length;
    } else {
      freeeDetails = items.freeeTransactions!.items;
      itemCount = freeeDetails.length;
    }

    return Container(
      color: AppColors.grey100,
      child: Column(
        children: [
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: itemCount,
            itemBuilder: (context, index) =>
                accountApiType == AccountApiType.web21
                    ? _IncomeAndExpenditureListItem(
                        item: web21Details[index],
                        accountApiType: accountApiType,
                        onDetailClicked: () =>
                            onWeb21DetailClicked!(web21Details[index]),
                      )
                    : _FreeeIncomeAndExpenditureListItem(
                        item: freeeDetails[index],
                        accountApiType: accountApiType,
                      ),
          ),
        ],
      ),
    );
  }
}

class _IncomeAndExpenditureListItem extends StatelessWidget {
  const _IncomeAndExpenditureListItem({
    required this.item,
    required this.onDetailClicked,
    required this.accountApiType,
  });

  final AccountTransactionHistoryDetail item;
  final void Function() onDetailClicked;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: const [
            BoxShadow(
              color: AppColors.incomeAndExpenditureBackgroundColor,
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                item.type.when(
                  income: const LabelContainer.income(),
                  expenditure: const LabelContainer.expenditure(),
                ),
                const SizedBox(width: 18),
                item.depositType.when(
                  fixed: const SizedBox.shrink(),
                  liquidity: Expanded(
                    child: NullableHyphenText(
                      data: item.payerName?.ellipsis(maxLength: 31),
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.bodyMedium!,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                item.amount == null
                    ? Text(AppLocalizations.of(context)!.fullWidthHyphen)
                    : _Amount(item.amount!),
                Column(
                  children: [
                    const SizedBox(width: 24),
                    Row(
                      children: [
                        const SizedBox(width: 24),
                        SizedBox(
                          height: 40,
                          child: OutlinedButton(
                            onPressed: onDetailClicked,
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(
                                color: AppColors.tradGreen,
                              ),
                            ),
                            child: Text(
                              AppLocalizations.of(context)!.detail,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    color: AppColors.tradGreen,
                                    fontWeight: FontWeight.w700,
                                    height: 1.5,
                                  ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Divider(
              color: AppColors.borderGrayGreen,
              height: 0,
              thickness: 1,
            ),
            const SizedBox(height: 12),
            Table(
              defaultVerticalAlignment: TableCellVerticalAlignment.bottom,
              children: [
                TableRow(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.startingDate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    NullableHyphenText(
                      data: item.startingDate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontSize: 12,
                          ),
                    ),
                    Text(
                      AppLocalizations.of(context)!.tradingDate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    NullableHyphenText(
                      data: item.tradingDate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontSize: 12,
                          ),
                    ),
                  ],
                ),
                TableRow(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.tradingType,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    NullableHyphenText(
                      data: item.transactionType?.name.ellipsis(maxLength: 6),
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 12,
                          ),
                    ),
                    item.depositType.when(
                      fixed: Text(
                        AppLocalizations.of(context)!.depositNumber,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: AppColors.textGrayGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                        maxLines: 1,
                      ),
                      liquidity: Text(
                        AppLocalizations.of(context)!.referenceNumber,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: AppColors.textGrayGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                      ),
                    ),
                    NullableHyphenText(
                      data: item.referenceNumber,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontSize: 12,
                          ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _FreeeIncomeAndExpenditureListItem extends StatelessWidget {
  const _FreeeIncomeAndExpenditureListItem({
    required this.item,
    required this.accountApiType,
  });

  final FreeeTransactionHistoryDetail item;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: const [
            BoxShadow(
              color: AppColors.incomeAndExpenditureBackgroundColor,
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                item.type == FreeeTransactionHistoryType.income
                    ? const LabelContainer.income()
                    : const LabelContainer.expenditure(),
                const SizedBox(width: 18),
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      item.amount == null
                          ? Text(AppLocalizations.of(context)!.fullWidthHyphen)
                          : Row(
                              children: [
                                RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text: item.amount!.withCommas,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontFamily:
                                                  FontFamily.robotoCondensed,
                                              fontSize: 20,
                                            ),
                                      ),
                                      const WidgetSpan(
                                        child: SizedBox(width: 4),
                                      ),
                                      TextSpan(
                                        text:
                                            AppLocalizations.of(context)!.yenJp,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Divider(
              color: AppColors.borderGrayGreen,
              height: 0,
              thickness: 1,
            ),
            const SizedBox(height: 12),
            Table(
              columnWidths: const {0: FixedColumnWidth(64)},
              defaultVerticalAlignment: TableCellVerticalAlignment.top,
              children: [
                TableRow(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.tradingDate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    NullableHyphenText(
                      data: item.tradingDate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontSize: 12,
                          ),
                    ),
                  ],
                ),
                TableRow(
                  children: [
                    Text(
                      AppLocalizations.of(context)!.tradingContent,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    AutoFoldText(
                      text: item.description ?? 'ー',
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _AccountInformationDetail extends HookConsumerWidget {
  const _AccountInformationDetail({
    required this.bankAccount,
    required this.accountApiType,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final BankAccountBalanceDetail balanceDetail = bankAccount.balanceDetail;
    final isCurrentBalanceNull = balanceDetail.currentBalance == null;

    return Container(
      color: AppColors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, top: 12, right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                accountApiType == AccountApiType.web21
                    ? Column(
                        children: [
                          Row(
                            children: [
                              SizedBox(
                                height: 22,
                                width: 62,
                                child: Image.asset(
                                  Assets.smbcLogoTagS,
                                  fit: BoxFit.cover,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                bankAccount.displayConfig.displayName,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      fontWeight: FontWeight.w700,
                                    ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: AutoFoldText(
                              text: balanceDetail.displayAccountName ?? '',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                accountApiType == AccountApiType.web21
                    ? const SizedBox.shrink()
                    : Column(
                        children: [
                          const SizedBox(height: 4),
                          // 金融機関名 ph0.5リリース時点では他行の場合は表示しない
                          Text(
                            balanceDetail.contactName ?? '',
                          ),
                        ],
                      ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 15, bottom: 24),
                      child: Text(
                        AppLocalizations.of(context)!.balance,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: AppColors.textGrayGreen,
                              fontWeight: FontWeight.w700,
                            ),
                      ),
                    ),
                    isCurrentBalanceNull
                        ? Text(AppLocalizations.of(context)!.fullWidthHyphen)
                        : Row(
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              Text(
                                balanceDetail.currentBalance! >= 0
                                    ? balanceDetail.currentBalance!.withCommas
                                    : '-${balanceDetail.currentBalance!.withCommas}',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge!
                                    .copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      fontSize: 28,
                                      fontWeight: FontWeight.w700,
                                    ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                AppLocalizations.of(context)!.yenJp,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                      fontWeight: FontWeight.w700,
                                    ),
                              ),
                            ],
                          ),
                  ],
                ),
                accountApiType == AccountApiType.web21
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (!state.isAccordionExpanded)
                            Column(
                              children: [
                                Divider(
                                  color: AppColors.borderGrayGreen,
                                  height: 0,
                                  thickness: 1,
                                ),
                                const SizedBox(height: 11),
                                TextButton(
                                  onPressed: () {
                                    // FirebaseAnalyticsログ送信
                                    analyticsLogController.sendButtonLog(
                                      buttonName: AppLocalizations.of(context)!
                                          .openAccountInformationDetails,
                                      screenName: AppLocalizations.of(context)!
                                          .incomeAndExpenditure,
                                    );
                                    controller.openAccordion();
                                  },
                                  style: ButtonStyle(
                                    padding: WidgetStateProperty.all(
                                      EdgeInsets.zero,
                                    ),
                                    minimumSize:
                                        WidgetStateProperty.all(Size.zero),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        strutStyle: StrutStyle(
                                          height: 1.5,
                                          fontSize: 12,
                                        ),
                                        AppLocalizations.of(context)!
                                            .accountInformationDetails,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(
                                              color: AppColors.tradGreen,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                              height: 1.5,
                                            ),
                                      ),
                                      const SizedBox(
                                        width: 4,
                                      ),
                                      SvgPicture.asset(
                                        Assets.arrowDownIcon,
                                        width: 16,
                                        height: 16,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 11),
                              ],
                            ),
                          Container(
                            alignment: Alignment.topLeft,
                            height: state.isAccordionExpanded ? 112 : 0,
                            child: DefaultTextStyle.merge(
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    color: AppColors.textGrayGreen,
                                  ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  NullableHyphenText(
                                    data: balanceDetail.bankName,
                                  ),
                                  const SizedBox(height: 4),
                                  NullableHyphenText(
                                    data: balanceDetail.branchName,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      NullableHyphenText(
                                        data: balanceDetail.accountType,
                                      ),
                                      const SizedBox(width: 8),
                                      NullableHyphenText(
                                        data: balanceDetail.accountNumber,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(
                                              fontFamily:
                                                  FontFamily.robotoCondensed,
                                              color: AppColors.textGrayGreen,
                                            ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    // 振込依頼人名 nullの場合は空文字で表示（webと表示を統一）
                                    bankAccount.baseInfo.remitterName ?? '',
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          // AccountApiType == AccountApiType.web21
                          if (state.isAccordionExpanded)
                            Column(
                              children: [
                                Divider(
                                  color: AppColors.borderGrayGreen,
                                  height: 0,
                                  thickness: 1,
                                ),
                                const SizedBox(height: 11),
                                TextButton(
                                  onPressed: () {
                                    // FirebaseAnalyticsログ送信
                                    analyticsLogController.sendButtonLog(
                                      buttonName: AppLocalizations.of(context)!
                                          .closeAccountInformationDetails,
                                      screenName: AppLocalizations.of(context)!
                                          .incomeAndExpenditure,
                                    );
                                    controller.closeAccordion();
                                  },
                                  style: ButtonStyle(
                                    padding: WidgetStateProperty.all(
                                      EdgeInsets.zero,
                                    ),
                                    minimumSize:
                                        WidgetStateProperty.all(Size.zero),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)!
                                            .accountInformationDetails,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(
                                              color: AppColors.tradGreen,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w700,
                                            ),
                                      ),
                                      const SizedBox(width: 4),
                                      SvgPicture.asset(
                                        Assets.arrowUpIcon,
                                        width: 16,
                                        height: 16,
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 11),
                              ],
                            ),
                        ],
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 自行口座エラー時の画面表示
class _ErrorDisplayArea extends StatelessWidget {
  const _ErrorDisplayArea({
    required this.errorCode,
    required this.errorMessage,
    required this.hasFaq,
  });
  final String errorCode;
  final String errorMessage;
  final bool hasFaq;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          border: Border.all(color: AppColors.red),
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                errorCode,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.grayGreen,
                    ),
              ),
              const SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SvgPicture.asset(
                    Assets.alertIcon,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      errorMessage,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            color: AppColors.red,
                          ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Visibility(
                visible: hasFaq,
                child: Column(
                  children: [
                    FaqContent(
                      linkId: LinkIds.faqTop,
                      buttonName: AppLocalizations.of(context)!.here,
                      screenId: ScreenIdNumber.incomeAndExpenditureScreenId,
                      screenName:
                          AppLocalizations.of(context)!.incomeAndExpenditure,
                      icon: Assets.actionIcon,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// freeeエラー時の画面表示
class _FreeeErrorDisplayArea extends HookConsumerWidget {
  const _FreeeErrorDisplayArea({
    required this.error,
  });

  final AppError error;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isErrorInfoForReAlignment =
        ErrorInfo.freeeNeedsReAlignmentErrorCode.contains(error.code);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.red),
          borderRadius: BorderRadius.circular(4),
          color: AppColors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                error.code ?? '',
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.grayGreen,
                    ),
              ),
              Container(
                padding: const EdgeInsets.only(top: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    SvgPicture.asset(Assets.alertIcon),
                    const SizedBox(width: 8),
                    Expanded(
                      child: SizedBox(
                        child: Text(
                          error.message ?? '',
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: AppColors.red,
                                    fontWeight: FontWeight.w700,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Column(
                children: [
                  Visibility(
                    visible: error.hasFaq,
                    child: Column(
                      children: [
                        FaqContent(
                          linkId: LinkIds.faqTop,
                          buttonName: AppLocalizations.of(context)!.here,
                          screenId:
                              ScreenIdNumber.incomeAndExpenditureFreeeScreenId,
                          screenName: AppLocalizations.of(context)!
                              .incomeAndExpenditure,
                          icon: Assets.actionIcon,
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: isErrorInfoForReAlignment,
                    child: Column(
                      children: [
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32),
                          child: SizedBox(
                            width: double.infinity,
                            child: AppRectangleButton(
                              label:
                                  AppLocalizations.of(context)!.reConnectFreee,
                              textColor: AppColors.white,
                              buttonColor: AppColors.tradGreen,
                              onPressed: () {
                                // FirebaseAnalyticsログ送信
                                analyticsLogController.sendButtonLog(
                                  buttonName: AppLocalizations.of(context)!
                                      .reConnectFreeeButton,
                                  screenName: AppLocalizations.of(context)!
                                      .incomeAndExpenditureLinkedAccount,
                                );
                                // Todo freee再連携ボタン押下時の挙動を記載
                              },
                              svgPicture:
                                  SvgPicture.asset(Assets.actionIconWhite),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 該当する入出金明細が０件だったときに表示するウィジェット
class _NoResultBody extends StatelessWidget {
  const _NoResultBody();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        bottom: 16,
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 64),
              color: AppColors.white,
              child: Center(
                child: Text(
                  AppLocalizations.of(context)!.noTransactionHistoryFound,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 金額の表示部
class _Amount extends StatelessWidget {
  const _Amount(this.amount);

  final int amount;

  @override
  Widget build(BuildContext context) {
    const maxDigit = 12;
    if (amount.toString().length > maxDigit) {
      return Text(
        BankAccountErrorInfo.overflowDigitError,
        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontFamily: FontFamily.notoSansJP,
            ),
      );
    }
    return Row(
      children: [
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: amount.withCommas,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      fontSize: 20,
                    ),
              ),
              const WidgetSpan(
                child: SizedBox(width: 4),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.yenJp,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontSize: 12,
                      fontWeight: FontWeight.w700,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
