import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/income_and_expenditure/income_and_expenditure_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:dtp_app/utils/ext/string_validator.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/narrow_down_zero_modal.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

class NarrowDownModal extends HookConsumerWidget {
  const NarrowDownModal({
    super.key,
    required this.serverDate,
    required this.items,
    required this.accountId,
    required this.accountApiType,
    required this.depositType,
  });

  final String serverDate;
  final List<AccountTransactionHistoryDetail> items;
  final String? accountId;
  final AccountApiType accountApiType;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.checkFirstDayAndLastDayCalendar(getFirstDate(serverDate));

          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.incomeAndExpenditureNarrowDownScreenId,
            screenName:
                AppLocalizations.of(context)!.incomeAndExpenditureNarrowDown,
          );
        });
        return null;
      },
      [],
    );

    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
          screenName:
              AppLocalizations.of(context)!.incomeAndExpenditureNarrowDown,
        );
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const _Header(),
            const SizedBox(height: 24),
            _ScrollArea(
              serverDate: serverDate,
              items: items,
              depositType: depositType,
            ),
            _Footer(
              items: items,
              accountId: accountId,
              serverDate: serverDate,
              accountApiType: accountApiType,
              depositType: depositType,
            ),
          ],
        ),
      ),
    );
  }
}

class _Header extends HookConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              const SizedBox(height: 24),
              Text(
                AppLocalizations.of(context)!.narrowDown,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.tradGreen,
                      height: 1.5,
                    ),
              ),
            ],
          ),
          IconButton(
            splashColor: AppColors.transparent,
            highlightColor: AppColors.transparent,
            iconSize: 32,
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(),
            onPressed: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.close,
                screenName: AppLocalizations.of(context)!
                    .incomeAndExpenditureNarrowDown,
              );

              Navigator.of(context).pop();
            },
            icon: SvgPicture.asset(
              Assets.buttonClose,
            ),
          ),
        ],
      ),
    );
  }
}

class _Footer extends HookConsumerWidget {
  const _Footer({
    required this.items,
    required this.accountId,
    required this.serverDate,
    required this.accountApiType,
    required this.depositType,
  });

  final List<AccountTransactionHistoryDetail> items;
  final String? accountId;
  final String serverDate;
  final AccountApiType accountApiType;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    int? toNumber(String text) {
      if (text.isNotEmpty && int.tryParse(text) != null) {
        // 数値に変換できる場合の処理
        return int.parse(text);
      } else {
        return null;
      }
    }

    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.narrowDownShadow,
            offset: Offset(0.0, -2.0),
            blurRadius: 4.0,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              style: CommonButtonStyles.textButtonStyle,
              onPressed: () async {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.reset,
                  screenName: AppLocalizations.of(context)!
                      .incomeAndExpenditureNarrowDown,
                );

                controller.reset();
                controller
                    .checkFirstDayAndLastDayCalendar(getFirstDate(serverDate));
                // reset処理にてstateを更新しているため、最新の状態を取得する
                final state = ref.read(incomeAndExpenditureScreenProvider);
                // 実際はキャッシュに対する絞り込みの為、ローディングは描画されないものの
                // 連打対策のためローディングを実施
                LoadingDialog.loading(context);
                // リセットボタン押下時に明細画面を絞り込みなしに初期化
                // ※明細画面にてセットされている入出金種別、ソート順に関してはリセットしない
                await controller.narrowDown(
                  accountId: accountId ?? '',
                  dateFrom: state.dateFrom!,
                  dateTo: state.dateTo!,
                  forceRefresh: false,
                  filterType: state.currentFilterType,
                  sortType: state.currentSortType,
                  order: state.currentSortOrder,
                  isNarrowDown: false,
                );
                if (context.mounted) LoadingDialog.loadingEnd(context);
              },
              child: Text(
                AppLocalizations.of(context)!.reset,
                style: const TextStyle(color: AppColors.tradGreen),
              ),
            ),
            const SizedBox(width: 27),
            Flexible(
              child: SizedBox(
                height: 48,
                width: 240,
                child: AppRectangleButton(
                  label: AppLocalizations.of(context)!.narrowingDown,
                  textColor: (controller.checkInvalid())
                      ? AppColors.inactiveText
                      : AppColors.white,
                  buttonColor: (controller.checkInvalid())
                      ? AppColors.backgroundGreen
                      : AppColors.tradGreen,
                  onPressed: (controller.checkInvalid())
                      ? null
                      : () async {
                          // 最新の情報を取得するため、描画時ではなく絞り込み時に金額を取得する
                          final largeAmountText =
                              controller.largeAmountController.text;
                          final smallAmountText =
                              controller.smallAmountController.text;
                          final referenceNumberText =
                              controller.referenceNumberController.text;
                          final depositNumberText =
                              controller.depositNumberController.text;
                          final clientNameText =
                              controller.clientNameController.text;
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName:
                                AppLocalizations.of(context)!.narrowingDown,
                            screenName: AppLocalizations.of(context)!
                                .incomeAndExpenditureNarrowDown,
                          );

                          // 実際はキャッシュに対する絞り込みの為、ローディングは描画されないものの
                          // 連打対策のためローディングを実施
                          LoadingDialog.loading(context);
                          await controller.narrowDown(
                            accountId: accountId ?? '',
                            dateFrom: state.dateFrom!,
                            dateTo: state.dateTo!,
                            forceRefresh: false,
                            filterType: state.currentFilterType,
                            sortType: state.currentSortType,
                            order: state.currentSortOrder,
                            startingDateFrom:
                                state.dateSegment == DateSegment.startingDate
                                    ? state.fromSelectedDay
                                    : null,
                            startingDateTo:
                                state.dateSegment == DateSegment.startingDate
                                    ? state.toSelectedDay
                                    : null,
                            tradingDateFrom:
                                state.dateSegment == DateSegment.tradingDate
                                    ? state.fromSelectedDay
                                    : null,
                            tradingDateTo:
                                state.dateSegment == DateSegment.tradingDate
                                    ? state.toSelectedDay
                                    : null,
                            transactionTypeCode:
                                state.transactionCodes.isNotEmpty
                                    ? state.transactionCodes
                                    : null,
                            payerName: clientNameText.isNotEmpty
                                ? clientNameText
                                : null,
                            minAmount: toNumber(
                              smallAmountText.replaceAll(',', ''),
                            ),
                            maxAmount: toNumber(
                              largeAmountText.replaceAll(',', ''),
                            ),
                            // 三項演算子がネストしているので、もっと綺麗な書き方があるかも？
                            referenceNumber: referenceNumberText.isNotEmpty
                                ? referenceNumberText
                                : depositNumberText.isNotEmpty
                                    ? depositNumberText
                                    : null,
                          );
                          if (context.mounted) {
                            LoadingDialog.loadingEnd(context);
                          }

                          ref
                              .watch(incomeAndExpenditureScreenProvider)
                              .accountTransactionHistory
                              .map(
                            empty: (empty) {
                              showModalBottomSheet(
                                isDismissible: false,
                                enableDrag: false,
                                isScrollControlled: true,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(8),
                                  ),
                                ),
                                context: context,
                                builder: (BuildContext context) {
                                  return NarrowDownZeroModal(
                                    serverDate: serverDate,
                                    items: items,
                                    freeeItems: const [],
                                    accountId: accountId,
                                    walletableId: 0,
                                    accountApiType: accountApiType,
                                    depositType: depositType,
                                  );
                                },
                              );
                            },
                            data: (data) {
                              if (data.items.isEmpty) {
                                Navigator.pop(context);
                                showModalBottomSheet(
                                  isDismissible: false,
                                  enableDrag: false,
                                  isScrollControlled: true,
                                  shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(8),
                                    ),
                                  ),
                                  context: context,
                                  builder: (BuildContext context) {
                                    return NarrowDownZeroModal(
                                      serverDate: serverDate,
                                      items: items,
                                      freeeItems: const [],
                                      accountId: accountId,
                                      walletableId: 0,
                                      accountApiType: accountApiType,
                                      depositType: depositType,
                                    );
                                  },
                                );
                              } else {
                                Navigator.pop(context);
                              }
                            },
                          );
                        },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  const _ScrollArea({
    required this.serverDate,
    required this.items,
    required this.depositType,
  });

  final String serverDate;
  final List<AccountTransactionHistoryDetail> items;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = ScrollController();
    final bottomSpace = MediaQuery.of(context).viewInsets.bottom;
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final TextStyle headingStyle =
        Theme.of(context).textTheme.bodySmall!.copyWith(
              fontSize: 14,
              color: AppColors.textGrayGreen,
              fontWeight: FontWeight.w700,
              height: 1.5,
            );
    final TextStyle itemTextStyle =
        Theme.of(context).textTheme.bodyMedium!.copyWith(
              height: 1.5,
            );

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // スクロールの開始地点をモーダルのトップにする
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
        return () => scrollController.dispose();
      },
      [],
    );

    return Expanded(
      child: SizedBox(
        child: RawScrollbar(
          thumbVisibility: true,
          thumbColor: AppColors.basicBackgroundColor,
          thickness: 6,
          radius: const Radius.circular(3.0),
          controller: scrollController,
          child: SingleChildScrollView(
            controller: scrollController,
            physics: const ClampingScrollPhysics(),
            reverse: true,
            child: Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, bottomSpace),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _DateSegment(
                      serverDate: serverDate,
                      headingStyle: headingStyle,
                      itemTextStyle: itemTextStyle,
                    ),
                    _TradingType(
                      headingStyle: headingStyle,
                      itemTextStyle: itemTextStyle,
                    ),
                    depositType == DepositType.liquidityDeposit
                        ? _ClientName(
                            headingStyle: headingStyle,
                            itemTextStyle: itemTextStyle,
                          )
                        : const SizedBox.shrink(),
                    _Amount(
                      headingStyle: headingStyle,
                      itemTextStyle: itemTextStyle,
                    ),
                    depositType == DepositType.liquidityDeposit
                        ? _ReferenceNumber(
                            headingStyle: headingStyle,
                            itemTextStyle: itemTextStyle,
                          )
                        : _DepositNumber(
                            headingStyle: headingStyle,
                            itemTextStyle: itemTextStyle,
                          ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _DateSegment extends HookConsumerWidget {
  const _DateSegment({
    required this.serverDate,
    required this.headingStyle,
    required this.itemTextStyle,
  });

  final String serverDate;
  final TextStyle headingStyle;
  final TextStyle itemTextStyle;
  String _format(DateTime? dateTime) =>
      dateTime != null ? DateFormat('yyyy/M/d').format(dateTime) : '';

  DateTime _toDateTime(String serverDate) {
    return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.dateSegment,
          style: headingStyle,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            GestureDetector(
              onTap: () {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.notSpecified,
                  screenName: AppLocalizations.of(context)!
                      .incomeAndExpenditureNarrowDown,
                );
                controller.changeDateSegment(DateSegment.notSpecified);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: state.dateSegment == DateSegment.notSpecified
                        ? SvgPicture.asset(Assets.selectedCircleIcon)
                        : SvgPicture.asset(Assets.circleIcon),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)!.notSpecified,
                    style: itemTextStyle,
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.startingDate,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.changeDateSegment(DateSegment.startingDate);
                },
                child: Row(
                  children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: state.dateSegment == DateSegment.startingDate
                          ? SvgPicture.asset(Assets.selectedCircleIcon)
                          : SvgPicture.asset(Assets.circleIcon),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocalizations.of(context)!.startingDate,
                      style: itemTextStyle,
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.tradingDate,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.changeDateSegment(DateSegment.tradingDate);
                },
                child: Row(
                  children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: state.dateSegment == DateSegment.tradingDate
                          ? SvgPicture.asset(Assets.selectedCircleIcon)
                          : SvgPicture.asset(Assets.circleIcon),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      child: Text(
                        AppLocalizations.of(context)!.tradingDate,
                        style: itemTextStyle,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.period,
          style: headingStyle,
        ),
        const SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
              child: _PeriodContainer(
                initialDate: _format(state.fromSelectedDay),
                serverDate: serverDate,
                firstDay: state.fromFirstDay ?? _toDateTime(serverDate),
                lastDay: state.fromLastDay ?? _toDateTime(serverDate),
                selectedDay: state.fromSelectedDay ?? _toDateTime(serverDate),
                changeSelectedDay: (selectedDay) {
                  controller.changeFromSelectedDay(selectedDay);
                },
                changeDialogFlag: (isFlag) =>
                    controller.changeIsFromDialog(isFlag),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SvgPicture.asset(Assets.tildeIcon),
            ),
            Expanded(
              child: _PeriodContainer(
                initialDate: _format(state.toSelectedDay),
                serverDate: serverDate,
                firstDay: state.toFirstDay ?? _toDateTime(serverDate),
                lastDay: state.toLastDay ?? _toDateTime(serverDate),
                selectedDay: state.toSelectedDay ?? _toDateTime(serverDate),
                changeSelectedDay: (selectedDay) {
                  controller.changeToSelectedDay(selectedDay);
                },
                changeDialogFlag: (isFlag) =>
                    controller.changeIsToDialog(isFlag),
              ),
            ),
          ],
        ),
        const _Divider(),
      ],
    );
  }
}

class _TradingType extends HookConsumerWidget {
  const _TradingType({required this.headingStyle, required this.itemTextStyle});
  final TextStyle headingStyle;
  final TextStyle itemTextStyle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.tradingType,
          style: headingStyle,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.cash,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.cash,
                    state.isCash,
                  );
                  controller.changeCash();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isCash
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.cash,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 40),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.payment,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.transfer,
                    state.isPayment,
                  );
                  controller.changePayment();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isPayment
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.payment,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.exchange,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.exchange,
                    state.isExchange,
                  );
                  controller.changeExchange();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isExchange
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.exchange,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 40),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.transfer,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.transfer2,
                    state.isTransfer,
                  );
                  controller.changeTransfer();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isTransfer
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.transfer,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName:
                        AppLocalizations.of(context)!.otherBanksTicketDeposit,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.otherBankCheck,
                    state.isOtherBanksTicket,
                  );
                  controller.changeOtherBanksTicket();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isOtherBanksTicket
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.otherBanksTicketDeposit,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 40),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.correction,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.correction,
                    state.isCorrection,
                  );
                  controller.changeCorrection();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isCorrection
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.correction,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.densai,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.densai,
                    state.isDensai,
                  );
                  controller.changeDensai();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isDensai
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.densai,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 40),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.continuation,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.continuation,
                    state.isContinuation,
                  );
                  controller.changeContinuation();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isContinuation
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.continuation,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.sonota,
                    screenName: AppLocalizations.of(context)!
                        .incomeAndExpenditureNarrowDown,
                  );
                  controller.judgeCheckBox(
                    TransactionTypeCode.other,
                    state.isSonota,
                  );
                  controller.changeSonota();
                },
                child: SizedBox(
                  height: 24,
                  child: Row(
                    children: [
                      state.isSonota
                          ? SvgPicture.asset(Assets.checkGreenIcon)
                          : SvgPicture.asset(Assets.checkGrayIcon),
                      const SizedBox(width: 8),
                      Text(
                        AppLocalizations.of(context)!.sonota,
                        style: itemTextStyle,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const _Divider(),
      ],
    );
  }
}

class _ClientName extends HookConsumerWidget {
  const _ClientName({
    required this.headingStyle,
    required TextStyle itemTextStyle,
  });
  final TextStyle headingStyle;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientNameError = ref.watch(
      incomeAndExpenditureScreenProvider
          .select((value) => value.clientNameError),
    );
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.clientName,
          style: headingStyle,
        ),
        const SizedBox(height: 16),
        _CustomTextFormField(
          width: double.infinity,
          height: 48,
          controller: controller.clientNameController,
          inputFormatters: [FilteringTextInputFormatter.singleLineFormatter],
          hintText: AppLocalizations.of(context)!.example(
            AppLocalizations.of(context)!.wakakusaHanako,
          ),
          textAlign: TextAlign.left,
          validation: clientNameError,
          validator: (String? value) {
            _validator(
              value: value,
              clear: controller.validateClientName,
              condition: value!.containsNonHalfWidth(),
              function: controller.validateClientName,
            );
            return null;
          },
          keyboardType: TextInputType.text,
          maxLength: 48,
          fontFamily: FontFamily.notoSansJP,
        ),
        const SizedBox(height: 8),
        if (clientNameError)
          _ValidationMessage(
            text: AppLocalizations.of(context)!.enterHalf,
          ),
        Text(
          AppLocalizations.of(context)!
              .caution(AppLocalizations.of(context)!.enterHalf),
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 12,
                color: AppColors.textBlack,
                height: 1.5,
              ),
        ),
        const _Divider(),
      ],
    );
  }
}

class _Amount extends HookConsumerWidget {
  const _Amount({required this.headingStyle, required TextStyle itemTextStyle});
  final TextStyle headingStyle;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final smallAmountController = controller.smallAmountController;
    final largeAmountController = controller.largeAmountController;
    final smallAmountFocus = useFocusNode();
    final largeAmountFocus = useFocusNode();

    // フォーカスが外れた時に3桁のカンマ区切りで表示
    void formatAmountWithCommas({required TextEditingController controller}) {
      final numericValue = int.tryParse(controller.text.replaceAll(',', ''));
      if (numericValue != null) {
        final formattedValue = NumberFormat('#,###').format(numericValue);
        controller.text = formattedValue;
      }
    }

    smallAmountFocus.addListener(() {
      if (!smallAmountFocus.hasFocus) {
        formatAmountWithCommas(controller: smallAmountController);
      }
    });

    largeAmountFocus.addListener(() {
      if (!largeAmountFocus.hasFocus) {
        formatAmountWithCommas(controller: largeAmountController);
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.amount,
          style: headingStyle,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                maxLength: 12,
                width: double.infinity,
                height: 48,
                controller: smallAmountController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                ],
                hintText: AppLocalizations.of(context)!
                    .example(AppLocalizations.of(context)!.zero),
                textAlign: TextAlign.right,
                validation: state.digitSmallAmountError ||
                    state.positiveSmallAmountError ||
                    state.correlationSmallAmountError,
                validator: (String? value) {
                  if (smallAmountController.text.isEmpty) {
                    controller.clearSmallAmountError();
                  }
                  if (value != null && value.isNotEmpty) {
                    // 数字以外を含んでいるか判定
                    controller.validateDigitAmount(
                      value: value.containsNonDigit(),
                      isSmallAmount: true,
                    );
                    // マイナスを含んでいるか判定
                    controller.validatePositiveAmount(
                      value: value.containsMinus(),
                      isSmallAmount: true,
                    );
                    // 数字以外を含んでいる or マイナスを含んでいる場合は金額の相関判定に行く前に処理を中断
                    if (smallAmountController.text.containsNonDigit() ||
                        smallAmountController.text.containsMinus() ||
                        largeAmountController.text.containsNonDigit() ||
                        largeAmountController.text.containsMinus()) {
                      return;
                    }
                    // 金額の相関判定
                    if (!value.containsNonDigit() && !value.containsMinus()) {
                      if (controller.largeAmountController.text.isEmpty ||
                          largeAmountFocus.hasFocus) {
                        return;
                      }

                      final isCorrect = value.checkAmountCorrelation(
                        smallAmount: controller.smallAmountController.text,
                        largeAmount: controller.largeAmountController.text,
                      );
                      controller.validateCorrelationAmount(
                        smallAmount: isCorrect,
                        largeAmount: isCorrect,
                      );
                    }
                  }
                  return null;
                },
                focusNode: smallAmountFocus,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${AppLocalizations.of(context)!.yenJp} 〜',
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.textGrayGreen, height: 1.5),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                maxLength: 12,
                width: double.infinity,
                height: 48,
                controller: controller.largeAmountController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                ],
                hintText: AppLocalizations.of(context)!.example(
                  AppLocalizations.of(context)!.fiveHundredMillion,
                ),
                textAlign: TextAlign.right,
                validation: state.digitLargeAmountError ||
                    state.positiveLargeAmountError ||
                    state.correlationLargeAmountError,
                validator: (String? value) {
                  if (largeAmountController.text.isEmpty) {
                    controller.clearLargeAmountError();
                  }
                  if (value != null && value.isNotEmpty) {
                    // 数字以外を含んでいるか判定
                    controller.validateDigitAmount(
                      value: value.containsNonDigit(),
                    );
                    // マイナスを含んでいるか判定
                    controller.validatePositiveAmount(
                      value: value.containsMinus(),
                    );
                    // 数字以外を含んでいる or マイナスを含んでいる場合は金額の相関判定に行く前に処理を中断
                    if (smallAmountController.text.containsNonDigit() ||
                        smallAmountController.text.containsMinus() ||
                        largeAmountController.text.containsNonDigit() ||
                        largeAmountController.text.containsMinus()) {
                      return;
                    }
                    // 金額の相関判定
                    if (!value.containsNonDigit() && !value.containsMinus()) {
                      if (controller.smallAmountController.text.isEmpty ||
                          smallAmountFocus.hasFocus) {
                        return;
                      }

                      final isCorrect = value.checkAmountCorrelation(
                        smallAmount: controller.smallAmountController.text,
                        largeAmount: controller.largeAmountController.text,
                      );
                      controller.validateCorrelationAmount(
                        smallAmount: isCorrect,
                        largeAmount: isCorrect,
                      );
                    }
                  }
                  return null;
                },
                focusNode: largeAmountFocus,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${AppLocalizations.of(context)!.yenJp} 　',
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.textGrayGreen, height: 1.5),
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (state.digitSmallAmountError || state.digitLargeAmountError)
          _ValidationMessage(text: AppLocalizations.of(context)!.enterDigit),
        if (state.positiveSmallAmountError || state.positiveLargeAmountError)
          _ValidationMessage(
            text:
                AppLocalizations.of(context)!.enterNumberGreaterThanOrEqualTo0,
          ),
        if (state.correlationSmallAmountError ||
            state.correlationLargeAmountError)
          _ValidationMessage(
            text: AppLocalizations.of(context)!
                .setAnAmountGreaterThanTheLowerLimit,
            hasSizedBox: false,
          ),
        const _Divider(),
      ],
    );
  }
}

class _ReferenceNumber extends HookConsumerWidget {
  const _ReferenceNumber({
    required this.headingStyle,
    required TextStyle itemTextStyle,
  });
  final TextStyle headingStyle;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final referenceNumberError = ref.watch(
      incomeAndExpenditureScreenProvider
          .select((value) => value.referenceNumberError),
    );
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.referenceNumber,
          style: headingStyle,
        ),
        const SizedBox(height: 16),
        _CustomTextFormField(
          width: double.infinity,
          height: 48,
          controller: controller.referenceNumberController,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          hintText: AppLocalizations.of(context)!
              .example(AppLocalizations.of(context)!.oneToEight),
          textAlign: TextAlign.left,
          validation: referenceNumberError,
          validator: (String? value) {
            _validator(
              value: value,
              clear: controller.validateReferenceNumber,
              condition: value!.isNotEightDigitNumber(),
              function: controller.validateReferenceNumber,
            );
            return null;
          },
          maxLength: 8,
        ),
        const SizedBox(height: 8),
        if (referenceNumberError)
          _ValidationMessage(
            text: AppLocalizations.of(context)!.enter8HalfWidthDigits,
          ),
        Text(
          AppLocalizations.of(context)!
              .caution(AppLocalizations.of(context)!.enterEightHalfNumbers),
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 12,
                color: AppColors.textBlack,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 40),
      ],
    );
  }
}

class _DepositNumber extends HookConsumerWidget {
  const _DepositNumber({
    required this.headingStyle,
    required TextStyle itemTextStyle,
  });
  final TextStyle headingStyle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final depositNumberError = ref.watch(
      incomeAndExpenditureScreenProvider
          .select((value) => value.depositNumberError),
    );
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.depositNumber,
          style: headingStyle,
        ),
        const SizedBox(height: 16),
        _CustomTextFormField(
          width: double.infinity,
          height: 48,
          controller: controller.depositNumberController,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          hintText: AppLocalizations.of(context)!
              .example(AppLocalizations.of(context)!.oneToEight),
          textAlign: TextAlign.left,
          validation: depositNumberError,
          validator: (String? value) {
            _validator(
              value: value,
              clear: controller.validateDepositNumber,
              condition: value!.isNotEightDigitNumber(),
              function: controller.validateDepositNumber,
            );
            return null;
          },
          maxLength: 8,
        ),
        const SizedBox(height: 8),
        if (depositNumberError)
          _ValidationMessage(
            text: AppLocalizations.of(context)!.enter8HalfWidthDigits,
          ),
        Text(
          AppLocalizations.of(context)!
              .caution(AppLocalizations.of(context)!.enterEightHalfNumbers),
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 12,
                color: AppColors.textBlack,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 40),
      ],
    );
  }
}

class _ValidationMessage extends StatelessWidget {
  const _ValidationMessage({
    required this.text,
    this.hasSizedBox = true,
  });

  final String text;
  final bool hasSizedBox;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SvgPicture.asset(
              Assets.captionListErrorIcon,
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.red),
            ),
          ],
        ),
        hasSizedBox ? const SizedBox(height: 8) : const SizedBox.shrink(),
      ],
    );
  }
}

class _PeriodContainer extends HookConsumerWidget {
  const _PeriodContainer({
    required this.initialDate,
    required this.serverDate,
    required this.firstDay,
    required this.lastDay,
    required this.selectedDay,
    required this.changeSelectedDay,
    required this.changeDialogFlag,
  });

  final String initialDate;
  final String serverDate;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime selectedDay;
  final void Function(DateTime dateTime) changeSelectedDay;
  final void Function(bool isFlag) changeDialogFlag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.selectDate,
          screenName:
              AppLocalizations.of(context)!.incomeAndExpenditureNarrowDown,
        );
        if (state.dateSegment != DateSegment.notSpecified) {
          showDialog(
            context: context,
            builder: (context) {
              return _CalendarDialog(
                lastDate: initialDate,
                serverDate: serverDate,
                firstDay: firstDay,
                lastDay: lastDay,
                selectedDay: selectedDay,
                changeSelectedDay: changeSelectedDay,
                changeDialogFlag: changeDialogFlag,
              );
            },
          );
        }
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: state.dateSegment == DateSegment.notSpecified
              ? AppColors.borderGrayGreen
              : AppColors.basicBackgroundLightGray,
          borderRadius: const BorderRadius.all(
            Radius.circular(4.0),
          ),
          border: Border.all(
            color: AppColors.borderGrayGreen,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                initialDate,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.textGrayGreen,
                    ),
              ),
              SvgPicture.asset(Assets.calendarGrayIcon),
            ],
          ),
        ),
      ),
    );
  }
}

class _CustomTextFormField extends StatelessWidget {
  const _CustomTextFormField({
    required this.width,
    required this.height,
    required this.controller,
    required this.inputFormatters,
    required this.hintText,
    required this.textAlign,
    required this.validation,
    this.validator,
    this.focusNode,
    this.keyboardType = TextInputType.number,
    this.maxLength,
    this.fontFamily = FontFamily.robotoCondensed,
  });

  final double width;
  final double height;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;
  final String hintText;
  final TextAlign textAlign;
  final bool validation;
  final String? Function(String?)? validator;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final int? maxLength;
  final String fontFamily;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: validation
            ? AppColors.lightRed
            : AppColors.basicBackgroundLightGray,
        border: Border.all(
          color: validation ? AppColors.red : AppColors.borderGrayGreen,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: NotCopyTextFormField(
                controller: controller,
                focusNode: focusNode,
                maxLength: maxLength,
                textAlign: textAlign,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: fontFamily,
                      color: validation ? AppColors.red : AppColors.textBlack,
                    ),
                keyboardType: keyboardType,
                inputFormatters: inputFormatters,
                decoration: InputDecoration(
                  hintText: hintText,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  counterText: '',
                ),
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: validator,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24),
      child: Container(
        color: AppColors.borderGrayGreen,
        height: 1,
      ),
    );
  }
}

class _CalendarDialog extends StatefulHookConsumerWidget {
  const _CalendarDialog({
    required this.lastDate,
    required this.serverDate,
    required this.firstDay,
    required this.lastDay,
    required this.selectedDay,
    required this.changeSelectedDay,
    required this.changeDialogFlag,
  });

  final String lastDate;
  final String serverDate;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime selectedDay;
  final void Function(DateTime dateTime) changeSelectedDay;
  final void Function(bool isFlag) changeDialogFlag;

  @override
  _CalendarDialogState createState() => _CalendarDialogState();
}

class _CalendarDialogState extends ConsumerState<_CalendarDialog> {
  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final serverDay =
        DateFormat('yyyy-M-d').parse(widget.serverDate.replaceAll('/', '-'));
    const CalendarFormat calendarFormat = CalendarFormat.month;
    final focusedDay = useState(widget.selectedDay);
    final selectedDay = useState(widget.selectedDay);

    return AlertDialog(
      contentPadding: const EdgeInsets.all(0),
      content: SizedBox(
        height: 430,
        width: 304,
        child: TableCalendar(
          locale: 'ja_JP',
          calendarFormat: calendarFormat,
          focusedDay: focusedDay.value,
          firstDay: widget.firstDay,
          lastDay: widget.lastDay,
          daysOfWeekHeight: 30,
          selectedDayPredicate: (day) {
            return isSameDay(selectedDay.value, day);
          },
          onDaySelected: (selected, focused) {
            if (!isSameDay(selectedDay.value, selected)) {
              setState(() {
                widget.changeDialogFlag(true);
                widget.changeSelectedDay(selected);
                controller.checkFirstDayAndLastDayCalendar(serverDay);
                selectedDay.value = selected;
                focusedDay.value = focused;
              });
            }
          },
          headerStyle: const HeaderStyle(
            titleTextStyle: TextStyle(color: AppColors.tradGreen),
            decoration: BoxDecoration(color: AppColors.lightGreen),
            formatButtonVisible: false,
            titleCentered: true,
            headerPadding: EdgeInsets.symmetric(vertical: 18),
            leftChevronIcon: Icon(
              Icons.chevron_left,
              color: AppColors.tradGreen,
            ),
            rightChevronIcon: Icon(
              Icons.chevron_right,
              color: AppColors.tradGreen,
            ),
          ),
          daysOfWeekStyle: const DaysOfWeekStyle(
            weekdayStyle: TextStyle(color: AppColors.textGrayGreen),
            weekendStyle: TextStyle(color: AppColors.textGrayGreen),
          ),
          calendarStyle: CalendarStyle(
            todayTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            defaultTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            holidayTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            weekendTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            outsideTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            withinRangeTextStyle:
                Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                    ),
            disabledTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.greyText,
                ),
            selectedTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.white,
                ),
            cellMargin: const EdgeInsets.all(4),
            selectedDecoration: const BoxDecoration(
              color: AppColors.tradGreen,
              shape: BoxShape.circle,
            ),
            todayDecoration: BoxDecoration(
              border: Border.all(color: AppColors.tradGreen),
              color: AppColors.white,
              shape: BoxShape.rectangle,
            ),
            tablePadding: const EdgeInsets.symmetric(horizontal: 16),
          ),
        ),
      ),
    );
  }
}

void _validator({
  required String? value,
  required void Function(bool) clear,
  required bool condition,
  required void Function(bool) function,
}) {
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (value == null || value.isEmpty) {
      clear(false);
    }
    if (value != null && value.isNotEmpty) {
      function(condition);
    }
  });
}
