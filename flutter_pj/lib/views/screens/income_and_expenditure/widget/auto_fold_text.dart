import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// テキストの行数に応じて自動的に表示を折りたたむウィジェット
class AutoFoldText extends StatelessWidget {
  const AutoFoldText({
    super.key,
    required this.text,
    this.style,
    this.maxLines = 1,
  });

  final String text;
  final TextStyle? style;

  /// 折りたたまずに表示される最大行数
  final int maxLines;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final numLines = _calculateNumberOfLines(
          text: text,
          maxWidth: constraints.maxWidth,
          style: style,
        );

        if (numLines > maxLines) {
          return _FoldedText(text, style, maxLines);
        } else {
          return _Text(text, style);
        }
      },
    );
  }

  int _calculateNumberOfLines({
    required String text,
    required double maxWidth,
    TextStyle? style,
  }) {
    final span = TextSpan(text: text, style: style);
    final tp = TextPainter(text: span, textDirection: TextDirection.ltr);
    tp.layout(maxWidth: maxWidth);
    return tp.computeLineMetrics().length;
  }
}

/// 折りたたまないで表示した場合のView
class _Text extends StatelessWidget {
  const _Text(this.text, this.style);

  final String text;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return Text(text, style: style);
  }
}

/// 折りたたんで表示した場合のView
/// ボタンによって開閉を制御する
class _FoldedText extends StatefulHookConsumerWidget {
  const _FoldedText(this.text, this.style, this.maxLine);

  final String text;
  final TextStyle? style;
  final int maxLine;

  @override
  _FoldedTextState createState() => _FoldedTextState();
}

class _FoldedTextState extends ConsumerState<_FoldedText> {
  bool isOpened = false;

  void _toggle() {
    setState(() {
      isOpened = !isOpened;
    });
  }

  @override
  Widget build(BuildContext context) {
    final firebaseController = ref.read(analyticsLogControllerProvider);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            widget.text,
            key: ValueKey<bool>(isOpened),
            style: widget.style,
            maxLines: isOpened ? null : widget.maxLine,
            overflow: isOpened ? null : TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(width: 10),
        TextButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            firebaseController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.switchAccordion,
              screenName: AppLocalizations.of(context)!
                  .incomeAndExpenditureLinkedAccount,
            );
            _toggle();
          },
          style: ButtonStyle(
            padding: WidgetStateProperty.all(
              EdgeInsets.zero,
            ),
            minimumSize: WidgetStateProperty.all(Size.zero),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Padding(
            padding: const EdgeInsets.only(top: 6),
            child: SvgPicture.asset(
              isOpened ? Assets.arrowUpIcon : Assets.arrowDownIcon,
            ),
          ),
        ),
      ],
    );
  }
}
