import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/narrow_down_freee_modal.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/narrow_down_modal.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class NarrowDownZeroModal extends HookConsumerWidget {
  const NarrowDownZeroModal({
    super.key,
    required this.serverDate,
    required this.items,
    required this.freeeItems,
    required this.accountId,
    required this.walletableId,
    required this.accountApiType,
    required this.depositType,
  });

  final String serverDate;
  final List<AccountTransactionHistoryDetail> items;
  final List<FreeeTransactionHistoryDetail> freeeItems;
  final String? accountId;
  final int walletableId;
  final AccountApiType accountApiType;
  final DepositType depositType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return SizedBox(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 16,
                right: 16,
              ),
              child: SizedBox(
                height: 32,
                width: 32,
                child: IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName:
                          AppLocalizations.of(context)!.close0CasesModal,
                      screenName: accountApiType == AccountApiType.web21
                          ? AppLocalizations.of(context)!
                              .incomeAndExpenditureNarrowDown
                          : AppLocalizations.of(context)!
                              .incomeAndExpenditureNarrowDownLinkedAccount,
                    );
                    Navigator.of(context).pop();
                  },
                  icon: SvgPicture.asset(
                    Assets.buttonClose,
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: SizedBox(
              width: 327,
              height: 85,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        AppLocalizations.of(context)!.narrowDownResult,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontSize: 20,
                            ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '0',
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                              fontSize: 36,
                            ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context)!.itemCountUnit,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontSize: 20,
                            ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocalizations.of(context)!.changeConditionsTryAgain,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          Center(
            child: Padding(
              padding: const EdgeInsets.only(top: 24, bottom: 40),
              child: SizedBox(
                width: 240,
                height: 48,
                child: AppRectangleButton(
                  label: AppLocalizations.of(context)!.changeConditions,
                  textColor: AppColors.white,
                  buttonColor: AppColors.tradGreen,
                  onPressed: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName:
                          AppLocalizations.of(context)!.changeConditions,
                      screenName: accountApiType == AccountApiType.web21
                          ? AppLocalizations.of(context)!
                              .incomeAndExpenditureNarrowDown
                          : AppLocalizations.of(context)!
                              .incomeAndExpenditureNarrowDownLinkedAccount,
                    );

                    Navigator.of(context).pop();
                    showModalBottomSheet(
                      isDismissible: false,
                      enableDrag: false,
                      isScrollControlled: true,
                      useSafeArea: true,
                      backgroundColor: AppColors.white,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(8),
                        ),
                      ),
                      context: context,
                      builder: (BuildContext context) {
                        return accountApiType == AccountApiType.web21
                            ? NarrowDownModal(
                                serverDate: serverDate,
                                items: items,
                                accountId: accountId,
                                accountApiType: accountApiType,
                                depositType: depositType,
                              )
                            : NarrowDownFreeeModal(
                                serverDate: serverDate,
                                items: freeeItems,
                                walletableId: walletableId,
                                accountApiType: accountApiType,
                              );
                      },
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
