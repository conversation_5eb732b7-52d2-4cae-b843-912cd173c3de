import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/ext/deposit_type_ext.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/ext/history_type_ext.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/label_container.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class IncomeAndExpenditureDetailModal extends HookConsumerWidget {
  const IncomeAndExpenditureDetailModal({
    super.key,
    required this.item,
    required this.bankAccount,
  });

  final AccountTransactionHistoryDetail item;
  final BankAccountWithBalanceDetail bankAccount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.incomeAndExpenditureDetailScreenId,
            screenName:
                AppLocalizations.of(context)!.incomeAndExpenditureDetail,
          );
        });
        return null;
      },
      const [],
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const _Header(),
        const SizedBox(height: 24),
        _ScrollArea(
          item: item,
          bankAccount: bankAccount,
        ),
      ],
    );
  }
}

class _Header extends HookConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 24),
                child: Text(
                  AppLocalizations.of(context)!.incomeAndExpenditureDetail,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        fontSize: 20,
                        color: AppColors.tradGreen,
                        height: 1.5,
                      ),
                ),
              ),
              SizedBox(
                height: 32,
                width: 32,
                child: IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!.batsu,
                      screenName: AppLocalizations.of(context)!
                          .incomeAndExpenditureDetail,
                    );
                    Navigator.of(context).pop();
                  },
                  icon: SvgPicture.asset(
                    Assets.buttonClose,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ScrollArea extends StatelessWidget {
  const _ScrollArea({
    required this.item,
    required this.bankAccount,
  });

  final AccountTransactionHistoryDetail item;
  final BankAccountWithBalanceDetail bankAccount;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: item.depositType == DepositType.liquidityDeposit ? 937 : 848,
        child: RawScrollbar(
          thumbColor: AppColors.basicBackgroundColor,
          thumbVisibility: true,
          thickness: 6,
          radius: const Radius.circular(3.0),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  _SummaryInfo(item: item),
                  const SizedBox(height: 24),
                  _DetailInfo(
                    item: item,
                    bankAccount: bankAccount,
                  ),
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _SummaryInfo extends StatelessWidget {
  const _SummaryInfo({required this.item});

  final AccountTransactionHistoryDetail item;

  @override
  Widget build(BuildContext context) {
    const fontSizeChangeDigit = 9;
    final isAmountSizeChangeDigit = item.amount != null &&
        item.amount.toString().length <= fontSizeChangeDigit;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.lightGreen,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          item.type.when(
            income: const LabelContainer.income(),
            expenditure: const LabelContainer.expenditure(),
          ),
          item.depositType.when(
            fixed: const SizedBox.shrink(),
            liquidity: Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppLocalizations.of(context)!.payerNamePerCode,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppColors.textGrayGreen,
                          fontWeight: FontWeight.w700,
                          height: 1.5,
                        ),
                  ),
                  SizedBox(
                    width: 28,
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        NullableHyphenText(
                          overflow: TextOverflow.visible,
                          data: item.payerName,
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    fontSize: 20,
                                    height: 1.5,
                                  ),
                        ),
                        SizedBox(
                          height: 4,
                        ),
                        NullableHyphenText(
                          data: item.remitterCode,
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                    fontSize: 20,
                                    height: 1.5,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              item.type.when(
                income: Text(
                  AppLocalizations.of(context)!.incomeAmount,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
                expenditure: Text(
                  AppLocalizations.of(context)!.expenditureAmount,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
              ),
              item.amount == null
                  ? Text(AppLocalizations.of(context)!.fullWidthHyphen)
                  : _Amount(item.amount!, isAmountSizeChangeDigit, 12, true),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                AppLocalizations.of(context)!.checksIssuedByOtherBanksAmount,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.textGrayGreen,
                      fontWeight: FontWeight.w700,
                    ),
              ),
              item.checksIssuedByOtherBanksAmount == null
                  ? Text(AppLocalizations.of(context)!.fullWidthHyphen)
                  : _Amount(
                      item.checksIssuedByOtherBanksAmount!,
                      isAmountSizeChangeDigit,
                      14,
                      false,
                    ),
            ],
          ),
        ],
      ),
    );
  }
}

class _DetailInfo extends StatelessWidget {
  const _DetailInfo({
    required this.item,
    required this.bankAccount,
  });

  final AccountTransactionHistoryDetail item;
  final BankAccountWithBalanceDetail bankAccount;

  @override
  Widget build(BuildContext context) {
    const width = 132.0;

    return Column(
      children: [
        //起算日
        Row(
          children: [
            SizedBox(
              width: width,
              child: Row(
                children: [
                  Text(
                    AppLocalizations.of(context)!.startingDate,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppColors.textGrayGreen,
                          fontWeight: FontWeight.w700,
                          height: 1.5,
                        ),
                  ),
                  const SizedBox(width: 8),
                  _Tooltip(
                    message: AppLocalizations.of(context)!
                        .startingDateTooltipMessage,
                    buttonName:
                        AppLocalizations.of(context)!.startingDateTooltip,
                  ),
                ],
              ),
            ),
            NullableHyphenText(
              data: item.startingDate,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    height: 1.5,
                  ),
            ),
          ],
        ),
        const _Divider(),
        //取引日
        Row(
          children: [
            SizedBox(
              width: width,
              child: Row(
                children: [
                  Text(
                    AppLocalizations.of(context)!.tradingDate,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppColors.textGrayGreen,
                          fontWeight: FontWeight.w700,
                          height: 1.5,
                        ),
                  ),
                  const SizedBox(width: 8),
                  _Tooltip(
                    message:
                        AppLocalizations.of(context)!.tradingDateTooltipMessage,
                    buttonName:
                        AppLocalizations.of(context)!.tradingDateTooltip,
                  ),
                ],
              ),
            ),
            NullableHyphenText(
              data: item.tradingDate,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    height: 1.5,
                  ),
            ),
          ],
        ),
        const _Divider(),
        //取引区分
        Row(
          children: [
            SizedBox(
              width: width,
              child: Text(
                AppLocalizations.of(context)!.tradingType,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.textGrayGreen,
                      fontWeight: FontWeight.w700,
                      height: 1.5,
                    ),
              ),
            ),
            Expanded(
              child: NullableHyphenText(
                data: item.transactionType?.name,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      height: 1.5,
                    ),
              ),
            ),
          ],
        ),
        const _Divider(),
        //手形小切手区分/番号
        item.depositType.when(
          fixed: const SizedBox.shrink(),
          liquidity: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    width: width,
                    child: Text(
                      AppLocalizations.of(context)!.billAndCheckTypeName,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontWeight: FontWeight.w700,
                            height: 1.5,
                          ),
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      NullableHyphenText(
                        data: item.billAndCheckTypeName,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              height: 1.5,
                            ),
                      ),
                      const SizedBox(width: 8),
                      NullableHyphenText(
                        data: item.billAndCheckNumber,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                              height: 1.5,
                            ),
                      ),
                    ],
                  ),
                ],
              ),
              const _Divider(),
            ],
          ),
        ),
        //EDI情報
        item.depositType.when(
          fixed: const SizedBox.shrink(),
          liquidity: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    width: width,
                    child: Text(
                      AppLocalizations.of(context)!.ediInfo,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontWeight: FontWeight.w700,
                            height: 1.5,
                          ),
                    ),
                  ),
                  Expanded(
                    child: NullableHyphenText(
                      data: item.ediInfo,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            height: 1.5,
                          ),
                    ),
                  ),
                ],
              ),
              const _Divider(),
            ],
          ),
        ),
        //利率
        item.depositType.when(
          fixed: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    width: width,
                    child: Text(
                      AppLocalizations.of(context)!.interestRate,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontWeight: FontWeight.w700,
                            height: 1.5,
                          ),
                    ),
                  ),
                  NullableHyphenText(
                    data: item.fixedInterestRate,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontFamily: FontFamily.robotoCondensed,
                          height: 1.5,
                        ),
                  ),
                ],
              ),
              const _Divider(),
            ],
          ),
          liquidity: const SizedBox.shrink(),
        ),
        //満期日
        item.depositType.when(
          fixed: Column(
            children: [
              Row(
                children: [
                  SizedBox(
                    width: width,
                    child: Text(
                      AppLocalizations.of(context)!.maturityDateAd,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontWeight: FontWeight.w700,
                            height: 1.5,
                          ),
                    ),
                  ),
                  NullableHyphenText(
                    data: item.maturityDateAd,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontFamily: FontFamily.robotoCondensed,
                          height: 1.5,
                        ),
                  ),
                ],
              ),
              const _Divider(),
            ],
          ),
          liquidity: const SizedBox.shrink(),
        ),
        //摘要
        Row(
          children: [
            SizedBox(
              width: width,
              child: Text(
                AppLocalizations.of(context)!.abstract,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.textGrayGreen,
                      fontWeight: FontWeight.w700,
                      height: 1.5,
                    ),
              ),
            ),
            NullableHyphenText(
              data: item.abstract,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    height: 1.5,
                  ),
            ),
          ],
        ),
        const _Divider(),
        //照会番号・預入番号
        Row(
          children: [
            SizedBox(
              width: width,
              child: item.depositType.when(
                fixed: Text(
                  AppLocalizations.of(context)!.depositNumber,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
                liquidity: Text(
                  AppLocalizations.of(context)!.referenceNumber,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
              ),
            ),
            NullableHyphenText(
              data: item.referenceNumber,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    height: 1.5,
                  ),
            ),
          ],
        ),
        const _Divider(),
        //入金・出金口座
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: width,
              child: item.type.when(
                income: Text(
                  AppLocalizations.of(context)!.incomeAccount,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
                expenditure: Text(
                  AppLocalizations.of(context)!.expenditureAccount,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    bankAccount.displayConfig.displayName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  NullableHyphenText(
                    data: bankAccount.balanceDetail.bankName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  NullableHyphenText(
                    data: bankAccount.balanceDetail.branchName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      NullableHyphenText(
                        data: bankAccount.balanceDetail.accountType,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      NullableHyphenText(
                        data: bankAccount.balanceDetail.accountNumber,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                            ),
                      ),
                    ],
                  ),
                  Text(
                    // 振込依頼人名 nullの場合は空文字で表示（webと表示を統一）
                    bankAccount.baseInfo.remitterName ?? '',
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _Tooltip extends HookConsumerWidget {
  const _Tooltip({
    required this.message,
    required this.buttonName,
  });

  final String message;
  final String buttonName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Tooltip(
      message: message,
      textStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
            color: AppColors.cardGradation,
            height: 1.5,
          ),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppColors.tradGreen,
        ),
      ),
      triggerMode: TooltipTriggerMode.tap,
      margin: const EdgeInsets.only(left: 62, right: 33),
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.all(3),
        height: 16.5,
        width: 16.5,
        decoration: const BoxDecoration(
          color: AppColors.tradGreen,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Icons.question_mark,
          color: AppColors.white,
          size: 10,
        ),
      ),
      onTriggered: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: buttonName,
          screenName: AppLocalizations.of(context)!.incomeAndExpenditureDetail,
        );
      },
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Container(
        color: AppColors.borderGrayGreen,
        height: 1.0,
      ),
    );
  }
}

/// 金額の表示部
class _Amount extends StatelessWidget {
  const _Amount(
    this.amount,
    this.isAmountSizeChangeDigit,
    this.maxDigit,
    this.isTransactionAmount,
  );
  final int amount;
  final bool isAmountSizeChangeDigit;
  final int maxDigit;
  // 入出金額の際には文字サイズに特別な制御が必要な為、判断フラグを儲ける
  final bool isTransactionAmount;
  @override
  Widget build(BuildContext context) {
    final double fontSize = isTransactionAmount
        ? isAmountSizeChangeDigit
            ? 32
            : 20
        : 18;
    // 最大桁数超過時には桁数超過ウィジェットを返却
    if (amount.toString().length > maxDigit) {
      return Text(
        BankAccountErrorInfo.overflowDigitError,
        style: TextStyle(
          fontFamily: FontFamily.notoSansJP,
          fontWeight: FontWeight.w700,
          fontSize: 20,
        ),
      );
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          amount.withCommasAndMinusSign,
          style: TextStyle(
            fontFamily: FontFamily.robotoCondensed,
            fontWeight: FontWeight.w700,
            fontSize: fontSize,
            height: 1.5,
          ),
        ),
        const SizedBox(width: 4),
        Text(
          AppLocalizations.of(context)!.yenJp,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w700,
            height: 1.5,
          ),
        ),
      ],
    );
  }
}
