import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class LabelContainer extends StatelessWidget {
  const LabelContainer.income({super.key}) : _isIncome = true;

  const LabelContainer.expenditure({super.key}) : _isIncome = false;

  final bool _isIncome;

  Color get _color =>
      _isIncome ? AppColors.functionalFreshGreen : AppColors.functionalBlue;

  String _labelText(BuildContext context) => _isIncome
      ? AppLocalizations.of(context)!.income
      : AppLocalizations.of(context)!.expenditure;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 36,
      height: 22,
      padding: const EdgeInsets.only(left: 6, top: 2),
      decoration: BoxDecoration(
        color: _color,
        borderRadius: const BorderRadius.all(Radius.circular(4)),
      ),
      child: Text(
        _labelText(context),
        style: const TextStyle(
          fontSize: 12,
          color: AppColors.textBlack,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }
}
