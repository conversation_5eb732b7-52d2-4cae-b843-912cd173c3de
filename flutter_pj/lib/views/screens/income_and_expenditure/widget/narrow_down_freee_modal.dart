import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/income_and_expenditure/income_and_expenditure_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/utils/ext/string_validator.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/screens/income_and_expenditure/widget/narrow_down_zero_modal.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

class NarrowDownFreeeModal extends HookConsumerWidget {
  const NarrowDownFreeeModal({
    super.key,
    required this.serverDate,
    required this.items,
    required this.walletableId,
    required this.accountApiType,
  });

  final String serverDate;
  final List<FreeeTransactionHistoryDetail> items;
  final int walletableId;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.checkFirstDayAndLastDayCalendar(getFirstDate(serverDate));

          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber:
                ScreenIdNumber.incomeAndExpenditureNarrowDownFreeeScreenId,
            screenName: AppLocalizations.of(context)!
                .incomeAndExpenditureNarrowDownLinkedAccount,
          );
        });
        return null;
      },
      [],
    );
    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
          screenName: AppLocalizations.of(context)!
              .incomeAndExpenditureNarrowDownLinkedAccount,
        );
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const _Header(),
            const SizedBox(height: 24),
            _ScrollArea(
              serverDate: serverDate,
              items: items,
            ),
            const SizedBox(height: 40),
            _Footer(
              items: items,
              walletableId: walletableId,
              serverDate: serverDate,
              accountApiType: accountApiType,
            ),
          ],
        ),
      ),
    );
  }
}

class _Header extends HookConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              const SizedBox(height: 24),
              Text(
                AppLocalizations.of(context)!.narrowDown,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.tradGreen,
                      height: 1.5,
                    ),
              ),
            ],
          ),
          IconButton(
            splashColor: AppColors.transparent,
            highlightColor: AppColors.transparent,
            iconSize: 32,
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(),
            onPressed: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.close,
                screenName: AppLocalizations.of(context)!
                    .incomeAndExpenditureNarrowDownLinkedAccount,
              );

              Navigator.of(context).pop();
            },
            icon: SvgPicture.asset(
              Assets.buttonClose,
            ),
          ),
        ],
      ),
    );
  }
}

class _Footer extends HookConsumerWidget {
  const _Footer({
    required this.items,
    required this.walletableId,
    required this.serverDate,
    required this.accountApiType,
  });

  final List<FreeeTransactionHistoryDetail> items;
  final int walletableId;
  final String serverDate;
  final AccountApiType accountApiType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    double? toNumber(String text) {
      if (text.isNotEmpty && int.tryParse(text) != null) {
        // 数値に変換できる場合の処理
        return double.parse(text);
      } else {
        return null;
      }
    }

    return Container(
      height: 80,
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.narrowDownShadow,
            offset: Offset(0.0, -2.0),
            blurRadius: 4.0,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              style: CommonButtonStyles.textButtonStyle,
              onPressed: () async {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.reset,
                  screenName: AppLocalizations.of(context)!
                      .incomeAndExpenditureNarrowDownLinkedAccount,
                );

                controller.reset();
                controller
                    .checkFirstDayAndLastDayCalendar(getFirstDate(serverDate));
                // reset処理にてstateが更新されるため、最新のものを取得
                final state = ref.read(incomeAndExpenditureScreenProvider);
                // 実際はキャッシュに対する絞り込みの為、ローディングは描画されないものの
                // 連打対策のためローディングを実施
                LoadingDialog.loading(context);
                // リセットボタン押下時に明細画面を絞り込みなしに初期化
                // ※明細画面にてセットされている入出金種別、ソート順に関してはリセットしない
                await controller.narrowDownFreee(
                  walletableId: walletableId,
                  walletableType: FreeeTransactionsType.freeeBankAccount,
                  dateFrom: state.dateFrom!,
                  dateTo: state.dateTo!,
                  forceRefresh: false,
                  filterType: state.currentFilterType,
                  order: state.currentSortOrder,
                  isNarrowDown: false,
                  tradingDateFrom: state.fromSelectedDay!,
                  tradingDateTo: state.toSelectedDay!,
                );
                if (context.mounted) LoadingDialog.loadingEnd(context);
              },
              child: Text(
                AppLocalizations.of(context)!.reset,
                style: const TextStyle(color: AppColors.tradGreen),
              ),
            ),
            const SizedBox(width: 27),
            Flexible(
              child: SizedBox(
                height: 48,
                width: 240,
                child: AppRectangleButton(
                  label: AppLocalizations.of(context)!.narrowingDown,
                  textColor: (controller.checkInvalid())
                      ? AppColors.inactiveText
                      : AppColors.white,
                  buttonColor: (controller.checkInvalid())
                      ? AppColors.backgroundGreen
                      : AppColors.tradGreen,
                  onPressed: (controller.checkInvalid())
                      ? null
                      : () async {
                          // 最新の情報を取得するため、描画時ではなくボタン押下時に金額を取得
                          final largeAmountText =
                              controller.largeAmountController.text;
                          final smallAmountText =
                              controller.smallAmountController.text;

                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName:
                                AppLocalizations.of(context)!.narrowingDown,
                            screenName: AppLocalizations.of(context)!
                                .incomeAndExpenditureNarrowDownLinkedAccount,
                          );
                          // 実際はキャッシュに対する絞り込みの為、ローディングは描画されないものの
                          // 連打対策のためローディングを実施
                          LoadingDialog.loading(context);
                          await controller.narrowDownFreee(
                            walletableId: walletableId,
                            walletableType:
                                FreeeTransactionsType.freeeBankAccount,
                            dateFrom: state.dateFrom!,
                            dateTo: state.dateTo!,
                            forceRefresh: false,
                            filterType: state.currentFilterType,
                            order: state.currentSortOrder,
                            tradingDateFrom: state.fromSelectedDay!,
                            tradingDateTo: state.toSelectedDay!,
                            minAmount: toNumber(
                              smallAmountText.replaceAll(',', ''),
                            ),
                            maxAmount: toNumber(
                              largeAmountText.replaceAll(',', ''),
                            ),
                          );
                          if (context.mounted) {
                            LoadingDialog.loadingEnd(context);
                          }
                          ref
                              .watch(incomeAndExpenditureScreenProvider)
                              .freeeTransactionHistory
                              .map(
                            empty: (empty) {
                              showModalBottomSheet(
                                isDismissible: false,
                                enableDrag: false,
                                isScrollControlled: true,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(
                                    top: Radius.circular(8),
                                  ),
                                ),
                                context: context,
                                builder: (BuildContext context) {
                                  return NarrowDownZeroModal(
                                    serverDate: serverDate,
                                    items: const [],
                                    freeeItems: items,
                                    accountId: '',
                                    walletableId: walletableId,
                                    accountApiType: accountApiType,
                                    depositType: DepositType.fixedDeposit,
                                  );
                                },
                              );
                            },
                            data: (data) {
                              if (data.items.isEmpty) {
                                Navigator.pop(context);
                                showModalBottomSheet(
                                  isDismissible: false,
                                  enableDrag: false,
                                  isScrollControlled: true,
                                  shape: const RoundedRectangleBorder(
                                    borderRadius: BorderRadius.vertical(
                                      top: Radius.circular(8),
                                    ),
                                  ),
                                  context: context,
                                  builder: (BuildContext context) {
                                    return NarrowDownZeroModal(
                                      serverDate: serverDate,
                                      items: const [],
                                      freeeItems: items,
                                      accountId: '',
                                      walletableId: walletableId,
                                      accountApiType: accountApiType,
                                      // freeeの場合は固定性、流動性どちらを渡しても良い
                                      depositType: DepositType.fixedDeposit,
                                    );
                                  },
                                );
                              } else {
                                Navigator.pop(context);
                              }
                            },
                          );
                        },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  const _ScrollArea({
    required this.serverDate,
    required this.items,
  });

  final String serverDate;
  final List<FreeeTransactionHistoryDetail> items;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = ScrollController();
    final bottomSpace = MediaQuery.of(context).viewInsets.bottom;
    final formKey = useMemoized(() => GlobalKey<FormState>());
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // スクロールの開始地点をモーダルのトップにする
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
        return () => scrollController.dispose();
      },
      [],
    );
    return Flexible(
      child: SizedBox(
        child: RawScrollbar(
          thumbVisibility: true,
          thumbColor: AppColors.basicBackgroundColor,
          thickness: 6,
          radius: const Radius.circular(3.0),
          controller: scrollController,
          child: SingleChildScrollView(
            controller: scrollController,
            physics: const ClampingScrollPhysics(),
            reverse: true,
            child: Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, bottomSpace),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _DateSegment(serverDate: serverDate),
                    const _Amount(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _DateSegment extends HookConsumerWidget {
  const _DateSegment({
    required this.serverDate,
  });

  final String serverDate;

  String _format(DateTime? dateTime) =>
      dateTime != null ? DateFormat('yyyy/M/d').format(dateTime) : '';

  DateTime _toDateTime(String serverDate) {
    return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.period,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 14,
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _PeriodContainer(
                initialDate: _format(state.fromSelectedDay),
                serverDate: serverDate,
                firstDay: state.dateFrom ?? _toDateTime(serverDate),
                lastDay: state.toSelectedDay ?? _toDateTime(serverDate),
                selectedDay: state.fromSelectedDay ?? _toDateTime(serverDate),
                changeSelectedDay: (selectedDay) {
                  controller.changeFromSelectedDay(selectedDay);
                },
                changeDialogFlag: (isFlag) =>
                    controller.changeIsFromDialog(isFlag),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SvgPicture.asset(Assets.tildeIcon),
            ),
            Expanded(
              child: _PeriodContainer(
                initialDate: _format(state.toSelectedDay),
                serverDate: serverDate,
                firstDay: state.fromSelectedDay ?? _toDateTime(serverDate),
                lastDay: state.dateTo ?? _toDateTime(serverDate),
                selectedDay: state.toSelectedDay ?? _toDateTime(serverDate),
                changeSelectedDay: (selectedDay) {
                  controller.changeToSelectedDay(selectedDay);
                },
                changeDialogFlag: (isFlag) =>
                    controller.changeIsToDialog(isFlag),
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 24),
          child: Container(
            color: AppColors.grey200,
            height: 1.0,
          ),
        ),
      ],
    );
  }
}

class _Amount extends HookConsumerWidget {
  const _Amount();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(incomeAndExpenditureScreenProvider);
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final smallAmountController = controller.smallAmountController;
    final largeAmountController = controller.largeAmountController;
    final smallAmountFocus = useFocusNode();
    final largeAmountFocus = useFocusNode();

    // フォーカスが外れた時に3桁のカンマ区切りで表示
    void formatAmountWithCommas({required TextEditingController controller}) {
      final numericValue = int.tryParse(controller.text.replaceAll(',', ''));
      if (numericValue != null) {
        final formattedValue = NumberFormat('#,###').format(numericValue);
        controller.text = formattedValue;
      }
    }

    smallAmountFocus.addListener(() {
      if (!smallAmountFocus.hasFocus) {
        formatAmountWithCommas(controller: smallAmountController);
      }
    });
    largeAmountFocus.addListener(() {
      if (!largeAmountFocus.hasFocus) {
        formatAmountWithCommas(controller: largeAmountController);
      }
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.amount,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 14,
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                maxLength: 12,
                width: 305,
                height: 48,
                controller: smallAmountController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                ],
                hintText: AppLocalizations.of(context)!
                    .example(AppLocalizations.of(context)!.zero),
                textAlign: TextAlign.right,
                validation: state.digitSmallAmountError ||
                    state.positiveSmallAmountError ||
                    state.correlationSmallAmountError,
                validator: (String? value) {
                  if (smallAmountController.text.isEmpty) {
                    controller.clearSmallAmountError();
                  }
                  if (value != null && value.isNotEmpty) {
                    // 数字以外を含んでいるか判定
                    controller.validateDigitAmount(
                      value: value.containsNonDigit(),
                      isSmallAmount: true,
                    ); // マイナスを含んでいるか判定
                    controller.validatePositiveAmount(
                      value: value.containsMinus(),
                      isSmallAmount: true,
                    ); // 数字以外を含んでいる or マイナスを含んでいる場合は金額の相関判定に行く前に処理を中断
                    if (smallAmountController.text.containsNonDigit() ||
                        smallAmountController.text.containsMinus() ||
                        largeAmountController.text.containsNonDigit() ||
                        largeAmountController.text.containsMinus()) {
                      return;
                    } // 金額の相関判定
                    if (!value.containsNonDigit() && !value.containsMinus()) {
                      if (controller.largeAmountController.text.isEmpty ||
                          largeAmountFocus.hasFocus) {
                        return;
                      }
                      final isCorrect = value.checkAmountCorrelation(
                        smallAmount: controller.smallAmountController.text,
                        largeAmount: controller.largeAmountController.text,
                      );
                      controller.validateCorrelationAmount(
                        smallAmount: isCorrect,
                        largeAmount: isCorrect,
                      );
                    }
                  }
                  return null;
                },
                focusNode: smallAmountFocus,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${AppLocalizations.of(context)!.yenJp} 〜',
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.textGrayGreen, height: 1.5),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                maxLength: 12,
                width: 305,
                height: 48,
                controller: controller.largeAmountController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                ],
                hintText: AppLocalizations.of(context)!.example(
                  AppLocalizations.of(context)!.fiveHundredMillion,
                ),
                textAlign: TextAlign.right,
                validation: state.digitLargeAmountError ||
                    state.positiveLargeAmountError ||
                    state.correlationLargeAmountError,
                validator: (String? value) {
                  if (largeAmountController.text.isEmpty) {
                    controller.clearLargeAmountError();
                  }
                  if (value != null && value.isNotEmpty) {
                    // 数字以外を含んでいるか判定
                    controller.validateDigitAmount(
                      value: value.containsNonDigit(),
                    );
                    // マイナスを含んでいるか判定
                    controller.validatePositiveAmount(
                      value: value.containsMinus(),
                    );
                    // 数字以外を含んでいる or マイナスを含んでいる場合は金額の相関判定に行く前に処理を中断
                    if (smallAmountController.text.containsNonDigit() ||
                        smallAmountController.text.containsMinus() ||
                        largeAmountController.text.containsNonDigit() ||
                        largeAmountController.text.containsMinus()) {
                      return;
                    }
                    // 金額の相関判定
                    if (!value.containsNonDigit() && !value.containsMinus()) {
                      if (controller.smallAmountController.text.isEmpty ||
                          smallAmountFocus.hasFocus) {
                        return;
                      }
                      final isCorrect = value.checkAmountCorrelation(
                        smallAmount: controller.smallAmountController.text,
                        largeAmount: controller.largeAmountController.text,
                      );
                      controller.validateCorrelationAmount(
                        smallAmount: isCorrect,
                        largeAmount: isCorrect,
                      );
                    }
                  }
                  return null;
                },
                focusNode: largeAmountFocus,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context)!.yenJp,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.textGrayGreen, height: 1.5),
            ),
            const SizedBox(width: 16),
          ],
        ),
        if (state.digitSmallAmountError || state.digitLargeAmountError)
          _ValidationMessage(text: AppLocalizations.of(context)!.enterDigit),
        if (state.positiveSmallAmountError || state.positiveLargeAmountError)
          _ValidationMessage(
            text:
                AppLocalizations.of(context)!.enterNumberGreaterThanOrEqualTo0,
          ),
        if (state.correlationSmallAmountError ||
            state.correlationLargeAmountError)
          _ValidationMessage(
            text: AppLocalizations.of(context)!
                .setAnAmountGreaterThanTheLowerLimit,
          ),
      ],
    );
  }
}

class _ValidationMessage extends StatelessWidget {
  const _ValidationMessage({
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 8),
        Row(
          children: [
            SvgPicture.asset(
              Assets.captionListErrorIcon,
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 4),
            Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.red),
            ),
          ],
        ),
      ],
    );
  }
}

class _PeriodContainer extends HookConsumerWidget {
  const _PeriodContainer({
    required this.initialDate,
    required this.serverDate,
    required this.firstDay,
    required this.lastDay,
    required this.selectedDay,
    required this.changeSelectedDay,
    required this.changeDialogFlag,
  });

  final String initialDate;
  final String serverDate;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime selectedDay;
  final void Function(DateTime dateTime) changeSelectedDay;
  final void Function(bool isFlag) changeDialogFlag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseController = ref.read(analyticsLogControllerProvider);
    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        firebaseController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.selectDate,
          screenName: AppLocalizations.of(context)!
              .incomeAndExpenditureNarrowDownLinkedAccount,
        );
        showDialog(
          context: context,
          builder: (context) {
            return _CalendarDialog(
              lastDate: initialDate,
              serverDate: serverDate,
              firstDay: firstDay,
              lastDay: lastDay,
              selectedDay: selectedDay,
              changeSelectedDay: changeSelectedDay,
              changeDialogFlag: changeDialogFlag,
            );
          },
        );
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.basicBackgroundLightGray,
          borderRadius: const BorderRadius.all(
            Radius.circular(4.0),
          ),
          border: Border.all(
            color: AppColors.borderGrayGreen,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                initialDate,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.textGrayGreen,
                    ),
              ),
              SvgPicture.asset(Assets.calendarGrayIcon),
            ],
          ),
        ),
      ),
    );
  }
}

class _CustomTextFormField extends StatelessWidget {
  const _CustomTextFormField({
    required this.width,
    required this.height,
    required this.controller,
    required this.inputFormatters,
    required this.hintText,
    required this.textAlign,
    required this.validation,
    this.validator,
    this.focusNode,
    this.maxLength,
  });

  final double width;
  final double height;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;
  final String hintText;
  final TextAlign textAlign;
  final bool validation;
  final String? Function(String?)? validator;
  final FocusNode? focusNode;
  final int? maxLength;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: validation
            ? AppColors.lightRed
            : AppColors.basicBackgroundLightGray,
        border: Border.all(
          color: validation ? AppColors.red : AppColors.borderGrayGreen,
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: NotCopyTextFormField(
                maxLength: maxLength,
                controller: controller,
                focusNode: focusNode,
                textAlign: textAlign,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: validation ? AppColors.red : AppColors.textBlack,
                    ),
                keyboardType: TextInputType.number,
                inputFormatters: inputFormatters,
                decoration: InputDecoration(
                  hintText: hintText,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  counterText: '',
                ),
                autovalidateMode: AutovalidateMode.onUserInteraction,
                onChanged: validator,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CalendarDialog extends StatefulHookConsumerWidget {
  const _CalendarDialog({
    required this.lastDate,
    required this.serverDate,
    required this.firstDay,
    required this.lastDay,
    required this.selectedDay,
    required this.changeSelectedDay,
    required this.changeDialogFlag,
  });

  final String lastDate;
  final String serverDate;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime selectedDay;
  final void Function(DateTime dateTime) changeSelectedDay;
  final void Function(bool isFlag) changeDialogFlag;

  @override
  _CalendarDialogState createState() => _CalendarDialogState();
}

class _CalendarDialogState extends ConsumerState<_CalendarDialog> {
  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(incomeAndExpenditureScreenProvider.notifier);
    final serverDay =
        DateFormat('yyyy-M-d').parse(widget.serverDate.replaceAll('/', '-'));
    const CalendarFormat calendarFormat = CalendarFormat.month;
    final focusedDay = useState(widget.selectedDay);
    final selectedDay = useState(widget.selectedDay);
    return AlertDialog(
      contentPadding: const EdgeInsets.all(0),
      content: SizedBox(
        height: 430,
        width: 304,
        child: TableCalendar(
          locale: 'ja_JP',
          calendarFormat: calendarFormat,
          focusedDay: focusedDay.value,
          firstDay: widget.firstDay,
          lastDay: widget.lastDay,
          daysOfWeekHeight: 30,
          selectedDayPredicate: (day) {
            return isSameDay(selectedDay.value, day);
          },
          onDaySelected: (selected, focused) {
            if (!isSameDay(selectedDay.value, selected)) {
              setState(() {
                widget.changeDialogFlag(true);
                widget.changeSelectedDay(selected);
                controller.checkFirstDayAndLastDayCalendar(serverDay);
                selectedDay.value = selected;
                focusedDay.value = focused;
              });
            }
          },
          headerStyle: const HeaderStyle(
            titleTextStyle: TextStyle(color: AppColors.tradGreen),
            decoration: BoxDecoration(color: AppColors.lightGreen),
            formatButtonVisible: false,
            titleCentered: true,
            headerPadding: EdgeInsets.symmetric(vertical: 18),
            leftChevronVisible: false,
            rightChevronVisible: false,
          ),
          daysOfWeekStyle: const DaysOfWeekStyle(
            weekdayStyle: TextStyle(color: AppColors.textGrayGreen),
            weekendStyle: TextStyle(color: AppColors.textGrayGreen),
          ),
          calendarStyle: CalendarStyle(
            todayTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            defaultTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            holidayTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            weekendTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            outsideTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            withinRangeTextStyle:
                Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                    ),
            disabledTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.greyText,
                ),
            selectedTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.white,
                ),
            cellMargin: const EdgeInsets.all(4),
            selectedDecoration: const BoxDecoration(
              color: AppColors.tradGreen,
              shape: BoxShape.circle,
            ),
            todayDecoration: BoxDecoration(
              border: Border.all(color: AppColors.tradGreen),
              color: AppColors.white,
              shape: BoxShape.rectangle,
            ),
            tablePadding: const EdgeInsets.symmetric(horizontal: 16),
          ),
        ),
      ),
    );
  }
}
