/// ログイン画面の遷移先画面
enum LoginNextScreens {
  /// 初期状態（画面遷移なし）
  initial,

  /// 次回以降のログインに生体認証を用いるかの確認ダイアログ表示(初回ログイン)
  checkFirstTimeBiometricsDialog,

  /// IDaaSOTP認証画面（WebView起動）
  iDaaSOtpScreen,

  /// 規約同意画面
  checkTermsScreen,

  /// 認可画面
  authScreen,

  /// ホーム画面へ遷移
  homeScreen,

  /// 生体認証利用解除ダイアログ
  biometricsCancellationDialog,

  /// 生体認証利用確認(2回目以降)ダイアログ
  biometricsConfirmationDialog,

  /// ID/PW上書き確認ダイアログ
  checkOverwriteIdPwDialog,

  /// 業務閉塞ダイアログ
  workBlockage,
}
