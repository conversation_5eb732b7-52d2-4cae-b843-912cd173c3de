import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';

/// ログイン画面の表示設定を保持するシングルトン
class LoginScreenSettings {
  static final LoginScreenSettings _settings = LoginScreenSettings._();

  LoginScreenSettings._();

  factory LoginScreenSettings() {
    return _settings;
  }

  SavedPasswordStatus _savedPw = SavedPasswordStatus.none;
  bool _isDtpLogin = true;
  bool _isBioAvailable = false;
  bool _isUsingBio = false;
  bool _isFirstLogin = true;
  bool _isBiometricsLoginButtonPressed = false;

  /// インスタンスの初期化処理
  void setParams({
    required SavedPasswordStatus savedPw,
    required bool isDtpLogin,
    required bool isBiometricsAvailable,
    required bool isUsingBiometrics,
    required bool isFirstLogin,
    required bool isBioLoginPressed,
  }) {
    _savedPw = savedPw;
    _isDtpLogin = isDtpLogin;
    _isBioAvailable = isBiometricsAvailable;
    _isUsingBio = isUsingBiometrics;
    _isFirstLogin = isFirstLogin;
    _isBiometricsLoginButtonPressed = isBioLoginPressed;
  }

  /// 生体認証ログインボタンの表示制御
  bool get biometricsLoginButtonVisibility {
    final isDtpIdPwSaved = _savedPw == SavedPasswordStatus.dtpId;
    final isVdPwSaved = _savedPw == SavedPasswordStatus.vdId;
    // 生体認証利用不可の場合には表示しない
    if (!_isBioAvailable) return false;
    if (isDtpIdPwSaved && _isDtpLogin && _isUsingBio) return true;
    if (isVdPwSaved && !_isDtpLogin && _isUsingBio) return true;
    return false;
  }

  /// 初回ログイン時、生体認証利用不可端末では設定ボタンを表示しない
  bool get biometricsSettingVisibility {
    if (!_isBioAvailable) return false;
    return !_isFirstLogin;
  }

  /// 生体認証ログインボタン押下可能であるかのフラグ
  /// 生体認証ログインボタン押下後、エラーの際には生体認証ログインを実施させないための制御
  bool get isBiometricsLoginButtonAvailable {
    return !_isBiometricsLoginButtonPressed;
  }
}
