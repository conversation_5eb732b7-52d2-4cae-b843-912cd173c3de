import 'dart:async';
import 'dart:io';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/business_blockage/business_blockage_controller.dart';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/idaas_path.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/my_app_bar.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/text_link_card.dart';
import 'package:dtp_app/views/components/web_view/customized/auth_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/customized/idaas_reset_password_input_email_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/customized/otp_input_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/login/login_next_screens.dart';
import 'package:dtp_app/views/screens/login/login_screen_settings.dart';
import 'package:dtp_app/views/screens/login/widget/announcement_info_area.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class LoginScreen extends HookConsumerWidget
    with RouteAware, CommonErrorHandlerMixin {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // DTPIDログイン用の変数
    final state = ref.watch(loginScreenProvider);
    final controller = ref.watch(loginScreenProvider.notifier);
    final homeNavigationController =
        ref.read(homeNavigationScreenProvider.notifier);
    final karteSdkController = ref.read(analyticsLogControllerProvider);

    final homeNavigationState = ref.read(homeNavigationScreenProvider);
    final isDtpLogin = state.isDtpLogin;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          final fraudAlertSendData =
              controller.terminalInformationTransmission();
          final loginInitialize = controller.initialize(
            homeNavigationState.isIdUnlinked,
            homeNavigationState.logoutCompleted,
          );
          await Future.wait([fraudAlertSendData, loginInitialize]);
          // ID連携解除フラグをリセット
          homeNavigationController.setIdUnlinked(false);
          // ログアウト完了フラグをリセット
          homeNavigationController.setLogoutComplete(false);
          // Firebase Screenログ・KARTE Viewイベント送信
          if (context.mounted) {
            karteSdkController.sendScreenLog(
              screenNumber: ScreenIdNumber.loginScreenId,
              screenName: isDtpLogin
                  ? AppLocalizations.of(context)!.dtpLogin
                  : AppLocalizations.of(context)!.vdLogin,
            );
          }
        });
        return null;
      },
      const [],
    );

    // 共通エラーハンドリング
    handleError(
      context,
      ref,
      state.error,
      screenId: ScreenIdNumber.loginScreenId,
      screenName: AppLocalizations.of(context)!.login,
      onOkTap: () async {
        // エラーダイアログのOKボタン押下時には
        // - fraudAlertSDK の端末情報送信
        // - 意図しない情報がFirebaseに送信されることがない様、mdManagerの揮発性情報を削除
        // を実施する
        final sendData = controller.terminalInformationTransmission();
        final deleteVolatileInfo = controller.deleteVolatileInfo();
        await Future.wait([sendData, deleteVolatileInfo]);
      },
    );
    _handleNavigation(context, ref);

    return const Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: MyAppBar(
        screenId: ScreenIdNumber.loginScreenId,
      ),
      body: LoginBody(),
    );
  }
}

class LoginBody extends HookConsumerWidget {
  const LoginBody({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(loginScreenProvider);
    final controller = ref.watch(loginScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final settings = LoginScreenSettings();
    final isAnnouncementInfo = state.announcementInfo != null;
    final isBioLoginPressed = useState(false);
    final isDtpLogin = state.isDtpLogin;

    //　お知らせ情報のデザイン
    final announcementInfoStyle =
        Theme.of(context).textTheme.bodySmall!.copyWith(
              color: AppColors.red,
              fontWeight: FontWeight.w700,
              height: 1.5,
            );

    settings.setParams(
      savedPw: state.savedPwStatus,
      isDtpLogin: state.isDtpLogin,
      isBiometricsAvailable: state.isBiometricsAvailable,
      isUsingBiometrics: state.isUsingBiometrics,
      isFirstLogin: state.isFirstTimeLogin,
      isBioLoginPressed: isBioLoginPressed.value,
    );

    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const ClampingScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: IntrinsicHeight(
              child: GestureDetector(
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
                    screenName: isDtpLogin
                        ? AppLocalizations.of(context)!.dtpLogin
                        : AppLocalizations.of(context)!.vdLogin,
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(
                        Assets.bgWaveHome,
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Column(
                    children: [
                      AnnouncementInfoArea(
                        announcementInfo: state.announcementInfo,
                        isAnnouncementInfo: isAnnouncementInfo,
                        style: announcementInfoStyle,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _BackButton(),
                          ScreenNumber(
                            screenNumber: ScreenIdNumber.loginScreenId,
                            color: AppColors.white,
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        decoration: const ShapeDecoration(
                          color: AppColors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.vertical(top: Radius.circular(8)),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 24),
                            Center(
                              child: Text(
                                AppLocalizations.of(context)!.smbcBusiness,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge!
                                    .copyWith(
                                      fontSize: 28,
                                      color: AppColors.tradGreen,
                                      fontFamily: FontFamily.robotoCondensed,
                                      height: 1.5,
                                    ),
                              ),
                            ),
                            Center(
                              child: Text(
                                AppLocalizations.of(context)!.login,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge!
                                    .copyWith(
                                      color: AppColors.tradGreen,
                                      height: 1.5,
                                    ),
                              ),
                            ),
                            const SizedBox(height: 24),
                            state.isDtpLogin ? _DtpLogin() : _VdLogin(),
                            if (settings.biometricsSettingVisibility)
                              const SizedBox(height: 24),
                            Container(
                              decoration: ShapeDecoration(
                                color: settings.biometricsSettingVisibility
                                    ? AppColors.lightGreen
                                    : AppColors.white,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(8),
                                  ),
                                ),
                              ),
                              padding: EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  BiometricLoginButton(
                                    isVisible: settings
                                        .biometricsLoginButtonVisibility,
                                    isBioButtonAvailable: settings
                                        .isBiometricsLoginButtonAvailable,
                                    onPressed: () async {
                                      isBioLoginPressed.value = true;
                                      final isLoading = ref.read(
                                        loginScreenProvider
                                            .select((state) => state.isLoading),
                                      );
                                      if (isLoading) return;

                                      // 閉塞チェック実行
                                      await controller
                                          .getBusinessBlockageStatus();

                                      // 非同期処理中にcontextがアンマウントされていないことを確認
                                      if (!context.mounted) return;

                                      final state =
                                          ref.read(loginScreenProvider);
                                      if (state.loginPhases ==
                                          LoginNextScreens.workBlockage) return;

                                      // 生体認証ログイン
                                      await controller.biometricsLogin();

                                      if (!context.mounted) return;
                                      analyticsLogController.sendButtonLog(
                                        buttonName: AppLocalizations.of(
                                          context,
                                        )!
                                            .loginBiometric,
                                        screenName: isDtpLogin
                                            ? AppLocalizations.of(context)!
                                                .dtpLogin
                                            : AppLocalizations.of(context)!
                                                .vdLogin,
                                      );
                                    },
                                  ),
                                  _BiometricsSettings(
                                    isVisible:
                                        settings.biometricsSettingVisibility,
                                    isUsingBiometrics: state.isUsingBiometrics,
                                    onChanged: controller.postBiometricsToggle,
                                  ),
                                ],
                              ),
                            ),
                            if (settings.biometricsSettingVisibility)
                              const SizedBox(height: 40),
                            state.isDtpLogin
                                ? TextButton(
                                    onPressed: () async {
                                      // FirebaseAnalyticsログ送信
                                      analyticsLogController.sendButtonLog(
                                        buttonName:
                                            AppLocalizations.of(context)!
                                                .forgotPassword,
                                        screenName:
                                            AppLocalizations.of(context)!
                                                .dtpLogin,
                                      );
                                      // DTPIDログイン画面では【IDaaS】PW再設定メールアドレス入力画面表示
                                      final idaasAgainMailAddressInputUrl =
                                          '${ref.watch(buildConfigProvider).idaasBaseUrl}${IdaasPath.resetPasswordInputEmailPath}';
                                      if (context.mounted) {
                                        await ref
                                            .watch(
                                              idaasResetPasswordInputEmailWebViewLauncherProvider,
                                            )
                                            .call(
                                              context,
                                              WebViewRequest.launchUrl(
                                                initialUrl:
                                                    idaasAgainMailAddressInputUrl,
                                              ),
                                              AppLocalizations.of(context)!
                                                  .forgotPassword,
                                              ScreenIdNumber.loginScreenId,
                                            );
                                      }
                                    },
                                    child: Row(
                                      children: [
                                        Text(
                                          AppLocalizations.of(context)!
                                              .forgotPassword,
                                          textAlign: TextAlign.center,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall!
                                              .copyWith(
                                                color: AppColors.tradGreen,
                                                height: 1.5,
                                              ),
                                        ),
                                        const SizedBox(width: 4),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            // SVGとTextのBaseラインがずれているため、調整用のSizedBox
                                            const SizedBox(height: 2),
                                            SvgPicture.asset(
                                              Assets.arrowRightIcon,
                                              width: 16,
                                              height: 16,
                                              colorFilter:
                                                  const ColorFilter.mode(
                                                AppColors.tradGreen,
                                                BlendMode.srcIn,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  )
                                : TextLinkCard(
                                    text: AppLocalizations.of(context)!
                                        .forgotPassword,
                                    onTap: () async {
                                      // FirebaseAnalyticsログ送信
                                      analyticsLogController.sendButtonLog(
                                        buttonName:
                                            AppLocalizations.of(context)!
                                                .forgotPassword,
                                        screenName:
                                            AppLocalizations.of(context)!
                                                .vdLogin,
                                      );
                                      await ref
                                          .read(urlLauncherProvider)
                                          .launchLinkId(
                                            LinkIds.forgotVdidPassword,
                                            AppLocalizations.of(context)!
                                                .forgotPassword,
                                            ScreenIdNumber.loginScreenId,
                                          );
                                    },
                                  ),
                            TextLinkCard(
                              text: AppLocalizations.of(context)!.vdInquiry,
                              onTap: () {
                                // FirebaseAnalyticsログ送信
                                analyticsLogController.sendButtonLog(
                                  buttonName:
                                      AppLocalizations.of(context)!.vdInquiry,
                                  screenName:
                                      AppLocalizations.of(context)!.vdLogin,
                                );
                                ref.read(urlLauncherProvider).launchLinkId(
                                      LinkIds.valueDoorIDFaq,
                                      AppLocalizations.of(context)!.vdInquiry,
                                      ScreenIdNumber.loginScreenId,
                                    );
                              },
                              isVisible: !state.isDtpLogin,
                            ),
                            const SizedBox(height: 24),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          controller.switchLoginScreen();
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName: isDtpLogin
                                ? AppLocalizations.of(context)!.loginVDID
                                : AppLocalizations.of(context)!
                                    .digitalTouchPointLogin,
                            screenName: isDtpLogin
                                ? AppLocalizations.of(context)!.dtpLogin
                                : AppLocalizations.of(context)!.vdLogin,
                          );
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          height: 56,
                          decoration: const ShapeDecoration(
                            color: AppColors.lightGreen,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.vertical(
                                bottom: Radius.circular(8),
                              ),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              state.isDtpLogin
                                  ? Text(
                                      AppLocalizations.of(context)!.loginVDID,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                            color: AppColors.tradGreen,
                                            height: 1.5,
                                          ),
                                    )
                                  : Text(
                                      AppLocalizations.of(context)!
                                          .digitalTouchPointLogin,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                            color: AppColors.tradGreen,
                                            height: 1.5,
                                          ),
                                    ),
                              const SizedBox(width: 4),
                              SvgPicture.asset(
                                Assets.arrowRightIcon,
                                width: 16,
                                height: 16,
                                colorFilter: const ColorFilter.mode(
                                  AppColors.tradGreen,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 80),
                      Expanded(child: const SizedBox.shrink()),
                      Image.asset(Assets.loginFooter),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _BackButton extends HookConsumerWidget {
  const _BackButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(loginScreenProvider);
    final firebaseController = ref.read(analyticsLogControllerProvider);

    final isDtpLogin = state.isDtpLogin;
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () async {
            // FirebaseAnalyticsログ送信
            firebaseController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName: isDtpLogin
                  ? AppLocalizations.of(context)!.dtpLogin
                  : AppLocalizations.of(context)!.vdLogin,
            );
            Navigator.of(context).pop();
          },
          child: Container(
            padding: EdgeInsets.fromLTRB(16, 16, 0, 0),
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // SVGとTextのBaseラインがずれているため、調整用のSizedBox
                    const SizedBox(height: 2),
                    SvgPicture.asset(
                      Assets.arrowLeftIcon,
                      width: 16,
                      height: 16,
                      colorFilter: const ColorFilter.mode(
                        AppColors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 4),
                Text(
                  AppLocalizations.of(context)!.back,
                  textAlign: TextAlign.center,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(color: AppColors.white),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class BiometricLoginButton extends StatelessWidget {
  const BiometricLoginButton({
    super.key,
    required this.isVisible,
    required this.isBioButtonAvailable,
    required this.onPressed,
  });

  final bool isVisible;
  final bool isBioButtonAvailable;
  final void Function() onPressed;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: Column(
        children: [
          Center(
            child: SizedBox(
              height: 48,
              width: 240,
              child: ColoredBox(
                color: isBioButtonAvailable
                    ? AppColors.white
                    : AppColors.inactiveText,
                child: OutlinedButton(
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    elevation: 0,
                    side: BorderSide(color: AppColors.tradGreen),
                  ),
                  onPressed: isBioButtonAvailable ? onPressed : null,
                  child: Center(
                    child: Text(
                      AppLocalizations.of(context)!.loginBiometric,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppColors.tradGreen,
                          ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class _LoginVaridationErrorInformation extends StatelessWidget {
  const _LoginVaridationErrorInformation({
    required this.error,
  });

  final ValidationError error;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Center(
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: AppColors.lightRed,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        SizedBox(height: 3),
                        SizedBox(
                          height: 16,
                          width: 16,
                          child: SvgPicture.asset(
                            Assets.loginVaridation,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      width: 3,
                    ),
                    Expanded(
                      child: Text(
                        error.toErrorMessage(context, '') ?? '',
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall!
                            .copyWith(color: AppColors.red),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}

/// DTPログイン
class _DtpLogin extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(loginScreenProvider);
    final controller = ref.watch(loginScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final dtpIdController = useTextEditingController();
    final dtpPassController = useTextEditingController();
    final isNotEmptyDtpId = useState(dtpIdController.text.isNotEmpty);
    final isNotEmptyDtpPass = useState(dtpPassController.text.isNotEmpty);
    final appLocalizations = AppLocalizations.of(context)!;

    final isLoginValidationError =
        state.loginValidationError != const ValidationError.none();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: isLoginValidationError,
          child: _LoginVaridationErrorInformation(
            error: state.loginValidationError,
          ),
        ),
        Text(
          AppLocalizations.of(context)!.idMailAddress,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
                fontFamily: FontFamily.robotoCondensed,
              ),
        ),
        const SizedBox(height: 16),
        Center(
          child: SizedBox(
            child: NotCopyTextFormField(
              style: TextStyle(
                color: isLoginValidationError
                    ? AppColors.red
                    : AppColors.textBlack,
                height: 1.5,
              ),
              textAlignVertical: TextAlignVertical.center,
              controller: dtpIdController,
              textAlign: TextAlign.left,
              decoration: InputDecoration(
                hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.grayGreen,
                      height: 1.5,
                    ),
                hintText: AppLocalizations.of(context)!.idMailAddressExample,
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: isLoginValidationError
                        ? AppColors.red
                        : AppColors.borderGrayGreen,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: isLoginValidationError
                        ? AppColors.red
                        : AppColors.borderGrayGreen,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                fillColor: isLoginValidationError
                    ? AppColors.lightRed
                    : AppColors.basicBackgroundLightGray,
                filled: true,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 16,
                ),
              ),
              onChanged: (_) {
                isNotEmptyDtpId.value = dtpIdController.text.isNotEmpty;
                if (state.loginValidationError != ValidationError.none()) {
                  controller.clearLoginValidationError();
                }
              },
            ),
          ),
        ),
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.password,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 16),
        Center(
          child: SizedBox(
            child: NotCopyTextFormField(
              style: TextStyle(
                color: isLoginValidationError
                    ? AppColors.red
                    : AppColors.textBlack,
                height: 1.5,
              ),
              textAlignVertical: TextAlignVertical.center,
              controller: dtpPassController,
              textAlign: TextAlign.left,
              decoration: InputDecoration(
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: isLoginValidationError
                        ? AppColors.red
                        : AppColors.borderGrayGreen,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: isLoginValidationError
                        ? AppColors.red
                        : AppColors.borderGrayGreen,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                fillColor: isLoginValidationError
                    ? AppColors.lightRed
                    : AppColors.basicBackgroundLightGray,
                filled: true,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: 0,
                  horizontal: 16,
                ),
                suffixIcon: IconButton(
                  icon: SvgPicture.asset(
                    state.isPasswordVisible
                        ? Assets.visibleIcon
                        : Assets.invisibleIcon,
                    colorFilter: ColorFilter.mode(
                      isLoginValidationError
                          ? AppColors.red
                          : AppColors.tradGreen,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!
                          .togglePasswordVisibility,
                      screenName: AppLocalizations.of(context)!.dtpLogin,
                    );
                    controller.togglePassWordVisibility();
                  },
                ),
              ),
              obscureText: !state.isPasswordVisible,
              obscuringCharacter: '●',
              onChanged: (_) {
                isNotEmptyDtpPass.value = dtpPassController.text.isNotEmpty;
                if (state.loginValidationError != ValidationError.none()) {
                  controller.clearLoginValidationError();
                }
              },
              textInputAction: TextInputAction.done,
            ),
          ),
        ),
        const SizedBox(height: 40),
        Center(
          child: SizedBox(
            height: 48,
            width: 240,
            child: OutlinedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                elevation: 0,
                backgroundColor: AppColors.tradGreen,
                disabledBackgroundColor: AppColors.tradGreen,
              ),
              onPressed: () async {
                final isLoading = ref.read(
                  loginScreenProvider.select((state) => state.isLoading),
                );
                if (isLoading) return;

                //DTP_IDとPasswordのバリデーション実施
                controller.dtpInputs(
                  dtpIdController.text,
                  dtpPassController.text,
                );
                final stateLoginValidationError = ref.read(loginScreenProvider);
                if (stateLoginValidationError.loginValidationError !=
                    ValidationError.none()) {
                  // Passwordの入力内容をリセット
                  dtpPassController.text = '';
                  // バリデーションエラーであれば処理中断
                  return;
                }

                // 閉塞チェック実行
                await controller.getBusinessBlockageStatus();

                // 非同期処理中にcontextがアンマウントされていないことを確認
                if (!context.mounted) return;
                final state = ref.read(loginScreenProvider);
                // 正常系のログイン処理
                if (state.loginPhases != LoginNextScreens.workBlockage) {
                  controller.clearLoginError();
                  await controller.loginDtpId(
                    dtpIdController.text,
                    dtpPassController.text,
                    true,
                  );
                  // FirebaseAnalyticsログ送信
                  // awaitすると処理が遅くなるため、完了をまたない
                  analyticsLogController.sendButtonLog(
                    buttonName: appLocalizations.login,
                    screenName: appLocalizations.dtpLogin,
                  );
                }
              },
              child: Center(
                child: Text(
                  AppLocalizations.of(context)!.login,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.white,
                        height: 1.5,
                      ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 生体認証利用設定
class _BiometricsSettings extends StatelessWidget {
  const _BiometricsSettings({
    required this.isVisible,
    required this.isUsingBiometrics,
    required this.onChanged,
  });

  final bool isVisible;
  final bool isUsingBiometrics;
  final void Function(bool isUsingBiometrics) onChanged;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppLocalizations.of(context)!.allowBiometricLogin,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: AppColors.textBlack,
                    height: 1.5,
                  ),
            ),
            SizedBox(
              height: 31,
              width: 51,
              child: CupertinoSwitch(
                activeColor: AppColors.tradGreen,
                value: isUsingBiometrics,
                onChanged: onChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// VDログイン
class _VdLogin extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(loginScreenProvider);
    final controller = ref.watch(loginScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    // VDIDログイン用の変数
    final vdIdController = useTextEditingController();
    final vdPassController = useTextEditingController();
    final isNotEmptyVdId = useState(vdIdController.text.isNotEmpty);
    final isNotEmptyVdPass = useState(vdPassController.text.isNotEmpty);
    final appLocalizations = AppLocalizations.of(context)!;

    final isLoginValidationError =
        state.loginValidationError != const ValidationError.none();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          visible: isLoginValidationError,
          child: _LoginVaridationErrorInformation(
            error: state.loginValidationError,
          ),
        ),
        Text(
          AppLocalizations.of(context)!.authenticationMethod,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
              ),
        ),
        const SizedBox(height: 16),
        Text(
          AppLocalizations.of(context)!.passwordNinshou,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: FontWeight.w400,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!
              .electronicAndIcCardAuthenticaionAreNotAvailable,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 12,
              ),
        ),
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.valueDoorID,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
              ),
        ),
        const SizedBox(height: 16),
        Center(
          child: NotCopyTextFormField(
            style: TextStyle(
              color:
                  isLoginValidationError ? AppColors.red : AppColors.textBlack,
              height: 1.5,
            ),
            textAlignVertical: TextAlignVertical.center,
            controller: vdIdController,
            textAlign: TextAlign.left,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context)!.vdIdExample,
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: isLoginValidationError
                      ? AppColors.red
                      : AppColors.borderGrayGreen,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: isLoginValidationError
                      ? AppColors.red
                      : AppColors.borderGrayGreen,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              fillColor: isLoginValidationError
                  ? AppColors.lightRed
                  : AppColors.basicBackgroundLightGray,
              filled: true,
              contentPadding: const EdgeInsets.symmetric(
                vertical: 0,
                horizontal: 16,
              ),
            ),
            onChanged: (_) {
              isNotEmptyVdId.value = vdIdController.text.isNotEmpty;
              if (state.loginValidationError != ValidationError.none()) {
                controller.clearLoginValidationError();
              }
            },
          ),
        ),
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.password,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
              ),
        ),
        const SizedBox(height: 16),
        Center(
          child: NotCopyTextFormField(
            style: TextStyle(
              color:
                  isLoginValidationError ? AppColors.red : AppColors.textBlack,
              height: 1.5,
            ),
            textAlignVertical: TextAlignVertical.center,
            controller: vdPassController,
            textAlign: TextAlign.left,
            decoration: InputDecoration(
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: isLoginValidationError
                      ? AppColors.red
                      : AppColors.borderGrayGreen,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: isLoginValidationError
                      ? AppColors.red
                      : AppColors.borderGrayGreen,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              fillColor: isLoginValidationError
                  ? AppColors.lightRed
                  : AppColors.basicBackgroundLightGray,
              filled: true,
              contentPadding: const EdgeInsets.symmetric(
                vertical: 0,
                horizontal: 16,
              ),
              suffixIcon: IconButton(
                icon: SvgPicture.asset(
                  state.isPasswordVisible
                      ? Assets.visibleIcon
                      : Assets.invisibleIcon,
                  colorFilter: ColorFilter.mode(
                    isLoginValidationError
                        ? AppColors.red
                        : AppColors.tradGreen,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName:
                        AppLocalizations.of(context)!.togglePasswordVisibility,
                    screenName: AppLocalizations.of(context)!.vdLogin,
                  );
                  controller.togglePassWordVisibility();
                },
              ),
            ),
            obscureText: !state.isPasswordVisible,
            obscuringCharacter: '●',
            onChanged: (_) {
              isNotEmptyVdPass.value = vdPassController.text.isNotEmpty;
              if (state.loginValidationError != ValidationError.none()) {
                controller.clearLoginValidationError();
              }
            },
            textInputAction: TextInputAction.done,
          ),
        ),
        const SizedBox(height: 40),
        Center(
          child: SizedBox(
            height: 48,
            width: 240,
            child: OutlinedButton(
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
                elevation: 0,
                backgroundColor: AppColors.tradGreen,
                disabledBackgroundColor: AppColors.tradGreen,
              ),
              onPressed: () async {
                final isLoading = ref.read(
                  loginScreenProvider.select((state) => state.isLoading),
                );
                if (isLoading) return;

                //VDIDとPasswordのバリデーション実施
                controller.vdInputs(
                  vdIdController.text,
                  vdPassController.text,
                );
                final stateLoginValidationError = ref.read(loginScreenProvider);
                if (stateLoginValidationError.loginValidationError !=
                    ValidationError.none()) {
                  // Passwordの入力内容をリセット
                  vdPassController.text = '';
                  // バリデーションエラーであれば処理中断
                  return;
                }
                // 閉塞チェック実行
                await controller.getBusinessBlockageStatus();
                // 非同期処理中にcontextがアンマウントされていないことを確認
                if (!context.mounted) return;
                final state = ref.read(loginScreenProvider);
                // 正常系のログイン処理
                if (state.loginPhases != LoginNextScreens.workBlockage) {
                  controller.clearLoginError();
                  await controller.login(
                    vdIdController.text,
                    vdPassController.text,
                    true,
                  );
                  // FirebaseAnalyticsログ送信
                  // awaitすると処理が遅くなるため、完了をまたない
                  analyticsLogController.sendButtonLog(
                    buttonName: appLocalizations.login,
                    screenName: appLocalizations.vdLogin,
                  );
                }
              },
              child: Center(
                child: Text(
                  AppLocalizations.of(context)!.login,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.white,
                      ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 画面遷移を処理する関数
void _handleNavigation(BuildContext context, WidgetRef ref) {
  final state = ref.watch(loginScreenProvider);
  final controller = ref.watch(loginScreenProvider.notifier);
  final analyticsLogController = ref.read(analyticsLogControllerProvider);
  final operationLogController = ref.watch(
    operationLogControllerProvider,
  );
  final businessBlockageController =
      ref.watch(businessBlockageControllerProvider);
  final isDtpLogin = state.isDtpLogin;
  final MemoryDataManager mdManager = ref.watch(mdManagerProvider);

  /// 生体認証利用キャンセルダイアログ
  Future<void> showBiometricCancellationDialog(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CommonDialog.okCancel(
        message: appLocalizations.cancelBiometricLogin,
        cancelButtonText: appLocalizations.off,
        okButtonText: appLocalizations.cancel,
        onCancelPressed: () {
          controller.cancelUsingBiometrics();
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.off,
            screenName: isDtpLogin
                ? AppLocalizations.of(context)!.dtpLogin
                : AppLocalizations.of(context)!.vdLogin,
          );
        },
        onOkPressed: () {
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.cancel,
            screenName: isDtpLogin
                ? AppLocalizations.of(context)!.dtpLogin
                : AppLocalizations.of(context)!.vdLogin,
          );
        },
        isPopAfterCancelButtonPress: true,
        isPopAfterOkButtonPress: true,
      ),
    );
  }

  /// 生体認証利用確認ダイアログ
  /// 現状初回確認時と同じデザインであるが、改修の可能性有りとのことから別で定義
  Future<void> showBiometricsConfirmationDialog(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CommonDialog.okCancel(
        message: AppLocalizations.of(context)!.checkBiometricsLogin,
        okButtonText: appLocalizations.use,
        onOkPressed: () {
          controller.setUsingBiometrics();
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.use,
            screenName: isDtpLogin
                ? appLocalizations.dtpLogin
                : appLocalizations.vdLogin,
          );
        },
        onCancelPressed: () {
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.cancel,
            screenName: isDtpLogin
                ? appLocalizations.dtpLogin
                : appLocalizations.vdLogin,
          );
        },
        isPopAfterCancelButtonPress: true,
        isPopAfterOkButtonPress: true,
      ),
    );
  }

  Future<void> showWillBiometricsDialog(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CommonDialog.okCancel(
        message: AppLocalizations.of(context)!.checkBiometricsLogin,
        onOkPressed: () {
          controller.setBiometricsLoginInfo(true);
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.useBiometric,
            screenName: isDtpLogin
                ? appLocalizations.dtpLogin
                : appLocalizations.vdLogin,
          );
        },
        onCancelPressed: () {
          controller.setBiometricsLoginInfo(false);
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.notUseBiometric,
            screenName: isDtpLogin
                ? appLocalizations.dtpLogin
                : appLocalizations.vdLogin,
          );
        },
        isPopAfterCancelButtonPress: true,
        isPopAfterOkButtonPress: true,
      ),
    );
  }

  /// ID/PW上書き確認ダイアログ
  Future<void> showIdPwOverwriteConfirmationDialog(BuildContext context) async {
    final appLocalizations = AppLocalizations.of(context)!;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CommonDialog.okCancel(
        message: AppLocalizations.of(context)!.checkOverwriteIdPassword,
        onOkPressed: () {
          controller.setBiometricsLoginInfoWithoutAuth(true);
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.overwriteOk,
            screenName: isDtpLogin
                ? AppLocalizations.of(context)!.dtpLogin
                : AppLocalizations.of(context)!.vdLogin,
          );
        },
        onCancelPressed: () {
          controller.setBiometricsLoginInfoWithoutAuth(false);
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.overwriteCancel,
            screenName: isDtpLogin
                ? AppLocalizations.of(context)!.dtpLogin
                : AppLocalizations.of(context)!.vdLogin,
          );
        },
        isPopAfterCancelButtonPress: true,
        isPopAfterOkButtonPress: true,
      ),
    );
  }

  // 画面遷移の処理
  WidgetsBinding.instance.addPostFrameCallback((_) async {
    if (!context.mounted) return;
    // API読み込み時にローディング
    if (state.isLoading) {
      LoadingDialog.loading(context);
    }
    if (!state.isLoading || state.error != null) {
      LoadingDialog.loadingEnd(context);
    }

    switch (state.loginPhases) {
      case LoginNextScreens.checkFirstTimeBiometricsDialog:
        controller.resetNextScreen();
        // 次回以降の生体認証利用確認ダイアログ表示
        await showWillBiometricsDialog(context);
      case LoginNextScreens.iDaaSOtpScreen:
        // 画面が複数回プッシュされるのを防ぐため、loginPhaseをリセットしておく
        controller.resetNextScreen();
        // 高リスクユーザーIDが返却された場合、OTP認証画面を呼び出す
        final highRiskUserId = state.highRiskUserId;
        if (highRiskUserId != null) {
          // 【IDaaS】OTP入力画面
          final encryptedCookie = await mdManager.encryptedCookie.load();
          if (!context.mounted) return;
          // 【IDaaS】入力画面webView立ち上げ
          final res = await ref.watch(otpInputWebViewLauncherProvider).call(
                context: context,
                encryptedCookie: encryptedCookie,
                buttonName: AppLocalizations.of(context)!.login,
                screenIdNumber: ScreenIdNumber.loginScreenId,
              );
          // webViewを閉じた際の挙動によって以下実施
          await res.when(
            success: (idaasEncryptedCookie) async {
              await controller.verifyHighRiskUserId(
                highRiskUserId: highRiskUserId,
                idaasTicket: idaasEncryptedCookie.ticket,
              );
            },
            cancel: () {
              // キャンセルされた場合は何もしない
            },
            error: (error) {
              // エラーが発生した場合はダイアログを表示する
              controller.notifyLoginError(
                AppError(
                  message: ErrorInfo.defaultErrorMessage,
                  code: ErrorInfo.defaultErrorCode,
                ),
              );
            },
          );
        }
      case LoginNextScreens.checkTermsScreen:
        // 複数回の画面遷移を防止するため、loginPhaseをリセット
        controller.resetNextScreen();
        // 利用規定同意画面に遷移
        await Navigator.pushNamed(
          context,
          RouteNames.terms,
        ).then((result) async {
          // 利用規定同意画面の戻り値で処理分け
          if (result != null) {
            // 同意して進むボタン押下時は利用規定同意状況を登録
            await controller.registerConsentStatus(result as List<String>);
            // 非同期処理中にcontextがアンマウントされていないことを確認
            if (!context.mounted) return;
            final state = ref.read(loginScreenProvider);

            // 顧客操作ログ送信
            await operationLogController.sendOperationLog(
              functionLog: OperationLogMessage.termsOfUse,
              operationLog: OperationLogMessage.agree,
              resultLog: state.error == null
                  ? OperationLogMessage.normal
                  : OperationLogMessage.abnormality,
              errorIdLog: state.error?.code ?? '',
            );
          } else {
            // 同意しないボタン押下時はログアウト
            await controller.logout();
          }
        });
      case LoginNextScreens.authScreen:
        // 12.認可画面表示
        // 複数回の画面遷移を防止するため、loginPhaseをリセット
        controller.resetNextScreen();
        final fromEbizParam = state.fromEbizParam;
        if (fromEbizParam is String && fromEbizParam.isNotEmpty) {
          // 認可画面を表示する
          final authUrl = ref.watch(buildConfigProvider).authWebViewUrl;
          await ref.watch(authWebViewLauncherProvider).call(
                context,
                WebViewRequest.launchUrl(initialUrl: authUrl),
                fromEbizParam: fromEbizParam,
                screenIdNumber: ScreenIdNumber.loginScreenId,
              );

          // トークン要求
          // 認可拒否時にも実行はするものの、repositoryにてハンドリングされている為処理自体は走らない
          await controller.getToken(false);
        }
      case LoginNextScreens.homeScreen:
        // 14.ログイン完了
        // ログイン状態を更新する
        controller.resetNextScreen();

        await ref
            .watch(homeNavigationScreenProvider.notifier)
            .fetchLoginStatus();

        /// ローディングが表示されている事もあるため、ログイン画面を必ずPOPさせる形で実装
        if (!context.mounted) return;
        await Navigator.of(context).pushNamedAndRemoveUntil(
          RouteNames.homeNavigation,
          (route) => false,
        );
      case LoginNextScreens.biometricsCancellationDialog:
        controller.resetNextScreen();
        await showBiometricCancellationDialog(context);
      case LoginNextScreens.biometricsConfirmationDialog:
        controller.resetNextScreen();
        await showBiometricsConfirmationDialog(context);
      case LoginNextScreens.checkOverwriteIdPwDialog:
        controller.resetNextScreen();
        await showIdPwOverwriteConfirmationDialog(context);
      case LoginNextScreens.workBlockage:
        controller.resetNextScreen();

        /// 業務閉塞のエラーダイアログ表示
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => CommonDialog.okCancel(
            title: AppLocalizations.of(context)!.sorryScreenErrorTitle,
            message: AppLocalizations.of(context)!.sorryScreenErrorMessage,
            messageTextStyle: Theme.of(context).textTheme.bodySmall,
            cancelButtonText: Platform.isIOS
                ? ' ${AppLocalizations.of(context)!.sorryScreenErrorButton1} '
                : AppLocalizations.of(context)!.sorryScreenErrorButton1,
            okButtonText: AppLocalizations.of(context)!.sorryScreenErrorButton2,
            isPopAfterOkButtonPress: false,
            onOkPressed: () async {
              analyticsLogController.sendButtonLog(
                buttonName:
                    AppLocalizations.of(context)!.sorryScreenErrorButton2,
                screenName: isDtpLogin
                    ? AppLocalizations.of(context)!.dtpLogin
                    : AppLocalizations.of(context)!.vdLogin,
              );
              // 法人アプリのストア画面に遷移
              await ref
                  .watch(urlLauncherProvider)
                  .launchCorporationAppStorePage(
                    AppLocalizations.of(context)!.sorryScreenErrorButton2,
                    ScreenIdNumber.loginScreenId,
                  );
              businessBlockageController.clearBlockageStatus();
            },
            onCancelPressed: () async {
              businessBlockageController.clearBlockageStatus();
              analyticsLogController.sendButtonLog(
                buttonName:
                    AppLocalizations.of(context)!.sorryScreenErrorButton1,
                screenName: isDtpLogin
                    ? AppLocalizations.of(context)!.dtpLogin
                    : AppLocalizations.of(context)!.vdLogin,
              );
              await Future.delayed(Duration(milliseconds: 500));
              exit(0);
            },
          ),
        );
      default:
    }
  });
}
