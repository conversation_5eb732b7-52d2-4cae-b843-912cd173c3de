import 'package:dtp_app/views/screens/account_inquiry/account_inquiry_screen.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/credit_card_statement_inquiry_screen.dart';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class InquiryScreen extends HookConsumerWidget {
  const InquiryScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeNavigationScreenProvider);
    switch (state.inquiryType) {
      case InquiryType.accountInquiry:
        return const AccountInquiryScreen();
      case InquiryType.creditCardStatementInquiry:
        return const CreditCardStatementInquiryScreen();
      default:
        return Container();
    }
  }
}
