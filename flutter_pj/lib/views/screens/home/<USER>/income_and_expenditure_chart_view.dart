import 'dart:math';

import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:dtp_app/gen/fonts.gen.dart';

class IncomeAndExpenditureChartView extends StatelessWidget {
  const IncomeAndExpenditureChartView({
    super.key,
    required this.data,
  });

  /// グラフの下端の線の色
  static const _colorBottomLine = AppColors.grayGreen;

  /// グラフの横線の色
  static const _colorHorizontalLine = AppColors.borderGrayGreen;

  /// 下の項目名ラベルの縦幅
  static const _bottomTitlesReservedSize = 40.0;

  /// 左の項目名ラベルの横幅
  static const _leftTitlesReservedSize = 53.0;

  /// ラベルのテキストスタイル（通常）
  static const _labelTextStyle = TextStyle(
    fontFamily: FontFamily.robotoCondensed,
    color: AppColors.textBlack,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    height: 1.5,
  );

  /// ラベルのテキストスタイル（太字）
  static const _labelTextStyleBold = TextStyle(
    fontFamily: FontFamily.robotoCondensed,
    color: AppColors.textBlack,
    fontSize: 12,
    fontWeight: FontWeight.bold,
    height: 1.5,
  );

  static const Gradient _incomeGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.freshGreen,
      AppColors.freshGreenDark,
    ],
  );

  static const Gradient _expenditureGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      AppColors.skyBlue,
      AppColors.skyBlueDark,
    ],
  );

  /// 計算用の定数
  static const double _man = 10000;
  static const double _oku = 100000000;
  static const double _cho = 1000000000000;

  /// 表示対象となるデータ
  final List<IncomeAndExpenditureChartData> data;

  /// グラフの最大値となる値
  double get _upperLimit {
    // 与えられたデータ上の最大値を求める
    final maxValue = [
      0,
      ...data.map((e) => e.incomeValue),
      ...data.map((e) => e.expenditureValue),
    ].reduce((a, b) => max(a, b));
    // 1桁繰上げた値を計算する
    // 例) 123 -> 1000, 1234 -> 10000
    // 文字列に変換後、文字数を数える
    final num = pow(10, maxValue.toString().length);
    if (num > maxValue * 2) {
      // 得られた値がデータの最大値の2倍より大きければ、2で割って返す
      return num / 2;
    } else {
      // そうでないなら、そのまま返す
      return num.toDouble();
    }
  }

  /// グラフの縦軸の間隔
  double get _verticalInterval => _upperLimit / 5;

  /// グラフの縦軸の目盛りとして表示する文字列を生成
  /// 例) 10000 -> '1万', 100000000 -> '1億'
  String _getVerticalTickTitle(int y, BuildContext context) {
    if (y < _man) {
      return y.withCommas;
    }
    if (y < _oku) {
      final num = y / _man;
      return '${num.toInt().withCommas}${AppLocalizations.of(context)!.man}';
    }
    if (y < _cho) {
      final num = y / _oku;
      return '${num.toInt().withCommas}${AppLocalizations.of(context)!.oku}';
    }
    final num = y / _cho;
    return '${num.toInt().withCommas}${AppLocalizations.of(context)!.cho}';
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 339,
      child: Padding(
        padding: const EdgeInsets.only(bottom: 24),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    gradient: _incomeGradient,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  AppLocalizations.of(context)!.income,
                  style: _labelTextStyle,
                ),
                const SizedBox(width: 16),
                Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    gradient: _expenditureGradient,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  AppLocalizations.of(context)!.expenditure,
                  style: _labelTextStyle,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: BarChart(
                BarChartData(
                  minY: 0,
                  maxY: _upperLimit,
                  titlesData: FlTitlesData(
                    topTitles: const AxisTitles(),
                    rightTitles: const AxisTitles(),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: _bottomTitlesReservedSize,
                        getTitlesWidget: (x, meta) {
                          // グラフの横軸に表示するラベル名を生成する
                          // xはBarChartGroupDataのxに設定した値
                          final item = data[x.toInt()];
                          //一番最後のラベル(当日)の判定
                          final isToday = x.toInt() == data.length - 1;
                          return Column(
                            children: [
                              SizedBox(height: 4),
                              Text(
                                item.date,
                                textAlign: TextAlign.center,
                                style: isToday
                                    ? _labelTextStyleBold.copyWith(
                                        color: AppColors.tradGreen,
                                        height: 1.5,
                                      )
                                    : _labelTextStyle,
                              ),
                              Text(
                                item.getDayOfWeek(context),
                                textAlign: TextAlign.center,
                                style: isToday
                                    ? _labelTextStyleBold.copyWith(
                                        color: AppColors.tradGreen,
                                        height: 1.5,
                                      )
                                    : _labelTextStyle,
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (y, meta) {
                          // グラフの縦軸に表示するラベル名を生成する
                          // yには最大値をintervalで分けた値が入る
                          // 例えば、最大値が1000でintervalが200の場合、
                          // yは0, 200, 400, 600, 800, 1000となる

                          // 縦軸の最大値の判定
                          final isUpperLimit = y.toInt() == _upperLimit;

                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                isUpperLimit
                                    ? Column(
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)!.yenJp,
                                            textAlign: TextAlign.end,
                                            style: _labelTextStyle.copyWith(
                                              fontFamily:
                                                  FontFamily.robotoCondensed,
                                              color: AppColors.grayGreen,
                                              height: 1,
                                            ),
                                          ),
                                          SizedBox(height: 6),
                                        ],
                                      )
                                    : SizedBox.shrink(),
                                Text(
                                  _getVerticalTickTitle(y.toInt(), context),
                                  textAlign: TextAlign.end,
                                  style: _labelTextStyle.copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                    color: AppColors.grayGreen,
                                    height: 1,
                                  ),
                                ),
                                isUpperLimit
                                    ? Text(
                                        '',
                                        textAlign: TextAlign.end,
                                        style: _labelTextStyle.copyWith(
                                          fontFamily:
                                              FontFamily.robotoCondensed,
                                          color: AppColors.grayGreen,
                                          height: 1.5,
                                        ),
                                      )
                                    : SizedBox.shrink(),
                                y.toInt() != 0
                                    ? SizedBox(
                                        height: 2,
                                      )
                                    : SizedBox.shrink(),
                              ],
                            ),
                          );
                        },
                        reservedSize: _leftTitlesReservedSize,
                        interval: _verticalInterval,
                      ),
                    ),
                  ),
                  gridData: FlGridData(
                    drawVerticalLine: false,
                    horizontalInterval: _verticalInterval,
                    getDrawingHorizontalLine: (value) {
                      return const FlLine(
                        color: _colorHorizontalLine,
                        strokeWidth: 1,
                      );
                    },
                  ),
                  borderData: FlBorderData(
                    border: const Border(
                      // グラフの下端の横線
                      bottom: BorderSide(
                        color: _colorBottomLine,
                        width: 1,
                      ),
                      // グラフの上端の横線
                      top: BorderSide(
                        color: _colorHorizontalLine,
                        width: 1,
                      ),
                    ),
                  ),
                  barTouchData: BarTouchData(
                    enabled: false,
                  ),
                  barGroups: data.mapIndexed(
                    (element, index) {
                      // 棒グラフのデータを生成する
                      return BarChartGroupData(
                        x: index,
                        barsSpace: 2,
                        barRods: [
                          // 入金額の棒グラフ
                          BarChartRodData(
                            toY: element.incomeValue.toDouble(),
                            gradient: _incomeGradient,
                            width: 8,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(2),
                              topRight: Radius.circular(2),
                            ),
                          ),
                          // 出金額の棒グラフ
                          BarChartRodData(
                            toY: element.expenditureValue.toDouble(),
                            gradient: _expenditureGradient,
                            width: 8,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(2),
                              topRight: Radius.circular(2),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 入出金グラフのデータ
class IncomeAndExpenditureChartData {
  const IncomeAndExpenditureChartData({
    required this.dateTime,
    required this.incomeValue,
    required this.expenditureValue,
  });

  /// 日付
  final DateTime dateTime;

  /// 入金額
  final int incomeValue;

  /// 出金額
  final int expenditureValue;

  /// 日付（MM/dd）
  String get date => '${dateTime.month}/${dateTime.day}';

  /// 曜日（月火水木金土日）
  String getDayOfWeek(BuildContext context) => [
        AppLocalizations.of(context)!.monday,
        AppLocalizations.of(context)!.tuesday,
        AppLocalizations.of(context)!.wednesday,
        AppLocalizations.of(context)!.thursday,
        AppLocalizations.of(context)!.friday,
        AppLocalizations.of(context)!.saturday,
        AppLocalizations.of(context)!.sunday,
      ][dateTime.weekday - 1];
}
