import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FreeeLinkageFlowModal extends HookConsumerWidget {
  FreeeLinkageFlowModal({super.key});

  final _pageController = PageController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const index = 0;
    const maxIndex = 2; // ウォークスルーの最大ページ
    final indexNotifier = useState(index);
    final isCompleteLoad = useState(false);
    bool isNextButtonEnabled = true;
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final state = ref.watch(homeScreenProvider);
    final operationLogController = ref.watch(operationLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          isCompleteLoad.value = false;
          // 取引先ID紐付け確認APIの実行
          await controller.checkFreeeLinksByClientId();
          isCompleteLoad.value = true;
        });
        return null;
      },
      const [],
    );

    if (state.checkFreeeLinksByClientIdError != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.microtask(
          () {
            if (!context.mounted) return;
            Navigator.pop(context);
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return CommonDialog.ok(
                  message: state.checkFreeeLinksByClientIdError?.message ??
                      ErrorInfo.defaultErrorMessage,
                  onOkPressed: () {
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!
                          .clearFreeeLinksByClientIdError,
                      screenName: AppLocalizations.of(context)!.home,
                    );
                    controller.clearFreeeLinksByClientIdError();
                  },
                );
              },
            );
          },
        );
      });
    }

    return SafeArea(
      child: isCompleteLoad.value
          ? Stack(
              children: [
                Column(
                  children: [
                    const SizedBox(height: 40),
                    Expanded(
                      child: (state.exists)
                          ? _FreeeLinkageEmployee()
                          : _FreeeLinkageRepresentativeBanker(
                              pageController: _pageController,
                              indexNotifier: indexNotifier,
                            ),
                    ),
                    SizedBox(
                      height: state.exists ? 24 : 16,
                    ),
                    (state.exists)
                        ? Text(
                            AppLocalizations.of(context)!.ifYouHaveFreeeId,
                            style: Theme.of(context).textTheme.bodySmall,
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: List.generate(
                              3,
                              (index) => _DotIndicator(
                                currentIndex: indexNotifier.value,
                                index: index,
                              ),
                            ),
                          ),
                    const SizedBox(height: 16),
                    state.exists
                        ? _FinishButton(
                            isRepresentativeBank: state.exists,
                            onPressed: () async {
                              // FirebaseAnalyticsログ送信
                              analyticsLogController.sendButtonLog(
                                buttonName:
                                    AppLocalizations.of(context)!.freeeLink,
                                screenName: AppLocalizations.of(context)!.home,
                              );
                              // freeeSSO画面リダイレクトURLを取得
                              final redirectUrl = ref
                                  .read(buildConfigProvider)
                                  .freeeRedirectScreen;
                              final url = await controller.getUrlToFreeeSSOPage(
                                redirectUrl,
                                FreeeSSOTypeFromHome.firstTime,
                              );
                              final state = ref.watch(homeScreenProvider);
                              // Todo DTPO-6557にエラー仕様の記載がないため現在仮実装 (DTPO-18249にて対応予定)
                              if (state.getUrlToFreeeSsoParamsError != null) {
                                // 操作ログ送信
                                unawaited(
                                  operationLogController.sendOperationLog(
                                    functionLog:
                                        OperationLogMessage.freeeIntegration,
                                    operationLog:
                                        OperationLogMessage.freeeIntegration,
                                    resultLog: OperationLogMessage.abnormality,
                                    errorIdLog: state
                                            .getUrlToFreeeSsoParamsError
                                            ?.code ??
                                        '',
                                  ),
                                );
                              } else {
                                // freeeサインアップ画面を外部ブラウザで表示
                                if (context.mounted && url != null) {
                                  // 顧客操作ログ送信
                                  unawaited(
                                    operationLogController.sendOperationLog(
                                      functionLog:
                                          OperationLogMessage.freeeIntegration,
                                      operationLog:
                                          OperationLogMessage.freeeIntegration,
                                      resultLog: OperationLogMessage.normal,
                                      errorIdLog: '',
                                    ),
                                  );
                                  final urlLauncher =
                                      ref.watch(urlLauncherProvider);
                                  if (await urlLauncher.canLaunch(url)) {
                                    if (!context.mounted) return;
                                    // freee連携画面を外部ブラウザで開く
                                    await urlLauncher.launchExternalUrl(
                                      AppLocalizations.of(context)!
                                          .freeeLoginButton,
                                      ScreenIdNumber.freeeLinkLoginModalId,
                                      url,
                                      useEncryptedCookie: true,
                                    );
                                  }
                                }
                              }
                            },
                          )
                        : Align(
                            alignment: Alignment.bottomCenter,
                            child: (indexNotifier.value != 2)
                                ? _NextButton(
                                    onPressed: () async {
                                      // FirebaseAnalyticsログ送信
                                      analyticsLogController.sendButtonLog(
                                        buttonName:
                                            AppLocalizations.of(context)!
                                                .freeeLinkageModalNext,
                                        screenName:
                                            AppLocalizations.of(context)!.home,
                                      );
                                      if (isNextButtonEnabled) {
                                        if (indexNotifier.value < maxIndex) {
                                          indexNotifier.value++;
                                          await _pageController.nextPage(
                                            duration: const Duration(
                                              milliseconds: 300,
                                            ),
                                            curve: Curves.easeIn,
                                          );
                                        } else {
                                          isNextButtonEnabled = false;
                                        }
                                      }
                                    },
                                  )
                                : _FinishButton(
                                    isRepresentativeBank: state.exists,
                                    onPressed: () async {
                                      // FirebaseAnalyticsログ送信
                                      analyticsLogController.sendButtonLog(
                                        buttonName:
                                            AppLocalizations.of(context)!
                                                .freeeLink,
                                        screenName:
                                            AppLocalizations.of(context)!.home,
                                      );
                                      // freeeSSOリダイレクト画面URLを取得
                                      final redirectUrl = ref
                                          .read(buildConfigProvider)
                                          .freeeRedirectScreen;
                                      final url =
                                          await controller.getUrlToFreeeSSOPage(
                                        redirectUrl,
                                        FreeeSSOTypeFromHome.firstTime,
                                      );
                                      final state =
                                          ref.watch(homeScreenProvider);
                                      // Todo DTPO-6557にエラー仕様の記載がないため現在仮実装 (DTPO-18249にて対応予定)
                                      if (state.getUrlToFreeeSsoParamsError !=
                                          null) {
                                        // 操作ログ送信
                                        unawaited(
                                          operationLogController
                                              .sendOperationLog(
                                            functionLog: OperationLogMessage
                                                .freeeIntegration,
                                            operationLog: OperationLogMessage
                                                .freeeIntegration,
                                            resultLog:
                                                OperationLogMessage.abnormality,
                                            errorIdLog: state
                                                    .getUrlToFreeeSsoParamsError
                                                    ?.code ??
                                                '',
                                          ),
                                        );
                                      } else {
                                        // freeeサインアップ画面を外部ブラウザで表示
                                        if (context.mounted && url != null) {
                                          // 操作ログ送信
                                          unawaited(
                                            operationLogController
                                                .sendOperationLog(
                                              functionLog: OperationLogMessage
                                                  .freeeIntegration,
                                              operationLog: OperationLogMessage
                                                  .freeeIntegration,
                                              resultLog:
                                                  OperationLogMessage.normal,
                                              errorIdLog: '',
                                            ),
                                          );
                                          final urlLauncher =
                                              ref.watch(urlLauncherProvider);
                                          if (await urlLauncher
                                              .canLaunch(url)) {
                                            if (!context.mounted) return;
                                            // freee連携画面を外部ブラウザで開く
                                            await urlLauncher.launchExternalUrl(
                                              AppLocalizations.of(context)!
                                                  .freeeLinkageFlowFinishButton,
                                              ScreenIdNumber
                                                  .freeeLinkStep2ModalId,
                                              url,
                                              useEncryptedCookie: true,
                                            );
                                          }
                                        }
                                      }
                                    },
                                  ),
                          ),
                  ],
                ),
                Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: GestureDetector(
                      child: SvgPicture.asset(
                        Assets.buttonClose,
                      ),
                      onTap: () {
                        // FirebaseAnalyticsログ送信
                        analyticsLogController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!
                              .freeeLinkageModalClose,
                          screenName: AppLocalizations.of(context)!.home,
                        );
                        Navigator.of(context).pop();
                      },
                    ),
                  ),
                ),
              ],
            )
          : // Todo: ローディングデザインを統一 (DTPO-5809にて対応予定)
          Center(
              // 画面外の戻る処理を無効化しつつローディング処理
              child: PopScope(
                canPop: false,
                child: CircularProgressIndicator(
                  color: AppColors.freshGreen,
                  backgroundColor: AppColors.white,
                ),
              ),
            ),
    );
  }
}

class _FreeeLinkageRepresentativeBanker extends StatelessWidget {
  const _FreeeLinkageRepresentativeBanker({
    required PageController pageController,
    required this.indexNotifier,
  }) : _pageController = pageController;

  final PageController _pageController;
  final ValueNotifier<int> indexNotifier;

  @override
  Widget build(BuildContext context) {
    return PageView(
      controller: _pageController,
      onPageChanged: (index) async {
        indexNotifier.value = index;
      },
      children: [
        // 1枚目
        _FreeeLinkagePage1(),
        // 2枚目
        _FreeeLinkagePage2(),
        // 3枚目
        _FreeeLinkagePage3(),
      ],
    );
  }
}

class _FreeeLinkageEmployee extends HookConsumerWidget {
  const _FreeeLinkageEmployee();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return Align(
      alignment: Alignment.topCenter,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context)!.freeeLinkageFlowEmployeeTitle,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.tradGreen,
                    ),
              ),
              const SizedBox(height: 20),
              Container(
                padding: EdgeInsets.fromLTRB(24, 32, 24, 24),
                decoration: const BoxDecoration(
                  color: AppColors.lightGreen,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                child: Column(
                  children: [
                    Image.asset(Assets.freeeLinkageFlowImageEmployee),
                    const SizedBox(height: 33),
                    Text(
                      AppLocalizations.of(context)!.employeeMessage,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                  border: Border.all(
                    color: AppColors.orange,
                    width: 1,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 32,
                            height: 32,
                            child: SvgPicture.asset(
                              Assets.informationIcon,
                              colorFilter: ColorFilter.mode(
                                AppColors.orange,
                                BlendMode.srcIn,
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            AppLocalizations.of(context)!.notHaveFreeeId,
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      color: AppColors.orange,
                                    ),
                          ),
                        ],
                      ),
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: AppLocalizations.of(context)!
                                  .employeeFreeeIdCreateRequest,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            WidgetSpan(
                              child: GestureDetector(
                                onTap: () {
                                  // FirebaseAnalyticsログ送信
                                  logController.sendButtonLog(
                                    buttonName:
                                        AppLocalizations.of(context)!.freeeFAQ,
                                    screenName:
                                        AppLocalizations.of(context)!.home,
                                  );
                                  ref.watch(urlLauncherProvider).launchLinkId(
                                        LinkIds.freeeLinksFaq,
                                        AppLocalizations.of(context)!.freeeFAQ,
                                        ScreenIdNumber.freeeLinkLoginModalId,
                                      );
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)!.here,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall!
                                          .copyWith(
                                            color: AppColors.primary,
                                            decoration:
                                                TextDecoration.underline,
                                          ),
                                    ),
                                    SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: SvgPicture.asset(
                                        Assets.actionIcon,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FreeeLinkagePage3 extends HookConsumerWidget {
  const _FreeeLinkagePage3();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              child: Text(
                AppLocalizations.of(context)!.freeeLinkageFlowStep1and2Title,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.tradGreen,
                    ),
              ),
            ),
            const SizedBox(height: 24),
            Container(
              padding: EdgeInsets.fromLTRB(24, 32, 24, 24),
              decoration: const BoxDecoration(
                color: AppColors.lightGreen,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 64,
                        height: 27,
                        child: SvgPicture.asset(
                          Assets.step2Icon,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Flexible(
                        child: Text(
                          AppLocalizations.of(context)!
                              .freeeLinkageFlowStep2Title,
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Text(
                    AppLocalizations.of(context)!.freeeLinkageFlowStep2Message,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: AppLocalizations.of(context)!
                          .freeeLinkageFlowStep2Attention1,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 12,
                          ),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context)!.termsOfUse,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 12,
                          ),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context)!
                          .freeeLinkageFlowStep2Attention2,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 12,
                          ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              height: 8,
            ),
            SizedBox(
              width: 343,
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.termsOfUse,
                    screenName: AppLocalizations.of(context)!.home,
                  );
                  ref.watch(urlLauncherProvider).launchLinkId(
                        LinkIds.termsOfUse,
                        AppLocalizations.of(context)!.termsOfUse,
                        ScreenIdNumber.freeeLinkStep2ModalId,
                      );
                },
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.termsOfUse,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 12,
                            color: AppColors.primary,
                          ),
                    ),
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: SvgPicture.asset(
                        Assets.actionIcon,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FreeeLinkagePage2 extends StatelessWidget {
  const _FreeeLinkagePage2();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              child: Text(
                AppLocalizations.of(context)!.freeeLinkageFlowStep1and2Title,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.tradGreen,
                    ),
              ),
            ),
            const SizedBox(height: 24),
            Container(
              padding: EdgeInsets.fromLTRB(24, 32, 24, 24),
              decoration: const BoxDecoration(
                color: AppColors.lightGreen,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 64,
                        height: 27,
                        child: SvgPicture.asset(
                          Assets.step1Icon,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Flexible(
                        child: Text(
                          AppLocalizations.of(context)!
                              .freeeLinkageFlowStep1Title,
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontWeight: FontWeight.w700,
                                  ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                    decoration: const BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 24,
                              height: 24,
                              child: SvgPicture.asset(
                                Assets.beginnerIcon,
                              ),
                            ),
                            SizedBox(width: 4),
                            Text(
                              AppLocalizations.of(context)!
                                  .freeeLinkageFlowStep1BeginnerTitle,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.primary,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context)!
                              .freeeLinkageFlowStep1BeginnerMessage,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                    decoration: const BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 24,
                              height: 24,
                              child: SvgPicture.asset(
                                Assets.humanIcon,
                              ),
                            ),
                            SizedBox(width: 4),
                            Text(
                              AppLocalizations.of(context)!
                                  .freeeLinkageFlowStep1ExistingTitle,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: AppColors.primary,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          AppLocalizations.of(context)!
                              .freeeLinkageFlowStep1ExistingMessage,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FreeeLinkagePage1 extends StatelessWidget {
  const _FreeeLinkagePage1();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              child: Text(
                AppLocalizations.of(context)!.freeeLinkageFlowStep0Title,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.tradGreen,
                    ),
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: EdgeInsets.fromLTRB(24, 32, 24, 24),
              decoration: const BoxDecoration(
                color: AppColors.lightGreen,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Column(
                children: [
                  Image.asset(Assets.freeeLinkageFlowImage),
                  const SizedBox(height: 33),
                  Text(
                    AppLocalizations.of(context)!.freeeLinkageFlowStep0Message,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              child: Row(
                children: [
                  SizedBox(
                    width: 43,
                    height: 18,
                    child: SvgPicture.asset(
                      Assets.step1Icon,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)!.freeeLinkageFlowStep1Title,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              child: Row(
                children: [
                  SizedBox(
                    width: 43,
                    height: 18,
                    child: SvgPicture.asset(
                      Assets.step2Icon,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    AppLocalizations.of(context)!.freeeLinkageFlowStep2Title,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _DotIndicator extends StatelessWidget {
  const _DotIndicator({
    required this.currentIndex,
    required this.index,
  });

  final int currentIndex;
  final int index;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Container(
        width: 10,
        height: 10,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color:
              currentIndex == index ? AppColors.tradGreen : AppColors.grey200,
        ),
      ),
    );
  }
}

class _NextButton extends StatelessWidget {
  const _NextButton({required this.onPressed});

  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 48,
          width: 240,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              elevation: 0,
              backgroundColor: AppColors.tradGreen,
              side: const BorderSide(
                color: Colors.transparent,
              ),
            ),
            onPressed: onPressed,
            child: Center(
              child: Text(
                AppLocalizations.of(context)!.next,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w700,
                    ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 42),
      ],
    );
  }
}

class _FinishButton extends StatelessWidget {
  const _FinishButton({
    required this.onPressed,
    this.isRepresentativeBank = false,
  });

  final Future<void> Function() onPressed;
  final bool isRepresentativeBank;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 48,
          width: 240,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              elevation: 0,
              backgroundColor: AppColors.tradGreen,
              side: const BorderSide(
                color: Colors.transparent,
              ),
            ),
            onPressed: () async {
              // ローディング開始
              LoadingDialog.loading(context);
              await onPressed();
              if (!context.mounted) return;
              // ローディング終了
              LoadingDialog.loadingEnd(context);
              if (!context.mounted) return;
              // モーダルを閉じる
              Navigator.pop(context);
            },
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    isRepresentativeBank
                        ? AppLocalizations.of(context)!.freeeLoginButton
                        : AppLocalizations.of(context)!
                            .freeeLinkageFlowFinishButton,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w700,
                        ),
                  ),
                  const SizedBox(width: 4),
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: SvgPicture.asset(
                      Assets.actionIcon,
                      colorFilter: ColorFilter.mode(
                        AppColors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.freeeLinkageFlowFinishButtonAttention,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 12,
              ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }
}
