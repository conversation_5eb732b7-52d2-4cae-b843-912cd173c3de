import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_account_card_item.freezed.dart';

@freezed
class BankAccountCardItem with _$BankAccountCardItem {
  /// SMBC口座
  const factory BankAccountCardItem.smbc({
    required BankAccount bankAccount,
  }) = BankAccountCardItemSMBC;

  /// freee連携口座
  const factory BankAccountCardItem.freee({
    required BankAccount bankAccount,
  }) = BankAccountCardItemFreee;

  /// SMBC口座エラー
  const factory BankAccountCardItem.smbcError({
    required AppError error,
  }) = BankAccountCardItemSMBCError;

  /// freee連携口座エラー
  const factory BankAccountCardItem.freeeError({
    required AppError error,
  }) = BankAccountCardItemFreeeError;

  /// freee-金融機関の連携期限切れ
  const factory BankAccountCardItem.freeeReConnectFinancialInstitution({
    required BankAccountWithBalanceDetail bankAccount,
  }) = BankAccountCardItemFreeeReConnect;
}
