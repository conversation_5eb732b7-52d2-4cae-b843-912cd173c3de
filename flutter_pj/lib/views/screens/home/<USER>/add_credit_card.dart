import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_update_dialog_content.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 他行連携リンク
class AddCreditCard extends HookConsumerWidget {
  const AddCreditCard({
    super.key,
    required this.onRefreshButtonPressed,
  });

  final void Function()? onRefreshButtonPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final operationLogController = ref.read(operationLogControllerProvider);
    final controller = ref.read(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final appLocalizations = AppLocalizations.of(context)!;
    final freeeRedirectURL = ref.read(buildConfigProvider).freeeRedirectScreen;
    final urlLauncher = ref.read(urlLauncherProvider);

    // 全体更新
    Future<void> refreshAllFreeCreditCardWalletables() async {
      if (onRefreshButtonPressed == null) {
        return;
      }
      // FirebaseAnalyticsログ送信
      analyticsLogController.sendButtonLog(
        buttonName: appLocalizations.reflectsCreditCardSettingsReflected,
        screenName: appLocalizations.home,
      );

      onRefreshButtonPressed?.call();
      await ref
          .read(homeScreenProvider.notifier)
          .refreshAllFreeCreditCardWalletables();
    }

    return Stack(
      children: [
        SizedBox(
          width: 312,
          height: 207,
          child: Image.asset(Assets.addCreditCard),
        ),
        SizedBox(
          width: 312,
          height: 207,
          child: Image.asset(Assets.addCreditCardFrame),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () async {
                // ローディング開始
                LoadingDialog.loading(context);
                // 顧客操作ログ送信
                unawaited(
                  operationLogController.sendOperationLog(
                    functionLog: OperationLogMessage.freeeIntegration,
                    operationLog: OperationLogMessage.freeeAddCard,
                    resultLog: '',
                    errorIdLog: '',
                  ),
                );
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: appLocalizations.addCard,
                  screenName: appLocalizations.home,
                );

                final url = await controller.getUrlToFreeeSSOPage(
                  freeeRedirectURL,
                  FreeeSSOTypeFromHome.creditCard,
                );
                // URL取得時にエラーが発生した場合、後続処理を中断
                if (ref
                        .read(homeScreenProvider)
                        .getUrlToFreeeSsoParamsError
                        .isNotNull ||
                    url == null) return;
                await urlLauncher.launchExternalUrl(
                  appLocalizations.addCard,
                  ScreenIdNumber.homeScreenId,
                  url,
                  useEncryptedCookie: true,
                );
                if (context.mounted) {
                  // ローディング終了
                  LoadingDialog.loadingEnd(context);
                  if (!context.mounted) return;
                  showFreeeUpdateDialog(
                    context,
                    ref,
                    AppLocalizations.of(context)!.closeAdditionCard,
                    AppLocalizations.of(context)!.updateAdditionCard,
                    isCreditCard: true,
                  );
                }
              },
              child: Container(
                width: 312,
                height: 159,
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        Container(
                          width: 46,
                          height: 46,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: AppColors.borderGrayGreen,
                              width: 1,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 28,
                          height: 28,
                          child: SvgPicture.asset(
                            Assets.navigationIcon2,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: 176,
                      height: 24,
                      child: Text(
                        textAlign: TextAlign.center,
                        AppLocalizations.of(context)!.addCard,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              color: AppColors.tradGreen,
                              fontWeight: FontWeight.w700,
                            ),
                      ),
                    ),
                    const SizedBox(width: 35),
                  ],
                ),
              ),
            ),
            GestureDetector(
              // 口座一覧取得API、口座明細一覧APIを実行し、全てのクレジットカード情報を取得する
              onTap: refreshAllFreeCreditCardWalletables,
              child: Container(
                color: AppColors.transparent,
                width: 312,
                height: 48,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 12),
                    SizedBox(
                      width: 269,
                      height: 24,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context)!
                                .linksToOtherBanksExplain3,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      color: AppColors.tradGreen,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
