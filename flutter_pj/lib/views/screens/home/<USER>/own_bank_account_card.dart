import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/string_ext.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/web_view/customized/auth_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_item.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_balance_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_frame.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class OwnBankAccountCard {
  static Widget success({
    required BankAccountCardItemSMBC item,
    required bool isLoading,
    required bool isReturned,
    required double carouselHeight,
    void Function()? onRefreshButtonPressed,
  }) {
    return _SmbcAccountCard(
      item: item,
      isLoading: isLoading,
      isReturned: isReturned,
      carouselHeight: carouselHeight,
      onRefreshButtonPressed: onRefreshButtonPressed,
    );
  }

  static Widget error({
    required AppError error,
    required bool reAuthorizationFlag,
    required double pageViewHeight,
  }) {
    return _SmbcAccountErrorCard(
      error: error,
      reAuthorizationFlag: reAuthorizationFlag,
      pageViewHeight: pageViewHeight,
    );
  }
}

/// SMBC口座の口座カードView
class _SmbcAccountCard extends StatelessWidget {
  const _SmbcAccountCard({
    required this.item,
    required this.isLoading,
    required this.isReturned,
    required this.carouselHeight,
    this.onRefreshButtonPressed,
  });

  final BankAccountCardItemSMBC item;
  final bool isLoading;
  final bool isReturned;
  final double carouselHeight;
  final void Function()? onRefreshButtonPressed;

  @override
  Widget build(BuildContext context) {
    // エラー時には高さを変更
    final isBankErrorCard = item.bankAccount is BankAccountWithError;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: isBankErrorCard ? carouselHeight + 118 : carouselHeight,
          child: _SmbcBankAccountCardFrame(
            child: BankAccountBalanceCard(
              bankAccount: item.bankAccount,
              isReturned: isReturned,
              carouselHeight: carouselHeight,
              displayName: item.bankAccount.displayConfig.displayName,
              onRefreshButtonPressed: onRefreshButtonPressed,
            ),
          ),
        ),
      ],
    );
  }
}

/// SMBC口座カードの外枠部分
class _SmbcBankAccountCardFrame extends StatelessWidget {
  const _SmbcBankAccountCardFrame({
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BankAccountCardFrame(
      backgroundGradientColors: const [
        AppColors.backgroundCard,
        AppColors.cardGradation,
      ],
      label: Padding(
        padding: const EdgeInsets.only(top: 3, right: 16),
        child: Align(
          alignment: Alignment.topRight,
          child: Image.asset(
            Assets.smbcLogo,
            height: 15.07,
            width: 53.31,
          ),
        ),
      ),
      child: child,
    );
  }
}

/// SMBC口座で下記のエラーが発生した際の口座カードView
/// アクセストークン取得エラー、利用者情報取得エラー、認可エラー、口座表示設定取得エラー
class _SmbcAccountErrorCard extends HookConsumerWidget {
  const _SmbcAccountErrorCard({
    required this.error,
    required this.reAuthorizationFlag,
    required this.pageViewHeight,
  });

  final AppError error;
  final bool reAuthorizationFlag;
  final double pageViewHeight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.read(homeScreenProvider);
    final hasInquiryAuth =
        ref.read(loginScreenProvider).authorizationStatus?.hasInquiryAuth ??
            false;
    final appLocalizations = AppLocalizations.of(context)!;
    final settings = BankAccountCardSettings(state);

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: pageViewHeight,
          child: _SmbcBankAccountCardFrame(
            child: SizedBox(
              width: 312,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 18, 16, 8),
                child:
                    // HACK 認可拒否履歴ありの場合のみ、reAuthorizationFlagを用いて条件分岐をさせているものの
                    // 他の再認可ボタンと同様に isWeb21ReauthorizationVisible にて管理する形にリファクタリング
                    reAuthorizationFlag
                        ? _ReAuthorizationMassage(authError: error)
                        : Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                error.code ?? '',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.grayGreen,
                                    ),
                              ),
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.baseline,
                                textBaseline: TextBaseline.alphabetic,
                                children: [
                                  // 照会権限エラーの場合にはアイコンを表示しない
                                  Visibility(
                                    visible: hasInquiryAuth,
                                    child: SvgPicture.asset(
                                      Assets.alertIcon,
                                    ),
                                  ),
                                  Visibility(
                                    visible: hasInquiryAuth,
                                    child: const SizedBox(width: 4),
                                  ),
                                  SizedBox(
                                    // 照会権限が無い際にはアラートアイコン非表示の為、テキストの表示領域を広げる
                                    width: hasInquiryAuth ? 243 : 280,
                                    child: Text(
                                      error.message ?? '',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyLarge!
                                          .copyWith(
                                            // 照会権限エラーの場合には黒字
                                            color: hasInquiryAuth
                                                ? AppColors.red
                                                : AppColors.black,
                                          ),
                                    ),
                                  ),
                                ],
                              ),
                              Visibility(
                                visible: error.hasFaq,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 8),
                                    FaqContent(
                                      linkId: LinkIds.bankAccountFaq,
                                      buttonName:
                                          appLocalizations.bankAccountFAQ,
                                      screenId: ScreenIdNumber.homeScreenId,
                                      screenName: appLocalizations.home,
                                      icon: Assets.actionIcon,
                                    ),
                                  ],
                                ),
                              ),
                              Visibility(
                                visible: settings.isWeb21ReauthorizationVisible(
                                  error.code,
                                ),
                                child: Column(
                                  children: [
                                    SizedBox(height: 16),
                                    ReAuthorizationToWeb21Button(),
                                  ],
                                ),
                              ),
                            ],
                          ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 要再認可時のSMBC口座残高カードの表示項目
class _ReAuthorizationMassage extends StatelessWidget {
  const _ReAuthorizationMassage({required this.authError});

  final AppError authError;

  @override
  Widget build(BuildContext context) {
    String errorMessageWithBold = '';
    String errorMessage = '';

    /// HACK エラーメッセージ変更時にClientにてバグが発生し得るため、正規表現を使用した無理やりなハンドリングは削除する
    try {
      // 一文ずつメッセージスタイルが異なるため分割
      final splitErrorMassage =
          authError.message!.split(RegExp(r'(?<=。)')).toList();
      errorMessageWithBold = splitErrorMassage[0];
      errorMessage = splitErrorMassage[1];
    } catch (_) {
      errorMessageWithBold = ErrorInfo.defaultErrorTitle;
      errorMessage = ErrorInfo.defaultErrorMessage;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 27),
        SizedBox(
          height: 48,
          child: Text(
            errorMessageWithBold,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w700,
                ),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 42,
          child: Text(
            errorMessage,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ),
        const SizedBox(height: 16),
        ReAuthorizationToWeb21Button(),
      ],
    );
  }
}

class ReAuthorizationToWeb21Button extends ConsumerWidget {
  const ReAuthorizationToWeb21Button({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeController = ref.read(homeScreenProvider.notifier);
    final loginController = ref.read(loginScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final hasInquiryAuth =
        ref.read(loginScreenProvider).authorizationStatus?.hasInquiryAuth ??
            false;
    final authUrl = ref.read(buildConfigProvider).authWebViewUrl;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: GestureDetector(
        onTap: () async {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.grantAccess,
            screenName: AppLocalizations.of(context)!.home,
          );
          // 認可画面呼び出し情報生成
          final fromEbizParam = await homeController.createAuthScreenInfo();
          // 生成失敗時には処理を中断
          if (fromEbizParam.isNullOrEmpty || !context.mounted) return;
          await ref.read(authWebViewLauncherProvider).call(
                context,
                WebViewRequest.launchUrl(initialUrl: authUrl),
                fromEbizParam: fromEbizParam!,
                buttonName: AppLocalizations.of(context)!.grantAccess,
                screenIdNumber: ScreenIdNumber.homeScreenId,
              );
          // 再認可時にWeb21権限情報を取得
          await loginController.checkBeforeAuth(true);
          final result = await loginController.getToken(true);
          // ホーム画面初期化 ※トークン取得に失敗した際には引き続き再認可画面を表示
          if (!result) return;
          await homeController.initialize(
            hasInquiryAuth: hasInquiryAuth,
            forceRefresh: true,
          );
        },
        child: Container(
          height: 40,
          width: 240,
          decoration: BoxDecoration(
            color: AppColors.tradGreen,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Align(
            alignment: Alignment.center,
            child: Text(
              AppLocalizations.of(context)!.grantAccess,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.w700,
                  ),
            ),
          ),
        ),
      ),
    );
  }
}
