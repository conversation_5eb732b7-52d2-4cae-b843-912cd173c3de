import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/screens/home/<USER>/loading_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_item.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_frame.dart';
import 'package:dtp_app/views/screens/home/<USER>/error_card_body.dart';
import 'package:dtp_app/views/screens/home/<USER>/other_bank_account_balance_card.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';

class OtherBankAccountCard {
  static Widget success({
    required BankAccountCardItemFreee item,
    required bool isReturned,
    required double carouselHeight,
    required bool isOtherBanksLoading,
    void Function()? onRefreshButtonPressed,
  }) {
    return _FreeeAccountCard(
      item: item,
      isReturned: isReturned,
      carouselHeight: carouselHeight,
      onRefreshButtonPressed: onRefreshButtonPressed,
      isOtherBanksLoading: isOtherBanksLoading,
    );
  }

  static Widget error({
    required AppError error,
    required double carouselHeight,
    void Function()? onRefreshButtonPressed,
    required bool isOtherBanksLoading,
  }) {
    return _FreeeAccountErrorCard(
      isOtherBanksLoading: isOtherBanksLoading,
      error: error,
      carouselHeight: carouselHeight,
      onRefreshButtonPressed: onRefreshButtonPressed,
    );
  }
}

/// freee口座一覧でエラー返却された場合の口座カードView
class _FreeeAccountCard extends StatelessWidget {
  const _FreeeAccountCard({
    required this.item,
    required this.isReturned,
    required this.carouselHeight,
    this.onRefreshButtonPressed,
    required this.isOtherBanksLoading,
  });

  final BankAccountCardItemFreee item;
  final bool isReturned;
  final double carouselHeight;
  final void Function()? onRefreshButtonPressed;
  final bool isOtherBanksLoading;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: carouselHeight,
          child: _FreeeAccountCardFrame(
            child: OtherBankAccountBalanceCard(
              bankAccount: item.bankAccount,
              isReturned: isReturned,
              onRefreshButtonPressed: onRefreshButtonPressed,
              carouselHeight: carouselHeight,
              isOtherBanksLoading: isOtherBanksLoading,
            ),
          ),
        ),
      ],
    );
  }
}

/// freee連携でエラーになった口座の口座カードView
class _FreeeAccountErrorCard extends StatelessWidget {
  const _FreeeAccountErrorCard({
    required this.error,
    this.onRefreshButtonPressed,
    required this.carouselHeight,
    required this.isOtherBanksLoading,
  });

  final AppError error;
  final void Function()? onRefreshButtonPressed;
  final double carouselHeight;
  final bool isOtherBanksLoading;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: carouselHeight,
          child: _FreeeAccountCardFrame(
            child: isOtherBanksLoading
                ? LoadingCard(isReturned: false)
                : SizedBox(
                    width: 312,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 18),
                          Row(
                            children: [
                              GestureDetector(
                                onTap: onRefreshButtonPressed,
                                child: SizedBox(
                                  width: 132,
                                  height: 24,
                                  child: Row(
                                    children: [
                                      // リフレッシュボタン押下時に個別口座情報取得APIを投げて情報更新、10秒間押下不可とする
                                      SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: SvgPicture.asset(
                                          Assets.refreshIcon,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      NullableHyphenText(
                                        data: error.baseDateTime,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall!
                                            .copyWith(
                                              fontFamily:
                                                  FontFamily.robotoCondensed,
                                              color: AppColors.textGrayGreen,
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          ErrorBody(
                            errorCode: error.code ?? '',
                            errorMessage: error.message ?? '',
                            hasFaq: error.hasFaq,
                            errorCategory: ErrorCategory.freeeError,
                          ),
                        ],
                      ),
                    ),
                  ),
          ),
        ),
      ],
    );
  }
}

/// freee連携口座カードの外枠部分
class _FreeeAccountCardFrame extends StatelessWidget {
  const _FreeeAccountCardFrame({required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BankAccountCardFrame(
      backgroundGradientColors: const [
        AppColors.backgroundGreen,
        AppColors.grey200,
      ],
      label: Padding(
        padding: const EdgeInsets.only(
          top: 5,
          right: 15,
        ),
        child: Align(
          alignment: Alignment.topRight,
          child: Text(
            AppLocalizations.of(context)!.linkedAccount,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 12,
                  color: AppColors.textGrayGreen,
                ),
          ),
        ),
      ),
      child: child,
    );
  }
}
