import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/web_view/customized/freee_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ErrorBody extends HookConsumerWidget {
  const ErrorBody({
    super.key,
    required this.errorCode,
    required this.errorMessage,
    required this.hasFaq,
    required this.errorCategory,
    this.maxLines,
    this.textOverflow,
    this.isReturned,
    this.isFreeeCreditCardExpired = false,
  });

  final String errorCode;
  final String errorMessage;
  final bool hasFaq;
  final ErrorCategory errorCategory;
  final int? maxLines;
  final TextOverflow? textOverflow;
  final bool? isReturned;
  final bool isFreeeCreditCardExpired;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);

    final isFreeeExpiration =
        errorCategory == ErrorCategory.freeeError && state.freeeExpirationFlag;

    return isFreeeExpiration
        ? FreeeExpirationErrorCardContents(
            errorCode: errorCode,
            errorMessage: errorMessage,
            hasFaq: hasFaq,
            isReturned: isReturned,
            isFreeeCreditCardExpired: isFreeeCreditCardExpired,
          )
        : ErrorCardContents(
            errorCode: errorCode,
            errorMessage: errorMessage,
            maxLines: maxLines,
            textOverflow: textOverflow,
            hasFaq: hasFaq,
          );
  }
}

class ErrorCardContents extends HookConsumerWidget {
  const ErrorCardContents({
    super.key,
    required this.errorCode,
    required this.errorMessage,
    required this.maxLines,
    required this.textOverflow,
    required this.hasFaq,
  });

  final String errorCode;
  final String errorMessage;
  final int? maxLines;
  final TextOverflow? textOverflow;
  final bool hasFaq;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.red),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              errorCode,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    color: AppColors.grayGreen,
                    fontSize: 12,
                  ),
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    const SizedBox(height: 2.5),
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: SvgPicture.asset(Assets.alertIconHome),
                    ),
                  ],
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    errorMessage,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppColors.red,
                        ),
                    maxLines: maxLines,
                    overflow: textOverflow,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // hasFaqフラグがtrueであれば、FAQリンクを表示する
            Visibility(
              visible: hasFaq,
              child: Column(
                children: [
                  FaqContent(
                    linkId: LinkIds.bankAccountFaq,
                    buttonName: AppLocalizations.of(context)!.bankAccountFAQ,
                    screenId: ScreenIdNumber.homeScreenId,
                    screenName: AppLocalizations.of(context)!.home,
                    icon: Assets.actionIcon,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FreeeExpirationErrorCardContents extends StatelessWidget {
  const FreeeExpirationErrorCardContents({
    super.key,
    required this.errorCode,
    required this.errorMessage,
    required this.hasFaq,
    this.maxLines,
    this.textOverflow,
    required this.isFreeeCreditCardExpired,
    this.isReturned,
  });

  final String errorCode;
  final String errorMessage;
  final bool hasFaq;
  final int? maxLines;
  final TextOverflow? textOverflow;
  final bool isFreeeCreditCardExpired;
  final bool? isReturned;

  @override
  Widget build(BuildContext context) {
    final double bodyWidth = isFreeeCreditCardExpired ? 343 : 312;
    final double bodyHeight;
    if (isFreeeCreditCardExpired) {
      bodyHeight = 198;
    } else {
      bodyHeight = isReturned == true ? 238 : 213;
    }

    return Container(
      width: bodyWidth,
      height: bodyHeight,
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.red),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              errorCode,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    color: AppColors.grayGreen,
                    fontSize: 12,
                  ),
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                SvgPicture.asset(Assets.alertIcon),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                          color: AppColors.red,
                        ),
                    maxLines: maxLines,
                    overflow: textOverflow,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Visibility(
              visible: hasFaq,
              child: Column(
                children: [
                  FaqContent(
                    linkId: LinkIds.bankAccountFaq,
                    buttonName: AppLocalizations.of(context)!.bankAccountFAQ,
                    screenId: ScreenIdNumber.homeScreenId,
                    screenName: AppLocalizations.of(context)!.home,
                    icon: Assets.actionIcon,
                  ),
                ],
              ),
            ),
            // freee連携切れエラーのため、再連携ボタンを表示
            _FreeeReAlignment(
              isFreeeCreditCardExpired: isFreeeCreditCardExpired,
            ),
          ],
        ),
      ),
    );
  }
}

class _FreeeReAlignment extends HookConsumerWidget {
  const _FreeeReAlignment({required this.isFreeeCreditCardExpired});

  final bool isFreeeCreditCardExpired;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(homeScreenProvider.notifier);
    final operationLogController = ref.watch(operationLogControllerProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Column(
      children: [
        const SizedBox(height: 10),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 32),
          child: SizedBox(
            width: double.infinity,
            child: AppRectangleButton(
              label: AppLocalizations.of(context)!.reConnectFreee,
              textColor: AppColors.white,
              buttonColor: AppColors.tradGreen,
              onPressed: () async {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.reConnectFreee,
                  screenName: AppLocalizations.of(context)!.home,
                );

                final url = await controller.getUrlToFreeeReLinkPage();
                final state = ref.watch(homeScreenProvider);
                // エラーが発生した際にはログ送信のみ実施
                if (state.getUrlToFreeeSsoParamsError != null || url.isNull) {
                  // 顧客操作ログ送信
                  unawaited(
                    operationLogController.sendOperationLog(
                      functionLog: OperationLogMessage.freeeIntegration,
                      operationLog: OperationLogMessage.freeeReIntegration,
                      resultLog: OperationLogMessage.abnormality,
                      errorIdLog: state.getUrlToFreeeSsoParamsError?.code ?? '',
                    ),
                  );
                  return;
                }
                // freeeサインアップ画面をWebViewで表示
                // 操作ログ送信
                unawaited(
                  operationLogController.sendOperationLog(
                    functionLog: OperationLogMessage.freeeIntegration,
                    operationLog: OperationLogMessage.freeeReIntegration,
                    resultLog: OperationLogMessage.normal,
                    errorIdLog: '',
                  ),
                );
                if (!context.mounted) return;
                await ref
                    .watch(freeeWebViewLauncherProvider)
                    .call(
                      context,
                      WebViewRequest.launchUrl(initialUrl: url!),
                      AppLocalizations.of(context)!.reConnectFreee,
                      ScreenIdNumber.homeScreenId,
                    )
                    .then((_) {
                  // FreeeTokenStatusが取得済み(freee連携完了)の場合
                  if (ref.watch(homeScreenProvider).freeeTokenStatus ==
                      FreeeTokenStatus.firstLinkageCompleted) {
                    // FreeeTokenStatusを取得済み(freee連携完了)から処理不要に
                    controller.updateFreeeTokenStatusToNoActionRequired();
                    // 他行口座・クレジットカード情報のロードを開始
                    controller.onLoadOtherBanksStarted();
                    // 他行口座・クレジットカード情報をリフレッシュ
                    controller.initializeFreeeInfo();
                  }
                });
              },
              svgPicture: SvgPicture.asset(Assets.actionIconWhite),
            ),
          ),
        ),
      ],
    );
  }
}

enum ErrorCategory {
  // 指定口座残高取得APIのエラー
  smbcBankAccountError,

  // freee口座一覧API、freee口座利用合計額取得APIのエラー
  freeeError,
}
