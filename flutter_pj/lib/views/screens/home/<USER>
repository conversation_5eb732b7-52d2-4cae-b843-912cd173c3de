import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/relate_information.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/screens/bank_account_display_config/bank_account_display_config_screen.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_list.dart';
import 'package:dtp_app/views/screens/home/<USER>/credit_card_area.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_linkage_flow_modal.dart';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/home/<USER>/announcement_info_area.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_balance_card_shimmer.dart';
import 'package:dtp_app/views/screens/home/<USER>/income_and_expenditure_chart_body.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HomeScreen extends HookConsumerWidget with CommonErrorHandlerMixin {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(homeScreenProvider.notifier);
    final state = ref.watch(homeScreenProvider);
    final appLocalizations = AppLocalizations.of(context)!;
    final loginState = ref.watch(loginScreenProvider);
    final logController = ref.read(analyticsLogControllerProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final hasTransactionError = state.transactionDailyError != null;
    final settings = HomeScreenSettings(
      hasInquiryAuth: loginState.authorizationStatus?.hasInquiryAuth ?? false,
      hasBankAccount: state.bankAccounts.onlyHasBalanceDetail().isNotEmpty,
      hasDailyTransactionData: state.transactionDailyTotal.isNotEmpty,
      hasDailyTransactionError: state.transactionDailyError != null,
      isFreeeLinked: state.isFreeeLinked,
      isFreeeExpired: state.freeeExpirationFlag,
      isFreeeLinkError: state.freeeLinkError != null,
    );

    // 画面遷移時のみ実行
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.homeScreenId,
            screenName: appLocalizations.home,
          );
          // ホーム画面初期化
          controller.initialize(
            hasInquiryAuth:
                loginState.authorizationStatus?.hasInquiryAuth ?? false,
          );
        });
        return null;
      },
      const [],
    );

    // freee連携完了確認ダイアログ
    Future<void> showFreeeLinkageCompletionDialog(
      BuildContext context,
      WidgetRef ref,
    ) async {
      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: const BorderSide(color: AppColors.grayGreen),
            ),
            backgroundColor: AppColors.white,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            content: const _FreeeLinkageCompletionDialogContent(),
          );
        },
        barrierColor: Colors.black.withOpacity(0.5), // 背景を薄暗くする
      );
    }

    useEffect(
      () {
        Future<void> freeeLinkage() async {
          switch (state.freeeTokenStatus) {
            case FreeeTokenStatus.error:
              // freee連携エラー時はfreee連携未完了ダイアログを表示する
              // 複数回の描画を避ける為FreeeTokenStatusをエラーから処理不要に
              controller.updateFreeeTokenStatusToNoActionRequired();
              await showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return CommonDialog.ok(
                    title: AppLocalizations.of(context)!
                        .linkageNotCompletedMessage,
                    // 異例ケースで\nを使用 (カード幅固定＆エラーコードで折り返しされないため)
                    message:
                        '${HomeErrorInfo.freeeTokenStatusErrorCode} \n${AppLocalizations.of(context)!.checkFreeeSettingsMessage}',
                    okButtonText: AppLocalizations.of(context)!.close,
                    isPopAfterOkButtonPress: true,
                    onOkPressed: () {
                      // FirebaseAnalyticsログ送信
                      logController.sendButtonLog(
                        buttonName:
                            AppLocalizations.of(context)!.freeeLinkClose,
                        screenName: AppLocalizations.of(context)!.home,
                      );
                    },
                  );
                },
              );
              break;
            case FreeeTokenStatus.firstLinkageCompleted:
              // 複数回の描画を避ける為FreeeTokenStatusを初回連携完了から処理不要に
              controller.updateFreeeTokenStatusToNoActionRequired();
              // freee初回連携ダイアログを表示
              await showFreeeLinkageCompletionDialog(context, ref);
              break;
            case FreeeTokenStatus.noActionRequired:
            default:
              break;
          }
        }

        WidgetsBinding.instance.addPostFrameCallback((_) {
          freeeLinkage();
        });
        return null;
      },
      [state.freeeTokenStatus],
    );

    final isAnnouncementInfo = state.announcementInfo.isNotNull;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // それぞれの格納されたエラーに応じたハンドリング
          if (state.hasErrorList.isNotEmpty) {
            for (final error in state.hasErrorList) {
              handleError(
                context,
                ref,
                error,
                onOkTap: () {},
                screenId: ScreenIdNumber.homeScreenId,
                screenName: AppLocalizations.of(context)!.home,
              );
            }
          }
        });
        return null;
      },
      [state.hasErrorList],
    );

    //　お知らせ情報のデザイン
    final announcementInfoStyle =
        Theme.of(context).textTheme.bodySmall!.copyWith(
              color: AppColors.red,
              fontWeight: FontWeight.w700,
              height: 1.5,
            );

    // 口座残高情報をView用のオブジェクトに変換
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  Assets.bgWaveHome,
                ),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              children: [
                AnnouncementInfoArea(
                  announcementInfo: state.announcementInfo,
                  isAnnouncementInfo: isAnnouncementInfo,
                  style: announcementInfoStyle,
                ),
                ScreenNumber(
                  screenNumber: ScreenIdNumber.homeScreenId,
                  color: AppColors.white,
                ),
                const SizedBox(height: 16),
                _TotalAccountBalance(settings),
                const SizedBox(height: 16),
                BankAccountCardList(),
                settings.isFreeeLinkButtonVisible
                    ? _FreeeLinkageButton()
                    : const SizedBox(height: 48),
                CreditCardArea(settings),
                // クレジットカードのShadow表示の為カルーセルを大きくしている分、5pxSizedBoxを小さくしている
                const SizedBox(height: 43),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: AppColors.basicBackgroundLightGray,
              image: DecorationImage(
                alignment: Alignment.bottomCenter,
                image: AssetImage(Assets.bgWaveGrey),
                fit: BoxFit.fitWidth,
              ),
            ),
            child: Column(
              children: [
                _IncomeAndExpenditureChart(settings, hasTransactionError),
                RelatedInformation(
                  underBannerContents: [
                    SizedBox(height: 80),
                  ],
                  screenId: ScreenIdNumber.homeScreenId,
                  screenName: AppLocalizations.of(context)!.home,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _FreeeLinkageButton extends HookConsumerWidget {
  const _FreeeLinkageButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return Column(
      children: [
        const SizedBox(height: 16),
        FittedBox(
          child: SizedBox(
            height: 40,
            child: OutlinedButton(
              style: OutlinedButton.styleFrom(
                backgroundColor: AppColors.white,
                side: BorderSide(
                  color: AppColors.primary,
                  width: 1,
                ),
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.0)),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(Assets.addFreeeLinkIcon),
                  SizedBox(width: 4),
                  Text(
                    AppLocalizations.of(context)!.otherLinkingWithFreee,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppColors.primary,
                        ),
                  ),
                ],
              ),
              onPressed: () async {
                logController.sendButtonLog(
                  buttonName:
                      AppLocalizations.of(context)!.otherLinkingWithFreee,
                  screenName: AppLocalizations.of(context)!.home,
                );
                await showModalBottomSheet(
                  context: context,
                  isDismissible: false,
                  isScrollControlled: true,
                  enableDrag: false,
                  useSafeArea: true,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(8),
                      topLeft: Radius.circular(8),
                    ),
                  ),
                  builder: (BuildContext context) {
                    return FreeeLinkageFlowModal();
                  },
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.availableForFree,
          style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontSize: 12,
                color: AppColors.white,
              ),
        ),
        const SizedBox(height: 48),
      ],
    );
  }
}

class _TotalAccountBalance extends HookConsumerWidget {
  const _TotalAccountBalance(this.settings);

  final HomeScreenSettings settings;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    //残高合計の桁数チェック。15桁より大きい場合、エラーメッセージを表示する。
    const totalBalanceMaxDigit = 15;
    final isValidDigit =
        state.totalBalance.toString().length <= totalBalanceMaxDigit;
    final String totalBalance;
    final isValidTotalBalance = state.totalBalance != null;

    if (!state.isBalanceVisible) {
      totalBalance = AppLocalizations.of(context)!.fullWidthHyphen;
    } else if (isValidDigit) {
      totalBalance = state.totalBalance == null
          ? AppLocalizations.of(context)!.fullWidthHyphen
          : state.totalBalance!.withCommas;
    } else {
      totalBalance = AppLocalizations.of(context)!.excessBalanceErrorMessage;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SvgPicture.asset(
                Assets.accountBalanceIcon,
                width: 32,
                height: 32,
              ),
              const SizedBox(width: 4),
              Text(
                AppLocalizations.of(context)!.accountBalance,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontFamily: FontFamily.notoSansJP,
                      fontSize: 20,
                      color: AppColors.white,
                    ),
              ),
              Expanded(child: SizedBox()),
              _DisplaySetting(settings),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  Transform.translate(
                    offset: Offset(0, 3),
                    child: Text(
                      AppLocalizations.of(context)!.total,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontFamily: FontFamily.notoSansJP,
                            fontSize: 14,
                            color: AppColors.white,
                          ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  state.isLoading ||
                          state.isLoadingOtherBanks ||
                          state.isAccountBalanceAsyncLoading
                      ? const BankAccountBalanceCardShimmer(
                          width: 170,
                          height: 16,
                        )
                      : _TotalBalance(totalBalance: totalBalance),
                  const SizedBox(width: 8),
                ],
              ),

              // 合計金額が取得できない時は表示しない
              Visibility(
                visible: isValidTotalBalance,
                child: GestureDetector(
                  onTap: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName:
                          AppLocalizations.of(context)!.amountDisplaySwitching,
                      screenName: AppLocalizations.of(context)!.home,
                    );

                    controller.toggleBalanceVisibility();
                  },
                  child: Container(
                    width: 52,
                    height: 28,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: AppColors.borderGrayGreen,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        state.isBalanceVisible
                            ? AppLocalizations.of(context)!.hidden
                            : AppLocalizations.of(context)!.display,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontFamily: FontFamily.notoSansJP,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              color: AppColors.tradGreen,
                            ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _TotalBalance extends HookConsumerWidget {
  const _TotalBalance({
    required this.totalBalance,
  });

  final String totalBalance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final isTotalBalanceNull = state.totalBalance == null;
    double totalBalanceFontSize = 24;

    if (totalBalance ==
        AppLocalizations.of(context)!.excessBalanceErrorMessage) {
      totalBalanceFontSize = 20;
    } else if (totalBalance.length < 13) {
      totalBalanceFontSize = 24;
    } else if (totalBalance.length < 14) {
      totalBalanceFontSize = 23;
    } else if (totalBalance.length < 15) {
      totalBalanceFontSize = 22;
    } else if (totalBalance.length < 16) {
      totalBalanceFontSize = 20;
    } else if (totalBalance.length < 18) {
      totalBalanceFontSize = 18;
    } else if (totalBalance.length < 19) {
      totalBalanceFontSize = 17;
    } else if (totalBalance.length < 20) {
      totalBalanceFontSize = 16;
    }

    if (isTotalBalanceNull) {
      return SizedBox(
        height: 36,
        width: 170,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              AppLocalizations.of(context)!.fullWidthHyphen,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    color: AppColors.white,
                  ),
            ),
            const SizedBox(width: 4),
            Transform.translate(
              offset: Offset(0, 3),
              child: Text(
                AppLocalizations.of(context)!.yenJp,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      fontFamily: FontFamily.notoSansJP,
                      color: AppColors.white,
                    ),
              ),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      width: 170,
      height: 36,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            state.isBalanceVisible && state.totalBalance! < 0
                ? '- $totalBalance'
                : totalBalance,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.white,
                  fontWeight: FontWeight.w700,
                  fontSize: totalBalanceFontSize,
                ),
          ),
          const SizedBox(width: 4),
          Transform.translate(
            offset: Offset(0, 3),
            child: Text(
              AppLocalizations.of(context)!.yenJp,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    fontFamily: FontFamily.notoSansJP,
                    color: AppColors.white,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}

class _DisplaySetting extends HookConsumerWidget {
  const _DisplaySetting(this.settings);

  final HomeScreenSettings settings;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isAccountSettingsValid =
        !state.isNoAccountError && settings.isAccountSettingsValid;

    return InkWell(
      // 利用者情報0件時には口座表示設定を非活性に
      onTap: isAccountSettingsValid
          ? () async {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.displaySettings,
                screenName: AppLocalizations.of(context)!.home,
              );

              // 口座表示設定をモーダル表示する
              final bool result = await showModalBottomSheet(
                    context: context,
                    isDismissible: false,
                    isScrollControlled: true,
                    enableDrag: false,
                    useSafeArea: true,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(8),
                        topLeft: Radius.circular(8),
                      ),
                    ),
                    builder: (BuildContext context) {
                      return BankAccountDisplayConfigScreen();
                    },
                  ) ??
                  false;
              // 保存成功した際には更新処理を実行
              if (result) controller.refreshFromDisplayConfig();
            }
          : null,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          SvgPicture.asset(
            Assets.settingIcon,
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              isAccountSettingsValid ? AppColors.white : AppColors.inactiveText,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            AppLocalizations.of(context)!.displaySettings,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  fontFamily: FontFamily.notoSansJP,
                  color: isAccountSettingsValid
                      ? AppColors.white
                      : AppColors.inactiveText,
                ),
          ),
        ],
      ),
    );
  }
}

class _IncomeAndExpenditureChart extends StatelessWidget {
  const _IncomeAndExpenditureChart(
    this.settings,
    this.hasTransactionDailyError,
  );

  final HomeScreenSettings settings;
  final bool hasTransactionDailyError;

  @override
  Widget build(BuildContext context) {
    if (!settings.isDashboardVisible) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          const SizedBox(height: 48),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SvgPicture.asset(
                Assets.incomeAndExpenditureChart,
              ),
              const SizedBox(width: 4),
              Text(
                AppLocalizations.of(context)!.incomeAndExpenditureIn7days,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.primary,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          hasTransactionDailyError
              ? const IncomeAndExpenditureChartErrorBody()
              : const IncomeAndExpenditureChartBody(),
        ],
      ),
    );
  }
}

///　freee連携完了確認ダイアログのcontent
class _FreeeLinkageCompletionDialogContent extends HookConsumerWidget {
  const _FreeeLinkageCompletionDialogContent();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    final homeController = ref.watch(homeScreenProvider.notifier);

    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 344, maxHeight: 466),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppLocalizations.of(context)!.welcomeBack,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 20,
                  color: AppColors.tradGreen,
                ),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.freeeLinkageCompletionQuestion,
            style: Theme.of(context).textTheme.bodyMedium!,
          ),
          const SizedBox(height: 16),
          Image.asset(Assets.freeeLinkageCompletion),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: AppRectangleButton(
              label: AppLocalizations.of(context)!.finished,
              textColor: AppColors.white,
              buttonColor: AppColors.tradGreen,
              onPressed: () {
                // FirebaseAnalyticsログ送信
                logController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.freeeLinkCompleted,
                  screenName: AppLocalizations.of(context)!.home,
                );
                // 他行口座、クレジットカード情報の更新
                // ローディングが表示される為、更新を待たずにダイアログを閉じる
                homeController.initializeFreeeInfo();
                Navigator.of(context).pop();
              },
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () {
              // FirebaseAnalyticsログ送信
              logController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.freeeLinkNotCompleted,
                screenName: AppLocalizations.of(context)!.home,
              );
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(
              minimumSize: Size.zero,
              padding: EdgeInsets.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              AppLocalizations.of(context)!.notCompleted,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    color: AppColors.tradGreen,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
