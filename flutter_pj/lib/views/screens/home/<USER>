class HomeScreenSettings {
  // メモリリーク防止の為、シングルトンパターンを採用
  static final HomeScreenSettings _instance = HomeScreenSettings._internal();

  /// 取引口座照会権限有無
  late bool _hasInquiryAuth;

  /// 口座有無
  late bool _hasBankAccount;

  /// 日時入出金明細情報有無
  late bool _hasDailyTransactionData;

  /// 入出金日時明細エラー有無
  late bool _hasDailyTransactionError;

  /// freee連携有無
  late bool _isFreeeLinked;

  /// freee連携期限切れ有無
  late bool _isFreeeExpired;

  /// freee連携エラー有無
  late bool _isFreeeLinkError;

  HomeScreenSettings._internal();

  factory HomeScreenSettings({
    required bool hasInquiryAuth,
    required bool hasBankAccount,
    required bool hasDailyTransactionData,
    required bool hasDailyTransactionError,
    required bool isFreeeLinked,
    required bool isFreeeExpired,
    required bool isFreeeLinkError,
  }) {
    _instance._update(
      hasInquiryAuth,
      hasBankAccount,
      hasDailyTransactionData,
      hasDailyTransactionError,
      isFreeeLinked,
      isFreeeExpired,
      isFreeeLinkError,
    );
    return _instance;
  }

  void _update(
    bool hasInquiryAuth,
    bool hasBankAccount,
    bool hasDailyTransactionData,
    bool hasDailyTransactionError,
    bool isFreeeLinked,
    bool isFreeeExpired,
    bool isFreeeLinkError,
  ) {
    _hasInquiryAuth = hasInquiryAuth;
    _hasBankAccount = hasBankAccount;
    _hasDailyTransactionData = hasDailyTransactionData;
    _hasDailyTransactionError = hasDailyTransactionError;
    _isFreeeLinked = isFreeeLinked;
    _isFreeeExpired = isFreeeExpired;
    _isFreeeLinkError = isFreeeLinkError;
  }

  /// 口座表示設定の活性フラグ
  bool get isAccountSettingsValid {
    return _hasInquiryAuth;
  }

  /// freee口座情報の活性フラグ
  bool get isFreeeInfoVisible {
    // 表示条件は口座表示設定と同様だが、今後の拡張性のために別実装
    return _hasInquiryAuth;
  }

  /// 日時入出金明細合計情報の活性フラグ
  bool get isDashboardVisible {
    // 日次入出金明細取得時エラーの際には表示
    if (_hasDailyTransactionError) return true;
    return _hasInquiryAuth && _hasBankAccount && _hasDailyTransactionData;
  }

  /// freee連携ボタン表示フラグ
  bool get isFreeeLinkButtonVisible {
    return !_isFreeeLinked &&
        !_isFreeeLinkError &&
        !_isFreeeExpired &&
        _hasInquiryAuth;
  }

  /// freeeクレジットカード情報表示フラグ
  bool get isFreeeCreditCardVisible {
    return _isFreeeLinked &&
        !_isFreeeLinkError &&
        !_isFreeeExpired &&
        _hasInquiryAuth;
  }

  /// freee連携ボタン(クレジットカード表示部)表示フラグ
  bool get isFreeeErrorCreditVisible {
    return !_isFreeeLinked && !_isFreeeLinkError;
  }

  /// freee連携エラーカード表示フラグ
  bool get isFreeeLinkErrorVisible {
    return _isFreeeLinkError;
  }

  /// freee連携期限エラーを表示するかのフラグ(クレジットカード)
  bool get isFreeeCreditCardExpired {
    return _isFreeeExpired;
  }
}
