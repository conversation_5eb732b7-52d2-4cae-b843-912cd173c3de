import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/bank_account_base_info/bank_account_base_info.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_item.dart';

class BankAccountCardSettings {
  // シングルトンパターン
  static final BankAccountCardSettings _instance =
      BankAccountCardSettings._internal();

  static const _bankNameMaxDigit = 19;

  late bool _isTokenError;
  late bool _isAuthError;
  late bool _isUserAccountError;
  late bool _isAccountSettingsError;
  late bool _isFreeeBankAccountError;
  late bool _isFreeeReConnect;
  late bool _isBankAccountError;
  late bool _isCardRetruned;

  factory BankAccountCardSettings(HomeScreenState state) {
    _instance._update(state);
    return _instance;
  }

  BankAccountCardSettings._internal();

  void _update(HomeScreenState state) {
    _isTokenError = state.tokenError.isNotNull;
    _isAuthError = state.authError.isNotNull;
    _isUserAccountError = state.userAccountError.isNotNull;
    _isAccountSettingsError = state.accountSettingsError.isNotNull;
    _isFreeeBankAccountError = state.freeeBankAccountError.isNotNull;
    _isFreeeReConnect = state.otherBankAccounts.isFreeeReConnect;
    _isBankAccountError = state.bankAccounts.firstError.isNotNull;
    _isCardRetruned = _setIsReturned(state.bankAccounts.onlyVisible());
  }

  /// 口座情報をView表示のために変換する関数
  List<BankAccountCardItem> smbcBankAccountCardItems(HomeScreenState state) {
    final items = <BankAccountCardItem>[];

    if (_isTokenError) {
      // アクセストークンエラーのエラーアイテムを追加
      items.add(
        BankAccountCardItem.smbcError(error: state.tokenError!),
      );
    }

    if (_isAuthError) {
      items.add(
        BankAccountCardItem.smbcError(error: state.authError!),
      );
    }

    if (_isUserAccountError) {
      //　利用者情報取得APIのエラーアイテムを追加
      items.add(
        BankAccountCardItem.smbcError(
          error: state.userAccountError!,
        ),
      );
    }

    // 口座残高詳細情報を変換して格納
    // 表示設定がオンのオブジェクトのみを表示対象とする
    for (final bankAccount in state.bankAccounts.onlyVisible()) {
      items.add(
        BankAccountCardItem.smbc(
          bankAccount: bankAccount,
        ),
      );
    }

    if (_isAccountSettingsError) {
      //SMBC口座情報が配列に入っている場合はクリア
      items.clear();
      items.add(
        BankAccountCardItem.smbcError(error: state.accountSettingsError!),
      );
    }
    // 利用者情報取得APIから値を取得するまで空のカード情報を使用する
    final emptyCard = [
      const BankAccountCardItem.smbc(
        bankAccount: BankAccount(
          baseInfo: BankAccountBaseInfo(),
          displayConfig: DisplayConfig(
            accountId: '',
            displayOrder: 0,
            isHidden: false,
            accountApiType: AccountApiType.web21,
          ),
        ),
      ),
    ];

    // SMBC口座情報が空か否かのフラグ
    bool isCardEmpty = state.bankAccounts.isEmpty;

    // 下記エラーが発生した際にローディングを回避するためisCardEmptyフラグをfalseに
    // アクセストークン取得エラー、利用者情報取得エラー、口座表示設定取得エラー
    if (_isTokenError ||
        _isUserAccountError ||
        _isAuthError ||
        _isAccountSettingsError) {
      isCardEmpty = false;
    }

    // 口座情報取得中もカード券面を表示するため、取得中は空のSMBC口座情報を渡す
    if (isCardEmpty) {
      items.addAll(emptyCard);
    }
    return items;
  }

  /// freee口座情報をView表示のために変換する関数
  List<BankAccountCardItem> freeeBankAccountCardItems(HomeScreenState state) {
    final items = <BankAccountCardItem>[];

    if (_isFreeeBankAccountError) {
      // エラーアイテムを追加
      items.add(
        BankAccountCardItem.freeeError(
          error: state.freeeBankAccountError!,
        ),
      );
    } else {
      // 口座残高詳細情報を変換して格納
      // 表示設定がオンで、詳細情報を持っているオブジェクトのみを表示対象とする
      for (final bankAccount
          in state.otherBankAccounts.onlyVisible().onlyHasBalanceDetail()) {
        if (bankAccount.balanceDetail.isFreeeReConnectFinancialInstitution) {
          // freee連携切れ時、再連携ボタンを表示
          items.add(
            BankAccountCardItem.freeeReConnectFinancialInstitution(
              bankAccount: bankAccount,
            ),
          );
        } else {
          items.add(
            BankAccountCardItem.freee(
              bankAccount: bankAccount,
            ),
          );
        }
      }
    }
    return items;
  }

  /// 銀行支店名2行表示判定
  bool _setIsReturned(Iterable<BankAccount> bankAccounts) {
    for (final item in bankAccounts) {
      if (item.baseInfo.bankName.toString().length +
              item.baseInfo.branchNameKanji.toString().length >
          _bankNameMaxDigit) {
        return true;
      }
    }
    return false;
  }

  bool get isReturned => _isCardRetruned;

  /// 口座情報表示部分の高さ
  double get getCardHeight => _isCardRetruned ? 238 : 213;

  /// 口座情報表示部分の高さ
  double get getPageViewHeight =>
      (_isBankAccountError && !_isAccountSettingsError)
          ? getCardHeight + 118
          : _isFreeeReConnect
              // freeeと金融機関連携切れの際は「確認する」ボタンおよびFAQ表示のため
              // オーバーフローしない最小の値（98pt）を足している
              ? getCardHeight + 98
              : getCardHeight;

  /// Web21再連携ボタン表示フラグ(認可拒否履歴ありの場合除く)
  bool isWeb21ReauthorizationVisible(String? errorCode) =>
      BankAccountErrorInfo.web21ReauthorizationErrorCodes.contains(errorCode);
}
