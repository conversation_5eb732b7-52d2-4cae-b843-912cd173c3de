import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/models/freee_credit_card_expense/freee_credit_card_expense.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';

class CreditCardSettings {
  // シングルトンパターン
  static final CreditCardSettings _instance = CreditCardSettings._internal();

  late bool _isFreeeCreditCardError;
  late bool _isFreeeReConnectFinancialInstitution;
  late bool _isFreeeBankAccountError;

  CreditCardSettings._internal();

  factory CreditCardSettings(HomeScreenState state) {
    _instance._update(state);
    return _instance;
  }

  void _update(HomeScreenState state) {
    _isFreeeCreditCardError = state.freeeCreditCardExpenses
        .any((element) => element.freeeCreditCardError.isNotNull);
    _isFreeeReConnectFinancialInstitution =
        state.freeeCreditCardExpenses.isFreeeReConnect;
    _isFreeeBankAccountError = state.freeeBankAccountError.isNotNull;
  }

  /// クレジットカード情報表示部分の高さ
  double getPageViewHeight() {
    if (_isFreeeReConnectFinancialInstitution) {
      // 金融機関連携期限エラーの場合　※クレジットカードのShadow表示の為高さを5px高くしている
      // 328pxの内訳:エラー本体+FAQリンク=323pxとshadow分5px
      return 328;
    }
    if (_isFreeeCreditCardError) {
      // 情報部分取得エラーの場合　※クレジットカードのShadow表示の為高さを5px高くしている
      // 316pxの内訳:エラー本体+FAQリンク=311pxとshadow分5px
      return 316;
    }
    if (_isFreeeBankAccountError) {
      // 情報全取得エラーの場合　※クレジットカードのShadow表示の為高さを5px高くしている
      // 223pxの内訳:エラー本体+FAQリンク=218pxとshadow分5px
      return 223;
    }
    return 212;
  }
}
