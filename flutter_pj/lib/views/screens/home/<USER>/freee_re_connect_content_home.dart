import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/screens/home/<USER>/loading_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_item.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_frame.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_update_dialog_content.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FreeeReConnectFinancialInstitutionContentHome extends StatelessWidget {
  const FreeeReConnectFinancialInstitutionContentHome({
    super.key,
    required this.item,
    required this.isReturned,
    required this.carouselHeight,
    required this.isOtherBankLoading,
    this.onRefreshButtonPressed,
  });

  final BankAccountCardItemFreeeReConnect item;
  final bool isReturned;
  final double carouselHeight;
  final void Function()? onRefreshButtonPressed;
  final bool isOtherBankLoading;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          // ローディング時には通常券面の高さにて表示
          height: isOtherBankLoading ? 219 : 311,
          child: _FreeeAccountCardFrame(
            child: OtherBankBalanceCardFreeeReConnectFinancialInstitution(
              bankAccount: item.bankAccount,
              isReturned: isReturned,
              onRefreshButtonPressed: onRefreshButtonPressed,
              carouselHeight: carouselHeight,
            ),
          ),
        ),
      ],
    );
  }
}

// freee再連携時のカード表示
class OtherBankBalanceCardFreeeReConnectFinancialInstitution
    extends HookConsumerWidget {
  const OtherBankBalanceCardFreeeReConnectFinancialInstitution({
    super.key,
    required this.bankAccount,
    required this.isReturned,
    required this.onRefreshButtonPressed,
    required this.carouselHeight,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final bool isReturned;
  final void Function()? onRefreshButtonPressed;
  final double carouselHeight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.read(homeScreenProvider);
    final controller = ref.read(homeScreenProvider.notifier);
    final displayAccountName = bankAccount.balanceDetail.displayAccountName;
    final operationLogController = ref.read(operationLogControllerProvider);
    final freeeRedirectURL = ref.read(buildConfigProvider).freeeRedirectScreen;
    final logController = ref.read(analyticsLogControllerProvider);

    if (state.isLoadingOtherBanks) {
      return LoadingCard(isReturned: false);
    }

    return SizedBox(
      width: 312,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 18),
            Row(
              children: [
                GestureDetector(
                  onTap: onRefreshButtonPressed,
                  child: SizedBox(
                    width: 132,
                    height: 24,
                    child: Row(
                      children: [
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: SvgPicture.asset(Assets.refreshIcon),
                        ),
                        const SizedBox(width: 8),
                        NullableHyphenText(
                          data: bankAccount.balanceDetail.lastSyncedAt,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                    color: AppColors.textGrayGreen,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 口座表示名
                _AccountDisplayNameArea(
                  displayAccountName: displayAccountName,
                ),
                const SizedBox(height: 37),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.red),
                    borderRadius: BorderRadius.circular(4),
                    color: AppColors.white,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ErrorInfo.freeeReConnectFinancialInstitutionErrorCode,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                    color: AppColors.grayGreen,
                                    fontSize: 12,
                                  ),
                        ),
                        Container(
                          padding: const EdgeInsets.only(top: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 3),
                                child: SvgPicture.asset(
                                  Assets.alertIconHome,
                                  width: 16,
                                  height: 16,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: SizedBox(
                                  child: Text(
                                    ErrorInfo
                                        .freeeReConnectFinancialInstitutionErrorMessage,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall!
                                        .copyWith(
                                          color: AppColors.red,
                                          fontWeight: FontWeight.w400,
                                          height: 1.5,
                                        ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            const SizedBox(width: 20),
                            Text(
                              AppLocalizations.of(context)!.relatedFAQ,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            GestureDetector(
                              onTap: () {
                                logController.sendButtonLog(
                                  buttonName: AppLocalizations.of(context)!
                                      .bankAccountFAQ,
                                  screenName:
                                      AppLocalizations.of(context)!.home,
                                );

                                ref.read(urlLauncherProvider).launchLinkId(
                                      LinkIds.bankAccountFaq,
                                      AppLocalizations.of(context)!
                                          .bankAccountFAQ,
                                      ScreenIdNumber.homeScreenId,
                                    );
                              },
                              child: Text(
                                AppLocalizations.of(context)!.here,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                      color: AppColors.tradGreen,
                                      decoration: TextDecoration.underline,
                                      decorationColor: AppColors.tradGreen,
                                    ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            SvgPicture.asset(Assets.actionIcon),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            // 「確認する」ボタン
            _ReConnectButton(
              operationLogController: operationLogController,
              controller: controller,
              freeeRedirectURL: freeeRedirectURL,
            ),
          ],
        ),
      ),
    );
  }
}

class _AccountDisplayNameArea extends StatelessWidget {
  const _AccountDisplayNameArea({
    required this.displayAccountName,
  });

  final String? displayAccountName;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 280,
      height: 24,
      child: Text(
        displayAccountName != null
            ? displayAccountName!.ellipsis(maxLength: 17)
            : AppLocalizations.of(context)!.noName,
        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w700,
              fontFamily: FontFamily.notoSansJP,
              color: displayAccountName != null
                  ? AppColors.textBlack
                  : AppColors.inactiveText,
            ),
      ),
    );
  }
}

class _ReConnectButton extends HookConsumerWidget {
  const _ReConnectButton({
    required this.operationLogController,
    required this.controller,
    required this.freeeRedirectURL,
  });

  final OperationLogController operationLogController;
  final HomeScreenNotifier controller;
  final String freeeRedirectURL;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final urlLauncher = ref.read(urlLauncherProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    return Padding(
      padding: const EdgeInsets.only(
        left: 32,
        right: 32,
        top: 16,
      ),
      child: SizedBox(
        width: double.infinity,
        child: AppRectangleButton(
          label: AppLocalizations.of(context)!.confirmFreee,
          textColor: AppColors.white,
          buttonColor: AppColors.tradGreen,
          onPressed: () async {
            // 操作ログ送信
            unawaited(
              operationLogController.sendOperationLog(
                functionLog: OperationLogMessage.freeeIntegration,
                operationLog: OperationLogMessage.freeeReIntegration,
                resultLog: '',
                errorIdLog: '',
              ),
            );
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.confirmFreeeOtherBank,
              screenName: AppLocalizations.of(context)!.home,
            );
            final url = await controller.getUrlToFreeeSSOPage(
              freeeRedirectURL,
              FreeeSSOTypeFromHome.expirationOfFinanceLinkage,
            );
            // URL取得時にエラーが発生した場合、後続処理を中断
            if (ref
                    .read(homeScreenProvider)
                    .getUrlToFreeeSsoParamsError
                    .isNotNull ||
                url == null) return;
            if (!context.mounted) return;
            await urlLauncher.launchExternalUrl(
              AppLocalizations.of(context)!.confirmFreeeOtherBank,
              ScreenIdNumber.homeScreenId,
              url,
              useEncryptedCookie: true,
            );
            if (context.mounted) {
              showFreeeUpdateDialog(
                context,
                ref,
                AppLocalizations.of(context)!.closeAdditionAccount,
                AppLocalizations.of(context)!.updateAdditionAccount,
                isCreditCard: false,
              );
            }
          },
          svgPicture: SvgPicture.asset(
            Assets.actionIconWhite,
          ),
        ),
      ),
    );
  }
}

/// freee連携口座カードの外枠部分
class _FreeeAccountCardFrame extends StatelessWidget {
  const _FreeeAccountCardFrame({
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BankAccountCardFrame(
      backgroundGradientColors: const [
        AppColors.backgroundGreen,
        AppColors.grey200,
      ],
      label: Padding(
        padding: const EdgeInsets.only(
          top: 5,
          right: 15,
        ),
        child: Align(
          alignment: Alignment.topRight,
          child: Text(
            AppLocalizations.of(context)!.linkedAccount,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 12,
                  color: AppColors.textGrayGreen,
                ),
          ),
        ),
      ),
      child: child,
    );
  }
}
