import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_balance_card_shimmer.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// ローディング時のカード表示
class LoadingCard extends StatelessWidget {
  const LoadingCard({super.key, required this.isReturned});

  final bool isReturned;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 312,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 132,
              height: 24,
              child: Row(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: SvgPicture.asset(Assets.refreshIcon),
                  ),
                  const SizedBox(width: 8),
                  const BankAccountBalanceCardShimmer(
                    width: 100,
                    height: 21,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const BankAccountBalanceCardShimmer(
              width: 280,
              height: 24,
            ),
            const SizedBox(height: 8),
            const BankAccountBalanceCardShimmer(
              width: 280,
              height: 21,
            ),
            const SizedBox(height: 4),
            isReturned
                ? const Column(
                    children: [
                      BankAccountBalanceCardShimmer(
                        width: 280,
                        height: 21,
                      ),
                      SizedBox(height: 4),
                    ],
                  )
                : const SizedBox.shrink(),
            const BankAccountBalanceCardShimmer(
              width: 85,
              height: 21,
            ),
            const SizedBox(height: 4),
            const BankAccountBalanceCardShimmer(
              width: 280,
              height: 21,
            ),
            const SizedBox(height: 6),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const BankAccountBalanceCardShimmer(
                  width: 219,
                  height: 42,
                ),
                const SizedBox(width: 16),
                Container(
                  width: 1,
                  height: 24,
                  color: AppColors.borderGrayGreen,
                ),
                const SizedBox(width: 16),
                SizedBox(
                  height: 21,
                  child: Text(
                    AppLocalizations.of(context)!.statement,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontFamily: FontFamily.notoSansJP,
                          color: AppColors.tradGreen,
                        ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
