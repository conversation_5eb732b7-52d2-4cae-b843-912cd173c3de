import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/home/<USER>/loading_card.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 口座残高カード(他行)
class OtherBankAccountBalanceCard extends StatelessWidget {
  const OtherBankAccountBalanceCard({
    super.key,
    required this.bankAccount,
    required this.isReturned,
    required this.onRefreshButtonPressed,
    required this.carouselHeight,
    required this.isOtherBanksLoading,
  });

  final BankAccount bankAccount;
  final bool isReturned;
  final void Function()? onRefreshButtonPressed;
  final double carouselHeight;
  final bool isOtherBanksLoading;

  @override
  Widget build(BuildContext context) {
    if (isOtherBanksLoading) {
      return LoadingCard(isReturned: isReturned);
    } else {
      return _OtherBankAccountBalanceCard(
        bankAccount: bankAccount as BankAccountWithBalanceDetail,
        isReturned: isReturned,
        onRefreshButtonPressed: onRefreshButtonPressed,
        carouselHeight: carouselHeight,
      );
    }
  }
}

/// 通常時のカード表示
class _OtherBankAccountBalanceCard extends HookConsumerWidget {
  const _OtherBankAccountBalanceCard({
    required this.bankAccount,
    required this.isReturned,
    required this.onRefreshButtonPressed,
    required this.carouselHeight,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final bool isReturned;
  final void Function()? onRefreshButtonPressed;
  final double carouselHeight;

  int? get _currentBalance => bankAccount.balanceDetail.currentBalance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isCurrentBalanceNull = _currentBalance == null;
    final displayAccountName = bankAccount.balanceDetail.displayAccountName;
    final serverDate = state.otherBankBaseDateTime ?? state.serverDate;

    double fontSize = 28.0;
    if (_currentBalance.toString().length == 13) {
      fontSize = 26.0;
    } else if (_currentBalance.toString().length == 14) {
      fontSize = 24.0;
    } else if (_currentBalance.toString().length == 15) {
      fontSize = 23.0;
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
          width: 312,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 18),
                SizedBox(
                  width: 132,
                  height: 24,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: SvgPicture.asset(Assets.refreshIcon),
                      ),
                      const SizedBox(width: 8),
                      NullableHyphenText(
                        data: bankAccount.balanceDetail.lastSyncedAt,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                              color: AppColors.textGrayGreen,
                            ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 280,
                      height: 24,
                      child: Text(
                        displayAccountName != null
                            ? displayAccountName.ellipsis(maxLength: 17)
                            : AppLocalizations.of(context)!.noName,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                              fontFamily: FontFamily.notoSansJP,
                              color: displayAccountName != null
                                  ? AppColors.textBlack
                                  : AppColors.inactiveText,
                            ),
                      ),
                    ),
                    isReturned
                        ? const SizedBox(height: 110)
                        : const SizedBox(height: 85),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        isCurrentBalanceNull
                            ? Text(
                                AppLocalizations.of(context)!.fullWidthHyphen,
                                style: const TextStyle(
                                  color: AppColors.textBlack,
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                ),
                              )
                            : SizedBox(
                                width: 219,
                                height: 42,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  textBaseline: TextBaseline.alphabetic,
                                  children: [
                                    state.isBalanceVisible &&
                                            _currentBalance != null
                                        ? Text(
                                            (state.isBalanceVisible &&
                                                    _currentBalance! < 0)
                                                ? '- ${_currentBalance!.withCommas}'
                                                : _currentBalance!.withCommas,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleLarge!
                                                .copyWith(
                                                  fontFamily: FontFamily
                                                      .robotoCondensed,
                                                  fontSize: fontSize,
                                                  color: AppColors.textBlack,
                                                ),
                                          )
                                        : Text(
                                            AppLocalizations.of(context)!
                                                .fullWidthHyphen,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleLarge!
                                                .copyWith(
                                                  fontFamily: FontFamily
                                                      .robotoCondensed,
                                                  fontSize: 28,
                                                  color: AppColors.textBlack,
                                                ),
                                          ),
                                    const SizedBox(width: 4),
                                    Text(
                                      AppLocalizations.of(context)!.yenJp,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall!
                                          .copyWith(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                            fontFamily: FontFamily.notoSansJP,
                                            color: AppColors.textBlack,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                        const SizedBox(width: 16),
                        Container(
                          width: 1,
                          height: 24,
                          color: AppColors.borderGrayGreen,
                        ),
                        const SizedBox(width: 16),
                        SizedBox(
                          height: 21,
                          child: Text(
                            AppLocalizations.of(context)!.statement,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.notoSansJP,
                                      color: AppColors.tradGreen,
                                    ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ],
            ),
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: onRefreshButtonPressed,
                  child: Container(
                    width: 132,
                    height: 54,
                    color: AppColors.transparent,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!
                          .incomeAndExpenditureOfOtherBank,
                      screenName: AppLocalizations.of(context)!.home,
                    );
                    // 入出金明細画面に遷移
                    Navigator.of(context).pushNamed(
                      RouteNames.incomeAndExpenditure,
                      arguments: {
                        'bankAccount': bankAccount,
                        'serverDate': serverDate,
                      },
                    );
                  },
                  child: Container(
                    color: AppColors.transparent,
                    width: 180,
                    height: 54,
                  ),
                ),
              ],
            ),
            GestureDetector(
              onTap: () {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!
                      .incomeAndExpenditureOfOtherBank,
                  screenName: AppLocalizations.of(context)!.home,
                );
                // 入出金明細画面に遷移
                Navigator.of(context).pushNamed(
                  RouteNames.incomeAndExpenditure,
                  arguments: {
                    'bankAccount': bankAccount,
                    'serverDate': serverDate,
                  },
                );
              },
              child: Container(
                color: AppColors.transparent,
                width: 312,
                height: carouselHeight - 54,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
