import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/home/<USER>/dynamic_padding_for_home.dart';
import 'package:dtp_app/views/screens/home/<USER>/other_bank_account_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/own_bank_account_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_card_item.dart';
import 'package:dtp_app/views/screens/home/<USER>/add_other_banks.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_re_connect_content_home.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BankAccountCardList extends HookConsumerWidget {
  /// リフレッシュボタン無効化秒数
  static const _refreshButtonDisabledSeconds = 10;

  const BankAccountCardList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final currentIndex = useState(0);
    final refreshButtonEnabled = useState(true);
    final appLocalizations = AppLocalizations.of(context)!;

    // カード枚数最大20枚
    const maxCardLength = 20;
    final isFreeeLinked = state.isFreeeLinked;
    final isFreeeExpired = state.freeeExpirationFlag;

    final pageSettings = BankAccountCardSettings(state);

    // リフレッシュボタンを一定時間無効にする
    void refreshButtonCoolDown() {
      refreshButtonEnabled.value = false;
      Future<void>.delayed(
        const Duration(seconds: _refreshButtonDisabledSeconds),
        () {
          refreshButtonEnabled.value = true;
        },
      );
    }

    // 口座残高情報をView用のオブジェクトに変換
    final items = <BankAccountCardItem>[];

    items.addAll(pageSettings.smbcBankAccountCardItems(state));

    // freeeが連携期限切れエラーの際には口座表示部にfreee情報を表示しない
    if (!state.freeeExpirationFlag) {
      items.addAll(pageSettings.freeeBankAccountCardItems(state));
    }

    final deviceWidth = MediaQuery.of(context).size.width;

    // 表示する残高カードの枚数を計算
    int itemCount;
    // 取得した口座情報数が最大表示可能枚数より大きい場合、最大表示可能枚数を代入
    items.length > maxCardLength
        ? itemCount = maxCardLength
        : itemCount = items.length;

    // freee連携有かつ連携切れでない場合、口座残高カードとは別で他行連携リンクを最右に表示するため１を足す
    if (isFreeeLinked && !isFreeeExpired) itemCount = itemCount + 1;

    // 最終券面かのフラグ
    final isLastItem = currentIndex.value + 1 == itemCount;
    // カードが一枚であるかのフラグ
    final isOnlyOneItem = itemCount == 1;

    final viewPortFraction = isOnlyOneItem && !state.isLoading
        ? 312 / deviceWidth
        : 328 / deviceWidth;

    // HACKME BankAccount->BankAccountCardItem の変換実施することで、可読性の低下を招いている
    // その為、仕組みの抜本的な見直しが必要
    return SizedBox(
      height: pageSettings.getPageViewHeight,
      child: PageView.builder(
        padEnds: isOnlyOneItem && !state.isLoading,
        onPageChanged: (index) {
          currentIndex.value = index;
        },
        controller: PageController(viewportFraction: viewPortFraction),
        itemCount: itemCount,
        //口座残高カード最後尾の場合は口座情報不要のため空のBankAccountBalanceDetailを渡す
        itemBuilder: (BuildContext context, int itemIndex) {
          if (itemIndex < items.length) {
            return items[itemIndex].map(
              smbc: (item) {
                // SMBC口座残高カードを表示
                return DynamicPadding(
                  isLoading: state.isLoading,
                  isLastItem: isLastItem,
                  isOnlyOneItem: isOnlyOneItem,
                  child: OwnBankAccountCard.success(
                    item: item,
                    isLoading: state.isLoading,
                    isReturned: pageSettings.isReturned,
                    carouselHeight: pageSettings.getCardHeight,
                    onRefreshButtonPressed: refreshButtonEnabled.value
                        ? () {
                            // FirebaseAnalyticsログ送信
                            analyticsLogController.sendButtonLog(
                              buttonName: appLocalizations.balanceUpdate,
                              screenName: appLocalizations.home,
                            );

                            refreshButtonCoolDown();
                            ref
                                .read(homeScreenProvider.notifier)
                                .refresh(item.bankAccount.accountId);
                          }
                        : null,
                  ),
                );
              },
              freee: (item) {
                // 口座残高カード（他行）を表示
                return DynamicPadding(
                  isLoading: state.isLoading,
                  isLastItem: isLastItem,
                  isOnlyOneItem: isOnlyOneItem,
                  child: OtherBankAccountCard.success(
                    item: item,
                    isReturned: pageSettings.isReturned,
                    carouselHeight: pageSettings.getCardHeight,
                    onRefreshButtonPressed: refreshButtonEnabled.value
                        ? () {
                            // FirebaseAnalyticsログ送信
                            analyticsLogController.sendButtonLog(
                              buttonName: appLocalizations
                                  .updateAccountInformationOfOtherBank,
                              screenName: appLocalizations.home,
                            );
                            refreshButtonCoolDown();
                            controller.getFreeeAccountBalance(
                              InquiryCategory.bankAccount,
                            );
                          }
                        : null,
                    isOtherBanksLoading: state.isLoadingOtherBanks,
                  ),
                );
              },
              smbcError: (item) {
                // SMBC口座のエラー情報を表示
                return DynamicPadding(
                  isLoading: state.isLoading,
                  isLastItem: isLastItem,
                  isOnlyOneItem: isOnlyOneItem,
                  child: OwnBankAccountCard.error(
                    pageViewHeight: pageSettings.getCardHeight,
                    error: item.error,
                    reAuthorizationFlag: state.reAuthorizationFlag,
                  ),
                );
              },
              freeeError: (item) => DynamicPadding(
                isLoading: state.isLoading,
                isLastItem: isLastItem,
                isOnlyOneItem: isOnlyOneItem,
                child: OtherBankAccountCard.error(
                  error: item.error,
                  carouselHeight: pageSettings.getCardHeight,
                  isOtherBanksLoading: state.isLoadingOtherBanks,
                  onRefreshButtonPressed: refreshButtonEnabled.value
                      ? () {
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName: appLocalizations
                                .updateAccountInformationOfOtherBank,
                            screenName: appLocalizations.home,
                          );
                          refreshButtonCoolDown();
                          controller.getFreeeAccountBalance(
                            InquiryCategory.bankAccount,
                          );
                        }
                      : null,
                ),
              ),
              // freee-金融機関の連携期限切れの場合に再連携ボタンを表示
              freeeReConnectFinancialInstitution: (item) => DynamicPadding(
                isLoading: state.isLoading,
                isLastItem: isLastItem,
                isOnlyOneItem: isOnlyOneItem,
                child: FreeeReConnectFinancialInstitutionContentHome(
                  item: item,
                  isReturned: pageSettings.isReturned,
                  isOtherBankLoading: state.isLoadingOtherBanks,
                  carouselHeight: pageSettings.getCardHeight,
                  onRefreshButtonPressed: refreshButtonEnabled.value
                      ? () {
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName: appLocalizations
                                .updateAccountInformationOfOtherBank,
                            screenName: appLocalizations.home,
                          );
                          refreshButtonCoolDown();
                          controller.getFreeeAccountBalance(
                            InquiryCategory.bankAccount,
                          );
                        }
                      : null,
                ),
              ),
            );
          } else {
            //最後尾は他行連携リンクを表示（freee連携有の場合のみ）
            return DynamicPadding(
              isLoading: state.isLoading,
              isLastItem: isLastItem,
              isOnlyOneItem: isOnlyOneItem,
              child: const AddOtherBankAccountCard(),
            );
          }
        },
      ),
    );
  }
}
