import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/screens/home/<USER>/income_and_expenditure_chart_view.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class IncomeAndExpenditureChartBody extends HookConsumerWidget {
  const IncomeAndExpenditureChartBody({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);

    if (state.bankAccounts.isEmpty || state.transactionDailyTotal.isEmpty) {
      return const SizedBox.shrink();
    }

    final bankAccount = state.bankAccounts
        .sortedByDisplayOrder()
        .firstWhere((account) => !account.displayConfig.isHidden);

    // 1行に格納できる「銀行名」+「支店名」の最大文字数。
    const bankNameMaxDigit = 19;

    //「銀行名」と「支店名」を改行して表示するかをチェック。20文字以上の際は改行する。
    // nullの際は全角ハイフンで表示するため、1文字としてカウント。
    final isReturned = (bankAccount.baseInfo.bankName?.length ?? 1) +
            (bankAccount.baseInfo.branchNameKanji?.length ?? 1) >
        bankNameMaxDigit;

    final baseTextStyle = Theme.of(context).textTheme.bodySmall!.copyWith(
          height: 1.5,
          color: AppColors.grayGreen,
        );

    return Column(
      children: [
        Container(
          decoration: ShapeDecoration(
            color: AppColors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            shadows: const [
              BoxShadow(
                color: AppColors.shadowGray,
                blurRadius: 4,
                offset: Offset(0, 0),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              children: [
                const SizedBox(height: 16),
                _UpdateInfo(bankAccount, baseTextStyle),
                const SizedBox(height: 12),
                _AccountInfo(bankAccount, isReturned, baseTextStyle),
                const SizedBox(height: 24),
                IncomeAndExpenditureChartView(
                  data: state.transactionDailyTotal
                      .map(
                        (e) => IncomeAndExpenditureChartData(
                          dateTime: e.date,
                          incomeValue: e.totalDeposit,
                          expenditureValue: e.totalWithdrawal,
                        ),
                      )
                      .toList(),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class IncomeAndExpenditureChartErrorBody extends HookConsumerWidget {
  const IncomeAndExpenditureChartErrorBody({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final error = state.transactionDailyError!;
    final errorCode = error.code ?? '';
    final errorMessage = error.message ?? '';

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.red),
        borderRadius: BorderRadius.circular(4),
        color: AppColors.white,
      ),
      child: SizedBox(
        width: 343,
        height: 181,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                errorCode,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.grayGreen,
                    ),
              ),
              const SizedBox(height: 17),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  SvgPicture.asset(Assets.alertIcon),
                  const SizedBox(width: 4),
                  SizedBox(
                    width: 275,
                    child: Text(
                      errorMessage,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            color: AppColors.red,
                          ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _UpdateInfo extends StatelessWidget {
  const _UpdateInfo(this.account, this.baseTextStyle);

  final BankAccount account;
  final TextStyle baseTextStyle;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 56,
          child: Text(
            AppLocalizations.of(context)!.lastUpdated,
            style: baseTextStyle,
          ),
        ),
        const SizedBox(width: 8),
        Column(
          children: [
            Align(
              alignment: Alignment(-2, 0),
              child: Row(
                children: [
                  NullableHyphenText(
                    data: account.baseDate,
                    style: baseTextStyle.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                    ),
                  ),
                  Text(
                    ' ',
                    style: baseTextStyle.copyWith(
                      color: AppColors.textBlack,
                    ),
                  ),
                  NullableHyphenText(
                    data: account.baseTime,
                    style: baseTextStyle.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _AccountInfo extends StatelessWidget {
  const _AccountInfo(this.account, this.isReturned, this.baseTextStyle);

  final BankAccount account;
  final bool isReturned;
  final TextStyle baseTextStyle;

  @override
  Widget build(BuildContext context) {
    final displayName = account.displayConfig.displayName;
    return Container(
      decoration: ShapeDecoration(
        color: AppColors.grey100,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              overflow: TextOverflow.ellipsis,
              displayName.isNotEmpty
                  ? displayName
                  : AppLocalizations.of(context)!.noName,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontSize: 14,
                    height: 1.5,
                    color:
                        displayName.isNotEmpty ? null : AppColors.inactiveText,
                  ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              child: Row(
                children: [
                  Flexible(
                    child: NullableHyphenText(
                      data: account.baseInfo.bankName,
                      style: baseTextStyle.copyWith(
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 銀行名、支店名が合計20文字以下であれば横並びで表示
                  Visibility(
                    visible: !isReturned,
                    child: Flexible(
                      child: NullableHyphenText(
                        data: account.baseInfo.branchNameKanji,
                        style: baseTextStyle.copyWith(
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 4),
            Visibility(
              visible: isReturned,
              child: SizedBox(
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    NullableHyphenText(
                      data: account.baseInfo.branchNameKanji,
                      style: baseTextStyle.copyWith(
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                ),
              ),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                NullableHyphenText(
                  data: account.baseInfo.accountType,
                  style: baseTextStyle.copyWith(
                    fontSize: 12,
                  ),
                ),
                const SizedBox(width: 8),
                NullableHyphenText(
                  data: account.baseInfo.accountNumber,
                  style: baseTextStyle.copyWith(
                    fontSize: 12,
                    fontFamily: FontFamily.robotoCondensed,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 5),
            Text(
              // 振込依頼人名 nullの場合は空文字で表示（webと表示を一致）
              account.baseInfo.remitterName ?? '',
              style: baseTextStyle.copyWith(
                fontSize: 12,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
