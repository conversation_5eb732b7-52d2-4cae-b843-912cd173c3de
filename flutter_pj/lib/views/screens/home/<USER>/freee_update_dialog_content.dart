import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// freee 画面更新ダイアログ
void showFreeeUpdateDialog(
  BuildContext context,
  WidgetRef ref,
  String closeBtnLogName,
  String updateBtnLogName, {
  required bool isCreditCard,
  bool isHome = true, // 取引口座照会画面から呼び出す場合falseを入れてコンストラクタ呼び出す。
}) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: const BorderSide(color: AppColors.grayGreen),
        ),
        backgroundColor: AppColors.white,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
        content: _FreeeUpdateDialogContent(
          closeBtnLogName: closeBtnLogName,
          updateBtnLogName: updateBtnLogName,
          isHome: isHome,
          isCreditCard: isCreditCard,
        ),
      );
    },
    barrierColor: Colors.black.withOpacity(0.5), // 背景を薄暗くする
  );
}

///　freee連携完了確認ダイアログのcontent
class _FreeeUpdateDialogContent extends HookConsumerWidget {
  const _FreeeUpdateDialogContent({
    required this.closeBtnLogName,
    required this.updateBtnLogName,
    required this.isHome,
    required this.isCreditCard,
  });

  final String closeBtnLogName;
  final String updateBtnLogName;
  final bool isHome;
  final bool isCreditCard;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    final homeController = ref.watch(homeScreenProvider.notifier);

    return ConstrainedBox(
      constraints: const BoxConstraints(
        maxWidth: double.infinity,
        maxHeight: 280,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 8, right: 8),
              child: IconButton(
                onPressed: () {
                  logController.sendButtonLog(
                    buttonName: closeBtnLogName,
                    screenName: AppLocalizations.of(context)!.home,
                  );

                  Navigator.of(context).pop();
                },
                icon: SvgPicture.asset(
                  Assets.buttonClose,
                  width: 32,
                  height: 32,
                ),
              ),
            ),
          ),
          Text(
            AppLocalizations.of(context)!.updateScreen,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 20,
                  color: AppColors.tradGreen,
                ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              AppLocalizations.of(context)!.updateScreenDescription,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium!,
            ),
          ),
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: AppRectangleButton(
              label: AppLocalizations.of(context)!.update,
              textColor: AppColors.white,
              buttonColor: AppColors.tradGreen,
              onPressed: () {
                logController.sendButtonLog(
                  buttonName: updateBtnLogName,
                  screenName: isHome
                      ? AppLocalizations.of(context)!.home
                      : AppLocalizations.of(context)!.transactionAccountInquiry,
                );
                final inquiryCategory = isCreditCard
                    ? InquiryCategory.creditCard
                    : InquiryCategory.bankAccount;
                // 他行口座、クレジットカード情報の更新
                homeController.getFreeeAccountBalance(inquiryCategory);
                Navigator.of(context).pop();
              },
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
