import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/screens/home/<USER>/dynamic_padding_for_home.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_linkage_flow_modal.dart';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/home/<USER>/add_credit_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_balance_card_shimmer.dart';
import 'package:dtp_app/views/screens/home/<USER>/credit_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/error_card_body.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CreditCardArea extends StatelessWidget {
  const CreditCardArea(this.settings, {super.key});

  final HomeScreenSettings settings;

  @override
  Widget build(BuildContext context) {
    if (!settings.isFreeeInfoVisible) return SizedBox.shrink();
    // freee連携あり、かつ、freee連携確認APIにて正常応答、かつ、有効期限切れの場合
    if (settings.isFreeeCreditCardExpired) {
      return _CreditCardList._freeeCreditCardExpired();
    }
    // freee連携あり、かつ、freee連携確認APIにて正常応答、かつ、有効期限切れではない場合
    if (settings.isFreeeCreditCardVisible) return _CreditCardList.normal();
    // freee連携未済、かつ、freee連携確認APIにて正常応答、かつ、有効期限切れではない場合
    if (settings.isFreeeErrorCreditVisible) return _CreditCardList.noCard();
    // freee連携確認APIにて異常応答の場合
    if (settings.isFreeeLinkErrorVisible) return _CreditCardList.error();
    return SizedBox.shrink();
  }
}

class _CreditCardList {
  /// freee連携あり、かつ、freee連携確認APIにて正常応答、かつ、有効期限切れではない場合
  static Widget normal() {
    return const _CreditCard();
  }

  /// freee連携あり、かつ、freee連携確認APIにて正常応答、かつ、有効期限切れの場合
  static Widget _freeeCreditCardExpired() {
    return const _FreeeExpirationCreditCard();
  }

  /// freee連携未済、かつ、freee連携確認APIにて正常応答、かつ、有効期限切れではない場合
  static Widget noCard() {
    return const _NoCreditCard();
  }

  /// freee連携確認APIにて異常応答の場合
  static Widget error() {
    return const _ErrorLinkageSetting();
  }
}

class _CreditCard extends StatefulHookConsumerWidget {
  const _CreditCard();

  @override
  _CreditCardState createState() => _CreditCardState();
}

class _CreditCardState extends ConsumerState<_CreditCard> {
  /// リフレッシュボタン無効化秒数
  static const _refreshButtonDisabledSeconds = 10;

  bool _refreshButtonEnabled = true;

  int currentIndex = 0;

  // リフレッシュボタンを一定時間無効にする
  void _refreshButtonCoolDown() {
    setState(() {
      _refreshButtonEnabled = false;
    });
    Future<void>.delayed(
      const Duration(seconds: _refreshButtonDisabledSeconds),
      () {
        if (!mounted) {
          return;
        }
        setState(() {
          _refreshButtonEnabled = true;
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(homeScreenProvider);
    final cardSettings = CreditCardSettings(state);

    // カード枚数最大100枚
    const maxCardLength = 100;

    final deviceWidth = MediaQuery.of(context).size.width;

    final itemCount = state.freeeCreditCardExpenses.length > maxCardLength
        // 他行連携リンクを最後に表示するため +1
        ? maxCardLength + 1
        : state.freeeCreditCardExpenses.length + 1;

    // 表示しているアイテムが最後かを判断するフラグ
    final isLastItem = currentIndex + 1 == itemCount;

    // アイテムが一つだけでないかを判断するフラグ
    final isOnlyOneItem = itemCount == 1;

    // 券面が一枚の場合のみはパディングなし中央配置
    final viewPortFraction =
        isOnlyOneItem ? 312 / deviceWidth : 328 / deviceWidth;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SvgPicture.asset(Assets.creditCardIcon),
              const SizedBox(width: 4),
              Text(
                AppLocalizations.of(context)!.creditCard,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.white,
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        const _TotalCreditCardExpense(),
        const SizedBox(height: 16),
        SizedBox(
          height: cardSettings.getPageViewHeight(),
          child: PageView.builder(
            onPageChanged: (index) {
              setState(() {
                currentIndex = index;
              });
            },
            padEnds: isOnlyOneItem,
            controller: PageController(viewportFraction: viewPortFraction),
            itemCount: itemCount,
            itemBuilder: (BuildContext context, int itemIndex) {
              if (itemIndex < state.freeeCreditCardExpenses.length) {
                return DynamicPadding(
                  isLoading: state.isCreditCardInRefresh,
                  isLastItem: isLastItem,
                  isOnlyOneItem: isOnlyOneItem,
                  child: CreditCardBody(
                    freeeCreditCardExpense:
                        state.freeeCreditCardExpenses[itemIndex],
                    onRefreshButtonPressed:
                        _refreshButtonEnabled ? _refreshButtonCoolDown : null,
                    itemIndex: itemIndex,
                  ),
                );
              } else {
                // 最後尾は他行連携リンクを表示
                return DynamicPadding(
                  isLoading: state.isCreditCardInRefresh,
                  isLastItem: isLastItem,
                  isOnlyOneItem: isOnlyOneItem,
                  child: AddCreditCard(
                    onRefreshButtonPressed:
                        _refreshButtonEnabled ? _refreshButtonCoolDown : null,
                  ),
                );
              }
            },
          ),
        ),
      ],
    );
  }
}

class _NoCreditCard extends StatelessWidget {
  const _NoCreditCard();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SvgPicture.asset(Assets.creditCardIcon),
              const SizedBox(width: 4),
              Text(
                AppLocalizations.of(context)!.creditCard,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.white,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 18),
          const _TotalCreditCardExpenseForNoCreditCard(),
          const SizedBox(height: 16),
          const Align(
            alignment: Alignment.center,
            child: _CreditCardLinkageSetting(),
          ),
        ],
      ),
    );
  }
}

class _CreditCardLinkageSetting extends HookConsumerWidget {
  const _CreditCardLinkageSetting();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return Column(
      children: [
        SizedBox(
          width: 312,
          height: 207,
          child: Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.topCenter,
            children: [
              Image.asset(Assets.freeeLinkCard),
              Padding(
                padding: const EdgeInsets.fromLTRB(36, 27, 37, 24),
                child: SizedBox(
                  child: Column(
                    children: [
                      Expanded(
                        child: Center(
                          child: SizedBox(
                            child: SvgPicture.asset(Assets.addCreditCardIcon),
                          ),
                        ),
                      ),
                      FittedBox(
                        child: SizedBox(
                          height: 40,
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              backgroundColor: AppColors.white,
                              side: BorderSide(
                                color: AppColors.primary,
                                width: 1,
                              ),
                              shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4.0)),
                              ),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(Assets.addFreeeLinkIcon),
                                SizedBox(width: 4),
                                Text(
                                  AppLocalizations.of(context)!
                                      .linkingWithFreee,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                        fontWeight: FontWeight.w700,
                                        color: AppColors.primary,
                                      ),
                                ),
                              ],
                            ),
                            onPressed: () async {
                              logController.sendButtonLog(
                                buttonName: AppLocalizations.of(context)!
                                    .linkingWithFreee,
                                screenName: AppLocalizations.of(context)!.home,
                              );
                              await showModalBottomSheet(
                                context: context,
                                isDismissible: false,
                                isScrollControlled: true,
                                enableDrag: false,
                                useSafeArea: true,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(8),
                                    topLeft: Radius.circular(8),
                                  ),
                                ),
                                builder: (BuildContext context) {
                                  return FreeeLinkageFlowModal();
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        AppLocalizations.of(context)!.availableForFree,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontSize: 12,
                              color: AppColors.white,
                            ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _FreeeExpirationCreditCard extends HookConsumerWidget {
  const _FreeeExpirationCreditCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final error = ref.watch(homeScreenProvider).freeeBankAccountError;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SvgPicture.asset(Assets.creditCardIcon),
                  const SizedBox(width: 4),
                  Text(
                    AppLocalizations.of(context)!.creditCard,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                          fontSize: 20,
                          color: AppColors.white,
                        ),
                  ),
                ],
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        const _TotalCreditCardExpense(),
        const SizedBox(height: 48),
        ErrorBody(
          errorCode: error?.code ?? '',
          errorMessage: error?.message ?? '',
          hasFaq: error?.hasFaq ?? false,
          textOverflow: TextOverflow.ellipsis,
          errorCategory: ErrorCategory.freeeError,
          isFreeeCreditCardExpired: true,
        ),
      ],
    );
  }
}

class _ErrorLinkageSetting extends HookConsumerWidget {
  const _ErrorLinkageSetting();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SvgPicture.asset(Assets.creditCardIcon),
              const SizedBox(width: 4),
              Text(
                AppLocalizations.of(context)!.creditCard,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: 20,
                      color: AppColors.white,
                    ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 48),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.red),
            borderRadius: BorderRadius.circular(4),
            color: AppColors.white,
          ),
          child: SizedBox(
            height: 218,
            width: 343,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),
                  Text(
                    state.freeeLinkError?.code ?? '',
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontFamily: FontFamily.robotoCondensed,
                          color: AppColors.grayGreen,
                        ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SvgPicture.asset(
                        Assets.alertIcon,
                      ),
                      const SizedBox(width: 8),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 54,
                            width: 275,
                            child: Text(
                              state.freeeLinkError?.message ?? '',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyLarge!
                                  .copyWith(
                                    color: AppColors.red,
                                    fontWeight: FontWeight.w700,
                                  ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          SizedBox(
                            width: 178,
                            height: 24,
                            child: Visibility(
                              visible: state.freeeLinkError?.hasFaq ?? false,
                              child: FaqContent(
                                linkId: LinkIds.freeeLinksFaq,
                                buttonName:
                                    AppLocalizations.of(context)!.freeeFAQ,
                                screenId: ScreenIdNumber.homeScreenId,
                                screenName: AppLocalizations.of(context)!.home,
                                icon: Assets.cardLinkIcon,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// クレジットカード利用額合計の桁数チェック、金額の算出と合計額、利用額の表示/非表示処理
class _TotalCreditCardExpense extends HookConsumerWidget {
  const _TotalCreditCardExpense();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    //クレカ利用金額合計の桁数チェック。15桁より大きい場合、エラーメッセージを表示する。
    const totalCreditCreditExpenseMaxDigit = 15;
    final isValidDigit = state.totalCreditExpense.toString().length <=
        totalCreditCreditExpenseMaxDigit;
    final String totalCreditExpense;
    final isValidTotalCreditExpense = state.totalCreditExpense != null;

    if (!state.isCreditCardExpenseVisible) {
      totalCreditExpense = AppLocalizations.of(context)!.fullWidthHyphen;
    } else if (isValidDigit) {
      totalCreditExpense = state.totalCreditExpense == null
          ? AppLocalizations.of(context)!.fullWidthHyphen
          : state.totalCreditExpense!.withCommas;
    } else {
      totalCreditExpense =
          AppLocalizations.of(context)!.excessBalanceErrorMessage;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  SizedBox(
                    width: 28,
                    child: Transform.translate(
                      offset: Offset(0, 3),
                      child: Text(
                        AppLocalizations.of(context)!.total,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontFamily: FontFamily.notoSansJP,
                              fontSize: 14,
                              color: AppColors.white,
                            ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  state.isCreditCardInRefresh
                      ? const BankAccountBalanceCardShimmer(
                          width: 170,
                          height: 16,
                        )
                      : _TotalExpense(totalCreditExpense: totalCreditExpense),
                  const SizedBox(width: 8),
                ],
              ),
              // 合計金額が取得できない時は表示しない
              Visibility(
                visible: isValidTotalCreditExpense,
                child: GestureDetector(
                  onTap: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!
                          .creditCardExpenseDisplaySwitching,
                      screenName: AppLocalizations.of(context)!.home,
                    );

                    controller.toggleCreditCardVisibility();
                  },
                  child: Container(
                    width: 52,
                    height: 28,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        color: AppColors.borderGrayGreen,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        state.isCreditCardExpenseVisible
                            ? AppLocalizations.of(context)!.hidden
                            : AppLocalizations.of(context)!.display,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              fontFamily: FontFamily.notoSansJP,
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                              color: AppColors.tradGreen,
                            ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// _NoCreditCardにて利用する、クレジットカード利用額合計部分の静的なウィジェット
// 処理量削減を考慮して_TotalCreditCardExpenseと分けて実装している
class _TotalCreditCardExpenseForNoCreditCard extends StatelessWidget {
  const _TotalCreditCardExpenseForNoCreditCard();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                SizedBox(
                  width: 28,
                  child: Transform.translate(
                    offset: Offset(0, 3),
                    child: Text(
                      AppLocalizations.of(context)!.total,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontFamily: FontFamily.notoSansJP,
                            fontSize: 14,
                            color: AppColors.white,
                          ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  height: 36,
                  width: 170,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.fullWidthHyphen,
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                              color: AppColors.white,
                            ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context)!.yenJp,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontSize: 14,
                              fontWeight: FontWeight.w700,
                              fontFamily: FontFamily.notoSansJP,
                              color: AppColors.white,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }
}

class _TotalExpense extends HookConsumerWidget {
  const _TotalExpense({
    required this.totalCreditExpense,
  });

  final String totalCreditExpense;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final isTotalCreditExpenseNull = state.totalCreditExpense == null;
    double totalCreditExpenseFontSize = 24;

    if (totalCreditExpense ==
        AppLocalizations.of(context)!.excessBalanceErrorMessage) {
      totalCreditExpenseFontSize = 20;
    } else if (totalCreditExpense.length < 13) {
      totalCreditExpenseFontSize = 24;
    } else if (totalCreditExpense.length < 14) {
      totalCreditExpenseFontSize = 23;
    } else if (totalCreditExpense.length < 15) {
      totalCreditExpenseFontSize = 22;
    } else if (totalCreditExpense.length < 16) {
      totalCreditExpenseFontSize = 20;
    } else if (totalCreditExpense.length < 18) {
      totalCreditExpenseFontSize = 18;
    } else if (totalCreditExpense.length < 19) {
      totalCreditExpenseFontSize = 17;
    } else if (totalCreditExpense.length < 20) {
      totalCreditExpenseFontSize = 16;
    }

    if (isTotalCreditExpenseNull) {
      return SizedBox(
        height: 36,
        width: 170,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              AppLocalizations.of(context)!.fullWidthHyphen,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    color: AppColors.white,
                  ),
            ),
            const SizedBox(width: 4),
            Text(
              AppLocalizations.of(context)!.yenJp,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    fontFamily: FontFamily.notoSansJP,
                    color: AppColors.white,
                  ),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      width: 170,
      height: 36,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            state.isCreditCardExpenseVisible && state.totalCreditExpense! < 0
                ? '- $totalCreditExpense'
                : totalCreditExpense,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.white,
                  fontWeight: FontWeight.w700,
                  fontSize: totalCreditExpenseFontSize,
                ),
          ),
          const SizedBox(width: 4),
          Transform.translate(
            offset: Offset(0, 3),
            child: Text(
              AppLocalizations.of(context)!.yenJp,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontSize: 14,
                    fontWeight: FontWeight.w700,
                    fontFamily: FontFamily.notoSansJP,
                    color: AppColors.white,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
