import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BankAccountBalanceCardShimmer extends StatelessWidget {
  const BankAccountBalanceCardShimmer({
    super.key,
    required this.width,
    required this.height,
  });

  final double width;
  final double height;

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.grey300,
      highlightColor: AppColors.white,
      child: Container(
        width: width,
        height: height,
        color: AppColors.grey100,
      ),
    );
  }
}
