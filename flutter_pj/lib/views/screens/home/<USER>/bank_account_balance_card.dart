import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/home/<USER>/loading_card.dart';
import 'package:dtp_app/views/screens/home/<USER>/error_card_body.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 口座残高カード
class BankAccountBalanceCard extends StatelessWidget {
  const BankAccountBalanceCard({
    super.key,
    required this.bankAccount,
    required this.isReturned,
    required this.displayName,
    required this.onRefreshButtonPressed,
    required this.carouselHeight,
  });

  final BankAccount bankAccount;
  final bool isReturned;
  final String displayName;
  final void Function()? onRefreshButtonPressed;
  final double carouselHeight;

  @override
  Widget build(BuildContext context) {
    return bankAccount.when(
      (baseInfo, displayConfig) => LoadingCard(isReturned: isReturned),
      withBalanceDetail: (baseInfo, balanceDetail, displayConfig) =>
          _BankAccountBalanceCard(
        bankAccount: bankAccount as BankAccountWithBalanceDetail,
        isReturned: isReturned,
        onRefreshButtonPressed: onRefreshButtonPressed,
        carouselHeight: carouselHeight,
      ),
      withError: (baseInfo, error, displayConfig) => _BankAccountErrorCard(
        bankAccount: bankAccount as BankAccountWithError,
        isReturned: isReturned,
        onRefreshButtonPressed: onRefreshButtonPressed,
      ),
    );
  }
}

/// 通常時のカード表示
class _BankAccountBalanceCard extends HookConsumerWidget {
  const _BankAccountBalanceCard({
    required this.bankAccount,
    required this.isReturned,
    required this.onRefreshButtonPressed,
    required this.carouselHeight,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final bool isReturned;
  final void Function()? onRefreshButtonPressed;
  final double carouselHeight;

  int? get _currentBalance => bankAccount.balanceDetail.currentBalance;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isCurrentBalanceNull = _currentBalance == null;
    final serverDate = state.serverDate;

    double fontSize = 28.0;
    if (_currentBalance.toString().length == 13) {
      fontSize = 26.0;
    } else if (_currentBalance.toString().length == 14) {
      fontSize = 24.0;
    } else if (_currentBalance.toString().length == 15) {
      fontSize = 23.0;
    }

    return Stack(
      children: [
        SizedBox(
          width: 312,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                Row(
                  children: [
                    SizedBox(
                      width: 134,
                      height: 24,
                      child: Row(
                        children: [
                          // リフレッシュボタン押下時に指定口座残高取得APIを投げて情報更新、10秒間押下不可とする
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          NullableHyphenText(
                            data: bankAccount.baseDate,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.textGrayGreen,
                                    ),
                          ),
                          const SizedBox(width: 4),
                          NullableHyphenText(
                            data: bankAccount.baseTime,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.textGrayGreen,
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _BaseInfo(
                      bankAccount: bankAccount,
                      isReturned: isReturned,
                    ),
                    const SizedBox(height: 6),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        isCurrentBalanceNull
                            ? Text(
                                AppLocalizations.of(context)!.fullWidthHyphen,
                                style: const TextStyle(
                                  color: AppColors.textBlack,
                                  fontSize: 20,
                                  fontWeight: FontWeight.w700,
                                ),
                              )
                            : SizedBox(
                                width: 219,
                                height: 42,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  textBaseline: TextBaseline.alphabetic,
                                  children: [
                                    state.isBalanceVisible &&
                                            _currentBalance != null
                                        ? Text(
                                            (state.isBalanceVisible &&
                                                    _currentBalance! < 0)
                                                ? '- ${_currentBalance!.withCommas}'
                                                : _currentBalance!.withCommas,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleLarge!
                                                .copyWith(
                                                  fontFamily: FontFamily
                                                      .robotoCondensed,
                                                  fontSize: fontSize,
                                                  color: AppColors.textBlack,
                                                ),
                                          )
                                        : Text(
                                            AppLocalizations.of(context)!
                                                .fullWidthHyphen,
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleLarge!
                                                .copyWith(
                                                  fontFamily: FontFamily
                                                      .robotoCondensed,
                                                  fontSize: 28,
                                                  color: AppColors.textBlack,
                                                ),
                                          ),
                                    const SizedBox(width: 4),
                                    Text(
                                      AppLocalizations.of(context)!.yenJp,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall!
                                          .copyWith(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                            fontFamily: FontFamily.notoSansJP,
                                            color: AppColors.textBlack,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                        const SizedBox(width: 16),
                        Container(
                          width: 1,
                          height: 24,
                          color: AppColors.borderGrayGreen,
                        ),
                        const SizedBox(width: 16),
                        SizedBox(
                          height: 21,
                          child: Text(
                            AppLocalizations.of(context)!.statement,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.notoSansJP,
                                      color: AppColors.tradGreen,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: onRefreshButtonPressed,
                  child: Container(
                    width: 132,
                    height: 54,
                    color: AppColors.transparent,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName:
                          AppLocalizations.of(context)!.incomeAndExpenditure,
                      screenName: AppLocalizations.of(context)!.home,
                    );

                    // 入出金明細画面に遷移
                    Navigator.of(context).pushNamed(
                      RouteNames.incomeAndExpenditure,
                      arguments: {
                        'bankAccount': bankAccount,
                        'serverDate': serverDate,
                      },
                    );
                  },
                  child: Container(
                    color: AppColors.transparent,
                    width: 180,
                    height: 54,
                  ),
                ),
              ],
            ),
            GestureDetector(
              onTap: () {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName:
                      AppLocalizations.of(context)!.incomeAndExpenditure,
                  screenName: AppLocalizations.of(context)!.home,
                );

                // 入出金明細画面に遷移
                Navigator.of(context).pushNamed(
                  RouteNames.incomeAndExpenditure,
                  arguments: {
                    'bankAccount': bankAccount,
                    'serverDate': serverDate,
                  },
                );
              },
              child: Container(
                color: AppColors.transparent,
                width: 312,
                height: carouselHeight - 54,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// エラー時のカード表示
class _BankAccountErrorCard extends StatelessWidget {
  const _BankAccountErrorCard({
    required this.bankAccount,
    required this.isReturned,
    required this.onRefreshButtonPressed,
  });

  final BankAccountWithError bankAccount;
  final bool isReturned;
  final void Function()? onRefreshButtonPressed;

  @override
  Widget build(BuildContext context) {
    final smbcBankAccountError = bankAccount.error;

    return Stack(
      children: [
        SizedBox(
          width: 312,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                Row(
                  children: [
                    SizedBox(
                      width: 134,
                      height: 24,
                      child: Row(
                        children: [
                          // リフレッシュボタン押下時に指定口座残高取得APIを投げて情報更新、10秒間押下不可とする
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          NullableHyphenText(
                            data: smbcBankAccountError.baseDateTime,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.textGrayGreen,
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _BaseInfo(
                      bankAccount: bankAccount,
                      isReturned: isReturned,
                    ),
                    const SizedBox(height: 6),
                    ErrorBody(
                      errorCode: smbcBankAccountError.code ?? '',
                      errorMessage: smbcBankAccountError.message ?? '',
                      hasFaq: smbcBankAccountError.hasFaq,
                      maxLines: 3,
                      textOverflow: TextOverflow.ellipsis,
                      errorCategory: ErrorCategory.smbcBankAccountError,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            GestureDetector(
              onTap: onRefreshButtonPressed,
              child: Container(
                width: 132,
                height: 54,
                color: AppColors.transparent,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _BaseInfo extends StatelessWidget {
  const _BaseInfo({
    required this.bankAccount,
    required this.isReturned,
  });

  final BankAccount bankAccount;
  final bool isReturned;

  @override
  Widget build(BuildContext context) {
    final displayAccountName = bankAccount.displayConfig.displayName;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 280,
          height: 24,
          child: Text(
            displayAccountName.isNotEmpty
                ? displayAccountName
                : AppLocalizations.of(context)!.noName,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  fontFamily: FontFamily.notoSansJP,
                  color: displayAccountName.isNotEmpty
                      ? AppColors.textBlack
                      : AppColors.inactiveText,
                ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // 横に並べる際も金融機関名は省略せず、万一オーバーフローする際には支店名を省略
            NullableHyphenText(
              data: bankAccount.baseInfo.bankName,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontFamily: FontFamily.notoSansJP,
                    color: AppColors.textBlack,
                  ),
            ),
            const SizedBox(width: 8),
            // 支店名+銀行名が合計20文字以下の場合には、銀行名の横に支店名を表示
            Visibility(
              visible: !isReturned,
              child: Flexible(
                child: NullableHyphenText(
                  data: bankAccount.baseInfo.branchNameKanji,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontFamily: FontFamily.notoSansJP,
                        color: AppColors.textBlack,
                      ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Visibility(
          visible: isReturned,
          child: Column(
            children: [
              NullableHyphenText(
                data: bankAccount.baseInfo.branchNameKanji,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: FontFamily.notoSansJP,
                      color: AppColors.textBlack,
                    ),
              ),
              const SizedBox(height: 4),
            ],
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            NullableHyphenText(
              data: bankAccount.baseInfo.accountType,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontFamily: FontFamily.notoSansJP,
                    color: AppColors.textBlack,
                  ),
            ),
            const SizedBox(width: 8),
            NullableHyphenText(
              data: bankAccount.baseInfo.accountNumber,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    color: AppColors.textBlack,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          // 振込依頼人名 nullの場合は空文字で表示（webと表示を統一）
          bankAccount.baseInfo.remitterName ?? '',
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontFamily: FontFamily.notoSansJP,
                fontSize: 14,
                color: AppColors.textBlack,
              ),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
