import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// HOME画面表示用の動的なPaddingのクラス
/// 基本的には券面を左付で表示、最終券面のみ右付という要件を満たすためのもの
class DynamicPadding extends StatelessWidget {
  const DynamicPadding({
    super.key,
    required this.child,
    required this.isLastItem,
    required this.isOnlyOneItem,
    required this.isLoading,
  });

  final Widget child;
  final bool isOnlyOneItem;
  final bool isLastItem;
  final bool isLoading;

  // 画面毎ではなく、カード毎に設定をする必要があるためHomeScreenSetting外にロジックを実装
  EdgeInsets get _padding {
    // 一枚のみ且つロードされていない際には中央揃えで表示
    if (isOnlyOneItem && !isLoading) return EdgeInsets.zero;
    // 最終券面且つ券面数が一枚以上存在している際には右寄せで表示
    if (isLastItem && !isOnlyOneItem) return EdgeInsets.only(right: 16);
    return EdgeInsets.only(left: 16);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: _padding,
      child: child.animate().fadeIn(),
    );
  }
}
