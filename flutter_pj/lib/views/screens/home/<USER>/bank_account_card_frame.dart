import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';

class BankAccountCardFrame extends StatelessWidget {
  const BankAccountCardFrame({
    super.key,
    required this.backgroundGradientColors,
    required this.label,
    required this.child,
  });

  final List<Color> backgroundGradientColors;
  final Widget label;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(
          width: 312,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: backgroundGradientColors,
            ),
            border: Border.all(
              color: backgroundGradientColors[0],
              width: 1,
            ),
          ),
          child: label,
        ),
        Container(
          margin: const EdgeInsets.only(top: 6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Image.asset(
                Assets.whiteCardBar,
              ),
              Expanded(
                child: Container(
                  width: 312,
                  decoration: const BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.vertical(
                      bottom: Radius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        child,
      ],
    );
  }
}
