// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account_card_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BankAccountCardItem {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccount bankAccount) smbc,
    required TResult Function(BankAccount bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(BankAccountWithBalanceDetail bankAccount)
        freeeReConnectFinancialInstitution,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccount bankAccount)? smbc,
    TResult? Function(BankAccount bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccount bankAccount)? smbc,
    TResult Function(BankAccount bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReConnect value)
        freeeReConnectFinancialInstitution,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountCardItemCopyWith<$Res> {
  factory $BankAccountCardItemCopyWith(
          BankAccountCardItem value, $Res Function(BankAccountCardItem) then) =
      _$BankAccountCardItemCopyWithImpl<$Res, BankAccountCardItem>;
}

/// @nodoc
class _$BankAccountCardItemCopyWithImpl<$Res, $Val extends BankAccountCardItem>
    implements $BankAccountCardItemCopyWith<$Res> {
  _$BankAccountCardItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$BankAccountCardItemSMBCImplCopyWith<$Res> {
  factory _$$BankAccountCardItemSMBCImplCopyWith(
          _$BankAccountCardItemSMBCImpl value,
          $Res Function(_$BankAccountCardItemSMBCImpl) then) =
      __$$BankAccountCardItemSMBCImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccount bankAccount});

  $BankAccountCopyWith<$Res> get bankAccount;
}

/// @nodoc
class __$$BankAccountCardItemSMBCImplCopyWithImpl<$Res>
    extends _$BankAccountCardItemCopyWithImpl<$Res,
        _$BankAccountCardItemSMBCImpl>
    implements _$$BankAccountCardItemSMBCImplCopyWith<$Res> {
  __$$BankAccountCardItemSMBCImplCopyWithImpl(
      _$BankAccountCardItemSMBCImpl _value,
      $Res Function(_$BankAccountCardItemSMBCImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = null,
  }) {
    return _then(_$BankAccountCardItemSMBCImpl(
      bankAccount: null == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccount,
    ));
  }

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BankAccountCopyWith<$Res> get bankAccount {
    return $BankAccountCopyWith<$Res>(_value.bankAccount, (value) {
      return _then(_value.copyWith(bankAccount: value));
    });
  }
}

/// @nodoc

class _$BankAccountCardItemSMBCImpl implements BankAccountCardItemSMBC {
  const _$BankAccountCardItemSMBCImpl({required this.bankAccount});

  @override
  final BankAccount bankAccount;

  @override
  String toString() {
    return 'BankAccountCardItem.smbc(bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemSMBCImpl &&
            (identical(other.bankAccount, bankAccount) ||
                other.bankAccount == bankAccount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bankAccount);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemSMBCImplCopyWith<_$BankAccountCardItemSMBCImpl>
      get copyWith => __$$BankAccountCardItemSMBCImplCopyWithImpl<
          _$BankAccountCardItemSMBCImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccount bankAccount) smbc,
    required TResult Function(BankAccount bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(BankAccountWithBalanceDetail bankAccount)
        freeeReConnectFinancialInstitution,
  }) {
    return smbc(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccount bankAccount)? smbc,
    TResult? Function(BankAccount bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
  }) {
    return smbc?.call(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccount bankAccount)? smbc,
    TResult Function(BankAccount bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (smbc != null) {
      return smbc(bankAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReConnect value)
        freeeReConnectFinancialInstitution,
  }) {
    return smbc(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
  }) {
    return smbc?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (smbc != null) {
      return smbc(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemSMBC implements BankAccountCardItem {
  const factory BankAccountCardItemSMBC(
      {required final BankAccount bankAccount}) = _$BankAccountCardItemSMBCImpl;

  BankAccount get bankAccount;

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemSMBCImplCopyWith<_$BankAccountCardItemSMBCImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemFreeeImplCopyWith<$Res> {
  factory _$$BankAccountCardItemFreeeImplCopyWith(
          _$BankAccountCardItemFreeeImpl value,
          $Res Function(_$BankAccountCardItemFreeeImpl) then) =
      __$$BankAccountCardItemFreeeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccount bankAccount});

  $BankAccountCopyWith<$Res> get bankAccount;
}

/// @nodoc
class __$$BankAccountCardItemFreeeImplCopyWithImpl<$Res>
    extends _$BankAccountCardItemCopyWithImpl<$Res,
        _$BankAccountCardItemFreeeImpl>
    implements _$$BankAccountCardItemFreeeImplCopyWith<$Res> {
  __$$BankAccountCardItemFreeeImplCopyWithImpl(
      _$BankAccountCardItemFreeeImpl _value,
      $Res Function(_$BankAccountCardItemFreeeImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = null,
  }) {
    return _then(_$BankAccountCardItemFreeeImpl(
      bankAccount: null == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccount,
    ));
  }

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BankAccountCopyWith<$Res> get bankAccount {
    return $BankAccountCopyWith<$Res>(_value.bankAccount, (value) {
      return _then(_value.copyWith(bankAccount: value));
    });
  }
}

/// @nodoc

class _$BankAccountCardItemFreeeImpl implements BankAccountCardItemFreee {
  const _$BankAccountCardItemFreeeImpl({required this.bankAccount});

  @override
  final BankAccount bankAccount;

  @override
  String toString() {
    return 'BankAccountCardItem.freee(bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemFreeeImpl &&
            (identical(other.bankAccount, bankAccount) ||
                other.bankAccount == bankAccount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, bankAccount);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemFreeeImplCopyWith<_$BankAccountCardItemFreeeImpl>
      get copyWith => __$$BankAccountCardItemFreeeImplCopyWithImpl<
          _$BankAccountCardItemFreeeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccount bankAccount) smbc,
    required TResult Function(BankAccount bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(BankAccountWithBalanceDetail bankAccount)
        freeeReConnectFinancialInstitution,
  }) {
    return freee(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccount bankAccount)? smbc,
    TResult? Function(BankAccount bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
  }) {
    return freee?.call(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccount bankAccount)? smbc,
    TResult Function(BankAccount bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (freee != null) {
      return freee(bankAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReConnect value)
        freeeReConnectFinancialInstitution,
  }) {
    return freee(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
  }) {
    return freee?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (freee != null) {
      return freee(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemFreee implements BankAccountCardItem {
  const factory BankAccountCardItemFreee(
          {required final BankAccount bankAccount}) =
      _$BankAccountCardItemFreeeImpl;

  BankAccount get bankAccount;

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemFreeeImplCopyWith<_$BankAccountCardItemFreeeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemSMBCErrorImplCopyWith<$Res> {
  factory _$$BankAccountCardItemSMBCErrorImplCopyWith(
          _$BankAccountCardItemSMBCErrorImpl value,
          $Res Function(_$BankAccountCardItemSMBCErrorImpl) then) =
      __$$BankAccountCardItemSMBCErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$BankAccountCardItemSMBCErrorImplCopyWithImpl<$Res>
    extends _$BankAccountCardItemCopyWithImpl<$Res,
        _$BankAccountCardItemSMBCErrorImpl>
    implements _$$BankAccountCardItemSMBCErrorImplCopyWith<$Res> {
  __$$BankAccountCardItemSMBCErrorImplCopyWithImpl(
      _$BankAccountCardItemSMBCErrorImpl _value,
      $Res Function(_$BankAccountCardItemSMBCErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$BankAccountCardItemSMBCErrorImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
    ));
  }

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$BankAccountCardItemSMBCErrorImpl
    implements BankAccountCardItemSMBCError {
  const _$BankAccountCardItemSMBCErrorImpl({required this.error});

  @override
  final AppError error;

  @override
  String toString() {
    return 'BankAccountCardItem.smbcError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemSMBCErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemSMBCErrorImplCopyWith<
          _$BankAccountCardItemSMBCErrorImpl>
      get copyWith => __$$BankAccountCardItemSMBCErrorImplCopyWithImpl<
          _$BankAccountCardItemSMBCErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccount bankAccount) smbc,
    required TResult Function(BankAccount bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(BankAccountWithBalanceDetail bankAccount)
        freeeReConnectFinancialInstitution,
  }) {
    return smbcError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccount bankAccount)? smbc,
    TResult? Function(BankAccount bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
  }) {
    return smbcError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccount bankAccount)? smbc,
    TResult Function(BankAccount bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (smbcError != null) {
      return smbcError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReConnect value)
        freeeReConnectFinancialInstitution,
  }) {
    return smbcError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
  }) {
    return smbcError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (smbcError != null) {
      return smbcError(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemSMBCError implements BankAccountCardItem {
  const factory BankAccountCardItemSMBCError({required final AppError error}) =
      _$BankAccountCardItemSMBCErrorImpl;

  AppError get error;

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemSMBCErrorImplCopyWith<
          _$BankAccountCardItemSMBCErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemFreeeErrorImplCopyWith<$Res> {
  factory _$$BankAccountCardItemFreeeErrorImplCopyWith(
          _$BankAccountCardItemFreeeErrorImpl value,
          $Res Function(_$BankAccountCardItemFreeeErrorImpl) then) =
      __$$BankAccountCardItemFreeeErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$BankAccountCardItemFreeeErrorImplCopyWithImpl<$Res>
    extends _$BankAccountCardItemCopyWithImpl<$Res,
        _$BankAccountCardItemFreeeErrorImpl>
    implements _$$BankAccountCardItemFreeeErrorImplCopyWith<$Res> {
  __$$BankAccountCardItemFreeeErrorImplCopyWithImpl(
      _$BankAccountCardItemFreeeErrorImpl _value,
      $Res Function(_$BankAccountCardItemFreeeErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$BankAccountCardItemFreeeErrorImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
    ));
  }

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$BankAccountCardItemFreeeErrorImpl
    implements BankAccountCardItemFreeeError {
  const _$BankAccountCardItemFreeeErrorImpl({required this.error});

  @override
  final AppError error;

  @override
  String toString() {
    return 'BankAccountCardItem.freeeError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemFreeeErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemFreeeErrorImplCopyWith<
          _$BankAccountCardItemFreeeErrorImpl>
      get copyWith => __$$BankAccountCardItemFreeeErrorImplCopyWithImpl<
          _$BankAccountCardItemFreeeErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccount bankAccount) smbc,
    required TResult Function(BankAccount bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(BankAccountWithBalanceDetail bankAccount)
        freeeReConnectFinancialInstitution,
  }) {
    return freeeError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccount bankAccount)? smbc,
    TResult? Function(BankAccount bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
  }) {
    return freeeError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccount bankAccount)? smbc,
    TResult Function(BankAccount bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (freeeError != null) {
      return freeeError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReConnect value)
        freeeReConnectFinancialInstitution,
  }) {
    return freeeError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
  }) {
    return freeeError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (freeeError != null) {
      return freeeError(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemFreeeError implements BankAccountCardItem {
  const factory BankAccountCardItemFreeeError({required final AppError error}) =
      _$BankAccountCardItemFreeeErrorImpl;

  AppError get error;

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemFreeeErrorImplCopyWith<
          _$BankAccountCardItemFreeeErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemFreeeReConnectImplCopyWith<$Res> {
  factory _$$BankAccountCardItemFreeeReConnectImplCopyWith(
          _$BankAccountCardItemFreeeReConnectImpl value,
          $Res Function(_$BankAccountCardItemFreeeReConnectImpl) then) =
      __$$BankAccountCardItemFreeeReConnectImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccountWithBalanceDetail bankAccount});
}

/// @nodoc
class __$$BankAccountCardItemFreeeReConnectImplCopyWithImpl<$Res>
    extends _$BankAccountCardItemCopyWithImpl<$Res,
        _$BankAccountCardItemFreeeReConnectImpl>
    implements _$$BankAccountCardItemFreeeReConnectImplCopyWith<$Res> {
  __$$BankAccountCardItemFreeeReConnectImplCopyWithImpl(
      _$BankAccountCardItemFreeeReConnectImpl _value,
      $Res Function(_$BankAccountCardItemFreeeReConnectImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = freezed,
  }) {
    return _then(_$BankAccountCardItemFreeeReConnectImpl(
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccountWithBalanceDetail,
    ));
  }
}

/// @nodoc

class _$BankAccountCardItemFreeeReConnectImpl
    implements BankAccountCardItemFreeeReConnect {
  const _$BankAccountCardItemFreeeReConnectImpl({required this.bankAccount});

  @override
  final BankAccountWithBalanceDetail bankAccount;

  @override
  String toString() {
    return 'BankAccountCardItem.freeeReConnectFinancialInstitution(bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemFreeeReConnectImpl &&
            const DeepCollectionEquality()
                .equals(other.bankAccount, bankAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(bankAccount));

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemFreeeReConnectImplCopyWith<
          _$BankAccountCardItemFreeeReConnectImpl>
      get copyWith => __$$BankAccountCardItemFreeeReConnectImplCopyWithImpl<
          _$BankAccountCardItemFreeeReConnectImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccount bankAccount) smbc,
    required TResult Function(BankAccount bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(BankAccountWithBalanceDetail bankAccount)
        freeeReConnectFinancialInstitution,
  }) {
    return freeeReConnectFinancialInstitution(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccount bankAccount)? smbc,
    TResult? Function(BankAccount bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
  }) {
    return freeeReConnectFinancialInstitution?.call(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccount bankAccount)? smbc,
    TResult Function(BankAccount bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (freeeReConnectFinancialInstitution != null) {
      return freeeReConnectFinancialInstitution(bankAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReConnect value)
        freeeReConnectFinancialInstitution,
  }) {
    return freeeReConnectFinancialInstitution(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
  }) {
    return freeeReConnectFinancialInstitution?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReConnect value)?
        freeeReConnectFinancialInstitution,
    required TResult orElse(),
  }) {
    if (freeeReConnectFinancialInstitution != null) {
      return freeeReConnectFinancialInstitution(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemFreeeReConnect
    implements BankAccountCardItem {
  const factory BankAccountCardItemFreeeReConnect(
          {required final BankAccountWithBalanceDetail bankAccount}) =
      _$BankAccountCardItemFreeeReConnectImpl;

  BankAccountWithBalanceDetail get bankAccount;

  /// Create a copy of BankAccountCardItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemFreeeReConnectImplCopyWith<
          _$BankAccountCardItemFreeeReConnectImpl>
      get copyWith => throw _privateConstructorUsedError;
}
