import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';

class AnnouncementInfoArea extends StatelessWidget {
  const AnnouncementInfoArea({
    super.key,
    this.announcementInfo,
    required this.isAnnouncementInfo,
    required this.style,
  });

  final String? announcementInfo;
  final bool isAnnouncementInfo;
  final TextStyle style;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isAnnouncementInfo,
      child: Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Container(
          decoration: BoxDecoration(
            color: AppColors.lightRed,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              // 表示されることはないがNPEを回避するためnull時は空文字を設定
              child: Text(
                announcementInfo ?? '',
                style: style,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

int calculateNumberOfLines({
  required String text,
  required double maxWidth,
  TextStyle? style,
}) {
  final span = TextSpan(text: text, style: style);
  final tp = TextPainter(text: span, textDirection: TextDirection.ltr);
  tp.layout(maxWidth: maxWidth);
  return tp.computeLineMetrics().length;
}
