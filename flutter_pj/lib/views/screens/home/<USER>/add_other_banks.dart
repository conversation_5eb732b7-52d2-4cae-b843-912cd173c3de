import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_update_dialog_content.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 他行口座追加
class AddOtherBankAccountCard extends HookConsumerWidget {
  const AddOtherBankAccountCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeController = ref.read(homeScreenProvider.notifier);
    final operationLogController = ref.read(operationLogControllerProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final appLocalizations = AppLocalizations.of(context)!;
    final freeeRedirectURL = ref.read(buildConfigProvider).freeeRedirectScreen;
    final urlLauncher = ref.read(urlLauncherProvider);

    return Stack(
      children: [
        SizedBox(
          width: 312,
          height: 213,
          child: Image.asset(Assets.addOtherBankCardFrame),
        ),
        Container(
          width: 312,
          height: 213,
          decoration: BoxDecoration(
            color: AppColors.white.withOpacity(0.6),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () async {
                  // ローディング開始
                  LoadingDialog.loading(context);
                  // 顧客操作ログ送信
                  unawaited(
                    operationLogController.sendOperationLog(
                      functionLog: OperationLogMessage.freeeIntegration,
                      operationLog: OperationLogMessage.freeeAddAccount,
                      resultLog: '',
                      errorIdLog: '',
                    ),
                  );
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: appLocalizations.addAccount,
                    screenName: appLocalizations.home,
                  );
                  final url = await homeController.getUrlToFreeeSSOPage(
                    freeeRedirectURL,
                    FreeeSSOTypeFromHome.bankAccount,
                  );
                  // URL取得時にエラーが発生した場合、後続処理を中断
                  if (ref
                          .read(homeScreenProvider)
                          .getUrlToFreeeSsoParamsError
                          .isNotNull ||
                      url == null) return;
                  await urlLauncher.launchExternalUrl(
                    appLocalizations.addAccount,
                    ScreenIdNumber.homeScreenId,
                    url,
                    useEncryptedCookie: true,
                  );
                  if (!context.mounted) return;
                  // ローディング終了
                  LoadingDialog.loadingEnd(context);
                  if (!context.mounted) return;
                  showFreeeUpdateDialog(
                    context,
                    ref,
                    AppLocalizations.of(context)!.closeAdditionAccount,
                    AppLocalizations.of(context)!.updateAdditionAccount,
                    isCreditCard: false,
                  );
                },
                child: Container(
                  width: 312,
                  height: 165,
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),
                      SizedBox(
                        width: 176,
                        child: Column(
                          children: [
                            SizedBox(
                              width: 40,
                              height: 40,
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  Container(
                                    width: 40,
                                    height: 40,
                                    decoration: const BoxDecoration(
                                      color: AppColors.white,
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  SvgPicture.asset(Assets.navigationIcon2),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            SizedBox(
                              width: 80,
                              height: 24,
                              child: Text(
                                AppLocalizations.of(context)!.addAccount,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      color: AppColors.tradGreen,
                                      fontWeight: FontWeight.w700,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 41),
                    ],
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName:
                        appLocalizations.reflectsOtherBankAccountSettings,
                    screenName: appLocalizations.home,
                  );

                  homeController
                      .getFreeeAccountBalance(InquiryCategory.bankAccount);
                },
                child: Container(
                  width: 312,
                  height: 48,
                  color: AppColors.transparent,
                  child: Column(
                    children: [
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            AppLocalizations.of(context)!
                                .linksToOtherBanksExplain3,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      color: AppColors.tradGreen,
                                    ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
