import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/freee_credit_card_expense/freee_credit_card_expense.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/screens/home/<USER>/bank_account_balance_card_shimmer.dart';
import 'package:dtp_app/views/screens/home/<USER>/error_card_body.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_update_dialog_content.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

/// 口座残高カード
class CreditCardBody extends HookConsumerWidget {
  const CreditCardBody({
    super.key,
    required this.freeeCreditCardExpense,
    required this.onRefreshButtonPressed,
    required this.itemIndex,
  });

  final CreditCardExpense freeeCreditCardExpense;
  final void Function()? onRefreshButtonPressed;
  final int itemIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final item = state.freeeCreditCardExpenses[itemIndex];

    final isBankAccountError = state.freeeBankAccountError != null;
    final isFreeeCreditCardError = item.freeeCreditCardError != null;
    final isLoading = item.isLoading;

    // 全体更新
    Future<void> refreshAllFreeCreditCardWalletables() async {
      if (onRefreshButtonPressed == null) {
        return;
      }
      // FirebaseAnalyticsログ送信
      analyticsLogController.sendButtonLog(
        buttonName: AppLocalizations.of(context)!.updateCreditCardInformation,
        screenName: AppLocalizations.of(context)!.home,
      );

      onRefreshButtonPressed!();
      await ref
          .watch(homeScreenProvider.notifier)
          .refreshAllFreeCreditCardWalletables();
    }

    // 個別更新
    Future<void> refreshCreditCardExpense() async {
      if (onRefreshButtonPressed == null) {
        return;
      }
      // FirebaseAnalyticsログ送信
      analyticsLogController.sendButtonLog(
        buttonName: AppLocalizations.of(context)!.updateCreditCardInformation,
        screenName: AppLocalizations.of(context)!.home,
      );

      onRefreshButtonPressed!();
      await ref
          .watch(homeScreenProvider.notifier)
          .refreshCreditCardExpense(itemIndex, item);
    }

    // ローディング中の場合
    if (isLoading) {
      return const _LoadingCard();
    }

    // 情報全取得エラーの場合
    if (isBankAccountError) {
      return _AllErrorCreditCard(
        onRefreshButtonPressed: refreshAllFreeCreditCardWalletables,
        freeeBankAccountError: state.freeeBankAccountError,
      );
    }

    // 情報部分取得エラーの場合
    if (!isBankAccountError && isFreeeCreditCardError && !isLoading) {
      return _PartialErrorCreditCard(
        lastSyncedAt: item.lastSyncedAt,
        displayName: item.creditCardName,
        freeeCreditCardError: item.freeeCreditCardError!,
        onRefreshButtonPressed: refreshAllFreeCreditCardWalletables,
      );
    }

    // 通常時
    if (!isBankAccountError && !isFreeeCreditCardError && !isLoading) {
      return item.isFreeeReConnectFinancialInstitution
          ? _FreeeReConnectFinancialInstitutionContentCreditCard(
              lastSyncedAt: item.lastSyncedAt,
              displayName: item.creditCardName,
              startDate: item.startDate,
              endDate: item.endDate,
              totalUsageAmount: item.totalExpense,
              onRefreshButtonPressed: refreshCreditCardExpense,
              itemIndex: itemIndex,
            )
          : _CreditCard(
              lastSyncedAt: item.lastSyncedAt,
              displayName: item.creditCardName,
              startDate: item.startDate,
              endDate: item.endDate,
              totalUsageAmount: item.totalExpense,
              onRefreshButtonPressed: refreshCreditCardExpense,
              itemIndex: itemIndex,
            );
    } else {
      return const _LoadingCard();
    }
  }
}

/// ローディング時のカード表示
class _LoadingCard extends StatelessWidget {
  const _LoadingCard();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 312,
          height: 207,
          decoration: const BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
        ),
        SizedBox(
          width: 312,
          height: 207,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                SizedBox(
                  width: 132,
                  height: 24,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: SvgPicture.asset(Assets.refreshIcon),
                      ),
                      const SizedBox(width: 8),
                      const BankAccountBalanceCardShimmer(
                        width: 100,
                        height: 21,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                const BankAccountBalanceCardShimmer(
                  width: 280,
                  height: 24,
                ),
                const SizedBox(height: 8),
                const BankAccountBalanceCardShimmer(
                  width: 280,
                  height: 21,
                ),
                const SizedBox(height: 40),
                Text(
                  AppLocalizations.of(context)!.totalUsageAmount,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        fontSize: 12,
                        color: AppColors.textGrayGreen,
                      ),
                ),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        SizedBox(width: 9),
                        BankAccountBalanceCardShimmer(
                          width: 74,
                          height: 21,
                        ),
                        SizedBox(width: 6),
                      ],
                    ),
                    SizedBox(width: 16),
                    BankAccountBalanceCardShimmer(
                      width: 190,
                      height: 36,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// 通常時のカード表示
class _CreditCard extends HookConsumerWidget {
  const _CreditCard({
    this.lastSyncedAt,
    required this.displayName,
    required this.startDate,
    required this.endDate,
    required this.totalUsageAmount,
    this.onRefreshButtonPressed,
    required this.itemIndex,
  });

  final String? lastSyncedAt;
  final String displayName;
  final String? startDate;
  final String? endDate;
  final int? totalUsageAmount;
  final void Function()? onRefreshButtonPressed;
  final int itemIndex;

  // yyyy-MM-dd
  String _convertDateString(String inputDateString) {
    if (inputDateString.isEmpty) {
      return '';
    }
    // 入力された文字列をDateTime型に変換
    final dateTime = DateTime.parse(inputDateString);

    // M/d形式に変換
    final formattedDate = DateFormat('M/d').format(dateTime);

    return formattedDate;
  }

  void onTapCreditCardStatementInquiry(
    BuildContext context,
    HomeScreenNotifier controller,
    HomeNavigationScreenNotifier navigationController,
    AnalyticsLogController analyticsLogController,
  ) {
    // FirebaseAnalyticsログ送信
    analyticsLogController.sendButtonLog(
      buttonName: AppLocalizations.of(context)!.cardDetail,
      screenName: AppLocalizations.of(context)!.home,
    );
    // クレジットカード利用明細画面に遷移
    controller.updateItemIndex(itemIndex);
    navigationController.onTapCreditCardStatementInquiry();
    navigationController.changeCurrentIndex(1);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final navigationController =
        ref.watch(homeNavigationScreenProvider.notifier);

    double fontSize = 24;
    if (totalUsageAmount.toString().length == 9) {
      fontSize = 23;
    }
    if (totalUsageAmount.toString().length == 10) {
      fontSize = 20;
    } else if (totalUsageAmount.toString().length == 11) {
      fontSize = 19;
    } else if (totalUsageAmount.toString().length == 12) {
      fontSize = 17;
    } else if (totalUsageAmount.toString().length == 13) {
      fontSize = 16;
    } else if (totalUsageAmount.toString().length == 14) {
      fontSize = 15;
    } else if (totalUsageAmount.toString().length == 15) {
      fontSize = 14;
    } else if (totalUsageAmount.toString().length == 16) {
      fontSize = 13;
    } else if (totalUsageAmount.toString().length >= 17) {
      fontSize = 10;
    }

    return Stack(
      children: [
        Container(
          width: 312,
          height: 207,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.creditCardShadow.withOpacity(0.4),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        SizedBox(
          width: 312,
          height: 207,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    SizedBox(
                      width: 132,
                      height: 24,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          NullableHyphenText(
                            data: lastSyncedAt,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.textGrayGreen,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: 280,
                      child: NullableHyphenText(
                        data: displayName.ellipsis(maxLength: 34),
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(fontSize: 16),
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: 280,
                      child: NullableHyphenText(
                        // Todo: SP15,16ではクレジットカード種類は表示しない
                        data: '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.totalUsageAmount,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontSize: 12,
                            color: AppColors.textGrayGreen,
                          ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          alignment: Alignment.centerLeft,
                          height: 36,
                          child: Container(
                            alignment: AlignmentDirectional.centerStart,
                            width: 64,
                            height: 18,
                            child: NullableHyphenText(
                              data:
                                  '${_convertDateString(startDate ?? '')}~${_convertDateString(endDate ?? '')}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                    fontSize: 12,
                                  ),
                            ),
                          ),
                        ),
                        SizedBox(width: 14),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            Container(
                              width: 123,
                              height: 36,
                              alignment: Alignment.centerRight,
                              child: state.isCreditCardExpenseVisible &&
                                      totalUsageAmount != null
                                  ? Text(
                                      (state.isCreditCardExpenseVisible &&
                                              totalUsageAmount! < 0)
                                          ? '- ${totalUsageAmount!.withCommas}'
                                          : totalUsageAmount!.withCommas,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge!
                                          .copyWith(
                                            fontFamily:
                                                FontFamily.robotoCondensed,
                                            fontSize: fontSize,
                                          ),
                                      maxLines: 1,
                                    )
                                  : Text(
                                      AppLocalizations.of(context)!
                                          .fullWidthHyphen,
                                      style: Theme.of(context)
                                          .textTheme
                                          .titleLarge!
                                          .copyWith(
                                            fontFamily:
                                                FontFamily.robotoCondensed,
                                            fontSize: 28,
                                          ),
                                    ),
                            ),
                            SizedBox(
                              width: 4,
                            ),
                            Text(
                              AppLocalizations.of(context)!.yenJp,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w700,
                                    fontFamily: FontFamily.notoSansJP,
                                    color: AppColors.textBlack,
                                    height: 1.5,
                                  ),
                            ),
                            const SizedBox(width: 16),
                            Container(
                              alignment: Alignment.bottomCenter,
                              width: 1,
                              height: 24,
                              color: AppColors.borderGrayGreen,
                            ),
                            const SizedBox(width: 16),
                            Text(
                              AppLocalizations.of(context)!.statement,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(
                                    fontFamily: FontFamily.notoSansJP,
                                    color: AppColors.tradGreen,
                                    fontSize: 14,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ],
            ),
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: onRefreshButtonPressed,
                  child: Container(
                    color: AppColors.transparent,
                    width: 132,
                    height: 54,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    onTapCreditCardStatementInquiry(
                      context,
                      controller,
                      navigationController,
                      analyticsLogController,
                    );
                  },
                  child: Container(
                    color: AppColors.transparent,
                    width: 180,
                    height: 54,
                  ),
                ),
              ],
            ),
            GestureDetector(
              onTap: () {
                onTapCreditCardStatementInquiry(
                  context,
                  controller,
                  navigationController,
                  analyticsLogController,
                );
              },
              child: Container(
                color: AppColors.transparent,
                width: 312,
                height: 153,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// freee再連携時のカード表示
class _FreeeReConnectFinancialInstitutionContentCreditCard
    extends HookConsumerWidget {
  const _FreeeReConnectFinancialInstitutionContentCreditCard({
    this.lastSyncedAt,
    required this.displayName,
    required this.startDate,
    required this.endDate,
    required this.totalUsageAmount,
    this.onRefreshButtonPressed,
    required this.itemIndex,
  });

  final String? lastSyncedAt;
  final String displayName;
  final String? startDate;
  final String? endDate;
  final int? totalUsageAmount;
  final void Function()? onRefreshButtonPressed;
  final int itemIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final operationLogController = ref.read(operationLogControllerProvider);
    final freeeRedirectURL = ref.read(buildConfigProvider).freeeRedirectScreen;
    final homeController = ref.read(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final urlLauncher = ref.read(urlLauncherProvider);

    return Stack(
      children: [
        Container(
          width: 312,
          height: 323,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.creditCardShadow.withOpacity(0.4),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        SizedBox(
          width: 312,
          height: 323,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    SizedBox(
                      width: 132,
                      height: 24,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          NullableHyphenText(
                            data: lastSyncedAt,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.textGrayGreen,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      height: 24,
                      width: 280,
                      child: NullableHyphenText(
                        data: displayName.ellipsis(maxLength: 34),
                        maxLines: 2,
                        style: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(fontSize: 16),
                      ),
                    ),
                    const SizedBox(height: 37),
                  ],
                ),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.red),
                    borderRadius: BorderRadius.circular(4),
                    color: AppColors.white,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ErrorInfo.freeeReConnectFinancialInstitutionErrorCode,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontSize: 12,
                                    fontFamily: FontFamily.robotoCondensed,
                                    color: AppColors.grayGreen,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.baseline,
                          textBaseline: TextBaseline.alphabetic,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 3,
                              ), //アイコンとエラーメッセージの高さを揃えた
                              child: SvgPicture.asset(
                                Assets.alertIconHome,
                                width: 16,
                                height: 16,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: SizedBox(
                                child: Text(
                                  ErrorInfo
                                      .freeeReConnectFinancialInstitutionErrorMessage,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        color: AppColors.red,
                                        fontWeight: FontWeight.w400,
                                        height: 1.5,
                                      ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        _FreeeFaqContent(),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 16,
                    left: 32,
                    bottom: 26,
                    right: 32,
                  ),
                  child: SizedBox(
                    width: double.infinity,
                    child: AppRectangleButton(
                      label: AppLocalizations.of(context)!.confirmFreee,
                      textColor: AppColors.white,
                      buttonColor: AppColors.tradGreen,
                      onPressed: () async {
                        // ローディング開始
                        LoadingDialog.loading(context);
                        // 操作ログ送信
                        unawaited(
                          operationLogController.sendOperationLog(
                            functionLog: OperationLogMessage.freeeIntegration,
                            operationLog:
                                OperationLogMessage.freeeReIntegration,
                            resultLog: '',
                            errorIdLog: '',
                          ),
                        );
                        // FirebaseAnalyticsログ送信
                        analyticsLogController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!
                              .confirmFreeeCreditCard,
                          screenName: AppLocalizations.of(context)!.home,
                        );
                        final url = await homeController.getUrlToFreeeSSOPage(
                          freeeRedirectURL,
                          FreeeSSOTypeFromHome.expirationOfFinanceLinkage,
                        );
                        // URL取得時にエラーが発生した場合、後続処理を中断
                        if (ref
                                .read(homeScreenProvider)
                                .getUrlToFreeeSsoParamsError
                                .isNotNull ||
                            url == null) return;
                        if (!context.mounted) return;
                        await urlLauncher.launchExternalUrl(
                          AppLocalizations.of(context)!.confirmFreeeCreditCard,
                          ScreenIdNumber.homeScreenId,
                          url,
                          useEncryptedCookie: true,
                        );
                        if (context.mounted) {
                          // ローディング終了
                          LoadingDialog.loadingEnd(context);
                          if (!context.mounted) return;
                          showFreeeUpdateDialog(
                            context,
                            ref,
                            AppLocalizations.of(context)!.closeAdditionAccount,
                            AppLocalizations.of(context)!.closeAdditionCard,
                            isCreditCard: true,
                          );
                        }
                      },
                      svgPicture: SvgPicture.asset(
                        Assets.actionIconWhite,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: onRefreshButtonPressed,
          child: Container(
            color: AppColors.transparent,
            width: 148,
            height: 54,
          ),
        ),
      ],
    );
  }
}

/// 情報全取得エラー時
class _AllErrorCreditCard extends StatelessWidget {
  const _AllErrorCreditCard({
    this.onRefreshButtonPressed,
    required this.freeeBankAccountError,
  });

  final void Function()? onRefreshButtonPressed;
  final AppError? freeeBankAccountError;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 312,
          height: 218,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.creditCardShadow.withOpacity(0.4),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        SizedBox(
          width: 312,
          height: 218,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                SizedBox(
                  width: 132,
                  height: 24,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 24,
                        height: 24,
                        child: SvgPicture.asset(Assets.refreshIcon),
                      ),
                      const SizedBox(width: 8),
                      NullableHyphenText(
                        data: freeeBankAccountError!.baseDateTime,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                              color: AppColors.textGrayGreen,
                            ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                ErrorBody(
                  errorCode: freeeBankAccountError!.code ?? '',
                  errorMessage: freeeBankAccountError!.message ?? '',
                  hasFaq: freeeBankAccountError!.hasFaq,
                  maxLines: 3,
                  textOverflow: TextOverflow.ellipsis,
                  errorCategory: ErrorCategory.freeeError,
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: onRefreshButtonPressed,
          child: Container(
            color: AppColors.transparent,
            width: 148,
            height: 54,
          ),
        ),
      ],
    );
  }
}

/// 情報部分取得エラー時
class _PartialErrorCreditCard extends StatelessWidget {
  const _PartialErrorCreditCard({
    this.lastSyncedAt,
    required this.displayName,
    required this.freeeCreditCardError,
    this.onRefreshButtonPressed,
  });

  final String? lastSyncedAt;
  final String displayName;
  final AppError freeeCreditCardError;
  final void Function()? onRefreshButtonPressed;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 312,
          height: 311,
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: const BorderRadius.all(
              Radius.circular(8),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.creditCardShadow.withOpacity(0.4),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        SizedBox(
          width: 312,
          height: 311,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    SizedBox(
                      width: 132,
                      height: 24,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: SvgPicture.asset(Assets.refreshIcon),
                          ),
                          const SizedBox(width: 8),
                          NullableHyphenText(
                            data: lastSyncedAt,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.textGrayGreen,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: 280,
                      child: NullableHyphenText(
                        data: displayName.ellipsis(maxLength: 34),
                        style: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(fontSize: 16),
                        maxLines: 2,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: 280,
                      child: NullableHyphenText(
                        // Todo: SP15,16ではクレジットカード種類は表示しない
                        data: '',
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                Column(
                  children: [
                    ErrorBody(
                      errorCode: freeeCreditCardError.code ?? '',
                      errorMessage: freeeCreditCardError.message ?? '',
                      hasFaq: freeeCreditCardError.hasFaq,
                      maxLines: 3,
                      textOverflow: TextOverflow.ellipsis,
                      errorCategory: ErrorCategory.freeeError,
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: onRefreshButtonPressed,
          child: Container(
            color: AppColors.transparent,
            width: 148,
            height: 54,
          ),
        ),
      ],
    );
  }
}

//HACK freee_reconnect_content.dartと同じなので、共通化してしまう
class _FreeeFaqContent extends HookConsumerWidget {
  const _FreeeFaqContent();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseController = ref.read(analyticsLogControllerProvider);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        const SizedBox(width: 20),
        Text(
          AppLocalizations.of(context)!.relatedFAQ,
          style: Theme.of(context).textTheme.bodySmall,
        ),
        GestureDetector(
          onTap: () {
            firebaseController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.faq,
              screenName: AppLocalizations.of(context)!.home,
            );
            ref.read(urlLauncherProvider).launchLinkId(
                  LinkIds.faq,
                  AppLocalizations.of(context)!.faq,
                  ScreenIdNumber.homeScreenId,
                );
          },
          child: Text(
            AppLocalizations.of(context)!.here,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: AppColors.tradGreen,
                  decoration: TextDecoration.underline,
                  decorationColor: AppColors.tradGreen,
                ),
          ),
        ),
        const SizedBox(width: 4),
        SvgPicture.asset(Assets.actionIcon),
      ],
    );
  }
}
