import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/before_login/before_login_screen_notifier.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/relate_information.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/before_login/corporate_bank_account_area.dart';
import 'package:dtp_app/views/screens/before_login/information_link.dart';
import 'package:dtp_app/views/screens/before_login/login_area.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BeforeLoginScreen extends HookConsumerWidget
    with CommonErrorHandlerMixin {
  const BeforeLoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(beforeLoginScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final arguments = ModalRoute.of(context)!.settings.arguments as Map?;
    final fromAccountOpeningSubmitted =
        (arguments?['fromAccountOpeningSubmitted'] as bool?) ?? false;
    // Push通知許可ダイアログを表示する関数
    Future<void> requestMessagingPermission() async {
      if (context.mounted) {
        await controller.requestMessagingPermission();
        // 画面がアクティブになるまで待機する
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    // ATT許可ダイアログを表示する関数
    Future<void> requestTrackingAuthorization() async {
      if (context.mounted) {
        await controller.requestTrackingAuthorization();
        // 画面がアクティブになるまで待機する
        await Future.delayed(const Duration(seconds: 1));
      }
    }

    // Firebase Screenログ・KARTE Viewイベントを送信する関数
    void sendScreenLog() {
      if (context.mounted) {
        analyticsLogController.sendScreenLog(
          screenNumber: ScreenIdNumber.beforeLoginScreenId,
          screenName: AppLocalizations.of(context)!.beforeLogin,
        );
      }
    }

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          if (await controller.isFirstTimeStartup()) {
            // 初回起動時（初回ログインではない）
            await requestMessagingPermission();
            await requestTrackingAuthorization();
            await controller.firstTimeStartupDone();
          } else {
            final isLoggedin = await controller.isFirstLogin();
            if (!isLoggedin || fromAccountOpeningSubmitted) {
              // await処理を実施することで、Push後にこちらの画面に戻るまでsplashの終了処理が実行されなくなる
              // その為、こちらの処理は実行結果を待たない。
              if (!context.mounted) return;
              unawaited(
                Navigator.of(context).pushNamed(RouteNames.login),
              );
            }
          }
          // Firebase Screenログ・KARTE Viewイベント送信
          sendScreenLog();
          // スプラッシュ画面を終了させる
          FlutterNativeSplash.remove();
        });
        return null;
      },
      const [],
    );

    return SafeArea(
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    Assets.bgWaveBeforeLogin,
                  ),
                  fit: BoxFit.cover,
                ),
              ),
              child: Column(
                children: [
                  ScreenNumber(
                    screenNumber: ScreenIdNumber.beforeLoginScreenId,
                    color: AppColors.white,
                  ),
                  const SizedBox(height: 16),
                  CorporateBankAccountArea(),
                  LoginArea(),
                ],
              ),
            ),
            RelatedInformation(
              // 関連情報下のコンテンツ
              underBannerContents: const [
                SizedBox(height: 48),
                InformationLinkWidget(),
                SizedBox(height: 80),
              ],
              screenId: ScreenIdNumber.beforeLoginScreenId,
              screenName: AppLocalizations.of(context)!.beforeLogin,
            ),
          ],
        ),
      ),
    );
  }
}
