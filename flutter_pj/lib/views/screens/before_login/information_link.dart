import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class InformationLinkWidget extends HookConsumerWidget {
  const InformationLinkWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    return Padding(
      padding: const EdgeInsets.only(left: 16),
      child: Row(
        children: [
          RichText(
            text: TextSpan(
              text: AppLocalizations.of(context)!.useCookie,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontSize: 16,
                    color: AppColors.primary,
                    height: 1.5,
                  ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.useCookie,
                    screenName: AppLocalizations.of(context)!.beforeLogin,
                  );
                  ref.read(urlLauncherProvider).launchLinkId(
                        LinkIds.useCookie,
                        AppLocalizations.of(context)!.useCookie,
                        ScreenIdNumber.beforeLoginScreenId,
                      );
                },
            ),
          ),
          const SizedBox(width: 4),
          SvgPicture.asset(
            Assets.cardLinkIcon,
            width: 16,
            height: 16,
          ),
        ],
      ),
    );
  }
}
