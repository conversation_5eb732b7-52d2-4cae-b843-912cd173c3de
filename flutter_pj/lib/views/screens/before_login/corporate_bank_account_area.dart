import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/before_login/before_login_screen_notifier.dart';
import 'package:dtp_app/business_logics/business_blockage/business_blockage_controller.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CorporateBankAccountArea extends StatelessWidget {
  const CorporateBankAccountArea({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.symmetric(vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.cardShadow,
            spreadRadius: 0,
            blurRadius: 4,
            offset: Offset(0, 0),
          ),
          BoxShadow(
            color: AppColors.cardShadow,
            spreadRadius: 0,
            blurRadius: 20,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: const Column(
        children: [
          _CorporateBankAccountMessage(),
          SizedBox(height: 12),
          _IdentificationButton(),
          SizedBox(height: 16),
          _CorporateBankAccountDetailMessage(),
        ],
      ),
    );
  }
}

class _CorporateBankAccountMessage extends HookConsumerWidget {
  const _CorporateBankAccountMessage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Text(
      AppLocalizations.of(context)!.corporateBankAccountMessage,
      style: Theme.of(context).textTheme.bodySmall!.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.black,
            height: 1.26,
          ),
    );
  }
}

class _IdentificationButton extends HookConsumerWidget {
  const _IdentificationButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(beforeLoginScreenProvider.notifier);
    final businessBlockageController =
        ref.watch(businessBlockageControllerProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    return SizedBox(
      width: 240,
      height: 48,
      child: ElevatedButton(
        onPressed: () async {
          // ローディング開始
          LoadingDialog.loading(context);

          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName:
                AppLocalizations.of(context)!.identificationOpeningAcount,
            screenName: AppLocalizations.of(context)!.beforeLogin,
          );

          // 閉塞チェック実行
          final isBlocked = await controller.getBusinessBlockageStatus();

          // 非同期処理中にcontextがアンマウントされていないことを確認
          if (!context.mounted) return;

          final state = ref.read(beforeLoginScreenProvider);

          // ローディング終了
          LoadingDialog.loadingEnd(context);

          // 正常系の画面遷移
          if (!isBlocked && state.error == null) {
            if (!context.mounted) return;
            await Navigator.pushNamed(context, RouteNames.jpkiTerms);
          } else {
            if (!context.mounted) return;
            // 閉塞中の場合はダイアログを表示
            showCommonErrorDialog(
              dialogContext: context,
              errorPattern: ErrorPattern.errorPattern13,
              onPressed1: () {
                onPressed1(
                  context,
                  ref,
                  ErrorPattern.errorPattern13,
                  AppLocalizations.of(context)!.beforeLogin,
                );
                businessBlockageController.clearBlockageStatus();
              },
              onPressed2: () {},
            );
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.tradGreen,
          elevation: 0,
        ),
        child: Text(
          AppLocalizations.of(context)!.identificationOpeningAcount,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
      ),
    );
  }
}

class _CorporateBankAccountDetailMessage extends HookConsumerWidget {
  const _CorporateBankAccountDetailMessage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppLocalizations.of(context)!.corporateBankAccountDetailMessage,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(height: 1.5),
        ),
        GestureDetector(
          onTap: () {
            logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.here,
              screenName: AppLocalizations.of(context)!.beforeLogin,
            );
            ref.read(urlLauncherProvider).launchLinkId(
                  LinkIds.hojinSpecialKouza,
                  AppLocalizations.of(context)!.here,
                  ScreenIdNumber.beforeLoginScreenId,
                );
          },
          child: Text(
            AppLocalizations.of(context)!.here,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: AppColors.tradGreen,
                  decoration: TextDecoration.underline,
                  height: 1.5,
                ),
          ),
        ),
        SizedBox(width: 4),
        SvgPicture.asset(
          Assets.cardLinkIcon,
          width: 16,
          height: 16,
        ),
      ],
    );
  }
}
