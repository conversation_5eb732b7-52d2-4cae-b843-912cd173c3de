import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class LoginArea extends StatelessWidget {
  const LoginArea({super.key});

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: Container(
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 48, top: 8),
        padding: const EdgeInsets.only(bottom: 16),
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.all(
            Radius.circular(8),
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.cardShadow,
              spreadRadius: 0,
              blurRadius: 4,
              offset: Offset(0, 0),
            ),
            BoxShadow(
              color: AppColors.cardShadow,
              spreadRadius: 0,
              blurRadius: 20,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: AppColors.lightGreen,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(8),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(height: 16, width: double.infinity),
                  _SmbcBusinessText(),
                  SizedBox(height: 4),
                  _SmbcBusinessInfoMessage(),
                  SizedBox(height: 8),
                  SizedBox(
                    width: 160,
                    height: 90,
                    child: Image.asset(
                      Assets.smbcBusinessCard,
                      fit: BoxFit.cover,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            _LoginButton(),
            SizedBox(height: 16),
            _PrivacyPolicyMessage(),
          ],
        ),
      ),
    );
  }
}

class _SmbcBusinessText extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Text(
      AppLocalizations.of(context)!.smbcBusiness,
      style: TextStyle(
        fontFamily: FontFamily.robotoCondensed,
        color: AppColors.primary,
        fontWeight: FontWeight.w700,
        fontSize: 20.0,
        height: 1.5,
      ),
    );
  }
}

class _SmbcBusinessInfoMessage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Text(
      AppLocalizations.of(context)!.smbcBusinessInfoMessage,
      style: Theme.of(context).textTheme.bodySmall!.copyWith(
            fontSize: 12,
            height: 1.5,
          ),
      textAlign: TextAlign.center,
    );
  }
}

class _LoginButton extends HookConsumerWidget {
  const _LoginButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return SizedBox(
      width: 240,
      height: 48,
      child: ElevatedButton(
        onPressed: () {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.navigateToLoginScreen,
            screenName: AppLocalizations.of(context)!.beforeLogin,
          );
          Navigator.of(context).pushNamed(RouteNames.login);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.white,
          elevation: 0,
          side: const BorderSide(color: AppColors.loginScreenButton),
        ),
        child: Text(
          AppLocalizations.of(context)!.navigateToLoginScreen,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: AppColors.loginScreenButton,
                fontWeight: FontWeight.w700,
                height: 1.1,
              ),
        ),
      ),
    );
  }
}

class _PrivacyPolicyMessage extends HookConsumerWidget {
  const _PrivacyPolicyMessage();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final linkTextStyle = Theme.of(context).textTheme.bodySmall!.copyWith(
          fontSize: 12,
          color: AppColors.tradGreen,
          decoration: TextDecoration.underline,
          height: 1.5,
        );

    // 個人情報の取り扱いを外部ブラウザで開く
    void launchPersonalInformationPolicy() {
      ref.read(analyticsLogControllerProvider).sendButtonLog(
            buttonName: AppLocalizations.of(context)!.aboutPersonalInformation,
            screenName: AppLocalizations.of(context)!.beforeLogin,
          );
      ref.read(urlLauncherProvider).launchLinkId(
            LinkIds.personalInformationPolicy,
            AppLocalizations.of(context)!.aboutPersonalInformation,
            ScreenIdNumber.beforeLoginScreenId,
          );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.noteSymbol,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 12,
                height: 1.5,
              ),
        ),
        SizedBox(width: 4),
        RichText(
          text: TextSpan(
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  fontSize: 12,
                  height: 1.5,
                ),
            children: [
              TextSpan(
                text: AppLocalizations.of(context)!.privacyPolicyMessage1,
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.privacyPolicyMessage2,
                style: linkTextStyle,
                recognizer: TapGestureRecognizer()
                  ..onTap = launchPersonalInformationPolicy,
              ),
              WidgetSpan(
                child: SvgPicture.asset(
                  Assets.cardLinkIcon,
                  width: 16,
                  height: 16,
                ),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.privacyPolicyMessage3,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
