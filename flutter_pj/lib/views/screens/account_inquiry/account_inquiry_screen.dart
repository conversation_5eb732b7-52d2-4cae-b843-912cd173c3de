import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/screen_title_header.dart';
import 'package:dtp_app/views/components/web_view/customized/freee_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/account_inquiry/item/bank_account_info_item.dart';
import 'package:dtp_app/views/screens/account_inquiry/widget/account_inquiry_error_body.dart';
import 'package:dtp_app/views/screens/account_inquiry/widget/balance_details_modal.dart';
import 'package:dtp_app/views/screens/account_inquiry/widget/freee_reconnect_content.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:dtp_app/views/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AccountInquiryScreen extends HookConsumerWidget {
  const AccountInquiryScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    final controller = ref.watch(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.accountInquiryScreenId,
            screenName: AppLocalizations.of(context)!.transactionAccountInquiry,
          );
          // 初期化処理の終了を待たずに後続処理を実施
          unawaited(
            ref
                .read(homeScreenProvider.notifier)
                .loadBankAccountsForInquiryScreen(),
          );
        });
        return null;
      },
      const [],
    );

    // freee連携エラー時はfreee連携未完了ダイアログを表示する
    if (state.freeeTokenStatus == FreeeTokenStatus.error) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // FreeeTokenStatusをエラーから処理不要に
        controller.updateFreeeTokenStatusToNoActionRequired();
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return CommonDialog.ok(
              title: AppLocalizations.of(context)!.linkageNotCompletedMessage,
              // 異例ケースで\nを使用 (カード幅固定＆エラーコードで折り返しされないため)
              message:
                  '${HomeErrorInfo.freeeTokenStatusErrorCode} \n${AppLocalizations.of(context)!.checkFreeeSettingsMessage}',
              okButtonText: AppLocalizations.of(context)!.close,
              isPopAfterOkButtonPress: true,
              onOkPressed: () {
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.freeeLinkClose,
                  screenName: AppLocalizations.of(context)!.accountInquiry,
                );
              },
            );
          },
        );
      });
    }

    return Container(
      color: theme.colorScheme.surface,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: Container(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              decoration: BoxDecoration(
                image: DecorationImage(
                  alignment: Alignment.bottomCenter,
                  image: AssetImage(
                    Assets.bgWaveGrey,
                  ),
                  fit: BoxFit.fitWidth,
                ),
              ),
              child: Column(
                children: [
                  ScreenTitleHeader(
                    title:
                        AppLocalizations.of(context)!.transactionAccountInquiry,
                    assetName: Assets.informationIcon2,
                  ),
                  Container(
                    color: AppColors.white,
                    child: ScreenNumber(
                      screenNumber: ScreenIdNumber.accountInquiryScreenId,
                    ),
                  ),
                  const _TotalAccountBalance(),
                  const _AccountInfoList(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class _TotalAccountBalance extends HookConsumerWidget {
  const _TotalAccountBalance();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    const totalBalanceMaxDigit = 15;
    final isValidTotalDigit =
        state.totalBalance.toString().length <= totalBalanceMaxDigit;
    final hasUserAccountError = state.userAccountError != null;
    final hasBankAccountError = state.bankAccounts.firstError != null;
    final hasSmbcError = hasUserAccountError || hasBankAccountError;
    String getDateTime() {
      if (hasSmbcError) {
        return hasUserAccountError
            // エラー時は発生しているエラーの日時を取得
            ? state.userAccountError!.baseDateTime
            : state.bankAccounts.firstError!.baseDateTime;
      }
      // エラー発生していない場合は日付を取得
      return state.serverDate;
    }

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 10, 16, 24),
      width: double.infinity,
      color: AppColors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //照会日時
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                AppLocalizations.of(context)!.fetchedDateTime,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.textGrayGreen,
                    ),
              ),
              const SizedBox(width: 8),
              Row(
                children: [
                  NullableHyphenText(
                    data: getDateTime(),
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppColors.textGrayGreen,
                          fontFamily: FontFamily.robotoCondensed,
                        ),
                  ),
                  const SizedBox(width: 4),
                  hasSmbcError
                      ? const SizedBox.shrink()
                      : NullableHyphenText(
                          data: state.smbcBaseTime,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    color: AppColors.textGrayGreen,
                                    fontFamily: FontFamily.robotoCondensed,
                                  ),
                        ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 12),
          //口座残高合計
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SvgPicture.asset(Assets.accountTotalIcon),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.totalAccountBalance,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    const SizedBox(height: 10),
                    isValidTotalDigit
                        ? const _ValidTotalDigit()
                        : Text(
                            AppLocalizations.of(context)!
                                .excessBalanceErrorMessage,
                            style: const TextStyle(
                              color: AppColors.tradGreen,
                              fontSize: 24,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Divider(
            height: 0,
            thickness: 1,
            color: AppColors.borderGrayGreen,
          ),
          const SizedBox(height: 16),
          Column(
            children: [
              // 他店内手形
              CommonAccountInquiryText(
                text: AppLocalizations.of(context)!
                    .checksIssuedByOtherBanksAmount,
                amountOfMoney: state.totalChecksIssuedByOtherBanksAmount,
                balanceMaxDigit: 14,
                allowsNegativeValue: false,
                // マイナスを許容しない
                isLoading: state.isLoading,
              ),
              // 貸越限度額
              CommonAccountInquiryText(
                text: AppLocalizations.of(context)!.overdraftLimit,
                amountOfMoney: state.totalOverdraftLimit,
                balanceMaxDigit: 14,
                allowsNegativeValue: false,
                // マイナスを許容しない
                isLoading: state.isLoading,
              ),
              // 支払可能残高
              CommonAccountInquiryText(
                text: AppLocalizations.of(context)!.withdrawableBalance,
                amountOfMoney: state.totalWithdrawableBalance,
                balanceMaxDigit: 15,
                isLoading: state.isLoading,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 合計金額を表示するView
class _ValidTotalDigit extends HookConsumerWidget {
  const _ValidTotalDigit();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    const fontSizeChangeDigit = 11;
    final isTotalBalanceNull = state.totalBalance == null;
    final isFontSizeChangeDigit =
        state.totalBalance.toString().length <= fontSizeChangeDigit;

    // null もしくは ロード中の際にはハイフン表示
    return isTotalBalanceNull || state.isLoading
        ? Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                AppLocalizations.of(context)!.fullWidthHyphen,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      fontSize: 36,
                    ),
              ),
              const SizedBox(width: 4.0),
              Column(
                children: [
                  const SizedBox(height: 10),
                  Text(
                    AppLocalizations.of(context)!.yenJp,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                  ),
                ],
              ),
            ],
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                state.totalBalance!.withCommasAndMinusSign,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontSize: isFontSizeChangeDigit ? 36 : 27,
                      fontFamily: FontFamily.robotoCondensed,
                    ),
              ),
              const SizedBox(width: 4.0),
              Text(
                AppLocalizations.of(context)!.yenJp,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
              ),
            ],
          );
  }
}

/// 口座情報一覧
class _AccountInfoList extends HookConsumerWidget with CommonErrorHandlerMixin {
  const _AccountInfoList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeScreenProvider);
    ref.watch(homeNavigationScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final hasUserAccountError = state.userAccountError != null;
    final hasAccountSettingsError = state.accountSettingsError != null;
    final items = <BankAccountInfoItem>[];
    final accounts = state.bankAccounts;
    final freeeBankAccounts = state.otherBankAccounts;
    final isAccountsEmpty = (hasUserAccountError || hasAccountSettingsError) &&
        freeeBankAccounts.isEmpty;
    final visibleBankAccount = accounts.onlyVisible();
    final visibleFreeeBankAccount =
        freeeBankAccounts.onlyVisible().onlyHasBalanceDetail();
    // エラーを検知した際は、エラーダイアログを表示する
    // 取引口座照会画面は画面に情報を表示するため、 FAQダイアログは表示しないようフラグを持たせる
    for (final error in accounts.errorList) {
      handleError(
        context,
        ref,
        error,
        onOkTap: () {},
        screenId: ScreenIdNumber.accountInquiryScreenId,
        screenName: AppLocalizations.of(context)!.accountInquiry,
      );
    }

    if (state.userAccountError != null) {
      //　利用者情報取得APIのエラーアイテムを追加
      items.add(
        BankAccountInfoItem.smbcError(error: state.userAccountError!),
      );
    }

    if (state.accountSettingsError != null) {
      // 口座表示設定取得エラー時のアイテムを追加
      items.add(
        BankAccountInfoItem.smbcError(error: state.accountSettingsError!),
      );
    }

    for (final bankAccount in visibleBankAccount) {
      // 指定口座残高取得APIのアイテムを追加
      if (bankAccount is BankAccountWithBalanceDetail) {
        items.add(
          BankAccountInfoItem.smbc(bankAccount: bankAccount),
        );
      }

      // 指定口座残高取得APIのエラーアイテムを追加
      if (bankAccount is BankAccountWithError) {
        items.add(
          BankAccountInfoItem.smbcBankAccountError(bankAccount: bankAccount),
        );
      }
    }

    if (state.freeeBankAccountError != null) {
      // freee口座一覧取得APIのエラーアイテムを追加
      items.add(
        BankAccountInfoItem.freeeError(error: state.freeeBankAccountError!),
      );
    }

    for (final freeeBankAccount in visibleFreeeBankAccount) {
      // freeeエラー時、ダミーの口座情報を入れているため、早期リターンさせる
      if (state.freeeBankAccountError != null) break;

      // freeeと金融機関の連携期限切れかを判定
      final isFreeeReConnect =
          freeeBankAccount.balanceDetail.isFreeeReConnectFinancialInstitution;
      final freeeItem = isFreeeReConnect
          ? BankAccountInfoItem.freeeReconnect(
              bankAccount: freeeBankAccount,
              hasFaq: state.freeeBankAccountError?.hasFaq ?? false,
            )
          : BankAccountInfoItem.freee(
              bankAccount: freeeBankAccount,
            );

      items.add(freeeItem);
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 40, 16, 76),
      child: Column(
        children: [
          isAccountsEmpty || state.isLoading
              ? const IntrinsicHeight(
                  child: SizedBox.expand(),
                )
              : //表示件数
              Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Text(
                        '${visibleBankAccount.length + visibleFreeeBankAccount.length}',
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                              fontFamily: FontFamily.robotoCondensed,
                            ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        AppLocalizations.of(context)!.itemCountUnit,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                      ),
                    ],
                  ),
                ),
          state.isLoading
              ? const CircularProgressIndicator() // TODO ローディング処理追加 (DTPO-5809で対応予定)
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    return items[index].map(
                      smbc: (item) => _AccountInfoItem(
                        item.bankAccount,
                        () {
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName: AppLocalizations.of(context)!
                                .incomeAndExpenditureInquiry,
                            screenName: AppLocalizations.of(context)!
                                .transactionAccountInquiry,
                          );

                          // 入出金明細ボタンをタップした時の処理
                          // 入出金明細画面に遷移
                          _navigateToIncomeAndExpenditure(
                            context,
                            item.bankAccount,
                            state.serverDate,
                          );
                        },
                      ).animate().fadeIn(),
                      smbcError: (item) =>
                          _ErrorAccountInfoItem(error: item.error),
                      smbcBankAccountError: (item) =>
                          _AccountInfoItemWithError(item.bankAccount),
                      freee: (item) => _AccountInfoFreeeItem(
                        item.bankAccount,
                        () {
                          // FirebaseAnalyticsログ送信
                          analyticsLogController.sendButtonLog(
                            buttonName: AppLocalizations.of(context)!
                                .incomeAndExpenditure,
                            screenName: AppLocalizations.of(context)!
                                .transactionAccountInquiry,
                          );

                          // 入出金明細ボタンをタップした時の処理
                          // 入出金明細画面に遷移
                          _navigateToIncomeAndExpenditure(
                            context,
                            item.bankAccount,
                            item.bankAccount.balanceDetail.baseDateTime ?? '',
                          );
                        },
                      ).animate().fadeIn(),
                      freeeError: (item) =>
                          _FreeeErrorAccountInfoItem(error: item.error),
                      freeeReconnect: (item) => AccountInfoReConnectFreeeItem(
                        bankAccount: item.bankAccount,
                        hasFaq: item.hasFaq,
                      ).animate().fadeIn(),
                    );
                  },
                ),
        ],
      ),
    );
  }

  /// 入出金明細画面に遷移する処理
  Future<void> _navigateToIncomeAndExpenditure(
    BuildContext context,
    BankAccountWithBalanceDetail bankAccount,
    String serverDate,
  ) =>
      // 入出金明細画面に遷移
      Navigator.of(context).pushNamed(
        RouteNames.incomeAndExpenditure,
        arguments: {
          'bankAccount': bankAccount,
          'serverDate': serverDate,
        },
      );
}

/// 自行口座
class _AccountInfoItem extends HookConsumerWidget {
  const _AccountInfoItem(
    this.bankAccount,
    this.onDetailButtonPressed,
  );

  final BankAccountWithBalanceDetail bankAccount;
  final void Function() onDetailButtonPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isCurrentBalanceNull =
        bankAccount.balanceDetail.currentBalance == null;

    // 口座表示名
    final displayName = bankAccount.displayConfig.displayName;
    final displayNameTextStyle =
        Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontWeight: FontWeight.w700,
            );

    // 口座表示名の表示幅
    // 画面サイズからパディングとアイコンサイズを引いて算出
    // @HACK: マジックナンバーとなってしまっている為、オーバーフローを描画前に検知できる仕組みがあると良い
    final displayNameAreaWidth = MediaQuery.of(context).size.width - 138;

    // 1行に収まる口座表示名の最大文字数を計算
    final displayNameMaxLength = displayName.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // 口座表示名がオーバーフローしているかを判定
    final isOverFlowed = displayName.length > displayNameMaxLength;

    // アコーディオンを開いているかのフラグ
    final isAccordionExpanded = useState(false);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 1),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 22,
                          width: 62,
                          child: Image.asset(
                            Assets.smbcLogoTagS,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 表示名
                        Expanded(
                          child: SizedBox(
                            // オーバーフローかつアコーディオンを開いていない時だけ「...」表示
                            child: Text(
                              displayName,
                              overflow:
                                  !isOverFlowed || isAccordionExpanded.value
                                      ? null
                                      : TextOverflow.ellipsis,
                              style: displayNameTextStyle,
                            ),
                          ),
                        ),
                        isOverFlowed
                            ? const SizedBox(width: 8)
                            : const SizedBox.shrink(),
                        isOverFlowed
                            ? GestureDetector(
                                onTap: () {
                                  // FirebaseAnalyticsログ送信
                                  analyticsLogController.sendButtonLog(
                                    buttonName: AppLocalizations.of(context)!
                                        .switchAccordion,
                                    screenName: AppLocalizations.of(context)!
                                        .transactionAccountInquiry,
                                  );
                                  isAccordionExpanded.value =
                                      !isAccordionExpanded.value;
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 6),
                                  child: SvgPicture.asset(
                                    isAccordionExpanded.value
                                        ? Assets.arrowUpIcon
                                        : Assets.arrowDownIcon,
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
                  //銀行名、支店名、科目、口座番号、口座名、振込依頼人名
                  DefaultTextStyle(
                    style: Theme.of(context).textTheme.bodySmall!,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NullableHyphenText(
                            data: bankAccount.baseInfo.bankName,
                          ),
                          const SizedBox(height: 4),
                          NullableHyphenText(
                            data: bankAccount.baseInfo.branchNameKanji,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              NullableHyphenText(
                                data: bankAccount.baseInfo.accountType,
                              ),
                              const SizedBox(width: 8),
                              NullableHyphenText(
                                data: bankAccount.baseInfo.accountNumber,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                    ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          NullableHyphenText(
                            data: bankAccount.baseInfo.remitterName,
                          ),
                        ],
                      ),
                    ),
                  ),
                  //残高
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.balance,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.textGrayGreen,
                            ),
                      ),
                      isCurrentBalanceNull
                          ? Text(
                              AppLocalizations.of(context)!.fullWidthHyphen,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge!
                                  .copyWith(
                                    fontFamily: FontFamily.robotoCondensed,
                                  ),
                            )
                          : Row(
                              crossAxisAlignment: CrossAxisAlignment.baseline,
                              textBaseline: TextBaseline.alphabetic,
                              children: [
                                Text(
                                  bankAccount.balanceDetail.currentBalance! >= 0
                                      ? bankAccount.balanceDetail
                                          .currentBalance!.withCommas
                                      : '-${bankAccount.balanceDetail.currentBalance!.withCommas}',
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleLarge!
                                      .copyWith(
                                        fontSize: 28,
                                        fontFamily: FontFamily.robotoCondensed,
                                      ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  AppLocalizations.of(context)!.yenJp,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        fontWeight: FontWeight.w700,
                                      ),
                                ),
                              ],
                            ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: double.infinity,
            height: 72,
            decoration: const BoxDecoration(
              color: AppColors.lightGreen1,
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: AppRectangleButton(
                      label: AppLocalizations.of(context)!.balanceDetails,
                      textColor: AppColors.tradGreen,
                      buttonColor: AppColors.white,
                      borderColor: AppColors.tradGreen,
                      onPressed: () {
                        // FirebaseAnalyticsログ送信
                        analyticsLogController.sendButtonLog(
                          buttonName:
                              AppLocalizations.of(context)!.balanceDetails,
                          screenName: AppLocalizations.of(context)!
                              .transactionAccountInquiry,
                        );

                        //残高詳細画面に遷移
                        showModalBottomSheet(
                          isScrollControlled: true,
                          enableDrag: false,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(8.0),
                            ),
                          ),
                          context: context,
                          builder: (BuildContext context) {
                            return BalanceDetailsModal(
                              bankAccount: bankAccount,
                            );
                          },
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 7),
                  Expanded(
                    child: AppRectangleButton(
                      label:
                          AppLocalizations.of(context)!.statementDetailsInquiry,
                      textColor: AppColors.white,
                      buttonColor: AppColors.tradGreen,
                      onPressed: onDetailButtonPressed,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 指定口座残高取得APIエラー
class _AccountInfoItemWithError extends HookConsumerWidget {
  const _AccountInfoItemWithError(this.bankAccount);

  final BankAccountWithError bankAccount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // 口座表示名
    final displayName = bankAccount.displayConfig.displayName;
    final displayNameTextStyle =
        Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontWeight: FontWeight.w700,
            );

    // 口座表示名の表示幅
    // 画面サイズからパディングとアイコンサイズを引いて算出
    // @HACK: マジックナンバーとなってしまっている為、オーバーフローを描画前に検知できる仕組みがあると良い
    final displayNameAreaWidth = MediaQuery.of(context).size.width - 138;

    // 1行に収まる口座表示名の最大文字数を計算
    final displayNameMaxLength = displayName.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // 口座表示名がオーバーフローしているかを判定
    final isOverFlowed = displayName.length > displayNameMaxLength;

    // アコーディオンを開いているかのフラグ
    final isAccordionExpanded = useState(false);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 1),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // SMBCロゴ
                        SizedBox(
                          height: 22,
                          width: 62,
                          child: Image.asset(
                            Assets.smbcLogoTagS,
                            fit: BoxFit.cover,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 表示名
                        Expanded(
                          child: SizedBox(
                            // オーバーフローかつアコーディオンを開いていない時だけ「...」表示
                            child: Text(
                              bankAccount.displayConfig.displayName,
                              overflow:
                                  !isOverFlowed || isAccordionExpanded.value
                                      ? null
                                      : TextOverflow.ellipsis,
                              style: displayNameTextStyle,
                            ),
                          ),
                        ),
                        isOverFlowed
                            ? const SizedBox(width: 8)
                            : const SizedBox.shrink(),
                        isOverFlowed
                            ? GestureDetector(
                                onTap: () {
                                  // FirebaseAnalyticsログ送信
                                  analyticsLogController.sendButtonLog(
                                    buttonName: AppLocalizations.of(context)!
                                        .switchAccordion,
                                    screenName: AppLocalizations.of(context)!
                                        .transactionAccountInquiry,
                                  );
                                  isAccordionExpanded.value =
                                      !isAccordionExpanded.value;
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 6),
                                  child: SvgPicture.asset(
                                    isAccordionExpanded.value
                                        ? Assets.arrowUpIcon
                                        : Assets.arrowDownIcon,
                                  ),
                                ),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
                  //銀行名、支店名、科目、口座番号、口座名
                  DefaultTextStyle(
                    style: Theme.of(context).textTheme.bodySmall!,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NullableHyphenText(
                            data: bankAccount.baseInfo.bankName,
                          ),
                          const SizedBox(height: 4),
                          NullableHyphenText(
                            data: bankAccount.baseInfo.branchNameKanji,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.baseline,
                            textBaseline: TextBaseline.alphabetic,
                            children: [
                              NullableHyphenText(
                                data: bankAccount.baseInfo.accountType,
                              ),
                              const SizedBox(width: 8),
                              NullableHyphenText(
                                data: bankAccount.baseInfo.accountNumber,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                    ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          NullableHyphenText(
                            data: bankAccount.baseInfo.remitterName,
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 指定口座残高取得APIエラーの場合のエラーメッセージを表示
                  Center(
                    child: AccountInquiryErrorBody(
                      errorCode: (bankAccount).error.code ?? '',
                      errorMessage: (bankAccount).error.message ?? '',
                      hasFaq: (bankAccount).error.hasFaq,
                      maxLines: 3,
                      textOverflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: double.infinity,
            height: 72,
            decoration: const BoxDecoration(
              color: AppColors.lightGreen1,
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Expanded(
                    child: AppRectangleButton(
                      label: AppLocalizations.of(context)!.balanceDetails,
                      textColor: AppColors.tradGreen,
                      buttonColor: AppColors.white,
                      borderColor: AppColors.tradGreen,
                      onPressed: () {
                        // FirebaseAnalyticsログ送信
                        analyticsLogController.sendButtonLog(
                          buttonName:
                              AppLocalizations.of(context)!.balanceDetails,
                          screenName: AppLocalizations.of(context)!
                              .transactionAccountInquiry,
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 7),
                  Expanded(
                    child: AppRectangleButton(
                      label:
                          AppLocalizations.of(context)!.statementDetailsInquiry,
                      textColor: AppColors.white,
                      buttonColor: AppColors.tradGreen,
                      onPressed: () {
                        // FirebaseAnalyticsログ送信
                        analyticsLogController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!
                              .statementDetailsInquiry,
                          screenName: AppLocalizations.of(context)!
                              .transactionAccountInquiry,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CommonAccountInquiryText extends StatelessWidget {
  const CommonAccountInquiryText({
    super.key,
    required this.text,
    required this.amountOfMoney,
    required this.balanceMaxDigit,
    this.allowsNegativeValue = true,
    required this.isLoading,
  });

  final String text;
  final int? amountOfMoney;
  final int balanceMaxDigit;
  final bool allowsNegativeValue;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
              ),
        ),
        _Digit(
          amountOfMoney: amountOfMoney,
          balanceMaxDigit: balanceMaxDigit,
          allowsNegativeValue: allowsNegativeValue,
          isLoading: isLoading,
        ),
      ],
    );
  }
}

/// 数値を表示するためのView
/// 数値のバリデーション結果によって表示するテキストを判定
class _Digit extends StatelessWidget {
  const _Digit({
    required this.amountOfMoney,
    required this.balanceMaxDigit,
    required this.allowsNegativeValue,
    required this.isLoading,
  });

  final int? amountOfMoney;
  final int balanceMaxDigit;
  final bool allowsNegativeValue;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    if (amountOfMoney == null ||
        isLoading ||
        !allowsNegativeValue && amountOfMoney!.isNegative) {
      // null, ロード中、または負の値が許可されない箇所で負の値だった場合は、ハイフンを表示
      return const _InvalidValueText();
    }
    if (amountOfMoney.toString().length > balanceMaxDigit) {
      // 桁数超過時はエラーを表示
      return const _OverflowValueDigit();
    }
    // 上記以外であれば数値を表示
    return _ValidValueText(amountOfMoney: amountOfMoney!);
  }
}

/// 桁超過時はエラーメッセージを表示
class _OverflowValueDigit extends StatelessWidget {
  const _OverflowValueDigit();

  @override
  Widget build(BuildContext context) {
    return Text(
      AppLocalizations.of(context)!.excessBalanceErrorMessage,
      style: const TextStyle(
        color: AppColors.tradGreen,
        fontSize: 24,
        fontWeight: FontWeight.w700,
        height: 1.5,
      ),
    );
  }
}

/// 値がnull、もしくは不正な値だった場合はハイフンを表示
class _InvalidValueText extends StatelessWidget {
  const _InvalidValueText();

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          AppLocalizations.of(context)!.fullWidthHyphen,
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
                fontFamily: FontFamily.robotoCondensed,
              ),
        ),
        const SizedBox(width: 4),
        Column(
          children: [
            const SizedBox(height: 5),
            Text(
              AppLocalizations.of(context)!.yenJp,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontWeight: FontWeight.w700,
                    height: 1.5,
                  ),
            ),
          ],
        ),
      ],
    );
  }
}

/// 数値を表示（負の値だった場合はマイナス記号をつける）
class _ValidValueText extends StatelessWidget {
  const _ValidValueText({
    required this.amountOfMoney,
  });

  final int amountOfMoney;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          amountOfMoney.withCommasAndMinusSign,
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
                fontSize: 24,
                fontFamily: FontFamily.robotoCondensed,
                height: 1.5,
              ),
        ),
        const SizedBox(width: 4.0),
        Text(
          AppLocalizations.of(context)!.yenJp,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
      ],
    );
  }
}

/// 取引口座照会全取得エラー時のエラーハンドリング
class _ErrorAccountInfoItem extends StatelessWidget {
  const _ErrorAccountInfoItem({
    required this.error,
  });

  final AppError error;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 40),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.red),
          borderRadius: BorderRadius.circular(4),
          color: AppColors.white,
        ),
        child: SizedBox(
          height: 181,
          width: 343,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  error.code ?? '',
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                        color: AppColors.grayGreen,
                      ),
                ),
                Container(
                  padding: const EdgeInsets.only(top: 16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SvgPicture.asset(Assets.alertIcon),
                      const SizedBox(width: 8),
                      SizedBox(
                        height: 81,
                        width: 275,
                        child: Text(
                          error.message ?? '',
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: AppColors.red,
                                    fontWeight: FontWeight.w700,
                                  ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Visibility(
                  visible: error.hasFaq,
                  child: FaqContent(
                    linkId: LinkIds.faqTop,
                    buttonName: AppLocalizations.of(context)!.bankAccountFAQ,
                    screenId: ScreenIdNumber.accountInquiryScreenId,
                    screenName:
                        AppLocalizations.of(context)!.transactionAccountInquiry,
                    icon: Assets.actionIcon,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// freee口座情報
/// 他行口座
class _AccountInfoFreeeItem extends HookConsumerWidget {
  const _AccountInfoFreeeItem(
    this.bankAccount,
    this.onDetailButtonPressed,
  );

  final BankAccountWithBalanceDetail bankAccount;
  final void Function() onDetailButtonPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // 口座表示名
    final displayName = bankAccount.displayConfig.displayName;
    final displayNameTextStyle = Theme.of(context)
        .textTheme
        .bodyMedium!
        .copyWith(fontWeight: FontWeight.w700);

    // 口座表示名の表示幅
    // 画面サイズからパディングとアイコンサイズを引いて算出
    final displayNameAreaWidth = MediaQuery.of(context).size.width - 88;

    // 1行に収まる口座表示名の最大文字数を計算
    final displayNameMaxLength = displayName.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // 口座表示名がオーバーフローしているかを判定
    final isOverFlowed = displayName.length > displayNameMaxLength;

    // 同期残高
    final currentBalance = bankAccount.balanceDetail.currentBalance ?? 0;

    // アコーディオンを開いているかのフラグ
    final isAccordionExpanded = useState(false);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 口座表示名
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: [
                      Expanded(
                        child: SizedBox(
                          // オーバーフローかつアコーディオンを開いていない時だけ「...」表示
                          child: Text(
                            !isOverFlowed || isAccordionExpanded.value
                                ? displayName
                                : displayName.ellipsis(
                                    maxLength: displayNameMaxLength,
                                  ),
                            style: displayNameTextStyle,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 16,
                        // オーバーフローしている時だけボタンを表示
                        child: !isOverFlowed
                            ? const SizedBox.shrink()
                            : GestureDetector(
                                onTap: () {
                                  // FirebaseAnalyticsログ送信
                                  analyticsLogController.sendButtonLog(
                                    buttonName: AppLocalizations.of(context)!
                                        .switchAccordion,
                                    screenName: AppLocalizations.of(context)!
                                        .transactionAccountInquiry,
                                  );
                                  isAccordionExpanded.value =
                                      !isAccordionExpanded.value;
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 6),
                                  child: SvgPicture.asset(
                                    isAccordionExpanded.value
                                        ? Assets.arrowUpIcon
                                        : Assets.arrowDownIcon,
                                  ),
                                ),
                              ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // 金融機関名 ph0.5時点では固定値（空）が返却される
                  Text(
                    bankAccount.balanceDetail.bankName ?? '',
                    style: Theme.of(context).textTheme.bodyMedium!,
                  ),
                  const SizedBox(height: 8),
                  // 残高
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.balance,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.textGrayGreen,
                            ),
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.baseline,
                        textBaseline: TextBaseline.alphabetic,
                        children: [
                          Text(
                            currentBalance >= 0
                                ? currentBalance.withCommas
                                : '-${currentBalance.withCommas}',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge!
                                .copyWith(
                                  fontSize: 28,
                                  fontFamily: FontFamily.robotoCondensed,
                                ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            AppLocalizations.of(context)!.yenJp,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontWeight: FontWeight.w700,
                                    ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: double.infinity,
            height: 72,
            decoration: const BoxDecoration(
              color: AppColors.lightGreen1,
              borderRadius: BorderRadius.vertical(
                bottom: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: AppRectangleButton(
                label: AppLocalizations.of(context)!.statementDetailsInquiry,
                textColor: AppColors.white,
                buttonColor: AppColors.tradGreen,
                onPressed: onDetailButtonPressed,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// freeeエラー時のエラーハンドリング
class _FreeeErrorAccountInfoItem extends HookConsumerWidget {
  const _FreeeErrorAccountInfoItem({
    required this.error,
  });

  final AppError error;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(homeScreenProvider.notifier);
    final operationLogController = ref.read(operationLogControllerProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isErrorInfoForReAlignment =
        ErrorInfo.freeeNeedsReAlignmentErrorCode.contains(error.code);

    return Padding(
      padding: const EdgeInsets.only(top: 36),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.red),
          borderRadius: BorderRadius.circular(4),
          color: AppColors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 26),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                error.code ?? '',
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.grayGreen,
                      fontSize: 12,
                    ),
              ),
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      const SizedBox(height: 2.5),
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: SvgPicture.asset(Assets.alertIconHome),
                      ),
                    ],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      error.message ?? '',
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.red,
                          ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Column(
                children: [
                  // hasFaqフラグがtrueであれば、FAQリンクを表示する
                  Visibility(
                    visible: error.hasFaq,
                    child: Column(
                      children: [
                        FaqContent(
                          linkId: LinkIds.faqTop,
                          buttonName:
                              AppLocalizations.of(context)!.bankAccountFAQ,
                          screenId: ScreenIdNumber.accountInquiryScreenId,
                          screenName: AppLocalizations.of(context)!
                              .transactionAccountInquiry,
                          icon: Assets.actionIcon,
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: isErrorInfoForReAlignment,
                    child: Column(
                      children: [
                        const SizedBox(height: 16),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32),
                          child: SizedBox(
                            width: double.infinity,
                            child: AppRectangleButton(
                              label:
                                  AppLocalizations.of(context)!.reConnectFreee,
                              textColor: AppColors.white,
                              buttonColor: AppColors.tradGreen,
                              onPressed: () async {
                                // FirebaseAnalyticsログ送信
                                analyticsLogController.sendButtonLog(
                                  buttonName: AppLocalizations.of(context)!
                                      .reConnectFreee,
                                  screenName: AppLocalizations.of(context)!
                                      .transactionAccountInquiry,
                                );
                                final url =
                                    await controller.getUrlToFreeeReLinkPage();
                                final state = ref.watch(homeScreenProvider);
                                if (state.getUrlToFreeeSsoParamsError != null) {
                                  // 顧客操作ログ送信
                                  unawaited(
                                    operationLogController.sendOperationLog(
                                      functionLog:
                                          OperationLogMessage.freeeIntegration,
                                      operationLog: OperationLogMessage
                                          .freeeReIntegration,
                                      resultLog:
                                          OperationLogMessage.abnormality,
                                      errorIdLog: state
                                              .getUrlToFreeeSsoParamsError
                                              ?.code ??
                                          '',
                                    ),
                                  );
                                  return;
                                }

                                // freeeサインアップ画面をWebViewで表示
                                // 顧客操作ログ送信
                                unawaited(
                                  operationLogController.sendOperationLog(
                                    functionLog:
                                        OperationLogMessage.freeeIntegration,
                                    operationLog:
                                        OperationLogMessage.freeeReIntegration,
                                    resultLog: OperationLogMessage.normal,
                                    errorIdLog: '',
                                  ),
                                );
                                if (!context.mounted) return;
                                await ref
                                    .watch(freeeWebViewLauncherProvider)
                                    .call(
                                      context,
                                      WebViewRequest.launchUrl(
                                        initialUrl: url!,
                                      ),
                                      AppLocalizations.of(context)!
                                          .reConnectFreee,
                                      ScreenIdNumber.accountInquiryScreenId,
                                    );
                              },
                              svgPicture:
                                  SvgPicture.asset(Assets.actionIconWhite),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
