import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'bank_account_info_item.freezed.dart';

@freezed
class BankAccountInfoItem with _$BankAccountInfoItem {
  /// SMBC口座
  const factory BankAccountInfoItem.smbc({
    required BankAccountWithBalanceDetail bankAccount,
  }) = BankAccountCardItemSMBC;

  /// freee連携口座
  const factory BankAccountInfoItem.freee({
    required BankAccountWithBalanceDetail bankAccount,
  }) = BankAccountCardItemFreee;

  /// SMBC口座全取得エラー
  const factory BankAccountInfoItem.smbcError({
    required AppError error,
  }) = BankAccountCardItemSMBCError;

  /// SMBC口座部分取得エラー
  const factory BankAccountInfoItem.smbcBankAccountError({
    required BankAccountWithError bankAccount,
  }) = BankAccountCardItemSMBCBankAccountError;

  /// freee連携口座エラー
  const factory BankAccountInfoItem.freeeError({
    required AppError error,
  }) = BankAccountCardItemFreeeError;

  /// freeeと金融機関の連携エラー
  const factory BankAccountInfoItem.freeeReconnect({
    required BankAccountWithBalanceDetail bankAccount,
    required bool hasFaq,
  }) = BankAccountCardItemFreeeReconnect;
}
