// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bank_account_info_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BankAccountInfoItem {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BankAccountInfoItemCopyWith<$Res> {
  factory $BankAccountInfoItemCopyWith(
          BankAccountInfoItem value, $Res Function(BankAccountInfoItem) then) =
      _$BankAccountInfoItemCopyWithImpl<$Res, BankAccountInfoItem>;
}

/// @nodoc
class _$BankAccountInfoItemCopyWithImpl<$Res, $Val extends BankAccountInfoItem>
    implements $BankAccountInfoItemCopyWith<$Res> {
  _$BankAccountInfoItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$BankAccountCardItemSMBCImplCopyWith<$Res> {
  factory _$$BankAccountCardItemSMBCImplCopyWith(
          _$BankAccountCardItemSMBCImpl value,
          $Res Function(_$BankAccountCardItemSMBCImpl) then) =
      __$$BankAccountCardItemSMBCImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccountWithBalanceDetail bankAccount});
}

/// @nodoc
class __$$BankAccountCardItemSMBCImplCopyWithImpl<$Res>
    extends _$BankAccountInfoItemCopyWithImpl<$Res,
        _$BankAccountCardItemSMBCImpl>
    implements _$$BankAccountCardItemSMBCImplCopyWith<$Res> {
  __$$BankAccountCardItemSMBCImplCopyWithImpl(
      _$BankAccountCardItemSMBCImpl _value,
      $Res Function(_$BankAccountCardItemSMBCImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = freezed,
  }) {
    return _then(_$BankAccountCardItemSMBCImpl(
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccountWithBalanceDetail,
    ));
  }
}

/// @nodoc

class _$BankAccountCardItemSMBCImpl implements BankAccountCardItemSMBC {
  const _$BankAccountCardItemSMBCImpl({required this.bankAccount});

  @override
  final BankAccountWithBalanceDetail bankAccount;

  @override
  String toString() {
    return 'BankAccountInfoItem.smbc(bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemSMBCImpl &&
            const DeepCollectionEquality()
                .equals(other.bankAccount, bankAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(bankAccount));

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemSMBCImplCopyWith<_$BankAccountCardItemSMBCImpl>
      get copyWith => __$$BankAccountCardItemSMBCImplCopyWithImpl<
          _$BankAccountCardItemSMBCImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) {
    return smbc(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) {
    return smbc?.call(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) {
    if (smbc != null) {
      return smbc(bankAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) {
    return smbc(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) {
    return smbc?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) {
    if (smbc != null) {
      return smbc(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemSMBC implements BankAccountInfoItem {
  const factory BankAccountCardItemSMBC(
          {required final BankAccountWithBalanceDetail bankAccount}) =
      _$BankAccountCardItemSMBCImpl;

  BankAccountWithBalanceDetail get bankAccount;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemSMBCImplCopyWith<_$BankAccountCardItemSMBCImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemFreeeImplCopyWith<$Res> {
  factory _$$BankAccountCardItemFreeeImplCopyWith(
          _$BankAccountCardItemFreeeImpl value,
          $Res Function(_$BankAccountCardItemFreeeImpl) then) =
      __$$BankAccountCardItemFreeeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccountWithBalanceDetail bankAccount});
}

/// @nodoc
class __$$BankAccountCardItemFreeeImplCopyWithImpl<$Res>
    extends _$BankAccountInfoItemCopyWithImpl<$Res,
        _$BankAccountCardItemFreeeImpl>
    implements _$$BankAccountCardItemFreeeImplCopyWith<$Res> {
  __$$BankAccountCardItemFreeeImplCopyWithImpl(
      _$BankAccountCardItemFreeeImpl _value,
      $Res Function(_$BankAccountCardItemFreeeImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = freezed,
  }) {
    return _then(_$BankAccountCardItemFreeeImpl(
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccountWithBalanceDetail,
    ));
  }
}

/// @nodoc

class _$BankAccountCardItemFreeeImpl implements BankAccountCardItemFreee {
  const _$BankAccountCardItemFreeeImpl({required this.bankAccount});

  @override
  final BankAccountWithBalanceDetail bankAccount;

  @override
  String toString() {
    return 'BankAccountInfoItem.freee(bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemFreeeImpl &&
            const DeepCollectionEquality()
                .equals(other.bankAccount, bankAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(bankAccount));

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemFreeeImplCopyWith<_$BankAccountCardItemFreeeImpl>
      get copyWith => __$$BankAccountCardItemFreeeImplCopyWithImpl<
          _$BankAccountCardItemFreeeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) {
    return freee(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) {
    return freee?.call(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) {
    if (freee != null) {
      return freee(bankAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) {
    return freee(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) {
    return freee?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) {
    if (freee != null) {
      return freee(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemFreee implements BankAccountInfoItem {
  const factory BankAccountCardItemFreee(
          {required final BankAccountWithBalanceDetail bankAccount}) =
      _$BankAccountCardItemFreeeImpl;

  BankAccountWithBalanceDetail get bankAccount;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemFreeeImplCopyWith<_$BankAccountCardItemFreeeImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemSMBCErrorImplCopyWith<$Res> {
  factory _$$BankAccountCardItemSMBCErrorImplCopyWith(
          _$BankAccountCardItemSMBCErrorImpl value,
          $Res Function(_$BankAccountCardItemSMBCErrorImpl) then) =
      __$$BankAccountCardItemSMBCErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$BankAccountCardItemSMBCErrorImplCopyWithImpl<$Res>
    extends _$BankAccountInfoItemCopyWithImpl<$Res,
        _$BankAccountCardItemSMBCErrorImpl>
    implements _$$BankAccountCardItemSMBCErrorImplCopyWith<$Res> {
  __$$BankAccountCardItemSMBCErrorImplCopyWithImpl(
      _$BankAccountCardItemSMBCErrorImpl _value,
      $Res Function(_$BankAccountCardItemSMBCErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$BankAccountCardItemSMBCErrorImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
    ));
  }

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$BankAccountCardItemSMBCErrorImpl
    implements BankAccountCardItemSMBCError {
  const _$BankAccountCardItemSMBCErrorImpl({required this.error});

  @override
  final AppError error;

  @override
  String toString() {
    return 'BankAccountInfoItem.smbcError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemSMBCErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemSMBCErrorImplCopyWith<
          _$BankAccountCardItemSMBCErrorImpl>
      get copyWith => __$$BankAccountCardItemSMBCErrorImplCopyWithImpl<
          _$BankAccountCardItemSMBCErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) {
    return smbcError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) {
    return smbcError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) {
    if (smbcError != null) {
      return smbcError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) {
    return smbcError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) {
    return smbcError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) {
    if (smbcError != null) {
      return smbcError(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemSMBCError implements BankAccountInfoItem {
  const factory BankAccountCardItemSMBCError({required final AppError error}) =
      _$BankAccountCardItemSMBCErrorImpl;

  AppError get error;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemSMBCErrorImplCopyWith<
          _$BankAccountCardItemSMBCErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemSMBCBankAccountErrorImplCopyWith<$Res> {
  factory _$$BankAccountCardItemSMBCBankAccountErrorImplCopyWith(
          _$BankAccountCardItemSMBCBankAccountErrorImpl value,
          $Res Function(_$BankAccountCardItemSMBCBankAccountErrorImpl) then) =
      __$$BankAccountCardItemSMBCBankAccountErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccountWithError bankAccount});
}

/// @nodoc
class __$$BankAccountCardItemSMBCBankAccountErrorImplCopyWithImpl<$Res>
    extends _$BankAccountInfoItemCopyWithImpl<$Res,
        _$BankAccountCardItemSMBCBankAccountErrorImpl>
    implements _$$BankAccountCardItemSMBCBankAccountErrorImplCopyWith<$Res> {
  __$$BankAccountCardItemSMBCBankAccountErrorImplCopyWithImpl(
      _$BankAccountCardItemSMBCBankAccountErrorImpl _value,
      $Res Function(_$BankAccountCardItemSMBCBankAccountErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = freezed,
  }) {
    return _then(_$BankAccountCardItemSMBCBankAccountErrorImpl(
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccountWithError,
    ));
  }
}

/// @nodoc

class _$BankAccountCardItemSMBCBankAccountErrorImpl
    implements BankAccountCardItemSMBCBankAccountError {
  const _$BankAccountCardItemSMBCBankAccountErrorImpl(
      {required this.bankAccount});

  @override
  final BankAccountWithError bankAccount;

  @override
  String toString() {
    return 'BankAccountInfoItem.smbcBankAccountError(bankAccount: $bankAccount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemSMBCBankAccountErrorImpl &&
            const DeepCollectionEquality()
                .equals(other.bankAccount, bankAccount));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(bankAccount));

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemSMBCBankAccountErrorImplCopyWith<
          _$BankAccountCardItemSMBCBankAccountErrorImpl>
      get copyWith =>
          __$$BankAccountCardItemSMBCBankAccountErrorImplCopyWithImpl<
              _$BankAccountCardItemSMBCBankAccountErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) {
    return smbcBankAccountError(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) {
    return smbcBankAccountError?.call(bankAccount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) {
    if (smbcBankAccountError != null) {
      return smbcBankAccountError(bankAccount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) {
    return smbcBankAccountError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) {
    return smbcBankAccountError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) {
    if (smbcBankAccountError != null) {
      return smbcBankAccountError(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemSMBCBankAccountError
    implements BankAccountInfoItem {
  const factory BankAccountCardItemSMBCBankAccountError(
          {required final BankAccountWithError bankAccount}) =
      _$BankAccountCardItemSMBCBankAccountErrorImpl;

  BankAccountWithError get bankAccount;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemSMBCBankAccountErrorImplCopyWith<
          _$BankAccountCardItemSMBCBankAccountErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemFreeeErrorImplCopyWith<$Res> {
  factory _$$BankAccountCardItemFreeeErrorImplCopyWith(
          _$BankAccountCardItemFreeeErrorImpl value,
          $Res Function(_$BankAccountCardItemFreeeErrorImpl) then) =
      __$$BankAccountCardItemFreeeErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AppError error});

  $AppErrorCopyWith<$Res> get error;
}

/// @nodoc
class __$$BankAccountCardItemFreeeErrorImplCopyWithImpl<$Res>
    extends _$BankAccountInfoItemCopyWithImpl<$Res,
        _$BankAccountCardItemFreeeErrorImpl>
    implements _$$BankAccountCardItemFreeeErrorImplCopyWith<$Res> {
  __$$BankAccountCardItemFreeeErrorImplCopyWithImpl(
      _$BankAccountCardItemFreeeErrorImpl _value,
      $Res Function(_$BankAccountCardItemFreeeErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? error = null,
  }) {
    return _then(_$BankAccountCardItemFreeeErrorImpl(
      error: null == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as AppError,
    ));
  }

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AppErrorCopyWith<$Res> get error {
    return $AppErrorCopyWith<$Res>(_value.error, (value) {
      return _then(_value.copyWith(error: value));
    });
  }
}

/// @nodoc

class _$BankAccountCardItemFreeeErrorImpl
    implements BankAccountCardItemFreeeError {
  const _$BankAccountCardItemFreeeErrorImpl({required this.error});

  @override
  final AppError error;

  @override
  String toString() {
    return 'BankAccountInfoItem.freeeError(error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemFreeeErrorImpl &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemFreeeErrorImplCopyWith<
          _$BankAccountCardItemFreeeErrorImpl>
      get copyWith => __$$BankAccountCardItemFreeeErrorImplCopyWithImpl<
          _$BankAccountCardItemFreeeErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) {
    return freeeError(error);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) {
    return freeeError?.call(error);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) {
    if (freeeError != null) {
      return freeeError(error);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) {
    return freeeError(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) {
    return freeeError?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) {
    if (freeeError != null) {
      return freeeError(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemFreeeError implements BankAccountInfoItem {
  const factory BankAccountCardItemFreeeError({required final AppError error}) =
      _$BankAccountCardItemFreeeErrorImpl;

  AppError get error;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemFreeeErrorImplCopyWith<
          _$BankAccountCardItemFreeeErrorImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BankAccountCardItemFreeeReconnectImplCopyWith<$Res> {
  factory _$$BankAccountCardItemFreeeReconnectImplCopyWith(
          _$BankAccountCardItemFreeeReconnectImpl value,
          $Res Function(_$BankAccountCardItemFreeeReconnectImpl) then) =
      __$$BankAccountCardItemFreeeReconnectImplCopyWithImpl<$Res>;
  @useResult
  $Res call({BankAccountWithBalanceDetail bankAccount, bool hasFaq});
}

/// @nodoc
class __$$BankAccountCardItemFreeeReconnectImplCopyWithImpl<$Res>
    extends _$BankAccountInfoItemCopyWithImpl<$Res,
        _$BankAccountCardItemFreeeReconnectImpl>
    implements _$$BankAccountCardItemFreeeReconnectImplCopyWith<$Res> {
  __$$BankAccountCardItemFreeeReconnectImplCopyWithImpl(
      _$BankAccountCardItemFreeeReconnectImpl _value,
      $Res Function(_$BankAccountCardItemFreeeReconnectImpl) _then)
      : super(_value, _then);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? bankAccount = freezed,
    Object? hasFaq = null,
  }) {
    return _then(_$BankAccountCardItemFreeeReconnectImpl(
      bankAccount: freezed == bankAccount
          ? _value.bankAccount
          : bankAccount // ignore: cast_nullable_to_non_nullable
              as BankAccountWithBalanceDetail,
      hasFaq: null == hasFaq
          ? _value.hasFaq
          : hasFaq // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$BankAccountCardItemFreeeReconnectImpl
    implements BankAccountCardItemFreeeReconnect {
  const _$BankAccountCardItemFreeeReconnectImpl(
      {required this.bankAccount, required this.hasFaq});

  @override
  final BankAccountWithBalanceDetail bankAccount;
  @override
  final bool hasFaq;

  @override
  String toString() {
    return 'BankAccountInfoItem.freeeReconnect(bankAccount: $bankAccount, hasFaq: $hasFaq)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BankAccountCardItemFreeeReconnectImpl &&
            const DeepCollectionEquality()
                .equals(other.bankAccount, bankAccount) &&
            (identical(other.hasFaq, hasFaq) || other.hasFaq == hasFaq));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(bankAccount), hasFaq);

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BankAccountCardItemFreeeReconnectImplCopyWith<
          _$BankAccountCardItemFreeeReconnectImpl>
      get copyWith => __$$BankAccountCardItemFreeeReconnectImplCopyWithImpl<
          _$BankAccountCardItemFreeeReconnectImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(BankAccountWithBalanceDetail bankAccount) smbc,
    required TResult Function(BankAccountWithBalanceDetail bankAccount) freee,
    required TResult Function(AppError error) smbcError,
    required TResult Function(BankAccountWithError bankAccount)
        smbcBankAccountError,
    required TResult Function(AppError error) freeeError,
    required TResult Function(
            BankAccountWithBalanceDetail bankAccount, bool hasFaq)
        freeeReconnect,
  }) {
    return freeeReconnect(bankAccount, hasFaq);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult? Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult? Function(AppError error)? smbcError,
    TResult? Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult? Function(AppError error)? freeeError,
    TResult? Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
  }) {
    return freeeReconnect?.call(bankAccount, hasFaq);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(BankAccountWithBalanceDetail bankAccount)? smbc,
    TResult Function(BankAccountWithBalanceDetail bankAccount)? freee,
    TResult Function(AppError error)? smbcError,
    TResult Function(BankAccountWithError bankAccount)? smbcBankAccountError,
    TResult Function(AppError error)? freeeError,
    TResult Function(BankAccountWithBalanceDetail bankAccount, bool hasFaq)?
        freeeReconnect,
    required TResult orElse(),
  }) {
    if (freeeReconnect != null) {
      return freeeReconnect(bankAccount, hasFaq);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(BankAccountCardItemSMBC value) smbc,
    required TResult Function(BankAccountCardItemFreee value) freee,
    required TResult Function(BankAccountCardItemSMBCError value) smbcError,
    required TResult Function(BankAccountCardItemSMBCBankAccountError value)
        smbcBankAccountError,
    required TResult Function(BankAccountCardItemFreeeError value) freeeError,
    required TResult Function(BankAccountCardItemFreeeReconnect value)
        freeeReconnect,
  }) {
    return freeeReconnect(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(BankAccountCardItemSMBC value)? smbc,
    TResult? Function(BankAccountCardItemFreee value)? freee,
    TResult? Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult? Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult? Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult? Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
  }) {
    return freeeReconnect?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(BankAccountCardItemSMBC value)? smbc,
    TResult Function(BankAccountCardItemFreee value)? freee,
    TResult Function(BankAccountCardItemSMBCError value)? smbcError,
    TResult Function(BankAccountCardItemSMBCBankAccountError value)?
        smbcBankAccountError,
    TResult Function(BankAccountCardItemFreeeError value)? freeeError,
    TResult Function(BankAccountCardItemFreeeReconnect value)? freeeReconnect,
    required TResult orElse(),
  }) {
    if (freeeReconnect != null) {
      return freeeReconnect(this);
    }
    return orElse();
  }
}

abstract class BankAccountCardItemFreeeReconnect
    implements BankAccountInfoItem {
  const factory BankAccountCardItemFreeeReconnect(
      {required final BankAccountWithBalanceDetail bankAccount,
      required final bool hasFaq}) = _$BankAccountCardItemFreeeReconnectImpl;

  BankAccountWithBalanceDetail get bankAccount;
  bool get hasFaq;

  /// Create a copy of BankAccountInfoItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BankAccountCardItemFreeeReconnectImplCopyWith<
          _$BankAccountCardItemFreeeReconnectImpl>
      get copyWith => throw _privateConstructorUsedError;
}
