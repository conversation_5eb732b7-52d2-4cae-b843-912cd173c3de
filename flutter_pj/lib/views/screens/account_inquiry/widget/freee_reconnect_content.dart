import 'dart:async';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/screens/home/<USER>/freee_update_dialog_content.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AccountInfoReConnectFreeeItem extends StatelessWidget {
  const AccountInfoReConnectFreeeItem({
    super.key,
    required this.bankAccount,
    required this.hasFaq,
  });

  final BankAccountWithBalanceDetail bankAccount;
  final bool hasFaq;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(4),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 口座表示名
                  _AccountDisplayNameArea(
                    displayName: bankAccount.displayConfig.displayName,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.red),
                      borderRadius: BorderRadius.circular(4),
                      color: AppColors.white,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            ErrorInfo
                                .freeeReConnectFinancialInstitutionErrorCode,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontFamily: FontFamily.robotoCondensed,
                                      color: AppColors.grayGreen,
                                      fontSize: 12,
                                    ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                children: [
                                  const SizedBox(height: 2.5),
                                  SizedBox(
                                    width: 16,
                                    height: 16,
                                    child:
                                        SvgPicture.asset(Assets.alertIconHome),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  ErrorInfo
                                      .freeeReConnectFinancialInstitutionErrorMessage,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        color: AppColors.red,
                                      ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Visibility(
                            visible: hasFaq,
                            child: FaqContent(
                              linkId: LinkIds.faq,
                              buttonName: AppLocalizations.of(context)!.here,
                              screenId: ScreenIdNumber.accountInquiryScreenId,
                              screenName: AppLocalizations.of(context)!
                                  .transactionAccountInquiry,
                              icon: Assets.actionIcon,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 「確認する」ボタン
                  _ReConnectButton(),
                ],
              ),
            ),
          ),
          // 「入出金明細」ボタン（非活性）
          _InactiveIncomeAndExpenditureButton(),
        ],
      ),
    );
  }
}

class _AccountDisplayNameArea extends HookConsumerWidget {
  const _AccountDisplayNameArea({
    required this.displayName,
  });

  final String displayName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final firebaseController = ref.read(analyticsLogControllerProvider);

    // 口座表示名
    final displayNameTextStyle = Theme.of(context)
        .textTheme
        .bodyMedium!
        .copyWith(fontWeight: FontWeight.w700);

    // 口座表示名の表示幅
    // 画面サイズからパディングとアイコンサイズを引いて算出
    final displayNameAreaWidth = MediaQuery.of(context).size.width - 88;

    // 1行に収まる口座表示名の最大文字数を計算
    final displayNameMaxLength = displayName.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // 口座表示名がオーバーフローしているかを判定
    final isOverFlowed = displayName.length > displayNameMaxLength;

    // アコーディオンを開いているかのフラグ
    final isAccordionExpanded = useState(false);

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Expanded(
          child: SizedBox(
            // オーバーフローかつアコーディオンを開いていない時だけ「...」表示
            child: Text(
              !isOverFlowed || isAccordionExpanded.value
                  ? displayName
                  : displayName.ellipsis(
                      maxLength: displayNameMaxLength,
                    ),
              style: displayNameTextStyle,
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 16,
          // オーバーフローしている時だけボタンを表示
          child: Visibility(
            visible: isOverFlowed,
            child: GestureDetector(
              onTap: () {
                // FirebaseAnalyticsログ送信
                firebaseController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.switchAccordion,
                  screenName:
                      AppLocalizations.of(context)!.transactionAccountInquiry,
                );
                isAccordionExpanded.value = !isAccordionExpanded.value;
              },
              child: Padding(
                padding: const EdgeInsets.only(top: 6),
                child: SvgPicture.asset(
                  isAccordionExpanded.value
                      ? Assets.arrowUpIcon
                      : Assets.arrowDownIcon,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _ReConnectButton extends HookConsumerWidget {
  const _ReConnectButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final operationLogController = ref.read(operationLogControllerProvider);
    final freeeRedirectURL = ref.read(buildConfigProvider).freeeRedirectScreen;
    final homeController = ref.read(homeScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final urlLauncher = ref.watch(urlLauncherProvider);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 32,
            vertical: 16,
          ),
          child: SizedBox(
            width: double.infinity,
            child: AppRectangleButton(
              label: AppLocalizations.of(context)!.confirmFreee,
              textColor: AppColors.white,
              buttonColor: AppColors.tradGreen,
              onPressed: () async {
                // ローディング開始
                LoadingDialog.loading(context);
                // 顧客操作ログ送信
                unawaited(
                  operationLogController.sendOperationLog(
                    functionLog: OperationLogMessage.freeeIntegration,
                    operationLog: OperationLogMessage.freeeReIntegration,
                    resultLog: '',
                    errorIdLog: '',
                  ),
                );
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName:
                      AppLocalizations.of(context)!.confirmFreeeOtherBank,
                  screenName: AppLocalizations.of(
                    context,
                  )!
                      .transactionAccountInquiry,
                );
                final url = await homeController.getUrlToFreeeSSOPage(
                  freeeRedirectURL,
                  FreeeSSOTypeFromHome.expirationOfFinanceLinkage,
                );
                // URL取得時にエラーが発生した場合、後続処理を中断
                if (ref
                        .read(homeScreenProvider)
                        .getUrlToFreeeSsoParamsError
                        .isNotNull ||
                    url == null) return;
                if (!context.mounted) return;
                await urlLauncher.launchExternalUrl(
                  AppLocalizations.of(context)!.confirmFreeeOtherBank,
                  ScreenIdNumber.accountInquiryScreenId,
                  url,
                  useEncryptedCookie: true,
                );
                if (!context.mounted) return;
                // ローディング終了
                LoadingDialog.loadingEnd(context);
                if (!context.mounted) return;
                showFreeeUpdateDialog(
                  context,
                  ref,
                  AppLocalizations.of(context)!.closeAdditionAccount,
                  AppLocalizations.of(context)!.updateAdditionAccount,
                  isCreditCard: false,
                  isHome: false,
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

class _InactiveIncomeAndExpenditureButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.lightGreen1,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 32,
              vertical: 16,
            ),
            child: SizedBox(
              width: double.infinity,
              // 非活性状態のため、ボタン押下時は何も実施しない
              child: AppRectangleButton(
                label: AppLocalizations.of(context)!.incomeAndExpenditure,
                textColor: AppColors.inactiveText,
                buttonColor: AppColors.backgroundGreen,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
