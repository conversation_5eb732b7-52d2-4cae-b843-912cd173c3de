import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dtp_app/views/styles/assets.dart';

class BalanceDetailsModal extends HookConsumerWidget {
  const BalanceDetailsModal({super.key, required this.bankAccount});

  final BankAccountWithBalanceDetail bankAccount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const fontSizeChangeDigit = 9;
    final balanceDetail = bankAccount.balanceDetail;
    final isCurrentBalanceSizeChangeDigit =
        balanceDetail.currentBalance.toString().length <= fontSizeChangeDigit;
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.balanceDetailsScreenId,
            screenName: AppLocalizations.of(context)!.balanceDetails,
          );
        });
        return null;
      },
      const [],
    );

    return Padding(
      padding: const EdgeInsets.only(left: 16, top: 16, right: 16, bottom: 40),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 24),
                child: Text(
                  AppLocalizations.of(context)!.balanceDetails,
                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                        fontSize: 20,
                        color: AppColors.tradGreen,
                        height: 1.5,
                      ),
                ),
              ),
              Align(
                alignment: Alignment.topRight,
                child: SizedBox(
                  height: 32,
                  width: 32,
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      // FirebaseAnalyticsログ送信
                      analyticsLogController.sendButtonLog(
                        buttonName: AppLocalizations.of(context)!.batsu,
                        screenName:
                            AppLocalizations.of(context)!.balanceDetails,
                      );
                      Navigator.of(context).pop();
                    },
                    icon: SvgPicture.asset(
                      Assets.buttonClose,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            width: double.infinity,
            decoration: BoxDecoration(
              color: AppColors.lightGreen,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.balance,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppColors.textGrayGreen,
                            height: 1.5,
                          ),
                    ),
                    _Amount(
                      isCurrentBalanceSizeChangeDigit,
                      balanceDetail.currentBalance,
                      15,
                      true,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      AppLocalizations.of(context)!
                          .checksIssuedByOtherBanksAmount,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppColors.textGrayGreen,
                            height: 1.5,
                          ),
                    ),
                    _Amount(
                      isCurrentBalanceSizeChangeDigit,
                      balanceDetail.checksIssuedByOtherBanks,
                      14,
                      false,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.overdraftLimit,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppColors.textGrayGreen,
                            height: 1.5,
                          ),
                    ),
                    _Amount(
                      isCurrentBalanceSizeChangeDigit,
                      balanceDetail.overdraftLimit,
                      14,
                      false,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.withdrawableBalance,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.w700,
                            color: AppColors.textGrayGreen,
                            height: 1.5,
                          ),
                    ),
                    _Amount(
                      isCurrentBalanceSizeChangeDigit,
                      balanceDetail.withdrawableBalance,
                      15,
                      false,
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          DefaultTextStyle(
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: AppColors.textGrayGreen,
                ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  bankAccount.displayConfig.displayName,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
                const SizedBox(
                  height: 8,
                ),
                NullableHyphenText(
                  data: balanceDetail.bankName,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        height: 1.5,
                      ),
                ),
                const SizedBox(
                  height: 4,
                ),
                NullableHyphenText(
                  data: balanceDetail.branchName,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        height: 1.5,
                      ),
                ),
                const SizedBox(
                  height: 4,
                ),
                Row(
                  children: [
                    NullableHyphenText(
                      data: balanceDetail.accountType,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: AppColors.textGrayGreen,
                            height: 1.5,
                          ),
                    ),
                    const SizedBox(width: 8),
                    NullableHyphenText(
                      data: balanceDetail.accountNumber,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            color: AppColors.textGrayGreen,
                            height: 1.5,
                          ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 4,
                ),
                NullableHyphenText(
                  data: bankAccount.baseInfo.remitterName,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        height: 1.5,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 金額の表示部
class _Amount extends StatelessWidget {
  const _Amount(
    this.isCurrentBalanceSizeChangeDigit,
    this.amount,
    this.maxDigit,
    this.isBankBalance,
  );

  final bool isCurrentBalanceSizeChangeDigit;
  final int? amount;
  final int maxDigit;

  // 口座残高の際には文字サイズに特別な制御が必要な為、判断フラグを儲ける
  final bool isBankBalance;

  @override
  Widget build(BuildContext context) {
    if (amount == null) {
      return Text(AppLocalizations.of(context)!.fullWidthHyphen);
    }

    final double fontSize =
        isBankBalance && isCurrentBalanceSizeChangeDigit ? 32 : 24;
    final textStyle = isBankBalance
        ? Theme.of(context).textTheme.titleLarge!.copyWith(
              fontFamily: FontFamily.robotoCondensed,
              fontSize: fontSize,
              height: 1.5,
            )
        : Theme.of(context).textTheme.bodyLarge!.copyWith(
              fontFamily: FontFamily.robotoCondensed,
              height: 1.5,
            );

    if (amount.toString().length > maxDigit) {
      return Text(
        BankAccountErrorInfo.overflowDigitError,
        style: Theme.of(context).textTheme.titleLarge!.copyWith(
              fontFamily: FontFamily.notoSansJP,
              fontSize: 16,
              height: 1.5,
            ),
      );
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Text(
          amount!.withCommasAndMinusSign,
          style: textStyle,
        ),
        const SizedBox(width: 4),
        Text(
          AppLocalizations.of(context)!.yenJp,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
      ],
    );
  }
}
