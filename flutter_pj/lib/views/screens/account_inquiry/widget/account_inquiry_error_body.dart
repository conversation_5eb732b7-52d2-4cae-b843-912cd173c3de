import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

//取引口座照会、部分エラー
class AccountInquiryErrorBody extends HookConsumerWidget {
  const AccountInquiryErrorBody({
    super.key,
    required this.errorCode,
    required this.errorMessage,
    required this.hasFaq,
    this.maxLines,
    this.textOverflow,
  });

  final String errorCode;
  final String errorMessage;
  final bool hasFaq;
  final int? maxLines;
  final TextOverflow? textOverflow;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.red),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              errorCode,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontFamily: FontFamily.robotoCondensed,
                    color: AppColors.grayGreen,
                    fontSize: 12,
                  ),
            ),
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    const SizedBox(height: 2.5),
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: SvgPicture.asset(Assets.alertIconHome),
                    ),
                  ],
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    errorMessage,
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(color: AppColors.red),
                    maxLines: maxLines,
                    overflow: textOverflow,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Visibility(
              visible: hasFaq,
              child: FaqContent(
                linkId: LinkIds.faqTop,
                buttonName: AppLocalizations.of(context)!.here,
                screenId: ScreenIdNumber.accountInquiryScreenId,
                screenName:
                    AppLocalizations.of(context)!.transactionAccountInquiry,
                icon: Assets.actionIcon,
                fontSize: 14,
                padding: EdgeInsets.only(left: 20),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
