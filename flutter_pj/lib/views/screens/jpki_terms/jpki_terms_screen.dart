import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/jpki_terms/jpki_terms_screen_notifier.dart';
import 'package:dtp_app/models/jpki_consent_status/jpki_consent_status.dart';
import 'package:dtp_app/utils/screen_id_number.dart';

import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/my_app_bar.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/components/web_view/web_view_screen.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JpkiTermsScreen extends HookConsumerWidget {
  const JpkiTermsScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.jpkiTermsScreenId,
            screenName: AppLocalizations.of(context)!.jpkiTerm,
          );
        });
        return null;
      },
      const [],
    );

    return PopScope(
      // 戻るボタンの無効化
      canPop: false,
      child: ColoredBox(
        color: AppColors.white,
        child: SafeArea(
          top: false,
          child: Scaffold(
            appBar: _AppBar(),
            body: const _Body(),
          ),
        ),
      ),
    );
  }
}

class _AppBar extends HookConsumerWidget implements PreferredSizeWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final actions = [
      GestureDetector(
        onTap: () {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.helpIcon,
            screenName: AppLocalizations.of(context)!.header,
          );
          ref.watch(urlLauncherProvider).launchLinkId(
                LinkIds.faq,
                AppLocalizations.of(context)!.helpIcon,
                ScreenIdNumber.jpkiTermsScreenId,
              );
        },
        child: SvgPicture.asset(Assets.helpWhiteIcon),
      ),
      const SizedBox(width: 13),
    ];

    return MyAppBar(
      actions: actions,
      // 戻るボタンを非表示
      automaticallyImplyLeading: false,
      screenId: ScreenIdNumber.jpkiTermsScreenId,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(56);
}

class _Body extends StatelessWidget {
  const _Body();

  @override
  Widget build(BuildContext context) {
    final deviceSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        Image.asset(
          Assets.bgWaveGrey,
          height: deviceSize.height,
          width: deviceSize.width,
          fit: BoxFit.cover,
        ),
        Column(
          children: [
            ScreenNumber(
              screenNumber: ScreenIdNumber.jpkiTermsScreenId,
              color: AppColors.textBlack,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  const _CheckList(),
                  const SizedBox(height: 40),
                  const _AgreeButton(),
                  const SizedBox(height: 16),
                  const _DisagreeButton(),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _CheckList extends HookConsumerWidget {
  const _CheckList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final jpkiEkycTermOfServiceWebViewUrl = ref
        .read(urlLauncherProvider)
        .getLinkIdUrl(LinkIds.jpkiEkycSmbcBusinessTerm);
    const termOfServiceName = 'DTP';
    final state = ref.watch(jpkiTermsScreenProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGray.withOpacity(0.4),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 24),
          Text(
            AppLocalizations.of(context)!.jpkiCheckList,
            style: Theme.of(context)
                .textTheme
                .bodyLarge!
                .copyWith(fontSize: 20, color: AppColors.tradGreen),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context)!.contentAgreementMessage,
            style: Theme.of(context).textTheme.bodyMedium!,
          ),
          const SizedBox(height: 24),
          const _Divider(),
          const SizedBox(height: 24),
          _CheckListItem(
            text: AppLocalizations.of(context)!.jpkiTermOfService,
            url: jpkiEkycTermOfServiceWebViewUrl,
            isChecked: state.jpkiAgreeTerms.contains(termOfServiceName),
            consentType: termOfServiceName,
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}

class _CheckListItem extends HookConsumerWidget {
  const _CheckListItem({
    required this.text,
    required this.url,
    required this.isChecked,
    required this.consentType,
  });

  final String text;
  final String url;
  final bool isChecked;
  final String consentType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(jpkiTermsScreenProvider);
    final controller = ref.watch(jpkiTermsScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SvgPicture.asset(
          isChecked ? Assets.checkGreen2Icon : Assets.checkIcon,
        ),
        const SizedBox(width: 16),
        GestureDetector(
          onTap: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.termOfService,
              screenName: AppLocalizations.of(context)!.jpkiTerm,
            );
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.termOfService,
              screenIdNumber: ScreenIdNumber.jpkiTermsScreenId,
            );
            // 引数のurlを元にWebViewを開く
            final completer = ref
                .watch(
                  webViewLauncherProvider,
                )
                .call(
                  context,
                  WebViewRequest.launchUrl(
                    initialUrl: url,
                  ),
                  AppLocalizations.of(context)!.jpkiTerm,
                  ScreenIdNumber.jpkiTermsScreenId,
                );
            completer.then(
              (_) {
                if (state.jpkiAgreeTerms.contains(consentType)) {
                  return;
                }
                // リンクが参照されたとして対象のチェックマークをONにする
                final checkedTerms = [
                  ...state.jpkiAgreeTerms,
                  consentType,
                ];
                controller.updateJpkiConsentStatus(checkedTerms);
              },
            );
          },
          child: Row(
            children: [
              Text(
                text,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(color: AppColors.tradGreen),
              ),
              const SizedBox(width: 4),
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: SvgPicture.asset(
                  Assets.arrowRightIcon,
                  width: 16,
                  height: 16,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _AgreeButton extends HookConsumerWidget {
  const _AgreeButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(jpkiTermsScreenProvider);
    final isAgreed = state.jpkiAgreeTerms.length == jpkiAgreeTermsNumber;
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    // final operationLogController = ref.watch(
    //   operationLogControllerProvider,
    // );

    return SizedBox(
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: AppColors.tradGreen,
          disabledBackgroundColor: AppColors.backgroundGreen,
          foregroundColor: AppColors.white,
          disabledForegroundColor:
              AppColors.buttonInactiveText.withOpacity(0.55),
          textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: FontWeight.w700,
              ),
        ),
        onPressed: isAgreed
            ? () {
                // Navigator の BuildContext を事前に取得
                final navigator = Navigator.of(context);
                // お手続き番号入力画面へ遷移
                navigator.pushNamed(RouteNames.identityReferenceNumber);

                // // 操作ログ送信
                // operationLogController.sendOperationLog(
                //   functionLog: OperationLogMessage.termsOfUse,
                //   operationLog: OperationLogMessage.agree,
                //   resultLog: state.error == null
                //       ? OperationLogMessage.normal
                //       : OperationLogMessage.abnormality,
                //   errorIdLog: state.error?.code ?? '',
                // );
                // FirebaseAnalyticsログ送信
                analyticsLogController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.agree,
                  screenName: AppLocalizations.of(context)!.jpkiTerm,
                );
              }
            : null,
        child: Center(
          child: Text(
            AppLocalizations.of(context)!.agree,
          ),
        ),
      ),
    );
  }
}

class _DisagreeButton extends HookConsumerWidget {
  const _DisagreeButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    // final operationLogRepository = ref.watch(
    //   operationLogRepositoryProvider,
    // );
    // final operationLogController =
    //     OperationLogController(operationLogRepository: operationLogRepository);

    return GestureDetector(
      onTap: () {
        // 操作ログ送信
        // operationLogController.sendOperationLog(
        //   functionLog: OperationLogMessage.termsOfUse,
        //   operationLog: OperationLogMessage.disagree,
        //   resultLog: state.error == null
        //       ? OperationLogMessage.normal
        //       : OperationLogMessage.abnormality,
        //   errorIdLog: state.error?.code ?? '',
        // );
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.disagree,
          screenName: AppLocalizations.of(context)!.jpkiTerm,
        );

        // 画面を戻る際に引数は渡さない
        Navigator.of(context).pop();
      },
      child: Text(
        AppLocalizations.of(context)!.disagree,
        style: Theme.of(context)
            .textTheme
            .bodyMedium!
            .copyWith(color: AppColors.tradGreen),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.grey200,
      height: 1,
    );
  }
}
