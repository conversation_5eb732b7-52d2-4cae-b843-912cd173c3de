import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/cupertino.dart';

class SplashView extends StatelessWidget {
  const SplashView({
    super.key,
    this.hiddenImage = true,
  });
  final bool hiddenImage;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.tradGreen,
      child: Center(
        child: SizedBox(
          width: 237,
          height: 113,
          child: hiddenImage
              ? const Text('')
              : Image.asset(
                  'assets/images/png/splash_logo_demo.png',
                  fit: BoxFit.contain,
                ),
        ),
      ),
    );
  }
}
