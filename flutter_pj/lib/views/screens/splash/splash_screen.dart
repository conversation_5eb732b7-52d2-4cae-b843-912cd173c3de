import 'package:dtp_app/business_logics/splash/splash_screen_notifier.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/splash/widget/splash_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(splashScreenProvider);
    final controller = ref.watch(splashScreenProvider.notifier);

    // 2重にbuildが呼ばれることがあるためuseEffectを利用
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          switch (state.launchingPhase) {
            case LaunchingPhases.initial:
              // アップデート情報とSALTを取得する
              controller.initializer();
            case LaunchingPhases.needUpdate:
              Future.microtask(() {
                if (!context.mounted) return;
                // 強制アップデート画面に遷移
                Navigator.of(context).pushReplacementNamed(
                  RouteNames.forceUpdate,
                  arguments: state.forceUpdateConfig!,
                );
              });
            case LaunchingPhases.completed:
              Future.microtask(() {
                if (!context.mounted) return;
                // jailbreak検知画面に遷移
                Navigator.of(context).pushReplacementNamed(
                  RouteNames.jailbreakDetection,
                );
              });
            case LaunchingPhases.errorSalt:
              // スプラッシュ画面を終了させる
              FlutterNativeSplash.remove();
              showCommonErrorDialog(
                dialogContext: context,
                errorPattern: ErrorPattern.errorPattern9,
                onPressed1: () {
                  // SALT取得をリトライ
                  controller.fetchSaltAndGenerateKey();
                },
                onPressed2: () {},
              );
          }
        });
        return null;
      },
      [state],
    );

    return const Scaffold(
      body: SplashView(),
    );
  }
}
