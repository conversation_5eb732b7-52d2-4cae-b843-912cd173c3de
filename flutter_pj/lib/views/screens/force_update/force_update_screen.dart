import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/force_update/force_update_screen_notifier.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/splash/widget/splash_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

class ForceUpdateScreen extends ConsumerWidget {
  const ForceUpdateScreen({super.key, required this.forceUpdateConfig});

  final ForceUpdateConfig forceUpdateConfig;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 画面ビルド時にスプラッシュ画面を終了させる
    FlutterNativeSplash.remove();

    final state = ref.watch(forceUpdateScreenProvider);
    final controller = ref.read(forceUpdateScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);

    switch (state.forceUpdateStatus) {
      case ForceUpdateStatus.initial:
        Future.microtask(() {
          controller.initialize(forceUpdateConfig: forceUpdateConfig);
        });
        break;
      case ForceUpdateStatus.forcedUpdate:
        // 強制アップデートダイアログ表示
        Future.microtask(() {
          if (!context.mounted) return;
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              final message = state.message ??
                  AppLocalizations.of(context)!.defaultForceUpdateDialogMessage;
              return CommonDialog.ok(
                title: AppLocalizations.of(context)!.forceUpdateDialogTitle,
                message: message,
                onOkPressed: () async {
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.showStore,
                    screenName: AppLocalizations.of(context)!.forceUpdateDialog,
                  );
                  await _launchStorePage(state.buttonLink);
                },
                okButtonText: AppLocalizations.of(context)!.showStore,
                isPopAfterOkButtonPress: false,
              );
            },
          );
        });
        break;
      case ForceUpdateStatus.optionalUpdate:
        // 任意アップデートダイアログ表示
        Future.microtask(() {
          if (!context.mounted) return;
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              final message = state.message ??
                  AppLocalizations.of(context)!
                      .defaultOptionalUpdateDialogMessage;
              return CommonDialog.okCancel(
                title: AppLocalizations.of(context)!.forceUpdateDialogTitle,
                message: message,
                onOkPressed: () {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.yes,
                    screenName:
                        AppLocalizations.of(context)!.optionalUpdateDialog,
                  );
                  _launchStorePage(state.buttonLink);
                },
                okButtonText: AppLocalizations.of(context)!.yes,
                isPopAfterOkButtonPress: false,
                onCancelPressed: () {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.no,
                    screenName:
                        AppLocalizations.of(context)!.optionalUpdateDialog,
                  );
                  controller.fetchSaltAndGenerateKey();
                },
                cancelButtonText: AppLocalizations.of(context)!.no,
              );
            },
          );
        });
        break;
      case ForceUpdateStatus.error:
        // エラーダイアログ表示
        Future.microtask(() {
          if (!context.mounted) return;
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return CommonDialog.ok(
                title: AppLocalizations.of(context)!
                    .errorDialogTitle(state.errorCode),
                message: state.message!,
                onOkPressed: () {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.okErrorDialog,
                    screenName: AppLocalizations.of(context)!.forceUpdateDialog,
                  );
                  controller.checkForceUpdate();
                },
              );
            },
          );
        });
        break;
      case ForceUpdateStatus.errorSalt:
        Future.microtask(() {
          if (!context.mounted) return;
          showCommonErrorDialog(
            shouldPopAfterButtonPress: true,
            dialogContext: context,
            errorPattern: ErrorPattern.errorPattern9,
            onPressed1: () {
              // SALT取得をリトライ
              controller.fetchSaltAndGenerateKey();
            },
            onPressed2: () {},
          );
        });
      case ForceUpdateStatus.completed:
        // jailbreak検知画面に遷移
        Future.microtask(() {
          if (!context.mounted) return;
          Navigator.of(context).pushReplacementNamed(
            RouteNames.jailbreakDetection,
          );
        });
        break;
    }

    return Scaffold(
      body: SplashView(
        hiddenImage: !state.isInitialized, // 初期化済みの場合は画像を表示
      ),
    );
  }

  /// ストアページへ遷移する
  Future<void> _launchStorePage(String url) async {
    final Uri uri = Uri.parse(url);
    await launchUrl(uri);
  }
}
