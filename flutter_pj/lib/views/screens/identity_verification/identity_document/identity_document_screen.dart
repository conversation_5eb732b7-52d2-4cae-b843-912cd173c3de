import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_document/identity_document_screen_notifier.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/utils/nfc_check.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/content_area.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:dtp_app/views/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 証明書選択画面
class IdentityDocumentScreen extends HookConsumerWidget {
  const IdentityDocumentScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityDocumentStateScreenProvider);
    final controller = ref.watch(identityDocumentStateScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);

    // Firebase Screenログ・KARTE Viewイベント送信
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.identityDocumentScreenId,
            screenName:
                AppLocalizations.of(context)!.identificationDocumentScreen,
          );
        });
        return null;
      },
      [],
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 次ページへの遷移
      if (state.nextPageName != null) {
        controller.stateReset();
        // ローディング処理を止める
        LoadingDialog.loadingEnd(context);
        Navigator.of(context).pushNamed(state.nextPageName!);
      }

      // エラーが発生した場合、パターンによって内容を出し分ける
      if (state.errorPattern != null) {
        // ローディング処理を止める
        LoadingDialog.loadingEnd(context);
        showCommonErrorDialog(
          dialogContext: context,
          errorPattern: state.errorPattern!,
          onPressed1: () {
            controller.stateReset();
            onPressed1(
              context,
              ref,
              state.errorPattern!,
              AppLocalizations.of(context)!.identificationDocumentScreen,
            );
          },
          onPressed2: () {
            controller.stateReset();
            onPressed2(
              context,
              ref,
              state.errorPattern!,
              AppLocalizations.of(context)!.identificationDocumentScreen,
            );
          },
        );
      }
    });

    return ColoredBox(
      color: AppColors.white,
      child: SafeArea(
        top: false,
        child: Scaffold(
          appBar: AppBarForIdentityVerification(
            title: AppLocalizations.of(context)!.identificationAppBarTitle,
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName:
                  AppLocalizations.of(context)!.identificationDocumentScreen,
            ),
          ),
          body: Stack(
            children: [
              Image.asset(
                Assets.bgWaveGrey,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
              ),
              SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    ScreenNumber(
                      screenNumber: ScreenIdNumber.identityDocumentScreenId,
                      color: AppColors.textBlack,
                    ),
                    ContentArea(
                      context: context,
                      title: AppLocalizations.of(context)!
                          .identityDocumentScreenTitle,
                      widget: const IdentityDocumentWidgets(),
                      minHeight: 360,
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: double.infinity,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    boxShadow: [
                      // SafeArea部分にも影が入ってしまうため、SafeAreaの境目に白色の影を追加
                      BoxShadow(
                        offset: const Offset(0, -2),
                        color: AppColors.cardShadow.withOpacity(0.15),
                        blurRadius: 4,
                      ),
                      const BoxShadow(
                        offset: Offset(0, 2),
                        color: AppColors.white,
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      // 次へ進むボタン
                      _NextButton(),
                      const SizedBox(height: 16),
                      // マイナンバーカードをお持ちでない場合ボタン
                      _TextLinkArrow(
                        text: AppLocalizations.of(context)!
                            .identityDocumentScreenNotHaveCard,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 証明書選択Widget
class IdentityDocumentWidgets extends StatelessWidget {
  const IdentityDocumentWidgets({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 24),
        Align(
          child: Image.asset(
            Assets.myNumberCard,
          ),
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.centerLeft,
          child: Image.asset(
            Assets.myNumberCardCatchPhrase,
            width: 92,
            height: 22,
          ),
        ),
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            AppLocalizations.of(context)!.identityDocumentScreenCatchphrase,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  fontSize: 12,
                  height: 1.5,
                ),
          ),
        ),
      ],
    );
  }
}

class _NextButton extends HookConsumerWidget {
  const _NextButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    final controller = ref.watch(identityDocumentStateScreenProvider.notifier);
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.tradGreen,
        disabledForegroundColor:
            AppColors.tradGreen.withOpacity(0.5).withOpacity(0.38),
        disabledBackgroundColor: AppColors.tradGreen.withOpacity(0.06),
        minimumSize: const Size(240, 48),
      ),
      onPressed: () async {
        // FirebaseAnalyticsログ送信
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.next,
          screenName:
              AppLocalizations.of(context)!.identificationDocumentScreen,
        );

        // ローディング処理
        LoadingDialog.loading(context);

        // NFCチェックNGの場合はダイアログを表示
        if (!await NfcCheck.isNfcAvailable()) {
          controller.changeErrorPattern(ErrorPattern.errorPattern12);
          return;
        } else {
          // マイナンバーカードのイメージボタン押下時処理
          await controller.onTapMyNumberCard();
        }
      },
      child: Text(
        AppLocalizations.of(context)!.next,
        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w700,
            ),
      ),
    );
  }
}

class _TextLinkArrow extends HookConsumerWidget {
  const _TextLinkArrow({
    required this.text,
  });

  final String text;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(identityDocumentStateScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);

    // テキストリンクとアイコンのWidget
    // 1行で表示できない場合は折り返しを行う
    return GestureDetector(
      onTap: () async {
        // FirebaseAnalyticsログ送信
        logController.sendButtonLog(
          buttonName:
              AppLocalizations.of(context)!.identityDocumentScreenNotHaveCard,
          screenName:
              AppLocalizations.of(context)!.identificationDocumentScreen,
        );
        controller.onTapNotHaveCard();
      },
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              style: theme.textTheme.bodyMedium
                  ?.copyWith(color: AppColors.tradGreen),
              text: text,
            ),
            const WidgetSpan(
              child: SizedBox(
                width: 4,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
