import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/ekyc_explanation/ekyc_explanation_screen_notifier.dart';
import 'package:dtp_app/utils/nfc_check.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/content_area.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/web_view/customized/ekyc_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EkycExplanationScreen extends HookConsumerWidget {
  const EkycExplanationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 画面幅
    final double unifiedWidth = MediaQuery.of(context).size.width * 0.9;

    final logController = ref.read(analyticsLogControllerProvider);
    final state = ref.watch(ekycExplanationStateScreenProvider);
    final controller = ref.watch(ekycExplanationStateScreenProvider.notifier);

    // Firebase Screenログ・KARTE Viewイベント送信
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.ekycExplanationScreenId,
            screenName: AppLocalizations.of(context)!.ekycExplanationScreen,
          );
          // 画面描画時にメモリに保持している情報を削除する
          controller.deleteMemoryData();
        });
        return null;
      },
      [],
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 次ページへの遷移
      if (state.nextPageName != null) {
        controller.stateReset();
        // ローディング処理を止める
        LoadingDialog.loadingEnd(context);
        Navigator.of(context).pushNamed(state.nextPageName!);
      }

      // エラーが発生した場合、パターンによって内容を出し分ける
      if (state.errorPattern != null) {
        if (!context.mounted) return;
        // ローディング処理を止める
        LoadingDialog.loadingEnd(context);
        showCommonErrorDialog(
          dialogContext: context,
          errorPattern: state.errorPattern!,
          onPressed1: () {
            controller.stateReset();
            onPressed1(
              context,
              ref,
              state.errorPattern!,
              AppLocalizations.of(context)!.ekycExplanationScreen,
            );
          },
          onPressed2: () {
            controller.stateReset();
            onPressed2(
              context,
              ref,
              state.errorPattern!,
              AppLocalizations.of(context)!.ekycExplanationScreen,
            );
          },
        );
      }
    });

    return PopScope(
      child: ColoredBox(
        color: AppColors.white,
        child: SafeArea(
          top: false,
          child: Scaffold(
            appBar: AppBarForIdentityVerification(
              title: AppLocalizations.of(context)!.identificationAppBarTitle,
              onTapLeadingLogAction: () => logController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.back,
                screenName: AppLocalizations.of(context)!.ekycExplanationScreen,
              ),
              onTapBackButtonAction: () async {
                // アプリバーの戻るボタンが押された場合
                await onTapBack(context);
              },
            ),
            body: Stack(
              children: [
                Image.asset(
                  Assets.bgWaveGrey,
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                ),
                Stack(
                  children: [
                    SingleChildScrollView(
                      physics: const ClampingScrollPhysics(),
                      child: Column(
                        children: <Widget>[
                          ScreenNumber(
                            screenNumber:
                                ScreenIdNumber.ekycExplanationScreenId,
                            color: AppColors.textBlack,
                          ),
                          _ScreenContentContainer(
                            ref: ref,
                            unifiedWidth: unifiedWidth,
                          ),
                          const SizedBox(
                            height: 120,
                          ),
                        ],
                      ),
                    ),
                    Container(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        width: double.infinity,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          boxShadow: [
                            // SafeArea部分にも影が入ってしまうため、SafeAreaの境目に白色の影を追加
                            BoxShadow(
                              offset: const Offset(0, -2),
                              color: AppColors.cardShadow.withOpacity(0.15),
                              blurRadius: 4,
                            ),
                            const BoxShadow(
                              color: AppColors.white,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Column(
                          children: [
                            SizedBox(height: 16),
                            // 次へ進むボタン
                            _NextButton(),
                            SizedBox(height: 16),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> onTapBack(BuildContext context) async {
    if (await NfcCheck.isNfcAvailable()) {
      if (context.mounted) {
        // NFC対応端末の場合 証明書選択画面まで戻る
        Navigator.popUntil(
          context,
          (route) => route.settings.name == RouteNames.identityDocument,
        );
      }
    } else {
      // NFC非対応端末（または 端末のNFC設定オフ）の場合　お手続き番号入力画面へ遷移
      if (context.mounted) {
        Navigator.popUntil(
          context,
          (route) => route.settings.name == RouteNames.identityReferenceNumber,
        );
      }
    }
  }
}

class _ScreenContentContainer extends StatelessWidget {
  const _ScreenContentContainer({
    required this.ref,
    required this.unifiedWidth,
  });

  final WidgetRef ref;
  final double unifiedWidth;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ContentArea(
        context: context,
        minHeight: 288,
        title: AppLocalizations.of(context)!.ekycExplanationTitle,
        widget: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Image.asset(Assets.ekycExplanationImage),
          ],
        ),
      ),
    );
  }
}

class _NextButton extends HookConsumerWidget {
  const _NextButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(ekycExplanationStateScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    return SizedBox(
      width: 240,
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.tradGreen,
          disabledForegroundColor:
              AppColors.tradGreen.withOpacity(0.5).withOpacity(0.38),
          disabledBackgroundColor: AppColors.tradGreen.withOpacity(0.06),
        ),
        onPressed: () async {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.next,
            screenName: AppLocalizations.of(context)!.ekycExplanationScreen,
          );

          // ローディング処理
          LoadingDialog.loading(context);

          // eKYCのWebViewURLを取得する
          final url = await controller.requestEkycUrl();

          if (context.mounted && url.isNotEmpty) {
            // ローディングを閉じる
            LoadingDialog.loadingEnd(context);

            if (!context.mounted) return;
            logController.sendTrack(
              buttonName: AppLocalizations.of(context)!.next,
              screenIdNumber: ScreenIdNumber.ekycExplanationScreenId,
            );
            // eKYCのWebViewを起動
            await ref.watch(ekycWebViewLauncherProvider).call(
                  context,
                  WebViewRequest.launchUrl(
                    initialUrl: url,
                  ),
                );
          }
        },
        child: Text(
          AppLocalizations.of(context)!.next,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w700,
              ),
        ),
      ),
    );
  }
}
