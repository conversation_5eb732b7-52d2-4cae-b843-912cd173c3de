import 'dart:async';

import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';

class AppBarForIdentityVerification extends StatelessWidget
    implements PreferredSizeWidget {
  const AppBarForIdentityVerification({
    super.key,
    required this.onTapLeadingLogAction,
    required this.title,
    this.onTapBackButtonAction,
  });

  final void Function() onTapLeadingLogAction;
  final String title;
  final Future<void> Function()? onTapBackButtonAction;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.tradGreen,
      child: Safe<PERSON>rea(
        child: AppBar(
          backgroundColor: AppColors.white,
          elevation: 0,
          shape: const Border(
            bottom: BorderSide(
              color: AppColors.borderGrayGreen,
            ),
          ),
          leadingWidth: 90,
          leading: GestureDetector(
            onTap: () async {
              // FirebaseAnalyticsログ送信
              onTapLeadingLogAction();

              // eKYC画面の時のだけNUllじゃなくなる
              if (onTapBackButtonAction != null) {
                await onTapBackButtonAction!();
              } else {
                Navigator.pop(context);
              }
            },
            child: Padding(
              padding: const EdgeInsets.only(left: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // SVGとTextのBaseラインがずれているため、調整用のSizedBox
                      const SizedBox(height: 2),
                      SvgPicture.asset(
                        Assets.arrowLeftIcon,
                        width: 16,
                        height: 16,
                        colorFilter: const ColorFilter.mode(
                          AppColors.tradGreen,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    AppLocalizations.of(context)!.back,
                    textAlign: TextAlign.center,
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(color: AppColors.tradGreen),
                  ),
                ],
              ),
            ),
          ),
          title: Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 16,
                  color: AppColors.tradGreen,
                ),
          ),
          centerTitle: true,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}

class AppBarEnteringNameScreen extends StatelessWidget
    implements PreferredSizeWidget {
  const AppBarEnteringNameScreen({
    super.key,
    required this.title,
  });

  final String title;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.tradGreen,
      child: SafeArea(
        child: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: AppColors.white,
          elevation: 0,
          shape: const Border(
            bottom: BorderSide(
              color: AppColors.borderGrayGreen,
            ),
          ),
          title: Text(
            title,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 16,
                  color: AppColors.tradGreen,
                ),
          ),
          centerTitle: true,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}
