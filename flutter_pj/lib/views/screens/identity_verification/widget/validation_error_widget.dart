import 'dart:core';

import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

// デザインは暫定実装
class ValidationMessage extends StatelessWidget {
  const ValidationMessage({
    super.key,
    required this.error,
    this.fieldName = '',
  });

  final ValidationError error;
  final String fieldName;

  @override
  Widget build(BuildContext context) {
    if (!error.hasError) {
      // エラーがない場合は表示しない
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // アイコンとテキストのベースラインを合わせる
                SizedBox(height: 1),
                SvgPicture.asset(
                  Assets.captionListErrorIcon,
                  width: 16,
                  height: 16,
                ),
              ],
            ),
            const SizedBox(width: 6),
            Expanded(
              child: Text(
                error.toErrorMessage(context, fieldName) ?? '',
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(color: AppColors.red),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
