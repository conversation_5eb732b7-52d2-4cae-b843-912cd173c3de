import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 次へボタン
/// 本人確認情報入力の各画面に表示される
class NextButton extends StatelessWidget {
  const NextButton({
    super.key,
    required this.enabled,
    required this.onTap,
  });

  /// ボタンの活性・非活性フラグ
  final bool enabled;

  /// タップ時の処理
  final Future<void> Function() onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 80,
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, -2),
            color: AppColors.nextButtonShadow,
            blurRadius: 4,
          ),
        ],
      ),
      child: Center(
        child: SizedBox(
          height: 48,
          width: 240,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              elevation: 0,
              backgroundColor: AppColors.tradGreen,
              disabledBackgroundColor: AppColors.backgroundGreen,
              foregroundColor: AppColors.white,
              disabledForegroundColor:
                  AppColors.buttonInactiveText.withOpacity(0.55),
              textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
            ),
            onPressed: enabled
                ? () async {
                    await onTap();
                  }
                : null,
            child: Center(
              child: Text(
                AppLocalizations.of(context)!.next,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
