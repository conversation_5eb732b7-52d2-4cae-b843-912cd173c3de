import 'dart:core';

import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TextFieldForName extends StatelessWidget {
  const TextFieldForName({
    super.key,
    required this.controller,
    this.maxLength,
    required this.focusNode,
    required this.error,
    this.fieldWidth = 160,
    this.onChanged,
    this.keyboardType,
    this.title,
    this.hintText,
    this.isForDigit = false,
    this.readOnly = false,
  });

  final TextEditingController controller;
  final Function(String?)? onChanged;
  final double fieldWidth;
  final String? title;
  final String? hintText;
  final int? maxLength;
  final FocusNode focusNode;
  final ValidationError error;
  final TextInputType? keyboardType;
  final bool isForDigit;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    final isEmptyTitle = title == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isEmptyTitle
            ? const SizedBox.shrink()
            : Text(
                title!,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.grayGreen,
                    ),
              ),
        isEmptyTitle ? const SizedBox.shrink() : const SizedBox(height: 8),
        const SizedBox(height: 8),
        SizedBox(
          height: 48,
          width: fieldWidth,
          child: TextFieldForInfoScreen(
            keyboardType: keyboardType,
            focusNode: focusNode,
            hasError: error.hasError,
            maxLength: maxLength,
            hintFontSize: 16,
            controller: controller,
            hintText: hintText ?? '',
            onChanged: onChanged,
            readOnly: readOnly,
          ),
        ),
      ],
    );
  }
}

class TextFieldForPostCode extends StatelessWidget {
  const TextFieldForPostCode({
    super.key,
    required this.controller,
    this.maxLength,
    required this.focusNode,
    required this.error,
    this.fieldWidth = 160,
    this.onChanged,
    this.keyboardType,
    this.title,
    this.hintText,
    this.isForDigit = false,
    this.readOnly = false,
  });

  final TextEditingController controller;
  final Function(String?)? onChanged;
  final double fieldWidth;
  final String? title;
  final String? hintText;
  final int? maxLength;
  final FocusNode focusNode;
  final ValidationError error;
  final TextInputType? keyboardType;
  final bool isForDigit;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    final isEmptyTitle = title == null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isEmptyTitle
            ? const SizedBox.shrink()
            : Text(
                title!,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.grayGreen,
                    ),
              ),
        const SizedBox(height: 8),
        SizedBox(
          height: 48,
          width: fieldWidth,
          child: TextFieldForInfoScreen(
            keyboardType: keyboardType,
            focusNode: focusNode,
            hasError: error.hasError,
            maxLength: maxLength,
            hintFontSize: 16,
            controller: controller,
            hintText: hintText ?? '',
            onChanged: onChanged,
            readOnly: readOnly,
          ),
        ),
      ],
    );
  }
}

class TextFieldForInfoScreen extends StatelessWidget {
  const TextFieldForInfoScreen({
    super.key,
    required this.focusNode,
    required this.controller,
    this.title,
    required this.hintText,
    this.maxLength,
    required this.hasError,
    this.hintFontSize = 16,
    this.fontFamily = FontFamily.notoSansJP,
    this.onChanged,
    this.keyboardType,
    this.isForDigit = false,
    this.readOnly = false,
  });

  final FocusNode focusNode;
  final TextEditingController controller;
  final String? title;
  final String hintText;
  final double hintFontSize;
  final Function(String?)? onChanged;
  final int? maxLength;
  final bool hasError;
  final TextInputType? keyboardType;
  final String fontFamily;
  final bool isForDigit;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    final isEmptyTitle = title == null;
    final inputForMatterText = [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.singleLineFormatter,
    ];
    final inputFormatterDigit = [
      LengthLimitingTextInputFormatter(maxLength),
      FilteringTextInputFormatter.singleLineFormatter,
      FilteringTextInputFormatter.digitsOnly,
    ];
    Color fillColor = AppColors.basicBackgroundLightGray;
    if (readOnly) {
      fillColor = AppColors.borderGrayGreen;
    }
    if (hasError) {
      fillColor = AppColors.lightRed;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isEmptyTitle
            ? const SizedBox.shrink()
            : Text(
                title!,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.grayGreen,
                    ),
              ),
        isEmptyTitle ? const SizedBox.shrink() : const SizedBox(height: 8),
        NotCopyTextFormField(
          keyboardType: keyboardType,
          focusNode: focusNode,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          textAlignVertical: TextAlignVertical.center,
          controller: controller,
          onChanged: onChanged,
          inputFormatters:
              isForDigit ? inputFormatterDigit : inputForMatterText,
          style: readOnly
              ? Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: AppColors.textGrayGreen,
                    fontSize: hintFontSize,
                    fontFamily: fontFamily,
                  )
              : TextStyle(
                  color: hasError ? AppColors.red : AppColors.textBlack,
                  fontFamily: fontFamily,
                ),
          readOnly: readOnly,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: hasError ? AppColors.red : AppColors.borderGrayGreen,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: hasError ? AppColors.red : AppColors.tradGreen,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            fillColor: fillColor,
            hintText: hintText,
            filled: true,
            hintStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: AppColors.textGrayGreen,
                  fontSize: hintFontSize,
                  fontFamily: fontFamily,
                ),
          ),
        ),
      ],
    );
  }
}

class TextFieldForBirthOfDate extends StatelessWidget {
  const TextFieldForBirthOfDate({
    super.key,
    required this.focusNode,
    required this.controller,
    this.title,
    required this.hintText,
    required this.maxLength,
    required this.hasError,
    this.hintFontSize = 16,
    this.fontFamily = FontFamily.notoSansJP,
    this.onChanged,
    this.keyboardType,
    this.isForDigit = false,
  });

  final FocusNode focusNode;
  final TextEditingController controller;
  final String? title;
  final String hintText;
  final double hintFontSize;
  final Function(String?)? onChanged;
  final int maxLength;
  final bool hasError;
  final TextInputType? keyboardType;
  final String fontFamily;
  final bool isForDigit;

  @override
  Widget build(BuildContext context) {
    final isEmptyTitle = title == null;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        isEmptyTitle
            ? const SizedBox.shrink()
            : Text(
                title!,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.grayGreen,
                    ),
              ),
        isEmptyTitle ? const SizedBox.shrink() : const SizedBox(height: 8),
        NotCopyTextFormField(
          keyboardType: keyboardType,
          focusNode: focusNode,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          textAlignVertical: TextAlignVertical.center,
          controller: controller,
          onChanged: onChanged,
          // 配列の中に格納
          inputFormatters: [CustomTextInputFormatter()],
          style: TextStyle(
            color: hasError ? AppColors.red : AppColors.textBlack,
            fontFamily: fontFamily,
          ),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(horizontal: 16),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: hasError ? AppColors.red : AppColors.borderGrayGreen,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(
                color: hasError ? AppColors.red : AppColors.tradGreen,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            fillColor: hasError
                ? AppColors.lightRed
                : AppColors.basicBackgroundLightGray,
            hintText: hintText,
            filled: true,
            hintStyle: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: AppColors.textGrayGreen,
                  fontSize: hintFontSize,
                  fontFamily: fontFamily,
                ),
          ),
          maxLength: maxLength,
          buildCounter: (
            context, {
            required int currentLength,
            required bool isFocused,
            int? maxLength,
          }) {
            return null;
          },
        ),
      ],
    );
  }
}

class CustomTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 入力している時
    if (oldValue.text.length > newValue.text.length && newValue.text.isEmpty) {
      return newValue.copyWith(
        text: '',
        selection: const TextSelection.collapsed(offset: 0),
      );
    } else if (oldValue.text.length > newValue.text.length) {
      return newValue;
    } else if (newValue.text.length == 4) {
      final newText = '${newValue.text}/';
      return oldValue.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    } else if (newValue.text.length == 7) {
      final newText = '${newValue.text}/';
      return oldValue.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    } else if (newValue.text.length == 5) {
      final newText =
          '${newValue.text.substring(0, 4)}/${newValue.text.substring(4)}';
      return oldValue.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    } else if (newValue.text.length == 8) {
      final newText =
          '${newValue.text.substring(0, 7)}/${newValue.text.substring(7)}';
      return oldValue.copyWith(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }

    return newValue;
  }
}
