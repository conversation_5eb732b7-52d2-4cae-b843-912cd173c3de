import 'dart:async';
import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ToLoginButton extends HookConsumerWidget {
  const ToLoginButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(analyticsLogControllerProvider);

    Future<void> sendLogAndResetReferenceNum() async {
      // FirebaseAnalyticsログ送信
      controller.sendButtonLog(
        buttonName: AppLocalizations.of(context)!.navigateToLoginScreen,
        screenName:
            ModalRoute.of(context)!.settings.name == RouteNames.verifiedUser
                ? AppLocalizations.of(context)!.verifiedUser
                : AppLocalizations.of(context)!.accountOpeningSubmitted,
      );
      // ユーザープロパティにお手続き番号を残置させないため
      unawaited(
        ref.read(mdManagerProvider).referenceNumber.delete(),
      );
    }

    return Container(
      width: double.infinity,
      height: 80,
      decoration: const BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.nextButtonShadow,
            blurRadius: 4,
          ),
        ],
      ),
      child: Center(
        child: SizedBox(
          height: 48,
          width: 240,
          child: OutlinedButton(
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              elevation: 0,
              backgroundColor: AppColors.tradGreen,
              disabledBackgroundColor: AppColors.backgroundGreen,
            ),
            onPressed: () async {
              unawaited(sendLogAndResetReferenceNum());
              unawaited(
                Navigator.of(context).pushNamedAndRemoveUntil(
                  RouteNames.homeNavigation,
                  (route) => false,
                  arguments: {'fromAccountOpeningSubmitted': true},
                ),
              );
            },
            child: Center(
              child: Text(
                AppLocalizations.of(context)!.navigateToLoginScreen,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontWeight: FontWeight.w700,
                      color: AppColors.white,
                    ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
