import 'dart:async';
import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 本人情報送信完了画面
class InfoEnteringFinishedScreen extends HookConsumerWidget {
  const InfoEnteringFinishedScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    final refState = ref.watch(identityReferenceNumberScreenProvider);
    final identityVerificationController =
        ref.watch(identityVerificationScreenProvider.notifier);
    final validationStatus = refState.openAccountAppTemp?.validationStatus;
    final isBeneficiary =
        ValidationStatusExt.fromString(validationStatus!).isBeneficiary;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: isBeneficiary
                ? ScreenIdNumber.finishedTosendInfoBeneficiaryScreenId
                : ScreenIdNumber.finishedTosendInfoScreenId,
            screenName: isBeneficiary
                ? AppLocalizations.of(context)!
                    .finishedTosendInfoBeneficiaryScreen
                : AppLocalizations.of(context)!.finishedTosendInfoScreen,
          );
          // 画面描画時にメモリ・セキュアストレージに保持している情報を削除する
          unawaited(
            identityVerificationController.deleteMemoryAndSecureData(),
          );
        });
        return null;
      },
      [],
    );

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: const _AppBar(),
      body: PopScope(
        canPop: false,
        child: _Body(
          isBeneficiary: isBeneficiary,
        ),
      ),
    );
  }
}

class _Body extends StatelessWidget {
  const _Body({
    required this.isBeneficiary,
  });

  // 実質的支配者フラグ
  final bool isBeneficiary;

  @override
  Widget build(BuildContext context) {
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;

    return Stack(
      children: [
        Image.asset(
          Assets.homeLightBackground,
          width: maxWidth,
          height: maxHeight,
          fit: BoxFit.fill,
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ScreenNumber(
              screenNumber: isBeneficiary
                  ? ScreenIdNumber.finishedTosendInfoBeneficiaryScreenId
                  : ScreenIdNumber.finishedTosendInfoScreenId,
            ),
            const SizedBox(height: 16),
            SvgPicture.asset(
              Assets.finishedIcon,
              width: 64,
              height: 64,
            ),
            const SizedBox(height: 8),
            Text(
              AppLocalizations.of(context)!.finishedToSend,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontSize: 24,
                    color: AppColors.tradGreen,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)!.wellDone,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                textAlign: TextAlign.center,
                isBeneficiary
                    ? AppLocalizations.of(context)!.continueOnWebIsBeneficiary
                    : AppLocalizations.of(context)!.continueOnWeb,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ),
            const SizedBox(height: 24),
            Visibility(
              visible: !isBeneficiary,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Image.asset(
                          Assets.continueOnWeb,
                          fit: BoxFit.scaleDown,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.noteSymbol,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontSize: 12,
                                  ),
                        ),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!.continueOnWebNote,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      fontSize: 12,
                                    ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _AppBar extends StatelessWidget implements PreferredSizeWidget {
  const _AppBar();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.tradGreen,
      child: SafeArea(
        child: AppBar(
          backgroundColor: AppColors.white,
          elevation: 0,
          shape: const Border(
            bottom: BorderSide(
              color: AppColors.borderGrayGreen,
            ),
          ),
          title: Text(
            AppLocalizations.of(context)!.finished,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 16,
                  color: AppColors.tradGreen,
                ),
          ),
          centerTitle: true,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);
}
