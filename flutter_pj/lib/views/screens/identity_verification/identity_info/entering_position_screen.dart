import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/next_buttton.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/text_field_for_info_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/validation_error_widget.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 本人情報入力画面(役職名)
class PositionScreen extends HookConsumerWidget {
  const PositionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final controller = ref.watch(identityVerificationScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.positionScreenId,
            screenName: AppLocalizations.of(context)!.positionScreen,
          );
        });
        return null;
      },
      [],
    );

    return PopScope(
      onPopInvokedWithResult: (_, __) => controller.onPopPositionScreen(),
      child: GestureDetector(
        onTap: () {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
            screenName: AppLocalizations.of(context)!.positionScreen,
          );
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: AppColors.white,
          appBar: AppBarForIdentityVerification(
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName: AppLocalizations.of(context)!.positionScreen,
            ),
            title: AppLocalizations.of(context)!.enteringInfo,
          ),
          body: ColoredBox(
            color: AppColors.white,
            child: SafeArea(
              top: false,
              child: Stack(
                children: [
                  Image.asset(
                    Assets.homeLightBackground,
                    height: maxHeight,
                    width: maxWidth,
                    fit: BoxFit.fill,
                  ),
                  Column(
                    children: [
                      _ScrollArea(),
                      NextButton(
                        enabled: state.requiredPositionIsNotNullOrEmpty,
                        onTap: () async {
                          if (controller.validatePositionForm()) {
                            if (context.mounted) {
                              if (state.isEkyc) {
                                await _onTapEkyc(context, ref);
                              } else {
                                await _onTapJPKI(context, ref);
                              }
                            }
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final notifier = ref.read(identityVerificationScreenProvider.notifier);
    final positionController = useTextEditingController();
    final positionFocusNode = useFocusNode();

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ScreenNumber(
              screenNumber: ScreenIdNumber.positionScreenId,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.white,
                  boxShadow: const [
                    BoxShadow(
                      color: AppColors.shadowGray,
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 24),
                      Text(
                        AppLocalizations.of(context)!.enterPosition,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              color: AppColors.tradGreen,
                              fontSize: 20,
                            ),
                      ),
                      const SizedBox(height: 24),
                      TextFieldForInfoScreen(
                        focusNode: positionFocusNode,
                        hasError: state.positionValidationError.hasError,
                        hintFontSize: 16,
                        onChanged: (value) {
                          notifier.updatePosition(value);
                        },
                        controller: positionController,
                        hintText: AppLocalizations.of(context)!.examplePosition,
                      ),
                      const SizedBox(height: 8),
                      ValidationMessage(
                        fieldName: AppLocalizations.of(context)!.position,
                        error: state.positionValidationError,
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Future<void> _onTapJPKI(BuildContext context, WidgetRef ref) async {
  final logController = ref.read(analyticsLogControllerProvider);

  logController.sendButtonLog(
    buttonName: AppLocalizations.of(context)!.next,
    screenName: AppLocalizations.of(context)!.positionScreen,
  );

  // contextがマウントされているかを確認
  if (!context.mounted) return;

  await Navigator.pushNamed(
    context,
    RouteNames.enteringPostCode,
  );
}

Future<void> _onTapEkyc(BuildContext context, WidgetRef ref) async {
  final logController = ref.read(analyticsLogControllerProvider);

  logController.sendButtonLog(
    buttonName: AppLocalizations.of(context)!.next,
    screenName: AppLocalizations.of(context)!.positionScreen,
  );

  // contextがマウントされているかを確認
  if (!context.mounted) return;

  await Navigator.pushNamed(
    context,
    RouteNames.enteringDateOfBirth,
  );
}
