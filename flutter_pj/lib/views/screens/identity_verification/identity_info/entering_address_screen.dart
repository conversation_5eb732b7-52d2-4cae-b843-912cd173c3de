import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_document/identity_document_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/utils/ext/string_ext.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/next_buttton.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/text_field_for_info_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/validation_error_widget.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddressScreen extends HookConsumerWidget {
  const AddressScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(identityVerificationScreenProvider.notifier);
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;
    final logController = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.addressScreenId,
            screenName: AppLocalizations.of(context)!.addressScreen,
          );
        });
        return null;
      },
      [],
    );

    return PopScope(
      onPopInvokedWithResult: (_, __) => controller.onPopAddressScreen(),
      child: GestureDetector(
        onTap: () {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
            screenName: AppLocalizations.of(context)!.addressScreen,
          );
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          backgroundColor: AppColors.white,
          resizeToAvoidBottomInset: false,
          appBar: AppBarForIdentityVerification(
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName: AppLocalizations.of(context)!.addressScreen,
            ),
            title: AppLocalizations.of(context)!.enteringInfo,
          ),
          body: SafeArea(
            top: false,
            child: Stack(
              children: [
                Image.asset(
                  Assets.homeLightBackground,
                  width: maxWidth,
                  height: maxHeight,
                  fit: BoxFit.fill,
                ),
                Column(
                  children: [
                    _ScrollArea(),
                    _NextButton(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 以下はテキストフィールド用のcontroller
    final prefectureController = useTextEditingController();
    final cityNameController = useTextEditingController();
    final addressNumberController = useTextEditingController();
    final postCodeController = useTextEditingController();

    // 以下はtextField用のfocusNode
    final postCodeFocus = useFocusNode();
    final prefectureFocus = useFocusNode();
    final cityNameFocus = useFocusNode();
    final addressNumberFocus = useFocusNode();

    final bottomSpace = MediaQuery.of(context).viewInsets.bottom;

    return Expanded(
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: bottomSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScreenNumber(
                screenNumber: ScreenIdNumber.addressScreenId,
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: AppColors.white,
                    boxShadow: const [
                      BoxShadow(
                        color: AppColors.shadowGray,
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _Address(
                          postCodeController: postCodeController,
                          postCodeFocus: postCodeFocus,
                          prefectureController: prefectureController,
                          prefectureFocus: prefectureFocus,
                          cityNameController: cityNameController,
                          cityNameFocus: cityNameFocus,
                          streetAddressController: addressNumberController,
                          streetAddressFocus: addressNumberFocus,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 80),
            ],
          ),
        ),
      ),
    );
  }
}

class _Address extends HookConsumerWidget {
  const _Address({
    required this.postCodeController,
    required this.postCodeFocus,
    required this.prefectureController,
    required this.prefectureFocus,
    required this.cityNameController,
    required this.cityNameFocus,
    required this.streetAddressController,
    required this.streetAddressFocus,
  });

  final TextEditingController postCodeController;
  final FocusNode postCodeFocus;
  final TextEditingController prefectureController;
  final FocusNode prefectureFocus;
  final TextEditingController cityNameController;
  final FocusNode cityNameFocus;
  final TextEditingController streetAddressController;
  final FocusNode streetAddressFocus;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final notifier = ref.watch(identityVerificationScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);

    // 住所検索APIでエラー発生時はダイアログを表示
    if (state.fetchAddressError != null) {
      Future.microtask(() {
        if (!context.mounted) return;
        notifier.clearFetchAddressError();
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return CommonDialog.ok(
              title: state.fetchAddressError!.code,
              message: state.fetchAddressError!.message ?? '',
              onOkPressed: () {
                // FirebaseAnalyticsログ送信
                logController.sendButtonLog(
                  buttonName:
                      AppLocalizations.of(context)!.clearSearchAddressAPIError,
                  screenName: AppLocalizations.of(context)!.addressScreen,
                );
                // okボタン押下でエラー情報削除
                notifier.clearFetchAddressError();
              },
              isPopAfterOkButtonPress: true,
            );
          },
        );
      });
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.enterAddress,
          style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                color: AppColors.tradGreen,
                fontSize: 20,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.enterLisenceAddress,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 12),
        ),
        const SizedBox(height: 24),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: TextFieldForName(
                focusNode: postCodeFocus,
                title: AppLocalizations.of(context)!.postCode,
                hintText: AppLocalizations.of(context)!.examplePostCode,
                controller: postCodeController,
                fieldWidth: double.infinity,
                error: state.postcodeValidationError,
                keyboardType: TextInputType.number,
                onChanged: (String? value) {
                  notifier.updatePostcode(value, true);
                },
              ),
            ),
            const SizedBox(width: 8),
            FittedBox(
              child: SizedBox(
                height: 48,
                child: AppRectangleButton(
                  label: AppLocalizations.of(context)!.autofill,
                  textColor: state.postcodeValidationError.hasError ||
                          state.postcode.isNullOrEmpty
                      ? AppColors.inactiveText
                      : AppColors.primary,
                  buttonColor: state.postcodeValidationError.hasError ||
                          state.postcode.isNullOrEmpty
                      ? AppColors.backgroundGreen
                      : AppColors.white,
                  borderColor: state.postcodeValidationError.hasError ||
                          state.postcode.isNullOrEmpty
                      ? AppColors.backgroundGreen
                      : AppColors.primary,
                  // 郵便番号のバリデーションエラー発生時(リアルタイム切り替え前は未入力の場合)は自動入力ボタンを非活性に
                  onPressed: state.postcodeValidationError.hasError ||
                          state.postcode.isNullOrEmpty
                      ? () {}
                      : () async {
                          final hasError = notifier.validatePostcodeForm();
                          if (hasError) return;
                          // FirebaseAnalyticsログ送信
                          logController.sendButtonLog(
                            buttonName: AppLocalizations.of(context)!.autofill,
                            screenName:
                                AppLocalizations.of(context)!.addressScreen,
                          );

                          LoadingDialog.loading(context);

                          // 住所検索を実行
                          await notifier.fetchAddress(postcode: state.postcode);

                          final afterState =
                              ref.read(identityVerificationScreenProvider);

                          // 取得した住所があればテキストフィールドに設定
                          // 都道府県
                          prefectureController.text =
                              afterState.prefecture ?? '';
                          // 市区町村
                          cityNameController.text = afterState.city ?? '';
                          // 町名・番地・号・建築名
                          streetAddressController.text =
                              afterState.sectionNumberAndBuildingName ?? '';

                          // 無効な郵便番号であれば、都道府県・市区町村のエラーをクリア
                          if (afterState.postcodeValidationError ==
                              const ValidationError.invalidPostalCodeFormat()) {
                            notifier.clearPrefectureCityError();
                          }

                          if (!context.mounted) return;
                          LoadingDialog.loadingEnd(context);
                        },
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // 郵便番号のバリデーションエラー
        ValidationMessage(
          fieldName: AppLocalizations.of(context)!.postCode,
          error: state.postcodeValidationError,
        ),
        Text(
          AppLocalizations.of(context)!.enterHalfWidthNumberWithoutHyphen,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 12),
        ),
        const SizedBox(height: 24),
        TextFieldForName(
          focusNode: prefectureFocus,
          title: AppLocalizations.of(context)!.prefecture,
          controller: prefectureController,
          fieldWidth: double.infinity,
          hintText: AppLocalizations.of(context)!.examplePrefecture,
          error: state.prefectureCityValidationError,
          onChanged: (String? value) {
            notifier.updatePrefecture(value);
          },
        ),
        // 都道府県のバリデーションエラー
        ValidationMessage(
          fieldName: AppLocalizations.of(context)!.prefecture,
          error: state.prefectureCityValidationError,
        ),
        const SizedBox(height: 24),
        TextFieldForName(
          focusNode: cityNameFocus,
          title: AppLocalizations.of(context)!.cityName,
          controller: cityNameController,
          fieldWidth: double.infinity,
          hintText: AppLocalizations.of(context)!.exampleCityName,
          error: state.cityNameValidationError,
          onChanged: (String? value) {
            notifier.updateCityName(value);
          },
        ),
        // 番地・号などのバリデーションエラー
        ValidationMessage(
          fieldName: AppLocalizations.of(context)!.cityName,
          error: state.cityNameValidationError,
        ),
        const SizedBox(height: 24),
        TextFieldForName(
          focusNode: streetAddressFocus,
          title: AppLocalizations.of(context)!.streetAddress,
          controller: streetAddressController,
          fieldWidth: double.infinity,
          hintText: AppLocalizations.of(context)!.exampleStreetAddress,
          error: state.streetAddressValidationError,
          onChanged: (String? value) {
            notifier.updateStreetAddress(value);
          },
        ),
        // マンション名のバリデーションエラー
        ValidationMessage(
          fieldName: AppLocalizations.of(context)!.streetAddress,
          error: state.streetAddressValidationError,
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}

class _NextButton extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final notifier = ref.watch(identityVerificationScreenProvider.notifier);
    final referenceNumberState =
        ref.watch(identityReferenceNumberScreenProvider);
    final controller = ref.watch(identityVerificationScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final role = ref.watch(identityDocumentStateScreenProvider).userTypeNumber;

    return NextButton(
      enabled: state.requiredAddressIsNotNullOrEmpty,
      onTap: () async {
        final hasError = notifier.validateAddressForm();
        if (hasError) return;
        // FirebaseAnalyticsログ送信
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.next,
          screenName: AppLocalizations.of(context)!.addressScreen,
        );

        LoadingDialog.loadingWithTitle(context);
        // 本人情報送信へ
        await controller.sendIdentityVerificationInfo(
          role: role,
          referenceNumber: referenceNumberState.cacheReferenceNumberText,
          isEkyc: true,
        );
        if (context.mounted) {
          LoadingDialog.loadingEnd(context);
        }
      },
    );
  }
}
