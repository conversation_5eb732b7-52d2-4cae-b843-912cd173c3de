import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/card_reader/card_reader_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_document/identity_document_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/next_buttton.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/text_field_for_info_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/validation_error_widget.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PostCodeScreen extends HookConsumerWidget {
  const PostCodeScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final referenceNumberState =
        ref.watch(identityReferenceNumberScreenProvider);
    final controller = ref.watch(identityVerificationScreenProvider.notifier);
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;

    final logController = ref.read(analyticsLogControllerProvider);
    final role = ref.watch(identityDocumentStateScreenProvider).userTypeNumber;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.postCodeScreenId,
            screenName: AppLocalizations.of(context)!.postCodeScreen,
          );
        });
        return null;
      },
      [],
    );

    return PopScope(
      onPopInvokedWithResult: (_, __) => controller.onPopPostCodeScreen(),
      child: GestureDetector(
        onTap: () {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
            screenName: AppLocalizations.of(context)!.postCodeScreen,
          );
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: AppColors.white,
          appBar: AppBarForIdentityVerification(
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName: AppLocalizations.of(context)!.postCodeScreen,
            ),
            title: AppLocalizations.of(context)!.enteringInfo,
          ),
          body: SafeArea(
            top: false,
            child: Stack(
              children: [
                Image.asset(
                  Assets.homeLightBackground,
                  width: maxWidth,
                  height: maxHeight,
                  fit: BoxFit.fill,
                ),
                Column(
                  children: [
                    _ScrollArea(),
                    NextButton(
                      enabled: state.requiredPostCodeIsNotNullOrEmpty,
                      onTap: () async {
                        // FirebaseAnalyticsログ送信
                        logController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!.next,
                          screenName:
                              AppLocalizations.of(context)!.postCodeScreen,
                        );
                        if (controller.validatePostcodeForm()) {
                          return;
                        }
                        // ローディング開始
                        LoadingDialog.loadingWithTitle(context);
                        // 本人情報送信へ
                        await controller.sendIdentityVerificationInfo(
                          role: role,
                          referenceNumber:
                              referenceNumberState.cacheReferenceNumberText,
                          isEkyc: false,
                        );
                        // ローディング終了
                        if (context.mounted) {
                          LoadingDialog.loadingEnd(context);
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 以下はテキストフィールド用のcontroller
    final postCodeController = useTextEditingController();

    // 以下はtextField用のfocusNode
    final postCodeFocus = useFocusNode();

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ScreenNumber(
              screenNumber: ScreenIdNumber.postCodeScreenId,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.white,
                  boxShadow: const [
                    BoxShadow(
                      color: AppColors.shadowGray,
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _PostCode(
                        postCodeController: postCodeController,
                        postCodeFocus: postCodeFocus,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PostCode extends HookConsumerWidget {
  const _PostCode({
    required this.postCodeController,
    required this.postCodeFocus,
  });

  final TextEditingController postCodeController;
  final FocusNode postCodeFocus;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final notifier = ref.watch(identityVerificationScreenProvider.notifier);
    final cardReaderScreenState = ref.watch(cardReaderScreenProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.enterPostcode,
          style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                color: AppColors.tradGreen,
                fontSize: 20,
              ),
        ),
        const SizedBox(height: 24),
        TextFieldForPostCode(
          focusNode: postCodeFocus,
          title: AppLocalizations.of(context)!.postCode,
          hintText: AppLocalizations.of(context)!.examplePostCode,
          controller: postCodeController,
          fieldWidth: 184,
          error: state.postcodeValidationError,
          keyboardType: TextInputType.number,
          onChanged: (String? value) {
            notifier.updatePostcode(value, true);
          },
        ),
        const SizedBox(height: 8),
        Text(
          AppLocalizations.of(context)!.enterHalfWidthNumberWithoutHyphen,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 12),
        ),
        // 郵便番号のバリデーションエラー
        ValidationMessage(
          fieldName: AppLocalizations.of(context)!.postCode,
          error: state.postcodeValidationError,
        ),
        const SizedBox(height: 24),
        const Divider(
          height: 0,
          thickness: 1,
          color: AppColors.grey200,
        ),
        const SizedBox(height: 24),
        Text(
          AppLocalizations.of(context)!.address,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: AppColors.grayGreen,
                fontWeight: FontWeight.w700,
              ),
        ),
        const SizedBox(height: 16),
        Text(
          cardReaderScreenState.addressBp,
          style: Theme.of(context)
              .textTheme
              .bodySmall!
              .copyWith(fontSize: 16)
              .copyWith(fontWeight: FontWeight.w400),
        ),
        const SizedBox(height: 24),
      ],
    );
  }
}
