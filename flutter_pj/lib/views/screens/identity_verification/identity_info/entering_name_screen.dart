import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/card_reader/card_reader_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/utils/ext/string_ext.dart';
import 'package:dtp_app/utils/katakana_to_hepburn_converter.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/info_finished_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/next_buttton.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/text_field_for_info_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/validation_error_widget.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 本人情報入力画面(姓名)
class NameScreen extends HookConsumerWidget with CommonErrorHandlerMixin {
  const NameScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    /// identityVerificationScreenProviderを購読するWidgetを残すため、下記stateはref.watch固定
    final state = ref.watch(identityVerificationScreenProvider);
    final controller = ref.watch(identityVerificationScreenProvider.notifier);
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.nameScreenId,
            screenName: AppLocalizations.of(context)!.nameScreen,
          );
        });
        return null;
      },
      [],
    );

    state.inputState.maybeWhen(
      sendSuccess: () {
        // 送信処理が成功した場合
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.resetInputState();
          // 即座に画面遷移を実施するとエラーが発生するため、待たせる。
          Future.delayed(const Duration(milliseconds: 10));
          if (!context.mounted) return;
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const InfoEnteringFinishedScreen(),
            ),
          );
        });
      },
      sendFailure: (error) {
        // 送信処理でエラーが発生した場合
        handleError(
          context,
          ref,
          error,
          screenId: ScreenIdNumber.nameScreenId,
          screenName: AppLocalizations.of(context)!.nameScreen,
        ).then((hasError) {
          if (hasError) {
            controller.resetInputState();
          }
        });
      },
      orElse: () {
        // 何もしない
      },
    );

    return PopScope(
      canPop: !state.isEkyc,
      onPopInvokedWithResult: (popResult, _) {
        if (popResult) controller.onPopNameScreen();
      },
      child: GestureDetector(
        onTap: () {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
            screenName: AppLocalizations.of(context)!.nameScreen,
          );
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: AppColors.white,
          appBar: state.isEkyc
              ? AppBarEnteringNameScreen(
                  title: AppLocalizations.of(context)!.enteringInfo,
                )
              : AppBarForIdentityVerification(
                  onTapLeadingLogAction: () => logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.back,
                    screenName: AppLocalizations.of(context)!.nameScreen,
                  ),
                  title: AppLocalizations.of(context)!.enteringInfo,
                ) as PreferredSizeWidget,
          body: ColoredBox(
            color: AppColors.white,
            child: SafeArea(
              top: false,
              child: Stack(
                children: [
                  Image.asset(
                    Assets.homeLightBackground,
                    width: maxWidth,
                    height: maxHeight,
                    fit: BoxFit.fill,
                  ),
                  Column(
                    children: [
                      _ScrollArea(),
                      _NextButton(),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(identityVerificationScreenProvider.notifier);
    final state = ref.watch(identityVerificationScreenProvider);
    final cardReaderScreenState = ref.watch(cardReaderScreenProvider);

    // 以下はテキストフィールド用のcontroller
    final seiKanjiController = useTextEditingController();
    final meiKanjiController = useTextEditingController();
    final seiController = useTextEditingController();
    final meiController = useTextEditingController();
    final lastNameController = useTextEditingController();
    final firstNameController = useTextEditingController();

    // 以下はtextField用のfocusNode
    final seiKanjiFocus = useFocusNode();
    final meiKanjiFocus = useFocusNode();
    final seiFocus = useFocusNode();
    final meiFocus = useFocusNode();
    final lastNameFocus = useFocusNode();
    final firstNameFocus = useFocusNode();

    // 姓（カタカナ）が編集されたとき、姓（ローマ字）を自動入力
    // フォーカスが外れたときに実行
    seiFocus.addListener(() {
      final sei = seiController.text;
      final hasNoError = !(notifier.validateFamilyNameKana(sei)).hasError;
      if (!seiFocus.hasFocus && lastNameController.text.isEmpty && hasNoError) {
        final romanSei = KatakanaToHepburn.convertToHepburn(sei);
        lastNameController.text = romanSei;
        notifier.updateFamilyNameAlphabetic(romanSei);
      }
    });

    // 名（カタカナ）が編集されたとき、名（ローマ字）を自動入力
    // フォーカスが外れたときに実行
    meiFocus.addListener(() {
      final mei = meiController.text;
      final hasNoError = !(notifier.validateGivenNameKana(mei)).hasError;
      if (!meiFocus.hasFocus &&
          firstNameController.text.isEmpty &&
          hasNoError) {
        final romanMei = KatakanaToHepburn.convertToHepburn(mei);
        firstNameController.text = romanMei;
        notifier.updateGivenNameAlphabetic(romanMei);
      }
    });

    /// JPKI氏名 変換処理
    String formatName(String input) =>
        input.removeBeforeUnderscore.removeBracketsAndContents.parseSpace;

    final bottomSpace = MediaQuery.of(context).viewInsets.bottom;

    return Expanded(
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Padding(
          padding: EdgeInsets.only(bottom: bottomSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScreenNumber(
                screenNumber: ScreenIdNumber.nameScreenId,
              ),
              const SizedBox(height: 16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: AppColors.white,
                    boxShadow: const [
                      BoxShadow(
                        color: AppColors.shadowGray,
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 24),
                        Text(
                          state.isEkyc
                              ? AppLocalizations.of(context)!
                                  .ekycEnteringNameMessage
                              : AppLocalizations.of(context)!
                                  .jpkiEnteringNameMessage,
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: AppColors.tradGreen,
                                    fontSize: 20,
                                  ),
                          softWrap: true,
                        ),
                        Visibility(
                          visible: !state.isEkyc,
                          child: Column(
                            children: [
                              const SizedBox(height: 24),
                              Text(
                                AppLocalizations.of(context)!.name,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      fontWeight: FontWeight.w700,
                                      color: AppColors.tradGreen,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: !state.isEkyc,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 24),
                              Text(
                                formatName(cardReaderScreenState.nameBp),
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      fontWeight: FontWeight.w400,
                                    ),
                              ),
                              const SizedBox(height: 24),
                              const Divider(
                                height: 0,
                                thickness: 1,
                                color: AppColors.grey200,
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: state.isEkyc,
                          child: Column(
                            children: [
                              SizedBox(height: 8),
                              Text(
                                AppLocalizations.of(context)!
                                    .enterRegisteredNameMessage,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(fontSize: 12),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                        _NameForm(
                          isEkyc: state.isEkyc,
                          seiKanjiFocus: seiKanjiFocus,
                          meiKanjiFocus: meiKanjiFocus,
                          seiFocus: seiFocus,
                          meiFocus: meiFocus,
                          firstNameFocus: firstNameFocus,
                          lastNameFocus: lastNameFocus,
                          seiKanjiController: seiKanjiController,
                          meiKanjiController: meiKanjiController,
                          seiController: seiController,
                          meiController: meiController,
                          firstNameController: firstNameController,
                          lastNameController: lastNameController,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}

class _NameForm extends HookConsumerWidget {
  const _NameForm({
    required this.isEkyc,
    required this.seiKanjiFocus,
    required this.meiKanjiFocus,
    required this.seiFocus,
    required this.meiFocus,
    required this.firstNameFocus,
    required this.lastNameFocus,
    required this.seiKanjiController,
    required this.meiKanjiController,
    required this.seiController,
    required this.meiController,
    required this.firstNameController,
    required this.lastNameController,
  });

  final bool isEkyc;
  final FocusNode seiKanjiFocus;
  final FocusNode meiKanjiFocus;
  final FocusNode seiFocus;
  final FocusNode meiFocus;
  final FocusNode lastNameFocus;
  final FocusNode firstNameFocus;
  final TextEditingController seiKanjiController;
  final TextEditingController meiKanjiController;
  final TextEditingController seiController;
  final TextEditingController meiController;
  final TextEditingController lastNameController;
  final TextEditingController firstNameController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final notifier = ref.watch(identityVerificationScreenProvider.notifier);
    const maxLengthKanji = 28;
    // テキストフィールドの幅を決定する変数
    // 固定値を設定するとオーバーフローの恐れがあるため
    // 端末幅からpaddingや他要素を引いた動的な値を用いる
    final textFieldWidth = (MediaQuery.of(context).size.width - 72) / 2;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // eKYCの時のみ表示
        Visibility(
          visible: isEkyc,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFieldForName(
                    fieldWidth: textFieldWidth,
                    focusNode: seiKanjiFocus,
                    maxLength: maxLengthKanji,
                    onChanged: (value) {
                      notifier.updateFamilyName(value);
                    },
                    title: AppLocalizations.of(context)!.lastNameKanji,
                    controller: seiKanjiController,
                    hintText: AppLocalizations.of(context)!.exampleSeiKanji,
                    error: state.familyNameValidationError,
                  ),
                  const SizedBox(width: 7),
                  TextFieldForName(
                    fieldWidth: textFieldWidth,
                    focusNode: meiKanjiFocus,
                    maxLength: maxLengthKanji,
                    onChanged: (value) {
                      notifier.updateGivenName(value);
                    },
                    title: AppLocalizations.of(context)!.firstNameKanji,
                    controller: meiKanjiController,
                    hintText: AppLocalizations.of(context)!.exampleMeiKanji,
                    error: state.givenNameValidationError,
                  ),
                ],
              ),
              // 姓のエラー表示
              ValidationMessage(
                fieldName: AppLocalizations.of(context)!.lastNameKanji,
                error: state.familyNameValidationError,
              ),
              // 名のエラー表示
              Visibility(
                // 姓にてエラー表示されている際にはメイのエラーは表示させない
                visible: !state.familyNameValidationError.hasError,
                child: ValidationMessage(
                  fieldName: AppLocalizations.of(context)!.firstNameKanji,
                  error: state.givenNameValidationError,
                ),
              ),
              const SizedBox(height: 24),
              const Divider(
                height: 0,
                thickness: 1,
                color: AppColors.grey200,
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFieldForName(
              fieldWidth: textFieldWidth,
              focusNode: seiFocus,
              onChanged: (value) {
                notifier.updateFamilyNameKana(value);
              },
              title: AppLocalizations.of(context)!.surnameKana,
              controller: seiController,
              hintText: AppLocalizations.of(context)!.exampleSei,
              error: state.familyNameKanaValidationError,
            ),
            const SizedBox(width: 7),
            TextFieldForName(
              fieldWidth: textFieldWidth,
              focusNode: meiFocus,
              onChanged: (value) {
                notifier.updateGivenNameKana(value);
              },
              title: AppLocalizations.of(context)!.firstNameKana,
              controller: meiController,
              hintText: AppLocalizations.of(context)!.exampleMei,
              error: state.givenNameKanaValidationError,
            ),
          ],
        ),
        // セイのエラー表示
        ValidationMessage(
          fieldName: AppLocalizations.of(context)!.surnameKana,
          error: state.familyNameKanaValidationError,
        ),
        // メイのエラー表示
        Visibility(
          // セイにてエラー表示されている際にはメイのエラーは表示させない
          visible: !state.familyNameKanaValidationError.hasError,
          child: ValidationMessage(
            fieldName: AppLocalizations.of(context)!.firstNameKana,
            error: state.givenNameKanaValidationError,
          ),
        ),
        const SizedBox(height: 24),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            const Divider(
              height: 0,
              thickness: 1,
              color: AppColors.grey200,
            ),
            const SizedBox(height: 24),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _CheckBox(
                  onChangedFlag: () {
                    notifier.updateToggleEditable();
                  },
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    AppLocalizations.of(context)!.checkBoxMessage +
                        AppLocalizations.of(context)!.checkBoxMessage2 +
                        AppLocalizations.of(context)!.checkBoxMessage3,
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(fontSize: 16),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextFieldForName(
                  fieldWidth: textFieldWidth,
                  focusNode: lastNameFocus,
                  onChanged: (value) {
                    notifier.updateFamilyNameAlphabetic(value);
                  },
                  title: AppLocalizations.of(context)!.lastName,
                  controller: lastNameController,
                  hintText: AppLocalizations.of(context)!.exampleLastName,
                  error: state.familyNameAlphabeticValidationError,
                  readOnly: !state.isNameEditable,
                ),
                const SizedBox(width: 7),
                TextFieldForName(
                  fieldWidth: textFieldWidth,
                  focusNode: firstNameFocus,
                  onChanged: (String? value) {
                    notifier.updateGivenNameAlphabetic(value);
                  },
                  title: AppLocalizations.of(context)!.firstName,
                  controller: firstNameController,
                  hintText: AppLocalizations.of(context)!.exampleFirstName,
                  error: state.givenNameAlphabeticValidationError,
                  readOnly: !state.isNameEditable,
                ),
              ],
            ),
            const SizedBox(height: 8),
            // lastNameのバリデーションエラー
            ValidationMessage(
              fieldName: AppLocalizations.of(context)!.lastName,
              error: state.familyNameAlphabeticValidationError,
            ),
            // firstNameのバリデーションエラー
            // firstNameがエラーの際にはLastNameのエラーは表示させない
            Visibility(
              visible: !state.familyNameAlphabeticValidationError.hasError,
              child: ValidationMessage(
                fieldName: AppLocalizations.of(context)!.firstName,
                error: state.givenNameAlphabeticValidationError,
              ),
            ),
            const SizedBox(height: 24),
          ],
        ),
      ],
    );
  }
}

class _CheckBox extends HookConsumerWidget {
  const _CheckBox({
    required this.onChangedFlag,
  });

  final void Function() onChangedFlag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final controller = ref.read(analyticsLogControllerProvider);

    String getAssetName() {
      if (state.isNameEditable) {
        return Assets.checkGreenIcon;
      } else {
        return Assets.checkGrayIcon;
      }
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            controller.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.switchIsEnglishEditMode,
              screenName: AppLocalizations.of(context)!.nameScreen,
            );
            onChangedFlag();
          },
          child: SizedBox(
            width: 24,
            height: 24,
            child: SvgPicture.asset(getAssetName()),
          ),
        ),
      ],
    );
  }
}

class _NextButton extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final controller = ref.read(identityVerificationScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);

    return NextButton(
      enabled: state.requiredNameFormIsNotNullOrEmpty,
      onTap: () async {
        // バリデーションチェックを実行
        if (controller.validateNameForm()) {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.next,
            screenName: AppLocalizations.of(context)!.nameScreen,
          );
          // アンマウントの場合には早期リターン
          if (!context.mounted) return;
          await Navigator.of(context).pushNamed(
            RouteNames.enteringPosition,
          );
        }
      },
    );
  }
}
