import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/next_buttton.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/text_field_for_info_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/validation_error_widget.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 本人情報入力画面(生年月日)
class DateOfBirthScreen extends HookConsumerWidget {
  const DateOfBirthScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final controller = ref.watch(identityVerificationScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.dateOfBirthScreenId,
            screenName: AppLocalizations.of(context)!.dateOfBirthScreen,
          );
        });
        return null;
      },
      [],
    );

    return PopScope(
      onPopInvokedWithResult: (_, __) => controller.onPopDateOfBirthScreen(),
      child: GestureDetector(
        onTap: () {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
            screenName: AppLocalizations.of(context)!.dateOfBirthScreen,
          );
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: AppColors.white,
          appBar: AppBarForIdentityVerification(
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName: AppLocalizations.of(context)!.dateOfBirthScreen,
            ),
            title: AppLocalizations.of(context)!.enteringInfo,
          ),
          body: ColoredBox(
            color: AppColors.white,
            child: SafeArea(
              top: false,
              child: Stack(
                children: [
                  Image.asset(
                    Assets.homeLightBackground,
                    width: maxWidth,
                    height: maxHeight,
                    fit: BoxFit.fill,
                  ),
                  Column(
                    children: [
                      _ScrollArea(),
                      NextButton(
                        enabled: state.requiredBirthDateIsNotNullOrEmpty,
                        onTap: () async {
                          // 生年月日のバリデーション
                          if (controller.validateBirthDateForm()) {
                            // FirebaseAnalyticsログ送信
                            logController.sendButtonLog(
                              buttonName: AppLocalizations.of(context)!.next,
                              screenName: AppLocalizations.of(context)!
                                  .dateOfBirthScreen,
                            );

                            if (!context.mounted) return;

                            // eMail入力画面へ
                            await Navigator.pushNamed(
                              context,
                              RouteNames.enteringAddress,
                              arguments: {'isEkyc': true},
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityVerificationScreenProvider);
    final notifier = ref.read(identityVerificationScreenProvider.notifier);
    final dateOfBirthController = useTextEditingController();
    final dateOfBirthFocusNode = useFocusNode();

    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ScreenNumber(
              screenNumber: ScreenIdNumber.dateOfBirthScreenId,
            ),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.white,
                  boxShadow: const [
                    BoxShadow(
                      color: AppColors.shadowGray,
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 24),
                      Text(
                        AppLocalizations.of(context)!.enterDateOfBirth,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                              color: AppColors.tradGreen,
                              fontSize: 20,
                            ),
                      ),
                      const SizedBox(height: 24),
                      TextFieldForBirthOfDate(
                        keyboardType: TextInputType.number,
                        focusNode: dateOfBirthFocusNode,
                        hasError: state.birthDateYearValidationError.hasError ||
                            state.birthDateMonthValidationError.hasError ||
                            state.birthDateDayValidationError.hasError,
                        maxLength: 10,
                        hintFontSize: 16,
                        onChanged: (value) {
                          if (value == null) return;
                          String year = '';
                          String month = '';
                          String day = '';
                          // 年の代入
                          if (value.length >= 4) {
                            year = value.substring(0, 4);
                          }
                          // 月の代入
                          if (value.length >= 7) {
                            month = value.substring(5, 7);
                          }
                          // 日付の代入
                          if (value.length >= 10) {
                            day = value.substring(8, 10);
                          }
                          notifier.updateBirthDateYear(year);
                          notifier.updateBirthDateMonth(month);
                          notifier.updateBirthDateDay(day);
                        },
                        controller: dateOfBirthController,
                        hintText: AppLocalizations.of(context)!.dateOfBirth,
                      ),
                      const SizedBox(height: 8),
                      ValidationMessage(
                        fieldName: AppLocalizations.of(context)!.year,
                        error: state.birthDateYearValidationError,
                      ),
                      Visibility(
                        // 年にてエラー表示されている際には月のエラーは表示させない
                        visible: !state.birthDateYearValidationError.hasError,
                        child: ValidationMessage(
                          fieldName: AppLocalizations.of(context)!.month,
                          error: state.birthDateMonthValidationError,
                        ),
                      ),
                      Visibility(
                        // 年か月にてエラー表示されている際には日のエラーは表示させない
                        visible: !state.birthDateYearValidationError.hasError &&
                            !state.birthDateMonthValidationError.hasError,
                        child: ValidationMessage(
                          fieldName: AppLocalizations.of(context)!.day,
                          error: state.birthDateDayValidationError,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
