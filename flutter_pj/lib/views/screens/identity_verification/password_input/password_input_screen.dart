import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/password_input/password_input_screen_notifier.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/components/content_area.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PasswordInputScreen extends HookConsumerWidget {
  const PasswordInputScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final passwordTextController = useTextEditingController();
    final provider = ref.watch(passwordInputScreenProvider.notifier);
    final state = ref.watch(passwordInputScreenProvider);
    final logController = ref.read(analyticsLogControllerProvider);

    // 画面幅
    final double unifiedWidth = MediaQuery.of(context).size.width * 0.9;

    // Firebase Screenログ・KARTE Viewイベント送信
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.passwordInputScreenId,
            screenName: AppLocalizations.of(context)!.passwordInputScreen,
          );
        });
        return null;
      },
      [],
    );

    return GestureDetector(
      // キーボード以外をタップした際にフォーカスを外し、キーボードを下げる
      onTap: () {
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
          screenName: AppLocalizations.of(context)!.passwordInputScreen,
        );
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: ColoredBox(
        color: AppColors.white,
        child: SafeArea(
          top: false,
          child: Scaffold(
            // キーボード表示時に画面に被せる形で表示するよう指定
            resizeToAvoidBottomInset: false,

            appBar: AppBarForIdentityVerification(
              title: AppLocalizations.of(context)!.identificationAppBarTitle,
              onTapLeadingLogAction: () => logController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.back,
                screenName: AppLocalizations.of(context)!.passwordInputScreen,
              ),
            ),
            body: Stack(
              children: [
                Image.asset(
                  Assets.bgWaveGrey,
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                ),
                SingleChildScrollView(
                  child: Column(
                    children: [
                      ScreenNumber(
                        screenNumber: ScreenIdNumber.passwordInputScreenId,
                        color: AppColors.textBlack,
                      ),
                      ContentArea(
                        context: context,
                        title: AppLocalizations.of(context)!
                            .enterSignatureCertPassword,
                        widget: PasswordInputWidget(
                          ref: ref,
                          state: state,
                          provider: provider,
                          passwordTextController: passwordTextController,
                        ),
                      ),
                      const SizedBox(
                        height: 120,
                      ),
                    ],
                  ),
                ),
                Container(
                  alignment: Alignment.bottomCenter,
                  child: Container(
                    width: double.infinity,
                    height: 120,
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      boxShadow: [
                        // SafeArea部分にも影が入ってしまうため、SafeAreaの境目に白色の影を追加
                        BoxShadow(
                          offset: const Offset(0, -2),
                          color: AppColors.cardShadow.withOpacity(0.15),
                          blurRadius: 4,
                        ),
                        const BoxShadow(
                          offset: Offset(0, 2),
                          color: AppColors.white,
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        const SizedBox(height: 16),
                        // 次へ進むボタン
                        _NextButton(
                          unifiedWidth: unifiedWidth,
                          provider: provider,
                          state: state,
                          passwordTextController: passwordTextController,
                        ),
                        const SizedBox(height: 16),
                        // 他の方法で本人確認するボタン
                        _OtherIdentityVerification(
                          passwordTextController: passwordTextController,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _PasswordTextForm extends HookConsumerWidget {
  const _PasswordTextForm({
    required this.unifiedWidth,
    required this.passwordTextController,
    required this.state,
    required this.provider,
  });

  final double unifiedWidth;
  final TextEditingController passwordTextController;
  final PasswordInputScreenState state;
  final PasswordInputScreenNotifier provider;

  // エラー時のテキストフィールドのボーダースタイル
  final errorTextBorder = const OutlineInputBorder(
    borderSide: BorderSide(
      color: AppColors.red, // エラー時のテキストフィールドのボーダーカラー
      width: 1.0,
    ),
  );

  // 通常時のテキストフィールドのボーダースタイル
  final normalTextBorder = const OutlineInputBorder(
    borderSide: BorderSide(
      color: AppColors.borderGrayGreen, // 通時のテキストフィールドのボーダーカラー
      width: 1.0,
    ),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: unifiedWidth),
      child: NotCopyTextFormField(
        controller: passwordTextController,
        obscureText: state.isObscure,
        obscuringCharacter: '●',
        onChanged: (text) {
          // パスワード非表示の場合、確定タイミングがないため、onChangedでバリデーション
          if (state.isObscure) {
            runValidation(
              passwordTextController.text,
              passwordTextController,
              provider,
            );
          }
        },
        onFieldSubmitted: (text) {
          // パスワード表示中の場合、確定したタイミング(onFieldSubmitted))でバリデーション
          if (!state.isObscure) {
            runValidation(
              passwordTextController.text,
              passwordTextController,
              provider,
            );
          }
        },
        onTapOutside: (event) {
          // 入力未確定中にフォーム外をタップした場合
          runValidation(
            passwordTextController.text,
            passwordTextController,
            provider,
          );
        },
        inputFormatters: [LengthLimitingTextInputFormatter(16)],
        style: provider.isValidationError() && !state.isFirstValidation
            ? Theme.of(context)
                .textTheme
                .bodyMedium!
                .copyWith(color: AppColors.red)
            : Theme.of(context).textTheme.bodyMedium,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
          filled: true,
          fillColor: provider.isValidationError() && !state.isFirstValidation
              ? AppColors.lightRed
              : AppColors.grey100,
          enabledBorder:
              provider.isValidationError() && !state.isFirstValidation
                  ? errorTextBorder
                  : normalTextBorder,
          focusedBorder:
              provider.isValidationError() && !state.isFirstValidation
                  ? errorTextBorder
                  : normalTextBorder,
          suffixIcon: IconButton(
            icon: SvgPicture.asset(
              state.isObscure ? Assets.invisibleIcon : Assets.visibleIcon,
              colorFilter: ColorFilter.mode(
                provider.isValidationError() && !state.isFirstValidation
                    ? AppColors.red
                    : AppColors.tradGreen,
                BlendMode.srcIn,
              ),
            ),
            onPressed: () {
              // FirebaseAnalyticsログ送信
              logController.sendButtonLog(
                buttonName:
                    AppLocalizations.of(context)!.togglePasswordVisibility,
                screenName: AppLocalizations.of(context)!.passwordInputScreen,
              );
              provider.setIsObscure(!state.isObscure);
            },
          ),
        ),
      ),
    );
  }
}

void runValidation(
  String text,
  TextEditingController passwordTextController,
  PasswordInputScreenNotifier passwordInputScreenNotifier,
) {
  final upperText = text.toUpperCase();
  passwordTextController.text = upperText;
  final validationResult =
      passwordInputScreenNotifier.validatePassword(upperText);
  passwordInputScreenNotifier.setIsValidationResult(validationResult);
}

class _ValidationErrorMessage extends StatelessWidget {
  const _ValidationErrorMessage({
    required this.unifiedWidth,
  });

  final double unifiedWidth;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 8),
        Center(
          child: SizedBox(
            width: unifiedWidth,
            child: Wrap(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Column(
                      children: [
                        const SizedBox(
                          height: 6,
                        ),
                        SvgPicture.asset(
                          Assets.alertIcon,
                          width: 12,
                          height: 11,
                        ),
                      ],
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)!.passwordComplexityWarning,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: AppColors.red,
                            ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _Tips extends StatelessWidget {
  const _Tips({
    required this.unifiedWidth,
  });

  final double unifiedWidth;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            child: FittedBox(
              fit: BoxFit.fitWidth,
              child: Row(
                children: [
                  RichText(
                    text: TextSpan(
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 12,
                            height: 1.5,
                          ),
                      children: [
                        TextSpan(
                          text: AppLocalizations.of(context)!
                              .passwordComplexityRequirementStart,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontSize: 12,
                                    height: 1.5,
                                  ),
                        ),
                        TextSpan(
                          text: AppLocalizations.of(context)!
                              .passwordComplexityRequirementMiddle,
                          style:
                              Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 12,
                                    height: 1.5,
                                  ),
                        ),
                        TextSpan(
                          text: AppLocalizations.of(context)!
                              .passwordComplexityRequirementEnd,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(
            height: 8,
          ),
          SizedBox(
            child: Text(
              AppLocalizations.of(context)!.notFourDigitCertPassword,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontSize: 12,
                    height: 1.5,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}

class PasswordInputWidget extends StatelessWidget {
  const PasswordInputWidget({
    super.key,
    required this.ref,
    required this.state,
    required this.provider,
    required this.passwordTextController,
  });

  final WidgetRef ref;
  final PasswordInputScreenState state;
  final TextEditingController passwordTextController;
  final PasswordInputScreenNotifier provider;

  @override
  Widget build(BuildContext context) {
    // 画面幅
    final double unifiedWidth = MediaQuery.of(context).size.width * 0.9;

    return Column(
      children: [
        const SizedBox(height: 16),
        // パスワード入力フィールド
        _PasswordTextForm(
          unifiedWidth: unifiedWidth,
          passwordTextController: passwordTextController,
          state: state,
          provider: provider,
        ),

        // バリデーションエラーがある場合の表示テキスト
        if (provider.isValidationError() && !state.isFirstValidation)
          _ValidationErrorMessage(unifiedWidth: unifiedWidth),
        if (provider.isValidationError() && !state.isFirstValidation)
          const SizedBox(height: 8),

        const SizedBox(
          height: 8,
        ),
        // パスワード入力についての補足テキスト
        _Tips(unifiedWidth: unifiedWidth),

        const SizedBox(height: 16),
        // パスワードを忘れた場合のリンク
        _ForgetPassword(
          ref: ref,
          unifiedWidth: unifiedWidth,
        ),
      ],
    );
  }
}

class _ForgetPassword extends StatelessWidget {
  const _ForgetPassword({
    required this.ref,
    required this.unifiedWidth,
  });

  final WidgetRef ref;
  final double unifiedWidth;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: unifiedWidth,
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: AppLocalizations.of(context)!.passwordForgotTitle,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.textBlack,
                      height: 1.5,
                    ),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.here,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                      decoration: TextDecoration.underline,
                      decorationColor: AppColors.primary,
                      height: 1.5,
                    ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    launchURL(context, ref);
                  },
              ),
              WidgetSpan(
                child: GestureDetector(
                  onTap: () {
                    launchURL(context, ref);
                  },
                  child: const SizedBox(
                    width: 4,
                  ),
                ),
              ),
              WidgetSpan(
                child: GestureDetector(
                  onTap: () {
                    launchURL(context, ref);
                  },
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        Assets.actionIcon,
                        width: 16,
                        height: 16,
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 外部URLを開くための関数
  Future<void> launchURL(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final logController = ref.read(analyticsLogControllerProvider);
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName: AppLocalizations.of(context)!.here,
      screenName: AppLocalizations.of(context)!.passwordInputScreen,
    );
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return CommonDialog.okCancel(
          title: AppLocalizations.of(context)!.moveToExternalSite,
          message: AppLocalizations.of(context)!.confirmExternalSiteNavigation,
          onOkPressed: () async {
            // FirebaseAnalyticsログ送信
            logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.yes,
              screenName: AppLocalizations.of(context)!.passwordInputScreen,
            );
            if (!context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    AppLocalizations.of(context)!.urlOpenErrorMessage,
                  ),
                ),
              );
              return;
            }
            // パスワード忘れのページを開く
            await ref.watch(urlLauncherProvider).launchLinkId(
                  LinkIds.forgotPassword,
                  AppLocalizations.of(context)!.yes,
                  ScreenIdNumber.passwordInputScreenId,
                );
          },
          onCancelPressed: () async {
            // FirebaseAnalyticsログ送信
            logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.no,
              screenName: AppLocalizations.of(context)!.passwordInputScreen,
            );
          },
          okButtonText: AppLocalizations.of(context)!.yes,
          cancelButtonText: AppLocalizations.of(context)!.no,
        );
      },
    );
  }
}

class _NextButton extends HookConsumerWidget {
  const _NextButton({
    required this.unifiedWidth,
    required this.provider,
    required this.state,
    required this.passwordTextController,
  });

  final double unifiedWidth;
  final PasswordInputScreenNotifier provider;
  final PasswordInputScreenState state;
  final TextEditingController passwordTextController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontWeight: FontWeight.w700,
            ),
        foregroundColor: AppColors.white,
        backgroundColor: AppColors.tradGreen,
        disabledForegroundColor: AppColors.buttonInactiveText.withOpacity(0.55),
        disabledBackgroundColor: AppColors.backgroundGreen,
        minimumSize: const Size(240, 48),
      ),
      onPressed: provider.isValidationComplete() ||
              (passwordTextController.text.isNotEmpty &&
                  state.isFirstValidation)
          ? () async {
              // FirebaseAnalyticsログ送信
              logController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.next,
                screenName: AppLocalizations.of(context)!.passwordInputScreen,
              );
              // 初回入力の場合、このタイミングでバリデーション処理を実施
              if (state.isFirstValidation) {
                runValidation(
                  passwordTextController.text,
                  passwordTextController,
                  provider,
                );
                // バリデーションエラーになっていたら、エラーメッセージを表示させつつ、初回入力フラグを更新する
                if (!provider.isValidationComplete()) {
                  provider.setIsFirstValidation(false);
                  return;
                }
              }
              // NavigatorのBuildContextをawait処理発生前に取得
              final navigator = Navigator.of(
                context,
              );
              await provider.setJpkiPassword(
                password: passwordTextController.text,
              );
              FocusManager.instance.primaryFocus?.unfocus();

              await navigator.pushNamed(
                RouteNames.cardReader,
              );
            }
          : null,
      child: Text(
        AppLocalizations.of(context)!.next,
      ),
    );
  }
}

class _OtherIdentityVerification extends HookConsumerWidget {
  const _OtherIdentityVerification({
    required this.passwordTextController,
  });

  final TextEditingController passwordTextController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return Center(
      child: TextButton(
        style: CustomButtonStyle.textButtonStyle,
        onPressed: () {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName:
                AppLocalizations.of(context)!.otherIdentityVerificationMethod,
            screenName: AppLocalizations.of(context)!.passwordInputScreen,
          );
          Navigator.pushNamedAndRemoveUntil(
            context,
            RouteNames.ekycExplanation,
            (route) => route.settings.name == RouteNames.identityDocument,
          );
        },
        child: Text(
          AppLocalizations.of(context)!.otherIdentityVerificationMethod,
        ),
      ),
    );
  }
}
