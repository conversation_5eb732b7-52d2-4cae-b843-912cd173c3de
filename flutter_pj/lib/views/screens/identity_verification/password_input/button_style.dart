import 'package:flutter/material.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/theme.dart';

class CommonButtonStyles {
  static final ButtonStyle elevatedButtonStyle = ElevatedButton.styleFrom(
    elevation: 0,
    backgroundColor: AppColors.tradGreen,
    minimumSize: const Size(343, 48),
    textStyle: theme.textTheme.bodyLarge
        ?.copyWith(color: AppColors.white, fontSize: 16),
  );

  static final ButtonStyle textButtonStyle = TextButton.styleFrom(
    padding: EdgeInsets.zero,
    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    textStyle: theme.textTheme.bodyMedium,
  ).copyWith(
    foregroundColor: WidgetStateProperty.all(AppColors.textBlack),
  );

  static final ButtonStyle elevatedButtonStyleMedium = ElevatedButton.styleFrom(
    elevation: 0,
    backgroundColor: AppColors.tradGreen,
    minimumSize: const Size(240, 48),
    textStyle: theme.textTheme.bodyLarge
        ?.copyWith(color: AppColors.white, fontSize: 16),
  );
}

class CustomButtonStyle {
  static ButtonStyle textButtonStyle = TextButton.styleFrom(
    minimumSize: const Size(176, 24),
    padding: EdgeInsets.zero,
    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
    textStyle: theme.textTheme.bodyMedium,
  ).copyWith(
    foregroundColor: WidgetStateProperty.all(AppColors.tradGreen),
  );
}
