import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/to_login_button.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 本人確認実施済み画面
class VerifiedUserScreen extends HookConsumerWidget {
  const VerifiedUserScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          controller.sendScreenLog(
            screenNumber: ScreenIdNumber.verifiedUserScreenId,
            screenName: AppLocalizations.of(context)!.verifiedUser,
          );
        });
        return null;
      },
      [],
    );

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBarForIdentityVerification(
        onTapLeadingLogAction: () => // FirebaseAnalyticsログ送信
            controller.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.back,
          screenName: AppLocalizations.of(context)!.verifiedUser,
        ),
        title: AppLocalizations.of(context)!.identification,
      ),
      body: SafeArea(
        child: Column(
          children: [
            _Body(),
            const ToLoginButton(),
          ],
        ),
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final maxWidth = MediaQuery.of(context).size.width;
    final maxHeight = MediaQuery.of(context).size.height;

    return Expanded(
      child: SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Stack(
          children: [
            Image.asset(
              Assets.homeLightBackground,
              width: maxWidth,
              height: maxHeight,
              fit: BoxFit.fill,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ScreenNumber(
                  screenNumber: ScreenIdNumber.verifiedUserScreenId,
                ),
                const SizedBox(height: 40),
                Text(
                  AppLocalizations.of(context)!.verifiedUserTitle1,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(fontSize: 20, color: AppColors.tradGreen),
                ),
                const SizedBox(height: 6),
                Text(
                  AppLocalizations.of(context)!.accountOpeningSubmittedTitle2,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(fontSize: 20, color: AppColors.tradGreen),
                ),
                const SizedBox(height: 40),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Container(
                    decoration: BoxDecoration(
                      color: AppColors.backgroundGreen,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DefaultTextStyle(
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(height: 1.5),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 24,
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!
                                  .accountOpeningSubmittedMessage1,
                            ),
                            Text(
                              AppLocalizations.of(context)!
                                  .accountOpeningSubmittedMessage2,
                            ),
                            const SizedBox(height: 16),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Column(
                                  children: [
                                    // ベースライン調整用
                                    const SizedBox(height: 8),
                                    SvgPicture.asset(
                                      Assets.greyCircle,
                                      width: 8,
                                      height: 8,
                                    ),
                                  ],
                                ),
                                const SizedBox(width: 16),
                                SizedBox(
                                  width: 272,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)!
                                            .accountOpeningSubmittedMessage3,
                                      ),
                                      Text(
                                        AppLocalizations.of(context)!
                                            .accountOpeningSubmittedMessage4,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Column(
                                  children: [
                                    // ベースライン調整用
                                    const SizedBox(height: 8),
                                    SvgPicture.asset(
                                      Assets.greyCircle,
                                      width: 8,
                                      height: 8,
                                    ),
                                  ],
                                ),
                                const SizedBox(width: 16),
                                Text(
                                  AppLocalizations.of(context)!
                                      .accountOpeningSubmittedMessage5,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
