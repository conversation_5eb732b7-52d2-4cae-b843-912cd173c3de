import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

MobileScannerController useQrController() {
  return use(const _QrControllerHook());
}

class _QrControllerHook extends Hook<MobileScannerController> {
  const _QrControllerHook();

  @override
  HookState<MobileScannerController, Hook<MobileScannerController>>
      createState() => _QrControllerHookState();
}

class _QrControllerHookState
    extends HookState<MobileScannerController, _QrControllerHook> {
  late final MobileScannerController _controller;

  @override
  void initHook() {
    _controller = MobileScannerController(
      formats: [BarcodeFormat.qrCode],
    );
  }

  @override
  MobileScannerController build(BuildContext context) {
    return _controller;
  }

  @override
  void dispose() {
    _controller.dispose();
  }
}
