import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/dialog_action_button.dart';
import 'package:dtp_app/views/screens/identity_verification/reference_number/use_qr_controller.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';

// 本画面はお手続番号入力画面と状態を共有しており
// 当該画面は本画面遷移時に残存しているため、ダイアログ表示などはそちらの画面のものを使用する。
class QrReader extends HookConsumerWidget with CommonErrorHandlerMixin {
  const QrReader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityReferenceNumberScreenProvider);
    final controller = ref.watch(
      identityReferenceNumberScreenProvider.notifier,
    );
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final appLocalizations = AppLocalizations.of(context)!;
    final qrController = useQrController();
    final canUseCamera = useState(true);
    final animation = _setupAnimation();

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.initializeQrScreen(qrController);
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.identifyQrReaderScreenId,
            screenName: appLocalizations.identifyQrReader,
          );
        });
        return null;
      },
      [],
    );

    // 画像識別エラー。QRコードから取得したお手続番号エラーのみこの画面で判別する
    if (state.pickImageError != null) {
      // ファイルからQRコードを読み取った時だけこの画面でエラーダイアログ表示
      handleError(
        context,
        ref,
        state.error,
        screenId: ScreenIdNumber.identifyQrReaderScreenId,
        screenName: appLocalizations.identifyQrReader,
        screenSpecifiedErrorDialogBuilder: (context) {
          return _errorDialog(
            context,
            state.mobileScannerErrorMessage,
            ref,
          );
        },
      ).then((hasError) async {
        await qrController.start();
      });
    }

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        // 画面POP時ローディングフラグリセット
        controller.onPopQrScreen();
      },
      child: Scaffold(
        appBar: AppBarForIdentityVerification(
          title: appLocalizations.identification,
          onTapLeadingLogAction: () =>
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.back,
            screenName: appLocalizations.identifyQrReader,
          ),
        ),
        backgroundColor: AppColors.black,
        body: Stack(
          children: [
            MobileScanner(
              fit: BoxFit.fill,
              controller: qrController,
              onDetect: (BarcodeCapture capture) async {
                // 複数回の読み込み防止のため、処理を中止
                await qrController.stop();
                await controller.qrCodeDetect(capture);
              },
              errorBuilder: (context, error, child) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  Log.e(error.toString());
                  canUseCamera.value = false;
                });
                qrController.stop();
                return _errorDialog(
                  context,
                  controller.setQrErrorMessage(error),
                  ref,
                  isPermissionError: error.errorCode ==
                      MobileScannerErrorCode.permissionDenied,
                );
              },
            ),
            Visibility(
              visible: canUseCamera.value,
              child: Align(
                alignment: Alignment.center,
                // バーコードが読み込まれるまでは点滅させる
                child: AnimatedBuilder(
                  animation: animation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: animation.value,
                      child: Container(
                        height: 200,
                        width: 200,
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.white),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
        floatingActionButton: Visibility(
          visible: canUseCamera.value,
          child: FloatingActionButton(
            backgroundColor: AppColors.tradGreen,
            foregroundColor: AppColors.white,
            onPressed: () async {
              analyticsLogController.sendButtonLog(
                buttonName: appLocalizations.openLibrary,
                screenName: appLocalizations.identifyQrReader,
              );

              await qrController.stop();

              final image = await controller.pickImage();

              if (image == null) {
                Log.e('image == null');
                controller.setImageErrorMassage(
                  '${IdentityVerificationErrorInfo.invalidReferenceNumberErrorCode}\n${IdentityVerificationErrorInfo.invalidReferenceNumberErrorMessage}',
                );
                return;
              }
              try {
                final BarcodeCapture? capture =
                    await qrController.analyzeImage(image.path);
                if (capture == null) {
                  Log.e('capture == null');
                  // BarcodeCapture取得失敗
                  controller.setImageErrorMassage(
                    '${IdentityVerificationErrorInfo.invalidReferenceNumberErrorCode}\n${IdentityVerificationErrorInfo.invalidReferenceNumberErrorMessage}',
                  );
                  return;
                }
                await controller.qrCodeDetect(capture);
              } catch (error) {
                Log.e('QRコード読み取り失敗 : $error');
                controller.setImageErrorMassage(
                  '${IdentityVerificationErrorInfo.invalidReferenceNumberErrorCode}\n${IdentityVerificationErrorInfo.invalidReferenceNumberErrorMessage}',
                );
              }
            },
            child: const Icon(Icons.image),
          ),
        ),
      ),
    );
  }

  /// AnimationControllerの初期化処理
  Animation<double> _setupAnimation() {
    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 1200),
    )..repeat(reverse: true);

    return useMemoized(
      () {
        return Tween<double>(
          begin: 1.0,
          end: 0,
        ).animate(animationController);
      },
      [animationController],
    );
  }

  CommonDialog _errorDialog(
    BuildContext context,
    String? message,
    WidgetRef ref, {
    bool isPermissionError = false,
  }) {
    final appLocalizations = AppLocalizations.of(context)!;
    final controller = ref.watch(
      identityReferenceNumberScreenProvider.notifier,
    );
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // エラーのクリア
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.clearImageErrorMassage();
      controller.clearReferenceNumberValidationError();
    });

    if (isPermissionError) {
      return CommonDialog(
        // カメラのアクセス許可がない場合
        content: Text(
          message ??
              '${ErrorInfo.defaultErrorCode}\n${ErrorInfo.defaultErrorMessage}',
        ),
        buttons: [
          DialogActionTextButton(
            text: appLocalizations.cancel,
            onPressed: () {
              analyticsLogController.sendButtonLog(
                buttonName: appLocalizations.cancel,
                screenName: appLocalizations.identifyQrReader,
              );
            },
          ),
          DialogActionTextButton(
            text: appLocalizations.settings,
            onPressed: () async {
              analyticsLogController.sendButtonLog(
                buttonName: appLocalizations.settings,
                screenName: appLocalizations.identifyQrReader,
              );
              // 設定アプリ アクセス許可画面に遷移
              await openAppSettings();
            },
          ),
        ],
      );
    }
    return CommonDialog.ok(
      message: message ?? ErrorInfo.defaultErrorMessage,
      onOkPressed: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: appLocalizations.ok,
          screenName: appLocalizations.identifyQrReader,
        );
      },
    );
  }
}
