import 'dart:async';
import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_reference_number/identity_reference_number_screen_notifier.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/validation_error.dart';
import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';
import 'package:dtp_app/utils/nfc_check.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/account_opening_submitted/account_opening_submitted_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/reference_number/qr_reader.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/next_buttton.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/validation_error_widget.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 本人確認お手続き番号確認画面
class IdentityReferenceNumberScreen extends HookConsumerWidget
    with CommonErrorHandlerMixin {
  const IdentityReferenceNumberScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller =
        ref.watch(identityReferenceNumberScreenProvider.notifier);
    final state = ref.watch(identityReferenceNumberScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.identityReferenceNumberScreenId,
            screenName: AppLocalizations.of(context)!.identityReferenceNumber,
          );
        });
        return null;
      },
      [],
    );

    // API読み込み時にローディング
    if (state.isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        LoadingDialog.loadingWithTitle(context);
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        LoadingDialog.loadingEnd(context);
      });
    }

    handleError(
      context,
      ref,
      state.error,
      onOkTap: state.isOnQrScreen ? () => Navigator.pop(context) : null,
      screenId: ScreenIdNumber.identityReferenceNumberScreenId,
      screenName: AppLocalizations.of(context)!.referenceNumber,
    ).then((hasError) {
      if (hasError) controller.clearError();
    });

    if (ref
            .watch(identityReferenceNumberScreenProvider)
            .identityVerificationPhases !=
        null) {
      // 一時保存テーブルの内容による画面遷移ハンドリング
      switch (state.identityVerificationPhases!) {
        case IdentityVerificationPhases.jpki:
          // 証明書選択画面に遷移
          Future.microtask(() async {
            controller.resetReferenceNumberStatus();
            final isNfcAvailable = await NfcCheck.isNfcAvailable();

            AndroidNfcStatus? androidNfcStatus;
            if (Platform.isAndroid) {
              androidNfcStatus = await controller.getAndroidNfcStatus();
            }

            if (!context.mounted) return;

            // QRコード読み込み画面にいる際、お手続番号入力画面に遷移
            if (state.isOnQrScreen) {
              Navigator.popUntil(
                context,
                (router) =>
                    router.settings.name == RouteNames.identityReferenceNumber,
              );
            }

            if (androidNfcStatus != null &&
                androidNfcStatus == AndroidNfcStatus.disabled) {
              // NFC対象端末 かつ　NFC設定OFFの場合(Androidのみ)は証明書選択画面へ遷移
              await Navigator.of(context)
                  .pushNamed(RouteNames.identityDocument);
              return;
            }

            if (!isNfcAvailable) {
              // NFC非対応端末は、eKYC概要画面へ遷移
              await Navigator.of(context).pushNamed(RouteNames.ekycExplanation);
              return;
            }

            // NFC利用可のため、証明書選択画面へ遷移
            await Navigator.of(context).pushNamed(RouteNames.identityDocument);
          }).then((_) async {
            await ref.read(mdManagerProvider).referenceNumber.delete();
          });
        case IdentityVerificationPhases.completed:
          // 本人確認実施済み画面に遷移
          Future.microtask(() async {
            if (!context.mounted) return;
            // QRコード読み込み画面にいる際、お手続番号入力画面に遷移
            if (state.isOnQrScreen) {
              Navigator.popUntil(
                context,
                (router) =>
                    router.settings.name == RouteNames.identityReferenceNumber,
              );
            }
            controller.resetReferenceNumberStatus();
            await Navigator.of(context).pushNamed(RouteNames.verifiedUser);
          }).then((_) async {
            await ref.read(mdManagerProvider).referenceNumber.delete();
          });
        case IdentityVerificationPhases.initial:
        // 初期状態なので何もせず、以下の通常のハンドリングに任せる
      }
    }

    // HACKME ReferenceNumberStatus.passedもIdentityVerificationStatusに含める
    if (state.referenceNumberStatus == ReferenceNumberStatus.passed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.resetReferenceNumberStatus();
        // QRコード読み込み画面にいる際、お手続番号入力画面に遷移
        if (state.isOnQrScreen) {
          Navigator.popUntil(
            context,
            (router) =>
                router.settings.name == RouteNames.identityReferenceNumber,
          );
        }
        // 口座開設申込済み画面に遷移する
        Navigator.of(context)
            .push(
          MaterialPageRoute(
            builder: (context) => const AccountOpeningSubmittedScreen(),
          ),
        )
            .then((_) async {
          await ref.read(mdManagerProvider).referenceNumber.delete();
        });
      });
    }

    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
          screenName: AppLocalizations.of(context)!.identityReferenceNumber,
        );
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: ColoredBox(
        color: AppColors.white,
        child: SafeArea(
          top: false,
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: AppBarForIdentityVerification(
              title: AppLocalizations.of(context)!.identification,
              onTapLeadingLogAction: () =>
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.back,
                screenName:
                    AppLocalizations.of(context)!.identityReferenceNumber,
              ),
            ),
            body: Column(
              children: [
                const _ScrollArea(),
                NextButton(
                  onTap: () async {
                    // FirebaseAnalyticsログ送信
                    analyticsLogController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!.next,
                      screenName:
                          AppLocalizations.of(context)!.identityReferenceNumber,
                    );

                    // お手続き番号のバリデーションチェック
                    final isValidationError = controller
                        .validateReferenceNumber(state.referenceNumberText);
                    if (isValidationError.hasError) {
                      // バリデーションエラーであれば処理中断
                      return;
                    }
                    // お手続き番号の値を元にuuidを取得し、ReferenceNumberStatusを更新する
                    await controller.getUuid();
                  },
                  enabled: state.referenceNumberText.isNotEmpty,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _ScrollArea extends StatelessWidget {
  const _ScrollArea();

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(
                        Assets.homeLightBackground,
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Column(
                    children: [
                      ScreenNumber(
                        screenNumber:
                            ScreenIdNumber.identityReferenceNumberScreenId,
                      ),
                      const _Body(),
                      Expanded(child: const SizedBox.shrink()),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

class _Body extends HookConsumerWidget {
  const _Body();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(identityReferenceNumberScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Stack(
      children: [
        Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.white,
                  boxShadow: const [
                    BoxShadow(
                      color: AppColors.shadowGray,
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 24,
                  ),
                  child: Column(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          AppLocalizations.of(context)!
                              .identificationReferenceNumber,
                          style:
                              Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    fontSize: 20,
                                    color: AppColors.tradGreen,
                                  ),
                          textAlign: TextAlign.left,
                        ),
                      ),
                      const SizedBox(height: 24),
                      _QrReaderButton(
                        sendButtonLog: () =>
                            analyticsLogController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!
                              .readingQrCodeWithCamera,
                          screenName: AppLocalizations.of(context)!
                              .identityReferenceNumber,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const ReferenceNumberForm(),
                      const SizedBox(height: 8),
                      ValidationMessage(
                        error: state.referenceNumberValidationError,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.caution(''),
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall!
                                .copyWith(fontSize: 12),
                          ),
                          Flexible(
                            child: Text(
                              AppLocalizations.of(context)!
                                  .referenceNumberProcess,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: AppColors.lightGreen1,
                        ),
                        child: Image.asset(
                          Assets.referenceNumberProcess,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class _QrReaderButton extends StatelessWidget {
  const _QrReaderButton({required this.sendButtonLog});

  final void Function() sendButtonLog;

  @override
  Widget build(BuildContext context) {
    return Material(
      child: InkWell(
        highlightColor: AppColors.onPressedColor,
        splashColor: AppColors.onPressedColor,
        onTap: () {
          // FirebaseAnalyticsログ送信
          sendButtonLog();
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const QrReader(),
            ),
          );
          // キーボードを閉じる
          FocusManager.instance.primaryFocus?.unfocus();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: AppColors.tradGreen),
            color: AppColors.white,
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  Assets.qrCodeIcon,
                  width: 24,
                  height: 24,
                ),
                const SizedBox(width: 4),
                Text(
                  AppLocalizations.of(context)!.readingQrCodeWithCamera,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: AppColors.tradGreen,
                        fontWeight: FontWeight.w700,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ReferenceNumberForm extends HookConsumerWidget {
  const ReferenceNumberForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.read(identityReferenceNumberScreenProvider.notifier);
    final referenceNumberController = useTextEditingController();
    final referenceNumberFocus = useFocusNode();
    final state = ref.watch(identityReferenceNumberScreenProvider);
    final hasValidationError = state.referenceNumberValidationError.hasError;
    final borderColor =
        hasValidationError ? AppColors.red : AppColors.borderGrayGreen;

    final borderWidget = OutlineInputBorder(
      borderSide: BorderSide(
        color: borderColor,
        width: 1,
      ),
      borderRadius: BorderRadius.circular(4),
    );

    if (state.referenceNumberText.isNotEmpty) {
      referenceNumberController.text = state.referenceNumberText;
    }

    return SizedBox(
      height: 48,
      child: NotCopyTextFormField(
        textAlignVertical: TextAlignVertical.center,
        controller: referenceNumberController,
        textAlign: TextAlign.left,
        decoration: InputDecoration(
          enabledBorder: borderWidget,
          focusedBorder: borderWidget,
          border: borderWidget,
          fillColor: hasValidationError
              ? AppColors.lightRed
              : AppColors.basicBackgroundLightGray,
          filled: true,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 0,
            horizontal: 10,
          ),
        ),
        inputFormatters: [
          FilteringTextInputFormatter.singleLineFormatter,
        ],
        onChanged: (_) {
          // テキストフォームの中身が変更された場合、お手続き番号のstateを更新する
          controller.setReferenceNumber(
            referenceNumber: referenceNumberController.text,
          );
        },
        focusNode: referenceNumberFocus,
        keyboardType: TextInputType.number,
      ),
    );
  }
}
