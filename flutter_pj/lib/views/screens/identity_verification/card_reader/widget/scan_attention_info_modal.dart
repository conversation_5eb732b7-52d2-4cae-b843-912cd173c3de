import 'dart:async';
import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/card_reader/card_reader_screen_notifier.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// 読み取り時の注意事項モーダル
void scanAttentionInfoModal(
  BuildContext context,
  WidgetRef ref, {
  required void Function() iOSAction,
  required void Function() androidAction,
}) {
  // 読み取り実施済みかどうかの判定フラグ
  final isSdkReading = ref.read(cardReaderScreenProvider).isSdkReading;
  final String cardReaderActionButtonWord;
  final logController = ref.read(analyticsLogControllerProvider);

  // 「読み取り開始」 or 「読み取りをやり直す」どちらの文言か判定
  if (isSdkReading) {
    cardReaderActionButtonWord =
        AppLocalizations.of(context)!.cardReaderErrorButton;
  } else {
    cardReaderActionButtonWord = AppLocalizations.of(context)!.startReading;
  }

  final double screenSizeWidth = MediaQuery.of(context).size.width;
  // スクロール範囲を指定するために画面全体の高さからTop、フッターの高さ、各SizedBoxを足した値を引く
  final double screenSizeHeight = MediaQuery.of(context).size.height - 192;
  const double padding = 16.0;

  // Firebase Screenログ・KARTE Viewイベント送信
  logController.sendScreenLog(
    screenNumber: ScreenIdNumber.scanAttentionInfoModalId,
    screenName: AppLocalizations.of(context)!.scanAttentionInfoModal,
  );

  showModalBottomSheet<void>(
    context: context,
    isScrollControlled: true,
    useSafeArea: true,
    isDismissible: false,
    enableDrag: false,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (BuildContext context) {
      return ColoredBox(
        color: AppColors.white,
        child: SafeArea(
          bottom: true,
          child: Scaffold(
            backgroundColor: AppColors.tradGreen,
            body: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                color: AppColors.white,
              ),
              child: Stack(
                children: [
                  RawScrollbar(
                    thumbColor: AppColors.basicBackgroundColor,
                    thumbVisibility: true,
                    thickness: 6,
                    radius: const Radius.circular(3.0),
                    child: SizedBox(
                      height: screenSizeHeight,
                      child: SingleChildScrollView(
                        child: Padding(
                          padding:
                              const EdgeInsets.symmetric(horizontal: padding),
                          child: Stack(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 46.2),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: <Widget>[
                                    SvgPicture.asset(
                                      Assets.alertIcon,
                                      width: 29.5,
                                      height: 27.2,
                                    ),
                                    const SizedBox(height: 9),
                                    Container(
                                      alignment: Alignment.center,
                                      child: Text(
                                        AppLocalizations.of(context)!
                                            .jpkiNotesOnReading,
                                        textAlign: TextAlign.center,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontSize: 20,
                                              color: AppColors.red,
                                            ),
                                      ),
                                    ),
                                    const SizedBox(height: 24),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiAccessoryInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiDeskInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiSmartPhoneHoldUpInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiSmartPhoneWaitInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiWifiInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiSmartPhoneOsUpdateInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiCardCheckInfo,
                                    ),
                                    const SizedBox(height: 8),
                                    _IconTextWidget(
                                      text: AppLocalizations.of(context)!
                                          .jpkiAndroidCardReaderInfo,
                                    ),
                                    const SizedBox(height: 24),
                                    SizedBox(
                                      width: screenSizeWidth,
                                      child: RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: AppLocalizations.of(
                                                context,
                                              )!
                                                  .cardReadingPositionExplain,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .copyWith(
                                                    color: AppColors.tradGreen,
                                                    decorationColor:
                                                        AppColors.primary,
                                                    height: 1.5,
                                                  ),
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () {
                                                  launchURL(
                                                    context,
                                                    ref,
                                                    LinkIds
                                                        .cardReadingPositionExplain,
                                                  );
                                                },
                                            ),
                                            WidgetSpan(
                                              child: GestureDetector(
                                                onTap: () {
                                                  launchURL(
                                                    context,
                                                    ref,
                                                    LinkIds
                                                        .cardReadingPositionExplain,
                                                  );
                                                },
                                                child: const SizedBox(
                                                  width: 4,
                                                ),
                                              ),
                                            ),
                                            WidgetSpan(
                                              child: Column(
                                                children: [
                                                  GestureDetector(
                                                    onTap: () {
                                                      launchURL(
                                                        context,
                                                        ref,
                                                        LinkIds
                                                            .cardReadingPositionExplain,
                                                      );
                                                    },
                                                    child: SvgPicture.asset(
                                                      Assets.actionIcon,
                                                      width: 16,
                                                      height: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    height: 2,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    SizedBox(
                                      width: screenSizeWidth,
                                      child: RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text:
                                                  AppLocalizations.of(context)!
                                                      .cardNotReadingExplain,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .copyWith(
                                                    color: AppColors.textBlack,
                                                    height: 1.5,
                                                  ),
                                            ),
                                            TextSpan(
                                              text:
                                                  AppLocalizations.of(context)!
                                                      .here,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium!
                                                  .copyWith(
                                                    color: AppColors.tradGreen,
                                                    decoration: TextDecoration
                                                        .underline,
                                                    decorationColor:
                                                        AppColors.primary,
                                                    height: 1.5,
                                                  ),
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () {
                                                  launchURL(
                                                    context,
                                                    ref,
                                                    LinkIds
                                                        .cardNotReadingExplain,
                                                  );
                                                },
                                            ),
                                            WidgetSpan(
                                              child: GestureDetector(
                                                onTap: () {
                                                  launchURL(
                                                    context,
                                                    ref,
                                                    LinkIds
                                                        .cardNotReadingExplain,
                                                  );
                                                },
                                                child: const SizedBox(
                                                  width: 4,
                                                ),
                                              ),
                                            ),
                                            WidgetSpan(
                                              child: Column(
                                                children: [
                                                  GestureDetector(
                                                    onTap: () {
                                                      launchURL(
                                                        context,
                                                        ref,
                                                        LinkIds
                                                            .cardNotReadingExplain,
                                                      );
                                                    },
                                                    child: SvgPicture.asset(
                                                      Assets.actionIcon,
                                                      width: 16,
                                                      height: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    height: 2,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 40,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: padding, right: 16),
                        child: IconButton(
                          iconSize: 32,
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          onPressed: () {
                            logController.sendButtonLog(
                              buttonName: AppLocalizations.of(context)!.batsu,
                              screenName: AppLocalizations.of(context)!
                                  .scanAttentionInfoModal,
                            );
                            Navigator.of(context).pop();
                          },
                          icon: SvgPicture.asset(
                            Assets.buttonClose,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      width: double.infinity,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        boxShadow: [
                          // SafeArea部分にも影が入ってしまうため、SafeAreaの境目に白色の影を追加
                          BoxShadow(
                            offset: const Offset(0, -2),
                            color: AppColors.cardShadow.withOpacity(0.15),
                            blurRadius: 4,
                          ),
                          const BoxShadow(
                            color: AppColors.white,
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          const SizedBox(height: 16),
                          // 読み取り開始 or やり直すボタン
                          cardReaderActionButton(
                            context,
                            ref,
                            cardReaderActionButtonWord,
                            iOSAction,
                            androidAction,
                          ),
                          const SizedBox(height: 16),
                          // 他の方法で本人確認するボタン
                          const _OtherIdentityVerification(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}

// 読み取り開始 or やり直すボタン
Widget cardReaderActionButton(
  BuildContext context,
  WidgetRef ref,
  String cardReaderActionButtonWord,
  void Function() iOSAction,
  void Function() androidAction,
) {
  final logController = ref.read(analyticsLogControllerProvider);
  return SizedBox(
    width: 240,
    height: 48,
    child: ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.tradGreen,
        disabledForegroundColor:
            AppColors.tradGreen.withOpacity(0.5).withOpacity(0.38),
        disabledBackgroundColor: AppColors.tradGreen.withOpacity(0.06),
        textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontWeight: FontWeight.w700,
            ),
      ),
      onPressed: () async {
        // FirebaseAnalyticsログ送信
        logController.sendButtonLog(
          buttonName: cardReaderActionButtonWord,
          screenName: AppLocalizations.of(context)!.scanAttentionInfoModal,
        );

        Navigator.popUntil(
          context,
          (route) => route.settings.name == RouteNames.cardReader,
        );

        if (Platform.isIOS) {
          iOSAction();
        } else {
          androidAction();
        }
      },
      child: Text(cardReaderActionButtonWord),
    ),
  );
}

// 他の方法で本人確認するボタン
class _OtherIdentityVerification extends HookConsumerWidget {
  const _OtherIdentityVerification();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return Center(
      child: TextButton(
        style: CustomButtonStyle.textButtonStyle,
        onPressed: () {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName:
                AppLocalizations.of(context)!.otherIdentityVerificationMethod,
            screenName: AppLocalizations.of(context)!.scanAttentionInfoModal,
          );

          Navigator.of(context).pop();

          Navigator.pushNamedAndRemoveUntil(
            context,
            RouteNames.ekycExplanation,
            (route) => route.settings.name == RouteNames.identityDocument,
          );
        },
        child: Text(
          AppLocalizations.of(context)!.otherIdentityVerificationMethod,
        ),
      ),
    );
  }
}

// 読み取り時の注意事項のフォーマット
class _IconTextWidget extends StatelessWidget {
  final String text;

  const _IconTextWidget({
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.baseline,
      textBaseline: TextBaseline.alphabetic,
      children: [
        const SizedBox(width: 8),
        Column(
          children: [
            const SizedBox(height: 8),
            SvgPicture.asset(
              Assets.middleDot,
              width: 8,
              height: 8,
            ),
          ],
        ),
        const SizedBox(width: 16),
        Flexible(
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: text,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

// 外部URLを開くための関数
Future<void> launchURL(
  BuildContext context,
  WidgetRef ref,
  LinkIds linkIds,
) async {
  final logController = ref.read(analyticsLogControllerProvider);
  late String sendButtonName;
  if (linkIds == LinkIds.cardNotReadingExplain) {
    sendButtonName = AppLocalizations.of(context)!.here;
  }
  if (linkIds == LinkIds.cardReadingPositionExplain) {
    sendButtonName = AppLocalizations.of(context)!.cardReadingPositionExplain;
  }
  // FirebaseAnalyticsログ送信
  logController.sendButtonLog(
    buttonName: sendButtonName,
    screenName: AppLocalizations.of(context)!.scanAttentionInfoModal,
  );
  await showDialog(
    context: context,
    builder: (BuildContext context) {
      return CommonDialog.okCancel(
        title: AppLocalizations.of(context)!.moveToExternalSite,
        message: AppLocalizations.of(context)!.confirmExternalSiteNavigation,
        onOkPressed: () async {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName: '${AppLocalizations.of(context)!.yes}($sendButtonName)',
            screenName: AppLocalizations.of(context)!.scanAttentionInfoModal,
          );
          if (!context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  AppLocalizations.of(context)!.urlOpenErrorMessage,
                ),
              ),
            );
            return;
          }
          // ページを開く
          await ref.watch(urlLauncherProvider).launchLinkId(
                linkIds,
                '${AppLocalizations.of(context)!.yes}（$sendButtonName）',
                ScreenIdNumber.scanAttentionInfoModalId,
              );
        },
        onCancelPressed: () {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName: '${AppLocalizations.of(context)!.no}($sendButtonName)',
            screenName: AppLocalizations.of(context)!.scanAttentionInfoModal,
          );
        },
        okButtonText: AppLocalizations.of(context)!.yes,
        cancelButtonText: AppLocalizations.of(context)!.no,
      );
    },
  );
}
