import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/business_logics/card_reader/card_reader_screen_notifier.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// カード読み取り準備完了モーダル
void cardReaderModalSetup(
  BuildContext context,
  Function function,
  WidgetRef ref,
) {
  final logController = ref.read(analyticsLogControllerProvider);

  // Firebase Screenログ・KARTE Viewイベント送信
  logController.sendScreenLog(
    screenNumber: ScreenIdNumber.cardReaderSetupModalId,
    screenName: AppLocalizations.of(context)!.cardReaderSetupModal,
  );

  showModalBottomSheet<void>(
    context: context,
    enableDrag: false,
    isDismissible: false,
    isScrollControlled: true,
    //trueにしないと、Containerのheightが反映されない
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    backgroundColor: AppColors.white,
    builder: (BuildContext context) {
      final double screenSize = MediaQuery.of(context).size.width;
      const double padding = 16.0;
      return PopScope(
        canPop: false,
        child: SizedBox(
          width: screenSize,
          height: 309,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: padding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(height: 24),
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      AppLocalizations.of(context)!.cardReaderSetup,
                      textAlign: TextAlign.center,
                      style: Theme.of(context)
                          .textTheme
                          .bodyLarge!
                          .copyWith(color: AppColors.black),
                    ),
                  ),
                  const SizedBox(height: 13),
                  Image.asset(
                    Assets.cardReaderSetupImage,
                    width: 115,
                    height: 118,
                    fit: BoxFit.cover,
                    gaplessPlayback: true,
                  ),
                  const SizedBox(height: 18),
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      AppLocalizations.of(context)!.myNumberCardHoldUp,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 37),
                  // キャンセルボタン
                  Center(
                    child: TextButton(
                      style: CustomButtonStyle.textButtonStyle,
                      onPressed: () {
                        // FirebaseAnalyticsログ送信
                        logController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!.cancel,
                          screenName: AppLocalizations.of(context)!
                              .cardReaderSetupModal,
                        );
                        Navigator.pop(context);
                        ref
                            .read(cardReaderScreenProvider.notifier)
                            .updateIsCanceled(true);
                        // F_JLA_02 NFC標準モード設定を呼び出す(リーダーモードから標準モードに切り替える)
                        function();
                      },
                      child: Text(AppLocalizations.of(context)!.cancel),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}

// カード読み取り中モーダル
void cardReaderModalReading(
  BuildContext context,
  Function function,
  WidgetRef ref,
) {
  final logController = ref.read(analyticsLogControllerProvider);

  // Firebase Screenログ・KARTE Viewイベント送信
  logController.sendScreenLog(
    screenNumber: ScreenIdNumber.cardReaderReadingModalId,
    screenName: AppLocalizations.of(context)!.cardReaderReadingModal,
  );

  showModalBottomSheet<void>(
    context: context,
    enableDrag: false,
    isDismissible: false,
    isScrollControlled: true,
    //trueにしないと、Containerのheightが反映されない
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    backgroundColor: AppColors.white,
    builder: (BuildContext context) {
      final double screenSize = MediaQuery.of(context).size.width;
      const double padding = 16.0;
      return PopScope(
        canPop: false,
        child: SizedBox(
          width: screenSize,
          height: 309,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: padding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(height: 24),
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      AppLocalizations.of(context)!.cardReaderReading,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ),
                  const SizedBox(height: 13),
                  Image.asset(
                    Assets.cardReaderReadingImage,
                    width: 115,
                    height: 118,
                    fit: BoxFit.cover,
                    gaplessPlayback: true,
                  ),
                  const SizedBox(height: 18),
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      AppLocalizations.of(context)!.myNumberCardNotMove,
                      textAlign: TextAlign.center,
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(color: AppColors.black),
                    ),
                  ),
                  const SizedBox(height: 37),
                  // キャンセルボタン
                  Center(
                    child: TextButton(
                      style: CustomButtonStyle.textButtonStyle,
                      onPressed: () {
                        // FirebaseAnalyticsログ送信
                        logController.sendButtonLog(
                          buttonName: AppLocalizations.of(context)!.cancel,
                          screenName: AppLocalizations.of(context)!
                              .cardReaderReadingModal,
                        );
                        Navigator.pop(context);
                        ref
                            .read(cardReaderScreenProvider.notifier)
                            .updateIsCanceled(true);
                        // F_JLA_02 NFC標準モード設定を呼び出す(リーダーモードから標準モードに切り替える)
                        function();
                      },
                      child: Text(AppLocalizations.of(context)!.cancel),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}

// カード読み取り完了モーダル
void cardReaderModalSuccess(BuildContext context, WidgetRef ref) {
  final logController = ref.read(analyticsLogControllerProvider);

  // Firebase Screenログ・KARTE Viewイベント送信
  logController.sendScreenLog(
    screenNumber: ScreenIdNumber.cardReaderSuccessModaId,
    screenName: AppLocalizations.of(context)!.cardReaderSuccessModal,
  );

  showModalBottomSheet<void>(
    context: context,
    enableDrag: false,
    isDismissible: false,
    isScrollControlled: true,
    //trueにしないと、Containerのheightが反映されない
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    backgroundColor: AppColors.white,
    builder: (BuildContext context) {
      final double screenSize = MediaQuery.of(context).size.width;
      const double padding = 16.0;

      return PopScope(
        canPop: false,
        child: SizedBox(
          width: screenSize,
          height: 309,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: padding),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(height: 67),
                  Image.asset(
                    Assets.cardReaderSuccessImage,
                    width: 115,
                    height: 118,
                    fit: BoxFit.cover,
                    gaplessPlayback: true,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    alignment: Alignment.center,
                    child: Text(
                      AppLocalizations.of(context)!.cardReaderSuccess,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  const SizedBox(height: 85),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}
