import 'dart:async';
import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/card_reader/card_reader_screen_notifier.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/content_area.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/text_link_arrow.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/card_reader/widget/card_reader_modal.dart';
import 'package:dtp_app/views/screens/identity_verification/card_reader/widget/scan_attention_info_modal.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CardReaderScreen extends HookConsumerWidget with CommonErrorHandlerMixin {
  const CardReaderScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cardReaderScreenController =
        ref.watch(cardReaderScreenProvider.notifier);
    const double footerHeight = 80;
    final state = ref.watch(cardReaderScreenProvider);
    final logController = ref.read(analyticsLogControllerProvider);
    final MemoryDataManager mdManager = ref.watch(mdManagerProvider);
    final hasSentIdentityInfo = useState(false);
    final identityVerificationController =
        ref.watch(identityVerificationScreenProvider.notifier);

    // Firebase Screenログ・KARTE Viewイベント送信
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.cardReaderScreenId,
            screenName: AppLocalizations.of(context)!.myNumberCardReadScreen,
          );
        });
        return null;
      },
      [],
    );

    handleError(
      context,
      ref,
      state.sendIdentityInfoError,
      screenId: ScreenIdNumber.cardReaderScreenId,
      screenName: AppLocalizations.of(context)!.myNumberCardReadScreen,
      screenSpecifiedErrorDialogBuilder: (context) {
        return CommonDialog.ok(
          message:
              '${IdentityVerificationErrorInfo.identityInfoNotSavedErrorCode}\n${IdentityVerificationErrorInfo.identityInfoNotSavedErrorMessage}',
          onOkPressed: () async {
            cardReaderScreenController.clearSendIdentityInfoError();
            await Future.microtask(() {
              if (!context.mounted) return;
              Navigator.pushNamedAndRemoveUntil(
                context,
                RouteNames.identityReferenceNumber,
                (route) => route.settings.name == RouteNames.cardReader,
              );
            });
          },
        );
      },
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // エラーハンドリング
      if (state.errorPattern != null) {
        // 本人確認APIでエラーが発生している場合
        if (state.identificationError != null) {
          // ローディングを閉じる
          LoadingDialog.loadingEnd(context);
        }

        if (state.cardReadingAndroidCanceled) {
          // キャンセルボタン押下されているため、後続のハンドリングもキャンセル
          cardReaderScreenController.androidCanceledReset();
          return;
        }

        if (!context.mounted) return;
        // 読み取り時の注意モーダルを表示する場合のケース
        if (state.errorPattern == ErrorPattern.errorPattern4) {
          ContextClass.setContext(context);
          cardReaderScreenController.errorReset();
          scanAttentionInfoModal(
            context,
            ref,
            iOSAction: () {
              cardReaderScreenController.startNfcSession();
            },
            androidAction: () {
              cardReaderScreenController.androidCanceledReset();
              androidReadMyNumberCard(
                context,
                ref
                    .read(cardReaderScreenProvider.notifier)
                    .setDisableNfcCardReader,
                cardReaderScreenController,
                ref,
              );
            },
          );
          return;
        }
        // 共通エラーダイアログを表示するケース
        showCommonErrorDialog(
          dialogContext: context,
          errorPattern: state.errorPattern!,
          onPressed1: () {
            cardReaderScreenController.errorReset();
            onPressed1(
              context,
              ref,
              state.errorPattern!,
              AppLocalizations.of(context)!.myNumberCardReadScreen,
            );
          },
          onPressed2: () {
            cardReaderScreenController.errorReset();
            onPressed2(
              context,
              ref,
              state.errorPattern!,
              AppLocalizations.of(context)!.myNumberCardReadScreen,
            );
          },
          retryCounter: state.retryCounter,
        );
      }

      // Android、iOSでの共通の本人情報確認API完了後の処理
      if (state.isComplete) {
        // 本画面に戻ってきた際に再チェックできるようにフラグを初期化
        cardReaderScreenController.updateIsComplete(false);

        // mdManagerからuserTypeと手続き番号を読み込み
        final userType = await mdManager.userType.load();
        final isBeneficiary = userType.contains('BENEFICIARY');

        // 本人確認実施者が実質的支配者の場合、本人情報保存APIを実行する
        if (isBeneficiary) {
          final isSuccess =
              await cardReaderScreenController.sendIdentityVerificationInfo(
            role: userType,
            isEkyc: false,
          );
          hasSentIdentityInfo.value = isSuccess;
        }

        if (!context.mounted) return;
        // ローディングを閉じる
        LoadingDialog.loadingEnd(context);

        if (hasSentIdentityInfo.value) {
          if (!context.mounted) return;
          // 本人情報保存APIで成功した場合、本人確認完了画面(実質的支配者)へ遷移する
          await Navigator.pushNamed(
            context,
            RouteNames.registerFinished,
          );
          // 完了フラグをfalseに戻す
          hasSentIdentityInfo.value = false;
        } else {
          if (!context.mounted) return;
          await Navigator.pushNamed(
            context,
            RouteNames.enteringName,
          );
        }
      }

      // iOS、Androidでのカード読み取り完了後の処理
      // 発火タイミングはそれぞれ以下とする
      // iOS : スプラッシュ表示(=iOS読み取り完了モーダル表示状態)が終了したら 、本人確認API実行
      // Android : 読み取り完了モーダル(カスタマイズ)の表示が完了したら、本人確認API実行
      if ((state.cardReadingIosCompleted && state.isActive) ||
          state.cardReadingAndroidCompleted) {
        // フラグを元に戻す
        Platform.isIOS
            ? cardReaderScreenController.updateCardReadingIosCompleted(false)
            : cardReaderScreenController
                .updateCardReadingAndroidCompleted(false);
        if (context.mounted) {
          // ローディング開始
          LoadingDialog.loading(context);
          // 本人確認APIを実行する
          final result =
              await cardReaderScreenController.getJpkiIdentification();
          // 本人確認APIのレスポンス受領後、本人確認方法判定フラグをJPKIにする
          if (result) {
            identityVerificationController.switchToJPKI();
          }
        }
      }
    });

    return ColoredBox(
      color: AppColors.white,
      child: SafeArea(
        top: false,
        child: Scaffold(
          appBar: AppBarForIdentityVerification(
            title: AppLocalizations.of(context)!.identificationAppBarTitle,
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName: AppLocalizations.of(context)!.myNumberCardReadScreen,
            ),
          ),
          body: Stack(
            children: [
              Image.asset(
                Assets.bgWaveGrey,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
              ),
              Stack(
                children: [
                  SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      children: <Widget>[
                        ScreenNumber(
                          screenNumber: ScreenIdNumber.cardReaderScreenId,
                          color: AppColors.textBlack,
                        ),
                        ContentArea(
                          context: context,
                          title: AppLocalizations.of(context)!
                              .jpkiCardReaderScreenContentTitle,
                          widget: CardReaderScreenWidgets(
                            ref: ref,
                          ),
                        ),
                        const SizedBox(height: footerHeight),
                      ],
                    ),
                  ),
                ],
              ),
              Container(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: double.infinity,
                  height: footerHeight,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    boxShadow: [
                      // SafeArea部分にも影が入ってしまうため、SafeAreaの境目に白色の影を追加
                      BoxShadow(
                        offset: const Offset(0, -2),
                        color: AppColors.cardShadow.withOpacity(0.15),
                        blurRadius: 4,
                      ),
                      const BoxShadow(
                        color: AppColors.white,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: 16,
                      ),
                      ElevatedButton(
                        style: CommonButtonStyles.elevatedButtonStyleMedium,
                        onPressed: () {
                          // FirebaseAnalyticsログ送信
                          logController.sendButtonLog(
                            buttonName:
                                AppLocalizations.of(context)!.startReading,
                            screenName: AppLocalizations.of(context)!
                                .myNumberCardReadScreen,
                          );
                          // マイナンバカードの読み取り処理を行う
                          readMyNumberCard(context, ref, false);
                        },
                        child: Text(
                          AppLocalizations.of(context)!.startReading,
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static void readMyNumberCard(
    BuildContext context,
    WidgetRef ref,
    bool shouldPopAfterButtonPress,
  ) {
    final setDisableNfcCardReader =
        ref.read(cardReaderScreenProvider.notifier).setDisableNfcCardReader;
    final cardReaderScreenController =
        ref.watch(cardReaderScreenProvider.notifier);

    if (shouldPopAfterButtonPress) {
      // trueの場合はダイアログを閉じてマイナンバーカードを読み込む
      Navigator.of(context).pop();
    }

    if (ref.read(cardReaderScreenProvider).cardReadingAndroidCanceled) {
      cardReaderScreenController.androidCanceledReset();
    }

    // マイナンバーカードの読み取り処理開始(iOS/Android)
    Platform.isIOS
        ? cardReaderScreenController.startNfcSession()
        : androidReadMyNumberCard(
            context,
            setDisableNfcCardReader,
            cardReaderScreenController,
            ref,
          );
  }
}

// Androidでのカード読み取り、カスタマイズモーダルの出しわけを行う処理
void androidReadMyNumberCard(
  BuildContext context,
  Future<void> Function() setDisableNfcCardReader,
  CardReaderScreenNotifier cardReaderScreenController,
  WidgetRef ref,
) {
  // Androidカスタムモーダルは、独立した3つのダイアログを順番に表示する方針
  Navigator.popUntil(
    context,
    (route) => route.settings.name == RouteNames.cardReader,
  );

  // 読み取り準備開始モーダルを表示させる
  cardReaderModalSetup(
    context,
    setDisableNfcCardReader,
    ref,
  );
  // F_JLA_01 NFCリーダーモード設定を呼び出す（標準モードからリーダーモードに切り替える）
  cardReaderScreenController.setEnableNfcCardReader().then((value) async {
    // F_JLA_01 NFCリーダーモード設定の呼び出し成功
    if (value && ref.read(cardReaderScreenProvider).sdkError == null) {
      // 非同期処理中にcontextがアンマウントされていないことを確認
      if (context.mounted) {
        if (ref.read(cardReaderScreenProvider).cardReadingAndroidCanceled) {
          return;
        }
        // 読み取り準備開始モーダルを閉じて、読み取り中モーダルを表示させる
        Navigator.of(context).pop();
        cardReaderModalReading(
          context,
          setDisableNfcCardReader,
          ref,
        );
        Future.delayed(const Duration(milliseconds: 500), () {
          if (ref.read(cardReaderScreenProvider).cardReadingAndroidCanceled) {
            return;
          }
          if (context.mounted) Navigator.of(context).pop();
          final errorPattern = ref.read(cardReaderScreenProvider).errorPattern;
          if (errorPattern != null) {
            cardReaderScreenController.updateErrorPattern(errorPattern);
            return;
          }
          //成功モーダルの表示
          if (context.mounted) cardReaderModalSuccess(context, ref);
          Future.delayed(const Duration(seconds: 3), () {
            if (context.mounted) Navigator.of(context).pop();
            // 成功モーダル表示が終わったら読み取り完了とする
            cardReaderScreenController.updateCardReadingAndroidCompleted(true);
          });
        });
      }

      // F_JLA_01 NFCリーダーモード設定の呼び出し失敗
    } else {
      // 読み取り準備開始モーダルのキャンセルボタン押下されていない場合はモーダルを閉じる
      // キャンセルボタン押下時はすでにモーダルを閉じているためそのままスキップする
      if (context.mounted &&
          !ref.read(cardReaderScreenProvider).cardReadingAndroidCanceled) {
        Navigator.of(context).pop();
      }
      ref.read(cardReaderScreenProvider.notifier).updateIsCanceled(false);
      // F_JLA_02 NFC標準モード設定を呼び出す(リーダーモードから標準モードに切り替える)
      await cardReaderScreenController.setDisableNfcCardReader();
      if (context.mounted) {
        cardReaderModalReading(
          context,
          setDisableNfcCardReader,
          ref,
        );
        Future.delayed(const Duration(milliseconds: 500), () {
          if (ref.read(cardReaderScreenProvider).cardReadingAndroidCanceled) {
            // キャンセルボタン押下時はすでにモーダルが閉じているため後続のハンドリングは不要
            cardReaderScreenController.androidCanceledReset();
            return;
          }

          // 読み取り中開始モーダルを閉じる
          if (context.mounted) Navigator.of(context).pop();
          final androidErrorPattern =
              ref.read(cardReaderScreenProvider).sdkError;
          Log.d('androidErrorPattern: ${androidErrorPattern?.code}');
          if (androidErrorPattern != null) {
            cardReaderScreenController.updateErrorPattern(
              ErrorInfoJpkiSdkAndroid.getErrorPattern(
                errorCode: androidErrorPattern.code,
              ),
            );
          }
        });
      }
    }
  });
}

class CardReaderScreenWidgets extends StatelessWidget {
  const CardReaderScreenWidgets({
    super.key,
    required this.ref,
  });

  final WidgetRef ref;

  @override
  Widget build(BuildContext context) {
    final logController = ref.read(analyticsLogControllerProvider);
    final cardReaderScreenController =
        ref.watch(cardReaderScreenProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Center(
          child: Image.asset(
            Assets.myNumberScanImage,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          AppLocalizations.of(context)!.jpkiCardReaderScreenContentExplanation,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            const SizedBox(width: 8),
            SvgPicture.asset(
              Assets.middleDot,
              width: 8,
              height: 8,
            ),
            const SizedBox(width: 16),
            Text(AppLocalizations.of(context)!.jpkiPersonalInfo),
          ],
        ),
        const SizedBox(height: 16),
        TextLinkArrow(
          text: AppLocalizations.of(context)!.jpkiNotesOnReading,
          onTap: () {
            logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.jpkiNotesOnReading,
              screenName: AppLocalizations.of(context)!.myNumberCardReadScreen,
            );
            ContextClass.setContext(context);
            scanAttentionInfoModal(
              context,
              ref,
              iOSAction: () {
                cardReaderScreenController.startNfcSession();
              },
              androidAction: () {
                androidReadMyNumberCard(
                  context,
                  ref
                      .read(cardReaderScreenProvider.notifier)
                      .setDisableNfcCardReader,
                  cardReaderScreenController,
                  ref,
                );
              },
            );
          },
        ),
      ],
    );
  }
}

class DummyBody extends HookConsumerWidget {
  const DummyBody({
    super.key,
    required this.log,
  });

  final String log;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        border: Border.all(color: AppColors.red),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '本人確認APIのログ出力',
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: AppColors.black),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    log,
                    style: Theme.of(context)
                        .textTheme
                        .bodyLarge!
                        .copyWith(color: AppColors.black),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ContextClass {
  static BuildContext? buildContext;

  static void setContext(BuildContext context) {
    buildContext = context;
  }
}
