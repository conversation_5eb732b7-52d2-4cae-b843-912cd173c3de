import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/content_area.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/screens/identity_verification/widget/app_bar.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class PasswordInputExplanationScreen extends HookConsumerWidget {
  const PasswordInputExplanationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 画面幅
    final double unifiedWidth = MediaQuery.of(context).size.width * 0.9;
    final logController = ref.read(analyticsLogControllerProvider);

    // Firebase Screenログ・KARTE Viewイベント送信
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.passwordInputExplanationScreenId,
            screenName:
                AppLocalizations.of(context)!.passwordInputExplanationScreen,
          );
        });
        return null;
      },
      [],
    );

    return ColoredBox(
      color: AppColors.white,
      child: SafeArea(
        top: false,
        child: Scaffold(
          appBar: AppBarForIdentityVerification(
            title: AppLocalizations.of(context)!.identificationAppBarTitle,
            onTapLeadingLogAction: () => logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.back,
              screenName:
                  AppLocalizations.of(context)!.passwordInputExplanationScreen,
            ),
          ),
          body: Stack(
            children: [
              Image.asset(
                Assets.bgWaveGrey,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
              ),
              Stack(
                children: [
                  SingleChildScrollView(
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      children: <Widget>[
                        ScreenNumber(
                          screenNumber:
                              ScreenIdNumber.passwordInputExplanationScreenId,
                          color: AppColors.textBlack,
                        ),
                        _ScreenContentContainer(
                          ref: ref,
                          unifiedWidth: unifiedWidth,
                        ),
                        const SizedBox(
                          height: 120,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      width: double.infinity,
                      height: 120,
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        boxShadow: [
                          // SafeArea部分にも影が入ってしまうため、SafeAreaの境目に白色の影を追加
                          BoxShadow(
                            offset: const Offset(0, -2),
                            color: AppColors.cardShadow.withOpacity(0.15),
                            blurRadius: 4,
                          ),
                          const BoxShadow(
                            color: AppColors.white,
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Column(
                        children: [
                          SizedBox(height: 16),
                          // 次へ進むボタン
                          _NextButton(),
                          SizedBox(height: 16),
                          // 他の方法で本人確認するボタン
                          _OtherIdentityVerification(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ForgetPassword extends StatelessWidget {
  const _ForgetPassword({
    required this.ref,
    required this.unifiedWidth,
  });

  final WidgetRef ref;
  final double unifiedWidth;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: unifiedWidth,
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: AppLocalizations.of(context)!.passwordForgotTitle,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.textBlack,
                      height: 1.5,
                    ),
              ),
              TextSpan(
                text: AppLocalizations.of(context)!.here,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                      decoration: TextDecoration.underline,
                      decorationColor: AppColors.primary,
                      height: 1.5,
                    ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    launchURL(context, ref);
                  },
              ),
              WidgetSpan(
                child: GestureDetector(
                  onTap: () {
                    launchURL(context, ref);
                  },
                  child: const SizedBox(
                    width: 4,
                  ),
                ),
              ),
              WidgetSpan(
                child: GestureDetector(
                  onTap: () {
                    launchURL(context, ref);
                  },
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        Assets.actionIcon,
                        width: 16,
                        height: 16,
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 外部URLを開くための関数
  Future<void> launchURL(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final logController = ref.watch(analyticsLogControllerProvider);
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName: AppLocalizations.of(context)!.here,
      screenName: AppLocalizations.of(context)!.passwordInputExplanationScreen,
    );
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return CommonDialog.okCancel(
          title: AppLocalizations.of(context)!.moveToExternalSite,
          message: AppLocalizations.of(context)!.confirmExternalSiteNavigation,
          onOkPressed: () async {
            final logController = ref.read(analyticsLogControllerProvider);
            logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.yes,
              screenName:
                  AppLocalizations.of(context)!.passwordInputExplanationScreen,
            );
            if (!context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    AppLocalizations.of(context)!.urlOpenErrorMessage,
                  ),
                ),
              );
              return;
            }
            // パスワード忘れのページを開く
            await ref.watch(urlLauncherProvider).launchLinkId(
                  LinkIds.forgotPassword,
                  AppLocalizations.of(context)!.yes,
                  ScreenIdNumber.passwordInputExplanationScreenId,
                );
          },
          onCancelPressed: () {
            // FirebaseAnalyticsログ送信
            logController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.no,
              screenName:
                  AppLocalizations.of(context)!.passwordInputExplanationScreen,
            );
          },
          okButtonText: AppLocalizations.of(context)!.yes,
          cancelButtonText: AppLocalizations.of(context)!.no,
        );
      },
    );
  }
}

class _ScreenContentContainer extends StatelessWidget {
  const _ScreenContentContainer({
    required this.ref,
    required this.unifiedWidth,
  });

  final WidgetRef ref;
  final double unifiedWidth;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ContentArea(
        context: context,
        minHeight: 427,
        title: AppLocalizations.of(context)!.passwordInputExplanationTitle,
        widget: Column(
          children: [
            const SizedBox(
              height: 16,
            ),
            Image.asset(Assets.passwordInputExplanationImage),
            const SizedBox(
              height: 16,
            ),
            Text(
              AppLocalizations.of(context)!.passwordInputExplanationContent,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontSize: 12,
                    height: 1.5,
                  ),
            ),
            const SizedBox(
              height: 24,
            ),
            _ForgetPassword(ref: ref, unifiedWidth: unifiedWidth),
          ],
        ),
      ),
    );
  }
}

class _NextButton extends HookConsumerWidget {
  const _NextButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return SizedBox(
      width: 240,
      height: 48,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.tradGreen,
          disabledForegroundColor:
              AppColors.tradGreen.withOpacity(0.5).withOpacity(0.38),
          disabledBackgroundColor: AppColors.tradGreen.withOpacity(0.06),
        ),
        onPressed: () {
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.next,
            screenName:
                AppLocalizations.of(context)!.passwordInputExplanationScreen,
          );
          Navigator.pushNamed(
            context,
            RouteNames.passwordInput,
          );
        },
        child: Text(
          AppLocalizations.of(context)!.next,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w700,
              ),
        ),
      ),
    );
  }
}

class _OtherIdentityVerification extends HookConsumerWidget {
  const _OtherIdentityVerification();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return Center(
      child: TextButton(
        style: CustomButtonStyle.textButtonStyle,
        onPressed: () {
          logController.sendButtonLog(
            buttonName:
                AppLocalizations.of(context)!.otherIdentityVerificationMethod,
            screenName:
                AppLocalizations.of(context)!.passwordInputExplanationScreen,
          );
          Navigator.pushNamedAndRemoveUntil(
            context,
            RouteNames.ekycExplanation,
            (route) => route.settings.name == RouteNames.identityDocument,
          );
        },
        child: Text(
          AppLocalizations.of(context)!.otherIdentityVerificationMethod,
        ),
      ),
    );
  }
}
