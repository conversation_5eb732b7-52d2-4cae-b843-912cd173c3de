import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/credit_card_statement_inquiry/credit_card_statement_inquiry_screen_notifier.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/utils/ext/int_ext.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/credit_card_inquiry_error_body.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/credit_card_narrow_down_modal.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/credit_card_switch_modal.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/info_title_and_divider.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/no_result_body.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/sort_area.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:dtp_app/views/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class CreditCardStatementInquiryScreen extends HookConsumerWidget
    with CommonErrorHandlerMixin {
  const CreditCardStatementInquiryScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeScreenState = ref.watch(homeScreenProvider);
    final homeScreenController = ref.read(homeScreenProvider.notifier);
    final creditCardState = ref.watch(creditCardStatementInquiryScreenProvider);
    final creditCardStatementInquiryScreenController =
        ref.read(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    // 明細ボタンから遷移した場合：選択されたクレジットカード情報のインデックスを指定する
    // フッターのモーダルから遷移した場合：金融機関連携エラーが発生していない
    // 且つ、クレジットカード情報の取得順で新しいもののインデックスを指定する
    final itemIndex = homeScreenState.selectCreditCardExpenseIndex ??
        homeScreenController.getCreditCardStatementInquiryIndex();
    final hasError = creditCardState.error != null;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.cardDetailScreenId,
            screenName: AppLocalizations.of(context)!.cardDetail,
          );

          final creditCarsExpense =
              homeScreenState.freeeCreditCardExpenses[itemIndex];

          // 明細ボタン押下時のitemIndexをリセット
          homeScreenController.updateItemIndex(null);

          /// 初回表示時のクレジットカードのインデックスを更新
          creditCardStatementInquiryScreenController.updateFirstCreditCardIndex(
            itemIndex,
            homeScreenState.freeeCreditCardExpenses,
          );

          // カード情報が変わったためフィルター解除
          creditCardStatementInquiryScreenController.narrowDownReset();

          // ローディング開始
          if (context.mounted) LoadingDialog.loading(context);

          // 初回読み込み
          await creditCardStatementInquiryScreenController
              .loadCreditCardTransactionHistory(
            creditCardExpense: creditCarsExpense,
          );

          // ローディング終了
          if (context.mounted) LoadingDialog.loadingEnd(context);
        });
        return null;
      },
      const [],
    );

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 共通エラー検知時に共通エラーダイアログを表示する
          handleError(
            context,
            ref,
            creditCardState.error?.copyWith(isShowOnDialog: false),
            onOkTap: () {},
            screenId: ScreenIdNumber.cardDetailScreenId,
            screenName: AppLocalizations.of(context)!.cardDetail,
          );
        });
        return null;
      },
      [creditCardState.error],
    );

    return Container(
      color: AppColors.tradGreen,
      child: SafeArea(
        bottom: false,
        child: Container(
          color: theme.colorScheme.surface,
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Container(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: InfoTitleWithDivider(
                    title: AppLocalizations.of(context)!.cardDetailTitle,
                    child: Container(
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          alignment: Alignment.bottomCenter,
                          image: AssetImage(
                            Assets.bgWaveGrey,
                          ),
                          fit: BoxFit.fitWidth,
                        ),
                      ),
                      child: Column(
                        children: [
                          const _FetchedDateTimeScreenNumberArea(),
                          const _YearMonthArea(),
                          const _CardDetailArea(),
                          const SizedBox(height: 16),
                          //　正常系の表示
                          Visibility(
                            visible: !hasError,
                            child: const _CreditCardTransactionHistory(),
                          ),
                          // 異常系の表示
                          Visibility(
                            visible: hasError,
                            child: ErrorDisplayArea(
                              errorCode: creditCardState.error?.code ?? '',
                              errorMessage:
                                  creditCardState.error?.message ?? '',
                              hasFaq: creditCardState.error?.hasFaq ?? false,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class _CreditCardTransactionHistory extends StatelessWidget {
  const _CreditCardTransactionHistory();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const _OptionArea(),
        const SizedBox(height: 22),
        const _FreeeTransactionHistoryList(),
      ],
    );
  }
}

class _FetchedDateTimeScreenNumberArea extends HookConsumerWidget {
  const _FetchedDateTimeScreenNumberArea();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lastSyncedAt = ref.watch(
      creditCardStatementInquiryScreenProvider
          .select((value) => value.creditCardExpense?.lastSyncedAt),
    );

    return Container(
      color: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
        // 照会日時
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Text(
                  AppLocalizations.of(context)!.fetchedDateTime,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.textGrayGreen,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                      ),
                ),
                const SizedBox(width: 8),
                NullableHyphenText(
                  data: lastSyncedAt,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                        color: AppColors.textGrayGreen,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                      ),
                ),
              ],
            ),
            // 画面番号
            // 画面番号が共通部品と異なるレイアウト（照会日時と並列）のため、個別に実装
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              textBaseline: TextBaseline.alphabetic,
              children: [
                Text(
                  AppLocalizations.of(context)!.gamenBangou,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontSize: 12,
                        color: AppColors.textBlack,
                      ),
                ),
                Text(
                  ScreenIdNumber.cardDetailScreenId,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                        fontSize: 12,
                        color: AppColors.textBlack,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _YearMonthArea extends HookConsumerWidget {
  const _YearMonthArea();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final controller =
        ref.read(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final baseDateTime = state.baseDateTime;
    final dateTo = state.dateTo;
    final selectedYearMonthIndex = state.selectedYearMonthIndex;

    final deviceHeight = MediaQuery.of(context).size.height;
    final scrollController = useScrollController();
    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    const itemCount = 13;
    const lastIndex = itemCount - 1;

    final selectedIndex = useState(0);

    // 初期値をnullにする
    final selectedYearMonth = useState<DateTime?>(null);

    return Container(
      color: AppColors.white,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              top: 8,
              left: 16,
              right: 16,
              bottom: 16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    GestureDetector(
                      onTap: () async {
                        await showDialog(
                          context: context,
                          builder: (_) {
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              // 選択した年月がダイアログのスクロール領域に隠れないように制御
                              if (selectedYearMonthIndex >= 7) {
                                scrollController.jumpTo(
                                  scrollController.position.maxScrollExtent,
                                );
                              }
                            });
                            return AlertDialog(
                              insetPadding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              contentPadding:
                                  const EdgeInsets.symmetric(vertical: 0),
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.all(
                                  Radius.circular(8),
                                ),
                              ),
                              content: Container(
                                // 高解像度の端末（基準高さ932）は表示期間の幅を広げる
                                height: deviceHeight >= 932 ? 580 : 375,
                                width: 343,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: ListView.builder(
                                    controller: scrollController,
                                    itemCount: itemCount,
                                    itemBuilder: (context, index) {
                                      final state = ref.watch(
                                        creditCardStatementInquiryScreenProvider,
                                      );
                                      final yearMonth =
                                          state.yearMonthList[index];
                                      return Column(
                                        children: [
                                          if (index == 0)
                                            const SizedBox(height: 12),
                                          _YearMonthItem(
                                            yearMonth: yearMonth,
                                            index: index,
                                            onTap: () {
                                              analyticsLogController
                                                  .sendButtonLog(
                                                buttonName: AppLocalizations.of(
                                                  context,
                                                )!
                                                    .selectYearMonthList,
                                                screenName: AppLocalizations.of(
                                                  context,
                                                )!
                                                    .cardDetail,
                                              );
                                              selectedIndex.value = index;
                                              selectedYearMonth.value =
                                                  yearMonth;
                                              controller.narrowDownReset();
                                              Navigator.of(context).pop();
                                            },
                                          ),
                                          if (index == lastIndex)
                                            const SizedBox(height: 12),
                                        ],
                                      );
                                    },
                                  ),
                                ),
                              ),
                            );
                          },
                        );

                        // 日付が選択されていない場合、またはクレジットカード情報がない場合は明細を取得しない
                        if (selectedYearMonth.value == null ||
                            baseDateTime == null) {
                          return;
                        }

                        final isSame = controller.checkSameYearMonth(
                          yearMonth: selectedYearMonth.value!,
                          selectedYearMonth: state.selectedYearMonth!,
                        );

                        // 日付が変更されていない場合は明細を取得しない
                        if (isSame) {
                          return;
                        }

                        final isCurrentMonth = controller.checkCurrentMonth(
                          yearMonth: selectedYearMonth.value!,
                          dateTime: baseDateTime,
                        );

                        final dateFrom = DateTime(
                          selectedYearMonth.value!.year,
                          selectedYearMonth.value!.month,
                          1,
                        );

                        final dateTo = isCurrentMonth
                            ? baseDateTime
                            : DateTime(
                                selectedYearMonth.value!.year,
                                selectedYearMonth.value!.month + 1,
                                0,
                              );

                        // FireBaseAnalytics送信
                        if (context.mounted) {
                          analyticsLogController.sendButtonLog(
                            buttonName:
                                AppLocalizations.of(context)!.displayYearMonth,
                            screenName:
                                AppLocalizations.of(context)!.cardDetail,
                          );
                        }

                        // ローディング開始
                        if (context.mounted) LoadingDialog.loading(context);

                        // クレジットカード口座明細を取得する
                        await controller.getCreditCardTransactionHistory(
                          sortType: state.currentSortType,
                          order: state.currentSortOrder,
                          dateFrom: dateFrom,
                          dateTo: dateTo,
                          yearMonth: selectedYearMonth.value!,
                          selectedYearMonthIndex: selectedIndex.value,
                          sortTitle: state.sortTitle,
                        );
                        // 絞り込みとソート時に参照している日付を更新する
                        controller.checkFirstDayAndLastDayCalendar(
                          getFirstDate(baseDateTime.toString()),
                        );
                        if (!context.mounted) return;
                        // ローディング終了
                        LoadingDialog.loadingEnd(context);
                      },
                      child: Row(
                        children: [
                          NullableHyphenText(
                            data: dateTo == null
                                ? null
                                : DateFormat('yyyy年M月').format(dateTo),
                            style:
                                Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      fontWeight: FontWeight.w700,
                                      height: 1.5,
                                      fontSize: 24,
                                      color: AppColors.tradGreen,
                                    ),
                          ),
                          const SizedBox(width: 8),
                          SvgPicture.asset(
                            height: 24,
                            width: 24,
                            Assets.arrowDownBottomIcon,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _CardDetailArea extends HookConsumerWidget {
  const _CardDetailArea();

  // M/d形式に変換
  String _convertDateString(DateTime dateTime) {
    return DateFormat('M/d').format(dateTime);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);

    // 3桁区切りのカンマ含めて19文字まで許容
    const totalCreditCardExpenseMaxDigit = 19;
    final isValidTotalDigit =
        (state.getTotalExpense?.length ?? 0) <= totalCreditCardExpenseMaxDigit;

    final dateFrom = state.dateFrom;
    final dateTo = state.dateTo;

    return Container(
      color: AppColors.white,
      padding: const EdgeInsets.only(
        top: 16,
        left: 16,
        right: 16,
        bottom: 0,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.totalUsageAmount,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            color: AppColors.textGrayGreen,
                            fontWeight: FontWeight.w700,
                            fontSize: 12,
                            height: 1.5,
                          ),
                    ),
                    SizedBox(
                      height: 4,
                    ),
                    NullableHyphenText(
                      data: (dateFrom == null || dateTo == null)
                          ? null
                          : '${_convertDateString(dateFrom)}~${_convertDateString(dateTo)}',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                            height: 1.5,
                            fontFamily: FontFamily.robotoCondensed,
                          ),
                    ),
                  ],
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SizedBox(
                    height: 42,
                    child: NullableHyphenText(
                      data: isValidTotalDigit
                          ? state.initialTotalExpense
                          : AppLocalizations.of(context)!
                              .excessBalanceErrorMessage,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontWeight: FontWeight.w700,
                            fontSize: 28,
                            height: 1.5,
                          ),
                    ),
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  !isValidTotalDigit
                      ? const SizedBox.shrink()
                      : Container(
                          // 数字の下と円を強引に合わせるためのpadding
                          padding: const EdgeInsets.only(bottom: 6),
                          alignment: Alignment.bottomCenter,
                          height: 42,
                          child: Text(
                            AppLocalizations.of(context)!.yenJp,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 14,
                                  height: 1.5,
                                ),
                          ),
                        ),
                ],
              ),
            ],
          ),
          const SizedBox(
            height: 16,
          ),
          const Divider(
            color: AppColors.infoScreenDivider,
            thickness: 1,
            height: 0,
            indent: 0,
          ),
          const SizedBox(
            height: 16,
          ),
          _CreditCardName(
            creditCardName: state.creditCardExpense?.creditCardName,
          ),
          const SizedBox(
            height: 45,
          ),
          _CardSwitchButton(),
          const SizedBox(
            height: 24,
          ),
        ],
      ),
    );
  }
}

class _OptionArea extends HookConsumerWidget {
  const _OptionArea();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final freeeTransactionHistory = ref.watch(
      creditCardStatementInquiryScreenProvider
          .select((value) => value.freeeTransactionHistory),
    );
    final itemCount = freeeTransactionHistory.map(
      empty: (_) => 0,
      data: (data) => data.items.length,
    );

    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        bottom: 0,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: AppLocalizations.of(context)!.periodTotal,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                            color: AppColors.textGrayGreen,
                          ),
                    ),
                    const WidgetSpan(
                      child: SizedBox(width: 8),
                    ),
                    TextSpan(
                      text: itemCount.toString(),
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            fontSize: 24,
                          ),
                    ),
                    const WidgetSpan(
                      child: SizedBox(width: 4),
                    ),
                    TextSpan(
                      text: AppLocalizations.of(context)!.itemCountUnit,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 14,
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                  ],
                ),
              ),
              const Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _NarrowDownButton(),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(
            height: 22,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SortArea(),
            ],
          ),
        ],
      ),
    );
  }
}

class _YearMonthItem extends HookConsumerWidget {
  const _YearMonthItem({
    required this.yearMonth,
    required this.index,
    this.onTap,
  });

  final DateTime yearMonth;
  final int index;
  final void Function()? onTap;

  String _format(DateTime? dateTime) =>
      dateTime != null ? DateFormat('yyyy年M月').format(dateTime) : '';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final isSelected = state.selectedYearMonth == yearMonth;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 48,
        width: double.infinity,
        padding: const EdgeInsets.only(left: 16),
        alignment: Alignment.centerLeft,
        color: isSelected ? AppColors.lightGreen2 : AppColors.white,
        child: Text(
          _format(yearMonth),
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                color: isSelected ? AppColors.tradGreen : AppColors.textBlack,
              ),
        ),
      ),
    );
  }
}

class _CreditCardName extends HookConsumerWidget {
  const _CreditCardName({
    required String? creditCardName,
  }) : displayName = creditCardName ?? 'ー';

  final String displayName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final controller =
        ref.read(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // カード名
    final displayNameTextStyle = Theme.of(context)
        .textTheme
        .bodyMedium!
        .copyWith(fontWeight: FontWeight.w700);

    // カード名の表示幅
    // 画面サイズからパディングとアイコンサイズを引いて算出
    final displayNameAreaWidth = MediaQuery.of(context).size.width - 62;

    // 1行に収まるカード名の最大文字数を計算
    final displayNameMaxLength = displayName.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // カード名がオーバーフローしているかを判定
    final isOverFlowed = displayName.length > displayNameMaxLength;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      textBaseline: TextBaseline.alphabetic,
      children: [
        Expanded(
          child: SizedBox(
            // オーバーフローかつアコーディオンを開いていない時だけ「...」表示
            child: Text(
              !isOverFlowed || state.isCreditCardNameAccordionExpanded
                  ? displayName
                  : displayName.ellipsis(
                      maxLength: displayNameMaxLength,
                    ),
              style: displayNameTextStyle,
            ),
          ),
        ),
        Visibility(
          visible: isOverFlowed,
          child: Row(
            children: [
              const SizedBox(width: 8),
              SizedBox(
                width: 16,
                child: GestureDetector(
                  onTap: () {
                    analyticsLogController.sendButtonLog(
                      buttonName:
                          AppLocalizations.of(context)!.switchCardNameAccordion,
                      screenName: AppLocalizations.of(context)!.cardDetail,
                    );
                    controller.updateIsCreditCardNameAccordionExpanded(
                      !state.isCreditCardNameAccordionExpanded,
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(top: 6),
                    child: SvgPicture.asset(
                      state.isCreditCardNameAccordionExpanded
                          ? Assets.arrowUpIcon
                          : Assets.arrowDownIcon,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _CardSwitchButton extends HookConsumerWidget {
  const _CardSwitchButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final creditCardStatementInquiryScreenController =
        ref.read(creditCardStatementInquiryScreenProvider.notifier);
    final homeScreenState = ref.watch(homeScreenProvider);
    final creditCardStatementInquiryScreenState =
        ref.watch(creditCardStatementInquiryScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Align(
      alignment: Alignment.center,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          width: 140,
          height: 36,
          color: AppColors.white,
          child: OutlinedButton(
            onPressed: () async {
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.cardSwitch,
                screenName: AppLocalizations.of(context)!.cardDetail,
              );
              await showModalBottomSheet(
                enableDrag: false,
                useSafeArea: true,
                backgroundColor: AppColors.transparent,
                isScrollControlled: true,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(8),
                  ),
                ),
                context: context,
                builder: (BuildContext context) {
                  return const CreditCardSwitchModal();
                },
              );

              final creditCardExpense = homeScreenState.freeeCreditCardExpenses[
                  ref
                      .watch(creditCardStatementInquiryScreenProvider)
                      .creditCardIndex];

              if (creditCardExpense !=
                  creditCardStatementInquiryScreenState.creditCardExpense) {
                // カード情報が変わったためフィルター解除
                creditCardStatementInquiryScreenController.narrowDownReset();

                // ローディング開始
                if (context.mounted) LoadingDialog.loading(context);

                // 読み込み
                await creditCardStatementInquiryScreenController
                    .loadCreditCardTransactionHistory(
                  creditCardExpense: creditCardExpense,
                );

                // ローディング終了
                if (context.mounted) LoadingDialog.loadingEnd(context);
              }
            },
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.zero,
              side: const BorderSide(
                color: AppColors.tradGreen,
              ),
              backgroundColor: AppColors.white,
            ),
            child: Row(
              children: [
                const SizedBox(
                  width: 16,
                ),
                SizedBox(
                  width: 24,
                  height: 24,
                  child: SvgPicture.asset(Assets.syncAltIcon),
                ),
                const SizedBox(
                  width: 4,
                ),
                SizedBox(
                  width: 80,
                  height: 24,
                  child: Text(
                    AppLocalizations.of(context)!.cardSwitch,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 16,
                          color: AppColors.tradGreen,
                          fontWeight: FontWeight.w700,
                          height: 1.5,
                        ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _NarrowDownButton extends HookConsumerWidget {
  const _NarrowDownButton();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        width: 124,
        height: 36,
        color: AppColors.white,
        child: OutlinedButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.narrowDown,
              screenName: AppLocalizations.of(context)!.cardDetail,
            );
            showModalBottomSheet(
              backgroundColor: AppColors.white,
              enableDrag: false,
              isScrollControlled: true,
              useSafeArea: true,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(
                  Radius.circular(8),
                ),
              ),
              context: context,
              builder: (BuildContext context) {
                return CreditCardNarrowDownModal(
                  serverDate: state.baseDateTime.toString(),
                );
              },
            );
          },
          style: OutlinedButton.styleFrom(
            padding: EdgeInsets.zero,
            side: const BorderSide(
              color: AppColors.tradGreen,
            ),
            backgroundColor: AppColors.white,
          ),
          child: Row(
            children: [
              const SizedBox(
                width: 16,
              ),
              SizedBox(
                width: 24,
                height: 24,
                child: SvgPicture.asset(Assets.filterIcon),
              ),
              const SizedBox(
                width: 4,
              ),
              SizedBox(
                width: 64,
                height: 24,
                child: Text(
                  AppLocalizations.of(context)!.narrowDown,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontSize: 16,
                        color: AppColors.tradGreen,
                        fontWeight: FontWeight.w700,
                        height: 1.5,
                      ),
                ),
              ),
              Align(
                alignment: const Alignment(1, -0.44),
                child: state.isNarrowDown
                    ? Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.orange,
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FreeeTransactionHistoryList extends HookConsumerWidget {
  const _FreeeTransactionHistoryList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final freeeTransactionHistory = ref.watch(
      creditCardStatementInquiryScreenProvider
          .select((value) => value.freeeTransactionHistory),
    );
    // 明細が空の時は明細がないことを表示するため、itemCountを1にする
    final itemCount = freeeTransactionHistory.map(
      empty: (_) => 1,
      data: (data) => data.items.length,
    );
    return Column(
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: itemCount,
          itemBuilder: (context, index) {
            return freeeTransactionHistory.map(
              empty: (_) => const NoResultBody(),
              data: (data) => _FreeeTransactionHistoryListItem(
                item: data.items[index],
              ),
            );
          },
        ),
      ],
    );
  }
}

class _Description extends HookConsumerWidget {
  const _Description({
    required String? description,
  }) : displayName = description ?? 'ー';

  final String displayName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    // 取引内容
    final displayNameTextStyle =
        Theme.of(context).textTheme.bodySmall!.copyWith(
              fontFamily: FontFamily.robotoCondensed,
              fontSize: 12,
              height: 1.5,
            );

    // 補足
    // 取引内容は2行で表示しきれない場合にアコーディオンが表示されるが、
    // 全角半角などを考慮すると、1行目と2行目のスペースで入る文字列が異なるため、それぞれ計算するよう実装

    // 取引内容の表示幅
    // 画面サイズからパディングとアイコンサイズを引いて算出
    final displayNameAreaWidth = (MediaQuery.of(context).size.width) - 170;

    // 1行目に収まる取引内容の最大文字数を計算
    final displayName1LineMaxLength = displayName.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // 2行目に表示される取引内容文字列
    final displayName2Line = displayName.substring(displayName1LineMaxLength);

    // 2行目に収まる取引内容の最大文字数を計算
    final displayName2LineMaxLength = displayName2Line.getMaxTextLength(
      textStyle: displayNameTextStyle,
      maxWidth: displayNameAreaWidth,
    );

    // 1行目と2行目で入りきる文字列数
    final totalDisplayNameMaxLength =
        displayName1LineMaxLength + displayName2LineMaxLength;

    // 取引内容がオーバーフローしているかを判定
    final isOverFlowed = displayName.length > totalDisplayNameMaxLength;

    // アコーディオンを開いているかのフラグ
    final isAccordionExpanded = useState(false);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        !isOverFlowed || isAccordionExpanded.value
            ? SizedBox(
                width: displayNameAreaWidth,
                child: Text(
                  displayName,
                  style: displayNameTextStyle,
                ),
              )
            :
            // オーバーフローかつアコーディオンを開いていない時だけ「...」表示
            SizedBox(
                width: displayNameAreaWidth,
                child: Text(
                  displayName.ellipsis(
                    maxLength: totalDisplayNameMaxLength,
                  ),
                  style: displayNameTextStyle,
                  maxLines: 2,
                ),
              ),
        Visibility(
          visible: isOverFlowed,
          child: Row(
            children: [
              const SizedBox(width: 8),
              SizedBox(
                width: 16,
                child: GestureDetector(
                  onTap: () {
                    logController.sendButtonLog(
                      buttonName: AppLocalizations.of(context)!
                          .switchTransactionDetailsAccordion,
                      screenName: AppLocalizations.of(context)!.cardDetail,
                    );
                    isAccordionExpanded.value = !isAccordionExpanded.value;
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(top: 0),
                    child: SvgPicture.asset(
                      isAccordionExpanded.value
                          ? Assets.arrowUpIcon
                          : Assets.arrowDownIcon,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _FreeeTransactionHistoryListItem extends StatelessWidget {
  const _FreeeTransactionHistoryListItem({
    required this.item,
  });

  final FreeeTransactionHistoryDetail item;

  @override
  Widget build(BuildContext context) {
    // 表示される金額(マイナス有無)を用意
    final isIncome = item.type == FreeeTransactionHistoryType.income;
    final amountDouble = item.amount ?? 0;
    final displayAmount = isIncome
        ? (amountDouble * -1).withCommasAndMinusSign
        : amountDouble.withCommasAndMinusSign;

    // 3桁区切りのカンマ含めて19文字まで許容
    const creditCardExpenseMaxDigit = 19;
    final isValidTotalDigit = displayAmount.length <= creditCardExpenseMaxDigit;

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: const [
            BoxShadow(
              color: AppColors.incomeAndExpenditureBackgroundColor,
              blurRadius: 4,
              spreadRadius: 0,
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(vertical: 13, horizontal: 16),
        child: Column(
          children: [
            // 取引日、金額
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 48,
                  child: Text(
                    AppLocalizations.of(context)!.dateOfUse,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.textGrayGreen,
                          fontWeight: FontWeight.w700,
                          fontSize: 12,
                          height: 1.5,
                        ),
                  ),
                ),
                SizedBox(
                  width: 16,
                ),
                NullableHyphenText(
                  data: item.tradingDate,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                        fontSize: 12,
                      ),
                ),
              ],
            ),
            SizedBox(
              height: 5,
            ),
            // 取引内容
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 48,
                  child: Text(
                    AppLocalizations.of(context)!.statement,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppColors.textGrayGreen,
                          fontWeight: FontWeight.w700,
                          fontSize: 12,
                          height: 1.5,
                        ),
                  ),
                ),
                SizedBox(
                  width: 16,
                ),
                _Description(
                  description: item.description,
                ),
                const SizedBox(width: 18),
              ],
            ),
            const SizedBox(height: 9),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                item.amount == null
                    ? Text(AppLocalizations.of(context)!.fullWidthHyphen)
                    : Row(
                        children: [
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: isValidTotalDigit
                                      ? displayAmount
                                      : AppLocalizations.of(context)!
                                          .excessBalanceErrorMessage,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge!
                                      .copyWith(
                                        fontFamily: FontFamily.robotoCondensed,
                                        fontSize: 20,
                                      ),
                                ),
                                const WidgetSpan(
                                  child: SizedBox(width: 4),
                                ),
                                TextSpan(
                                  text: AppLocalizations.of(context)!.yenJp,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall!
                                      .copyWith(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w700,
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
