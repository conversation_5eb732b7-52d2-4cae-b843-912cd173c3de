import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/utils/faq_content.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ErrorDisplayArea extends HookConsumerWidget {
  const ErrorDisplayArea({
    super.key,
    required this.errorCode,
    required this.errorMessage,
    required this.hasFaq,
  });

  final String errorCode;
  final String errorMessage;
  final bool hasFaq;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.white,
          border: Border.all(color: AppColors.red),
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                errorCode,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.grayGreen,
                    ),
              ),
              const SizedBox(height: 15),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SvgPicture.asset(
                    Assets.alertIcon,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      errorMessage,
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            color: AppColors.red,
                          ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Column(
                children: [
                  Visibility(
                    visible: hasFaq,
                    child: FaqContent(
                      linkId: LinkIds.freeeLinksFaq,
                      buttonName: AppLocalizations.of(context)!.freeeFAQ,
                      screenId: ScreenIdNumber.cardDetailScreenId,
                      screenName:
                          AppLocalizations.of(context)!.creditCardStatement,
                      icon: Assets.actionIcon,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
