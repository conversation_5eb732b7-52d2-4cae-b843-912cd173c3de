import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// クレジット明細で0件のとき
class NoResultBody extends StatelessWidget {
  const NoResultBody({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
        bottom: 16,
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 64),
              color: AppColors.white,
              child: Center(
                child: Text(
                  AppLocalizations.of(context)!.noCreditCardStatementFound,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
