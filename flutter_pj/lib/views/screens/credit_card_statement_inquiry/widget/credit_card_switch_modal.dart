import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/credit_card_statement_inquiry/credit_card_statement_inquiry_screen_notifier.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/models/freee_credit_card_expense/freee_credit_card_expense.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// クレジットカード切替
class CreditCardSwitchModal extends HookConsumerWidget {
  const CreditCardSwitchModal({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeScreenState = ref.watch(homeScreenProvider);
    final controller =
        ref.read(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // カード切替モーダル用のリスト
    final creditCardSwitchModalList = controller.getCreditCardSwitchModalList(
      homeScreenState.freeeCreditCardExpenses,
    );
    // クレジットカードの一覧リスト
    final freeeCreditCardList = homeScreenState.freeeCreditCardExpenses;

    // クレジットカードの枚数が3枚以上だったらフルモーダルにする
    final isFullModal = creditCardSwitchModalList.length > 2;

    // Firebase Screenログ・KARTE Viewイベント送信
    analyticsLogController.sendScreenLog(
      screenNumber: ScreenIdNumber.cardSwitchModalId,
      screenName: AppLocalizations.of(context)!.creditCardSwitchModal,
    );

    return isFullModal
        ? _FullCreditCardSwitchModal(
            creditCardSwitchModalList: creditCardSwitchModalList,
            freeeCreditCardList: freeeCreditCardList,
          )
        : _HalfCreditCardSwitchModal(
            creditCardSwitchModalList: creditCardSwitchModalList,
            freeeCreditCardList: freeeCreditCardList,
          );
  }
}

class _FullCreditCardSwitchModal extends HookConsumerWidget {
  const _FullCreditCardSwitchModal({
    required this.creditCardSwitchModalList,
    required this.freeeCreditCardList,
  });

  final List<String> creditCardSwitchModalList;
  final List<CreditCardExpense> freeeCreditCardList;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final creditCardStatementInquiryScreenState =
        ref.watch(creditCardStatementInquiryScreenProvider);
    final creditCardIndex =
        useState(creditCardStatementInquiryScreenState.creditCardIndex);
    final selectCreditCardIndex =
        useState(creditCardStatementInquiryScreenState.selectCreditCardIndex);

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Container(
            height: 56,
            color: Colors.transparent,
          ),
        ),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
              color: AppColors.white,
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _CardSwitchModalMessageArea(),
                  _CardSwitchModalContentScrollArea(
                    creditCardSwitchModalList: creditCardSwitchModalList,
                    freeeCreditCardList: freeeCreditCardList,
                    creditCardIndex: creditCardIndex,
                    selectCreditCardIndex: selectCreditCardIndex,
                  ),
                  _CardSwitchModalButton(
                    creditCardIndex: creditCardIndex,
                    selectCreditCardIndex: selectCreditCardIndex,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _HalfCreditCardSwitchModal extends HookConsumerWidget {
  const _HalfCreditCardSwitchModal({
    required this.creditCardSwitchModalList,
    required this.freeeCreditCardList,
  });

  final List<String> creditCardSwitchModalList;
  final List<CreditCardExpense> freeeCreditCardList;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final creditCardStatementInquiryScreenState =
        ref.watch(creditCardStatementInquiryScreenProvider);
    final creditCardIndex =
        useState(creditCardStatementInquiryScreenState.creditCardIndex);
    final selectCreditCardIndex =
        useState(creditCardStatementInquiryScreenState.selectCreditCardIndex);

    return SizedBox(
      height: creditCardSwitchModalList.length == 1 ? 338 : 438,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
          color: AppColors.white,
        ),
        child: SafeArea(
          child: Column(
            children: [
              _CardSwitchModalMessageArea(),
              _CardSwitchModalContentArea(
                creditCardSwitchModalList: creditCardSwitchModalList,
                freeeCreditCardList: freeeCreditCardList,
                creditCardIndex: creditCardIndex,
                selectCreditCardIndex: selectCreditCardIndex,
              ),
              _CardSwitchModalButton(
                creditCardIndex: creditCardIndex,
                selectCreditCardIndex: selectCreditCardIndex,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _CardSwitchModalMessageArea extends HookConsumerWidget {
  const _CardSwitchModalMessageArea();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return SizedBox(
      height: 86,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              top: 40,
              left: 16,
              bottom: 16,
            ),
            child: Text(
              AppLocalizations.of(context)!.cardSwitch,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.w700,
                    height: 1.5,
                    fontSize: 20,
                    color: AppColors.tradGreen,
                  ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 16,
              right: 16,
              bottom: 38,
            ),
            child: SizedBox(
              height: 32,
              width: 32,
              child: IconButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  // FirebaseAnalyticsログ送信
                  analyticsLogController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.batsu,
                    screenName:
                        AppLocalizations.of(context)!.creditCardSwitchModal,
                  );

                  Navigator.of(context).pop();
                },
                icon: SvgPicture.asset(
                  Assets.buttonClose,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CardSwitchModalContentScrollArea extends StatelessWidget {
  const _CardSwitchModalContentScrollArea({
    required this.creditCardSwitchModalList,
    required this.freeeCreditCardList,
    required this.creditCardIndex,
    required this.selectCreditCardIndex,
  });

  final List<String> creditCardSwitchModalList;
  final List<CreditCardExpense> freeeCreditCardList;
  final ValueNotifier<int> creditCardIndex;
  final ValueNotifier<int> selectCreditCardIndex;

  @override
  Widget build(BuildContext context) {
    final scrollController = ScrollController();

    return Expanded(
      child: RawScrollbar(
        thumbVisibility: true,
        thumbColor: AppColors.basicBackgroundColor,
        thickness: 6,
        radius: const Radius.circular(3.0),
        controller: scrollController,
        child: SingleChildScrollView(
          controller: scrollController,
          physics: ClampingScrollPhysics(),
          child: Column(
            children: [
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: creditCardSwitchModalList.length,
                itemBuilder: (context, index) {
                  return _CustomRadioButton(
                    value: index,
                    selectedValue: selectCreditCardIndex.value,
                    text: creditCardSwitchModalList[index],
                    list: creditCardSwitchModalList,
                    onTap: () {
                      // カード切替モーダルで選択されたクレジットカードの本来のインデックスを特定し取得する
                      freeeCreditCardList.asMap().entries.map((entry) {
                        final int freeeCreditCardListIndex = entry.key;
                        final String freeeCreditCardListCardName =
                            entry.value.creditCardName;
                        if (creditCardSwitchModalList[index] ==
                            freeeCreditCardListCardName) {
                          creditCardIndex.value = freeeCreditCardListIndex;
                        }
                      }).toList();

                      // カード切替モーダルで選択されたインデックスを特定し取得する
                      creditCardSwitchModalList.asMap().entries.map((entry) {
                        final int creditCardSwitchModalListIndex = entry.key;
                        final String creditCardSwitchModalListCardName =
                            entry.value.toString();
                        if (creditCardSwitchModalList[index] ==
                            creditCardSwitchModalListCardName) {
                          selectCreditCardIndex.value =
                              creditCardSwitchModalListIndex;
                        }
                      }).toList();
                    },
                  );
                },
              ),
              SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}

class _CardSwitchModalContentArea extends StatelessWidget {
  const _CardSwitchModalContentArea({
    required this.creditCardSwitchModalList,
    required this.freeeCreditCardList,
    required this.creditCardIndex,
    required this.selectCreditCardIndex,
  });

  final List<String> creditCardSwitchModalList;
  final List<CreditCardExpense> freeeCreditCardList;
  final ValueNotifier<int> creditCardIndex;
  final ValueNotifier<int> selectCreditCardIndex;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        children: [
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: creditCardSwitchModalList.length,
            itemBuilder: (context, index) {
              return _CustomRadioButton(
                value: index,
                selectedValue: selectCreditCardIndex.value,
                text: creditCardSwitchModalList[index],
                list: creditCardSwitchModalList,
                onTap: () {
                  // カード切替モーダルで選択されたクレジットカードの本来のインデックスを特定し取得する
                  freeeCreditCardList.asMap().entries.map((entry) {
                    final int freeeCreditCardListIndex = entry.key;
                    final String freeeCreditCardListCardName =
                        entry.value.creditCardName;
                    if (creditCardSwitchModalList[index] ==
                        freeeCreditCardListCardName) {
                      creditCardIndex.value = freeeCreditCardListIndex;
                    }
                  }).toList();

                  // カード切替モーダルで選択されたインデックスを特定し取得する
                  creditCardSwitchModalList.asMap().entries.map((entry) {
                    final int creditCardSwitchModalListIndex = entry.key;
                    final String creditCardSwitchModalListCardName =
                        entry.value.toString();
                    if (creditCardSwitchModalList[index] ==
                        creditCardSwitchModalListCardName) {
                      selectCreditCardIndex.value =
                          creditCardSwitchModalListIndex;
                    }
                  }).toList();
                },
              );
            },
          ),
          SizedBox(height: 40),
        ],
      ),
    );
  }
}

class _CardSwitchModalButton extends HookConsumerWidget {
  const _CardSwitchModalButton({
    required this.creditCardIndex,
    required this.selectCreditCardIndex,
  });

  final ValueNotifier<int> creditCardIndex;
  final ValueNotifier<int> selectCreditCardIndex;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final creditCardStatementInquiryScreenController =
        ref.read(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Container(
      height: 106,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: const BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: AppColors.bottomNavigationBarShadow,
            blurRadius: 4,
            offset: Offset(0, -2),
            spreadRadius: 0,
          ),
        ],
        color: AppColors.white,
      ),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.switchOver,
                screenName: AppLocalizations.of(context)!.creditCardSwitchModal,
              );

              // 選択されたクレジットカードのindexに更新する
              creditCardStatementInquiryScreenController
                  .updateSelectCreditCardIndex(
                creditCardIndex.value,
                selectCreditCardIndex.value,
              );
              // 絞り込みを初期化する
              creditCardStatementInquiryScreenController.cardSwitchReset();
              Navigator.of(context).pop();
            },
            child: Container(
              alignment: Alignment.center,
              width: 240,
              child: AppRectangleButton(
                height: 48,
                label: AppLocalizations.of(context)!.switchOver,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context)!.cardAdditionMessageInHomeScreen,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  fontSize: 12,
                ),
          ),
        ],
      ),
    );
  }
}

class _CustomRadioButton extends StatelessWidget {
  const _CustomRadioButton({
    required this.value,
    required this.selectedValue,
    required this.text,
    this.onTap,
    required this.list,
  });

  final int value;
  final int selectedValue;
  final String text;
  final void Function()? onTap;
  final List<String> list;

  @override
  Widget build(BuildContext context) {
    final isSelected = value == selectedValue;
    final textStyle = Theme.of(context).textTheme.bodyLarge!.copyWith(
          fontSize: 16,
        );

    //　テキスト表示エリア
    final displayAreaWidth = isSelected
        ? MediaQuery.of(context).size.width - 83.5
        : MediaQuery.of(context).size.width - 84.5;

    //　１行に収まる最大文字数を計算
    final maxTextLength = text.getMaxTextLength(
      textStyle: textStyle,
      maxWidth: displayAreaWidth,
    );

    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 72,
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border(
            bottom: list.length != value + 1
                ? const BorderSide(
                    color: AppColors.grey200,
                  )
                : BorderSide.none,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              isSelected
                  ? SvgPicture.asset(
                      Assets.selectedCircleIcon,
                      width: 24,
                      height: 24,
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // SVG表示時とX軸に差がでているため、こちらで調整
                        const SizedBox(
                          width: 0.5,
                        ),
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            shape: BoxShape.circle,
                            border: Border.all(color: AppColors.tradGreen),
                          ),
                        ),
                      ],
                    ),
              isSelected
                  // SVG表示時とX軸に差がでているため、こちらで調整
                  ? const SizedBox(
                      width: 11.5,
                    )
                  : const SizedBox(
                      width: 12,
                    ),
              Expanded(
                child: Text(
                  // 最大文字数を超えた部分は...表示
                  text.ellipsis(maxLength: maxTextLength + 1),
                  style: textStyle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
