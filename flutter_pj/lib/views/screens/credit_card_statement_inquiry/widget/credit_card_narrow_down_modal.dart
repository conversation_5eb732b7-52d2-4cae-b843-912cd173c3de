import 'dart:core';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/credit_card_statement_inquiry/credit_card_statement_inquiry_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/utils/ext/string_validator.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_text_from_field.dart';
import 'package:dtp_app/views/screens/credit_card_statement_inquiry/widget/credit_card_narrow_down_zero_modal.dart';
import 'package:dtp_app/views/screens/identity_verification/password_input/button_style.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

class CreditCardNarrowDownModal extends HookConsumerWidget {
  const CreditCardNarrowDownModal({
    super.key,
    required this.serverDate,
  });

  final String serverDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          controller.checkFirstDayAndLastDayCalendar(getFirstDate(serverDate));

          // Firebase Screenログ・KARTE Viewイベント送信
          analyticsLogController.sendScreenLog(
            screenNumber: ScreenIdNumber.cardNarrowDownModalId,
            screenName: AppLocalizations.of(context)!.creditCardNarrowDownModal,
          );
        });
        return null;
      },
      [],
    );
    return GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
        analyticsLogController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.unfocusKeyboard,
          screenName: AppLocalizations.of(context)!.creditCardNarrowDownModal,
        );
      },
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const _Header(),
            const SizedBox(height: 24),
            _ScrollArea(
              serverDate: serverDate,
            ),
            Container(
              decoration: const BoxDecoration(
                color: AppColors.white,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.creditNarrowDownShadow,
                    offset: Offset(0, -2),
                    blurRadius: 4.0,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                children: [
                  const SizedBox(height: 16),
                  _Footer(
                    serverDate: serverDate,
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _Header extends HookConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              SizedBox(height: 24),
              Text(
                AppLocalizations.of(context)!.narrowDown,
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 20,
                      color: AppColors.tradGreen,
                      height: 1.5,
                    ),
              ),
            ],
          ),
          IconButton(
            iconSize: 32,
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(),
            onPressed: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.batsu,
                screenName:
                    AppLocalizations.of(context)!.creditCardNarrowDownModal,
              );

              Navigator.of(context).pop();
            },
            icon: SvgPicture.asset(
              Assets.buttonClose,
            ),
          ),
        ],
      ),
    );
  }
}

class _Footer extends HookConsumerWidget {
  const _Footer({
    required this.serverDate,
  });

  final String serverDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final controller =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final largeAmountText = controller.largeAmountController.text;
    final smallAmountText = controller.smallAmountController.text;
    DateTime getFirstDate(String serverDate) {
      return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
    }

    double? toNumber(String text) {
      if (text.isNotEmpty && int.tryParse(text) != null) {
        // 数値に変換できる場合の処理
        return double.parse(text);
      } else {
        return null;
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TextButton(
            style: CommonButtonStyles.textButtonStyle,
            onPressed: () {
              // FirebaseAnalyticsログ送信
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.reset,
                screenName:
                    AppLocalizations.of(context)!.creditCardNarrowDownModal,
              );

              controller.narrowDownReset();
              controller
                  .checkFirstDayAndLastDayCalendar(getFirstDate(serverDate));
            },
            child: Text(
              AppLocalizations.of(context)!.reset,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
                fontFamily: FontFamily.notoSansJP,
                color: AppColors.tradGreen,
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(width: 27),
          Flexible(
            child: SizedBox(
              width: 240,
              child: AppRectangleButton(
                height: 48,
                label: AppLocalizations.of(context)!.narrowingDown,
                textColor: (controller.checkInvalid())
                    ? AppColors.inactiveText
                    : AppColors.white,
                buttonColor: (controller.checkInvalid())
                    ? AppColors.backgroundGreen
                    : AppColors.tradGreen,
                onPressed: (controller.checkInvalid())
                    ? null
                    : () async {
                        // FirebaseAnalyticsログ送信
                        analyticsLogController.sendButtonLog(
                          buttonName:
                              AppLocalizations.of(context)!.narrowingDown,
                          screenName: AppLocalizations.of(context)!
                              .creditCardNarrowDownModal,
                        );
                        await controller.narrowDownCreditCardHistory(
                          dateFrom: state.dateFrom!,
                          dateTo: state.dateTo!,
                          forceRefresh: false,
                          filterType: state.currentFilterType,
                          order: state.currentSortOrder,
                          sortType: state.currentSortType,
                          tradingDateFrom: state.fromSelectedDay!,
                          tradingDateTo: state.toSelectedDay!,
                          minAmountWithSign: toNumber(
                            smallAmountText.replaceAll(',', ''),
                          ),
                          maxAmountWithSign: toNumber(
                            largeAmountText.replaceAll(',', ''),
                          ),
                          descriptionSearch: state.descriptionSearch,
                        );
                        ref
                            .watch(creditCardStatementInquiryScreenProvider)
                            .freeeTransactionHistory
                            .map(
                          empty: (empty) {
                            Navigator.pop(context);
                            showModalBottomSheet(
                              enableDrag: false,
                              isScrollControlled: true,
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.vertical(
                                  top: Radius.circular(8.0),
                                ),
                              ),
                              context: context,
                              builder: (BuildContext context) {
                                // 絞り込み結果0件モーダル
                                return CreditCardNarrowDownZeroModal(
                                  serverDate: serverDate,
                                );
                              },
                            );
                          },
                          data: (data) {
                            Navigator.pop(context);
                          },
                        );
                      },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ScrollArea extends HookConsumerWidget {
  const _ScrollArea({
    required this.serverDate,
  });

  final String serverDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final scrollController = ScrollController();
    final bottomSpace = MediaQuery.of(context).viewInsets.bottom;
    final formKey = useMemoized(() => GlobalKey<FormState>());
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // スクロールの開始地点をモーダルのトップにする
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        });
        return () => scrollController.dispose();
      },
      [],
    );
    return Flexible(
      child: SizedBox(
        child: RawScrollbar(
          thumbVisibility: true,
          thumbColor: AppColors.basicBackgroundColor,
          thickness: 6,
          radius: const Radius.circular(3.0),
          controller: scrollController,
          child: SingleChildScrollView(
            controller: scrollController,
            physics: const ClampingScrollPhysics(),
            reverse: true,
            child: Padding(
              padding: EdgeInsets.fromLTRB(16, 0, 16, bottomSpace),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _DateSegment(serverDate: serverDate),
                    const _UsageDetails(),
                    const _Amount(),
                    const SizedBox(
                      height: 40,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _DateSegment extends HookConsumerWidget {
  const _DateSegment({
    required this.serverDate,
  });

  final String serverDate;

  String _format(DateTime? dateTime) =>
      dateTime != null ? DateFormat('yyyy/M/d').format(dateTime) : '';

  DateTime _toDateTime(String serverDate) {
    return DateFormat('yyyy-M-d').parse(serverDate.replaceAll('/', '-'));
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final controller =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.period,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 14,
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _PeriodContainer(
                initialDate: _format(state.fromSelectedDay),
                serverDate: serverDate,
                firstDay: state.dateFrom ?? _toDateTime(serverDate),
                lastDay: state.toSelectedDay ?? _toDateTime(serverDate),
                selectedDay: state.fromSelectedDay ?? _toDateTime(serverDate),
                changeSelectedDay: (selectedDay) {
                  controller.changeFromSelectedDay(selectedDay);
                },
                changeDialogFlag: (isFlag) =>
                    controller.changeIsFromDialog(isFlag),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: SvgPicture.asset(Assets.tildeIcon),
            ),
            Expanded(
              child: _PeriodContainer(
                initialDate: _format(state.toSelectedDay),
                serverDate: serverDate,
                firstDay: state.fromSelectedDay ?? _toDateTime(serverDate),
                lastDay: state.dateTo ?? _toDateTime(serverDate),
                selectedDay: state.toSelectedDay ?? _toDateTime(serverDate),
                changeSelectedDay: (selectedDay) {
                  controller.changeToSelectedDay(selectedDay);
                },
                changeDialogFlag: (isFlag) =>
                    controller.changeIsToDialog(isFlag),
              ),
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 24),
          child: Container(
            color: AppColors.grey200,
            height: 1.0,
          ),
        ),
      ],
    );
  }
}

class _UsageDetails extends HookConsumerWidget {
  const _UsageDetails();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final controller =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);

    final usageDetailsController = controller.usageDetailsController;
    final usageDetailsFocus = useFocusNode();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.usageDetails,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 14,
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                width: double.infinity,
                height: 48,
                controller: usageDetailsController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                ],
                hintText: AppLocalizations.of(context)!.example(
                  AppLocalizations.of(context)!.wakakusaShouji,
                ),
                textAlign: TextAlign.left,
                validation: state.usageDetailsError,
                validator: (String? value) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (usageDetailsController.text.isEmpty) {
                      controller.clearUsageDetailsError();
                    }
                    if (value != null && value.isNotEmpty) {
                      // 絵文字を含んでいるか判定
                      controller.validateUsageDetails(
                        value: value.containsEmoji(),
                        isUsageDetails: true,
                      );
                    }
                    controller
                        .changeDescriptionSearch(usageDetailsController.text);
                  });
                  return null;
                },
                isAmount: false,
                focusNode: usageDetailsFocus,
              ),
            ),
          ],
        ),
        state.usageDetailsError
            ? Column(
                children: [
                  const SizedBox(height: 8),
                  _ValidationMessage(
                    text: AppLocalizations.of(context)!.enterNotEmoji,
                  ),
                ],
              )
            : const SizedBox(),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 24),
          child: Container(
            color: AppColors.grey200,
            height: 1.0,
          ),
        ),
      ],
    );
  }
}

class _Amount extends HookConsumerWidget {
  const _Amount();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(creditCardStatementInquiryScreenProvider);
    final controller =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);
    final smallAmountController = controller.smallAmountController;
    final largeAmountController = controller.largeAmountController;
    final smallAmountFocus = useFocusNode();
    final largeAmountFocus = useFocusNode();

    // フォーカスが外れた時に3桁のカンマ区切りで表示
    void formatAmountWithCommas({required TextEditingController controller}) {
      final numericValue = int.tryParse(controller.text.replaceAll(',', ''));
      if (numericValue != null) {
        final formattedValue = NumberFormat('#,###').format(numericValue);
        controller.text = formattedValue;
      }
    }

    smallAmountFocus.addListener(() {
      if (!smallAmountFocus.hasFocus) {
        formatAmountWithCommas(controller: smallAmountController);
      }
    });
    largeAmountFocus.addListener(() {
      if (!largeAmountFocus.hasFocus) {
        formatAmountWithCommas(controller: largeAmountController);
      }
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context)!.amount,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                fontSize: 14,
                color: AppColors.textGrayGreen,
                fontWeight: FontWeight.w700,
                height: 1.5,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                maxLength: 12,
                width: double.infinity,
                height: 48,
                controller: smallAmountController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                  FilteringTextInputFormatter.deny(RegExp(r'\s+')),
                ],
                hintText: AppLocalizations.of(context)!
                    .example(AppLocalizations.of(context)!.zero),
                textAlign: TextAlign.right,
                validation: state.digitSmallAmountError ||
                    state.positiveSmallAmountError ||
                    (state.smallAmountFocus &&
                        state.correlationSmallAmountError),
                validator: (String? value) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    // バリデーションをクリアする
                    controller.clearSmallAmountError();
                    // 文字入力されているか判定
                    if (value != null && value.isNotEmpty) {
                      // 与えられた文字列に半角マイナスは先頭のみ許容し、それ以外の記号はそもそも不可
                      // 以降の文字に数字以外(カンマ除く)を含んでいるか判定
                      if (value.checkContainsDigitHalfMinus()) {
                        // バリデーションエラー：数字を入力してください
                        controller.validateDigitAmount(
                          value: true,
                          isSmallAmount: true,
                        );
                      }
                      // 与えられた文字列に半角マイナスを複数含んでいるか判定
                      if (value.substring(1).contains('-')) {
                        // バリデーションエラー：『-（半角マイナス）』は一文字目のみ使用可能です。
                        controller.validatePositiveAmount(
                          value: true,
                          isSmallAmount: true,
                        );
                      }
                      // 金額の相関判定
                      if (controller.largeAmountController.text.isEmpty ||
                          largeAmountFocus.hasFocus) {
                        return;
                      }
                      // 最小金額、最大金額が数値ではない場合、相関判定しない
                      if (controller.hasAmountNumError()) {
                        return;
                      }
                      controller.switchAmountFocus(
                        smallAmountFocus: true,
                        largeAmountFocus: false,
                      );
                      final isCorrect = value.checkAmountMinusCorrelation(
                        smallAmount: controller.smallAmountController.text,
                        largeAmount: controller.largeAmountController.text,
                      );
                      controller.validateCorrelationAmount(
                        smallAmount: isCorrect,
                        largeAmount: isCorrect,
                      );
                    }
                  });
                  return null;
                },
                isAmount: true,
                focusNode: smallAmountFocus,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '${AppLocalizations.of(context)!.yenJp} 〜',
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.textGrayGreen, height: 1.5),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _CustomTextFormField(
                maxLength: 12,
                width: double.infinity,
                height: 48,
                controller: largeAmountController,
                inputFormatters: [
                  FilteringTextInputFormatter.singleLineFormatter,
                  FilteringTextInputFormatter.deny(RegExp(r'\s+')),
                ],
                hintText: AppLocalizations.of(context)!.example(
                  AppLocalizations.of(context)!.fiveHundredMillion,
                ),
                textAlign: TextAlign.right,
                validation: state.digitLargeAmountError ||
                    state.positiveLargeAmountError ||
                    (state.largeAmountFocus &&
                        state.correlationLargeAmountError),
                validator: (String? value) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    // バリデーションをクリアする
                    controller.clearLargeAmountError();
                    // 文字入力されているか判定
                    if (value != null && value.isNotEmpty) {
                      // 与えられた文字列に半角マイナスは先頭のみ許容し、それ以外の記号はそもそも不可
                      // 以降の文字に数字以外(カンマ除く)を含んでいるか判定
                      if (value.checkContainsDigitHalfMinus()) {
                        // バリデーションエラー：数字を入力してください
                        controller.validateDigitAmount(
                          value: true,
                        );
                      }
                      // 与えられた文字列に半角マイナスを複数含んでいるか判定
                      if (value.substring(1).contains('-')) {
                        // バリデーションエラー：『-（半角マイナス）』は一文字目のみ使用可能です。
                        controller.validatePositiveAmount(
                          value: true,
                        );
                      }
                      // 金額の相関判定
                      if (controller.smallAmountController.text.isEmpty ||
                          smallAmountFocus.hasFocus) {
                        return;
                      }
                      // 最小金額、最大金額が数値ではない場合、相関判定しない
                      if (controller.hasAmountNumError()) {
                        return;
                      }
                      controller.switchAmountFocus(
                        smallAmountFocus: false,
                        largeAmountFocus: true,
                      );
                      final isCorrect = value.checkAmountMinusCorrelation(
                        smallAmount: controller.smallAmountController.text,
                        largeAmount: controller.largeAmountController.text,
                      );
                      controller.validateCorrelationAmount(
                        smallAmount: isCorrect,
                        largeAmount: isCorrect,
                      );
                    }
                  });
                  return null;
                },
                isAmount: true,
                focusNode: largeAmountFocus,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              AppLocalizations.of(context)!.yenJp,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.textGrayGreen, height: 1.5),
            ),
            const SizedBox(width: 17),
          ],
        ),
        state.digitSmallAmountError || state.digitLargeAmountError
            ? Column(
                children: [
                  const SizedBox(height: 8),
                  _ValidationMessage(
                    text: AppLocalizations.of(context)!.enterDigit,
                  ),
                ],
              )
            : const SizedBox(),
        state.positiveSmallAmountError || state.positiveLargeAmountError
            ? Column(
                children: [
                  const SizedBox(height: 8),
                  _ValidationMessage(
                    text: AppLocalizations.of(context)!
                        .enterHalfWidthSpaceWordFirst,
                  ),
                ],
              )
            : const SizedBox(),
        state.correlationSmallAmountError || state.correlationLargeAmountError
            ? Column(
                children: [
                  const SizedBox(height: 8),
                  _ValidationMessage(
                    text: AppLocalizations.of(context)!
                        .setAnAmountGreaterThanTheLowerLimit,
                    hasSizedBox: false,
                  ),
                ],
              )
            : const SizedBox(),
      ],
    );
  }
}

class _ValidationMessage extends StatelessWidget {
  const _ValidationMessage({
    required this.text,
    this.hasSizedBox = true,
  });

  final String text;
  final bool hasSizedBox;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SvgPicture.asset(
              Assets.captionListErrorIcon,
              width: 16,
              height: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.red,
                      height: 1.5,
                    ),
              ),
            ),
          ],
        ),
        hasSizedBox ? const SizedBox(height: 8) : const SizedBox.shrink(),
      ],
    );
  }
}

class _PeriodContainer extends HookConsumerWidget {
  const _PeriodContainer({
    required this.initialDate,
    required this.serverDate,
    required this.firstDay,
    required this.lastDay,
    required this.selectedDay,
    required this.changeSelectedDay,
    required this.changeDialogFlag,
  });

  final String initialDate;
  final String serverDate;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime selectedDay;
  final void Function(DateTime dateTime) changeSelectedDay;
  final void Function(bool isFlag) changeDialogFlag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.selectDate,
          screenName: AppLocalizations.of(context)!.creditCardNarrowDownModal,
        );
        showDialog(
          context: context,
          builder: (context) {
            return _CalendarDialog(
              lastDate: initialDate,
              serverDate: serverDate,
              firstDay: firstDay,
              lastDay: lastDay,
              selectedDay: selectedDay,
              changeSelectedDay: changeSelectedDay,
              changeDialogFlag: changeDialogFlag,
            );
          },
        );
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.basicBackgroundLightGray,
          borderRadius: const BorderRadius.all(
            Radius.circular(4.0),
          ),
          border: Border.all(
            color: AppColors.borderGrayGreen,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                initialDate,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                      color: AppColors.textGrayGreen,
                      fontWeight: FontWeight.w400,
                    ),
              ),
              SvgPicture.asset(Assets.calendarGrayIcon),
            ],
          ),
        ),
      ),
    );
  }
}

class _CustomTextFormField extends StatelessWidget {
  const _CustomTextFormField({
    required this.width,
    required this.height,
    required this.controller,
    required this.inputFormatters,
    required this.hintText,
    required this.textAlign,
    required this.validation,
    required this.isAmount,
    this.validator,
    this.focusNode,
    this.maxLength,
  });

  final double width;
  final double height;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;
  final String hintText;
  final TextAlign textAlign;
  final bool validation;
  final String? Function(String?)? validator;
  final bool isAmount;
  final FocusNode? focusNode;
  final int? maxLength;

  @override
  Widget build(BuildContext context) {
    return isAmount
        ? Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: validation
                  ? AppColors.lightRed
                  : AppColors.basicBackgroundLightGray,
              border: Border.all(
                color: validation ? AppColors.red : AppColors.borderGrayGreen,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: NotCopyTextFormField(
                      maxLength: maxLength,
                      controller: controller,
                      focusNode: focusNode,
                      textAlign: textAlign,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            color: validation
                                ? AppColors.red
                                : AppColors.textBlack,
                          ),
                      inputFormatters: inputFormatters,
                      decoration: InputDecoration(
                        hintText: hintText,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        counterText: '',
                      ),
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      validator: validator,
                      onChanged: (value) {
                        if (value.contains('－')) {
                          controller.value = controller.value.copyWith(
                            text: value.replaceAll('－', '-'),
                            // カーソル位置調整
                            selection:
                                TextSelection.collapsed(offset: value.length),
                          );
                        }
                        if (value.contains('ー')) {
                          controller.value = controller.value.copyWith(
                            text: value.replaceAll('ー', '-'),
                            // カーソル位置調整
                            selection:
                                TextSelection.collapsed(offset: value.length),
                          );
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          )
        : Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: validation
                  ? AppColors.lightRed
                  : AppColors.basicBackgroundLightGray,
              border: Border.all(
                color: validation ? AppColors.red : AppColors.borderGrayGreen,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: NotCopyTextFormField(
                      maxLength: maxLength,
                      controller: controller,
                      focusNode: focusNode,
                      textAlign: textAlign,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontFamily: FontFamily.robotoCondensed,
                            color: validation
                                ? AppColors.red
                                : AppColors.textBlack,
                          ),
                      inputFormatters: inputFormatters,
                      decoration: InputDecoration(
                        hintText: hintText,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        counterText: '',
                      ),
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      validator: validator,
                    ),
                  ),
                ),
              ],
            ),
          );
  }
}

class _CalendarDialog extends StatefulHookConsumerWidget {
  const _CalendarDialog({
    required this.lastDate,
    required this.serverDate,
    required this.firstDay,
    required this.lastDay,
    required this.selectedDay,
    required this.changeSelectedDay,
    required this.changeDialogFlag,
  });

  final String lastDate;
  final String serverDate;
  final DateTime firstDay;
  final DateTime lastDay;
  final DateTime selectedDay;
  final void Function(DateTime dateTime) changeSelectedDay;
  final void Function(bool isFlag) changeDialogFlag;

  @override
  _CalendarDialogState createState() => _CalendarDialogState();
}

class _CalendarDialogState extends ConsumerState<_CalendarDialog> {
  @override
  Widget build(BuildContext context) {
    final controller =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);
    final serverDay =
        DateFormat('yyyy-M-d').parse(widget.serverDate.replaceAll('/', '-'));
    const CalendarFormat calendarFormat = CalendarFormat.month;
    final focusedDay = useState(widget.selectedDay);
    final selectedDay = useState(widget.selectedDay);
    return AlertDialog(
      contentPadding: const EdgeInsets.all(0),
      content: SizedBox(
        height: 430,
        width: 304,
        child: TableCalendar(
          locale: 'ja_JP',
          calendarFormat: calendarFormat,
          focusedDay: focusedDay.value,
          firstDay: widget.firstDay,
          lastDay: widget.lastDay,
          daysOfWeekHeight: 30,
          selectedDayPredicate: (day) {
            return isSameDay(selectedDay.value, day);
          },
          onDaySelected: (selected, focused) {
            if (!isSameDay(selectedDay.value, selected)) {
              setState(() {
                widget.changeDialogFlag(true);
                widget.changeSelectedDay(selected);
                controller.checkFirstDayAndLastDayCalendar(serverDay);
                selectedDay.value = selected;
                focusedDay.value = focused;
              });
            }
          },
          headerStyle: const HeaderStyle(
            titleTextStyle: TextStyle(color: AppColors.tradGreen),
            decoration: BoxDecoration(color: AppColors.lightGreen),
            formatButtonVisible: false,
            titleCentered: true,
            headerPadding: EdgeInsets.symmetric(vertical: 18),
            leftChevronVisible: false,
            rightChevronVisible: false,
          ),
          daysOfWeekStyle: const DaysOfWeekStyle(
            weekdayStyle: TextStyle(color: AppColors.textGrayGreen),
            weekendStyle: TextStyle(color: AppColors.textGrayGreen),
          ),
          calendarStyle: CalendarStyle(
            todayTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            defaultTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            holidayTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            weekendTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            outsideTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                ),
            withinRangeTextStyle:
                Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                    ),
            disabledTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.greyText,
                ),
            selectedTextStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  color: AppColors.white,
                ),
            cellMargin: const EdgeInsets.all(4),
            selectedDecoration: const BoxDecoration(
              color: AppColors.tradGreen,
              shape: BoxShape.circle,
            ),
            todayDecoration: BoxDecoration(
              border: Border.all(color: AppColors.tradGreen),
              color: AppColors.white,
              shape: BoxShape.rectangle,
            ),
            tablePadding: const EdgeInsets.symmetric(horizontal: 16),
          ),
        ),
      ),
    );
  }
}
