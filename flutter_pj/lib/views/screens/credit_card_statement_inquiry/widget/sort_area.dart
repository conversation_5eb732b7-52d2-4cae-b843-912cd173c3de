import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/credit_card_statement_inquiry/credit_card_statement_inquiry_screen_notifier.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SortArea extends HookConsumerWidget {
  const SortArea({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);
    final sortTitle = ref.watch(
      creditCardStatementInquiryScreenProvider
          .select((value) => value.sortTitle),
    );

    return GestureDetector(
      onTap: () async {
        logController.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.selectSortType,
          screenName: AppLocalizations.of(context)!.cardDetail,
        );
        await showDialog(
          context: context,
          builder: (_) {
            return AlertDialog(
              insetPadding: const EdgeInsets.symmetric(horizontal: 16),
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              content: SizedBox(
                width: 343,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _SortItem(
                      title: AppLocalizations.of(context)!.orderByNewDateOfUse,
                      sortType: TransactionHistorySortType.date,
                      sortOrder: TransactionHistorySortOrder.descending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByNewDateOfUse,
                    ),
                    _SortItem(
                      title: AppLocalizations.of(context)!.orderByOldDateOfUse,
                      sortType: TransactionHistorySortType.date,
                      sortOrder: TransactionHistorySortOrder.ascending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByOldDateOfUse,
                    ),
                    _SortItem(
                      title:
                          AppLocalizations.of(context)!.orderByDescendingAmount,
                      sortType: TransactionHistorySortType.amount,
                      sortOrder: TransactionHistorySortOrder.descending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByDescendingAmount,
                    ),
                    _SortItem(
                      title:
                          AppLocalizations.of(context)!.orderByAscendingAmount,
                      sortType: TransactionHistorySortType.amount,
                      sortOrder: TransactionHistorySortOrder.ascending,
                      buttonName: AppLocalizations.of(context)!
                          .sortCriteriaButtonOrderByAscendingAmount,
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
      child: Row(
        children: [
          SvgPicture.asset(Assets.pulldownArrowIcon),
          const SizedBox(width: 4),
          Text(
            sortTitle.isEmpty
                ? AppLocalizations.of(context)!.orderByNewDateOfUse
                : sortTitle,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w700,
                  color: AppColors.tradGreen,
                ),
          ),
        ],
      ),
    );
  }
}

class _SortItem extends HookConsumerWidget {
  const _SortItem({
    required this.title,
    required this.sortType,
    required this.sortOrder,
    required this.buttonName,
  });

  final String title;
  final TransactionHistorySortType sortType;
  final TransactionHistorySortOrder sortOrder;
  final String buttonName;

  double? toNumber(String text) {
    if (text.isNotEmpty && int.tryParse(text) != null) {
      // 数値に変換できる場合の処理
      return double.parse(text);
    } else {
      return null;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final creditCardStatementInquiryScreenController =
        ref.watch(creditCardStatementInquiryScreenProvider.notifier);
    final creditCardState = ref.watch(creditCardStatementInquiryScreenProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final isSelected = creditCardState.currentSortType == sortType &&
        creditCardState.currentSortOrder == sortOrder;
    final largeAmountText =
        creditCardStatementInquiryScreenController.largeAmountController.text;
    final smallAmountText =
        creditCardStatementInquiryScreenController.smallAmountController.text;
    return GestureDetector(
      onTap: () {
        // FirebaseAnalyticsログ送信
        analyticsLogController.sendButtonLog(
          buttonName: buttonName,
          screenName: AppLocalizations.of(context)!.creditCardStatementInquiry,
        );

        creditCardStatementInquiryScreenController
            .getCreditCardTransactionHistory(
          dateFrom: creditCardState.dateFrom!,
          dateTo: creditCardState.dateTo!,
          yearMonth: creditCardState.selectedYearMonth!,
          selectedYearMonthIndex: creditCardState.selectedYearMonthIndex,
          sortTitle: title,
          sortType: sortType,
          filterType: creditCardState.currentFilterType,
          order: sortOrder,
          tradingDateFrom: creditCardState.fromSelectedDay,
          tradingDateTo: creditCardState.toSelectedDay,
          minAmountWithSign: toNumber(
            smallAmountText.replaceAll(',', ''),
          ),
          maxAmountWithSign: toNumber(
            largeAmountText.replaceAll(',', ''),
          ),
        );
        Navigator.pop(context);
      },
      child: Container(
        height: 48,
        width: double.infinity,
        padding: const EdgeInsets.only(left: 16),
        alignment: Alignment.centerLeft,
        color: isSelected ? AppColors.lightGreen2 : AppColors.white,
        child: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontWeight: isSelected ? FontWeight.w700 : FontWeight.w400,
                color: isSelected ? AppColors.tradGreen : AppColors.textBlack,
              ),
        ),
      ),
    );
  }
}
