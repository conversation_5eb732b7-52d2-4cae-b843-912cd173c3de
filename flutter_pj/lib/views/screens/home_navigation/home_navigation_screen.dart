import 'dart:async';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/business_logics/operation_log/operation_log_controller.dart';
import 'package:dtp_app/business_logics/payment/payment_screen_notifier.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/operation_log_message.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_links/app_links_behavior.dart';
import 'package:dtp_app/views/components/app_links/app_links_provider.dart';
import 'package:dtp_app/views/components/chatbot/chatbot_button.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/my_app_bar.dart';
import 'package:dtp_app/views/screens/home_navigation/home_navigation_settings.dart';
import 'package:dtp_app/views/screens/home_navigation/widget/app_bottom_modal_action_sheet.dart';
import 'package:dtp_app/views/screens/home_navigation/widget/app_bottom_navigation_bar.dart';
import 'package:dtp_app/views/screens/home_navigation/widget/app_bottom_navigation_bar_item.dart';
import 'package:dtp_app/views/screens/login/login_screen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HomeNavigationScreen extends HookConsumerWidget {
  const HomeNavigationScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = HomeNavigationSettings();
    final controller = ref.read(homeNavigationScreenProvider.notifier);
    final state = ref.watch(homeNavigationScreenProvider);
    final loginState = ref.watch(loginScreenProvider);
    final paymentState = ref.watch(paymentScreenProvider);
    // ユニバーサルリンク関連変数
    final appLinks = ref.read(appLinksProvider);
    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          /// ユニバーサルリンクの受付登録
          /// HomeNavigationの描画時に一度だけサブスクリプションを実施
          appLinks.uriLinkStream.listen(
            (uri) {
              if (!context.mounted) return;
              _handleUniversalLink(ref, uri, context);
            },
          );
          await controller.initialize();
        });
        return null;
      },
      const [],
    );

    /// 表示設定の初期化処理
    settings.setParams(
      authorizationStatus: loginState.authorizationStatus,
      loginStatus: state.loginStatus ?? LoginStatus.logout(),
      currentIndex: state.currentIndex,
    );

    final screenIdNumber =
        controller.getScreenIdNumber(paymentState.paymentScreenStatus);

    return Scaffold(
      appBar: MyAppBar(
        screenId: screenIdNumber,
      ),
      body: GestureDetector(
        onTap: () {
          // フッタータブを切り替える度に呼び出されるため
          // FirebaseAnalyticsログは送信しない
          controller.resetModalType();
        },
        child: Stack(
          children: <Widget>[
            settings.navigationScreen,
            // チャットボットボタン - ログイン状態かつホーム画面の場合のみ表示
            ...(state.loginStatus is LoginStatusLogin && state.currentIndex == 0)
                ? [
                    const Positioned(
                      bottom: 20, // 画面下部に表示（ナビゲーションバーの上ではなく）
                      right: 20,
                      child: ChatbotButton(),
                    ),
                  ]
                : [],
          ],
        ),
      ),
      bottomNavigationBar: _BottomNavigationBar(),
      floatingActionButton:
          (state.modalType != ModalType.none) ? _ShowModal() : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButtonAnimator: FloatingActionButtonAnimator.noAnimation,
    );
  }
}

class _BottomNavigationBar extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeNavigationScreenProvider);
    final controller = ref.watch(homeNavigationScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    /// StateNotifierを監視対象とすることで、実行後にDisposeされることを防止
    final paymentController = ref.watch(paymentScreenProvider.notifier);
    final operationLogController = ref.watch(operationLogControllerProvider);
    final homeScreenState = ref.watch(homeScreenProvider);
    final settings = HomeNavigationSettings();
    final appLocalizations = AppLocalizations.of(context)!;

    return AppBottomNavigationBar(
      currentIndex: state.currentIndex,
      items: [
        // ホーム
        AppBottomNavigationBarItem(
          iconAssetName: Assets.homeIcon,
          label: appLocalizations.home,
          onItemTap: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: appLocalizations.home,
              screenName: appLocalizations.footer,
            );

            // 顧客操作ログを送信
            operationLogController.onTapHome();

            // ホーム画面に遷移
            controller.changeCurrentIndex(0);
          },
        ),
        // 照会
        AppBottomNavigationBarItem(
          iconAssetName: Assets.balanceIcon,
          label: appLocalizations.inquiry,
          enabled: settings.isInquiryFooterValid,
          onItemTap: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: appLocalizations.inquiry,
              screenName: appLocalizations.footer,
            );

            // 顧客操作ログを送信
            operationLogController.onTapInquiry();

            // freee未連携またはfreee連携切れの場合、取引口座照会画面に遷移
            if (!homeScreenState.isFreeeLinked ||
                homeScreenState.freeeExpirationFlag) {
              // 次画面に遷移
              controller.onTapAccountInquiry();
              controller.changeCurrentIndex(1);
              return;
            }

            // モーダルを表示
            controller.changeModalType(ModalType.inquiry);
          },
        ),
        // 振込
        AppBottomNavigationBarItem(
          iconAssetName: Assets.paymentIcon,
          label: appLocalizations.payment,
          enabled: settings.isTransferFooterButtonValid &&
              !homeScreenState.isLoading,
          onItemTap: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: appLocalizations.payment,
              screenName: appLocalizations.footer,
            );

            // 顧客操作ログを送信
            operationLogController.onTapPayment();

            // 振込、承認の権限なしの場合、エラーダイアログを表示し処理中断
            if (!settings.isTransferModalButtonValid &&
                !settings.isApprovalModalButtonValid) {
              showAuthErrorDialog(context, ref);
              return;
            }

            // 振込のみ使用可能な場合にはモーダルを表示せずに振込画面に遷移
            if (settings.isTransferModalButtonValid &&
                !settings.isApprovalModalButtonValid) {
              paymentController.getWeb21SsoSaml(true);
              controller.changeCurrentIndex(2);
              return;
            }

            // モーダルを表示
            controller.changeModalType(ModalType.payment);
          },
        ),
        // マイページ
        AppBottomNavigationBarItem(
          iconAssetName: Assets.myPageIcon,
          label: appLocalizations.myPage,
          onItemTap: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: appLocalizations.myPage,
              screenName: appLocalizations.footer,
            );

            // 顧客操作ログを送信
            operationLogController.onTapMyPage();

            // マイページ画面に遷移
            controller.changeCurrentIndex(3);
          },
        ),
      ],
      isLoggedIn: state.loginStatus is LoginStatusLogin,
      loginScreenBuilder: (_) => LoginScreen(),
    );
  }
}

class _ShowModal extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeNavigationScreenProvider);

    Widget getModalWidget(ModalType type) {
      switch (state.modalType) {
        case ModalType.none:
          return SizedBox.shrink();
        case ModalType.inquiry:
          return _ShowModalInquiry();
        case ModalType.payment:
          return _ShowModalPayment();
      }
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        getModalWidget(state.modalType)
            .animate()
            .slideY(begin: 0.1, end: 0, duration: Duration(milliseconds: 10)),
        SizedBox(height: 60),
      ],
    );
  }
}

class _ShowModalInquiry extends HookConsumerWidget {
  const _ShowModalInquiry();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(homeNavigationScreenProvider.notifier);
    final homeScreenState = ref.watch(homeScreenProvider);
    final homeScreenController = ref.watch(homeScreenProvider.notifier);
    final freeeCreditCardExpensesLength =
        homeScreenState.freeeCreditCardExpenses.length;
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return AppBottomModalActionSheet(
      actions: [
        AppBottomModalAction(
          title: AppLocalizations.of(context)!.transactionAccountInquiry,
          onPressed: () async {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName:
                  AppLocalizations.of(context)!.transactionAccountInquiry,
              screenName: AppLocalizations.of(context)!.footer,
            );

            // 非同期処理中にcontextがアンマウントされていないことを確認
            if (!context.mounted) return;

            // 閉塞中以外の場合は次画面に遷移
            controller.onTapAccountInquiry();
            controller.changeCurrentIndex(1);
          },
        ),
        AppBottomModalAction(
          title: AppLocalizations.of(context)!.creditCardStatementInquiry,
          disabled: homeScreenState.isLoading ||
              freeeCreditCardExpensesLength == 0 ||
              homeScreenController.getCreditCardStatementInquiryIndex() == -1,
          onPressed: () async {
            Log.d(homeScreenController.getCreditCardStatementInquiryIndex());
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName:
                  AppLocalizations.of(context)!.creditCardStatementInquiry,
              screenName: AppLocalizations.of(context)!.footer,
            );
            controller.onTapCreditCardStatementInquiry();
            controller.changeCurrentIndex(1);
          },
        ),
      ],
    );
  }
}

class _ShowModalPayment extends HookConsumerWidget {
  const _ShowModalPayment();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(homeNavigationScreenProvider.notifier);
    final paymentController = ref.read(paymentScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final settings = HomeNavigationSettings();

    return AppBottomModalActionSheet(
      actions: [
        AppBottomModalAction(
          title: AppLocalizations.of(context)!.payment,
          onPressed: () async {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.payment,
              screenName: AppLocalizations.of(context)!.footer,
            );
            // 振込のみ利用不可の場合にはエラーダイアログ表示の上処理中断
            if (settings.isApprovalModalButtonValid &&
                !settings.isTransferModalButtonValid) {
              if (!context.mounted) return;
              await showAuthErrorDialog(context, ref);
              controller.changeModalType(ModalType.none);
              return;
            }
            unawaited(paymentController.getWeb21SsoSaml(true));
            controller.changeCurrentIndex(2);
          },
        ),
        AppBottomModalAction(
          title: AppLocalizations.of(context)!.approval,
          onPressed: () async {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.approval,
              screenName: AppLocalizations.of(context)!.footer,
            );
            // KARTE送信
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.approval,
              screenIdNumber: ScreenIdNumber.approvalScreenId,
            );
            unawaited(paymentController.getWeb21SsoSaml(false));
            controller.changeCurrentIndex(2);
          },
        ),
      ],
    );
  }
}

extension _OperationLogExt on OperationLogController {
  /// 顧客操作ログ
  /// フッターメニュー：ホームをタップ
  void onTapHome() {
    sendOperationLog(
      functionLog: OperationLogMessage.home,
      operationLog: OperationLogMessage.inquiry,
      resultLog: OperationLogMessage.normal,
      errorIdLog: null,
    );
  }

  /// 顧客操作ログ
  /// フッターメニュー：取引口座照会をタップ
  void onTapInquiry() {
    sendOperationLog(
      functionLog: OperationLogMessage.accountInquiry,
      operationLog: OperationLogMessage.inquiry,
      resultLog: OperationLogMessage.normal,
      errorIdLog: null,
    );
  }

  /// 顧客操作ログ
  /// フッターメニュー：振込をタップ
  void onTapPayment() {
    sendOperationLog(
      functionLog: OperationLogMessage.payment,
      operationLog: OperationLogMessage.paymentSSO,
      resultLog: OperationLogMessage.normal,
      errorIdLog: null,
    );
  }

  /// 顧客操作ログ
  /// フッターメニュー：マイページをタップ
  void onTapMyPage() {
    sendOperationLog(
      functionLog: OperationLogMessage.myPage,
      operationLog: OperationLogMessage.inquiry,
      resultLog: OperationLogMessage.normal,
      errorIdLog: null,
    );
  }
}

Future<void> showAuthErrorDialog(BuildContext context, WidgetRef ref) async {
  final controller = ref.read(analyticsLogControllerProvider);
  await showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => CommonDialog.okCancel(
      message: HomeNavigationErrorInfo.authErrorMessage,
      code: HomeNavigationErrorInfo.authErrorCode,
      okButtonText: AppLocalizations.of(context)!.toFaq,
      cancelButtonText: AppLocalizations.of(context)!.back,
      onOkPressed: () {
        ref.watch(urlLauncherProvider).launchLinkId(
              LinkIds.faqTop,
              AppLocalizations.of(context)!.toFaq,
              ScreenIdNumber.paymentScreenId,
            );
        controller.sendButtonLog(
          buttonName: AppLocalizations.of(context)!.toFaq,
          screenName: AppLocalizations.of(context)!.payment,
        );
      },
      onCancelPressed: () => controller.sendButtonLog(
        buttonName: AppLocalizations.of(context)!.cancel,
        screenName: AppLocalizations.of(context)!.payment,
      ),
    ),
  );
}

void _handleUniversalLink(WidgetRef ref, Uri uri, BuildContext context) {
  /// stream実行時と画面描画時では状態にずれが発生するため、
  /// streamのcallBack内部にてcontrollerを初期化する
  final otpController = ref.watch(paymentScreenProvider.notifier);
  final firebaseController = ref.read(analyticsLogControllerProvider);
  final homeController = ref.read(homeScreenProvider.notifier);
  final controller = ref.read(homeNavigationScreenProvider.notifier);
  // freee初回連携から戻ってきた場合
  AppLinksBehaviors.freeeInitialLink.onAccept(uri, (param) async {
    await homeController.linkToFreee(param.code, param.state);
  });

  switch (ref.read(homeNavigationScreenProvider).universalLinkStatus) {
    // 特定のWebViewページが起動されている場合のみ後続処理を実施
    case UniversalLinkStatus.acceptable:
      controller.setUniversalLinkAcceptable(UniversalLinkStatus.inProgress);
      // 時刻OTP認証から戻ってきた場合
      AppLinksBehaviors.timeBasedOtpAuth
          .onAcceptFromWeb21App(uri, otpController, (param) async {
        await otpController.onReceiveUniversalLinks(
          userId: param.userId,
          result: param.result,
          inputTimeOtp: param.inputTimeOtp,
        );
      });
      // トランザクション認証から戻ってきた場合
      AppLinksBehaviors.transactionAuth.onAcceptFromWeb21App(uri, otpController,
          (param) async {
        await otpController.onReceiveUniversalLinks(
          userId: param.userId,
          result: param.result,
        );
      });
    // 条件が満たされていない場合にはエラーダイアログを表示
    case UniversalLinkStatus.notAcceptable:
      if (!context.mounted) return;
      // 複数のエラーダイアログが表示されることを防止するためフラグをエラー中に
      controller.setUniversalLinkAcceptable(UniversalLinkStatus.inProgress);
      // OTP待機中じゃないときにOTPのユニバーサルリンクが踏まれたときのエラー
      void showOtpErrorDialog() {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) {
            return CommonDialog.ok(
              title: AppLocalizations.of(context)!.failedToProcess,
              message:
                  '${PaymentErrorInfo.screenChangedErrorCode}\n${PaymentErrorInfo.screenChangedErrorMessage}',
              onOkPressed: () async {
                firebaseController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!
                      .okForMovingErrorWhileTransaction,
                  screenName: AppLocalizations.of(context)!.payment,
                );
                // ユニバーサルリンク受付フラグを初期状態に
                controller.setUniversalLinkAcceptable(
                  UniversalLinkStatus.notAcceptable,
                );
                // HOME画面に遷移
                controller.changeCurrentIndex(0);
              },
            );
          },
        );
      }

      // 時刻OTP認証から戻ってきた場合
      AppLinksBehaviors.timeBasedOtpAuth.onAccept(uri, (param) async {
        // アプリ側が待機中じゃなければエラー
        showOtpErrorDialog();
      });
      // トランザクション認証から戻ってきた場合
      AppLinksBehaviors.transactionAuth.onAccept(uri, (param) async {
        // アプリ側が待機中じゃなければエラー
        showOtpErrorDialog();
      });
    // ユニバーサルリンク受付が処理中の場合は何もしない
    case UniversalLinkStatus.inProgress:
      {}
  }
}
