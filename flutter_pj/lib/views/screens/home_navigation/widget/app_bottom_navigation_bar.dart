import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:dtp_app/views/screens/home_navigation/widget/app_bottom_navigation_bar_item.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AppBottomNavigationBar extends HookConsumerWidget {
  const AppBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.items,
    required this.isLoggedIn,
    required this.loginScreenBuilder,
  });

  static const selectedBackgroundColor = AppColors.lightGreen2;
  static const backgroundColor = AppColors.white;
  final List<AppBottomNavigationBarItem> items;
  final int currentIndex;
  final bool isLoggedIn;
  final WidgetBuilder loginScreenBuilder;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return ColoredBox(
      color: backgroundColor,
      child: SafeArea(
        child: Container(
          decoration: const BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: AppColors.bottomNavigationBarShadow,
                blurRadius: 8,
                offset: Offset(0, -6),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            children: items
                .mapIndexed(
                  (item, index) => Expanded(
                    child: Material(
                      color: index == currentIndex
                          ? selectedBackgroundColor
                          : backgroundColor,
                      child: InkWell(
                        onTap: () {
                          // ログアウト時はログイン画面を表示する
                          if (!isLoggedIn) {
                            logController.sendButtonLog(
                              buttonName:
                                  '${item.label} ${AppLocalizations.of(context)!.beforeLoginFooter} ',
                              screenName: AppLocalizations.of(context)!.footer,
                            );
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: loginScreenBuilder,
                              ),
                            );
                            return;
                          }

                          // API重複実行を避けるため、同じフッターを押下した際は処理中断
                          if (currentIndex == index) {
                            return;
                          }

                          // 各アイテムのタップ処理を実行
                          item.onTap();
                        },
                        child: item.iconImage,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ),
    );
  }
}
