import 'dart:io';

import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class AppBottomModalActionSheet extends StatelessWidget {
  const AppBottomModalActionSheet({
    super.key,
    required this.actions,
  });

  final List<AppBottomModalAction> actions;

  @override
  Widget build(BuildContext context) {
    return Platform.isIOS
        ? _ModalIOS(actions: actions)
        : _ModalAndroid(actions: actions);
  }
}

class AppBottomModalAction {
  const AppBottomModalAction({
    required this.title,
    required this.onPressed,
    this.disabled = false,
  });

  final String title;
  final bool disabled;
  final void Function() onPressed;
}

TextStyle _modalTextStyle(BuildContext context, bool disabled) {
  if (disabled) {
    return Theme.of(context).textTheme.bodyMedium!.copyWith(
          fontWeight: FontWeight.w400,
          color: AppColors.inactiveText,
        );
  } else {
    return TextStyle(fontWeight: FontWeight.w700, color: AppColors.tradGreen);
  }
}

class _ModalIOS extends StatelessWidget {
  const _ModalIOS({
    required this.actions,
  });

  final List<AppBottomModalAction> actions;

  @override
  Widget build(BuildContext context) {
    return CupertinoActionSheet(
      actions: actions
          .map(
            (e) => CupertinoActionSheetAction(
              onPressed: () {
                if (!e.disabled) {
                  e.onPressed();
                }
              },
              child: Text(
                e.title,
                style: _modalTextStyle(
                  context,
                  e.disabled,
                ),
              ),
            ),
          )
          .toList(),
    );
  }
}

class _ModalAndroid extends StatelessWidget {
  const _ModalAndroid({
    required this.actions,
  });

  final List<AppBottomModalAction> actions;

  @override
  Widget build(BuildContext context) {
    final items = <Widget>[];

    for (var i = 0; i < actions.length; i++) {
      final action = actions[i];
      items.add(
        ListTile(
          title: Text(
            action.title,
            style: _modalTextStyle(context, action.disabled),
          ),
          textColor: AppColors.tradGreen,
          onTap: () {
            if (!action.disabled) {
              action.onPressed();
            }
          },
        ),
      );

      if (i < actions.length - 1) {
        items.add(
          Divider(color: Colors.black26),
        );
      }
    }

    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: items,
      ),
    );
  }
}
