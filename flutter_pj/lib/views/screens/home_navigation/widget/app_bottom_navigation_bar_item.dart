import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AppBottomNavigationBarItem {
  AppBottomNavigationBarItem({
    required this.iconAssetName,
    required this.label,
    this.enabled = true,
    required void Function() onItemTap,
  }) : _onTap = onItemTap;

  /// アイコン非活性時のカラーフィルター
  static const disableColorFilter = ColorFilter.mode(
    AppColors.inactiveText,
    BlendMode.srcIn,
  );

  final String iconAssetName;
  final String label;
  final bool enabled;
  final void Function() _onTap;

  Widget get iconImage => SvgPicture.asset(
        iconAssetName,
        colorFilter: enabled ? null : disableColorFilter,
      );

  void onTap() {
    if (enabled) {
      _onTap.call();
    }
  }
}
