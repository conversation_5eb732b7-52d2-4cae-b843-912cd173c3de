import 'package:dtp_app/models/authorization_status/authorization_status.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/views/screens/before_login/before_login_screen.dart';
import 'package:dtp_app/views/screens/home/<USER>';
import 'package:dtp_app/views/screens/inquiry/inquiry_screen.dart';
import 'package:dtp_app/views/screens/my_page/my_page_screen.dart';
import 'package:dtp_app/views/screens/payment/payment_screen.dart';
import 'package:flutter/cupertino.dart';

/// homeNavigationの表示設定を保持するシングルトン
class HomeNavigationSettings {
  static final HomeNavigationSettings _settings = HomeNavigationSettings._();

  HomeNavigationSettings._();

  factory HomeNavigationSettings() {
    return _settings;
  }

  LoginStatus _loginStatus = LoginStatus.logout();
  int _currentIndex = 0;
  AuthorizationStatus? _authorizationStatus;

  /// プロパティのセッター
  void setParams({
    required LoginStatus loginStatus,
    required int currentIndex,
    AuthorizationStatus? authorizationStatus,
  }) {
    _loginStatus = loginStatus;
    _currentIndex = currentIndex;
    _authorizationStatus = authorizationStatus;
  }

  /// ナビゲーションバーにて表示する画面の制御
  Widget get navigationScreen {
    // 非ログイン時はログイン前ホーム画面を表示
    if (_loginStatus == LoginStatus.logout()) return BeforeLoginScreen();
    return [
      const HomeScreen(),
      const InquiryScreen(),
      const PaymentScreen(),
      const MyPageScreen(),
    ].elementAt(_currentIndex);
  }

  /// 照会フッターの活性フラグ
  bool get isInquiryFooterValid {
    final isLoggedIn = _loginStatus is LoginStatusLogin;
    // 未ログイン時には制御を実施しない
    if (!isLoggedIn) return true;
    final hasNoAuth = _authorizationStatus?.hasNoAuth ?? true;
    final hasInquiryAuth = _authorizationStatus?.hasInquiryAuth ?? false;
    return !hasNoAuth && hasInquiryAuth;
  }

  /// 振込フッターの活性フラグ
  /// Web21権限を全て保有しない場合にはボタンを非活性
  bool get isTransferFooterButtonValid {
    final isLoggedIn = _loginStatus is LoginStatusLogin;
    // 未ログイン時には制御を実施しない
    if (!isLoggedIn) return true;
    final hasNoAuth = _authorizationStatus?.hasNoAuth ?? true;
    return !hasNoAuth;
  }

  /// 振込ボタン（モーダル表示）活性フラグ
  bool get isTransferModalButtonValid {
    final isLoggedIn = _loginStatus is LoginStatusLogin;
    // 未ログイン時には制御を実施しない
    if (!isLoggedIn) return true;
    final hasTransferAuth = _authorizationStatus?.hasTransferAuth ?? false;
    return hasTransferAuth;
  }

  /// 承認ボタン（モーダル表示）活性フラグ
  bool get isApprovalModalButtonValid {
    final isLoggedIn = _loginStatus is LoginStatusLogin;
    // 未ログイン時には制御を実施しない
    if (!isLoggedIn) return true;
    final hasApprovalAuth = _authorizationStatus?.hasApprovalAuth ?? false;
    return hasApprovalAuth;
  }
}
