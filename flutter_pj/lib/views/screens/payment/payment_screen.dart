import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/payment/payment_screen_notifier.dart';
import 'package:dtp_app/utils/log.dart';

import 'package:dtp_app/utils/screen_id_number.dart';

import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

/// WebView（Web21振込先情報入力画面・承認画面）
class PaymentScreen extends HookConsumerWidget with CommonErrorHandlerMixin {
  const PaymentScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(paymentScreenProvider);
    final paymentController = ref.watch(paymentScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final homeNavigationController =
        ref.watch(homeNavigationScreenProvider.notifier);
    final appLocalizations = AppLocalizations.of(context)!;

    handleError(
      context,
      ref,
      state.error,
      screenId: ScreenIdNumber.paymentScreenId,
      screenName: AppLocalizations.of(context)!.payment,
      onOkTap: () {
        if (ref.read(paymentScreenProvider).isMovingHomeNeeded) {
          paymentController.clearMovingHomeFlag();
          homeNavigationController.changeCurrentIndex(0);
        }
      },
    ).then((hasError) {
      paymentController.clearAppError();
    });

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.paymentScreenId,
            screenName: appLocalizations.payment,
          );
        });
        return null;
      },
      [],
    );

    switch (state.ssoLoginPhases) {
      case SSOLoginPhases.initial:
        // 初期状態の画面を表示
        // Todo: ローディングのデザイン確定次第、正式版に置き換え (DTPO-5809にて対応予定)
        return const Center(child: CircularProgressIndicator());
      case SSOLoginPhases.web21:
        // Web21アプリをWebView表示
        return const _TransferWebView();
      default:
        return const _Initial();
    }
  }
}

class _Initial extends StatelessWidget {
  const _Initial();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Text(
          AppLocalizations.of(context)!.paymentMessage,
          style: const TextStyle(fontSize: 20),
        ),
      ),
    );
  }
}

class _TransferWebView extends HookConsumerWidget {
  const _TransferWebView();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.read(paymentScreenProvider);
    final paymentController = ref.watch(paymentScreenProvider.notifier);
    final homeNavigationController =
        ref.watch(homeNavigationScreenProvider.notifier);
    final baseUrl = ref.read(buildConfigProvider).baseUrl;
    final web21UniversalLink = ref.read(buildConfigProvider).web21UniversalLink;
    final otpIssuanceUrl = ref.read(buildConfigProvider).otpIssuanceUrl;
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // Web21振込振替トップ（または承認）画面遷移のため、User-Agentを設定
    final userAgent = Platform.isIOS ? 'iPhone' : 'Android';
    final urlEncodedHeaders = {
      'User-Agent': userAgent,
      'Content-Type': 'application/x-www-form-urlencoded',
    };
    final bodyMap = {'from_vd_param': state.fromVdParameter};
    final encodedBody =
        Uint8List.fromList(utf8.encode(Uri(queryParameters: bodyMap).query));

    /// 初期URLリクエスト
    final initialUrlRequest = URLRequest(
      url: WebUri(
        WebViewRequest.launchUrl(initialUrl: state.redirectUrl)
            .getInitialUrl(baseUrl),
      ),
      method: 'POST',
      headers: urlEncodedHeaders,
      body: encodedBody,
    );

    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        return whitelistURLsString.split(',');
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrlRequest.url.toString()];
      }
    });

    return InAppWebView(
      onWebViewCreated: (controller) => {
        analyticsLogController.sendTrack(
          buttonName: AppLocalizations.of(context)!.payment,
          screenIdNumber: ScreenIdNumber.paymentScreenId,
        ),
      },
      initialUrlRequest: initialUrlRequest,
      initialSettings: InAppWebViewSettings(
        cacheEnabled: false, // cache無効化
        useShouldOverrideUrlLoading: true,
        useShouldInterceptRequest: true,
      ),
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final uri = navigationAction.request.url;
        // uriがnullの場合は何も実施しない
        if (uri == null) return NavigationActionPolicy.CANCEL;
        final url = uri.toString();

        // Web21振込先情報入力画面、「ワンタイムパスワード発行」ボタン押下時（シーケンス図No.14）
        if (url.contains(otpIssuanceUrl)) {
          //　法人用アプリからのディープリンク受付のため、フラグ更新＆WebViewControllerの受け渡し
          paymentController.setWebViewController(controller);
          homeNavigationController
              .setUniversalLinkAcceptable(UniversalLinkStatus.acceptable);
          // URLからの情報抽出
          final queryParameters = uri.queryParameters;
          // OTP種別(otp)をクエリパラメータから取得
          final otpType = queryParameters['otp'] ?? '';
          // 入力方式(notInputOTP)をクエリパラメータから取得
          final inputType = queryParameters['notInputOTP'] ?? '';
          // 取引情報(encrypt)をクエリパラメータから取得
          final encrypt = queryParameters['encrypt'] ?? '';

          Log.i('otpType: $otpType');
          Log.i('inputType: $inputType');

          // 取得した値をStateに格納
          paymentController.setOtp(
            otpType: otpType,
            inputType: inputType,
          );

          // 暗号化VDIDを取得
          final encryptedVdId = await paymentController.getEncryptedVdId();

          // VDID暗号化にてエラー発生時は後続処理を実施せずリターン
          if (encryptedVdId == null ||
              (ref.read(paymentScreenProvider).error) != null) {
            return NavigationActionPolicy.CANCEL;
          }

          // ユニバーサルリンクに設定する際はURLエンコードが必要
          // アプリリンクの場合はURLエンコード不要であるため、frontにてエンコードを実施する
          final urlEncodedEncryptedVdId = Uri.encodeComponent(encryptedVdId);
          // トランザクション情報のURLエンコーディング
          final urlEncodedEncrypt = Uri.encodeComponent(encrypt);

          // 法人用アプリに遷移する(保守性を鑑みイミュータブルに宣言)
          late final String url;
          final notInputOTP = inputType;
          final userid = encryptedVdId;

          // 認証種別によって遷移先のURL（クエリパラメータ）を設定する
          switch (ref.read(paymentScreenProvider).otpType) {
            // 時刻OTP認証の場合は、OTP種別・OTP入力方式・ユーザーID（VDID）を設定、取引情報は設定不要
            case OtpType.time:
              url =
                  '$web21UniversalLink?otp=$otpType&notInputOTP=$inputType&userid=$urlEncodedEncryptedVdId';

            case OtpType.signature:
              // トランザクション認証の場合は、OTP種別・ユーザーID（VDID）・取引情報を設定、OTP入力方式は設定不要
              url =
                  '$web21UniversalLink?otp=$otpType&userid=$urlEncodedEncryptedVdId&encrypt=$urlEncodedEncrypt';

            // 認証種別が設定されていない場合は、クエリに何も設定しない（異常ケース）
            case OtpType.none:
              url = web21UniversalLink;
          }

          // Androidの場合はintentFlag設定のためmethodChanel経由でアプリリンク発火
          if (Platform.isAndroid) {
            await paymentController.launchIntent(
              url: web21UniversalLink,
              otp: otpType,
              userid: userid,
              notInputOTP: notInputOTP,
              encrypt: encrypt,
            );
          }
          // iOSの場合はユニバーサルリンク発火し法人用アプリを起動
          // 動作保証外であるが、iOS,Android以外の場合は本分岐にいれて外部ブラウザに飛ばしている
          else {
            await _launchWeb21App(url);
          }
          // 後続画面に遷移させない、本処理がないとWeb21システムエラー画面に遷移してしまう
          // なお、iOSの場合はstopLoadingで制御可能だが、AndroidはstopLoadingによる制御が間に合わずに次画面遷移してしまうため
          // NavigationActionPolicy.CANCEL返却により制御する
          return NavigationActionPolicy.CANCEL;
        } else {
          // 別の画面に遷移した場合は、ディープリンク受付フラグを「受付不可」に
          homeNavigationController
              .setUniversalLinkAcceptable(UniversalLinkStatus.notAcceptable);
        }

        // ホワイトリストに前方一致する場合はWebViewで処理を継続
        if (whitelistURLs.any((whitelistURL) => url.startsWith(whitelistURL))) {
          return NavigationActionPolicy.ALLOW;
        } else {
          // ホワイトリストに前方一致しない場合は外部ブラウザで開く
          await launchUrl(uri, mode: LaunchMode.externalApplication);
          return NavigationActionPolicy.CANCEL;
        }
      },
      onConsoleMessage: (controller, consoleMessage) {
        Log.i(consoleMessage.message); // JavaScriptのコンソールログを出力
      },
    );
  }

  /// 法人用アプリに遷移する
  Future<void> _launchWeb21App(String url) async {
    final Uri uri = Uri.parse(url);
    await launchUrl(uri, mode: LaunchMode.externalApplication);
  }
}
