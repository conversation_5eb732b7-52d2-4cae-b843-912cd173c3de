import 'dart:io';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/debug/debug_menu_screen_notifier.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/bank_account/bank_account.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/bank_account_base_info/bank_account_base_info.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/dialog_action_button.dart';
import 'package:dtp_app/views/components/web_view/customized/auth_web_view_screen.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/account_opening_submitted/account_opening_submitted_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/entering_address_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/identity_info/info_finished_screen.dart';
import 'package:dtp_app/views/screens/identity_verification/reference_number/qr_reader.dart';
import 'package:dtp_app/views/screens/identity_verification/verified_user/verified_user_screen.dart';
import 'package:dtp_app/views/screens/splash/widget/splash_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class DebugMenuScreen extends HookConsumerWidget {
  const DebugMenuScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 画面ビルド時にスプラッシュ画面を終了させる
    FlutterNativeSplash.remove();

    final controller = ref.watch(debugMenuScreenProvider.notifier);

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          await controller.initDebugMenuScreen();
        });
        return null;
      },
      const [],
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('デバッグメニュー'),
        actions: [
          IconButton(
            onPressed: () {
              showDialog<String>(
                context: context,
                builder: (context) {
                  final controller = TextEditingController();
                  return AlertDialog(
                    title: const Text('プロキシ設定'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('プロキシサーバとなっているPCのIPアドレスを入力してください'),
                        TextField(
                          controller: controller,
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          decoration: const InputDecoration(
                            hintText: 'xxx.xxx.xxx.xxx',
                          ),
                        ),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop(controller.text);
                        },
                        child: const Text('OK'),
                      ),
                    ],
                  );
                },
              ).then((value) {
                Log.i('Proxy IP Address: $value');
              });
            },
            icon: const Icon(Icons.settings),
          ),
        ],
      ),
      body: ListView(
        children: [
          ExpansionTile(
            title: const Text('基本機能'),
            children: [
              ListTile(
                title: const Text('ホーム画面（未ログイン）'),
                onTap: () {
                  // ログイン状態を未ログインにする（スタブ使用時のみ）
                  controller.setLoginStatus(true);
                  Navigator.of(context).pushNamed(RouteNames.homeNavigation);
                },
              ),
              ListTile(
                title: const Text('ホーム画面（ログイン済）'),
                subtitle: const Text('※機能しないのでホーム画面（未ログイン）からログインしてください'),
                onTap: () {
                  // ログイン状態をログイン済みにする（スタブ使用時のみ）
                  controller.setLoginStatus(false);
                  Navigator.of(context).pushNamed(RouteNames.homeNavigation);
                },
              ),
              ListTile(
                title: const Text('利用規定画面'),
                onTap: () {
                  Navigator.of(context).pushNamed(
                    RouteNames.terms,
                    arguments: <String>[],
                  );
                },
              ),
              ListTile(
                title: const Text('認可画面（WebView）'),
                onTap: () {
                  final authUrl = ref.watch(buildConfigProvider).authWebViewUrl;
                  ref.watch(authWebViewLauncherProvider).call(
                        context,
                        WebViewRequest.launchUrl(initialUrl: authUrl),
                        fromEbizParam:
                            '44GU5Yip55So44GC44KK44GM44Go44GG44GU44GW44GE44G+44GZ44CC',
                        screenIdNumber: ScreenIdNumber.undecidedId,
                      );
                },
              ),
              ListTile(
                title: const Text('口座照会画面'),
                // Todo 直接遷移できるよう修正
                subtitle: const Text('※ホーム画面経由で遷移しないと情報は表示されません'),
                onTap: () {
                  Navigator.of(context).pushNamed(RouteNames.accountInquiry);
                },
              ),
              ListTile(
                title: const Text('入出金明細画面'),
                // Todo アプリ内スタブ利用時意外にも遷移できるよう修正
                subtitle: const Text('※アプリ内スタブ利用時以外は遷移不可'),
                onTap: () {
                  Navigator.of(context).pushNamed(
                    RouteNames.incomeAndExpenditure,
                    arguments: {
                      'bankAccount': const BankAccountWithBalanceDetail(
                        balanceDetail: BankAccountBalanceDetail(
                          contactName: '口座名義人1',
                          accountId: '1',
                          bankName: '三井住友銀行',
                          branchName: '支店名',
                          branchCode: '支店コード',
                          accountType: '普通',
                          accountNumber: '*********',
                          baseDate: '2001/1/1',
                          baseTime: '10:00',
                          baseDateTime: '2001-01-01-10:00:00',
                          currentBalance: 100,
                          checksIssuedByOtherBanks: 100,
                          overdraftLimit: 200,
                          withdrawableBalance: 300,
                          displayAccountName: '顧客管理用',
                          displayOrder: 0,
                        ),
                        baseInfo: BankAccountBaseInfo(
                          accountId: '*************',
                          account: '************',
                          branchNameKana: 'ｼﾃﾝﾒｲ',
                          branchNameKanji: '支店名',
                          accountType: '普通',
                          accountNumber: '*********',
                          remitterName: 'ｲﾀｸｼｬﾒｲ1',
                          bankName: '三井住友銀行',
                        ),
                        displayConfig: DisplayConfig(
                          accountId: '1',
                          displayOrder: 0,
                          isHidden: false,
                          accountApiType: AccountApiType.web21,
                          //AccountApiType.freee
                        ),
                      ),
                      'serverDate': '2020/1/1',
                    },
                  );
                },
              ),
              ListTile(
                title: const Text('マイページ画面'),
                // Todo アプリ内スタブ利用時意外にも遷移できるよう修正
                subtitle: const Text('※アプリ内スタブ利用時以外は遷移不可'),
                onTap: () {
                  Navigator.of(context).pushNamed(RouteNames.myPage);
                },
              ),
              ListTile(
                title: const Text('マイページ画面（セッションID追加あり）'),
                // Todo アプリ内スタブ利用時意外にも遷移できるよう修正
                subtitle: const Text('※アプリ内スタブ利用時以外は遷移不可'),
                onTap: () async {
                  await (controller.setSessionId()).then(
                    (value) => {
                      if (context.mounted)
                        Navigator.of(context).pushNamed(RouteNames.myPage),
                    },
                  );
                },
              ),
              ListTile(
                title: const Text('スプラッシュ画面（表示確認用）'),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SplashView(
                        hiddenImage: false,
                      ),
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('ID連携設定画面（DTPID連携あり）'),
                onTap: () {
                  Navigator.of(context).pushNamed(
                    RouteNames.idLinkage,
                    arguments: {
                      'vdId': '*********0',
                      'dtpId': '<EMAIL>',
                    },
                  );
                },
              ),
              ListTile(
                title: const Text('ID連携設定画面（DTPID連携なし）'),
                onTap: () {
                  Navigator.of(context).pushNamed(
                    RouteNames.idLinkage,
                    arguments: {
                      'vdId': '*********0',
                      'dtpId': '',
                    },
                  );
                },
              ),
            ],
          ),
          ExpansionTile(
            title: const Text('本人確認'),
            children: [
              ListTile(
                title: const Text('【繋ぎこみ済】お手続番号入力画面'),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RouteNames.identityReferenceNumber,
                  );
                },
              ),
              ListTile(
                title: const Text('QRコード読み込み画面(ダミー)'),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const QrReader(),
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('【JPKI】証明書選択画面'),
                onTap: () {
                  Navigator.pushNamed(context, RouteNames.identityDocument);
                },
              ),
              ListTile(
                title: const Text(
                  '【JPKI】NFCチェック回避用 署名用電子証明書暗証番号説明選択画面',
                ),
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    RouteNames.passwordInputExplanation,
                  );
                },
              ),
              ListTile(
                title: const Text('本人情報送信完了画面'),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const InfoEnteringFinishedScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('口座開設申し込み済み画面'),
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AccountOpeningSubmittedScreen(),
                  ),
                ),
              ),
              ListTile(
                title: const Text('本人確認実施済み画面'),
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const VerifiedUserScreen(),
                  ),
                ),
              ),
              ListTile(
                title: const Text('住所入力画面'),
                onTap: () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AddressScreen(),
                  ),
                ),
              ),
              ListTile(
                title: const Text('本人情報入力画面(役職名)'),
                onTap: () => Navigator.of(context)
                    .pushNamed(RouteNames.enteringPosition),
              ),
              ListTile(
                title: const Text('本人情報入力画面(生年月日)'),
                onTap: () => Navigator.of(context)
                    .pushNamed(RouteNames.enteringDateOfBirth),
              ),
              ListTile(
                title: const Text('本人情報入力画面(名前)'),
                onTap: () =>
                    Navigator.of(context).pushNamed(RouteNames.enteringName),
              ),
              ListTile(
                title: const Text('本人情報入力画面(郵便番号)'),
                onTap: () => Navigator.of(context)
                    .pushNamed(RouteNames.enteringPostCode),
              ),
            ],
          ),
          ExpansionTile(
            title: const Text('ダイアログ関連'),
            children: [
              ListTile(
                title: const Text('基盤系エラーダイアログ'),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => CommonDialog(
                      title:
                          AppLocalizations.of(context)!.sorryScreenErrorTitle,
                      content: Text(
                        AppLocalizations.of(context)!.sorryScreenErrorMessage,
                      ),
                      buttons: [
                        DialogActionTextButton(
                          // iOSの場合、ボタンのテキストにスペースを追加
                          // これにより、ボタンがオーバーフローして縦並びになる
                          text: Platform.isIOS
                              ? ' ${AppLocalizations.of(context)!.sorryScreenErrorButton1} '
                              : AppLocalizations.of(context)!
                                  .sorryScreenErrorButton1,
                          onPressed: () {
                            // アプリを終了
                            exit(0);
                          },
                        ),
                        DialogActionTextButton(
                          text: AppLocalizations.of(context)!
                              .sorryScreenErrorButton2,
                          shouldPopAfterButtonPress: false,
                          onPressed: () {
                            // 法人アプリのストア画面に遷移
                            // ダミー画面のIDがないのでダミー値設定
                            ref
                                .watch(urlLauncherProvider)
                                .launchCorporationAppStorePage(
                                  AppLocalizations.of(context)!
                                      .sorryScreenErrorButton2,
                                  ScreenIdNumber.undecidedId,
                                );
                          },
                        ),
                      ],
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('共通エラーダイアログ'),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => CommonDialog(
                      title: ErrorInfo.defaultErrorTitle,
                      content: Text(
                        '${ErrorInfo.defaultErrorCode}\n ${ErrorInfo.defaultErrorMessage}',
                      ),
                      buttons: [
                        DialogActionOkButton(
                          shouldPopAfterButtonPress: true,
                        ),
                      ],
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('任意アップデートダイアログ'),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => CommonDialog.okCancel(
                      title:
                          AppLocalizations.of(context)!.forceUpdateDialogTitle,
                      message: AppLocalizations.of(context)!
                          .defaultOptionalUpdateDialogMessage,
                      okButtonText: AppLocalizations.of(context)!.yes,
                      cancelButtonText: AppLocalizations.of(context)!.no,
                      isPopAfterOkButtonPress: true,
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('強制アップデートダイアログ'),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => CommonDialog.ok(
                      title:
                          AppLocalizations.of(context)!.forceUpdateDialogTitle,
                      message: AppLocalizations.of(context)!
                          .defaultForceUpdateDialogMessage,
                      okButtonText: AppLocalizations.of(context)!.showStore,
                      isPopAfterOkButtonPress: true,
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('業務閉塞ダイアログ'),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (_) => CommonDialog.okCancel(
                      title:
                          AppLocalizations.of(context)!.sorryScreenErrorTitle,
                      message:
                          AppLocalizations.of(context)!.sorryScreenErrorMessage,
                      messageTextStyle: Theme.of(context).textTheme.bodySmall,
                      cancelButtonText: Platform.isIOS
                          ? ' ${AppLocalizations.of(context)!.sorryScreenErrorButton1} '
                          : AppLocalizations.of(context)!
                              .sorryScreenErrorButton1,
                      okButtonText:
                          AppLocalizations.of(context)!.sorryScreenErrorButton2,
                      isPopAfterOkButtonPress: false,
                      onOkPressed: () async {
                        // 法人アプリのストア画面に遷移
                        // ダミー画面のIDがないのでダミー値設定
                        await ref
                            .watch(urlLauncherProvider)
                            .launchCorporationAppStorePage(
                              AppLocalizations.of(context)!
                                  .sorryScreenErrorButton2,
                              ScreenIdNumber.undecidedId,
                            );
                      },
                      onCancelPressed: () async {
                        exit(0);
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
