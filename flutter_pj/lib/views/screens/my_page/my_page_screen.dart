import 'dart:async';
import 'dart:io';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/business_logics/my_page/my_page_screen_notifier.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/user_info/user_info.dart';
import 'package:dtp_app/utils/code_map.dart';
import 'package:dtp_app/utils/ext/nullable_ext.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/app_round_rectangle_button.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_mixin.dart';
import 'package:dtp_app/views/components/convert_hyphen_text.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/screen_number.dart';
import 'package:dtp_app/views/components/screen_title_header.dart';
import 'package:dtp_app/views/extensions/string_ext.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:dtp_app/views/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MyPageScreen extends HookConsumerWidget with CommonErrorHandlerMixin {
  const MyPageScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(myPageScreenProvider);
    final userInfo = state.userInfo;
    final homeScreenState = ref.watch(homeScreenProvider);
    final myPageController = ref.read(myPageScreenProvider.notifier);
    final homeNavigationController =
        ref.read(homeNavigationScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final appLocalizations = AppLocalizations.of(context)!;

    useEffect(
      () {
        WidgetsBinding.instance.addPostFrameCallback((_) async {
          // Firebase Screenログ・KARTE Viewイベント送信
          logController.sendScreenLog(
            screenNumber: ScreenIdNumber.myPageScreenId,
            screenName: appLocalizations.myPage,
          );
          await myPageController.initialize();
        });
        return null;
      },
      [],
    );

    /// HACKME ローディング処理はどこかで一括管理する
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 画面遷移を伴う動作のため、描画完了後に実施する
      if (state.isLoading) {
        LoadingDialog.loading(context);
      } else {
        LoadingDialog.loadingEnd(context);
      }
    });

    // エラーを検知した際は、エラーダイアログを表示する
    handleError(
      context,
      ref,
      state.error,
      screenId: ScreenIdNumber.myPageScreenId,
      screenName: AppLocalizations.of(context)!.myPage,
      onOkTap: () async {
        // ユーザー情報取得APIエラー応答時のみログアウト
        // その他エラー時にはマイページからの遷移は発生しない
        if (state.error?.isUserInfoErrorLogout ?? false) {
          await myPageController.logout();
          homeNavigationController.changeCurrentIndex(0);
          homeNavigationController.setLogoutComplete(true);
          await homeNavigationController.fetchLoginStatus();
        }
      },
    ).then((hasCommonError) {
      if (hasCommonError) myPageController.clearError();
    });

    return Container(
      color: theme.colorScheme.surface,
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ScreenTitleHeader(
              title: appLocalizations.myPage,
              assetName: Assets.myPageVariationIcon,
            ),
            ScreenNumber(
              screenNumber: ScreenIdNumber.myPageScreenId,
              color: AppColors.textBlack,
            ),
            SizedBox(height: 16),
            _CustomerInformation(
              userInfo: userInfo,
              error: state.error,
            ),
            Visibility(
              visible: homeScreenState.isFreeeLinked,
              child: _IdManagement(),
            ),
            _FAQs(
              userInfo: userInfo,
              appName: state.appName,
              versionName: state.versionName,
            ),
          ],
        ),
      ),
    );
  }
}

/// お客様情報
class _CustomerInformation extends StatelessWidget {
  const _CustomerInformation({
    required this.userInfo,
    required this.error,
  });

  final UserInfo userInfo;
  final AppError? error;

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _CommonTitle(
            text: appLocalizations.customerInformation,
            assetName: Assets.objectsVariationIcon,
          ),
          SizedBox(height: 16),
          Container(
            decoration: ShapeDecoration(
              color: AppColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              shadows: const [
                BoxShadow(
                  color: AppColors.shadowGray,
                  blurRadius: 4,
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 24,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _UserTypeLabel(
                        labelText: CodeMap.userType(
                          context,
                          userInfo.userType,
                        ),
                      ),
                      _LogoutButton(error: error),
                    ],
                  ),
                  const SizedBox(height: 16),
                  EmptyHyphenText(
                    data: userInfo.userSeiMei,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _IdInformation(userInfo: userInfo),
                  const SizedBox(height: 16),
                  _RepresentativeAccount(userInfo: userInfo),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 利用者区分ラベル
class _UserTypeLabel extends StatelessWidget {
  const _UserTypeLabel({required this.labelText});

  final String labelText;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: ShapeDecoration(
        color: AppColors.backgroundGreen,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 6,
          vertical: 2,
        ),
        child: EmptyHyphenText(
          data: labelText,
          style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                fontSize: 12,
              ),
        ),
      ),
    );
  }
}

/// ログアウトボタン
class _LogoutButton extends HookConsumerWidget {
  const _LogoutButton({required this.error});

  final AppError? error;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeNavigationController =
        ref.read(homeNavigationScreenProvider.notifier);
    final myPageController = ref.read(myPageScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    final appLocalizations = AppLocalizations.of(context)!;

    Future<void> onLogoutButtonPressed() async {
      // FirebaseAnalyticsログ送信
      logController.sendButtonLog(
        buttonName: Platform.isAndroid
            ? appLocalizations.logoutDialog
            : appLocalizations.cancel,
        screenName: appLocalizations.myPage,
      );

      // ログアウト処理の実行（セッションID削除）
      await myPageController.logout();
      // ホーム画面に遷移する
      homeNavigationController.changeCurrentIndex(0);
      // ホーム画面のログアウト完了フラグの更新
      homeNavigationController.setLogoutComplete(true);
      // ホーム画面のログイン状況をログインからログアウトに更新
      await homeNavigationController.fetchLoginStatus();
    }

    Future<void> onCancelPressed() async {
      // FirebaseAnalyticsログ送信
      logController.sendButtonLog(
        buttonName: Platform.isAndroid
            ? appLocalizations.cancel
            : appLocalizations.logoutDialog,
        screenName: appLocalizations.myPage,
      );
    }

    Future<void> showLogoutDialog() async {
      // iosのログアウトダイアログのみ反転を戻す
      // 反転を削除して良いかは横展確認が必要
      final logoutText = Platform.isAndroid
          ? appLocalizations.logout
          : appLocalizations.cancel;
      final cancelText = Platform.isAndroid
          ? appLocalizations.cancel
          : appLocalizations.logout;
      final logoutPressed =
          Platform.isAndroid ? onLogoutButtonPressed : onCancelPressed;
      final cancelPressed =
          Platform.isAndroid ? onCancelPressed : onLogoutButtonPressed;

      await showDialog(
        context: context,
        builder: (context) => CommonDialog.okCancel(
          message: appLocalizations.logoutDialogMessage,
          messageTextStyle: Theme.of(context).textTheme.bodySmall,
          okButtonText: logoutText,
          cancelButtonText: cancelText,
          onOkPressed: logoutPressed,
          onCancelPressed: cancelPressed,
          isTextAlignStart: true,
        ),
      );
    }

    return TextButton(
      onPressed: () async {
        // FirebaseAnalyticsログ送信
        logController.sendButtonLog(
          buttonName: appLocalizations.logout,
          screenName: appLocalizations.myPage,
        );

        await showLogoutDialog();
      },
      style: TextButton.styleFrom(
        minimumSize: Size.zero,
        padding: EdgeInsets.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.logoutIcon,
            width: 24,
            height: 24,
          ),
          const SizedBox(width: 4),
          Text(
            appLocalizations.logout,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontSize: 16,
                  color: AppColors.tradGreen,
                ),
          ),
        ],
      ),
    );
  }
}

/// ID情報
class _IdInformation extends StatelessWidget {
  const _IdInformation({required this.userInfo});

  final UserInfo userInfo;

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.lightGreen1,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  appLocalizations.idInformation,
                  style: Theme.of(context).textTheme.bodyLarge!,
                ),
                _ManagementButton(userInfo: userInfo),
              ],
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Text(
                  appLocalizations.digitalTouchPoint,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.cardGradation,
                        fontFamily: FontFamily.robotoCondensed,
                      ),
                ),
                Visibility(
                  visible: userInfo.dtpId.isEmpty,
                  child: Row(
                    children: [
                      SizedBox(width: 8),
                      Container(
                        width: 48,
                        height: 22,
                        decoration: BoxDecoration(
                          color: AppColors.functionalFreshGreen,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: 2,
                            horizontal: 6,
                          ),
                          child: Text(
                            appLocalizations.unassociated,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w700,
                                    ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Text(
                  appLocalizations.idColon,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.textGrayGreen,
                      ),
                ),
                SizedBox(width: 4),
                Expanded(
                  child: EmptyHyphenText(
                    data: userInfo.dtpId,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontFamily: FontFamily.robotoCondensed,
                        ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              appLocalizations.valueDoor,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppColors.cardGradation,
                    fontFamily: FontFamily.robotoCondensed,
                  ),
            ),
            SizedBox(height: 4),
            Row(
              children: [
                Text(
                  appLocalizations.idColon,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.textGrayGreen,
                      ),
                ),
                SizedBox(width: 4),
                EmptyHyphenText(
                  data: userInfo.vdId,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                      ),
                ),
                Text(
                  '（',
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                      ),
                ),
                EmptyHyphenText(
                  data: CodeMap.ninsyoKbn(
                    context,
                    userInfo.ninsyoKbn,
                  ),
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                      ),
                ),
                Text(
                  '）',
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontFamily: FontFamily.robotoCondensed,
                      ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// 管理ボタン
class _ManagementButton extends HookConsumerWidget {
  const _ManagementButton({required this.userInfo});

  final UserInfo userInfo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logController = ref.read(analyticsLogControllerProvider);

    return SizedBox(
      width: 64,
      height: 36,
      child: AppRectangleButton(
        label: AppLocalizations.of(context)!.management,
        textColor: AppColors.tradGreen,
        buttonColor: AppColors.white,
        borderColor: AppColors.tradGreen,
        onPressed: () {
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.management,
            screenName: AppLocalizations.of(context)!.myPage,
          );

          // ID管理画面に遷移
          Navigator.of(context).pushNamed(
            RouteNames.idLinkage,
            arguments: {
              'vdId': userInfo.vdId,
              'dtpId': userInfo.dtpId,
            },
          );
        },
      ),
    );
  }
}

/// 申込代表口座
class _RepresentativeAccount extends HookConsumerWidget {
  const _RepresentativeAccount({required this.userInfo});

  final UserInfo userInfo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appLocalizations = AppLocalizations.of(context)!;

    final compNameMaxLength = 34;
    // 取引先名名がオーバーフローしているかを判定
    final isOverFlowed = userInfo.compName.length > compNameMaxLength;
    // アコーディオンを開いているかのフラグ
    final isAccordionExpanded = useState(false);
    final logController = ref.read(analyticsLogControllerProvider);

    String getCompName(String compName) {
      if (compName.isEmpty) {
        return appLocalizations.fullWidthHyphen;
      }
      if (isAccordionExpanded.value) {
        return compName;
      }
      if (isOverFlowed) {
        return compName.ellipsis(maxLength: compNameMaxLength);
      }
      return compName;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          appLocalizations.representativeAccount,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: Text(
                getCompName(userInfo.compName),
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: FontFamily.robotoCondensed,
                    ),
              ),
            ),
            Visibility(
              visible: isOverFlowed,
              child: GestureDetector(
                onTap: () {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: appLocalizations.switchAccordion,
                    screenName: appLocalizations.myPage,
                  );
                  isAccordionExpanded.value = !isAccordionExpanded.value;
                },
                child: Container(
                  width: 24,
                  // 小さい端末用にpaddingをつけてタップ領域に少し幅を持たせる
                  padding: const EdgeInsets.fromLTRB(8, 4, 0, 15),
                  color: Colors.transparent,
                  // オーバーフローしている時だけボタンを表示
                  child: SvgPicture.asset(
                    isAccordionExpanded.value
                        ? Assets.arrowUpIcon
                        : Assets.arrowDownIcon,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            EmptyHyphenText(
              data: userInfo.branchCode.isNotEmpty
                  ? userInfo.branchCode.substring(0, 3)
                  : userInfo.branchCode,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              '-',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            EmptyHyphenText(
              data: CodeMap.accountTypeFromVD(
                context,
                userInfo.accountType,
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              '-',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            EmptyHyphenText(
              data: userInfo.accountNumber.isNotEmpty
                  ? userInfo.accountNumber.substring(0, 7)
                  : userInfo.accountNumber,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }
}

/// ID管理
class _IdManagement extends HookConsumerWidget {
  const _IdManagement();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appLocalizations = AppLocalizations.of(context)!;
    final myPageController = ref.read(myPageScreenProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);
    // freeeメンバー追加用URL
    // クエリパラメータには多言語対応の不要である為、ベタ書きにて管理
    final freeeAddMemberUrl =
        '${ref.read(buildConfigProvider).freeeRedirectScreen}?loginType=addMembersLogin';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 48),
          _CommonTitle(
            text: appLocalizations.idManagement,
            assetName: Assets.idManagementIcon,
          ),
          const SizedBox(height: 20),
          TextButton(
            // モバイルの場合はボタンが非活性のため押下不可
            onPressed: null,
            style: TextButton.styleFrom(
              fixedSize: const Size(116, 24),
              minimumSize: Size.zero,
              padding: EdgeInsets.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Row(
              children: [
                Text(
                  appLocalizations.userIdAddition,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        color: AppColors.inactiveText,
                      ),
                ),
                const SizedBox(width: 4),
                SvgPicture.asset(
                  Assets.inactiveArrowRightIcon,
                  width: 16,
                  height: 16,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            appLocalizations.canOnlyBeOperatedOnPc,
            style: Theme.of(context).textTheme.bodySmall!,
          ),
          const SizedBox(height: 16),
          // 他の金融機関の口座・カード連携用ID管理
          _CommonActionTextButton(
            text: appLocalizations.freeeLinkageAccount,
            textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                  color: AppColors.tradGreen,
                ),
            fixedSize: const Size(324, 24),
            onPressed: () async {
              // FirebaseAnalyticsログ送信
              logController.sendButtonLog(
                buttonName: appLocalizations.freeeLinkageAccount,
                screenName: appLocalizations.myPage,
              );
              await myPageController.refreshEncryptedCookie();
              // 暗号化Cookie取得時にエラーが発生した際には処理を中断
              if (ref.read(myPageScreenProvider).error.isNotNull) return;
              await ref.read(urlLauncherProvider).launchExternalUrl(
                    appLocalizations.freeeLinkageAccount,
                    ScreenIdNumber.myPageScreenId,
                    freeeAddMemberUrl,
                    useEncryptedCookie: true,
                  );
            },
          ),
          const SizedBox(height: 16),
          Text(
            appLocalizations.freeeLinkageAccountMessage,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

/// FAQ（区切り線より下）
class _FAQs extends HookConsumerWidget {
  const _FAQs({
    required this.userInfo,
    required this.appName,
    required this.versionName,
  });

  final UserInfo userInfo;
  final String appName;
  final String versionName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appLocalizations = AppLocalizations.of(context)!;
    final logController = ref.read(analyticsLogControllerProvider);

    return Stack(
      children: [
        Image.asset(
          Assets.bgWaveGreyHalf,
          width: double.infinity,
          fit: BoxFit.cover,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _Diver(),
              const SizedBox(height: 16),
              Text(
                appLocalizations.pleaseUseAPcForVariousProcedures,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 16),
              _CommonActionTextButton(
                text: appLocalizations.goToFaqTop,
                textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                    ),
                fixedSize: const Size(324, 24),
                onPressed: () async {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: appLocalizations.goToFaqTop,
                    screenName: appLocalizations.myPage,
                  );
                  await ref.watch(urlLauncherProvider).launchLinkId(
                        LinkIds.faqTop,
                        appLocalizations.goToFaqTop,
                        ScreenIdNumber.myPageScreenId,
                      );
                },
              ),
              const SizedBox(height: 48),
              // 利用規定
              _CommonActionTextButton(
                text: appLocalizations.termsOfUse,
                textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                    ),
                fixedSize: const Size(207, 24),
                onPressed: () async {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: appLocalizations.termsOfUse,
                    screenName: appLocalizations.myPage,
                  );
                  await ref.watch(urlLauncherProvider).launchLinkId(
                        LinkIds.termsOfUse,
                        appLocalizations.termsOfUse,
                        ScreenIdNumber.myPageScreenId,
                      );
                },
              ),
              const SizedBox(height: 16),
              // プライバシーポリシー
              _CommonActionTextButton(
                text: appLocalizations.privacyPolicy,
                textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                    ),
                fixedSize: const Size(180, 24),
                onPressed: () async {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: appLocalizations.privacyPolicy,
                    screenName: appLocalizations.myPage,
                  );
                  await ref.watch(urlLauncherProvider).launchLinkId(
                        LinkIds.privacyPolicy,
                        appLocalizations.privacyPolicy,
                        ScreenIdNumber.myPageScreenId,
                      );
                },
              ),
              const SizedBox(height: 16),
              // ライセンス
              _CommonActionTextButton(
                text: appLocalizations.license,
                textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      color: AppColors.tradGreen,
                    ),
                fixedSize: const Size(100, 24),
                onPressed: () {
                  // FirebaseAnalyticsログ送信
                  logController.sendButtonLog(
                    buttonName: appLocalizations.license,
                    screenName: appLocalizations.myPage,
                  );
                  showLicensePage(
                    context: context,
                    applicationName: appName,
                    applicationVersion: versionName,
                  );
                },
                assetName: Assets.arrowRightIcon,
              ),
              const SizedBox(height: 80),
            ],
          ),
        ),
      ],
    );
  }
}

/// タイトル共通クラス
class _CommonTitle extends StatelessWidget {
  const _CommonTitle({
    required this.text,
    required this.assetName,
  });

  final String text;
  final String assetName;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgPicture.asset(
          assetName,
          width: 24,
          height: 24,
        ),
        const SizedBox(width: 8),
        Text(
          text,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ],
    );
  }
}

/// 外部URL遷移ボタンの共通クラス
class _CommonActionTextButton extends HookConsumerWidget {
  const _CommonActionTextButton({
    required this.text,
    required this.fixedSize,
    this.textStyle,
    this.onPressed,
    this.assetName,
  });

  final String text;
  final Size? fixedSize;
  final TextStyle? textStyle;
  final void Function()? onPressed;
  final String? assetName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        fixedSize: fixedSize,
        minimumSize: Size.zero,
        padding: EdgeInsets.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Row(
        children: [
          Text(
            text,
            style: textStyle ??
                Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppColors.tradGreen,
                    ),
          ),
          const SizedBox(width: 4),
          SvgPicture.asset(
            assetName ?? Assets.actionIcon,
            width: 16,
            height: 16,
          ),
        ],
      ),
    );
  }
}

/// 区切り線
class _Diver extends StatelessWidget {
  const _Diver();

  @override
  Widget build(BuildContext context) {
    return const Divider(
      height: 0,
      thickness: 1,
      color: AppColors.borderGrayGreen,
    );
  }
}
