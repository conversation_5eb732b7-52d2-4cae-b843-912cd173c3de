import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

// データがnullの場合は全角ハイフン表示
class NullableHyphenText extends StatelessWidget {
  const NullableHyphenText({
    super.key,
    this.data,
    this.style,
    this.overflow = TextOverflow.ellipsis,
    this.maxLines,
  });

  final String? data;
  final TextStyle? style;
  final TextOverflow? overflow;
  final int? maxLines;

  @override
  Widget build(BuildContext context) {
    return Text(
      data ?? AppLocalizations.of(context)!.fullWidthHyphen,
      style: style,
      overflow: overflow,
      maxLines: maxLines,
    );
  }
}

// データが空の場合は全角ハイフン表示
class EmptyHyphenText extends StatelessWidget {
  const EmptyHyphenText({
    super.key,
    required this.data,
    this.style,
    this.overflow,
  });

  final String data;
  final TextStyle? style;
  final TextOverflow? overflow;

  @override
  Widget build(BuildContext context) {
    return Text(
      data.isEmpty ? AppLocalizations.of(context)!.fullWidthHyphen : data,
      style: style,
      overflow: overflow,
    );
  }
}
