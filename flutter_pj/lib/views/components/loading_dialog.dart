import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// 本人情報確認関連のローディング(タイトルなし)
class LoadingDialog {
  static bool _isShow = false;

  static void loading(BuildContext context) {
    if (_isShow) return;
    _isShow = true;

    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: AppColors.textBlack.withOpacity(0.3),
      transitionDuration: Duration.zero,
      pageBuilder: (
        context,
        animation,
        secondaryAnimation,
      ) {
        return const Center(
          // 画面外の戻る処理を無効化しつつローディング処理
          child: PopScope(
            canPop: false,
            child: CircularProgressIndicator(
              color: AppColors.freshGreen,
              backgroundColor: AppColors.white,
            ),
          ),
        );
      },
    ).then((_) => _isShow = false);
  }

  /// 本人情報確認関連のローディング(タイトルあり)
  static void loadingWithTitle(BuildContext context) {
    if (_isShow) return;

    _isShow = true;
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: AppColors.textBlack.withOpacity(0.3),
      transitionDuration: Duration.zero,
      pageBuilder: (
        context,
        animation,
        secondaryAnimation,
      ) {
        return Center(
          // 画面外の戻る処理を無効化しつつローディング処理
          child: PopScope(
            canPop: false,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 64,
                  width: 64,
                  child: CircularProgressIndicator(
                    color: AppColors.freshGreen,
                    backgroundColor: AppColors.white,
                  ),
                ),
                const SizedBox(height: 15),
                Text(
                  AppLocalizations.of(context)!.sendingInfo,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w700,
                      ),
                ),
              ],
            ),
          ),
        );
      },
    ).then((_) => _isShow = false);
  }

  /// 本人情報確認関連のローディング削除
  static void loadingEnd(BuildContext context) {
    if (!_isShow) return;
    _isShow = false;
    Navigator.of(context).pop();
  }
}
