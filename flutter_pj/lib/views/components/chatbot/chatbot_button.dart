import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/customized/allganize_chatbot_fullscreen.dart';
import 'package:dtp_app/views/components/web_view/customized/allganize_chatbot_web_view_screen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// AIチャットボットボタン
/// ホーム画面の右下に表示されるボタン
class ChatbotButton extends HookConsumerWidget {
  const ChatbotButton({
    super.key,
    this.backgroundColor = AppColors.primary,
    this.iconColor = AppColors.white,
    this.size = 56.0,
  });

  final Color backgroundColor;
  final Color iconColor;
  final double size;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final appLocalizations = AppLocalizations.of(context)!;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // FirebaseAnalyticsログ送信
          analyticsLogController.sendButtonLog(
            buttonName: appLocalizations.chatbotButton,
            screenName: appLocalizations.home,
          );
          // KARTE送信
          analyticsLogController.sendTrack(
            buttonName: appLocalizations.chatbotButton,
            screenIdNumber: ScreenIdNumber.homeScreenId,
          );

          // ダイアログ表示（コメントアウト）
          // showAllganizeChatbotDialog(context);

          // 全画面表示
          showAllganizeChatbotFullscreen(context);
        },
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: backgroundColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Icon(
              Icons.chat,
              color: iconColor,
              size: size * 0.5,
            ),
          ),
        ),
      ),
    );
  }

  /* 表示方法選択ダイアログ（現在は使用していません）
  void _showDisplayModeSelectionDialog(BuildContext context, AnalyticsLogController analyticsLogController) {
    final appLocalizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(appLocalizations.chatbot),
          content: Text('表示方法を選択してください'),
          actions: [
            // ダイアログ表示
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 選択ダイアログを閉じる

                // ダイアログモードでチャットボットを表示
                analyticsLogController.sendButtonLog(
                  buttonName: 'ダイアログモード',
                  screenName: appLocalizations.chatbot,
                );
                showAllganizeChatbotDialog(context);
              },
              child: const Text('ダイアログ表示'),
            ),
            // 全画面表示
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 選択ダイアログを閉じる

                // 全画面モードでチャットボットを表示
                analyticsLogController.sendButtonLog(
                  buttonName: '全画面モード',
                  screenName: appLocalizations.chatbot,
                );
                showAllganizeChatbotFullscreen(context);
              },
              child: const Text('全画面表示'),
            ),
          ],
        );
      },
    );
  }
  */
}
