import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';

class ContentArea extends StatelessWidget {
  const ContentArea({
    super.key,
    required this.context,
    required this.title,
    required this.widget,
    this.minHeight,
  });

  final BuildContext context;
  final String title;
  final Widget widget;
  final double? minHeight;

  @override
  Widget build(BuildContext context) {
    // タイトル文字を含むエリアのWidget
    return Container(
      constraints: BoxConstraints(minHeight: minHeight ?? 0),
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      margin: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      width: double.infinity,
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(4),
          bottom: Radius.circular(4),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowGray,
            blurRadius: 4,
            offset: Offset(0, 0),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          title.isNotEmpty
              ? Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                          fontSize: 20,
                          color: AppColors.tradGreen,
                          height: 1.5, // 行間30px指定のため、20(fontSize) * 1.5 を指定
                        ),
                  ),
                )
              : const SizedBox(),
          widget,
        ],
      ),
    );
  }
}
