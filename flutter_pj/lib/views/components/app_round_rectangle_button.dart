import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AppRectangleButton extends StatelessWidget {
  const AppRectangleButton({
    super.key,
    required this.label,
    this.onPressed,
    this.textColor = AppColors.white,
    this.buttonColor = AppColors.tradGreen,
    this.borderColor,
    this.isEnabled = true,
    this.svgPicture,
    this.height,
  });

  final String label;
  final void Function()? onPressed;
  final Color textColor;
  final Color buttonColor;
  final Color? borderColor;
  final bool isEnabled;
  final SvgPicture? svgPicture;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height ?? 40,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          foregroundColor: textColor,
          backgroundColor: isEnabled ? buttonColor : AppColors.greyText,
          side: BorderSide(
            color: isEnabled ? borderColor ?? buttonColor : AppColors.greyText,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(4.0)),
          ),
        ),
        onPressed: onPressed,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: textColor,
                fontWeight: FontWeight.w700,
              ),
            ),
            svgPicture == null
                ? const SizedBox.shrink()
                : Padding(
                    padding: const EdgeInsets.only(left: 4),
                    child: svgPicture!,
                  ),
          ],
        ),
      ),
    );
  }
}
