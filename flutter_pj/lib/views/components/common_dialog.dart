import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/dialog_action_button.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// アプリ内で共通して使用するダイアログ
class CommonDialog extends StatelessWidget {
  final String? title;
  final Widget content;
  final TextStyle? messageTextStyle;
  final List<DialogActionButton> buttons;

  const CommonDialog({
    super.key,
    this.title,
    required this.content,
    this.messageTextStyle,
    required this.buttons,
  });

  /// OKボタンのみのダイアログを作成する
  /// [okButtonText]がnullの場合、デフォルトで「OK」を表示する
  /// [isPopAfterOkButtonPress]がtrueの場合、OKボタンをタップした後にダイアログを閉じる
  factory CommonDialog.ok({
    String? title,
    required String message,
    TextStyle? messageTextStyle,
    void Function()? onOkPressed,
    String? okButtonText,
    TextStyle? okButtonTextStyle,
    bool? isPopAfterOkButtonPress,
  }) {
    return CommonDialog(
      title: title,
      content: Text(message),
      buttons: [
        DialogActionOkButton(
          text: okButtonText,
          textStyle: okButtonTextStyle,
          onPressed: onOkPressed,
          shouldPopAfterButtonPress: isPopAfterOkButtonPress,
        ),
      ],
    );
  }

  /// OKボタンとキャンセルボタンのダイアログを作成する
  /// [okButtonText]がnullの場合、デフォルトで「OK」を表示する
  /// [cancelButtonText]がnullの場合、デフォルトで「キャンセル」を表示する
  /// [isPopAfterOkButtonPress]がtrueの場合、OKボタンをタップした後にダイアログを閉じる
  /// [isPopAfterCancelButtonPress]がtrueの場合、キャンセルボタンをタップした後にダイアログを閉じる
  factory CommonDialog.okCancel({
    String? title,
    required String message,
    String? code,
    TextStyle? messageTextStyle,
    void Function()? onOkPressed,
    void Function()? onCancelPressed,
    String? okButtonText,
    TextStyle? okButtonTextStyle,
    String? cancelButtonText,
    TextStyle? cancelButtonTextStyle,
    bool? isPopAfterOkButtonPress,
    bool? isPopAfterCancelButtonPress,
    bool? isTextAlignStart = false,
  }) {
    return CommonDialog(
      title: title,
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: (Platform.isAndroid && isTextAlignStart!)
            ? CrossAxisAlignment.start
            : CrossAxisAlignment.center,
        children: [
          Visibility(
            visible: code != null,
            child: Text(
              code ?? '',
              textAlign: Platform.isIOS ? TextAlign.center : TextAlign.start,
            ),
          ),
          Text(message),
        ],
      ),
      messageTextStyle: messageTextStyle,
      buttons: [
        DialogActionCancelButton(
          text: cancelButtonText,
          textStyle: cancelButtonTextStyle,
          onPressed: onCancelPressed,
          shouldPopAfterButtonPress: isPopAfterCancelButtonPress,
        ),
        DialogActionOkButton(
          text: okButtonText,
          textStyle: okButtonTextStyle,
          onPressed: onOkPressed,
          shouldPopAfterButtonPress: isPopAfterOkButtonPress,
        ),
      ],
    );
  }

  /// FAQ表示ありのダイアログを作成する
  /// [okButtonText]がnullの場合、デフォルトで「OK」を表示する
  /// [isPopAfterOkButtonPress]がtrueの場合、OKボタンをタップした後にダイアログを閉じる
  factory CommonDialog.withContent({
    String? title,
    required Widget content,
    TextStyle? messageTextStyle,
    void Function()? onOkPressed,
    String? okButtonText,
    TextStyle? okButtonTextStyle,
    bool? isPopAfterOkButtonPress,
  }) {
    return CommonDialog(
      title: title,
      content: content,
      buttons: [
        DialogActionOkButton(
          text: okButtonText,
          textStyle: okButtonTextStyle,
          onPressed: onOkPressed,
          shouldPopAfterButtonPress: isPopAfterOkButtonPress,
        ),
      ],
    );
  }

  /// FAQ表示の必要性を判断してエラーダイアログを表示する
  factory CommonDialog.error({
    TextStyle? messageTextStyle,
    void Function()? onOkPressed,
    TextStyle? okButtonTextStyle,
    bool? isPopAfterOkButtonPress,
    required AppError error,
    required String screenId,
    required String screenName,
  }) {
    String formatErrorMessage() {
      final rowErrorMessage = error.message ?? ErrorInfo.defaultErrorMessage;
      // タイトルが"処理に失敗しました"の場合、'処理に失敗しました。'の部分を本文から削除(Ph0.5 UAT指摘反映より)
      // 現在はデフォルトで上記文言が指定されるためエラー文言の整形を実施
      if (!rowErrorMessage.startsWith(
        ErrorInfo.deletionTargetOfDuplicationToTitle,
      )) return rowErrorMessage;
      final formattedErrorMessage = rowErrorMessage.replaceAll(
        ErrorInfo.deletionTargetOfDuplicationToTitle,
        '',
      );
      return formattedErrorMessage;
    }

    final errorMessage = formatErrorMessage();
    final content = error.hasFaq
        ? FaqDialogContent(
            errorCode: error.code ?? ErrorInfo.defaultErrorCode,
            displayText: errorMessage,
            faqText: error.faqText,
            screenId: screenId,
            screenName: screenName,
          )
        : Text('${error.code}\n $errorMessage');

    return CommonDialog(
      title: ErrorInfo.defaultErrorTitle,
      content: content,
      buttons: [
        DialogActionOkButton(
          text: error.okButtonText,
          textStyle: okButtonTextStyle,
          onPressed: onOkPressed,
          shouldPopAfterButtonPress: isPopAfterOkButtonPress,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> actions = buttons
        .map(
          (button) => _buildButton(
            Platform.isIOS ? _buildCupertinoAction : _buildAndroidTextButton,
            context: context,
            text: button.buildText(context),
            textStyle: button.textStyle,
            onPressed: button.onPressed,
            shouldPopAfterButtonPress: button.shouldPopAfterButtonPress ?? true,
          ),
        )
        .toList();

    // Material Design、Human Interface Guidelinesに従いフォントサイズを設定
    final cupertinoMessageTextStyle = messageTextStyle ??
        const TextStyle(
          fontSize: 13,
          color: AppColors.textBlack,
          fontFamily: FontFamily.notoSansJP,
        );
    final androidMessageTextStyle = messageTextStyle ??
        const TextStyle(
          fontSize: 15,
          color: AppColors.textBlack,
          fontFamily: FontFamily.notoSansJP,
        );

    // ダイアログは全てPOPさせない
    return PopScope(
      canPop: false,
      child: Platform.isIOS
          ? CupertinoAlertDialog(
              title: title != null
                  ? Text(
                      title!,
                      style: const TextStyle(fontSize: 16),
                    )
                  : null,
              content: DefaultTextStyle(
                style: cupertinoMessageTextStyle,
                textAlign: TextAlign.center,
                child: content,
              ),
              actions: actions.reversed.toList(),
            )
          : AlertDialog(
              title: title != null
                  ? Text(
                      title!,
                      style: const TextStyle(fontSize: 18),
                    )
                  : null,
              content: DefaultTextStyle(
                style: androidMessageTextStyle,
                child: content,
              ),
              actions: actions,
            ),
    );
  }

  Widget _buildButton(
    Widget Function(
      BuildContext context,
      String text,
      TextStyle? textStyle,
      void Function()? onPressed,
      bool shouldPopAfterButtonPress,
    ) builder, {
    required BuildContext context,
    required String text,
    TextStyle? textStyle,
    void Function()? onPressed,
    bool shouldPopAfterButtonPress = true,
  }) {
    return builder(
      context,
      text,
      textStyle,
      onPressed,
      shouldPopAfterButtonPress,
    );
  }

  /// iOS用のボタンを作成する
  Widget _buildCupertinoAction(
    BuildContext context,
    String text,
    TextStyle? textStyle,
    void Function()? onPressed,
    bool shouldPopAfterButtonPress,
  ) {
    return CupertinoDialogAction(
      onPressed: _buildOnPressed(context, onPressed, shouldPopAfterButtonPress),
      child: _buildButtonText(text, textStyle, AppColors.blue),
    );
  }

  /// Android用のボタンを作成する
  Widget _buildAndroidTextButton(
    BuildContext context,
    String text,
    TextStyle? textStyle,
    void Function()? onPressed,
    bool shouldPopAfterButtonPress,
  ) {
    return TextButton(
      onPressed: _buildOnPressed(context, onPressed, shouldPopAfterButtonPress),
      child: _buildButtonText(text, textStyle, AppColors.textBlack),
    );
  }

  /// ボタンをタップした後の処理を作成する
  /// [shouldPopAfterButtonPress]がtrueの場合、ボタンをタップした後にダイアログを閉じる
  void Function() _buildOnPressed(
    BuildContext context,
    void Function()? onPressed,
    bool shouldPopAfterButtonPress,
  ) {
    return () {
      onPressed?.call();
      if (shouldPopAfterButtonPress) {
        Navigator.pop(context);
      }
    };
  }

  /// ボタンのテキストを作成する
  /// [textStyle]がnullの場合、デフォルトのスタイルを使用する
  Widget _buildButtonText(String text, TextStyle? textStyle, Color color) {
    return Text(
      text,
      style: textStyle ??
          TextStyle(
            color: color,
          ),
    );
  }
}

/// FAQダイアログのcontent
class FaqDialogContent extends HookConsumerWidget {
  const FaqDialogContent({
    super.key,
    required this.displayText,
    required this.errorCode,
    required this.screenId,
    required this.screenName,
    this.faqText,
    this.linkId,
  });

  final String displayText;
  final String errorCode;
  final String screenId;
  final String screenName;
  final String? faqText;
  final LinkIds? linkId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          errorCode,
          textAlign: Platform.isIOS ? TextAlign.center : TextAlign.start,
        ),
        Text(displayText),
        Row(
          mainAxisAlignment: Platform.isIOS
              ? MainAxisAlignment.center
              : MainAxisAlignment.start,
          children: [
            Visibility(visible: faqText == null, child: Text('FAQリンクは')),
            Flexible(
              child: RichText(
                softWrap: true,
                overflow: TextOverflow.visible,
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: faqText ?? AppLocalizations.of(context)!.here,
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          ref
                              .read(analyticsLogControllerProvider)
                              .sendButtonLog(
                                buttonName: AppLocalizations.of(context)!.here,
                                screenName: screenName,
                              );
                          ref.read(urlLauncherProvider).launchLinkId(
                                LinkIds.faqTop,
                                AppLocalizations.of(context)!.here,
                                screenId,
                              );
                        },
                    ),
                    WidgetSpan(
                      child: SvgPicture.asset(Assets.actionIcon),
                    ),
                  ],
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppColors.tradGreen,
                        decoration: TextDecoration.underline,
                        decorationColor: AppColors.tradGreen,
                      ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

extension WidgetList on List<Widget> {
  List<Widget> insertDivider() {
    final list = <Widget>[];
    for (var i = 0; i < length; i++) {
      list.add(this[i]);
      if (i != length - 1) {
        list.add(
          const Divider(
            height: 2,
            color: Colors.black45,
          ),
        );
      }
    }
    return list;
  }
}
