import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

class RelatedInformation extends HookConsumerWidget {
  const RelatedInformation({
    super.key,
    required this.screenId,
    required this.screenName,
    this.underBannerContents,
  });

  /// バナー下のコンテンツ
  final List<Widget>? underBannerContents;
  final String screenId;
  final String screenName;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final urlLauncher = ref.watch(urlLauncherProvider);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final banners = [
      // Business Navi
      _Banner(
        url: urlLauncher.getLinkIdUrl(LinkIds.businessNavi),
        image: Assets.relatedInformation01,
      ),
      // DX-link
      _Banner(
        url: urlLauncher.getLinkIdUrl(LinkIds.dxLink),
        image: Assets.relatedInformation02,
      ),
      // 外国為替情報
      _Banner(
        url: urlLauncher.getLinkIdUrl(LinkIds.fxInformation),
        image: Assets.relatedInformation03,
      ),
      // 経済・業界動向レポート
      _Banner(
        url: urlLauncher.getLinkIdUrl(LinkIds.businessTrendReport),
        image: Assets.relatedInformation04,
      ),
      // 各国資料
      _Banner(
        url: urlLauncher.getLinkIdUrl(LinkIds.nationalMaterials),
        image: Assets.relatedInformation05,
      ),
    ];

    /// 外部ページへ遷移する
    Future<void> launchExternalPage(
      String url,
      String buttonName,
      String screeenId,
    ) async {
      final Uri uri = Uri.parse(url);
      analyticsLogController.sendTrack(
        buttonName: buttonName,
        screenIdNumber: screeenId,
      );
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 48),
        Row(
          children: [
            const SizedBox(width: 16),
            SvgPicture.asset(
              Assets.relatedIcon,
              width: 32,
              height: 32,
            ),
            const SizedBox(width: 4),
            Text(
              AppLocalizations.of(context)!.relatedInformation,
              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.tradGreen,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 25),
        SizedBox(
          height: 154,
          child: ListView.separated(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: banners.length,
            itemBuilder: (context, index) {
              final banner = banners[index];
              return Row(
                children: [
                  GestureDetector(
                    onTap: () async {
                      // FirebaseAnalyticsログ送信
                      analyticsLogController.sendButtonLog(
                        buttonName: AppLocalizations.of(context)!
                            .pushExternalBrowserBanner,
                        screenName: screenName,
                      );
                      await launchExternalPage(
                        banner.url,
                        AppLocalizations.of(context)!.pushExternalBrowserBanner,
                        screenId,
                      );
                    },
                    child: Container(
                      width: 232,
                      height: 154,
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.grey200),
                        borderRadius: const BorderRadius.all(
                          Radius.circular(4),
                        ),
                        boxShadow: [
                          BoxShadow(
                            offset: Offset.zero,
                            blurRadius: 4,
                            spreadRadius: 0,
                            color: AppColors.shadowGray.withOpacity(0.4),
                          ),
                        ],
                        image: DecorationImage(
                          image: AssetImage(banner.image),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
            separatorBuilder: (context, index) {
              return const SizedBox(width: 16);
            },
          ),
        ),
        ...?underBannerContents,
      ],
    );
  }
}

class _Banner {
  _Banner({
    required this.url,
    required this.image,
  });

  final String url;
  final String image;
}
