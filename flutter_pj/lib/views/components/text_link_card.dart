import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:dtp_app/views/styles/assets.dart';

class TextLinkCard extends StatelessWidget {
  const TextLinkCard({
    super.key,
    required this.text,
    required this.onTap,
    this.isVisible = true,
  });

  final String text;
  final void Function() onTap;
  final bool isVisible;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: TextButton(
        onPressed: onTap,
        child: Row(
          children: [
            Text(
              text,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(color: AppColors.tradGreen),
            ),
            const SizedBox(width: 4),
            SvgPicture.asset(
              Assets.cardLinkIcon,
              width: 16,
              height: 16,
            ),
          ],
        ),
      ),
    );
  }
}
