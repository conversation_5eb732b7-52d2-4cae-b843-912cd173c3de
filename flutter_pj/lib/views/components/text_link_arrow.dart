import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:dtp_app/views/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class TextLinkArrow extends StatelessWidget {
  const TextLinkArrow({
    super.key,
    required this.text,
    required this.onTap,
  });

  final String text;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    // テキストリンクとアイコンのWidget
    // 1行で表示できない場合は折り返しを行う
    return GestureDetector(
      onTap: onTap,
      child: Wrap(
        spacing: 10,
        alignment: WrapAlignment.spaceEvenly, // 改行された場合、行の最後にiconを表示
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  style: theme.textTheme.bodyMedium
                      ?.copyWith(color: AppColors.tradGreen),
                  text: text,
                ),
                const WidgetSpan(
                  child: <PERSON><PERSON><PERSON><PERSON>(
                    width: 4,
                  ),
                ),
                WidgetSpan(
                  child: SvgPicture.asset(
                    Assets.arrowRightIcon,
                    width: 16,
                    height: 16,
                    colorFilter: const ColorFilter.mode(
                      AppColors.tradGreen,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
