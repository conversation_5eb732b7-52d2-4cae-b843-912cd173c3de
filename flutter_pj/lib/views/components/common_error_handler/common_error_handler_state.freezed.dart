// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'common_error_handler_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CommonErrorHandlerState {
  bool get isDialogOpened => throw _privateConstructorUsedError;

  /// Create a copy of CommonErrorHandlerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommonErrorHandlerStateCopyWith<CommonErrorHandlerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommonErrorHandlerStateCopyWith<$Res> {
  factory $CommonErrorHandlerStateCopyWith(CommonErrorHandlerState value,
          $Res Function(CommonErrorHandlerState) then) =
      _$CommonErrorHandlerStateCopyWithImpl<$Res, CommonErrorHandlerState>;
  @useResult
  $Res call({bool isDialogOpened});
}

/// @nodoc
class _$CommonErrorHandlerStateCopyWithImpl<$Res,
        $Val extends CommonErrorHandlerState>
    implements $CommonErrorHandlerStateCopyWith<$Res> {
  _$CommonErrorHandlerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommonErrorHandlerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDialogOpened = null,
  }) {
    return _then(_value.copyWith(
      isDialogOpened: null == isDialogOpened
          ? _value.isDialogOpened
          : isDialogOpened // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommonErrorHandlerStateImplCopyWith<$Res>
    implements $CommonErrorHandlerStateCopyWith<$Res> {
  factory _$$CommonErrorHandlerStateImplCopyWith(
          _$CommonErrorHandlerStateImpl value,
          $Res Function(_$CommonErrorHandlerStateImpl) then) =
      __$$CommonErrorHandlerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool isDialogOpened});
}

/// @nodoc
class __$$CommonErrorHandlerStateImplCopyWithImpl<$Res>
    extends _$CommonErrorHandlerStateCopyWithImpl<$Res,
        _$CommonErrorHandlerStateImpl>
    implements _$$CommonErrorHandlerStateImplCopyWith<$Res> {
  __$$CommonErrorHandlerStateImplCopyWithImpl(
      _$CommonErrorHandlerStateImpl _value,
      $Res Function(_$CommonErrorHandlerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommonErrorHandlerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isDialogOpened = null,
  }) {
    return _then(_$CommonErrorHandlerStateImpl(
      isDialogOpened: null == isDialogOpened
          ? _value.isDialogOpened
          : isDialogOpened // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CommonErrorHandlerStateImpl implements _CommonErrorHandlerState {
  const _$CommonErrorHandlerStateImpl({this.isDialogOpened = false});

  @override
  @JsonKey()
  final bool isDialogOpened;

  @override
  String toString() {
    return 'CommonErrorHandlerState(isDialogOpened: $isDialogOpened)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommonErrorHandlerStateImpl &&
            (identical(other.isDialogOpened, isDialogOpened) ||
                other.isDialogOpened == isDialogOpened));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isDialogOpened);

  /// Create a copy of CommonErrorHandlerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommonErrorHandlerStateImplCopyWith<_$CommonErrorHandlerStateImpl>
      get copyWith => __$$CommonErrorHandlerStateImplCopyWithImpl<
          _$CommonErrorHandlerStateImpl>(this, _$identity);
}

abstract class _CommonErrorHandlerState implements CommonErrorHandlerState {
  const factory _CommonErrorHandlerState({final bool isDialogOpened}) =
      _$CommonErrorHandlerStateImpl;

  @override
  bool get isDialogOpened;

  /// Create a copy of CommonErrorHandlerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommonErrorHandlerStateImplCopyWith<_$CommonErrorHandlerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
