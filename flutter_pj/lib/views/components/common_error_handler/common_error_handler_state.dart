import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'common_error_handler_state.freezed.dart';

/// 共通エラーハンドラの状態
@freezed
class CommonErrorHandlerState with _$CommonErrorHandlerState {
  const factory CommonErrorHandlerState({
    @Default(false) bool isDialogOpened,
  }) = _CommonErrorHandlerState;
}

/// 共通エラーハンドラの状態を管理するStateNotifier
class CommonErrorHandlerStateController
    extends StateNotifier<CommonErrorHandlerState> {
  CommonErrorHandlerStateController() : super(const CommonErrorHandlerState());

  /// ダイアログを開けるかどうか判定する。
  /// ダイアログが開かれていない場合、trueを返す
  bool get canOpenDialog {
    return !state.isDialogOpened;
  }

  /// ダイアログが開かれたことを通知する
  void notifyDialogOpened() {
    state = state.copyWith(isDialogOpened: true);
  }

  /// ダイアログが閉じられたことを通知する
  void notifyDialogClosed() {
    state = state.copyWith(isDialogOpened: false);
  }
}

final commonErrorHandlerStateProvider = StateNotifierProvider<
    CommonErrorHandlerStateController, CommonErrorHandlerState>((ref) {
  return CommonErrorHandlerStateController();
});
