import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home_navigation/home_navigation_screen_notifier.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/vdid_login_exception/vdid_login_exception.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/common_error_handler/common_error_handler_state.dart';
import 'package:dtp_app/views/components/dialog_action_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// AppErrorの共通処理をおこなうMixin
mixin CommonErrorHandlerMixin {
  /// エラーを処理する関数
  /// 対応するエラーがある場合、trueを返す
  /// その後、ダイアログの表示など、所定の処理をおこなう
  /// 対応するエラーがない場合、falseを返す
  // Todo: JPKIチーム実装ファイルに組み込まれていないため、DTPO-15175 にて対応
  Future<bool> handleError(
    BuildContext context,
    WidgetRef ref,
    AppError? error, {
    String okButtonName = 'ok',
    void Function()? onOkTap,
    required String screenId,
    required String screenName,
    Widget Function(BuildContext)? screenSpecifiedErrorDialogBuilder,
  }) {
    final reason = error?.reason;
    final controller = ref.read(commonErrorHandlerStateProvider.notifier);
    final logController = ref.read(analyticsLogControllerProvider);

    return Future.microtask(() {
      if (!context.mounted) return false;
      // エラーの種類によって処理を分岐
      if (error?.reason is SorryScreenException) {
        // 基盤系エラー
        return _showErrorDialog(
          context: context,
          controller: controller,
          builder: (context) {
            return CommonDialog(
              title: AppLocalizations.of(context)!.sorryScreenErrorTitle,
              content:
                  Text(AppLocalizations.of(context)!.sorryScreenErrorMessage),
              buttons: [
                DialogActionTextButton(
                  // iOSの場合、ボタンのテキストにスペースを追加
                  // これにより、ボタンがオーバーフローして縦並びになる
                  text: Platform.isIOS
                      ? ' ${AppLocalizations.of(context)!.sorryScreenErrorButton1} '
                      : AppLocalizations.of(context)!.sorryScreenErrorButton1,
                  onPressed: () {
                    controller.notifyDialogClosed();
                    // アプリを終了
                    exit(0);
                  },
                ),
                DialogActionTextButton(
                  text: AppLocalizations.of(context)!.sorryScreenErrorButton2,
                  shouldPopAfterButtonPress: false,
                  onPressed: () {
                    controller.notifyDialogClosed();
                    // 法人アプリのストア画面に遷移
                    ref
                        .watch(urlLauncherProvider)
                        .launchCorporationAppStorePage(
                          AppLocalizations.of(context)!.sorryScreenErrorButton2,
                          screenId,
                        );
                    logController.sendButtonLog(
                      buttonName:
                          AppLocalizations.of(context)!.sorryScreenErrorButton2,
                      screenName: screenName,
                    );
                  },
                ),
              ],
            );
          },
        );
      }

      if (error?.isSessionTimeOutError ?? false) {
        /// セッションタイムアウトエラー(有効期限切れ/TTL切れ/フロント側のセッション非保持)
        error?.clearErrorOnShowDialog?.call();
        return _showErrorDialog(
          context: context,
          controller: controller,
          builder: (context) {
            return CommonDialog.ok(
              title: AppLocalizations.of(context)!.sessionTimeoutErrorTitle,
              message: AppLocalizations.of(context)!.sessionTimeoutErrorMessage,
              isPopAfterOkButtonPress: false,
              onOkPressed: () async {
                // ログアウト処理
                await logout(context, ref);
                // ダイアログを閉じる
                if (!context.mounted) return;
                Navigator.pop(context);
                controller.notifyDialogClosed();
                // 強制ログアウト後の処理
                onOkTap?.call();
              },
            );
          },
        );
      }

      // ダイアログ上に表示するエラーがある場合
      if (error != null && error.isShowOnDialog) {
        // ダイアログの重複表示を避ける為、エラー削除を行う
        error.clearErrorOnShowDialog?.call();

        // VDシステムエラー
        if (reason is VdidLoginApplicationException) {
          return _showErrorDialog(
            context: context,
            controller: controller,
            builder: (context) {
              return CommonDialog.ok(
                title: ErrorInfo.defaultErrorTitle,
                message: '${error.code ?? ''} \n${reason.message}',
                okButtonText: error.okButtonText,
                onOkPressed: () {
                  controller.notifyDialogClosed();
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.ok,
                    screenName: screenName,
                  );
                  onOkTap?.call();
                },
              );
            },
          );
        } else if (reason is VdidLoginSystemException) {
          // システムエラーと処理は同じだが、今後の拡張を見据え別実装
          return _showErrorDialog(
            context: context,
            controller: controller,
            builder: (context) {
              return CommonDialog.ok(
                title: ErrorInfo.defaultErrorTitle,
                message: '${error.code ?? ''} \n${reason.message}',
                okButtonText: error.okButtonText,
                onOkPressed: () {
                  controller.notifyDialogClosed();
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.ok,
                    screenName: screenName,
                  );
                  onOkTap?.call();
                },
              );
            },
          );
        } else {
          /// エラーが渡されており、エラーダイアログ表示対象画面である場合
          return _showErrorDialog(
            context: context,
            controller: controller,
            builder: (context) {
              return screenSpecifiedErrorDialogBuilder?.call(context) ??
                  CommonDialog.error(
                    error: error,
                    onOkPressed: () {
                      logController.sendButtonLog(
                        buttonName: okButtonName,
                        screenName: screenName,
                      );
                      onOkTap?.call();
                      controller.notifyDialogClosed();
                    },
                    screenId: screenId,
                    screenName: screenName,
                  );
            },
          );
        }
      }

      // 処理すべきエラーがない場合、falseを返す
      return false;
    });
  }

  /// ダイアログを表示
  bool _showErrorDialog({
    required BuildContext context,
    required CommonErrorHandlerStateController controller,
    required WidgetBuilder builder,
  }) {
    if (controller.canOpenDialog) {
      controller.notifyDialogOpened();
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: builder,
      );
      return true;
    } else {
      return false;
    }
  }

  /// セッションタイムアウト時のログアウト処理
  Future<void> logout(BuildContext context, WidgetRef ref) async {
    final homeNavigationController =
        ref.read(homeNavigationScreenProvider.notifier);
    await homeNavigationController.logout();
    // ホーム画面に遷移する
    homeNavigationController.changeCurrentIndex(0);
    // ホーム画面のログアウト完了フラグの更新
    homeNavigationController.setLogoutComplete(true);
    // ホーム画面のログイン状況をログインからログアウトに更新
    await homeNavigationController.fetchLoginStatus();
  }
}
