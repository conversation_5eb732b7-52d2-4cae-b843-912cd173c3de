// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_links_parameters.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TimeBasedOtpAuthParam {
  String get userId => throw _privateConstructorUsedError;
  String get result => throw _privateConstructorUsedError;
  String get inputTimeOtp => throw _privateConstructorUsedError;

  /// Create a copy of TimeBasedOtpAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TimeBasedOtpAuthParamCopyWith<TimeBasedOtpAuthParam> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TimeBasedOtpAuthParamCopyWith<$Res> {
  factory $TimeBasedOtpAuthParamCopyWith(TimeBasedOtpAuthParam value,
          $Res Function(TimeBasedOtpAuthParam) then) =
      _$TimeBasedOtpAuthParamCopyWithImpl<$Res, TimeBasedOtpAuthParam>;
  @useResult
  $Res call({String userId, String result, String inputTimeOtp});
}

/// @nodoc
class _$TimeBasedOtpAuthParamCopyWithImpl<$Res,
        $Val extends TimeBasedOtpAuthParam>
    implements $TimeBasedOtpAuthParamCopyWith<$Res> {
  _$TimeBasedOtpAuthParamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TimeBasedOtpAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? result = null,
    Object? inputTimeOtp = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
      inputTimeOtp: null == inputTimeOtp
          ? _value.inputTimeOtp
          : inputTimeOtp // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TimeBasedOtpAuthParamImplCopyWith<$Res>
    implements $TimeBasedOtpAuthParamCopyWith<$Res> {
  factory _$$TimeBasedOtpAuthParamImplCopyWith(
          _$TimeBasedOtpAuthParamImpl value,
          $Res Function(_$TimeBasedOtpAuthParamImpl) then) =
      __$$TimeBasedOtpAuthParamImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, String result, String inputTimeOtp});
}

/// @nodoc
class __$$TimeBasedOtpAuthParamImplCopyWithImpl<$Res>
    extends _$TimeBasedOtpAuthParamCopyWithImpl<$Res,
        _$TimeBasedOtpAuthParamImpl>
    implements _$$TimeBasedOtpAuthParamImplCopyWith<$Res> {
  __$$TimeBasedOtpAuthParamImplCopyWithImpl(_$TimeBasedOtpAuthParamImpl _value,
      $Res Function(_$TimeBasedOtpAuthParamImpl) _then)
      : super(_value, _then);

  /// Create a copy of TimeBasedOtpAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? result = null,
    Object? inputTimeOtp = null,
  }) {
    return _then(_$TimeBasedOtpAuthParamImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
      inputTimeOtp: null == inputTimeOtp
          ? _value.inputTimeOtp
          : inputTimeOtp // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TimeBasedOtpAuthParamImpl implements _TimeBasedOtpAuthParam {
  const _$TimeBasedOtpAuthParamImpl(
      {required this.userId, required this.result, required this.inputTimeOtp});

  @override
  final String userId;
  @override
  final String result;
  @override
  final String inputTimeOtp;

  @override
  String toString() {
    return 'TimeBasedOtpAuthParam(userId: $userId, result: $result, inputTimeOtp: $inputTimeOtp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TimeBasedOtpAuthParamImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.result, result) || other.result == result) &&
            (identical(other.inputTimeOtp, inputTimeOtp) ||
                other.inputTimeOtp == inputTimeOtp));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId, result, inputTimeOtp);

  /// Create a copy of TimeBasedOtpAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TimeBasedOtpAuthParamImplCopyWith<_$TimeBasedOtpAuthParamImpl>
      get copyWith => __$$TimeBasedOtpAuthParamImplCopyWithImpl<
          _$TimeBasedOtpAuthParamImpl>(this, _$identity);
}

abstract class _TimeBasedOtpAuthParam implements TimeBasedOtpAuthParam {
  const factory _TimeBasedOtpAuthParam(
      {required final String userId,
      required final String result,
      required final String inputTimeOtp}) = _$TimeBasedOtpAuthParamImpl;

  @override
  String get userId;
  @override
  String get result;
  @override
  String get inputTimeOtp;

  /// Create a copy of TimeBasedOtpAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TimeBasedOtpAuthParamImplCopyWith<_$TimeBasedOtpAuthParamImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$TransactionAuthParam {
  String get userId => throw _privateConstructorUsedError;
  String get result => throw _privateConstructorUsedError;

  /// Create a copy of TransactionAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionAuthParamCopyWith<TransactionAuthParam> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionAuthParamCopyWith<$Res> {
  factory $TransactionAuthParamCopyWith(TransactionAuthParam value,
          $Res Function(TransactionAuthParam) then) =
      _$TransactionAuthParamCopyWithImpl<$Res, TransactionAuthParam>;
  @useResult
  $Res call({String userId, String result});
}

/// @nodoc
class _$TransactionAuthParamCopyWithImpl<$Res,
        $Val extends TransactionAuthParam>
    implements $TransactionAuthParamCopyWith<$Res> {
  _$TransactionAuthParamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TransactionAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? result = null,
  }) {
    return _then(_value.copyWith(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransactionAuthParamImplCopyWith<$Res>
    implements $TransactionAuthParamCopyWith<$Res> {
  factory _$$TransactionAuthParamImplCopyWith(_$TransactionAuthParamImpl value,
          $Res Function(_$TransactionAuthParamImpl) then) =
      __$$TransactionAuthParamImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String userId, String result});
}

/// @nodoc
class __$$TransactionAuthParamImplCopyWithImpl<$Res>
    extends _$TransactionAuthParamCopyWithImpl<$Res, _$TransactionAuthParamImpl>
    implements _$$TransactionAuthParamImplCopyWith<$Res> {
  __$$TransactionAuthParamImplCopyWithImpl(_$TransactionAuthParamImpl _value,
      $Res Function(_$TransactionAuthParamImpl) _then)
      : super(_value, _then);

  /// Create a copy of TransactionAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? result = null,
  }) {
    return _then(_$TransactionAuthParamImpl(
      userId: null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      result: null == result
          ? _value.result
          : result // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$TransactionAuthParamImpl implements _TransactionAuthParam {
  const _$TransactionAuthParamImpl(
      {required this.userId, required this.result});

  @override
  final String userId;
  @override
  final String result;

  @override
  String toString() {
    return 'TransactionAuthParam(userId: $userId, result: $result)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionAuthParamImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.result, result) || other.result == result));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId, result);

  /// Create a copy of TransactionAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionAuthParamImplCopyWith<_$TransactionAuthParamImpl>
      get copyWith =>
          __$$TransactionAuthParamImplCopyWithImpl<_$TransactionAuthParamImpl>(
              this, _$identity);
}

abstract class _TransactionAuthParam implements TransactionAuthParam {
  const factory _TransactionAuthParam(
      {required final String userId,
      required final String result}) = _$TransactionAuthParamImpl;

  @override
  String get userId;
  @override
  String get result;

  /// Create a copy of TransactionAuthParam
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionAuthParamImplCopyWith<_$TransactionAuthParamImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$FreeeInitialLinkParam {
  String get code => throw _privateConstructorUsedError;
  String get state => throw _privateConstructorUsedError;

  /// Create a copy of FreeeInitialLinkParam
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FreeeInitialLinkParamCopyWith<FreeeInitialLinkParam> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FreeeInitialLinkParamCopyWith<$Res> {
  factory $FreeeInitialLinkParamCopyWith(FreeeInitialLinkParam value,
          $Res Function(FreeeInitialLinkParam) then) =
      _$FreeeInitialLinkParamCopyWithImpl<$Res, FreeeInitialLinkParam>;
  @useResult
  $Res call({String code, String state});
}

/// @nodoc
class _$FreeeInitialLinkParamCopyWithImpl<$Res,
        $Val extends FreeeInitialLinkParam>
    implements $FreeeInitialLinkParamCopyWith<$Res> {
  _$FreeeInitialLinkParamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FreeeInitialLinkParam
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? state = null,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FreeeInitialLinkParamImplCopyWith<$Res>
    implements $FreeeInitialLinkParamCopyWith<$Res> {
  factory _$$FreeeInitialLinkParamImplCopyWith(
          _$FreeeInitialLinkParamImpl value,
          $Res Function(_$FreeeInitialLinkParamImpl) then) =
      __$$FreeeInitialLinkParamImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String code, String state});
}

/// @nodoc
class __$$FreeeInitialLinkParamImplCopyWithImpl<$Res>
    extends _$FreeeInitialLinkParamCopyWithImpl<$Res,
        _$FreeeInitialLinkParamImpl>
    implements _$$FreeeInitialLinkParamImplCopyWith<$Res> {
  __$$FreeeInitialLinkParamImplCopyWithImpl(_$FreeeInitialLinkParamImpl _value,
      $Res Function(_$FreeeInitialLinkParamImpl) _then)
      : super(_value, _then);

  /// Create a copy of FreeeInitialLinkParam
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? state = null,
  }) {
    return _then(_$FreeeInitialLinkParamImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as String,
      state: null == state
          ? _value.state
          : state // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$FreeeInitialLinkParamImpl implements _FreeeInitialLinkParam {
  const _$FreeeInitialLinkParamImpl({required this.code, required this.state});

  @override
  final String code;
  @override
  final String state;

  @override
  String toString() {
    return 'FreeeInitialLinkParam(code: $code, state: $state)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FreeeInitialLinkParamImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.state, state) || other.state == state));
  }

  @override
  int get hashCode => Object.hash(runtimeType, code, state);

  /// Create a copy of FreeeInitialLinkParam
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FreeeInitialLinkParamImplCopyWith<_$FreeeInitialLinkParamImpl>
      get copyWith => __$$FreeeInitialLinkParamImplCopyWithImpl<
          _$FreeeInitialLinkParamImpl>(this, _$identity);
}

abstract class _FreeeInitialLinkParam implements FreeeInitialLinkParam {
  const factory _FreeeInitialLinkParam(
      {required final String code,
      required final String state}) = _$FreeeInitialLinkParamImpl;

  @override
  String get code;
  @override
  String get state;

  /// Create a copy of FreeeInitialLinkParam
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FreeeInitialLinkParamImplCopyWith<_$FreeeInitialLinkParamImpl>
      get copyWith => throw _privateConstructorUsedError;
}
