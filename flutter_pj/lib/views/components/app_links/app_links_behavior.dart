import 'dart:io';

import 'package:dtp_app/business_logics/payment/payment_screen_notifier.dart';
import 'package:dtp_app/views/components/app_links/app_links_parameters.dart';

/// ユニバーサルリンクURLのパスとクエリパラメータを静的に定義するためのクラス
abstract interface class AppLinksBehaviors {
  /// 法人用アプリの時刻OTP認証から戻ってくる際のURL
  static AppLinksBehavior<TimeBasedOtpAuthParam> get timeBasedOtpAuth =>
      AppLinksBehavior(
        pathPattern: '/app/otpfinish',
        paramMapper: (param) {
          return TimeBasedOtpAuthParam(
            userId: param['userid'] ?? '',
            result: param['result'] ?? '',
            inputTimeOtp: param['inputTimeOtp'] ?? '',
          );
        },
      );

  /// 法人用アプリのトランザクション認証から戻ってくる際のURL
  static AppLinksBehavior<TransactionAuthParam> get transactionAuth =>
      AppLinksBehavior(
        pathPattern: '/app/approveTxOtp',
        paramMapper: (param) {
          return TransactionAuthParam(
            userId: param['userid'] ?? '',
            result: param['result'] ?? '',
          );
        },
      );

  /// freee初回連携から戻ってくる際のURL
  static AppLinksBehavior<FreeeInitialLinkParam> get freeeInitialLink =>
      AppLinksBehavior(
        pathPattern: '/mobile/api/freee/auth',
        paramMapper: (param) {
          return FreeeInitialLinkParam(
            code: param['code'] ?? '',
            state: param['state'] ?? '',
          );
        },
      );
}

class AppLinksBehavior<Param> {
  const AppLinksBehavior({
    required this.pathPattern,
    required this.paramMapper,
  });

  // 再描画に起因するユニバーサルリンク重複を防ぐための受付フラグ
  static bool _isAcceptable = true;

  final String pathPattern;
  final Param Function(Map<String, String>) paramMapper;

  bool _match(Uri uri) => uri.path == pathPattern;

  /// Uriの中身をチェックし、設定されたパスに応じてクエリからパラメータを取得し、処理を実行する
  Future<void> onAccept(
    Uri uri,
    Future<void> Function(Param param) callback,
  ) async {
    if (_match(uri) && _isAcceptable) {
      _isAcceptable = false;
      await callback(paramMapper(uri.queryParameters));
      _isAcceptable = true;
    }
  }

  /// Uriの中身をチェックし、設定されたパスに応じて処理を実行する（法人用アプリからの呼び出し時に利用）
  Future<void> onAcceptFromWeb21App(
    Uri uri,
    PaymentScreenNotifier otpController,
    Future<void> Function(Param param) callback,
  ) async {
    if (_match(uri) && _isAcceptable) {
      _isAcceptable = false;

      // 法人用アプリからの遷移はiOS・Androidによってパラメータ取得方法が異なるため分岐
      // iOSの場合はクエリパラメータをURLデコードしてから取得する
      if (Platform.isIOS) {
        final decodedParams = uri.queryParameters.map(
          (key, value) => MapEntry(key, Uri.decodeComponent(value)),
        );
        await callback(paramMapper(decodedParams));
      }
      // Androidの場合はExtra要素より取得する
      else {
        final extraParams = await otpController.getIntentExtras();
        if (extraParams != null) await callback(paramMapper(extraParams));
      }
      _isAcceptable = true;
    }
  }
}
