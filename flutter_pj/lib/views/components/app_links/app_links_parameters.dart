import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_links_parameters.freezed.dart';

@freezed
class TimeBasedOtpAuthParam with _$TimeBasedOtpAuthParam {
  const factory TimeBasedOtpAuthParam({
    required String userId,
    required String result,
    required String inputTimeOtp,
  }) = _TimeBasedOtpAuthParam;
}

@freezed
class TransactionAuthParam with _$TransactionAuthParam {
  const factory TransactionAuthParam({
    required String userId,
    required String result,
  }) = _TransactionAuthParam;
}

@freezed
class FreeeInitialLinkParam with _$FreeeInitialLinkParam {
  const factory FreeeInitialLinkParam({
    required String code,
    required String state,
  }) = _FreeeInitialLinkParam;
}
