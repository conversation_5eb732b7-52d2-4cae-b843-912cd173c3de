import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/url_launcher.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class MyAppBar extends HookConsumerWidget implements PreferredSizeWidget {
  const MyAppBar({
    super.key,
    this.title,
    this.actions,
    this.automaticallyImplyLeading = true,
    this.leadingWidth,
    this.leading,
    this.centerTitle,
    this.backgroundColor,
    required this.screenId,
  });

  final Widget? title;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;
  final double? leadingWidth;
  final Widget? leading;
  final bool? centerTitle;
  final Color? backgroundColor;
  final String screenId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppBar(
      centerTitle: false,
      leadingWidth: 86,
      leading: Row(
        children: [
          const SizedBox(width: 16),
          Image.asset(
            Assets.smbcLogo,
            height: 20,
            width: 70,
          ),
        ],
      ),
      automaticallyImplyLeading: automaticallyImplyLeading,
      title: title,
      actions: [
        //TODO お知らせアイコンが実装された場合、お知らせアイコン押下時もFirebaseAnalyticsへログを送信する
        //TODO Ph0.5/SP5/DTPO-2450では未対応

        IconButton(
          icon: SvgPicture.asset(Assets.helpWhiteIcon),
          onPressed: () {
            // FirebaseAnalyticsログ送信
            ref.read(analyticsLogControllerProvider).sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.helpIcon,
                  screenName: AppLocalizations.of(context)!.header,
                );

            ref.watch(urlLauncherProvider).launchLinkId(
                  LinkIds.faq,
                  AppLocalizations.of(context)!.helpIcon,
                  screenId,
                );
          },
        ),
      ],
      // actions,
      backgroundColor: backgroundColor,
      elevation: 0,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(56);
}
