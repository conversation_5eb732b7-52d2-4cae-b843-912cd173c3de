import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class ScreenTitleHeader extends StatelessWidget {
  const ScreenTitleHeader({
    super.key,
    required this.title,
    required this.assetName,
  });

  final String title;
  final String assetName;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 116,
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              Assets.bgWave,
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              const SizedBox(width: 16),
              SvgPicture.asset(
                assetName,
                width: 32,
                height: 32,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      color: AppColors.white,
                      height: 1.5,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
