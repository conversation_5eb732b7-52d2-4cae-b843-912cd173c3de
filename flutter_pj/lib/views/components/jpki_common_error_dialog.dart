import 'dart:async';
import 'dart:io';

import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/utils/nfc_check.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/dialog_action_button.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/screens/identity_verification/card_reader/card_reader_screen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// [shouldPopAfterButtonPress]がtrueの場合、ボタンをタップした後にダイアログを閉じる
void showCommonErrorDialog({
  required BuildContext dialogContext,
  required ErrorPattern errorPattern,
  required Function onPressed1,
  required Function onPressed2,
  bool shouldPopAfterButtonPress = false,
  int? retryCounter,
}) {
  // エラーパターンから、ダイアログで表示する文言を取得する
  DialogErrorTexts getDialogErrorMessage(ErrorPattern errorPattern) {
    switch (errorPattern) {
      case ErrorPattern.errorPattern1:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Action1,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Action2,
        );
      case ErrorPattern.errorPattern2:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern2Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern2Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern2Action1,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern2Action2,
        );
      case ErrorPattern.errorPattern5:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern5Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern5Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern5Action1,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern5Action2,
        );
      case ErrorPattern.errorPattern6:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern6Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern6Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern6Action1,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern6Action2,
        );
      case ErrorPattern.errorPattern9:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern9Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern9Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern9Action,
          '',
        );
      case ErrorPattern.errorPattern10:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern10Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern10Message,
          Platform.isIOS
              // IOSの場合縦型表示するために空白を8個追加
              ? '${AppLocalizations.of(dialogContext)!.jpkiErrorPattern10Action1}        '
              : AppLocalizations.of(dialogContext)!.jpkiErrorPattern10Action1,
          Platform.isIOS
              // IOSの場合縦型表示するために空白を7個追加
              ? '${AppLocalizations.of(dialogContext)!.jpkiErrorPattern10Action2}       '
              : AppLocalizations.of(dialogContext)!.jpkiErrorPattern10Action2,
        );
      case ErrorPattern.errorPattern11:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern11Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern11Message,
          AppLocalizations.of(dialogContext)!.ok,
          '',
        );
      case ErrorPattern.errorPattern12:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern12Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern12Message,
          AppLocalizations.of(dialogContext)!.ok,
          '',
        );
      case ErrorPattern.errorPattern13:
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern13Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern13Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern13ButtonMessage,
          '',
        );
      default:
        // 必ずどれかのパターンに入る想定だが、仮に入った場合は暫定的にパターン1を返却
        return DialogErrorTexts(
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Title,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Message,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Action1,
          AppLocalizations.of(dialogContext)!.jpkiErrorPattern1Action2,
        );
    }
  }

  final DialogErrorTexts dialogErrorTexts = getDialogErrorMessage(errorPattern);

  // アクション2のテキストが空文字(選択肢が1つのみ)のダイアログの場合
  if (dialogErrorTexts.dialogButtonText2.isEmpty) {
    showDialog(
      context: dialogContext,
      barrierColor: AppColors.textBlack.withOpacity(0.3),
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: CommonDialog.ok(
          title: dialogErrorTexts.dialogTitle,
          message: dialogErrorTexts.dialogContent,
          messageTextStyle: Theme.of(context).textTheme.bodySmall,
          okButtonText: dialogErrorTexts.dialogButtonText1,
          onOkPressed: () {
            onPressed1();
          },
          isPopAfterOkButtonPress: shouldPopAfterButtonPress,
        ),
      ),
    );
    return;
  }
  if (Platform.isAndroid) {
    showDialog(
      context: dialogContext,
      barrierDismissible: false,
      barrierColor: AppColors.textBlack.withOpacity(0.3),
      builder: (BuildContext dialogContext) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: Center(
              child: Text(
                dialogErrorTexts.dialogTitle,
                style: const TextStyle(fontSize: 18),
              ),
            ),
            content: IntrinsicHeight(
              child: Center(
                child: Column(
                  children: [
                    // エラーパターン5で残試行回数がnullでない時に残試行回数を表示する
                    errorPattern != ErrorPattern.errorPattern5 ||
                            retryCounter == null
                        ? const SizedBox()
                        : Text(
                            '${AppLocalizations.of(dialogContext)!.rest}$retryCounter${AppLocalizations.of(dialogContext)!.count}\n',
                            style: const TextStyle(
                              fontSize: 15,
                            ),
                            textAlign: TextAlign.center,
                          ),
                    Text(
                      dialogErrorTexts.dialogContent,
                      style: const TextStyle(
                        fontSize: 15,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: <Widget>[
              Align(
                alignment: Alignment.bottomCenter,
                child: TextButton(
                  onPressed: () {
                    onPressed1();
                    if (shouldPopAfterButtonPress) {
                      Navigator.pop(dialogContext);
                    }
                  },
                  child: Text(
                    dialogErrorTexts.dialogButtonText1,
                    style: const TextStyle(color: AppColors.black),
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: TextButton(
                  onPressed: () {
                    onPressed2();
                    if (shouldPopAfterButtonPress) {
                      Navigator.pop(dialogContext);
                    }
                  },
                  child: Text(
                    dialogErrorTexts.dialogButtonText2,
                    style: const TextStyle(color: AppColors.black),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  } else {
    // 共通エラーダイアログを表示する
    // エラーパターン5で残試行回数がnullでない時に残試行回数を表示する
    final dialogContent = errorPattern != ErrorPattern.errorPattern5 ||
            retryCounter == null
        ? dialogErrorTexts.dialogContent
        : '${AppLocalizations.of(dialogContext)!.rest}$retryCounter${AppLocalizations.of(dialogContext)!.count}\n\n${dialogErrorTexts.dialogContent}';
    showDialog(
      context: dialogContext,
      barrierDismissible: false,
      barrierColor: AppColors.textBlack.withOpacity(0.3),
      builder: (context) {
        return PopScope(
          canPop: false,
          child: CommonDialog(
            title: dialogErrorTexts.dialogTitle,
            content: Text(dialogContent),
            buttons: [
              DialogActionTextButton(
                text: dialogErrorTexts.dialogButtonText2,
                textStyle: const TextStyle(
                  fontSize: 14,
                  color: AppColors.blue,
                ),
                onPressed: () {
                  onPressed2();
                },
                shouldPopAfterButtonPress: shouldPopAfterButtonPress,
              ),
              DialogActionTextButton(
                text: dialogErrorTexts.dialogButtonText1,
                textStyle: const TextStyle(
                  fontSize: 14,
                  color: AppColors.blue,
                ),
                onPressed: () {
                  onPressed1();
                },
                shouldPopAfterButtonPress: shouldPopAfterButtonPress,
              ),
            ],
          ),
        );
      },
    );
  }
}

// ボタン1の挙動
void onPressed1(
  BuildContext context,
  WidgetRef ref,
  ErrorPattern errorPattern,
  String screenName,
) {
  final logController = ref.read(analyticsLogControllerProvider);
  if (errorPattern == ErrorPattern.errorPattern1) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern1Action1}(ダイアログ1)',
      screenName: screenName,
    );
    transitionToEkycScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern2) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern2Action1}(ダイアログ2)',
      screenName: screenName,
    );
    CardReaderScreen.readMyNumberCard(context, ref, true);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern4) {
    // エラーパターン4は注意文言モーダル表示のため、ここでのFirebaseAnalyticsログ送信はなし
    CardReaderScreen.readMyNumberCard(context, ref, true);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern5) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern5Action1}(ダイアログ5)',
      screenName: screenName,
    );
    returnToPasswordInputScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern6) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern6Action1}(ダイアログ6)',
      screenName: screenName,
    );
    transitionToEkycScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern9) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern9Action}(ダイアログ9)',
      screenName: screenName,
    );
    Navigator.of(context).pop();
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern10) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern10Action1}(ダイアログ10)',
      screenName: screenName,
    );
    Navigator.of(context).pop();
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern11) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName: '${AppLocalizations.of(context)!.ok}(ダイアログ11)',
      screenName: screenName,
    );
    Navigator.of(context).pop();
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern12) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName: '${AppLocalizations.of(context)!.ok}(ダイアログ12)',
      screenName: screenName,
    );
    Navigator.of(context).pop();
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern13) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern13ButtonMessage}(ダイアログ13)',
      screenName: screenName,
    );
    // アプリを終了
    exit(0);
  }

  // どのパターンにも当てはまらない場合はパターン1と同じ挙動にする
  transitionToEkycScreen(context);
}

// ボタン2の挙動
Future<void> onPressed2(
  BuildContext context,
  WidgetRef ref,
  ErrorPattern errorPattern,
  String screenName, {
  /// [screenIdNumber]が指定されてれば、[errorPattern]==ErrorPattern.errorPattern10の時にtrackイベントを送信する。
  String? screenIdNumber,
}) async {
  final logController = ref.read(analyticsLogControllerProvider);
  if (errorPattern == ErrorPattern.errorPattern1) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern1Action2}(ダイアログ1)',
      screenName: screenName,
    );
    returnToIdentityDocumentScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern2) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern2Action2}(ダイアログ2)',
      screenName: screenName,
    );
    transitionToEkycScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern4) {
    // エラーパターン4は注意文言モーダル表示のため、ここでのFirebaseAnalyticsログ送信はなし
    transitionToEkycScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern5) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern5Action2}(ダイアログ5)',
      screenName: screenName,
    );
    transitionToEkycScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern6) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern6Action2}(ダイアログ6)',
      screenName: screenName,
    );
    returnToIdentityDocumentScreen(context);
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern9) {
    // エラーパターン9の時は何もしない
    return;
  }

  if (errorPattern == ErrorPattern.errorPattern10) {
    // FirebaseAnalyticsログ送信
    logController.sendButtonLog(
      buttonName:
          '${AppLocalizations.of(context)!.jpkiErrorPattern10Action2}(ダイアログ10)',
      screenName: screenName,
    );
    if (screenIdNumber != null) {
      logController.sendTrack(
        buttonName:
            '${AppLocalizations.of(context)!.jpkiErrorPattern10Action2}(ダイアログ10)',
        screenIdNumber: screenIdNumber,
      );
    }
    // NFCが有効な場合、書類選択画面へ戻ってからeKYC画面へ遷移
    if (await NfcCheck.isNfcAvailable()) {
      if (!context.mounted) return;
      await Navigator.pushNamedAndRemoveUntil(
        context,
        RouteNames.ekycExplanation,
        (route) => route.settings.name == RouteNames.identityDocument,
      );
    } else {
      //NFCが無効な場合、お手続番号入力画面に戻ってからekyc概要説明画面へ遷移
      if (!context.mounted) return;
      await Navigator.pushNamedAndRemoveUntil(
        context,
        RouteNames.ekycExplanation,
        (route) => route.settings.name == RouteNames.identityReferenceNumber,
      );
    }
  }
  if (!context.mounted) return;
  // どのパターンにも当てはまらない場合はパターン1と同じ挙動にする
  returnToIdentityDocumentScreen(context);
}

void transitionToEkycScreen(BuildContext context) {
  // 書類選択画面へ戻ってからeKYC画面へ遷移
  Navigator.pushNamedAndRemoveUntil(
    context,
    RouteNames.ekycExplanation,
    (route) => route.settings.name == RouteNames.identityDocument,
  );
}

void returnToIdentityDocumentScreen(BuildContext context) {
  // 証明書画面へ遷移
  Navigator.popUntil(
    context,
    (route) => route.settings.name == RouteNames.identityDocument,
  );
}

void returnToPasswordInputScreen(BuildContext context) {
  // 署名用電子証明書パスワード画面へ遷移
  Navigator.popUntil(
    context,
    (route) => route.settings.name == RouteNames.passwordInput,
  );
}
