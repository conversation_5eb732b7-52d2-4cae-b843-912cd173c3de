import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/home/<USER>';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// WebView（freeeサインアップ画面）
class FreeeViewScreen extends HookConsumerWidget {
  const FreeeViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.buttonName,
    required this.screenIdNumber,
  });

  final String baseUrl;
  final WebViewRequest request;
  final String buttonName;
  final String screenIdNumber;

  /// 初期URL
  String get initialUrl => request.getInitialUrl(baseUrl);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final homeController = ref.watch(homeScreenProvider.notifier);
    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        // WHITELISTED_LIST設定済みの場合は初期URLをホワイトリストの末尾に含める
        return whitelistURLsString.split(',')..add(initialUrl);
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrl];
      }
    });
    Log.i('build - whitelistURLs: $whitelistURLs');

    // フック対象のfreeeからのリダイレクトURL
    // todo 環境変数への移行
    const freeeAuthRedirectPath = '/mobile/api/freee/auth';

    final WebViewController webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(initialUrl));

    webViewController.setNavigationDelegate(
      NavigationDelegate(
        onPageStarted: (String url) {
          analyticsLogController.sendTrack(
            buttonName: buttonName,
            screenIdNumber: screenIdNumber,
          );
          Log.i('onPageStarted - url : $url');
        },
        onNavigationRequest: (NavigationRequest request) async {
          // request.urlはnull非許容なので、早期リターン不要
          Log.i('Navigation request - URL: ${request.url}');
          final url = Uri.parse(request.url);
          // フック対象のURLにリダイレクトされた場合
          if (request.url.contains(freeeAuthRedirectPath)) {
            // クエリパラメータから値を取得しstateに保存
            final queryParameters = url.queryParameters;
            final code = queryParameters['code'] ?? '';
            final stateFromFreee = queryParameters['state'] ?? '';
            await homeController.linkToFreee(code, stateFromFreee, false);
            if (!ref
                .read(homeScreenProvider)
                .freeeTokenStatus
                .isFreeeTokenError) {
              // 取引口座照会画面にてローディングを表示するため、処理の完了を待たない
              unawaited(homeController.initializeFreeeInfo());
            }
            // WebViewを閉じる
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          }
          final shouldNavigate = whitelistURLs
              .any((whitelistURL) => request.url.startsWith(whitelistURL));
          Log.i('Should prevent navigation: $shouldNavigate');
          return shouldNavigate
              ? NavigationDecision.navigate
              : NavigationDecision.prevent;
        },
        onPageFinished: (url) async {
          // ページロード完了時にキャッシュを削除
          await webViewController.clearCache();
        },
      ),
    );

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.freeeWebView,
            );
            // KARTE送信
            // HACK screenIdは最初に表示されたページのIdを入れたいが未決定のため未定義のIDを入れている
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.undecidedId,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
        elevation: 0,
      ),
      body: WebViewWidget(controller: webViewController),
    );
  }
}

typedef WebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request,
  String buttonName,
  String screenIdNumber,
);

final freeeWebViewLauncherProvider = Provider<WebViewLauncher>((ref) {
  return (context, request, buttonName, screenIdNumber) async {
    final baseUrl = ref.read(buildConfigProvider).baseUrl;
    final cookieManager = WebViewCookieManager();
    final encryptedCookie =
        await ref.read(mdManagerProvider).encryptedCookie.load();

    await encryptedCookie.maybeMap(
      (data) async {
        Log.i(data.toLogText);
        await cookieManager.clearCookies();
        await cookieManager.setCookie(
          WebViewCookie(
            name: 'ticket',
            value: data.ticket,
            domain: data.domain,
            path: data.path,
          ),
        );
      },
      orElse: () {},
    );
    if (!context.mounted) return;
    Log.i('request : $request');
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => FreeeViewScreen(
          baseUrl: baseUrl,
          request: request,
          buttonName: buttonName,
          screenIdNumber: screenIdNumber,
        ),
      ),
    );
  };
});
