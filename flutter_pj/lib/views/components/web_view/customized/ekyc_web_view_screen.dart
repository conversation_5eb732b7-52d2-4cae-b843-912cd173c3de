import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/ekyc_explanation/ekyc_explanation_screen_notifier.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/identity_verification/identity_verification_screen_notifier.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/error_info_jpki.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/components/jpki_common_error_dialog.dart';
import 'package:dtp_app/views/components/loading_dialog.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/business_logics/ekyc_webview/ekyc_webview_screen_notifier.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:dtp_app/views/styles/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// WebView（eKYC画面）
class EkycViewScreen extends HookConsumerWidget {
  const EkycViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
  });

  final String baseUrl;
  final WebViewRequest request;

  /// 初期URLリクエスト
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ekycWebViewController =
        ref.watch(ekycWebViewScreenStateScreenProvider.notifier);
    final ekycExplanationController =
        ref.watch(ekycExplanationStateScreenProvider.notifier);
    final state = ref.watch(ekycExplanationStateScreenProvider);
    final logController = ref.read(analyticsLogControllerProvider);
    final MemoryDataManager mdManager = ref.watch(mdManagerProvider);
    final hasSentIdentityInfo = useState(false);
    final onPressedClose = useState(false);
    final identityVerificationController =
        ref.watch(identityVerificationScreenProvider.notifier);

    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        // WHITELISTED_LIST設定済みの場合は初期URLをホワイトリストの末尾に含める
        return whitelistURLsString.split(',')
          ..add(initialUrlRequest.url.toString());
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrlRequest.url.toString()];
      }
    });

    Future<void> completeEkyc() async {
      // ローディング開始
      LoadingDialog.loading(context);
      final userType = await mdManager.userType.load();
      // 本人確認実施者が実質的支配者の場合、本人情報保存APIを実行する
      if (userType.contains('BENEFICIARY')) {
        final isSuccess =
            await ekycExplanationController.sendIdentityVerificationInfo(
          role: userType,
          isEkyc: true,
        );
        hasSentIdentityInfo.value = isSuccess;
      }
      if (!context.mounted) return;
      // ローディング終了
      LoadingDialog.loadingEnd(context);
      // KARTE Trackイベント送信
      logController.sendTrack(
        buttonName: AppLocalizations.of(context)!.ekycWebViewNext,
        screenIdNumber: ScreenIdNumber.ekycWebViewCompleteScreenId,
      );
      if (hasSentIdentityInfo.value) {
        // 本人情報保存APIで成功した場合、本人確認完了画面(実質的支配者)へ遷移する
        await Navigator.pushNamed(
          context,
          RouteNames.registerFinished,
        );
        // 完了フラグをfalseに戻す
        hasSentIdentityInfo.value = false;
      } else {
        // 個人情報入力画面(eKYC)に自動遷移する
        ekycExplanationController.changeNextPageName(RouteNames.enteringName);
      }
    }

    if (state.sendIdentityInfoError != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.microtask(() {
          if (!context.mounted) return;
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              return CommonDialog.ok(
                message:
                    '${IdentityVerificationErrorInfo.identityInfoNotSavedErrorCode}\n${IdentityVerificationErrorInfo.identityInfoNotSavedErrorMessage}',
                onOkPressed: () {
                  logController.sendButtonLog(
                    buttonName: AppLocalizations.of(context)!.ok,
                    screenName:
                        AppLocalizations.of(context)!.ekycExplanationScreen,
                  );
                  ekycExplanationController.clearSendIdentityInfoError();
                  Future.microtask(() {
                    if (!context.mounted) return;
                    Navigator.pushNamedAndRemoveUntil(
                      context,
                      RouteNames.identityReferenceNumber,
                      (route) => route.settings.name == RouteNames.cardReader,
                    );
                  });
                },
              );
            },
          );
        });
      });
    }

    return SafeArea(
      top: false,
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, _) async {
          if (didPop) {
            return;
          }
          showCommonErrorDialog(
            dialogContext: context,
            errorPattern: ErrorPattern.errorPattern10,
            onPressed1: () {
              onPressed1(
                context,
                ref,
                ErrorPattern.errorPattern10,
                AppLocalizations.of(context)!.identificationDocumentScreen,
              );
            },
            onPressed2: () {
              onPressed2(
                context,
                ref,
                ErrorPattern.errorPattern10,
                AppLocalizations.of(context)!.identificationDocumentScreen,
                screenIdNumber: ScreenIdNumber.ekycExplanationScreenId,
              );
            },
          );
          // FirebaseAnalyticsログ送信
          logController.sendButtonLog(
            buttonName: AppLocalizations.of(context)!.batsu,
            screenName: AppLocalizations.of(context)!.ekycExplanationScreen,
          );
        },
        child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            leading: IconButton(
              onPressed: () async {
                final isEkycComplete = ref
                    .read(ekycWebViewScreenStateScreenProvider)
                    .isEkycComplete;
                if (isEkycComplete) {
                  onPressedClose.value = true;
                  await completeEkyc();
                } else {
                  // ekycの処理を完了せずに閉じた場合、共通ダイアログ10を表示する
                  showCommonErrorDialog(
                    dialogContext: context,
                    errorPattern: ErrorPattern.errorPattern10,
                    onPressed1: () {
                      onPressed1(
                        context,
                        ref,
                        ErrorPattern.errorPattern10,
                        AppLocalizations.of(context)!.ekycExplanationScreen,
                      );
                    },
                    onPressed2: () {
                      onPressed2(
                        context,
                        ref,
                        ErrorPattern.errorPattern10,
                        AppLocalizations.of(context)!.ekycExplanationScreen,
                        screenIdNumber: ScreenIdNumber.ekycExplanationScreenId,
                      );
                    },
                  );
                }
                // FirebaseAnalyticsログ送信
                if (!context.mounted) return;
                logController.sendButtonLog(
                  buttonName: AppLocalizations.of(context)!.batsu,
                  screenName:
                      AppLocalizations.of(context)!.ekycExplanationScreen,
                );
              },
              icon: SizedBox(
                width: 24,
                height: 24,
                child: SvgPicture.asset(
                  Assets.closeIcon,
                ),
              ),
            ),
            elevation: 0,
          ),
          body: InAppWebView(
            initialUrlRequest: initialUrlRequest,
            initialSettings: InAppWebViewSettings(
              cacheEnabled: false,
              // cache無効化
              useShouldOverrideUrlLoading: true,
              mediaPlaybackRequiresUserGesture: false,
              // 運転免許証の自動読み取りを可能にする
              applicationNameForUserAgent: 'SMBC-BIZ',
              useHybridComposition: true,
              allowsInlineMediaPlayback: true, // インライン再生を可能に
            ),
            onWebViewCreated: (controller) {
              Log.i('onWebViewCreated - whitelistURLs: $whitelistURLs');
            },
            onPermissionRequest: (controller, request) async {
              // カメラ権限をリクエスト
              await Permission.camera.request();

              return PermissionResponse(
                resources: request.resources,
                action: PermissionResponseAction.GRANT,
              );
            },
            shouldOverrideUrlLoading: (controller, navigationAction) async {
              final uri = navigationAction.request.url;

              if (uri != null) {
                final url = uri.toString();
                Log.i('shouldOverrideUrlLoading - url: $uri');
                // eKYCからのリダイレクトURL（フック対象）と一致しているか確認
                if (url
                    .contains(ref.watch(buildConfigProvider).ekycRedirectUrl)) {
                  // eKYCのWebhook検知後、本人確認方法判定フラグをeKYCにする
                  identityVerificationController.switchToEkyc();

                  ekycWebViewController.updateIsEkycComplete();
                  // eKYCの完了画面を4秒間表示する(暫定)
                  Future.delayed(const Duration(seconds: 4), () async {
                    if (!onPressedClose.value) {
                      await completeEkyc();
                    }
                  });
                  return NavigationActionPolicy.CANCEL;
                } else if (whitelistURLs
                    .any((whitelistURL) => url.startsWith(whitelistURL))) {
                  // ホワイトリストに前方一致する場合はWebViewを起動
                  return NavigationActionPolicy.ALLOW;
                } else {
                  // ホワイトリストに前方一致しない場合は外部ブラウザで開く
                  await launchUrl(uri, mode: LaunchMode.externalApplication);
                  return NavigationActionPolicy.CANCEL;
                }
              }
              return NavigationActionPolicy.ALLOW;
            },
          ),
        ),
      ),
    );
  }
}

typedef WebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request,
);

final ekycWebViewLauncherProvider = Provider<WebViewLauncher>((ref) {
  final baseUrl = ref.read(buildConfigProvider).baseUrl;
  return (context, request) {
    final completer = Completer();
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (_) => EkycViewScreen(baseUrl: baseUrl, request: request),
          ),
        )
        .then(
          (value) => {
            completer.complete(),
          },
        );
    return completer.future;
  };
});
