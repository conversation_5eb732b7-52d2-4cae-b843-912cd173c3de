import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Allganize AIチャットボット用WebView
class AllganizeChatbotWebViewScreen extends HookConsumerWidget {
  const AllganizeChatbotWebViewScreen({
    super.key,
    required this.onClose,
  });

  final VoidCallback onClose;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final baseUrl = ref.read(buildConfigProvider).baseUrl;
    final memoryDataManager = ref.read(mdManagerProvider);
    final isLoading = useState(true);
    final hasError = useState(false);
    final errorMessage = useState<String?>(null);
    final webViewController = useState<InAppWebViewController?>(null);

    // WebViewのURLを構築
    final chatbotUrl = '$baseUrl/allganize_chatbot.html';

    // エラーダイアログを表示する関数
    void showErrorDialog(String message) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CommonDialog.ok(
            title: AppLocalizations.of(context)!.failedToProcess,
            message: message,
            okButtonText: AppLocalizations.of(context)!.close,
            onOkPressed: () {
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.close,
                screenName: AppLocalizations.of(context)!.chatbot,
              );
              onClose();
            },
          );
        },
      );
    }

    // JavaScriptハンドラーを登録
    useEffect(() {
      if (webViewController.value != null) {
        // チャットボットの読み込み完了時のハンドラー
        webViewController.value!.addJavaScriptHandler(
          handlerName: 'onChatbotLoaded',
          callback: (args) {
            Log.i('Chatbot loaded successfully');
            isLoading.value = false;
          },
        );

        // チャットボットのエラー発生時のハンドラー
        webViewController.value!.addJavaScriptHandler(
          handlerName: 'onChatbotError',
          callback: (args) {
            Log.e('Chatbot error: ${args.first}');
            isLoading.value = false;
            hasError.value = true;
            errorMessage.value = args.first.toString();
            showErrorDialog(args.first.toString());
          },
        );

        // チャットボットの再試行時のハンドラー
        webViewController.value!.addJavaScriptHandler(
          handlerName: 'onChatbotRetry',
          callback: (args) {
            Log.i('Chatbot retry requested');
            // 再試行時にローディングアイコンを表示
            isLoading.value = true;
            hasError.value = false;
            errorMessage.value = null;
          },
        );
      }
      return null;
    }, [webViewController.value]);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.chatbot),
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.chatbot,
            );
            // KARTE送信
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.homeScreenId,
            );
            onClose();
          },
          icon: const Icon(Icons.close),
        ),
        elevation: 0,
      ),
      body: FutureBuilder<String>(
        future: memoryDataManager.sessionId.load().timeout(
          const Duration(seconds: 5),
          onTimeout: () => '',
        ),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // sessionId 로딩 중
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (snapshot.hasError) {
            Log.e('Failed to load sessionId: ${snapshot.error}');
            // エラーが発生した場合は空のsessionIdでWebViewを表示
          }

          final sessionId = snapshot.data ?? '';
          Log.i('Loaded sessionId for chatbot: $sessionId');

          // sessionIdが空の場合はログインが必要であることを表示
          if (sessionId.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.login,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'ログインが必要です',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // ログイン画面に遷移
                      Navigator.of(context).pushNamedAndRemoveUntil(
                        RouteNames.login,
                        (route) => false,
                      );
                    },
                    child: Text(AppLocalizations.of(context)!.login),
                  ),
                ],
              ),
            );
          }

          return Stack(
            children: [
              InAppWebView(
                initialUrlRequest: URLRequest(
                  url: WebUri(chatbotUrl),
                  headers: sessionId.isNotEmpty
                      ? {
                          'sessionId': sessionId,
                          'X-Session-ID': sessionId,
                          'Authorization': 'Bearer $sessionId',
                        }
                      : {},
                ),
                initialSettings: InAppWebViewSettings(
                  cacheEnabled: false,
                  useShouldOverrideUrlLoading: true,
                  javaScriptEnabled: true,
                  // Allganize SDK V2のために必要な設定を追加
                  allowsInlineMediaPlayback: true,
                  mediaPlaybackRequiresUserGesture: false,
                  // ESモジュールをサポートするための設定
                  supportZoom: false,
                  verticalScrollBarEnabled: false,
                  horizontalScrollBarEnabled: false,
                ),
                onWebViewCreated: (controller) {
                  webViewController.value = controller;
                  Log.i('Chatbot WebView created');
                },
                onReceivedError: (controller, request, error) {
                  Log.e('Chatbot WebView load error: ${error.hashCode}, ${error.description}');
                  isLoading.value = false;
                  hasError.value = true;
                  errorMessage.value = error.description;
                  showErrorDialog(error.description);
                },
                onConsoleMessage: (controller, consoleMessage) {
                  // コンソールメッセージをログに出力（デバッグ用）
                  Log.d('WebView Console: ${consoleMessage.message}');
                },
                onLoadStop: (controller, url) {
                  Log.i('Chatbot WebView load stopped: $url');
                  // ページの読み込みが完了したら、一定時間後にローディングを非表示にする
                  // JavaScriptハンドラーが呼ばれない場合のフォールバック
                  Future.delayed(const Duration(seconds: 3), () {
                    if (isLoading.value) {
                      isLoading.value = false;
                    }
                  });
                },
                shouldOverrideUrlLoading: (controller, navigationAction) async {
                  final uri = navigationAction.request.url;
                  if (uri != null) {
                    final url = uri.toString();
                    Log.i('shouldOverrideUrlLoading - url: $uri');
                    // Allganize SDK V2のドメインを許可
                    if (url.startsWith(baseUrl) ||
                        url.startsWith('https://sdk.allganize.ai') ||
                        url.startsWith('https://sdk.alli.ai')) {
                      return NavigationActionPolicy.ALLOW;
                    } else {
                      // 外部URLは許可しない
                      return NavigationActionPolicy.CANCEL;
                    }
                  }
                  return NavigationActionPolicy.ALLOW;
                },
              ),
              if (isLoading.value)
                const Center(
                  child: CircularProgressIndicator(),
                ),
            ],
          );
        },
      ),
    );
  }
}

/// Allganize AIチャットボットを表示するためのモーダルダイアログ
Future<void> showAllganizeChatbotDialog(BuildContext context) async {
  await showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Dialog(
        insetPadding: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: SizedBox(
          width: double.infinity,
          height: MediaQuery.of(context).size.height * 0.8,
          child: AllganizeChatbotWebViewScreen(
            onClose: () => Navigator.of(context).pop(),
          ),
        ),
      );
    },
  );
}
