import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/otp_input_result/otp_input_result.dart';
import 'package:dtp_app/utils/idaas_path.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// OTP入力画面
class OtpInputWebViewScreen extends HookConsumerWidget {
  const OtpInputWebViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.encryptedCookie,
    required this.buttonName,
    required this.screenIdNumber,
  });

  final String baseUrl;
  final WebViewRequest request;
  final EncryptedCookie encryptedCookie;
  final String buttonName;
  final String screenIdNumber;

  /// 初期URLリクエスト
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    useEffect(
      () {
        final cookieManager = CookieManager.instance();
        Future(() async {
          final uri = WebUri(request.getInitialUrl(baseUrl));
          await encryptedCookie.maybeMap(
            (data) async {
              Log.i(data.toLogText);
              await cookieManager.setCookie(
                url: uri,
                name: 'ticket',
                value: data.ticket,
                domain: data.domain,
                path: data.path,
                // BFFから取得する際にJSONにエンコードしているため型としてはStringで入ってくる、手動で変える必要がある為bool型に変換
                isSecure: data.secure == 'true',
                isHttpOnly: data.httpOnly == 'true',
              );
              await cookieManager.getCookies(url: uri);
            },
            orElse: () {
              // 何もしない
            },
          );
        });

        return null;
      },
      const [],
    );

    // リダイレクト済みかどうかのフラグ
    bool hasProcessedRedirect = false;
    final controller = ref.watch(loginScreenProvider.notifier);

    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        return whitelistURLsString.split(',');
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrlRequest.url.toString()];
      }
    });

    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () async {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.otpInputWebView,
            );
            // KARTE送信
            // screenIdは最初に表示されたページのIdを入れている
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.otpInputScreenId,
            );
            // キャンセル
            Navigator.of(context).pop(const OtpInputResult.cancel());
            // カウリス宛に端末情報送信処理実行
            await controller.terminalInformationTransmission();
          },
          icon: const Icon(Icons.close),
        ),
      ),
      body: InAppWebView(
        initialUrlRequest: initialUrlRequest,
        initialSettings: InAppWebViewSettings(
          cacheEnabled: false, // cache無効化
          useShouldOverrideUrlLoading: true,
          disallowOverScroll: true,
        ),
        onWebViewCreated: (controller) {
          // KARTE送信
          analyticsLogController.sendTrack(
            buttonName: buttonName,
            screenIdNumber: screenIdNumber,
          );
          Log.i('onWebViewCreated - whitelistURLs: $whitelistURLs');
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          final uri = navigationAction.request.url;
          if (uri != null) {
            const redirectPath = '/web/api/idaas-otp-login';
            const webLoginRedirectPath = '/web/id/login';
            final url = uri.toString();
            Log.i('shouldOverrideUrlLoading - url: $uri');
            if (url.contains(redirectPath) && !hasProcessedRedirect) {
              // 処理が2回呼ばれることを抑止するためフラグをtrueに
              hasProcessedRedirect = true;
              await controller.stopLoading();
              // 暗号化クッキーをCookieManagerで取得
              // (それぞれの項目をWebViewから取得する)
              final cookieManager = CookieManager();
              final cookies = await cookieManager.getCookies(url: uri);
              final idaasEncryptedCookie = await getEncryptedCookie(cookies);

              // WebViewを閉じる
              if (context.mounted) {
                // WebViewを閉じる際にOtpInputResult.successさせることでセッションIDリクエストをlogin_screen.dartで実行
                Navigator.of(context)
                    .pop(OtpInputResult.success(idaasEncryptedCookie));
              }
              return NavigationActionPolicy.CANCEL;
            } else if (url.contains(webLoginRedirectPath)) {
              // WebView内で「戻る」ボタン、もしくは「ログイン画面に戻る」ボタンを押下した時はWebViewを閉じる
              if (context.mounted) {
                Navigator.of(context).pop(OtpInputResult.cancel());
              }
              return NavigationActionPolicy.CANCEL;
            } else if (whitelistURLs
                .any((whitelistURL) => url.startsWith(whitelistURL))) {
              return NavigationActionPolicy.ALLOW;
            } else {
              // ホワイトリストに前方一致しない場合は外部ブラウザで開く
              await launchUrl(uri, mode: LaunchMode.externalApplication);
              return NavigationActionPolicy.CANCEL;
            }
          }
          return NavigationActionPolicy.ALLOW;
        },
      ),
    );
  }

  /// Cookie配列から暗号化クッキーを取得する
  Future<EncryptedCookie> getEncryptedCookie(
    List<Cookie> cookies,
  ) async {
    // 暗号化クッキー文字列を抽出
    const ticket = 'ticket';
    final hasTicketCookie = cookies.firstWhere(
      (cookie) => cookie.name == ticket,
      orElse: () => Cookie(name: '', value: '', domain: '', path: ''),
    );

    if (hasTicketCookie.name == ticket) {
      final encryptedCookie = EncryptedCookie(
        ticket: hasTicketCookie.value.toString(),
        domain: hasTicketCookie.domain.toString(),
        path: hasTicketCookie.path.toString(),
        secure: hasTicketCookie.isSecure.toString(),
        httpOnly: hasTicketCookie.isHttpOnly.toString(),
      );
      Log.i('encryptedCookie: $encryptedCookie');
      return encryptedCookie;
    }
    // なかった時、空で返す(空の場合はID紐付け情報登録APIでエラーになる)
    return EncryptedCookie.empty();
  }
}

typedef OtpInputWebViewLauncher = Future<OtpInputResult> Function({
  required BuildContext context,
  required EncryptedCookie encryptedCookie,
  required String buttonName,
  required String screenIdNumber,
});

final otpInputWebViewLauncherProvider =
    Provider<OtpInputWebViewLauncher>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final baseUrl = bc.baseUrl;
  final otpInputUrl =
      '${ref.watch(buildConfigProvider).idaasBaseUrl}${IdaasPath.otpInputPath}';

  return ({
    required BuildContext context,
    required EncryptedCookie encryptedCookie,
    required String buttonName,
    required String screenIdNumber,
  }) async {
    final res = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => OtpInputWebViewScreen(
          baseUrl: baseUrl,
          request: WebViewRequest.launchUrl(initialUrl: otpInputUrl),
          encryptedCookie: encryptedCookie,
          buttonName: buttonName,
          screenIdNumber: screenIdNumber,
        ),
      ),
    );

    return res as OtpInputResult;
  };
});
