import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// WebView（IDaaSメールアドレス変更画面）
class IdaasMailAddressChangeWebViewScreen extends HookConsumerWidget {
  const IdaasMailAddressChangeWebViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.buttonName,
    required this.screenIdNumber,
  });

  final String baseUrl;
  final WebViewRequest request;
  final String buttonName;
  final String screenIdNumber;

  /// 初期URLリクエスト
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.idaasResetEmailWebView,
            );
            // KARTE送信
            // screenIdは最初に表示されたページのIdを入れている
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.emailChangeWebViewScreenId,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
      ),
      body: InAppWebView(
        initialSettings: InAppWebViewSettings(
          // cache無効化
          cacheEnabled: false,
        ),
        initialUrlRequest: initialUrlRequest,
        onWebViewCreated: (controller) async {
          analyticsLogController.sendTrack(
            buttonName: buttonName,
            screenIdNumber: screenIdNumber,
          );
          Log.i('Url - url: $initialUrlRequest');
        },
        onLoadStop: (controller, url) async {
          // ページの読み込みが完了したときにCookieを取得する
          final cookies = await CookieManager.instance().getCookies(url: url!);
          Log.e(url);
          for (final cookie in cookies) {
            Log.e(
              'Cookie - name: ${cookie.name}, value: ${cookie.value}, domain: ${cookie.domain}, path: ${cookie.path}, isSecure: ${cookie.isSecure}, isHttpOnly: ${cookie.isHttpOnly}',
            );
          }
        },
        onUpdateVisitedHistory: (controller, uri, androidIsReload) async {
          Log.i('Navigated to: $uri');
          const redirectPath = '/dtpid-email-change-complete';
          const timeoutRedirectPath = '/web/timeout';
          const webLoginRedirectPath = '/web/id/login';
          final url = uri.toString();
          if (url.contains(redirectPath)) {
            if (context.mounted) {
              // WebViewを閉じる
              Navigator.of(context).popUntil(
                (router) => router.settings.name == RouteNames.idLinkage,
              );
            }
          }
          if (url.contains(timeoutRedirectPath) ||
              url.contains(webLoginRedirectPath)) {
            // WebView内で「戻る」ボタン、もしくは「ログイン画面に戻る」ボタンを押下した時はWebViewを閉じる
            if (context.mounted) {
              Navigator.of(context).popUntil(
                (router) => router.settings.name == RouteNames.idLinkage,
              );
            }
          }
        },
      ),
    );
  }
}

typedef IdaasMailAddressChangeWebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request,
  String buttonName,
  String screenIdNumber,
);

final idaasMailAddressChangeWebViewLauncherProvider =
    Provider<IdaasMailAddressChangeWebViewLauncher>((ref) {
  return (context, request, buttonName, screenIdNumber) async {
    final cookieManager = CookieManager.instance();
    final uri = WebUri(request.initialUrl);
    final baseUrl = ref.read(buildConfigProvider).baseUrl;
    final encryptedCookie =
        await ref.watch(mdManagerProvider).encryptedCookie.load();

    await encryptedCookie.when(
      (ticket, domain, path, secure, httpOnly) async {
        await cookieManager.setCookie(
          url: uri,
          name: 'ticket',
          value: ticket,
          domain: domain,
          path: path,
          // BFFから取得する際にJSONにエンコードしているため型としてはStringで入ってくる、手動で変える必要がある為bool型に変換
          isSecure: secure == 'true',
          isHttpOnly: httpOnly == 'true',
        );
        await cookieManager.getCookies(url: uri);
      },
      empty: () {},
    );
    if (!context.mounted) return;
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => IdaasMailAddressChangeWebViewScreen(
          baseUrl: baseUrl,
          request: request,
          buttonName: buttonName,
          screenIdNumber: screenIdNumber,
        ),
      ),
    );
  };
});
