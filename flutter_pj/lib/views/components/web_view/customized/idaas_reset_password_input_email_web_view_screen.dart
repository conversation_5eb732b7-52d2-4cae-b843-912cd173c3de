import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// WebView（IDaaSPW再設定メールアドレス入力画面（ログイン画面からの遷移））
class IdaasResetPasswordInputEmailWebViewScreen extends HookConsumerWidget {
  const IdaasResetPasswordInputEmailWebViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.buttonName,
    required this.screenIdNumber,
  });

  final String baseUrl;
  final WebViewRequest request;
  final String buttonName;
  final String screenIdNumber;

  /// 初期URLリクエスト
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName:
                  AppLocalizations.of(context)!.idaasResetPWInputEmailWebView,
            );
            // KARTE送信
            // screenIdは最初に表示されたページのIdを入れている
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.idaasResetPWInputEmailScreenId,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
        elevation: 0,
      ),
      body: InAppWebView(
        initialSettings: InAppWebViewSettings(cacheEnabled: false), // cache無効化
        initialUrlRequest: initialUrlRequest,
        onWebViewCreated: (controller) async {
          // KARTE Trackイベント送信
          analyticsLogController.sendTrack(
            buttonName: buttonName,
            screenIdNumber: screenIdNumber,
          );
          Log.i('Url - url: $initialUrlRequest');
        },
        onUpdateVisitedHistory: (controller, uri, androidIsReload) async {
          Log.i('Navigated to: $uri');
          const redirectPath = '/id/login';
          final url = uri.toString();
          if (url.contains(redirectPath)) {
            if (context.mounted) {
              // WebViewを閉じる
              Navigator.of(context).popUntil(
                (router) => router.settings.name == RouteNames.login,
              );
            }
          }
        },
      ),
    );
  }
}

typedef IdaasResetPasswordInputEmailWebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request,
  String buttonName,
  String screenIdNumber,
);

final idaasResetPasswordInputEmailWebViewLauncherProvider =
    Provider<IdaasResetPasswordInputEmailWebViewLauncher>((ref) {
  final baseUrl = ref.read(buildConfigProvider).baseUrl;
  return (context, request, buttonName, screenIdNumber) {
    final completer = Completer();
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (_) => IdaasResetPasswordInputEmailWebViewScreen(
              baseUrl: baseUrl,
              request: request,
              buttonName: buttonName,
              screenIdNumber: screenIdNumber,
            ),
          ),
        )
        .then(
          (value) => {
            completer.complete(),
          },
        );
    return completer.future;
  };
});
