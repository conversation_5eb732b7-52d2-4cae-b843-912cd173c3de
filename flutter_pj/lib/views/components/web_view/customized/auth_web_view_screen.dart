import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/login/login_screen_notifier.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// AuthWebView（認可画面）
class AuthWebViewScreen extends HookConsumerWidget {
  const AuthWebViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.fromEbizParam,
    this.buttonName,
    required this.screenIdNumber,
  });

  final String baseUrl;
  final WebViewRequest request;
  final String fromEbizParam;
  final String? buttonName;
  final String screenIdNumber;

  /// 初期URLリクエスト（認可画面はPOSTデータとして送信する）
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
        method: 'POST',
        body: Uint8List.fromList(
          utf8.encode('from_ebiz_param=${Uri.encodeComponent(fromEbizParam)}'),
        ),
        headers: {'content-type': 'application/x-www-form-urlencoded'},
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    const authResponsePath = '/mobile/api/bank/auth';
    final loginController = ref.watch(loginScreenProvider.notifier);
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        return whitelistURLsString.split(',');
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrlRequest.url.toString()];
      }
    });

    Log.d('initialUrlRequest: $initialUrlRequest');
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        //HACK WebViewの閉じるボタン共通化する
        leading: IconButton(
          onPressed: () async {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.auth,
            );
            // KARTE送信
            // screenIdは最初に表示されたページのIdを入れている
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.web21AuthWebViewScreenId,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
      ),
      body: InAppWebView(
        onWebViewCreated: (controller) => {
          // ボタン起動でないwebviewのためwebviewの中でログ送信を行う
          // HACK 画面名をクラスに記載しクラスから取得
          analyticsLogController.sendTrack(
            buttonName: buttonName ??
                AppLocalizations.of(context)!.web21AuthWebViewScreen,
            screenIdNumber: screenIdNumber,
          ),
        },
        initialUrlRequest: initialUrlRequest,
        initialSettings: InAppWebViewSettings(
          cacheEnabled: false, // cache無効化
          useShouldOverrideUrlLoading: true,
        ),
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          // 取得したHTMLを表示する（デバッグ用）
          final html = await controller.getHtml();
          Log.d(html);
          final uri = navigationAction.request.url;
          if (uri != null) {
            final url = uri.toString();
            Log.i('shouldOverrideUrlLoading - url: $uri');
            if (url.startsWith(baseUrl) && url.contains(authResponsePath)) {
              // 認可処理成功後のGETレスポンスを処理する
              final queryParameters = uri.queryParameters;
              final code = queryParameters['code'] ?? '';
              final state = queryParameters['state'] ?? '';
              if (code.isNotEmpty && state.isNotEmpty) {
                await loginController.clearAuthError();
                // 認可ボタン押下時、認可拒否履歴のデータを削除
                await loginController.deleteRefusalFlag();
                // 認可コードとstateを設定する
                loginController.setAuthCode(code, state);
                // 認可処理が正しく実施されたことを呼び出し元に通知
                if (context.mounted) Navigator.of(context).pop(true);
              } else {
                // 拒否ボタン押下時codeが返されないので認可拒否履歴にデータを登録
                await loginController.registerRefusalFlag();
                if (context.mounted) Navigator.of(context).pop();
              }
              return NavigationActionPolicy.CANCEL;
            } else if (whitelistURLs
                .any((whitelistURL) => url.startsWith(whitelistURL))) {
              return NavigationActionPolicy.ALLOW;
            } else {
              // ホワイトリストに前方一致しない場合は外部ブラウザで開く
              await launchUrl(uri, mode: LaunchMode.externalApplication);
              return NavigationActionPolicy.CANCEL;
            }
          }
          return NavigationActionPolicy.ALLOW;
        },
      ),
    );
  }
}

typedef AuthWebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request, {
  required String fromEbizParam,
  String? buttonName,
  required String screenIdNumber,
});

final authWebViewLauncherProvider = Provider<AuthWebViewLauncher>((ref) {
  final baseUrl = ref.read(buildConfigProvider).baseUrl;
  return (
    context,
    request, {
    required String fromEbizParam,
    String? buttonName,
    required String screenIdNumber,
  }) async {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => AuthWebViewScreen(
          baseUrl: baseUrl,
          request: request,
          fromEbizParam: fromEbizParam,
          buttonName: buttonName,
          screenIdNumber: screenIdNumber,
        ),
      ),
    );
  };
});
