import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/common_dialog.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Allganize AIチャットボット用全画面WebView
class AllganizeChatbotFullscreenScreen extends HookConsumerWidget {
  const AllganizeChatbotFullscreenScreen({
    super.key,
    required this.sessionId,
  });

  final String sessionId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final baseUrl = ref.read(buildConfigProvider).baseUrl;
    final isLoading = useState(true);
    final hasError = useState(false);
    final errorMessage = useState<String?>(null);
    final webViewController = useState<InAppWebViewController?>(null);

    // WebViewのURLを構築
    final chatbotUrl = '$baseUrl/allganize_chatbot.html';

    Log.i('Fullscreen Chatbot WebView initialized with sessionId: $sessionId');

    // エラーダイアログを表示する関数
    void showErrorDialog(String message) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return CommonDialog.ok(
            title: AppLocalizations.of(context)!.failedToProcess,
            message: message,
            okButtonText: AppLocalizations.of(context)!.close,
            onOkPressed: () {
              analyticsLogController.sendButtonLog(
                buttonName: AppLocalizations.of(context)!.close,
                screenName: AppLocalizations.of(context)!.chatbot,
              );
              Navigator.of(context).pop(); // エラーダイアログを閉じる
              Navigator.of(context).pop(); // 全画面を閉じる
            },
          );
        },
      );
    }

    // JavaScriptハンドラーを登録
    useEffect(() {
      if (webViewController.value != null) {
        // チャットボットの読み込み完了時のハンドラー
        webViewController.value!.addJavaScriptHandler(
          handlerName: 'onChatbotLoaded',
          callback: (args) {
            Log.i('Chatbot loaded successfully');
            isLoading.value = false;
          },
        );

        // チャットボットのエラー発生時のハンドラー
        webViewController.value!.addJavaScriptHandler(
          handlerName: 'onChatbotError',
          callback: (args) {
            Log.e('Chatbot error: ${args.first}');
            isLoading.value = false;
            hasError.value = true;
            errorMessage.value = args.first.toString();
            showErrorDialog(args.first.toString());
          },
        );

        // チャットボットの再試行時のハンドラー
        webViewController.value!.addJavaScriptHandler(
          handlerName: 'onChatbotRetry',
          callback: (args) {
            Log.i('Chatbot retry requested');
            // 再試行時にローディングアイコンを表示
            isLoading.value = true;
            hasError.value = false;
            errorMessage.value = null;
          },
        );
      }
      return null;
    }, [webViewController.value]);

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.chatbot),
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.chatbot,
            );
            // KARTE送信
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.homeScreenId,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
        elevation: 0,
      ),
      body: !isSessionIdLoaded.value
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : sessionId.value?.isEmpty ?? true
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.login,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'ログインが必要です',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          // ログイン画면에 遷移
                          Navigator.of(context).pushNamedAndRemoveUntil(
                            RouteNames.login,
                            (route) => false,
                          );
                        },
                        child: Text(AppLocalizations.of(context)!.login),
                      ),
                    ],
                  ),
                )
              : Stack(
                  children: [
                    InAppWebView(
                      initialUrlRequest: URLRequest(
                        url: WebUri(chatbotUrl),
                        headers: sessionId.isNotEmpty
                            ? {
                                'sessionId': sessionId,
                                'X-Session-ID': sessionId,
                                'Authorization': 'Bearer $sessionId',
                              }
                            : {},
                      ),
                      initialSettings: InAppWebViewSettings(
                        cacheEnabled: false,
                        useShouldOverrideUrlLoading: true,
                        javaScriptEnabled: true,
                        // Allganize SDK V2のために必要な設定を追加
                        allowsInlineMediaPlayback: true,
                        mediaPlaybackRequiresUserGesture: false,
                        // ESモジュールをサポートするための設定
                        supportZoom: false,
                        verticalScrollBarEnabled: false,
                        horizontalScrollBarEnabled: false,
                      ),
                      onWebViewCreated: (controller) {
                        webViewController.value = controller;
                        Log.i('Chatbot WebView created');
                      },
                      onReceivedError: (controller, request, error) {
                        Log.e('Chatbot WebView load error: ${error.code}, ${error.description}');
                        isLoading.value = false;
                        hasError.value = true;
                        errorMessage.value = error.description;
                        showErrorDialog(error.description);
                      },
                      onLoadStop: (controller, url) {
                        Log.i('Chatbot WebView load stopped: $url');
                        // ページの読み込みが完了したら、一定時間後にローディングを非表示にする
                        // JavaScriptハンドラーが呼ばれない場合のフォールバック
                        Future.delayed(const Duration(seconds: 3), () {
                          if (isLoading.value) {
                            isLoading.value = false;
                          }
                        });
                      },
                      onConsoleMessage: (controller, consoleMessage) {
                        // コンソールメッセージをログに出力（デバッグ用）
                        Log.d('WebView Console: ${consoleMessage.message}');
                      },
                      shouldOverrideUrlLoading: (controller, navigationAction) async {
                        final uri = navigationAction.request.url;
                        if (uri != null) {
                          final url = uri.toString();
                          Log.i('shouldOverrideUrlLoading - url: $uri');
                          // Allganize SDK V2のドメインを許可
                          if (url.startsWith(baseUrl) ||
                              url.startsWith('https://sdk.allganize.ai') ||
                              url.startsWith('https://sdk.alli.ai')) {
                            return NavigationActionPolicy.ALLOW;
                          } else {
                            // 외부URLは許可しない
                            return NavigationActionPolicy.CANCEL;
                          }
                        }
                        return NavigationActionPolicy.ALLOW;
                      },
                    ),
                    if (isLoading.value)
                      const Center(
                        child: CircularProgressIndicator(),
                      ),
                  ],
                ),
    );
  }
}

/// Allganize AIチャットボットを全画面で表示する
Future<void> showAllganizeChatbotFullscreen(BuildContext context) async {
  // sessionIdを事前に取得
  final memoryDataManager = ProviderScope.containerOf(context).read(mdManagerProvider);

  String sessionId = '';
  try {
    sessionId = await memoryDataManager.sessionId.load().timeout(
      const Duration(seconds: 5),
      onTimeout: () => '',
    );
    Log.i('Loaded sessionId for fullscreen chatbot: $sessionId');
  } catch (error) {
    Log.e('Failed to load sessionId: $error');
    sessionId = '';
  }

  // sessionIdが空の場合はログイン画面に遷移
  if (sessionId.isEmpty) {
    if (context.mounted) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        RouteNames.login,
        (route) => false,
      );
    }
    return;
  }

  // sessionIdが取得できた場合のみ全画面表示
  if (context.mounted) {
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AllganizeChatbotFullscreenScreen(
          sessionId: sessionId,
        ),
      ),
    );
  }
}
