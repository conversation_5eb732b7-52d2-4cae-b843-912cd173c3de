import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/business_logics/id_linkage/id_linkage_screen_notifier.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:dtp_app/views/routers/route_names.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// WebView（IDaaSアカウント作成画面）
/// HACK クラス名をアカウント作成画面とわかるように変更
class IdaasWebViewScreen extends HookConsumerWidget {
  const IdaasWebViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.buttonName,
    required this.screenIdNumber,
  });

  final String baseUrl;
  final WebViewRequest request;
  final String buttonName;
  final String screenIdNumber;

  /// 初期URLリクエスト
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);

    // IDaaS リダイレクトパス
    const nextPath = '/api/user/link-new-ids';
    const timeoutRedirectPath = '/web/timeout';
    const webLoginRedirectPath = '/web/id/login';
    // リダイレクト済みかどうかのフラグ
    bool hasProcessedRedirect = false;
    final idLinkageController = ref.read(idLinkageScreenProvider.notifier);

    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        return whitelistURLsString.split(',');
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrlRequest.url.toString()];
      }
    });

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: AppLocalizations.of(context)!.idaasWebView,
            );
            // KARTE送信
            // screenIdは最初に表示されたページのIdを入れている
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: ScreenIdNumber.accountCreateWebViewScreenId,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
      ),
      body: InAppWebView(
        onWebViewCreated: (controller) => {
          analyticsLogController.sendTrack(
            buttonName: buttonName,
            screenIdNumber: screenIdNumber,
          ),
        },
        initialUrlRequest: initialUrlRequest,
        initialSettings: InAppWebViewSettings(
          cacheEnabled: false, // cache無効化
          useShouldOverrideUrlLoading: true,
        ),
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          final uri = navigationAction.request.url;

          if (uri != null) {
            final url = uri.toString();
            Log.i('shouldOverrideUrlLoading - url: $uri');
            if (url.contains(nextPath) && !hasProcessedRedirect) {
              // 処理が2回呼ばれることを抑止するためフラグをtrueに
              hasProcessedRedirect = true;
              await controller.stopLoading();
              // 暗号化クッキーをCookieManagerで取得
              // (それぞれの項目をWebViewから取得する)
              final cookieManager = CookieManager();
              final cookies = await cookieManager.getCookies(url: uri);
              final encryptedCookie = await getEncryptedCookie(cookies);
              // ID紐付け情報登録APIを実行
              await idLinkageController.issueAndLinkDtpId(encryptedCookie);
              // WebViewを閉じる
              if (context.mounted) {
                Navigator.popUntil(
                  context,
                  (router) => router.settings.name == RouteNames.idLinkage,
                );
              }
              return NavigationActionPolicy.CANCEL;
            } else if (url.contains(timeoutRedirectPath) ||
                url.contains(webLoginRedirectPath)) {
              // WebView内で「戻る」ボタンを押下した時はWebViewを閉じる
              if (context.mounted) {
                Navigator.popUntil(
                  context,
                  (router) => router.settings.name == RouteNames.idLinkage,
                );
              }
              return NavigationActionPolicy.CANCEL;
            } else if (whitelistURLs
                .any((whitelistURL) => url.startsWith(whitelistURL))) {
              return NavigationActionPolicy.ALLOW;
            } else {
              // ホワイトリストに前方一致しない場合は外部ブラウザで開く
              await launchUrl(uri, mode: LaunchMode.externalApplication);
              return NavigationActionPolicy.CANCEL;
            }
          }
          return NavigationActionPolicy.ALLOW;
        },
      ),
    );
  }

  /// Cookie配列から暗号化クッキーを取得する
  Future<EncryptedCookie> getEncryptedCookie(
    List<Cookie> cookies,
  ) async {
    // 暗号化クッキー文字列を抽出
    const ticket = 'ticket';
    final hasTicketCookie = cookies.firstWhere(
      (cookie) => cookie.name == ticket,
      orElse: () => Cookie(name: '', value: '', domain: '', path: ''),
    );

    if (hasTicketCookie.name == ticket) {
      final encryptedCookie = EncryptedCookie(
        ticket: hasTicketCookie.value.toString(),
        domain: hasTicketCookie.domain.toString(),
        path: hasTicketCookie.path.toString(),
        secure: hasTicketCookie.isSecure.toString(),
        httpOnly: hasTicketCookie.isHttpOnly.toString(),
      );
      Log.i('encryptedCookie: $encryptedCookie');
      return encryptedCookie;
    }
    // なかった時、空で返す(空の場合はID紐付け情報登録APIでエラーになる)
    return EncryptedCookie.empty();
  }
}

typedef IdaasWebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request,
  String buttonName,
  String screenIdNumber,
);

final idaasWebViewLauncherProvider = Provider<IdaasWebViewLauncher>((ref) {
  final baseUrl = ref.read(buildConfigProvider).baseUrl;
  return (context, request, buttonName, screenIdNumber) {
    final completer = Completer();
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (_) => IdaasWebViewScreen(
              baseUrl: baseUrl,
              request: request,
              buttonName: buttonName,
              screenIdNumber: screenIdNumber,
            ),
          ),
        )
        .then(
          (value) => {
            completer.complete(),
          },
        );
    return completer.future;
  };
});
