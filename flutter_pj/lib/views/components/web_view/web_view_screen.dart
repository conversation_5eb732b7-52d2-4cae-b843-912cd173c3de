import 'dart:async';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/views/components/web_view/web_view_request.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// WebView（汎用）
class WebViewScreen extends HookConsumerWidget {
  const WebViewScreen({
    super.key,
    required this.baseUrl,
    required this.request,
    required this.screenIdNumber,
    required this.screenName,
  });

  final String baseUrl;
  final WebViewRequest request;
  final String screenIdNumber;
  final String screenName;

  /// 初期URLリクエスト
  URLRequest get initialUrlRequest => URLRequest(
        url: WebUri(
          request.getInitialUrl(baseUrl),
        ),
      );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final analyticsLogController = ref.read(analyticsLogControllerProvider);
    final whitelistURLs = useMemoized(() {
      final String whitelistURLsString =
          ref.watch(buildConfigProvider).webViewWhiteList;
      if (whitelistURLsString.isNotEmpty) {
        // WHITELISTED_LIST設定済みの場合は初期URLをホワイトリストの末尾に含める
        return whitelistURLsString.split(',')
          ..add(initialUrlRequest.url.toString());
      } else {
        // WHITELISTED_LIST未設定の場合は初期URLをホワイトリストに含める
        return [initialUrlRequest.url.toString()];
      }
    });

    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {
            // FirebaseAnalyticsログ送信
            analyticsLogController.sendButtonLog(
              buttonName: AppLocalizations.of(context)!.close,
              screenName: screenName,
            );
            // KARTE送信
            // screenIdは前のページのIdを入れている(jpkiTermsScreenId or termsScreenId)
            analyticsLogController.sendTrack(
              buttonName: AppLocalizations.of(context)!.close,
              screenIdNumber: screenIdNumber,
            );
            Navigator.of(context).pop();
          },
          icon: const Icon(Icons.close),
        ),
        elevation: 0,
      ),
      body: InAppWebView(
        initialUrlRequest: initialUrlRequest,
        initialSettings: InAppWebViewSettings(
          cacheEnabled: false, // cache無効化
          useShouldOverrideUrlLoading: true,
        ),
        onWebViewCreated: (controller) {
          Log.i('onWebViewCreated - whitelistURLs: $whitelistURLs');
        },
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          final uri = navigationAction.request.url;
          if (uri != null) {
            final url = uri.toString();
            Log.i('shouldOverrideUrlLoading - url: $uri');
            if (whitelistURLs
                .any((whitelistURL) => url.startsWith(whitelistURL))) {
              return NavigationActionPolicy.ALLOW;
            } else {
              // ホワイトリストに前方一致しない場合は外部ブラウザで開く
              await launchUrl(uri, mode: LaunchMode.externalApplication);
              return NavigationActionPolicy.CANCEL;
            }
          }
          return NavigationActionPolicy.ALLOW;
        },
      ),
    );
  }
}

typedef WebViewLauncher = Future<void> Function(
  BuildContext context,
  WebViewRequest request,
  String screenName,
  String screenIdNumber,
);

final webViewLauncherProvider = Provider<WebViewLauncher>((ref) {
  final baseUrl = ref.read(buildConfigProvider).baseUrl;
  return (context, request, screenName, screenIdNumber) {
    final completer = Completer();
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (_) => WebViewScreen(
              baseUrl: baseUrl,
              request: request,
              screenIdNumber: screenIdNumber,
              screenName: screenName,
            ),
          ),
        )
        .then(
          (value) => {
            completer.complete(),
          },
        );
    return completer.future;
  };
});
