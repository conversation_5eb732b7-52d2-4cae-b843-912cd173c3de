class WebViewRequest {
  const WebViewRequest.launchUrl({
    this.title = '',
    required this.initialUrl,
  }) : initialPath = '';

  const WebViewRequest.launchPath({
    this.title = '',
    required this.initialPath,
  }) : initialUrl = '';

  final String title;
  final String initialUrl;
  final String initialPath;

  String getInitialUrl(String baseUrl) {
    if (initialUrl.isNotEmpty) {
      return initialUrl;
    } else {
      if (baseUrl.endsWith('/')) {
        return '$baseUrl$initialPath';
      } else {
        return '$baseUrl/$initialPath';
      }
    }
  }
}
