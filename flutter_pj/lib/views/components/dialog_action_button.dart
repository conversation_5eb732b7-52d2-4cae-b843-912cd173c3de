import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// アプリ内のダイアログ上に表示するボタン
abstract class DialogActionButton {
  /// ボタンのテキストを生成する
  String buildText(BuildContext context);

  /// ボタンのテキストスタイル
  TextStyle? get textStyle;

  /// ボタンをタップした時の処理
  void Function()? get onPressed;

  /// ボタンをタップした後にダイアログを閉じるかどうか
  bool? get shouldPopAfterButtonPress;
}

/// デフォルトの文言を持つボタン
abstract class _DialogActionButtonWithDefaultText
    implements DialogActionButton {
  @override
  String buildText(BuildContext context) {
    if (text != null) {
      // テキストが設定されている場合はそのまま表示
      return text!;
    } else {
      // テキストが設定されていない場合はデフォルトの文言を表示
      return buildDefaultText(context);
    }
  }

  /// デフォルトの文言を生成する
  /// [text]が設定されていない場合に使用される
  /// AppLocalizationsから対応する文字列を取ってくる
  String buildDefaultText(BuildContext context);

  String? text;

  _DialogActionButtonWithDefaultText({this.text});
}

/// OKボタン
class DialogActionOkButton extends _DialogActionButtonWithDefaultText {
  @override
  String buildDefaultText(BuildContext context) =>
      AppLocalizations.of(context)!.ok;

  @override
  final TextStyle? textStyle;
  @override
  final void Function()? onPressed;
  @override
  final bool? shouldPopAfterButtonPress;

  /// OKボタンを作成する
  /// [text]に値が設定されていない場合は、[AppLocalizations.ok]を表示する
  DialogActionOkButton({
    super.text,
    this.textStyle,
    this.onPressed,
    this.shouldPopAfterButtonPress,
  });
}

/// キャンセルボタン
class DialogActionCancelButton extends _DialogActionButtonWithDefaultText {
  @override
  String buildDefaultText(BuildContext context) =>
      AppLocalizations.of(context)!.cancel;
  @override
  final TextStyle? textStyle;
  @override
  final void Function()? onPressed;
  @override
  final bool? shouldPopAfterButtonPress;

  /// キャンセルボタンを作成する
  /// [text]に値が設定されていない場合は、[AppLocalizations.cancel]を表示する
  DialogActionCancelButton({
    super.text,
    this.textStyle,
    this.onPressed,
    this.shouldPopAfterButtonPress,
  });
}

/// その他のテキストを持つボタン
class DialogActionTextButton implements DialogActionButton {
  @override
  String buildText(BuildContext context) => text;
  @override
  final TextStyle? textStyle;
  @override
  final void Function()? onPressed;
  @override
  final bool? shouldPopAfterButtonPress;

  final String text;

  /// その他のテキストを持つボタンを作成する
  DialogActionTextButton({
    required this.text,
    this.textStyle,
    this.onPressed,
    this.shouldPopAfterButtonPress,
  });
}
