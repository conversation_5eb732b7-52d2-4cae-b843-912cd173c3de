import 'package:flutter/material.dart';

//HACK: Assertion処理を実施し、TextFormFieldやTextFieldが使用できないようにする。
class NotCopyTextForm<PERSON>ield extends TextFormField {
  NotCopyTextFormField({
    super.key,
    super.controller,
    super.initialValue,
    super.focusNode,
    super.decoration,
    super.keyboardType,
    super.textCapitalization,
    super.textInputAction,
    super.style,
    super.strutStyle,
    super.textDirection,
    super.textAlign,
    super.textAlignVertical,
    super.autofocus,
    super.readOnly,
    super.showCursor,
    super.obscuring<PERSON>haracter,
    super.obscureText,
    super.autocorrect,
    super.smartDashesType,
    super.smartQuotesType,
    super.enableSuggestions,
    super.maxLengthEnforcement,
    super.maxLines,
    super.minLines,
    super.expands,
    super.maxLength,
    super.onChanged,
    super.onTap,
    super.onTapAlwaysCalled,
    super.onTapOutside,
    super.onEditingComplete,
    super.onFieldSubmitted,
    super.inputFormatters,
    super.enabled,
    super.ignorePointers,
    super.cursorWidth,
    super.cursorHeight,
    super.cursorRadius,
    super.cursorColor,
    super.cursorErrorColor,
    super.keyboardAppearance,
    super.scrollPadding,
    super.enableInteractiveSelection,
    super.selectionControls,
    super.buildCounter,
    super.scrollPhysics,
    super.autofillHints,
    super.autovalidateMode,
    super.scrollController,
    super.restorationId,
    super.enableIMEPersonalizedLearning,
    super.mouseCursor,
    EditableTextContextMenuBuilder? contextMenuBuilder,
    super.spellCheckConfiguration,
    super.magnifierConfiguration,
    super.undoController,
    super.onAppPrivateCommand,
    super.cursorOpacityAnimates,
    super.selectionHeightStyle,
    super.selectionWidthStyle,
    super.dragStartBehavior,
    super.contentInsertionConfiguration,
    super.statesController,
    super.clipBehavior,
    super.scribbleEnabled,
    super.canRequestFocus,
    super.validator,
    super.groupId,
    super.forceErrorText,
    super.onSaved,
  }) : super(
          contextMenuBuilder: _customContextMenuBuilder,
        );

  // デフォルトのcontextMenuBuilder
  static Widget _customContextMenuBuilder(
    BuildContext context,
    EditableTextState editableTextState,
  ) {
    final List<ContextMenuButtonItem> buttonItems =
        editableTextState.contextMenuButtonItems;

    final filteredButtonItems =
        buttonItems.where((ContextMenuButtonItem buttonItem) {
      return buttonItem.type == ContextMenuButtonType.paste;
    }).toList();

    return AdaptiveTextSelectionToolbar.buttonItems(
      anchors: editableTextState.contextMenuAnchors,
      buttonItems: filteredButtonItems,
    );
  }
}
