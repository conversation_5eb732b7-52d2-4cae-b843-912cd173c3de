import 'package:dtp_app/gen/fonts.gen.dart';
import 'package:dtp_app/views/styles/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ScreenNumber extends StatelessWidget {
  const ScreenNumber({
    super.key,
    required this.screenNumber,
    this.color = AppColors.textBlack,
  });

  final String screenNumber;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 16, 16, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Text(
            AppLocalizations.of(context)!.gamenBangou,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  fontSize: 12,
                  color: color,
                ),
          ),
          Text(
            screenNumber,
            style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  fontFamily: FontFamily.robotoCondensed,
                  fontSize: 12,
                  color: color,
                ),
          ),
        ],
      ),
    );
  }
}
