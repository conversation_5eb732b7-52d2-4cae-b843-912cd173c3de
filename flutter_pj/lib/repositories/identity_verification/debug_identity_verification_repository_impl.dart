import 'package:dtp_app/business_logics/interfaces/identity_verification_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/identity_verification_address/identity_verification_address.dart';
import 'package:dtp_app/models/identity_verification_info/identity_verification_info.dart';

class DebugIdentityVerificationRepositoryImpl
    implements IdentityVerificationRepository {
  @override
  Future<AppResult<void>> sendIdentityVerificationInfo(
    IdentityVerificationInfo info,
  ) async {
    // return const AppResult.failure(AppError(message: 'エラー'));

    return const AppResult.success(null);
  }

  @override
  Future<AppResult<Address>> fetchAddress(
    String? postcode,
  ) async {
    return const AppResult.success(
      Address(prefecture: '東京都', city: '港区', street: '六本木'),
    );
  }
}
