import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/corporate_web_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class CorporateWebRepositoryImpl implements CorporateWebRepository {
  CorporateWebRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  @override
  Future<AppResult<bool>> checkCorporateWebStatus() async {
    try {
      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/identityVerification/corporateWebStatus',
      );
      final json = parseJsonMap(res.body);
      final isOpened = json['isOpened'];

      if (isOpened is bool) {
        Log.d('法人Web開局状況(開局中：true,閉局中：false): $isOpened');
        return AppResult.success(isOpened);
      } else {
        // 法人Web開局状況確認フラグがbool値でない場合、エラー扱い
        const corporateWebError = AppError(message: 'isOpened is not boolean');
        return const AppResult.failure(corporateWebError);
      }
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final corporateWebRepositoryProvider = Provider<CorporateWebRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);

  return CorporateWebRepositoryImpl(bc.baseUrl, bc.apiVersion, api);
});
