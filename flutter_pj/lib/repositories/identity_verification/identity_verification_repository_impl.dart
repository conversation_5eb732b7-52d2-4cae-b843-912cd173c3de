import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/identity_verification_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/identity_verification_address/identity_verification_address.dart';
import 'package:dtp_app/models/identity_verification_info/identity_verification_info.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class IdentityVerificationRepositoryImpl
    implements IdentityVerificationRepository {
  IdentityVerificationRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  @override
  Future<AppResult<void>> sendIdentityVerificationInfo(
    IdentityVerificationInfo info,
  ) async {
    try {
      await _api.post(
        endpoint: 'api/$_apiVersion/identityVerification',
        body: info.toJson(),
      );
      return const AppResult.success(null);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<Address>> fetchAddress(
    String? postcode,
  ) async {
    try {
      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/identityVerification/address?postcode=$postcode',
      );

      final json = parseJsonMap(res.body);
      final address = Address.fromJson(json);

      return AppResult.success(address);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final identityVerificationRepositoryProvider =
    Provider<IdentityVerificationRepository>((ref) {
  final bc = ref.read(buildConfigProvider);
  return IdentityVerificationRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    ref.read(apiClientProvider),
  );
});
