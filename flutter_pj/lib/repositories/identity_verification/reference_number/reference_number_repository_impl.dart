import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/identity_reference_number_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/identity_verification_reference_number_info/identity_verification_reference_number_info.dart';
import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class IdentityReferenceNumberRepositoryImpl
    implements IdentityReferenceNumberRepository {
  IdentityReferenceNumberRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  @override
  Future<AppResult<OpenAccountAppTemp>> fetchOpenAccountAppTemp({
    required String referenceNumber,
  }) async {
    try {
      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/identityVerification/openAccountApplication/temporary?referenceNumber=$referenceNumber',
      );

      final json = parseJsonMap(res.body);
      final openAccountAppTemp = OpenAccountAppTemp.fromJson(json);

      return AppResult.success(openAccountAppTemp);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<ReferenceNumberInfo>> checkReferenceNumberStatus({
    required String referenceNumber,
  }) async {
    try {
      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/identityVerification/openAccountApplication/screeningStatuses?referenceNumber=$referenceNumber',
      );

      final json = parseJsonMap(res.body);
      final referenceNumberInfo = ReferenceNumberInfo.fromJson(json);
      Log.d('お手続番号に紐づく本人確認状況: $referenceNumberInfo');

      return AppResult.success(referenceNumberInfo);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<String>> getUuid({
    required String referenceNumber,
  }) async {
    try {
      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/identityVerification/uuid?referenceNumber=$referenceNumber',
      );

      final json = parseJsonMap(res.body);
      final uuid = json['uuid'] as String;

      return AppResult.success(uuid);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final identityReferenceNumberRepositoryProvider =
    Provider<IdentityReferenceNumberRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  return IdentityReferenceNumberRepositoryImpl(bc.baseUrl, bc.apiVersion, api);
});
