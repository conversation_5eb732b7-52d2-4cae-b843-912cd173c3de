import 'package:dtp_app/business_logics/interfaces/identity_reference_number_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/identity_verification_reference_number_info/identity_verification_reference_number_info.dart';
import 'package:dtp_app/models/open_account_app_temp/open_account_app_temp.dart';

class DebugReferenceNumberRepositoryImpl
    implements IdentityReferenceNumberRepository {
  @override
  Future<AppResult<OpenAccountAppTemp>> fetchOpenAccountAppTemp({
    required String referenceNumber,
  }) async {
    return const AppResult.success(
      OpenAccountAppTemp(
        isIdentityVerified: false,
        validationStatus: 'STEP4',
        userType: 'BENEFICIARY4',
      ),
    );
  }

  @override
  Future<AppResult<ReferenceNumberInfo>> checkReferenceNumberStatus({
    required String referenceNumber,
  }) async {
    return const AppResult.success(
      ReferenceNumberInfo(
        isAccountOpeningRequestSubmitted: false,
        isRepresentativeHasFault: false,
        isAgentHasFault: false,
        isBeneficiary1HasFault: false,
        isBeneficiary2HasFault: false,
        isBeneficiary3HasFault: false,
        isBeneficiary4HasFault: false,
      ),
    );
  }

  @override
  Future<AppResult<String>> getUuid({required String referenceNumber}) async {
    return const AppResult.success('**********');
  }
}
