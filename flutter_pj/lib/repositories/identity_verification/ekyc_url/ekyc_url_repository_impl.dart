import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/ekyc_url_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class EkycUrlRepositoryImpl implements EkycUrlRepository {
  EkycUrlRepositoryImpl(
    this._apiVersion,
    this._api,
  );

  final String _apiVersion;
  final ApiClient _api;

  @override
  Future<AppResult<String>> requestEkycUrl({
    required String referenceNumber,
    required String uuid,
    required String userType,
  }) async {
    try {
      final body = jsonEncode({
        'referenceNumber': referenceNumber,
        'uuid': uuid,
        'userType': userType,
      });

      final res = await _api.postJsonData(
        endpoint: 'api/$_apiVersion/identityVerification/ekycUrl',
        body: body,
      );

      final json = parseJsonMap(res.body);
      final ekycUrl = json['ekycUrl'] as String;

      Log.d('eKYCのWebViewURL: $ekycUrl');
      return AppResult.success(ekycUrl);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final ekycUrlRepositoryProvider = Provider<EkycUrlRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  return EkycUrlRepositoryImpl(bc.apiVersion, api);
});
