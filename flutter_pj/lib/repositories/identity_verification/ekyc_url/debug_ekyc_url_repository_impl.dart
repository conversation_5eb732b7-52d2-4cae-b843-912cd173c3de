import 'package:dtp_app/business_logics/interfaces/ekyc_url_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

class DebugEkycUrlRepositoryImpl implements EkycUrlRepository {
  @override
  Future<AppResult<String>> requestEkycUrl({
    required String referenceNumber,
    required String uuid,
    required String userType,
  }) async {
    return const AppResult.success('https://www.ekyc');
  }
}
