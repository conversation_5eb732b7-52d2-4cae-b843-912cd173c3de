import 'dart:async';
import 'dart:collection';

import 'package:dtp_app/models/api_response_with_date/api_response_with_date.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

typedef _Task = Future<void> Function();

/// 一度にひとつのタスクのみを実行するAPIクライアント
/// 同時に来たリクエストをキューに入れて順番に実行する
/// Anserを利用するAPIにのみ使用する想定
class SerialCallApiClient implements ApiClient {
  SerialCallApiClient(this._apiClient);

  final ApiClient _apiClient;

  final _controller = StreamController<_Task>();
  final queue = Queue<_Task>();
  bool busy = false;
  StreamSubscription<_Task>? _subscription;

  /// 初回サブスクライブ処理
  void initialize() {
    _subscription = _controller.stream.listen((task) async {
      // 新しいリクエストをキューに追加
      queue.add(task);
      if (!busy) {
        // キューを実行中でなければ、そのまま処理を開始する
        busy = true;
        while (busy) {
          // キューに入れられたリクエストを順番に実行
          await queue.first();
          queue.removeFirst();
          if (queue.isEmpty) {
            // キューが空になったら実行中フラグをオフに戻して終了
            busy = false;
          }
        }
      }
    });
  }

  void dispose() {
    _subscription?.cancel();
    _controller.close();
  }

  Future<ApiResponseWithDate> _enqueue(
    Future<ApiResponseWithDate> Function() action,
  ) {
    final completer = Completer<ApiResponseWithDate>();
    _controller.add(() async {
      await action().then((result) {
        // API呼び出しが成功したらCompleterを通じて呼び出し元に通知
        completer.complete(result);
      }).catchError((error) {
        completer.completeError(error as Object);
      });
    });
    // リクエスト呼び出し元にはCompleterを返す
    return completer.future;
  }

  @override
  Future<ApiResponseWithDate> get({
    String? url,
    Map<String, String>? headers,
  }) =>
      _enqueue(
        () async => _apiClient.get(
          url: url,
          headers: headers,
        ),
      );

  @override
  Future<ApiResponseWithDate> post({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  }) =>
      _enqueue(
        () => _apiClient.post(
          endpoint: endpoint,
          headers: headers,
          body: body,
        ),
      );

  @override
  Future<ApiResponseWithDate> postJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  }) =>
      _enqueue(
        () => _apiClient.postJsonData(
          endpoint: endpoint,
          headers: headers,
          body: body,
        ),
      );

  @override
  Future<ApiResponseWithDate> postUrlEncodeData({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, String> body,
  }) =>
      _enqueue(
        () => _apiClient.postUrlEncodeData(
          endpoint: endpoint,
          headers: headers,
          body: body,
        ),
      );

  @override
  Future<ApiResponseWithDate> put({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  }) =>
      _enqueue(
        () => _apiClient.put(
          endpoint: endpoint,
          headers: headers,
          body: body,
        ),
      );

  @override
  Future<ApiResponseWithDate> putJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  }) =>
      _enqueue(
        () => _apiClient.putJsonData(
          endpoint: endpoint,
          headers: headers,
          body: body,
        ),
      );

  @override
  Future<ApiResponseWithDate> delete({
    required String endpoint,
    Map<String, String>? headers,
  }) =>
      _enqueue(
        () => _apiClient.delete(
          endpoint: endpoint,
          headers: headers,
        ),
      );
}

final serialCallApiClientProvider = Provider<ApiClient>((ref) {
  final serialCallApiClient = SerialCallApiClient(ref.watch(apiClientProvider));
  ref.onDispose(serialCallApiClient.dispose);
  return serialCallApiClient..initialize();
});
