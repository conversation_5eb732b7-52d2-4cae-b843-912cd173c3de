import 'dart:async';
import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/datas/login_flag_provider.dart';
import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/api_response_with_date/api_response_with_date.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/authorization_status/authorization_status.dart';
import 'package:dtp_app/models/consent_status/consent_status.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/models/session/session.dart';
import 'package:dtp_app/models/session_id_or_high_risk_user_id/session_id_or_high_risk_user_id.dart';
import 'package:dtp_app/models/session_permission_status/session_permission_status.dart';
import 'package:dtp_app/models/vdid_login/vdid_login_response.dart';
import 'package:dtp_app/models/vdid_login_exception/vdid_login_exception.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/bank_account/bank_account_repository_impl.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class LoginRepositoryImpl implements LoginRepository {
  LoginRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._valueDoorApi,
    this._mdManager,
    this._secureStorageManager,
    this._bankAccountRepository,
    this._onLoginStatusChanged,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final ApiClient _valueDoorApi;
  final MemoryDataManager _mdManager;
  final SecureStorageManager _secureStorageManager;
  final BankAccountRepository _bankAccountRepository;
  final void Function() _onLoginStatusChanged;

  // Gets権限有無のフラグを保存するキャッシュ（FirebaseAnalytics送信用）
  String _getsAuthStatus = '';

  // VDへのAPIリクエストの際にリクエストヘッダに格納する値
  String _currentVdCookie = '';

  // VDへのAPIリクエストの際のセッションID
  String _vdCurrentSessionId = '';

  // AP次画面IDを保存するキャッシュ
  String _apNextScreenId = '';

  // HACK 認可時のエラーをrepositoryにキャッシュとして保持するのではなく
  // 引数として情報を渡すようにする。
  // 認可時のエラーを格納する変数
  AppError? _authError;

  // 共通エラー
  final defaultError = AppError(
    message: ErrorInfo.defaultErrorMessage,
    code: ErrorInfo.defaultErrorCode,
  );

  /// HACK LoginStatusとして現状有効な情報を保持していないセッション情報は返却しないようにする
  @override
  Future<AppResult<LoginStatus>> fetchLoginStatus() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      if (sessionId.isNotEmpty) {
        final session = await _mdManager.session.load();
        return AppResult.success(LoginStatus.login(session: session));
      } else {
        return const AppResult.success(LoginStatus.logout());
      }
    } catch (e) {
      return AppResult.failure(defaultError);
    }
  }

  @override
  Future<AppResult<SessionIdOrHighRiskUserId>> verifySamlResponse(
    String samlResponse,
    String caulisSessionId,
  ) async {
    try {
      // SAML検証
      final verifyRes = await _api.post(
        endpoint: 'api/$_apiVersion/saml/verify',
        body: {
          'samlResponse': samlResponse,
          'caulisSessionId': caulisSessionId,
        },
      );

      final response = parseJsonMap(verifyRes.body);
      final encryptedCookieJson = response['encryptedCookie'];
      final sessionIdOrHighRiskUserId = SessionIdOrHighRiskUserId.from(
        sessionId: response['sessionId'],
        highRiskUserId: response['highRiskUserId'],
      );

      if (sessionIdOrHighRiskUserId != null && encryptedCookieJson != null) {
        // 取得した値を格納
        final encryptedCookie = EncryptedCookie(
          ticket: encryptedCookieJson['ticket'] as String,
          domain: encryptedCookieJson['domain'] as String,
          path: encryptedCookieJson['path'] as String,
          secure: encryptedCookieJson['secure'] as String,
          httpOnly: encryptedCookieJson['httponly'] as String,
        );
        // 暗号化Cookieを保存
        await _mdManager.encryptedCookie.save(encryptedCookie);

        // sessionIdの場合のみ、保存してログイン中フラグを更新する
        if (sessionIdOrHighRiskUserId.isSessionId) {
          await _mdManager.sessionId.save(sessionIdOrHighRiskUserId.value);
          _onLoginStatusChanged();
        }

        return AppResult.success(sessionIdOrHighRiskUserId);
      } else {
        // セッションIDか暗号化Cookieがない場合、エラー扱い
        return const AppResult.failure(AppError());
      }
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<ConsentStatus>> checkConsentStatus() async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      // 利用規定同意状況確認
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/tos/consentStatus/check',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );
      final jsonList = parseJsonList(res.body);
      final agreeTerms = jsonList.isNotEmpty
          ? jsonList.map((item) => item['agreeTerm'] as String).toList()
          : <String>[];
      // NOTE: 今後複数利用規定が必要となった場合配列化が必要
      final agreeTermsUpdateAt = jsonList.isNotEmpty
          ? jsonList.map((item) => item['updatedAt'] as String?).first
          : null;
      if (agreeTermsUpdateAt != null) {
        await _mdManager.agreeTermsUpdatedAt.save(agreeTermsUpdateAt);
      }
      Log.d('利用規定同意状況確認API - 利用規定同意状況 : $agreeTerms');
      if (agreeTerms.isEmpty || agreeTerms.length != agreeTermsNumber) {
        // 利用規定同意が必要な場合
        return AppResult.success(
          ConsentStatus.required(
            agreeTerms: agreeTerms,
          ),
        );
      } else {
        // 利用規定同意が不要の場合
        return const AppResult.success(
          ConsentStatus.notRequired(),
        );
      }
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<void>> registerConsentStatus(
    List<String> agreeTerms,
  ) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      for (final agreeTerm in agreeTerms) {
        final body = jsonEncode({
          'agreeTerm': agreeTerm,
          'channel': '02',
        });
        await _api.postJsonData(
          endpoint: 'api/$_apiVersion/tos/consentStatus/register',
          headers: {
            'Authorization': 'Bearer $sessionId',
          },
          body: body,
        );
      }
      return const AppResult.success(null);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<AuthorizationStatus>> checkAuthorization() async {
    try {
      // 端末に保持されている認可エラーを削除
      await clearAuthError();
      final sessionId = await _mdManager.sessionId.load();
      // 認可要否確認
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/authorization/check',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );
      final json = parseJsonMap(res.body);
      // HACK 現状authorizationStatusと同時に企業コードを取得してしまっている為
      // それぞれ別APIにて実行し、authorizationStatusの正常系コードないにエラー情報を持たない形にリファクタリング
      final authorizationStatus = AuthorizationStatus.fromJson(json);
      // HACK _authError廃止する際に削除
      // ホーム画面に企業コードエラーを伝播させる際に使用
      if (!authorizationStatus.hasKigyoCd) {
        _authError = AppError(
          code: authorizationStatus.errorCode,
          message: authorizationStatus.errorMessage,
          hasFaq: authorizationStatus.hasFaq ?? false,
        );
      }
      return AppResult.success(authorizationStatus);
    } catch (e) {
      _authError = e.toAppError();
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<String>> createAuthScreenInfo() async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      // 認可画面呼び出し情報生成
      final sessionRes = await _api.post(
        endpoint: 'api/$_apiVersion/authScreenInfo/create',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        body: {},
      );
      final fromEbizParam = parseJsonMap(sessionRes.body)['fromEbizParam'];
      if (fromEbizParam is String && fromEbizParam.isNotEmpty) {
        Log.d('認可画面呼び出し情報生成API - リクエストパラメータ: $fromEbizParam');
        return AppResult.success(fromEbizParam);
      } else {
        // リクエストパラメータがない場合、エラー扱い
        return AppResult.failure(defaultError);
      }
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<void>> getToken(
    String code,
    String state,
  ) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      await _api.post(
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        endpoint: 'api/$_apiVersion/token?code=$code&state=$state',
        body: {},
      );
      // セッション情報を保存
      await _mdManager.session.save(
        const Session.identified(
          permissionStatus: SessionPermissionStatus.permitted,
        ),
      );
      return const AppResult.success(null);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  // 認可エラーのゲッター
  @override
  AppError? getAuthError() {
    return _authError;
  }

  // 認可エラーを削除するメソッド
  @override
  Future<void> clearAuthError() async {
    _authError = null;
  }

  @override
  Future<AppResult<void>> logout() async {
    final sessionId = await _mdManager.sessionId.load();
    try {
      await deleteVolatileInfo();
      // Gets権限をリセット
      _getsAuthStatus = '';

      // 他行口座連携の有無と他社クレジットカード連携の有無をリセット
      _bankAccountRepository
          .resetOtherBanksLinkageAndOtherCompanyCardsLinkage();

      // ログイン中フラグを更新する
      _onLoginStatusChanged();

      // ValueDoor認証APIのI/Fに沿ったリクエスト項目であるため、
      // I/Fが更新された場合はリクエスト項目（ハードコーディングの値）も同様に更新する。
      final body = {
        'UkeID': _apNextScreenId,
        'APNextScrID': 'AVV99001',
        '_W_WebRtn': '1',
        '_W_OthWin': '0',
        '_W_CurSessionID': _vdCurrentSessionId,
        'Tci': '1',
        'App_Ver': '',
      };
      final headers = {'Cookie': _currentVdCookie};

      /// VDログアウト
      // 処理の結果に応じて動きが変動することはないため、処理を待たない
      unawaited(
        _valueDoorApi.postUrlEncodeData(
          endpoint: 'avve9h901/logoutdtpsp',
          headers: headers,
          body: body,
        ),
      );

      /// セッション削除
      // 処理の結果に応じて動きが変動することはないため、処理を待たない
      unawaited(
        _api.delete(
          endpoint: 'api/$_apiVersion/session',
          headers: {
            'Authorization': 'Bearer $sessionId',
          },
        ),
      );

      return const AppResult.success(null);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<bool> getUsingBiometrics() async {
    return _secureStorageManager.isUsingBiometrics.load();
  }

  @override
  Future<bool> getFirstLoginWithIDPW() {
    return _secureStorageManager.isFirstLoginWithIDPW.load();
  }

  @override
  Future<bool> getDtpLoginFlag() {
    return _secureStorageManager.isDtpIdLogin.load();
  }

  @override
  Future<String> getLoginId() async {
    return _secureStorageManager.loginId.load();
  }

  @override
  Future<String> getPassword() async {
    return _secureStorageManager.loginPassword.load();
  }

  @override
  Future<void> setBiometricsLoginInfo({
    required String id,
    required String password,
    required bool isUsingBiometrics,
    required bool isFirstLoginWithIDPW,
    required bool isDtpIdLogin,
  }) async {
    await _secureStorageManager.loginId.save(id);
    await _secureStorageManager.loginPassword.save(password);
    await _secureStorageManager.isUsingBiometrics.save(isUsingBiometrics);
    await _secureStorageManager.isFirstLoginWithIDPW.save(isFirstLoginWithIDPW);
    await _secureStorageManager.isDtpIdLogin.save(isDtpIdLogin);
  }

  @override
  Future<AppResult<VdidLoginResponse>> login(
    String vdId,
    String password,
    String caulisSessionId,
  ) async {
    // キャッシュをクリア
    _currentVdCookie = '';
    _apNextScreenId = '';

    // ValueDoor認証APIのI/Fに沿ったリクエスト項目であるため、
    // I/Fが更新された場合はリクエスト項目（ハードコーディングの値）も同様に更新する。
    // Map形式のデータをURLエンコード形式に変換する
    final body = {
      'UkeID': 'AVVE9H901',
      'APNextScrID': 'AVV999002',
      '_W_WebRtn': '0',
      '_W_OthWin': '0',
      'User': vdId,
      'Pwd1': password,
      'NinsyoKbn': '01',
      'Tci': '1',
      'App_Ver': '',
    };
    try {
      // ValueDoor認証
      final response = await _valueDoorApi.postUrlEncodeData(
        endpoint: 'authentication/ex66w06',
        body: body,
      );
      final json = parseJsonMap(response.body);
      final vdidLoginResponse = VdidLoginResponse.fromJson(json);

      // キャッシュに保存
      _currentVdCookie = response.extractCookie();
      _vdCurrentSessionId = vdidLoginResponse.currentSessionId ?? '';
      _apNextScreenId = vdidLoginResponse.apNextScreenId ?? '';
      Log.d('ValueDoor認証APIレスポンス : $vdidLoginResponse');

      // FIXME VdidLoginResponseモデルのcheckApplicationOrSystemError()と中身を合わせる（要リファクタ検討）
      if (vdidLoginResponse.hasApplicationError) {
        // 業務エラーがあった場合
        throw VdidLoginApplicationException(
          message: vdidLoginResponse.errorTarget,
          code: vdidLoginResponse.errorMessageId,
          pageHandleType: const PageHandleType.backToPreviousPage(),
        );
      }
      //  FIXME VdidLoginResponseモデルのcheckApplicationOrSystemError()と中身を合わせる（要リファクタ検討）
      if (vdidLoginResponse.hasSystemError) {
        // システムエラーがあった場合
        if (vdidLoginResponse.containsErrorMessageId()) {
          await loginSuspiciousDetection(
            vdId,
            caulisSessionId,
            vdidLoginResponse.errorMessageId,
          );
        }
        throw VdidLoginSystemException(
          message: vdidLoginResponse.errorTarget,
          code: vdidLoginResponse.errorMessageId,
        );
      }

      return AppResult.success(vdidLoginResponse);
    } catch (error) {
      final isApplicationException =
          error.toAppError().reason is VdidLoginApplicationException;
      final isSystemException =
          error.toAppError().reason is VdidLoginSystemException;

      // 業務エラー時は怪しさ判定メソッドを呼び出さない
      // システムエラー時は怪しさ判定メソッドを2回実行しない
      if (!isApplicationException && !isSystemException) {
        await loginSuspiciousDetection(vdId, caulisSessionId, 'C0026');
      }
      return AppResult.failure(error.toAppError());
    }
  }

  /// ログイン認証の際に用いるリポジトリ
  @override
  Future<AppResult<SessionIdOrHighRiskUserId>> loginDtpId(
    String dtpId,
    String password,
    String caulisSessionId,
  ) async {
    final body = jsonEncode({
      'dtpId': dtpId,
      'password': password,
      'caulisSessionId': caulisSessionId,
    });

    try {
      final response = await _api.postJsonData(
        endpoint: 'api/$_apiVersion/loginDtpId',
        body: body,
      );
      final json = parseJsonMap(response.body);
      Log.d(json);

      // セッションIDor高リスクユーザーIDを取得
      final sessionIdOrHighRiskUserId = SessionIdOrHighRiskUserId.from(
        sessionId: json['sessionId'],
        highRiskUserId: json['highRiskUserId'],
      );

      final encryptedCookieJson = json['encryptedCookie'];

      if (sessionIdOrHighRiskUserId != null && encryptedCookieJson != null) {
        // 取得した値を格納
        final encryptedCookie = EncryptedCookie(
          ticket: encryptedCookieJson['ticket'] as String,
          domain: encryptedCookieJson['domain'] as String,
          path: encryptedCookieJson['path'] as String,
          secure: encryptedCookieJson['secure'] as String,
          httpOnly: encryptedCookieJson['httponly'] as String,
        );
        Log.d('encryptedCookie : $encryptedCookie');

        // 暗号化Cookieの保存
        await _mdManager.encryptedCookie.save(encryptedCookie);

        // sessionIdの場合のみ、保存してログイン中フラグを更新する
        if (sessionIdOrHighRiskUserId.isSessionId) {
          await _mdManager.sessionId.save(sessionIdOrHighRiskUserId.value);
          _onLoginStatusChanged();
        }

        return AppResult.success(sessionIdOrHighRiskUserId);
      } else {
        // セッションIDと暗号化Cookieがない場合、エラー扱い
        return AppResult.failure(defaultError);
      }
    } catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<void> loginSuspiciousDetection(
    String vdId,
    String caulisSessionId,
    String errorMessageId,
  ) async {
    final body = {
      'vdId': vdId,
      'caulisSessionId': caulisSessionId,
      'errorMessageId': errorMessageId,
    };

    // 怪しさ判定用情報送信API
    await _api.post(
      endpoint: 'api/$_apiVersion/loginSuspiciousDetection',
      body: body,
    );
  }

  @override
  Future<AppResult<void>> checkGetsAuthorization() async {
    try {
      // キャッシュのリセット（Gets権限有無）
      _getsAuthStatus = '';

      final sessionId = await _mdManager.sessionId.load();
      // Gets権限有無確認
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/getsAuthorization/check',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );
      final getsAuthStatus = parseJsonMap(res.body)['getsAuthStatus'] as String;
      Log.d('Gets権限確認API - 権限 : $getsAuthStatus');
      _getsAuthStatus = getsAuthStatus;
      return const AppResult.success(null);
    } catch (e) {
      _authError = e.toAppError();
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  String getGetsAuthStatus() {
    return _getsAuthStatus;
  }

  @override
  Future<AppResult<String>> getWeb21SsoSaml(bool isTransfer) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      // 振込を選択した際は'web21-transfer-top'、承認を選択した際は'web21-approval-top'を遷移先として設定
      final service = isTransfer ? 'web21-transfer-top' : 'web21-approval-top';
      // SSO共通呼び出しAPI
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/sso/$service',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final toWeb21Param = json['toWeb21Param'] as String;
      return AppResult.success(toWeb21Param);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<VdidLoginResponse>> navigateVdSystem(
    String toWeb21Param,
  ) async {
    // Web21SSO用SAML取得APIで取得したSAMLレスポンスをVD傘下システム遷移APIに渡す
    final body = {
      'UkeID': 'AVVE9H901', // IF定義書より固定値で設定
      'to_vd_param': toWeb21Param,
    };

    try {
      // VD傘下システム遷移API
      final response = await _valueDoorApi.postUrlEncodeData(
        endpoint: 'authentication/ex66w07',
        body: body,
      );
      final json = parseJsonMap(response.body);
      final vdidLoginResponse = VdidLoginResponse.fromJson(json);
      // キャッシュに保存
      _vdCurrentSessionId = vdidLoginResponse.currentSessionId ?? '';
      _currentVdCookie = response.extractCookie();
      _apNextScreenId = vdidLoginResponse.apNextScreenId ?? '';
      Log.d('VD傘下システム遷移APIレスポンス : $vdidLoginResponse');
      vdidLoginResponse.checkApplicationOrSystemError();
      return AppResult.success(vdidLoginResponse);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<VdidLoginResponse>> enableOtpLater() async {
    // I/Fが更新された場合はリクエスト項目（ハードコーディングの値）も同様に更新する。
    final body = {
      'UkeID': 'AVVE9H216',
      'APNextScrID': 'AVV999002',
      '_W_WebRtn': '0',
      '_W_OthWin': '0',
      '_W_CurSessionID': _vdCurrentSessionId,
      'OtpPwd': '',
      'Tci': '1',
      'App_Ver': '',
    };

    try {
      // OTP有効化API（スキップ）
      final response = await _valueDoorApi.postUrlEncodeData(
        endpoint: 'avve9h211/laterdtpsp',
        body: body,
        headers: {'Cookie': _currentVdCookie},
      );
      final json = parseJsonMap(response.body);
      final vdidLoginResponse = VdidLoginResponse.fromJson(json);
      // キャッシュに保存
      _vdCurrentSessionId = vdidLoginResponse.currentSessionId ?? '';
      _currentVdCookie = response.extractCookie();
      _apNextScreenId = vdidLoginResponse.apNextScreenId ?? '';
      Log.d('OTP有効化API（スキップ）レスポンス : $vdidLoginResponse');
      vdidLoginResponse.checkApplicationOrSystemError();
      return AppResult.success(vdidLoginResponse);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<void>> getSessionId({
    required String highRiskUserId,
    required String idaasTicket,
  }) async {
    final encryptedCookie = await _mdManager.encryptedCookie.load();
    return encryptedCookie.when(
      (ticket, domain, path, secure, httpOnly) async {
        try {
          final res = await _api.get(
            url:
                '$_baseUrl/api/$_apiVersion/sessionId?ticket=$ticket&idaasTicket=$idaasTicket',
            headers: {
              'Authorization': 'Bearer $highRiskUserId',
            },
          );
          final response = parseJsonMap(res.body);
          final sessionId = response['sessionId'];

          if (sessionId is String && sessionId.isNotEmpty) {
            // セッションIDを保存
            await _mdManager.sessionId.save(sessionId);

            // ログイン中フラグを更新する
            _onLoginStatusChanged();

            return const AppResult.success(null);
          } else {
            // セッションIDがない場合、エラー扱い
            return AppResult.failure(defaultError);
          }
        } on Exception catch (error) {
          return AppResult.failure(error.toAppError());
        }
      },
      empty: () => AppResult.failure(defaultError),
    );
  }

  @override
  Future<String> loadDtpIdCache() {
    return _mdManager.dtpId.load();
  }

  @override
  Future<void> saveDtpIdCache(String dtpId) async {
    await _mdManager.dtpId.save(dtpId);
  }

  @override
  Future<String> loadVdIdCache() {
    return _mdManager.vdId.load();
  }

  @override
  Future<void> saveVdIdCache(String vdId) async {
    await _mdManager.vdId.save(vdId);
  }

  @override
  Future<AppResult<void>> getEncryptedCookie() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/idaas/sso/cookie',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );
      final response = parseJsonMap(res.body);
      final encryptedCookieJson = response['encryptedCookie'];
      final encryptedCookie = EncryptedCookie(
        ticket: encryptedCookieJson['ticket'] as String,
        domain: encryptedCookieJson['domain'] as String,
        path: encryptedCookieJson['path'] as String,
        secure: encryptedCookieJson['secure'] as String,
        httpOnly: encryptedCookieJson['httponly'] as String,
      );
      await _mdManager.encryptedCookie.save(encryptedCookie);
      return AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<void>> registerRefusalFlag() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      await _api.post(
        endpoint: 'api/$_apiVersion/refusalFlag',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        body: {},
      );
      return AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<void>> deleteRefusalFlag() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      await _api.delete(
        endpoint: 'api/$_apiVersion/refusalFlag',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );
      return AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<void> deleteVolatileInfo() async {
    // メモリ上のセッションID・セッション情報・暗号化Cookie・valueDoorId・DTPID・内部ID・Web21契約種別・利用者権限・利用者区分、お手続き番号は最初に削除する（APIの成否に関わらず削除する）
    await _mdManager.sessionId.delete();
    await _mdManager.session.delete();
    await _mdManager.encryptedCookie.delete();
    await _mdManager.valueDoorId.delete();
    await _mdManager.dtpId.delete();
    await _mdManager.vdId.delete();
    await _mdManager.internalId.delete();
    await _mdManager.contractType.delete();
    await _mdManager.userAuths.delete();
    await _mdManager.userTypeForFireBaseAnalytics.delete();
    await _mdManager.referenceNumber.delete();
  }
}

final loginRepositoryProvider = Provider<LoginRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final valueDoorApi = ref.watch(valueDoorApiClientProvider);
  final md = ref.watch(mdManagerProvider);
  final ss = ref.watch(secureStorageManagerProvider);
  final bankAccountRepository = ref.watch(bankAccountRepositoryProvider);

  return LoginRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    api,
    valueDoorApi,
    md,
    ss,
    bankAccountRepository,
    () => ref.invalidate(loginFlagProvider),
  );
});
