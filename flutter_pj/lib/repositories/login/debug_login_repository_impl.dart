import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/authorization_status/authorization_status.dart';
import 'package:dtp_app/models/consent_status/consent_status.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/login_status/login_status.dart';
import 'package:dtp_app/models/session/session.dart';
import 'package:dtp_app/models/session_id_or_high_risk_user_id/session_id_or_high_risk_user_id.dart';
import 'package:dtp_app/models/session_permission_status/session_permission_status.dart';
import 'package:dtp_app/models/vdid_login/vdid_login_response.dart';

class DebugLoginRepositoryImpl implements LoginRepository {
  var _isUsingBiometrics = false;
  var _isFirstLoginWithIDPW = true;
  var _vdId = '';
  var _password = '';
  var _getsAuthStatus = '';
  var fetchLoginStatusCalledNumber = 0;

  @override
  Future<AppResult<LoginStatus>> fetchLoginStatus() async {
    // HACK useStub=trueの際に、ログイン→ログアウトの動作確認ができるよう、綺麗に改修したい
    // ログイン処理でfetchLoginStatusが2回呼ばれるので、下の条件でログイン判定している
    fetchLoginStatusCalledNumber++;
    return AppResult.success(
      fetchLoginStatusCalledNumber >= 2
          ? const LoginStatus.login(
              session: Session.identified(
                permissionStatus: SessionPermissionStatus.permitted,
              ),
            )
          : const LoginStatus.logout(),
    );

    //アクセストークン取得エラー
    // return AppResult.success(
    //   LoginStatus.login(
    //     session: Session.identified(
    //       permissionStatus: SessionPermissionStatus.denied,
    //       accessTokenError: const ApiClientException(
    //         statusCode: 500,
    //         message: '現在データが取得できません。暫く経ってから再度取得お願いいたします。',
    //         code: 'E00000000',
    //         baseDate: '2023-12-04',
    //         baseTime: '13:30',
    //       ).toAppError(),
    //     ),
    //   ),
    // );
  }

  @override
  Future<AppResult<SessionIdOrHighRiskUserId>> verifySamlResponse(
    String samlResponse,
    String caulisSessionId,
  ) async {
    return const AppResult.success(
      SessionIdOrHighRiskUserId.sessionId(''),
    );
  }

  @override
  Future<AppResult<ConsentStatus>> checkConsentStatus() async {
    return const AppResult.success(
      ConsentStatus.required(agreeTerms: []),
    );
  }

  @override
  Future<AppResult<void>> registerConsentStatus(
    List<String> agreeTerms,
  ) async {
    return const AppResult.success(null);
  }

  @override
  Future<AppResult<AuthorizationStatus>> checkAuthorization() async {
    return const AppResult.success(
      AuthorizationStatus(
        authorizationStatus: '02',
        hasNoAuth: false,
        hasInquiryAuth: false,
        hasTransferAuth: true,
        hasApprovalAuth: true,
        hasKigyoCd: true,
      ),
    );

    // 認可拒否履歴あり
    // return const AppResult.failure(
    //   AppError(
    //     code: '*********',
    //     message: '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。',
    //   ),
    // );
  }

  @override
  Future<AppResult<String>> createAuthScreenInfo() async {
    return const AppResult.success(
      '44GU5Yip55So44GC44KK44GM44Go44GG44GU44GW44GE44G+44GZ44CC',
    );
  }

  @override
  Future<AppResult<void>> getToken(
    String code,
    String state,
  ) async {
    return const AppResult.success(null);
  }

  @override
  Future<bool> getUsingBiometrics() async {
    return _isUsingBiometrics;
  }

  @override
  Future<bool> getFirstLoginWithIDPW() async {
    return _isFirstLoginWithIDPW;
  }

  @override
  Future<String> getLoginId() async {
    return _vdId;
  }

  @override
  Future<String> getPassword() async {
    return _password;
  }

  @override
  Future<void> setBiometricsLoginInfo({
    required String id,
    required String password,
    required bool isUsingBiometrics,
    required bool isFirstLoginWithIDPW,
    required bool isDtpIdLogin,
  }) async {
    _vdId = id;
    _password = password;
    _isUsingBiometrics = isUsingBiometrics;
    _isFirstLoginWithIDPW = isFirstLoginWithIDPW;
  }

  @override
  AppError? getAuthError() {
    return null;
    // 03:認可不可エラー
    // return const AppError(
    //   code:'*********',
    //   message: '口座情報へのアクセスするための権限がありません。口座照会権限設定を見直してください。',
    // );

    // Web21参照権限チェックがNG
    // return const AppError(
    //   code:'*********',
    //   message: 'ログインに必要な権限がありませんでした。権限設定を見直してください。',
    // );

    // 認可拒否履歴あり
    // return const AppError(
    //   code: '*********',
    //   message: '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。',
    // );
  }

  @override
  Future<void> clearAuthError() async {}

  @override
  Future<AppResult<void>> logout() async {
    // ログアウトのタイミングでfetchLoginStatusが呼ばれた回数をクリア
    fetchLoginStatusCalledNumber = 0;
    return const AppResult.success(null);
  }

  @override
  Future<AppResult<VdidLoginResponse>> login(
    String vdId,
    String password,
    String caulisSessionId,
  ) async {
    const vdidLoginResponse = VdidLoginResponse(
      ukeId: 'AVVE9H901',
      gyoumuCode: '00',
      sessionId: '123',
      // 有効期限切れ
      // apNextScreenId: 'AVVE9H025',
      // パスワード変更
      // apNextScreenId: 'AVVE9H003',
      // 各種登録
      // apNextScreenId: 'AVVE9H220',
      // OTP有効化
      // apNextScreenId: 'AVVE9H211',
      // ログイン成功
      apNextScreenId: 'AVV999100',
      outPageId: '0',
      hasErrorMessage: '1',
      errorMessageId: 'C0028',
      errorTarget: 'test',
      currentSessionId: '11111',
      isOtpLateActivationEnable: true,
      user: '54321',
      fromVdParameter:
          'PHNhbWwycDpSZXNwb25zZSB4bWxuczpzYW1sMnA9InVybjpvYXNpczpuYW1lczp0YzpTQU1MOjIuMDpwcm90b2NvbCIgRGVzdGluYXRpb249Imh0dHBzOi8vZHRwLXNtYmMuY28uanAvYXBpL2xvZ2luIiBJRD0iVlZBYUtJb1R3c2wwdXpyZiIgSW5SZXNwb25zZVRvPSJWTGhoTEIzNGNBdFhpUjBpIiBJc3N1ZUluc3RhbnQ9IjIwMjQtMDQtMjNUMDE6NDI6MzguNjIzWiIgVmVyc2lvbj0iMi4wIj48c2FtbDI6SXNzdWVyIHhtbG5zOnNhbWwyPSJ1cm46b2FzaXM6bmFtZXM6dGM6U0FNTDoyLjA6YXNzZXJ0aW9uIj5odHRwczovL3ZhbHVlZG9vci5zbWJjLmNvLmpwL29hdXRoMjwvc2FtbDI6SXNzdWVyPjxzYW1sMnA6U3RhdHVzPjxzYW1sMnA6U3RhdHVzQ29kZSBWYWx1ZT0idXJuOm9hc2lzOm5hbWVzOnRjOlNBTUw6Mi4wOnN0YXR1czpTdWNjZXNzIi8',
      otpSerialNo: '1234567890',
      otpKind: VdOtpKind.app,
      otpActivateKigen: '20240404',
      otpHuchakueKbn: VdOtpDeliveryType.received,
      otpHuchakueRsn: 'OtpHuchakueRsn',
    );
    return const AppResult.success(vdidLoginResponse);
  }

  @override
  Future<AppResult<SessionIdOrHighRiskUserId>> loginDtpId(
    String id,
    String password,
    String caulisSessionId,
  ) async {
    return const AppResult.success(SessionIdOrHighRiskUserId.sessionId(''));
    // return const AppResult.success(
    //   SessionIdOrHighRiskUserId.highRiskUserId('12345678'),
    // );

    // const appError = AppError(code: '1234', message: '認証エラー');
    // return const AppResult.failure(appError);
  }

  @override
  Future<void> loginSuspiciousDetection(
    String vdId,
    String caulisSessionId,
    String errorMessageId,
  ) async {}

  void setLoginStatus(bool isLoggedIn) {
    isLoggedIn
        ? fetchLoginStatusCalledNumber = 2
        : fetchLoginStatusCalledNumber = 0;
  }

  @override
  Future<AppResult<void>> checkGetsAuthorization() async {
    _getsAuthStatus = '1';
    return const AppResult.success(null);
  }

  @override
  String getGetsAuthStatus() {
    return _getsAuthStatus;
  }

  @override
  Future<AppResult<String>> getWeb21SsoSaml(bool isTransfer) async {
    return const AppResult.success('12345');

    // const appError = AppError(code: '1234', message: '認証エラー');
    // return const AppResult.failure(appError);
  }

  @override
  Future<AppResult<VdidLoginResponse>> navigateVdSystem(
    String toWeb21Param,
  ) async {
    const vdidLoginResponse = VdidLoginResponse(
      ukeId: 'AVVE9H901',
      gyoumuCode: '00',
      sessionId: '123',
      // 有効期限切れ
      // apNextScreenId: 'AVVE9H025',
      // パスワード変更
      // apNextScreenId: 'AVVE9H003',
      // 各種登録
      // apNextScreenId: 'AVVE9H220',
      // OTP有効化
      // apNextScreenId: 'AVVE9H211',
      // ログイン成功
      apNextScreenId: 'AVV999100',
      outPageId: '0',
      hasErrorMessage: '1',
      errorMessageId: 'C0028',
      errorTarget: 'test',
      currentSessionId: '11111',
      isOtpLateActivationEnable: false,
      user: '54321',
      redirectUrl:
          'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/transfer_info_input_dummy.html',
      fromVdParameter:
          'PHNhbWwycDpSZXNwb25zZSB4bWxuczpzYW1sMnA9InVybjpvYXNpczpuYW1lczp0YzpTQU1MOjIuMDpwcm90b2NvbCIgRGVzdGluYXRpb249Imh0dHBzOi8vZHRwLXNtYmMuY28uanAvYXBpL2xvZ2luIiBJRD0iVlZBYUtJb1R3c2wwdXpyZiIgSW5SZXNwb25zZVRvPSJWTGhoTEIzNGNBdFhpUjBpIiBJc3N1ZUluc3RhbnQ9IjIwMjQtMDQtMjNUMDE6NDI6MzguNjIzWiIgVmVyc2lvbj0iMi4wIj48c2FtbDI6SXNzdWVyIHhtbG5zOnNhbWwyPSJ1cm46b2FzaXM6bmFtZXM6dGM6U0FNTDoyLjA6YXNzZXJ0aW9uIj5odHRwczovL3ZhbHVlZG9vci5zbWJjLmNvLmpwL29hdXRoMjwvc2FtbDI6SXNzdWVyPjxzYW1sMnA6U3RhdHVzPjxzYW1sMnA6U3RhdHVzQ29kZSBWYWx1ZT0idXJuOm9hc2lzOm5hbWVzOnRjOlNBTUw6Mi4wOnN0YXR1czpTdWNjZXNzIi8',
      otpSerialNo: '1234567890',
      otpActivateKigen: '20240404',
      otpKind: VdOtpKind.app,
      otpHuchakueKbn: VdOtpDeliveryType.nonDelivery,
      otpHuchakueRsn: 'OtpHuchakueRsn',
    );
    return const AppResult.success(vdidLoginResponse);

    // const appError = AppError(code: '1234', message: '認証エラー');
    // return const AppResult.failure(appError);
  }

  @override
  Future<AppResult<VdidLoginResponse>> enableOtpLater() async {
    const vdidLoginResponse = VdidLoginResponse(
      ukeId: 'AVVE9H901',
      gyoumuCode: '00',
      sessionId: '123',
      apNextScreenId: 'AVV999100',
      outPageId: '0',
      hasErrorMessage: '0',
      errorMessageId: 'C0028',
      errorTarget: 'test',
      currentSessionId: '11111',
      user: '54321',
      redirectUrl:
          'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/transfer_info_input_dummy.html',
      fromVdParameter:
          'PHNhbWwycDpSZXNwb25zZSB4bWxuczpzYW1sMnA9InVybjpvYXNpczpuYW1lczp0YzpTQU1MOjIuMDpwcm90b2NvbCIgRGVzdGluYXRpb249Imh0dHBzOi8vZHRwLXNtYmMuY28uanAvYXBpL2xvZ2luIiBJRD0iVlZBYUtJb1R3c2wwdXpyZiIgSW5SZXNwb25zZVRvPSJWTGhoTEIzNGNBdFhpUjBpIiBJc3N1ZUluc3RhbnQ9IjIwMjQtMDQtMjNUMDE6NDI6MzguNjIzWiIgVmVyc2lvbj0iMi4wIj48c2FtbDI6SXNzdWVyIHhtbG5zOnNhbWwyPSJ1cm46b2FzaXM6bmFtZXM6dGM6U0FNTDoyLjA6YXNzZXJ0aW9uIj5odHRwczovL3ZhbHVlZG9vci5zbWJjLmNvLmpwL29hdXRoMjwvc2FtbDI6SXNzdWVyPjxzYW1sMnA6U3RhdHVzPjxzYW1sMnA6U3RhdHVzQ29kZSBWYWx1ZT0idXJuOm9hc2lzOm5hbWVzOnRjOlNBTUw6Mi4wOnN0YXR1czpTdWNjZXNzIi8',
    );
    return const AppResult.success(vdidLoginResponse);

    // const appError = AppError(code: '1234', message: '認証エラー');
    // return const AppResult.failure(appError);
  }

  @override
  Future<AppResult<void>> getSessionId({
    required String highRiskUserId,
    required String idaasTicket,
  }) {
    return Future.value(const AppResult.success(null));
  }

  @override
  Future<bool> getDtpLoginFlag() {
    return Future.value(true);
  }

  @override
  Future<String> loadDtpIdCache() async {
    return '';
  }

  @override
  Future<void> saveDtpIdCache(String dtpId) async {}

  @override
  Future<String> loadVdIdCache() async {
    return '';
  }

  @override
  Future<void> saveVdIdCache(String vdId) async {}

  @override
  Future<AppResult<EncryptedCookie>> getEncryptedCookie() {
    return Future.value(
      AppResult.success(
        EncryptedCookie(
          ticket: 'ticket',
          domain: 'domain',
          path: 'path',
          secure: 'true',
          httpOnly: 'true',
        ),
      ),
    );
  }

  @override
  Future<AppResult<void>> registerRefusalFlag() async {
    return Future.value(
      AppResult.success(null),
    );
  }

  @override
  Future<AppResult<void>> deleteRefusalFlag() async {
    return Future.value(
      AppResult.success(null),
    );
  }

  @override
  Future<void> deleteVolatileInfo() async {}
}
