import 'package:dtp_app/business_logics/interfaces/fraud_alert_sdk_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

class DebugFraudAlertSDKImpl extends FraudAlertSDKRepository {
  @override
  Future<AppResult<String>> initialize() async {
    return const AppResult.success('成功');
  }

  @override
  Future<AppResult<String>> sendData() async {
    return const AppResult.success('成功');
  }

  @override
  Future<AppResult<String>> getSessionId() async {
    return const AppResult.success('成功');
  }
}
