import 'package:dtp_app/business_logics/interfaces/fraud_alert_sdk_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FraudAlertSDKImpl extends FraudAlertSDKRepository {
  final MethodChannel _fraudAlertSdkMethodChannel;

  FraudAlertSDKImpl(this._fraudAlertSdkMethodChannel);

  // 共通エラー
  final defaultError = AppError(
    message: ErrorInfo.defaultErrorMessage,
    code: ErrorInfo.defaultErrorCode,
  );

  @override
  Future<AppResult<String>> initialize() async {
    try {
      final result = await _fraudAlertSdkMethodChannel
          .invokeMethod('initializeWith') as String;
      Log.i('fraudAlertSDK初期化: $result');
      return AppResult.success(result);
    } on Exception catch (error) {
      Log.e('fraudAlertSDK initialize error = $error');
      return AppResult.failure(defaultError);
    }
  }

  @override
  Future<AppResult<String>> sendData() async {
    try {
      final result = await _fraudAlertSdkMethodChannel.invokeMethod(
        'sendData',
      ) as String;
      Log.i('fraudAlertSDK端末情報送信: $result');
      return AppResult.success(result);
    } on Exception catch (error) {
      Log.e('fraudAlertSDK sendData error = $error');
      return AppResult.failure(defaultError);
    }
  }

  @override
  Future<AppResult<String>> getSessionId() async {
    try {
      final result = await _fraudAlertSdkMethodChannel.invokeMethod(
        'getSessionId',
      ) as String;
      Log.i('fraudAlertSDK SessionId取得処理: $result');
      return AppResult.success(result);
    } on Exception catch (error) {
      Log.e('fraudAlertSDK getSessionId error = $error');
      return AppResult.failure(defaultError);
    }
  }
}

final fraudAlertSDKRepositoryProvider =
    Provider<FraudAlertSDKRepository>((ref) {
  const fraudAlertSdkMethodChannel = MethodChannel('fraud_alert');
  return FraudAlertSDKImpl(fraudAlertSdkMethodChannel);
});
