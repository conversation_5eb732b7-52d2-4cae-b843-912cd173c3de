import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/announcement_info.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// 広報Webからお知らせ情報を取得する
class AnnouncementInfoRepositoryImpl implements AnnouncementInfoRepository {
  AnnouncementInfoRepositoryImpl(
    this._url,
    this._api,
  );

  final String _url;
  final ApiClient _api;

  @override
  Future<AppResult<String?>> getAnnouncementInfo() async {
    try {
      final res = await _api.get(url: _url);
      final json = parseJsonMap(utf8.decode(res.body.runes.toList()));
      final content = json['content'];

      if (content is String && content.isNotEmpty) {
        return AppResult.success(content);
      }

      return const AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final announcementInfoRepositoryProvider =
    Provider<AnnouncementInfoRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);

  return AnnouncementInfoRepositoryImpl(bc.announceJsonUrl, api);
});
