import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/business_blockage_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BusinessBlockageRepositoryImpl implements BusinessBlockageRepository {
  BusinessBlockageRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  @override
  Future<AppResult<String>> getBlockageStatus({
    required String functionId,
  }) async {
    try {
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/work/blockage?functionId=$functionId',
      );
      final json = parseJsonMap(res.body);

      final blockageStatus = json['status'] as String;
      Log.d('閉塞状態(0:利用可能、1:利用不可): $blockageStatus');

      return AppResult.success(blockageStatus);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final businessBlockageRepositoryProvider =
    Provider<BusinessBlockageRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);

  return BusinessBlockageRepositoryImpl(bc.baseUrl, bc.apiVersion, api);
});
