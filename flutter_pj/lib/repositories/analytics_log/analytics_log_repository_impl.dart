import 'dart:async';

import 'package:dtp_app/business_logics/interfaces/analytics_log_repository.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_repository.dart';
import 'package:dtp_app/business_logics/interfaces/login_repository.dart';
import 'package:dtp_app/datas/analytics_manager.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/extension/string_encryption.dart';
import 'package:dtp_app/repositories/bank_account/bank_account_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FirebaseAnalyticsLogRepositoryImpl
    implements FirebaseAnalyticsLogRepository {
  FirebaseAnalyticsLogRepositoryImpl(
    this._loginRepository,
    this._bankAccountRepository,
    this._analyticsManager,
    this._memoryDataManager,
  );

  final LoginRepository _loginRepository;
  final BankAccountRepository _bankAccountRepository;
  final AnalyticsManager _analyticsManager;
  final MemoryDataManager _memoryDataManager;

  @override
  Future<void> sendScreenFirebaseLog({
    required String screenNumber,
    required String screenName,
  }) async {
    final vdId = await _memoryDataManager.valueDoorId.load();
    final dtpId = await _memoryDataManager.dtpId.load();
    final internalId = await _memoryDataManager.internalId.load();
    final contractType = await _memoryDataManager.contractType.load();
    final userAuths = await _memoryDataManager.userAuths.load();
    final userType =
        await _memoryDataManager.userTypeForFireBaseAnalytics.load();
    final referenceNumber = await _memoryDataManager.referenceNumber.load();

    /// VDID、内部ID、DTPID、お手続き番号はsha256でハッシュ化
    final hashedVdId = vdId.convertToSHA256Hash(36);
    final hashedDtpId = dtpId.convertToSHA256Hash(36);
    final hashedInternalId = internalId.convertToSHA256Hash(36);
    final hashedReferenceNumber = referenceNumber.convertToSHA256Hash(36);

    final getsAuthority = _loginRepository.getGetsAuthStatus();
    final otherBanksLinkage = _bankAccountRepository.getOtherBanksLinkage();
    final otherCompanyCardsLinkage =
        _bankAccountRepository.getOtherCompanyCardsLinkage();

    unawaited(
      _analyticsManager.sendScreenLog(
        screenNumber: screenNumber,
        screenName: screenName,
      ),
    );
    unawaited(
      _analyticsManager.sendUserProperty(
        vdId: hashedVdId,
        getsAuthority: getsAuthority,
        otherBanksLinkage: otherBanksLinkage,
        otherCompanyCardsLinkage: otherCompanyCardsLinkage,
        dtpId: hashedDtpId,
        internalId: hashedInternalId,
        contractType: contractType,
        userAuths: userAuths,
        userType: userType,
        referenceNumber: hashedReferenceNumber,
      ),
    );
  }

  @override
  Future<void> sendButtonFirebaseLog({
    required String buttonName,
    required String screenName,
  }) async {
    final vdId = await _memoryDataManager.valueDoorId.load();
    final dtpId = await _memoryDataManager.dtpId.load();
    final internalId = await _memoryDataManager.internalId.load();
    final contractType = await _memoryDataManager.contractType.load();
    final userAuths = await _memoryDataManager.userAuths.load();
    final userType =
        await _memoryDataManager.userTypeForFireBaseAnalytics.load();
    final referenceNumber = await _memoryDataManager.referenceNumber.load();

    /// VDID、内部ID、DTPID、お手続き番号はsha256でハッシュ化
    final hashedVdId = vdId.convertToSHA256Hash(36);
    final hashedDtpId = dtpId.convertToSHA256Hash(36);
    final hashedInternalId = internalId.convertToSHA256Hash(36);
    final hashedReferenceNumber = referenceNumber.convertToSHA256Hash(36);

    final getsAuthority = _loginRepository.getGetsAuthStatus();
    final otherBanksLinkage = _bankAccountRepository.getOtherBanksLinkage();
    final otherCompanyCardsLinkage =
        _bankAccountRepository.getOtherCompanyCardsLinkage();
    unawaited(
      _analyticsManager.sendButtonLog(
        buttonName: buttonName,
        screenName: screenName,
      ),
    );
    unawaited(
      _analyticsManager.sendUserProperty(
        vdId: hashedVdId,
        getsAuthority: getsAuthority,
        otherBanksLinkage: otherBanksLinkage,
        otherCompanyCardsLinkage: otherCompanyCardsLinkage,
        dtpId: hashedDtpId,
        internalId: hashedInternalId,
        contractType: contractType,
        userAuths: userAuths,
        userType: userType,
        referenceNumber: hashedReferenceNumber,
      ),
    );
  }
}

final analyticsLogRepositoryProvider =
    Provider<FirebaseAnalyticsLogRepository>((ref) {
  final login = ref.watch(loginRepositoryProvider);
  final bankAccount = ref.watch(bankAccountRepositoryProvider);
  final analytics = ref.watch(analyticsManagerProvider);
  final md = ref.watch(mdManagerProvider);

  return FirebaseAnalyticsLogRepositoryImpl(
    login,
    bankAccount,
    analytics,
    md,
  );
});
