import 'package:dtp_app/business_logics/interfaces/bank_account_display_config_repository.dart';
import 'package:dtp_app/models/account_settings/account_settings.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

class DebugBankAccountDisplayConfigRepositoryImpl
    implements BankAccountDisplayConfigRepository {
  @override
  Future<AppResult<void>> save(List<DisplayConfig> configs) async {
    return const AppResult.success(null);
  }

  @override
  Future<AppResult<AccountSettings>> getAccountSettings() async {
    return const AppResult.success(
      AccountSettings(
        accounts: [
          DisplayConfigResponse(
            accountId: '*************',
            isHidden: false,
            accountApiType: 'web21',
            displayName: '顧客管理用1',
          ),
          DisplayConfigResponse(
            accountId: '*************',
            isHidden: false,
            accountApiType: 'web21',
            displayName: '顧客管理用2',
          ),
          DisplayConfigResponse(
            accountId: '*************',
            isHidden: false,
            accountApiType: 'freee',
            displayName: '顧客管理用3',
          ),
        ],
      ),
    );

    // // 口座表示設定取得エラー
    // return const AppResult.failure(
    //   AppError(
    //     code: 'E00000000',
    //     message: '現在データが取得できません。暫く経ってから再度取得お願いいたします。',
    //   ),
    // );
  }
}
