import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_display_config_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/account_settings/account_settings.dart';
import 'package:dtp_app/models/account_settings/display_config/display_config.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class BankAccountDisplayConfigRepositoryImpl
    implements BankAccountDisplayConfigRepository {
  BankAccountDisplayConfigRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._mdManager,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final MemoryDataManager _mdManager;

  @override
  Future<AppResult<void>> save(List<DisplayConfig> configs) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final body = jsonEncode({
        'accounts': configs
            .map(
              (e) => {
                'accountId': e.accountId,
                'displayName': e.accountApiType.isWeb21 ? e.displayName : '',
                'isHidden': e.isHidden,
                'accountApiType': e.accountApiType.name,
              },
            )
            .toList(),
      });

      await _api.putJsonData(
        endpoint: 'api/$_apiVersion/accountSettings',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        body: body,
      );
      return const AppResult.success(null);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<AccountSettings>> getAccountSettings() async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/accountSettings',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final accountSettings = AccountSettings.fromJson(json);
      return AppResult.success(accountSettings);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final bankAccountDisplayConfigRepositoryProvider =
    Provider<BankAccountDisplayConfigRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final md = ref.watch(mdManagerProvider);
  return BankAccountDisplayConfigRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    api,
    md,
  );
});
