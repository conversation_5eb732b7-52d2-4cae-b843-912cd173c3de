import 'package:dtp_app/business_logics/interfaces/non_sdk_method_channel_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

class DebugMethodChannelRepositoryImpl
    implements NonSdkMethodChannelRepository {
  @override
  Future<AppResult<void>> launchIntent({
    required String launchURL,
    required String otp,
    required String userid,
    String? notInputOTP,
    String? encrypt,
  }) async {
    return const AppResult.success(null);

    // const appError = AppError(code: '1234', message: 'エラー');
    // return const AppResult.failure(appError);
  }

  @override
  Future<AppResult<Map<String, String>>> getIntentExtras() async {
    // 時刻OTP認証の場合
    const params = {
      'userid': '1234567890',
      'result': '1',
      'inputTimeOtp': '678910',
    };
    return const AppResult.success(params);

    // getIntentExtrasをエラー返却させたい場合はこちらを利用
    // const appError = AppError(code: '1234', message: 'エラー');
    // return const AppResult.failure(appError);
  }
}
