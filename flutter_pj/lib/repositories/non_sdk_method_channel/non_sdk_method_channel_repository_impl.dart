import 'package:dtp_app/business_logics/interfaces/non_sdk_method_channel_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class NonSdkMethodChannelRepositoryImpl
    implements NonSdkMethodChannelRepository {
  NonSdkMethodChannelRepositoryImpl(this._methodChannel);

  final MethodChannel _methodChannel;

  // 共通エラー
  final defaultError = AppError(
    message: ErrorInfo.defaultErrorMessage,
    code: ErrorInfo.defaultErrorCode,
  );

  /// 外部アプリ起動
  @override
  Future<AppResult<void>> launchIntent({
    required String launchURL,
    required String otp,
    required String userid,
    String? notInputOTP,
    String? encrypt,
  }) async {
    try {
      await _methodChannel.invokeMethod('launchIntent', {
        'url': launchURL,
        'otp': otp,
        'notInputOTP': notInputOTP,
        'userid': userid,
        'encrypt': encrypt,
      });
      return const AppResult.success(null);
    } on Exception catch (error) {
      Log.e('methodChannel launchIntent error = $error');
      return AppResult.failure(defaultError);
    }
  }

  @override
  Future<AppResult<Map<String, String>>> getIntentExtras() async {
    try {
      final params = await _methodChannel.invokeMethod('getIntentExtras');
      Log.i(params);

      // 後続処理（paramMapper）のため、Map<dynamic,dynamic>をMap<String,String>に変換
      if (params is Map) {
        final paramsMap = params.map<String, String>((key, value) {
          final newKey = key.toString();
          final newValue = value?.toString() ?? '';
          return MapEntry(newKey, newValue);
        });
        return AppResult.success(paramsMap);
      }
      // methodChannelからの返却値がMapでない場合は空で返却し、後続のチェック処理でエラーハンドンリングさせる
      return AppResult.success(<String, String>{});
    } on Exception catch (error) {
      Log.e('methodChannel getIntentExtras error = $error');
      return AppResult.failure(defaultError);
    }
  }
}

final methodChannelRepositoryProvider =
    Provider<NonSdkMethodChannelRepository>((ref) {
  const methodChannel = MethodChannel('app_channel_shared_data');
  return NonSdkMethodChannelRepositoryImpl(methodChannel);
});
