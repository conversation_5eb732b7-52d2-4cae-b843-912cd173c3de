import 'dart:convert';
import 'dart:typed_data';

import 'package:crypto/crypto.dart';
import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:encrypt/encrypt.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

abstract class AesSaltRepository {
  Future<AppResult<void>> fetchSaltAndGenerateKey();
}

class AesSaltRepositoryImpl implements AesSaltRepository {
  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final SecureStorageManager _secureStorageManager;

  AesSaltRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._secureStorageManager,
  );

  @override
  Future<AppResult<void>> fetchSaltAndGenerateKey() async {
    try {
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/classified/salt',
      );
      final json = parseJsonMap(res.body);

      final salt = json['mobileEncryptSalt'] as String? ?? '';
      // BFFではSecret値の取得に失敗した場合、空値を返すようになっているため
      // 空値はエラーとみなす
      if (salt.isEmpty) throw Exception('SALT取得エラー');

      // SALT + (ランダム生成) をSHA256でhash化して AESのKEYを作成
      final randomKey = Key.fromLength(32).bytes;
      final keyIntList = utf8.encode(salt) + randomKey;
      final hashedKey = sha256.convert(keyIntList);

      final aesKey = Key(Uint8List.fromList(hashedKey.bytes));
      final aesIv = IV.fromLength(16);
      await _secureStorageManager.aesKey.save(aesKey.base64);
      await _secureStorageManager.aesIv.save(aesIv.base64);

      return AppResult.success(null);
    } on Exception catch (error) {
      //基盤系エラーの場合は握りつぶす
      if (error is SorryScreenException) {
        return AppResult.success(null);
      }
      return AppResult.failure(error.toAppError());
    }
  }
}

final aesSaltRepositoryProvider = Provider<AesSaltRepository>((ref) {
  final buildConfig = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final secureStorageManager = ref.watch(secureStorageManagerProvider);

  return AesSaltRepositoryImpl(
    buildConfig.baseUrl,
    buildConfig.apiVersion,
    api,
    secureStorageManager,
  );
});
