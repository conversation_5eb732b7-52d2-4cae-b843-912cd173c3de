import 'package:dtp_app/repositories/account_transaction_daily_repository/account_transaction_daily_repository_impl.dart';
import 'package:dtp_app/repositories/account_transaction_daily_repository/debug_account_transaction_daily_repository_impl.dart';
import 'package:dtp_app/repositories/account_transaction_history/account_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/account_transaction_history/debug_account_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/announcement_info/announcement_info_repository_impl.dart';
import 'package:dtp_app/repositories/announcement_info/debug_announcement_info_repository_impl.dart';
import 'package:dtp_app/repositories/app_launching/app_launching_repository_impl.dart';
import 'package:dtp_app/repositories/app_launching/debug_app_launching_repository_impl.dart';
import 'package:dtp_app/repositories/bank_account/bank_account_repository_impl.dart';
import 'package:dtp_app/repositories/bank_account/debug_bank_account_repository_impl.dart';
import 'package:dtp_app/repositories/bank_account_display_config/bank_account_display_config_repository_impl.dart';
import 'package:dtp_app/repositories/bank_account_display_config/debug_bank_account_display_config_repository_impl.dart';
import 'package:dtp_app/repositories/business_blockage/business_blockage_repository_impl.dart';
import 'package:dtp_app/repositories/business_blockage/debug_business_blockage_repository_impl.dart';
import 'package:dtp_app/repositories/credit_card_transaction_history/credit_card_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/credit_card_transaction_history/debug_credit_card_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/fraud_alert_sdk/debug_fraud_alert_sdk_impl.dart';
import 'package:dtp_app/repositories/fraud_alert_sdk/fraud_alert_sdk_impl.dart';
import 'package:dtp_app/repositories/freee_transaction_history/debug_freee_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/freee_transaction_history/freee_transaction_history_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/corporate_web/corporate_web_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/corporate_web/debug_corporate_web_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/debug_identity_verification_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/ekyc_url/debug_ekyc_url_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/ekyc_url/ekyc_url_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/identity_verification_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/reference_number/debug_reference_number_repository_impl.dart';
import 'package:dtp_app/repositories/identity_verification/reference_number/reference_number_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_sdk/debug_jpki_sdk_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_sdk/jpki_sdk_repository_impl.dart';
import 'package:dtp_app/repositories/karte_sdk/debug_karte_sdk_repository_impl.dart';
import 'package:dtp_app/repositories/karte_sdk/karte_sdk_repository_impl.dart';
import 'package:dtp_app/repositories/login/debug_login_repository_impl.dart';
import 'package:dtp_app/repositories/login/login_repository_impl.dart';
import 'package:dtp_app/repositories/non_sdk_method_channel/non_sdk_method_channel_repository_impl.dart';
import 'package:dtp_app/repositories/non_sdk_method_channel/debug_method_non_sdk_channel_repository_impl.dart';
import 'package:dtp_app/repositories/payment/debug_web21_payment_repository_impl.dart';
import 'package:dtp_app/repositories/payment/web21_payment_repository_impl.dart';
import 'package:dtp_app/repositories/user/debug_user_repository_impl.dart';
import 'package:dtp_app/repositories/user/user_repository_impl.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

/// デバッグ用リポジトリクラスのオーバーライド
final debugRepositoryProviderOverrides = <Override>{
  appLaunchingRepositoryProvider
      .overrideWithValue(DebugAppLaunchingRepositoryImpl()),
  bankAccountRepositoryProvider
      .overrideWithValue(DebugBankAccountRepositoryImpl()),
  bankAccountDisplayConfigRepositoryProvider
      .overrideWithValue(DebugBankAccountDisplayConfigRepositoryImpl()),
  accountTransactionHistoryRepositoryProvider
      .overrideWithValue(DebugAccountTransactionHistoryRepositoryImpl()),
  loginRepositoryProvider.overrideWithValue(DebugLoginRepositoryImpl()),
  userRepositoryProvider.overrideWithValue(DebugUserRepositoryImpl()),
  accountTransactionDailyRepositoryProvider
      .overrideWithValue(DebugAccountTransactionDailyRepositoryImpl()),
  freeeTransactionHistoryRepositoryProvider
      .overrideWithValue(DebugFreeeTransactionHistoryRepositoryImpl()),
  creditCardTransactionHistoryRepositoryProvider
      .overrideWithValue(DebugCreditCardTransactionHistoryRepositoryImpl()),
  businessBlockageRepositoryProvider
      .overrideWithValue(DebugBusinessBlockageRepository()),
  identityVerificationRepositoryProvider
      .overrideWithValue(DebugIdentityVerificationRepositoryImpl()),
  web21PaymentRepositoryProvider
      .overrideWithValue(DebugWeb21PaymentRepositoryImpl()),
  jpkiSdkRepositoryProvider.overrideWithValue(DebugJpkiSdkRepositoryImpl()),
  ekycUrlRepositoryProvider.overrideWithValue(DebugEkycUrlRepositoryImpl()),
  corporateWebRepositoryProvider
      .overrideWithValue(DebugCorporateWebRepositoryImpl()),
  fraudAlertSDKRepositoryProvider.overrideWithValue(DebugFraudAlertSDKImpl()),
  announcementInfoRepositoryProvider
      .overrideWithValue(DebugAnnouncementInfoRepositoryImpl()),
  identityReferenceNumberRepositoryProvider
      .overrideWithValue(DebugReferenceNumberRepositoryImpl()),
  methodChannelRepositoryProvider
      .overrideWithValue(DebugMethodChannelRepositoryImpl()),
  karteSdkRepositoryProvider.overrideWithValue(DebugKarteSdkImpl()),
};
