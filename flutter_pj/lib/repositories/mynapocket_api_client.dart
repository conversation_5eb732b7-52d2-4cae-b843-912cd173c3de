import 'dart:convert';
import 'dart:io';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

typedef JsonMap = Map<String, dynamic>;
typedef JsonList = List<dynamic>;

JsonMap parseJsonMap(String input) => json.decode(input) as JsonMap;

JsonList parseJsonList(String input) => json.decode(input) as JsonList;

final mynaPocketApiClientProvider = Provider<MynaPocketApiClient>((ref) {
  final bc = ref.watch(buildConfigProvider);
  return MynaPocketApiClientImpl(
    baseUrl: bc.mynaPocketBaseUrl,
    isHttpAllowed: bc.isHttpAllowed == 'YES',
  );
});

abstract class MynaPocketApiClient {
  Future<String> get({
    String? url,
    Map<String, String>? headers,
  });

  Future<String> post({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  });

  Future<String> postJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  });

  Future<String> put({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  });

  Future<String> putJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  });

  Future<String> delete({
    required String endpoint,
    Map<String, String>? headers,
  });
}

class MynaPocketApiClientImpl implements MynaPocketApiClient {
  MynaPocketApiClientImpl({
    required this.baseUrl,
    required this.isHttpAllowed,
  }) {
    final httpClient = HttpClient();
    _client = IOClient(httpClient);
  }
  final String baseUrl;
  final bool isHttpAllowed;
  late http.Client _client;

  @override
  Future<String> get({
    String? url,
    Map<String, String>? headers,
  }) {
    _checkIsHttpsRequest(url ?? '');
    return _safeApiCall(
      () async => _client.get(
        Uri.parse(url ?? baseUrl),
        headers: headers,
      ),
    );
  }

  @override
  Future<String> post({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    return _safeApiCall(
      () async => _client.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
        body: body,
      ),
    );
  }

  @override
  Future<String> postJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    final jsonHeaders = {'Content-Type': 'application/json'};
    if (headers != null) {
      jsonHeaders.addAll(headers);
    }
    return _safeApiCall(
      () async => _client.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: jsonHeaders,
        body: body,
      ),
    );
  }

  @override
  Future<String> put({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    return _safeApiCall(
      () async => _client.put(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
        body: body,
      ),
    );
  }

  @override
  Future<String> putJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    final jsonHeaders = {'Content-Type': 'application/json'};
    if (headers != null) {
      jsonHeaders.addAll(headers);
    }
    return _safeApiCall(
      () async => _client.put(
        Uri.parse('$baseUrl/$endpoint'),
        headers: jsonHeaders,
        body: body,
      ),
    );
  }

  @override
  Future<String> delete({
    required String endpoint,
    Map<String, String>? headers,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    return _safeApiCall(
      () async => _client.delete(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
      ),
    );
  }

  void _printResponse({
    required String url,
    required int statusCode,
    required String body,
    Map<String, String>? responseHeaders,
  }) {
    if (statusCode == 200) {
      Log.i(
        'url:$url\nstatus: $statusCode\nbody:$body\nheaders:$responseHeaders',
      );
    } else {
      Log.e(
        'url:$url\nstatus: $statusCode\nbody:$body\nheaders:$responseHeaders',
      );
    }
  }

  /// http通信制御用メソッド
  /// セキュリティガイドライン3-3対応
  void _checkIsHttpsRequest(String url) {
    // dev環境のみはhttp通信を許容
    if (isHttpAllowed) return;
    // それ以外の場合にはhttps通信のみ許容
    if (!url.startsWith('https://')) {
      Log.e('http通信は許容されていません。');
      throw MynapocketApiClientException(
        statusCode: 400,
        error: ErrorInfo.defaultErrorMessage,
      );
    }
  }

  Future<String> _safeApiCall(Function callback) async {
    try {
      // ignore: avoid_dynamic_calls
      final response = await callback() as http.Response;

      // 返却値のバイトデータを取得
      final List<int> responseBytes = response.bodyBytes;

      // 文字化け対策でUTF-8にデコードする
      final String decodedString = utf8.decode(responseBytes);

      _printResponse(
        url: response.request?.url.toString() ?? '',
        statusCode: response.statusCode,
        body: decodedString,
        responseHeaders: response.headers,
      );
      return _parseResponse(
        statusCode: response.statusCode,
        response: response,
      );
    } on SocketException catch (error) {
      throw Exception('No Internet Connection: $error');
    } on HttpException catch (error) {
      throw Exception('HttpException: $error');
    }
  }

  String _parseResponse({
    required int statusCode,
    required http.Response response,
  }) {
    // 返却値のバイトデータを取得
    final List<int> responseBytes = response.bodyBytes;

    // 文字化け対策でUTF-8にデコードする
    final String decodedString = utf8.decode(responseBytes);

    // MynapocketApiから返却されるエラーのステータスコードをここに定義する
    final apiErrorMessages = {
      400: 'Bad Request',
      401: 'Unauthorized',
      404: 'Not Found',
      405: 'Method Not Allowed',
      409: 'Conflict',
      422: 'Method Not Allowed',
      429: 'Too Many Requests',
      500: 'Internal Server Error',
      503: 'Service Unavailable',
      504: 'Gateway Timeout',
    };

    switch (statusCode) {
      case 200:
      case 201:
        return decodedString;
      default:
        throw apiErrorMessages.containsKey(statusCode)
            ? _generateExceptionFromHttpResponse(
                statusCode: statusCode,
                response: response,
                defaultMessage: apiErrorMessages[statusCode]!,
              )
            : Exception('Http status $statusCode: unknown error');
    }
  }

  /// HTTPレスポンスから例外を生成する
  Exception _generateExceptionFromHttpResponse({
    required int statusCode,
    required http.Response response,
    required String defaultMessage,
  }) {
    try {
      final json = parseJsonMap(response.body);
      final String error = json['error'].toString();
      final String errorDescription = json['error_description'].toString();
      final String httpCode = json['httpCode'].toString();
      final String httpMessage = json['httpMessage'].toString();
      final String moreInformation = json['moreInformation'].toString();

      // response.bodyがjson形式の場合はApiClientExceptionを返す
      // MynapocketのApi毎にエラーレスポンスの形式が異なるため、対象外のものはnullで返却
      return MynapocketApiClientException(
        statusCode: statusCode,
        error: error,
        errorDescription: errorDescription,
        httpCode: httpCode,
        httpMessage: httpMessage,
        moreInformation: moreInformation,
      );
    } catch (error) {
      // デフォルトメッセージを設定したExceptionを返す
      return Exception('$statusCode $defaultMessage');
    }
  }
}

/// MynapocketApiClientException独自例外
class MynapocketApiClientException implements Exception {
  final int statusCode;
  final String error;
  final String? errorDescription;
  final String? httpCode;
  final String? httpMessage;
  final String? moreInformation;

  const MynapocketApiClientException({
    required this.statusCode,
    required this.error,
    this.errorDescription,
    this.httpCode,
    this.httpMessage,
    this.moreInformation,
  });

  @override
  String toString() {
    return 'MynapocketApiClientException: statusCode: $statusCode error: $error errorDescription: $errorDescription httpCode: $httpCode httpMessage :$httpMessage $moreInformation';
  }
}
