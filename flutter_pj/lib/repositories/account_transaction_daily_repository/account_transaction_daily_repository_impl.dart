import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/account_transaction_daily_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/transaction_daily_total/transaction_daily_total.dart';
import 'package:dtp_app/models/transaction_daily_total/transaction_daily_total_cache_key.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/serial_call_api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class AccountTransactionDailyRepositoryImpl
    implements AccountTransactionDailyRepository {
  AccountTransactionDailyRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._mdManager,
  ) : _cache = {};

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final MemoryDataManager _mdManager;
  final Map<TransactionDailyTotalCacheKey, List<TransactionDailyTotal>> _cache;

  String _convertToDateParam(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  DateTime _convertToDateTime(String date) {
    return DateFormat('yyyy-MM-dd').parse(date);
  }

  @override
  Future<AppResult<List<TransactionDailyTotal>>> getWeeklyTotal({
    required String accountId,
    required DateTime endDate,
  }) async {
    final dateTo = _convertToDateParam(endDate);
    final key = TransactionDailyTotalCacheKey(
      accountId: accountId,
      dateTo: dateTo,
    );
    final cache = _cache[key];

    if (cache != null && cache.length == 7) {
      return AppResult.success(cache);
    }

    try {
      final sessionId = await _mdManager.sessionId.load();
      final dateFrom =
          _convertToDateParam(endDate.subtract(const Duration(days: 6)));
      final url =
          '$_baseUrl/api/$_apiVersion/bank/accounts/$accountId/transactions/dailyTotals?from=$dateFrom&to=$dateTo';
      final res = await _api.get(
        url: url,
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final list = (json['transactionsDailyTotals'] as List)
          .map(
            (e) => TransactionDailyTotal(
              date: _convertToDateTime(e['date'] as String),
              totalDeposit: e['totalDeposit'] as int,
              totalWithdrawal: e['totalWithdrawal'] as int,
            ),
          )
          .toList();

      // キャッシュにアカウントIDと取引日（to）をキーに情報を保存
      _cache[key] = list;

      return AppResult.success(list);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }
}

final accountTransactionDailyRepositoryProvider =
    Provider<AccountTransactionDailyRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  // 同時に呼び出すとエラーになるAPIがあるので、直列呼び出し用クライアントを使用する。
  final serialApi = ref.watch(serialCallApiClientProvider);
  final md = ref.watch(mdManagerProvider);
  return AccountTransactionDailyRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    serialApi,
    md,
  );
});
