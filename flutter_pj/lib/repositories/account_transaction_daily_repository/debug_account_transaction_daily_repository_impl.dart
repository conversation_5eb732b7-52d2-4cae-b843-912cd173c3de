import 'dart:math';

import 'package:dtp_app/business_logics/interfaces/account_transaction_daily_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/transaction_daily_total/transaction_daily_total.dart';

class DebugAccountTransactionDailyRepositoryImpl
    implements AccountTransactionDailyRepository {
  @override
  Future<AppResult<List<TransactionDailyTotal>>> getWeeklyTotal({
    required String accountId,
    required DateTime endDate,
  }) async {
    final days = [
      endDate,
      endDate.subtract(const Duration(days: 1)),
      endDate.subtract(const Duration(days: 2)),
      endDate.subtract(const Duration(days: 3)),
      endDate.subtract(const Duration(days: 4)),
      endDate.subtract(const Duration(days: 5)),
      endDate.subtract(const Duration(days: 6)),
    ].reversed.toList();

    final rand = Random(endDate.millisecondsSinceEpoch);

    return AppResult.success(
      days
          .map(
            (d) => TransactionDailyTotal(
              date: d,
              totalDeposit: rand.nextInt(**********),
              totalWithdrawal: rand.nextInt(**********),
            ),
          )
          .toList(),
    );
  }
}
