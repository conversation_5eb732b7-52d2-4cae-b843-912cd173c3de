import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/web21_payment_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class Web21PaymentRepositoryImpl implements Web21PaymentRepository {
  Web21PaymentRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._mdManager,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final MemoryDataManager _mdManager;

  @override
  Future<AppResult<String>> getDecryptedOtp(String encryptedOtp) async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      final res = await _api.postJsonData(
        endpoint: 'api/$_apiVersion/web21Otp/decrypt',
        headers: {'Authorization': 'Bearer $sessionId'},
        body: jsonEncode({'encryptedOtp': encryptedOtp}),
      );

      final json = parseJsonMap(res.body);
      final otp = json['otp'].toString();
      return AppResult.success(otp);
    } catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<void>> checkEncryptedVdid(String encryptedVdid) async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      await _api.postJsonData(
        endpoint: 'api/$_apiVersion/encryptedVdid/check',
        headers: {'Authorization': 'Bearer $sessionId'},
        body: jsonEncode({'encryptedVdid': encryptedVdid}),
      );

      return const AppResult.success(null);
    } catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<String>> getEncryptedVdId() async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/encryptedVdid/encrypt',
        headers: {'Authorization': 'Bearer $sessionId'},
      );

      final json = parseJsonMap(res.body);
      final encryptedVdId = json['encryptedVdId'].toString();

      return AppResult.success(encryptedVdId);
    } catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final web21PaymentRepositoryProvider = Provider<Web21PaymentRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final md = ref.watch(mdManagerProvider);

  return Web21PaymentRepositoryImpl(bc.baseUrl, bc.apiVersion, api, md);
});
