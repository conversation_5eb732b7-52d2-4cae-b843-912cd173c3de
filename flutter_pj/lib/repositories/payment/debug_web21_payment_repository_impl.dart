import 'package:dtp_app/business_logics/interfaces/web21_payment_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

class DebugWeb21PaymentRepositoryImpl implements Web21PaymentRepository {
  @override
  Future<AppResult<String>> getDecryptedOtp(String encryptedOtp) async {
    return const AppResult.success('12345');
  }

  @override
  Future<AppResult<void>> checkEncryptedVdid(String vdid) async {
    return const AppResult.success(null);
  }

  @override
  Future<AppResult<String>> getEncryptedVdId() async {
    return const AppResult.success('encryptedVdId');
  }
}
