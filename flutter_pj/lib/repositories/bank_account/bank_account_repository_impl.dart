import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/bank_account_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/datas/shared_preferences/shared_preferences_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/freee_links_client_id_response/freee_links_client_id_response.dart';
import 'package:dtp_app/models/freee_signup_params/freee_signup_params.dart';
import 'package:dtp_app/models/freee_transaction_total_response/freee_transaction_total_response.dart';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/serial_call_api_client.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class BankAccountRepositoryImpl implements BankAccountRepository {
  BankAccountRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._serialApi,
    this._spManager,
    this._mdManager,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  // 同時に呼び出すとエラーになるAPIの呼び出し用
  final ApiClient _serialApi;
  final SharedPreferencesManager _spManager;
  final MemoryDataManager _mdManager;

  // freee連携有無のフラグを保存するキャッシュ
  bool isFreeeLinked = false;

  // 他行口座連携有無のフラグを保存するキャッシュ（FirebaseAnalytics送信用）
  String otherBanksLinkage = '';

  // 他社クレジットカード連携有無のフラグを保存するキャッシュ（FirebaseAnalytics送信用）
  String otherCompanyCardsLinkage = '';

  // freee連携確認APIのエラー情報を保存するキャッシュ
  AppError? freeeLinkError;

  @override
  Future<AppResult<BankAccountBalance>> getAccountBalance({
    required String? accountId,
    required String date,
  }) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final dateParam = _convertToDateParam(date);

      final accountIdsParam = accountId;

      final res = await _serialApi.get(
        url:
            '$_baseUrl/api/$_apiVersion/bank/balance/$accountIdsParam?date=$dateParam',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final bankAccountBalance = BankAccountBalance.fromJson(json);
      return AppResult.success(bankAccountBalance);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<bool> getBalanceVisible() async {
    return _spManager.isBalanceVisible.load();
  }

  @override
  Future<bool> setBalanceVisible(bool value) async {
    return _spManager.isBalanceVisible.save(value);
  }

  @override
  bool getIsFreeeLinked() {
    return isFreeeLinked;
  }

  @override
  String getOtherBanksLinkage() {
    return otherBanksLinkage;
  }

  @override
  String getOtherCompanyCardsLinkage() {
    return otherCompanyCardsLinkage;
  }

  @override
  void resetOtherBanksLinkageAndOtherCompanyCardsLinkage() {
    otherBanksLinkage = '';
    otherCompanyCardsLinkage = '';
  }

  @override
  Future<AppResult<FreeeSsoParams>> getFreeeSsoParams() async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/freee/sso',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final freeeSsoParams = FreeeSsoParams.fromJson(json);

      return AppResult.success(freeeSsoParams);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<String>> getUrlToFreeeReLinkPage() async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/freee/relink',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final redirectUrl = json['redirectUrl'] as String;
      Log.d('URL: $redirectUrl');

      return AppResult.success(redirectUrl);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<bool>> getFreeeLinkStatus() async {
    try {
      // キャッシュのリセット（freee連携有無・エラー情報）
      isFreeeLinked = false;
      freeeLinkError = null;

      final sessionId = await _mdManager.sessionId.load();

      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/freee/link',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final isLinked = json['isLinked'] as bool;

      Log.d('freee連携有無フラグ: $isLinked');

      // キャッシュを更新
      isFreeeLinked = isLinked;
      return AppResult.success(isLinked);
    } catch (error) {
      const defaultError = AppError(
        code: ErrorInfo.defaultErrorCode,
        message: ErrorInfo.defaultErrorMessage,
      );
      freeeLinkError = error.toAppError();
      return AppResult.failure(freeeLinkError ?? defaultError);
    }
  }

  @override
  Future<AppResult<FreeeWalletables>> getFreeeAccountBalance(
    InquiryCategory inquiryCategory,
  ) async {
    try {
      // キャッシュのリセット（他社口座連携有無・他社クレジットカード連携有無）
      if (inquiryCategory == InquiryCategory.bankAccount) {
        otherBanksLinkage = '';
      }
      if (inquiryCategory == InquiryCategory.creditCard) {
        otherCompanyCardsLinkage = '';
      }

      final inquiryCategoryString =
          _inquiryCategoryInSnakeCase(inquiryCategory);
      final sessionId = await _mdManager.sessionId.load();
      const withBalance = true;
      const syncStatus = true;

      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/freee/walletables?type=$inquiryCategoryString&with_balance=$withBalance&with_sync_status=$syncStatus',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);

      final freeeWalletables = FreeeWalletables.fromJson(json);
      //　freee口座情報を更新
      final result = freeeWalletables.copyWith(
        // 表示口座数をMax15にする
        walletables: freeeWalletables.walletables.updateIsHiddenFlag(max: 15),
      );
      if (inquiryCategory == InquiryCategory.bankAccount) {
        otherBanksLinkage = freeeWalletables.walletables.isEmpty ? '0' : '1';
      }
      if (inquiryCategory == InquiryCategory.creditCard) {
        otherCompanyCardsLinkage =
            freeeWalletables.walletables.isEmpty ? '0' : '1';
      }
      return AppResult.success(result);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

//freee口座明細一覧取得API（クレジットカード利用合計額取得）
  @override
  Future<AppResult<FreeeTransactionTotalResponse>> getFreeeTotalExpense(
    int walletableId,
  ) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final walletableType =
          _inquiryCategoryInSnakeCase(InquiryCategory.creditCard);
      final res = await _api.get(
        url:
            '$_baseUrl/api/$_apiVersion/freee/transactions/totalExpense?walletable_id=$walletableId&walletable_type=$walletableType',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);

      final freeeTransactionTotalResponse =
          FreeeTransactionTotalResponse.fromJson(json);
      return AppResult.success(freeeTransactionTotalResponse);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<void>> linkToFreee({
    required String code,
    required String stateFromFreee,
  }) async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final body = jsonEncode({
        'code': code,
        'stateFromFreee': stateFromFreee,
      });

      await _api.postJsonData(
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        endpoint: 'api/$_apiVersion/freee/link',
        body: body,
      );
      return const AppResult.success(null);
    } catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }

  @override
  Future<AppResult<FreeeLinksClientIdResponse>>
      checkFreeeLinksByClientId() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/freee/links/check/partnerId',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);

      final freeeLinksClientIdResponse =
          FreeeLinksClientIdResponse.fromJson(json);
      return AppResult.success(freeeLinksClientIdResponse);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  String _convertToDateParam(String date) {
    if (date.isEmpty) {
      return '';
    }
    final dateTime = DateFormat('yyyy/M/d').parse(date);
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  ///照会区分をスネークケースに変換
  String _inquiryCategoryInSnakeCase(
    InquiryCategory inquiryCategoryInCamelCase,
  ) {
    switch (inquiryCategoryInCamelCase) {
      case InquiryCategory.bankAccount:
        return 'bank_account';
      case InquiryCategory.creditCard:
        return 'credit_card';
      case InquiryCategory.wallet:
        return 'wallet';
      default:
        return '';
    }
  }
}

final bankAccountRepositoryProvider = Provider<BankAccountRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final serialApi = ref.watch(serialCallApiClientProvider);
  final sp = ref.watch(spManagerProvider);
  final md = ref.watch(mdManagerProvider);
  return BankAccountRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    api,
    serialApi,
    sp,
    md,
  );
});
