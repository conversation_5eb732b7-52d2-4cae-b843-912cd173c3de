import 'package:dtp_app/business_logics/interfaces/bank_account_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/bank_account_balance/bank_account_balance.dart';
import 'package:dtp_app/models/freee_links_client_id_response/freee_links_client_id_response.dart';
import 'package:dtp_app/models/freee_signup_params/freee_signup_params.dart';
import 'package:dtp_app/models/freee_transaction_total_response/freee_transaction_total_response.dart';
import 'package:dtp_app/models/freee_walletables/freee_walletables.dart';

class DebugBankAccountRepositoryImpl implements BankAccountRepository {
  var isBalanceVisible = true;
  var isFreeeLinked = true;
  var otherBanksLinkage = '1';
  var otherCompanyCardsLinkage = '1';

  @override
  Future<bool> getBalanceVisible() async {
    return isBalanceVisible;
  }

  @override
  Future<bool> setBalanceVisible(bool value) async {
    isBalanceVisible = value;
    return true;
  }

  @override
  bool getIsFreeeLinked() {
    return isFreeeLinked;
  }

  @override
  String getOtherBanksLinkage() {
    return otherBanksLinkage;
  }

  @override
  String getOtherCompanyCardsLinkage() {
    return otherCompanyCardsLinkage;
  }

  @override
  void resetOtherBanksLinkageAndOtherCompanyCardsLinkage() {
    otherBanksLinkage = '';
    otherCompanyCardsLinkage = '';
  }

  @override
  Future<AppResult<FreeeSsoParams>> getFreeeSsoParams() async {
    return const AppResult.success(
      FreeeSsoParams(
        state: 'state',
        partnerId: 'partnerId',
        isPartnerIdExists: false,
      ),
    );
  }

  @override
  Future<AppResult<String>> getUrlToFreeeReLinkPage() async {
    return const AppResult.success(
      'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/freee_signup_and_auth_dummy.html?state=021mhugbFEtLzz5CXQSsPfgBavI7MFt6QRH4LgTT7ennwnXo',
    );
  }

  @override
  Future<AppResult<bool>> getFreeeLinkStatus() async {
    return const AppResult.success(true);
  }

  @override
  Future<AppResult<BankAccountBalance>> getAccountBalance({
    required String? accountId,
    required String date,
    bool forceRefresh = false,
  }) async {
    // return const AppResult.failure(AppError(message: 'エラー'));
    return AppResult.success(
      BankAccountBalance(
        count: 2,
        accounts: [
          BankAccountBalanceDetail(
            contactName: 'ｺｳｻﾞﾒｲｷﾞｺｳｻﾞﾒｲｷﾞｺｳｻﾞﾒｲｷﾞｺｳｻﾞﾒｲｷﾞｺｳｻﾞﾒｲｷﾞ',
            accountId: '*************',
            bankName: '三井住友銀行',
            branchName: '支店名',
            branchCode: '支店コード',
            accountType: '貯蓄',
            accountNumber: '*********',
            baseDate: '2001/1/1',
            baseTime: '10:00',
            baseDateTime: '2001-01-01-10:00:00',
            currentBalance: 6789*********12,
            checksIssuedByOtherBanks: *********,
            overdraftLimit: *********,
            withdrawableBalance: ***********,
            displayAccountName: '振込用口座1',
            displayAccountFlag: true,
            displayOrder: 0,
          ),
          BankAccountBalanceDetail(
            contactName: '口座名義人2',
            accountId: '*************',
            bankName: '三井住友銀行',
            branchName: '支店名',
            branchCode: '支店コード',
            accountType: '当座',
            accountNumber: '*********',
            baseDate: '2002/2/2',
            baseTime: '10:00',
            baseDateTime: '2002-02-02-10:00:00',
            currentBalance: 0,
            checksIssuedByOtherBanks: 100,
            overdraftLimit: 200,
            withdrawableBalance: 300,
            displayAccountName: '振込用口座2',
            displayAccountFlag: true,
            displayOrder: 1,
          ),
          BankAccountBalanceDetail(
            contactName: '口座名義人3',
            accountId: '*************',
            bankName: '三井住友銀行',
            branchName: '支店名',
            branchCode: '支店コード',
            accountType: '当座',
            accountNumber: '*********',
            baseDate: '2002/2/2',
            baseTime: '10:00',
            baseDateTime: '2002-02-02-10:00:00',
            currentBalance: 0,
            checksIssuedByOtherBanks: 100,
            overdraftLimit: 200,
            withdrawableBalance: 300,
            displayAccountName: '従業員給与支払用',
            displayAccountFlag: true,
          ),
        ],
        serverDate: '2023/9/1',
      ),
    );
  }

  @override
  Future<AppResult<FreeeWalletables>> getFreeeAccountBalance(
    InquiryCategory type,
  ) async {
    final walletables = <FreeeWalletablesDetail>[];
    for (int i = 1; i <= 16; i++) {
      walletables.add(
        FreeeWalletablesDetail(
          id: i,
          accountName: '他行口座$i',
          inquiryCategory: 'bank_account',
          lastBalance: *************,
          walletableBalance: ***********,
          serverDateTime: '2023/1/1 9:00',
          lastSyncedAt: '2025/1/1 9:00',
          bankName: '他行銀行$i',
          // 1件目のみ非表示、2件目以降は全て表示（表示件数を超えた場合のテスト用）
          isHidden: i == 1 ? true : false,
          // 1件目のみ表示、2件目以降は全て非表示（表示件数が0件の場合のテスト用）
          // isHidden: i == 1 ? false : true,
          displayOrder: i - 1,
          accountApiType: 'freee',
        ),
      );
    }

    // 通常時
    return AppResult.success(
      FreeeWalletables(walletables: walletables),
    );

    // return const AppResult.failure(
    //   AppError(
    //     code: 'I00300001',
    //     message: 'freeeの連携口座のデータが取得できません。しばらくたってから再度取得お願いいたします。[401]',
    //   ),
    // );

    // return const AppResult.success(
    //   FreeeWalletables(
    //     walletables: [
    //       FreeeWalletablesDetail(
    //         id: 1,
    //         accountName: '他行口座１',
    //         inquiryCategory: 'credit_card',
    //         lastBalance: 777,
    //         lastSyncedAt: '2025/1/1 9:00',
    //         walletableBalance: 777,
    //         isHidden: false,
    //         displayOrder: 0,
    //         accountApiType: 'freee',
    //       ),
    //       FreeeWalletablesDetail(
    //         id: 2,
    //         accountName: '他行口座2',
    //         inquiryCategory: 'credit_card',
    //         lastBalance: ********,
    //         lastSyncedAt: '2025/1/1 9:00',
    //         walletableBalance: ********,
    //         isHidden: false,
    //         displayOrder: 0,
    //         accountApiType: 'freee',
    //         syncStatus: '1',
    //       ),
    //     ],
    //   ),
    // );

    // return const AppResult.failure(
    //   AppError(message: 'error1', code: 'E003-00009'),
    // );
  }

  @override
  Future<AppResult<FreeeTransactionTotalResponse>> getFreeeTotalExpense(
    int walletableId,
  ) async {
    return const AppResult.success(
      FreeeTransactionTotalResponse(
        startDate: '2023-08-01',
        endDate: '2023-08-31',
        totalExpense: *********,
        serverDateTime: '2023/12/27 17:30',
      ),
    );

    // const AppResult.failure(
    //   AppError(
    //     code: 'X000-X0000',
    //     message: 'freeeの連携口座のデータが取得できません。しばらく経ってから再度取得お願いいたします。',
    //     baseDateTime: '2023/12/27 17:30',
    //   ),
    // );
  }

  @override
  Future<AppResult<FreeeLinksClientIdResponse>>
      checkFreeeLinksByClientId() async {
    return const AppResult.success(
      FreeeLinksClientIdResponse(exists: true),
    );
  }

  @override
  Future<AppResult<void>> linkToFreee({
    required String code,
    required String stateFromFreee,
  }) async {
    return const AppResult.success(null);
    // return const AppResult.failure(AppError());
  }
}
