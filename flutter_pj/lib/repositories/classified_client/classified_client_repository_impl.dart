import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/classified_client_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/classified_client/classified_client.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class ClassifiedClientRepositoryImpl implements ClassifiedClientRepository {
  ClassifiedClientRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  @override
  Future<AppResult<ClassifiedClient>> getClassifiedClient() async {
    try {
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/classified/client',
      );
      final json = parseJsonMap(res.body);

      final classifiedClient = ClassifiedClient.fromJson(json);

      // BFFではSecret値の取得に失敗した場合、空値を返すようになっているため
      // 空値はエラーとみなす
      if (classifiedClient.clientId.isEmpty) {
        throw Exception('ClientId取得エラー');
      }
      if (classifiedClient.clientSecret.isEmpty) {
        throw Exception('ClientSecret取得エラー');
      }

      return AppResult.success(classifiedClient);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final classifiedClientRepositoryProvider =
    Provider<ClassifiedClientRepository>((ref) {
  final buildConfig = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  return ClassifiedClientRepositoryImpl(
    buildConfig.baseUrl,
    buildConfig.apiVersion,
    api,
  );
});
