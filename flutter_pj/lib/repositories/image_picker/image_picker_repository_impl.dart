import 'package:dtp_app/business_logics/interfaces/image_picker_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

class ImagePickerRepositoryImpl implements ImagePickerRepository {
  ImagePickerRepositoryImpl();

  @override
  Future<AppResult<XFile?>> pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);
      return AppResult.success(image);
    } catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final imagePickerRepositoryProvider = Provider<ImagePickerRepository>((ref) {
  return ImagePickerRepositoryImpl();
});
