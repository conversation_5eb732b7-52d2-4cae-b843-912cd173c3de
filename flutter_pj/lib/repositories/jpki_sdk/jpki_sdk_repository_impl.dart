import 'dart:convert';

import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/datas/sig_cer_entity.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/utils/sdk_parameter.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JpkiSdkRepositoryImpl implements JpkiSdkRepository {
  JpkiSdkRepositoryImpl(
    this._jpkiServiceMethodChannel,
    this._memoryDataManager,
  );

  final MethodChannel _jpkiServiceMethodChannel;
  final MemoryDataManager _memoryDataManager;

  @override
  Future<String> getJpkiPassword() async {
    return _memoryDataManager.jpkiPassword.load();
  }

  @override
  Future<void> setJpkiPassword(String value) async {
    await _memoryDataManager.jpkiPassword.save(value);
  }

  @override
  Future<String> getCertForSig() async {
    return _memoryDataManager.certForSig.load();
  }

  @override
  Future<void> setCertForSig(String value) async {
    await _memoryDataManager.certForSig.save(value);
  }

  @override
  Future<String> getSigDoc() async {
    return _memoryDataManager.sigDoc.load();
  }

  @override
  Future<void> setSigDoc(String value) async {
    await _memoryDataManager.sigDoc.save(value);
  }

  // F_SLI_CMN_01 ICカード通信開始
  @override
  Future<AppResult<String>> startNfcSession(String statusMessage) async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'startNfcSession',
        {
          'statusMessage': statusMessage,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_CMN_02 ICカード通信正常終了
  @override
  Future<AppResult<String>> finishNfcSessionSuccess(
    String statusMessage,
  ) async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'finishNfcSessionSuccess',
        {
          'statusMessage': statusMessage,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_CMN_03 ICカード通信エラー終了
  @override
  Future<AppResult<String>> finishNfcSessionError(String statusMessage) async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'finishNfcSessionError',
        {
          'statusMessage': statusMessage,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_CMN_04 ICカードスキャン文言変更
  @override
  Future<AppResult<String>> setCardScanStatusMessage(String message) async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'setCardScanStatusMessage',
        {
          'message': message,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_CMN_05 カード種別判定
  // @override
  // Future<AppResult<String>> getCardKind() async {
  //   try {
  //     final result = await _jpkiServiceMethodChannel.invokeMethod('getCardKind')
  //     as String;
  //     return AppResult.success(result);
  //   } on Exception catch (e) {
  //     return AppResult.failure(e.toAppError(convert: true));
  //   }
  // }

  // F_SLI_JPK_01 署名用パスワード照合
  @override
  Future<AppResult<String>> verifyPasswordForSign() async {
    try {
      final password = await _memoryDataManager.jpkiPassword.load();
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'verifyPasswordForSign',
        {
          'password': password,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_JPK_02 署名用電子証明書取得
  @override
  Future<AppResult<void>> getCertificateForSign() async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'getCertificateForSign',
      ) as Uint8List;
      // 署名用電子証明書をbase64にエンコードして保存
      await _memoryDataManager.certForSig.save(base64Encode(result));
      return const AppResult.success(null);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_JPK_03 署名書電子署名作成(生データより作成)
  @override
  Future<AppResult<void>> makeSignatureForSign() async {
    try {
      // 暫定的な署名対象ドキュメント
      const data = SdkParameter.data;
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'makeSignatureForSign',
        {
          'data': data,
        },
      ) as Uint8List;
      // 署名済みドキュメントをbase64にエンコードして保存
      await _memoryDataManager.sigDoc.save(base64Encode(result));
      return const AppResult.success(null);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_SLI_JPK_05 署名用パスワード照合残試行可能回数取得
  @override
  Future<AppResult<int>> getRetryCounterForSign() async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'getRetryCounterForSign',
      ) as int;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // ここからAndroidのSDKのメソッドを記載
  // F_JLA_01 NFCリーダーモード設定
  @override
  Future<AppResult<String>> setEnableNfcCardReader(int timeout) async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'setEnableNfcCardReader',
        {
          'timeout': timeout,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_JLA_03 残試行回数取得
  @override
  Future<AppResult<int>> getAttemptRemain(int type) async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'getAttemptRemain',
        {
          'type': type,
        },
      ) as Map<Object?, Object?>;
      final Map<String, dynamic> castedResult = result.cast<String, dynamic>();
      final retryCounter = castedResult['attemptRemainEntity'] as int;
      return AppResult.success(retryCounter);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_JLA_JPK_07 署名用電子証明書AP認証確認
  @override
  Future<AppResult<String>> verifySignatureCertPin() async {
    try {
      final pin = await _memoryDataManager.jpkiPassword.load();
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'verifySignatureCertPin',
        {
          'pin': pin,
        },
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_JLA_JPK_01 署名用電子証明書取得
  @override
  Future<AppResult<void>> getSignatureCert() async {
    try {
      final pin = await _memoryDataManager.jpkiPassword.load();
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'getSignatureCert',
        {
          'pin': pin,
        },
      ) as Map<Object?, Object?>;
      final Map<String, dynamic> castedResult = result.cast<String, dynamic>();
      final sigCert = SigCerEntity.map(castedResult).cert;
      // 署名用電子証明書をbase64にエンコードして保存
      await _memoryDataManager.certForSig.save(base64Encode(sigCert));
      return const AppResult.success(null);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_JLA_JPK_02 署名用電子署名作成
  @override
  Future<AppResult<void>> makeSignatureCertDigitalSignature() async {
    try {
      // 暫定的な署名対象ドキュメント(AndroidはSDKに渡すためbyteへ変換)
      final bytes = Uint8List.fromList(utf8.encode(SdkParameter.data));
      Log.d('methodchannel経由でmakeSignatureCertDigitalSignature呼び出し');
      final pin = await _memoryDataManager.jpkiPassword.load();
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'makeSignatureCertDigitalSignature',
        {
          'pin': pin,
          'ds': bytes,
        },
      ) as Map<Object?, Object?>;
      final Map<String, dynamic> castedResult = result.cast<String, dynamic>();
      await _memoryDataManager.sigDoc
          .save(base64Encode(castedResult['dsResult'] as Uint8List));
      return const AppResult.success(null);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // F_JLA_02 NFC標準モード設定
  @override
  Future<AppResult<String>> setDisableNfcCardReader() async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'setDisableNfcCardReader',
      ) as String;
      return AppResult.success(result);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError(convertPlatformException: true));
    }
  }

  // AndroidのNFC状態取得
  @override
  Future<AppResult<AndroidNfcStatus>> getAndroidNfcStatus() async {
    try {
      final result = await _jpkiServiceMethodChannel.invokeMethod(
        'getAndroidNfcStatus',
      ) as String;

      AndroidNfcStatus status = AndroidNfcStatus.enabled;
      switch (result) {
        case 'enabled':
          status = AndroidNfcStatus.enabled;
          break;
        case 'not_supported':
          status = AndroidNfcStatus.notSupported;
          break;
        case 'disabled':
          status = AndroidNfcStatus.disabled;
          break;
      }

      return AppResult.success(status);
    } on Exception catch (e) {
      return AppResult.failure(e.toAppError());
    }
  }
}

final jpkiSdkRepositoryProvider = Provider<JpkiSdkRepository>((ref) {
  const jpkiServiceMethodChannel = MethodChannel('jpki_service');
  final memoryDataManager = ref.watch(mdManagerProvider);
  return JpkiSdkRepositoryImpl(jpkiServiceMethodChannel, memoryDataManager);
});
