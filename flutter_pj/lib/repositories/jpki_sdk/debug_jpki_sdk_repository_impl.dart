import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

// SDK処理のアプリ内スタブ
class DebugJpkiSdkRepositoryImpl implements JpkiSdkRepository {
  var data = 'aaa';

  @override
  Future<String> getJpkiPassword() async {
    return 'ABC123';
  }

  @override
  Future<void> setJpkiPassword(String value) async {}

  @override
  Future<String> getCertForSig() async {
    return data;
  }

  @override
  Future<void> setCertForSig(String value) async {}

  @override
  Future<String> getSigDoc() async {
    return data;
  }

  @override
  Future<void> setSigDoc(String value) async {}

  // F_SLI_CMN_01 ICカード通信開始
  @override
  Future<AppResult<String>> startNfcSession(String statusMessage) async {
    return const AppResult.success('success');
  }

  // F_SLI_CMN_02 ICカード通信正常終了
  @override
  Future<AppResult<String>> finishNfcSessionSuccess(
    String statusMessage,
  ) async {
    return const AppResult.success('success');
  }

  // F_SLI_CMN_03 ICカード通信エラー終了
  @override
  Future<AppResult<String>> finishNfcSessionError(String statusMessage) async {
    return const AppResult.success('success');
  }

  // F_SLI_CMN_04 ICカードスキャン文言変更
  @override
  Future<AppResult<String>> setCardScanStatusMessage(String message) async {
    // return const AppResult.failure(AppError(code: 'E_21_401'));
    return const AppResult.success('success');
  }

  // F_SLI_CMN_05 カード種別判定
  // @override
  // Future<AppResult<String>> getCardKind() async {
  //     return const AppResult.success('success');
  // }

  // F_SLI_JPK_01 署名用パスワード照合
  @override
  Future<AppResult<String>> verifyPasswordForSign() async {
    return const AppResult.success('success');
    // return const AppResult.failure(AppError(code: 'E_21_403'));
    // return const AppResult.failure(AppError(code: 'E_21_201'));
  }

  // F_SLI_JPK_02 署名用電子証明書取得
  @override
  Future<AppResult<void>> getCertificateForSign() async {
    return const AppResult.success(null);
  }

  // F_SLI_JPK_03 署名書電子署名作成(生データより作成)
  @override
  Future<AppResult<void>> makeSignatureForSign() async {
    return const AppResult.success(null);
  }

  // F_SLI_JPK_05 署名用パスワード照合残試行可能回数取得
  @override
  Future<AppResult<int>> getRetryCounterForSign() async {
    return const AppResult.success(5);
  }

  // ここからAndroidのSDKのメソッドを記載
  // F_JLA_01 NFCリーダーモード設定
  @override
  Future<AppResult<String>> setEnableNfcCardReader(int timeout) async {
    return const AppResult.success('success');
    // return const AppResult.failure(AppError(code: 'M.ERR.F_JLA_JPK_01.3'));
  }

  // F_JLA_03 残試行回数取得
  @override
  Future<AppResult<int>> getAttemptRemain(int type) async {
    return const AppResult.success(5);
  }

  // F_JLA_JPK_07 署名用電子証明書AP認証確認
  @override
  Future<AppResult<String>> verifySignatureCertPin() async {
    return const AppResult.success('success');
  }

  // F_JLA_JPK_01 署名用電子証明書取得
  @override
  Future<AppResult<void>> getSignatureCert() async {
    return const AppResult.success(null);
  }

  // F_JLA_JPK_02 署名用電子署名作成
  @override
  Future<AppResult<void>> makeSignatureCertDigitalSignature() async {
    return const AppResult.success(null);
  }

  // F_JLA_02 NFC標準モード設定
  @override
  Future<AppResult<String>> setDisableNfcCardReader() async {
    return const AppResult.success('success');
  }

  // AndroidのNFC状態取得
  @override
  Future<AppResult<AndroidNfcStatus>> getAndroidNfcStatus() async {
    // NFC有効を返却
    return const AppResult.success(AndroidNfcStatus.enabled);
  }
}
