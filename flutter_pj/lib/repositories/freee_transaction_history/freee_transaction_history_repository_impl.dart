import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/freee_transaction_history_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history_cache_key.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history_list.dart';
import 'package:dtp_app/models/freee_transaction_response/freee_transaction_response.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class FreeeTransactionHistoryRepositoryImpl
    implements FreeeTransactionHistoryRepository {
  FreeeTransactionHistoryRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._mdManager,
  ) : _cache = {};

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final Map<FreeeTransactionHistoryCacheKey, FreeeTransactionHistory> _cache;
  final MemoryDataManager _mdManager;

  /// キャッシュから取得する
  FreeeTransactionHistory? _getFromCache(
    int walletableId,
    DateTime dateFrom,
    DateTime dateTo,
  ) {
    final key = FreeeTransactionHistoryCacheKey(
      walletableId: walletableId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );
    if (_cache.containsKey(key)) {
      return _cache[key];
    } else {
      return null;
    }
  }

  /// APIから取得する
  Future<FreeeTransactionHistory> _getAll(
    int walletableId,
    FreeeTransactionsType walletableType,
    DateTime startDate,
    DateTime endDate,
  ) async {
    final sessionId = await _mdManager.sessionId.load();
    final walletableType1 = _convertToTypeParam(walletableType);
    final startDate1 = _convertToDateParam(startDate);
    final endDate1 = _convertToDateParam(endDate);
    final url =
        '$_baseUrl/api/$_apiVersion/freee/transactions?walletable_id=$walletableId&walletable_type=$walletableType1&start_date=$startDate1&end_date=$endDate1';
    final res = await _api.get(
      url: url,
      headers: {
        'Authorization': 'Bearer $sessionId',
      },
    );
    final json = parseJsonMap(res.body);
    final response = FreeeTransactionResponse.fromJson(json);
    final items = response.transactions
        .map(
          (e) => FreeeTransactionHistoryDetail(
            type: e.entrySide == 'income'
                ? FreeeTransactionHistoryType.income
                : FreeeTransactionHistoryType.expenditure,
            transactionType: _convertToTransactionsType(e.walletableType),
            description: e.description,
            tradingDate: e.date,
            amount: e.amount,
          ),
        )
        .toList();
    final data = FreeeTransactionHistory.data(
      items: items,
      baseDateTime: '${response.baseDate} ${response.baseTime}',
    );

    final key = FreeeTransactionHistoryCacheKey(
      walletableId: walletableId,
      dateFrom: startDate,
      dateTo: endDate,
    );

    // 取得した情報をキャッシュに保存
    _cache[key] = data;

    return data;
  }

  FreeeTransactionsType _convertToTransactionsType(String? transactionType) {
    switch (transactionType) {
      case 'bank_account':
        return FreeeTransactionsType.freeeBankAccount;
      case 'credit_card':
        return FreeeTransactionsType.creditCard;
      case 'wallet':
        return FreeeTransactionsType.wallet;
      default:
        return FreeeTransactionsType.freeeBankAccount;
    }
  }

  /// デフォルトは照会番号（預入番号）降順でソート
  @override
  Future<AppResult<FreeeTransactionHistory>> getFreeeTransactionHistory({
    required int walletableId,
    required FreeeTransactionsType walletableType,
    required DateTime startDateFrom,
    required DateTime startDateTo,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    TransactionHistorySortType sortType = TransactionHistorySortType.date,
    double? minAmount,
    double? maxAmount,
  }) async {
    Future<FreeeTransactionHistory> getData() async {
      final cachedData =
          _getFromCache(walletableId, startDateFrom, startDateTo);
      if (forceRefresh || cachedData == null) {
        // キャッシュがない、または強制再取得の場合、APIから取得する
        return _getAll(
          walletableId,
          walletableType,
          startDateFrom,
          startDateTo,
        );
      } else {
        // キャッシュがある場合、キャッシュから取得する
        return cachedData;
      }
    }

    try {
      final history = await getData();

      return history.map(
        empty: (empty) => AppResult.success(empty),
        data: (data) => AppResult.success(
          _filterAndSort(
            data,
            filterType: filterType,
            order: order,
            startingDateFrom: startDateFrom,
            startingDateTo: startDateTo,
            sortType: sortType,
            tradingDateFrom: tradingDateFrom,
            tradingDateTo: tradingDateTo,
            minAmount: minAmount,
            maxAmount: maxAmount,
          ),
        ),
      );
    } catch (e) {
      Log.e(e);
      return AppResult.failure(e.toAppError());
    }
  }

  String _convertToDateParam(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  String _convertToTypeParam(FreeeTransactionsType walletableType) {
    switch (walletableType) {
      case FreeeTransactionsType.freeeBankAccount:
        return 'bank_account';
      case FreeeTransactionsType.creditCard:
        return 'credit_card';
      case FreeeTransactionsType.wallet:
        return 'wallet';
    }
  }

  FreeeTransactionHistoryData _filterAndSort(
    FreeeTransactionHistoryData history, {
    required TransactionHistoryFilterType filterType,
    required TransactionHistorySortOrder order,
    required TransactionHistorySortType sortType,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    double? minAmount,
    double? maxAmount,
  }) {
    return history.copyWith(
      items: history.items
          .filterByType(filterType)
          .filterByTradingDatePeriod(tradingDateFrom, tradingDateTo)
          .filterByMinAmount(minAmount)
          .filterByMaxAmount(maxAmount)
          .sortItems(sortType, order),
    );
  }
}

final freeeTransactionHistoryRepositoryProvider =
    Provider<FreeeTransactionHistoryRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final md = ref.watch(mdManagerProvider);
  return FreeeTransactionHistoryRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    api,
    md,
  );
});
