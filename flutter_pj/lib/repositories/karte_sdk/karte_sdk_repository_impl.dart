import 'package:dtp_app/business_logics/interfaces/katre_sdk_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:karte_core/karte_core.dart';

class KarteSdkImpl extends KarteSdkRepository {
  // 共通エラー
  final defaultError = AppError(
    message: ErrorInfo.defaultErrorMessage,
    code: ErrorInfo.defaultErrorCode,
  );

  @override
  AppResult<void> sendView({
    required String screenIdNumber,
    required String screenName,
  }) {
    try {
      Tracker.view(screenIdNumber, screenName);
      Log.i(
        'KARTEイベント（view）送信成功: \n'
        'screenIdNumber=$screenIdNumber,screenName=$screenName',
      );

      return AppResult.success(null);
    } on Exception catch (error) {
      Log.e('KARTEイベント（view）送信失敗: $error');
      return AppResult.failure(defaultError);
    }
  }

  @override
  AppResult<void> sendTrack({
    required String buttonName,
    required String screenIdNumber,
  }) {
    try {
      Tracker.track('${buttonName}_$screenIdNumber');
      Log.i(
        'KARTEイベント（track）送信成功: \n'
        'buttonName=$buttonName,screenIdNumber=$screenIdNumber',
      );

      return AppResult.success(null);
    } on Exception catch (error) {
      Log.e('KARTEイベント（track）送信成功: $error');
      return AppResult.failure(defaultError);
    }
  }

  @override
  AppResult<void> sendIdentifyAndAttribute({
    required String userId,
    required Map<String, dynamic> attribute,
  }) {
    try {
      // KARTEの仕様のため、nullまたは空文字値の項目は項目ごと送らない
      attribute.removeWhere((key, value) => value == null || value == '');

      // identifyメソッド実行時はuser_id項目の設定が必須
      Tracker.identifyWithUserId(userId);
      Tracker.attribute(attribute);

      Log.i('KARTEイベント（identify）送信成功: user_id=$userId');
      return AppResult.success(null);
    } on Exception catch (error) {
      Log.e('KARTEイベント（identify）送信失敗: $error');
      return AppResult.failure(defaultError);
    }
  }

  @override
  AppResult<void> sendAttribute({
    required Map<String, dynamic> attribute,
  }) {
    try {
      // KARTEの仕様のため、nullまたは空文字値の項目は項目ごと送らない
      attribute.removeWhere((key, value) => value == null || value == '');

      Tracker.attribute(attribute);

      Log.i('KARTEイベント（attribute）送信成功: attribute=$attribute');
      return AppResult.success(null);
    } on Exception catch (error) {
      Log.e('KARTEイベント（attribute）送信失敗: $error');
      return AppResult.failure(defaultError);
    }
  }
}

final karteSdkRepositoryProvider = Provider<KarteSdkRepository>((ref) {
  return KarteSdkImpl();
});
