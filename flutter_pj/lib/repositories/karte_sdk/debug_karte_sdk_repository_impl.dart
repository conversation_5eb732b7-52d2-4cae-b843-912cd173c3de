import 'package:dtp_app/business_logics/interfaces/katre_sdk_repository.dart';
import 'package:dtp_app/models/app_result/app_result.dart';

class DebugKarteSdkImpl extends KarteSdkRepository {
  @override
  AppResult<void> sendView({
    required String screenIdNumber,
    required String screenName,
  }) {
    return const AppResult.success(null);
  }

  @override
  AppResult<void> sendTrack({
    required String buttonName,
    required String screenIdNumber,
  }) {
    return const AppResult.success(null);
  }

  @override
  AppResult<void> sendIdentifyAndAttribute({
    required String userId,
    required Map<String, dynamic> attribute,
  }) {
    return const AppResult.success(null);
  }

  @override
  AppResult<void> sendAttribute({
    required Map<String, String> attribute,
  }) {
    return const AppResult.success(null);
  }
}
