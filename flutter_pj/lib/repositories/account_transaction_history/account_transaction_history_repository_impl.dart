import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/account_transaction_history_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history_cache_key.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history_list.dart';
import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:dtp_app/models/account_transaction_response/account_transaction_response.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/serial_call_api_client.dart';
import 'package:dtp_app/utils/ext/list_ext.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

class AccountTransactionHistoryRepositoryImpl
    implements AccountTransactionHistoryRepository {
  AccountTransactionHistoryRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._mdManager,
  ) : _cache = {};

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;
  final Map<AccountTransactionHistoryCacheKey, AccountTransactionHistory>
      _cache;
  final MemoryDataManager _mdManager;

  /// キャッシュから取得する
  AccountTransactionHistory? _getFromCache(
    String accountId,
    DateTime dateFrom,
    DateTime dateTo,
  ) {
    final key = AccountTransactionHistoryCacheKey(
      accountId: accountId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    );
    if (_cache.containsKey(key)) {
      return _cache[key];
    } else {
      return null;
    }
  }

  /// APIから取得する
  Future<AccountTransactionHistory> _getAll(
    String accountId,
    DateTime dateFrom,
    DateTime dateTo,
  ) async {
    final sessionId = await _mdManager.sessionId.load();
    final dateFrom1 = _convertToDateParam(dateFrom);
    final dateTo1 = _convertToDateParam(dateTo);
    final url =
        '$_baseUrl/api/$_apiVersion/bank/accounts/$accountId/transactions?dateFrom=$dateFrom1&dateTo=$dateTo1';
    final res = await _api.get(
      url: url,
      headers: {
        'Authorization': 'Bearer $sessionId',
      },
    );
    final json = parseJsonMap(res.body);
    final response = AccountTransactionResponse.fromJson(json);
    final transactions1 = response.transactions1.mapIndexed(
      (e, index) => AccountTransactionHistoryDetail(
        type: e.depositCreditType == '1'
            ? TransactionHistoryType.income
            : TransactionHistoryType.expenditure,
        depositType: DepositType.liquidityDeposit,
        payerName: e.remitterNameContractorNumber,
        startingDate: e.valueDateAd,
        tradingDate: e.transactionDateAd,
        transactionType: TransactionType.from(
          e.transactionType,
          e.transactionTypeName,
        ),
        amount: e.amount,
        referenceNumber: e.inquiryNumber,
        remitterCode: e.remitterCode,
        checksIssuedByOtherBanksAmount: e.checksIssuedByOtherBanksAmount,
        billAndCheckTypeName: e.billAndCheckTypeName,
        billAndCheckNumber: e.billAndCheckNumber,
        ediInfo: e.ediInfo,
        abstract: e.abstract,
      ),
    );
    final transactions2 = response.transactions2.mapIndexed(
      (e, index) => AccountTransactionHistoryDetail(
        type: e.depositCreditType == '1'
            ? TransactionHistoryType.income
            : TransactionHistoryType.expenditure,
        depositType: DepositType.fixedDeposit,
        startingDate: e.valueDateAd,
        tradingDate: e.transactionDateAd,
        transactionType: TransactionType.from(
          e.transactionType,
          e.transactionTypeName,
        ),
        amount: e.amount,
        referenceNumber: e.identificationNumber,
        checksIssuedByOtherBanksAmount: e.checksIssuedByOtherBanksAmount,
        interestRate: e.interestRate,
        maturityDateAd: e.maturityDateAd,
        abstract: e.abstract,
      ),
    );

    final data = AccountTransactionHistory.data(
      baseDateTime: '${response.baseDate} ${response.baseTime}',
      itemCount: response.transactionDataCount,
      totalIncome: response.totalDepositAmount,
      totalExpenditure: response.totalWithdrawalAmount,
      items: response.whenAccountType(
        liquidity: () => transactions1,
        fixed: () => transactions2,
        other: () => const [],
      ),
    );

    // キャッシュに保存する
    _cache[AccountTransactionHistoryCacheKey(
      accountId: accountId,
      dateFrom: dateFrom,
      dateTo: dateTo,
    )] = data;

    return data;
  }

  /// デフォルトは照会番号（預入番号）降順でソート
  @override
  Future<AppResult<AccountTransactionHistory>> getAccountTransactionHistory({
    required String accountId,
    required DateTime dateFrom,
    required DateTime dateTo,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortType sortType = TransactionHistorySortType.date,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    List<TransactionTypeCode>? transactionTypeCode,
    String? payerName,
    int? minAmount,
    int? maxAmount,
    String? referenceNumber,
  }) async {
    Future<AccountTransactionHistory> getData() async {
      final cachedData = _getFromCache(accountId, dateFrom, dateTo);
      if (forceRefresh || cachedData == null) {
        // キャッシュがない、または強制再取得の場合、APIから取得する
        return _getAll(accountId, dateFrom, dateTo);
      } else {
        // キャッシュがある場合、キャッシュから取得する
        return cachedData;
      }
    }

    try {
      final history = await getData();

      return history.map(
        empty: (empty) => AppResult.success(empty),
        data: (data) => AppResult.success(
          _filterAndSort(
            data,
            filterType: filterType,
            sortType: sortType,
            order: order,
            startingDateFrom: startingDateFrom,
            startingDateTo: startingDateTo,
            tradingDateFrom: tradingDateFrom,
            tradingDateTo: tradingDateTo,
            transactionTypeCode: transactionTypeCode,
            payerName: payerName,
            minAmount: minAmount,
            maxAmount: maxAmount,
            referenceNumber: referenceNumber,
          ),
        ),
      );
    } catch (e) {
      Log.e(e);
      return AppResult.failure(e.toAppError());
    }
  }

  String _convertToDateParam(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd').format(dateTime);
  }

  AccountTransactionHistoryData _filterAndSort(
    AccountTransactionHistoryData history, {
    required TransactionHistoryFilterType filterType,
    required TransactionHistorySortType sortType,
    required TransactionHistorySortOrder order,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    List<TransactionTypeCode>? transactionTypeCode,
    String? payerName,
    int? minAmount,
    int? maxAmount,
    String? referenceNumber,
  }) {
    return history.copyWith(
      items: history.items
          .filterByType(
            filterType,
          )
          .filterByStartingDatePeriod(startingDateFrom, startingDateTo)
          .filterByTradingDatePeriod(tradingDateFrom, tradingDateTo)
          .filterByTransactionType(transactionTypeCode)
          .filterByPayerName(payerName)
          .filterByMinAmount(minAmount)
          .filterByMaxAmount(maxAmount)
          .filterByReferenceNumber(referenceNumber)
          .sortItems(sortType, order),
    );
  }
}

final accountTransactionHistoryRepositoryProvider =
    Provider<AccountTransactionHistoryRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  // 同時に呼び出すとエラーになるAPIがあるので、直列呼び出し用クライアントを使用する。
  final serialApi = ref.watch(serialCallApiClientProvider);
  final md = ref.watch(mdManagerProvider);
  return AccountTransactionHistoryRepositoryImpl(
    bc.baseUrl,
    bc.apiVersion,
    serialApi,
    md,
  );
});
