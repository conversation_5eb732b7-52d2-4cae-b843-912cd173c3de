import 'package:dtp_app/business_logics/interfaces/account_transaction_history_repository.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/account_transaction_history/transaction_type.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';

class DebugAccountTransactionHistoryRepositoryImpl
    implements AccountTransactionHistoryRepository {
  static const in_1_10000 = 0; // [入金] No.1 ¥10,000
  static const ex_2_00100 = 1; // [出金] No.2    ¥100
  static const in_3_10000 = 2; // [入金] No.3 ¥10,000
  static const ex_4_05000 = 3; // [出金] No.4  ¥5,000
  static const in_5_00100 = 4; // [入金] No.5    ¥100
  static const ex_6_10000 = 5; // [出金] No.6 ¥10,000
  static const in_7_01000 = 6; // [入金] No.7  ¥1,000
  static const ex_8_00100 = 7; // [出金] No.8    ¥100

  List<AccountTransactionHistoryDetail> items = [
    // [入金] No.1 ¥10,000
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.income,
      depositType: DepositType.fixedDeposit,
      payerName: '[入金] No.1 ¥10,000',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 10000,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [出金] No.2 ¥100
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.expenditure,
      depositType: DepositType.fixedDeposit,
      payerName: '[出金] No.2 ¥100',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 100,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [入金] No.3 ¥10,000
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.income,
      depositType: DepositType.fixedDeposit,
      payerName: '[入金] No.3 ¥10,000',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 10000,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [出金] No.4 ¥5,000
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.expenditure,
      depositType: DepositType.fixedDeposit,
      payerName: '[出金] No.4 ¥5,000',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 5000,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [入金] No.5 ¥100
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.income,
      depositType: DepositType.fixedDeposit,
      payerName: '[入金] No.5 ¥100',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 100,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [出金] No.6 ¥10,000
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.expenditure,
      depositType: DepositType.fixedDeposit,
      payerName: '[出金] No.6 ¥10,000',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 10000,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [入金] No.7 ¥1,000
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.income,
      depositType: DepositType.fixedDeposit,
      payerName: '[入金] No.7 ¥1,000',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 1000,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
    // [出金] No.8 ¥100
    AccountTransactionHistoryDetail(
      type: TransactionHistoryType.expenditure,
      depositType: DepositType.fixedDeposit,
      payerName: '[出金] No.8 ¥100',
      startingDate: '2020/1/1',
      tradingDate: '2020/1/1',
      transactionType: TransactionType.from(
        '11',
        '振込',
      ),
      amount: 100,
      referenceNumber: '********',
      remitterCode: '********',
      checksIssuedByOtherBanksAmount: 20000,
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      ediInfo: '01209315034789264425',
      interestRate: '020000',
      maturityDateAd: '2020/12/12',
      abstract: 'ﾌﾘｺﾐ',
    ),
  ];

  @override
  Future<AppResult<AccountTransactionHistory>> getAccountTransactionHistory({
    required String accountId,
    required DateTime dateFrom,
    required DateTime dateTo,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortType sortType = TransactionHistorySortType.date,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    DateTime? startingDateFrom,
    DateTime? startingDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    List<TransactionTypeCode>? transactionTypeCode,
    String? payerName,
    int? minAmount,
    int? maxAmount,
    String? referenceNumber,
  }) async {
    // ignore: unused_local_variable
    List<AccountTransactionHistoryDetail> sortedItems;
    switch (filterType) {
      case TransactionHistoryFilterType.income:
        // 入金
        if (sortType == TransactionHistorySortType.date) {
          // 照会番号（預入番号）
          sortedItems = order == TransactionHistorySortOrder.ascending
              ?
              // 昇順
              [
                  items[in_1_10000], // [入金] No.1 ¥10,000
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[in_7_01000], // [入金] No.7  ¥1,000
                ]
              :
              // 降順
              [
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[in_1_10000], // [入金] No.1 ¥10,000
                ];
        } else {
          // 金額
          sortedItems = order == TransactionHistorySortOrder.ascending
              ?
              // 昇順
              [
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[in_1_10000], // [入金] No.1 ¥10,000
                ]
              :
              // 降順
              [
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[in_1_10000], // [入金] No.1 ¥10,000
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[in_5_00100], // [入金] No.5    ¥100
                ];
        }
        break;
      case TransactionHistoryFilterType.expenditure:
        // 出金
        if (sortType == TransactionHistorySortType.date) {
          // 照会番号（預入番号）
          sortedItems = order == TransactionHistorySortOrder.ascending
              ?
              // 昇順
              [
                  items[ex_2_00100], // [出金] No.2    ¥100
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[ex_8_00100], // [出金] No.8    ¥100
                ]
              :
              // 降順
              [
                  items[ex_8_00100], // [出金] No.8    ¥100
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[ex_2_00100], // [出金] No.2    ¥100
                ];
        } else {
          // 金額
          sortedItems = order == TransactionHistorySortOrder.ascending
              ?
              // 昇順
              [
                  items[ex_8_00100], // [出金] No.8    ¥100
                  items[ex_2_00100], // [出金] No.2    ¥100
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                ]
              :
              // 降順
              [
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[ex_8_00100], // [出金] No.8    ¥100
                  items[ex_2_00100], // [出金] No.2    ¥100
                ];
        }
        break;
      default:
        // すべて
        if (sortType == TransactionHistorySortType.date) {
          // 照会番号（預入番号）
          sortedItems = order == TransactionHistorySortOrder.ascending
              ?
              // 昇順
              [
                  items[in_1_10000], // [入金] No.1 ¥10,000
                  items[ex_2_00100], // [出金] No.2    ¥100
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[ex_8_00100], // [出金] No.8    ¥100
                ]
              :
              // 降順
              [
                  items[ex_8_00100], // [出金] No.8    ¥100
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[ex_2_00100], // [出金] No.2    ¥100
                  items[in_1_10000], // [入金] No.1 ¥10,000
                ];
        } else {
          // 金額
          sortedItems = order == TransactionHistorySortOrder.ascending
              ?
              // 昇順
              [
                  items[ex_8_00100], // [出金] No.8    ¥100
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[ex_2_00100], // [出金] No.2    ¥100
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[in_1_10000], // [入金] No.1 ¥10,000
                ]
              :
              // 降順
              [
                  items[ex_6_10000], // [出金] No.6 ¥10,000
                  items[in_3_10000], // [入金] No.3 ¥10,000
                  items[in_1_10000], // [入金] No.1 ¥10,000
                  items[ex_4_05000], // [出金] No.4  ¥5,000
                  items[in_7_01000], // [入金] No.7  ¥1,000
                  items[ex_8_00100], // [出金] No.8    ¥100
                  items[in_5_00100], // [入金] No.5    ¥100
                  items[ex_2_00100], // [出金] No.2    ¥100
                ];
        }
        break;
    }
    // return const AppResult.failure(
    //   AppError(
    //     code: 'E00300002',
    //     message: '照会可能な期間を超えています。[246]',
    //     baseDateTime: '2023/12/1 17:00',
    //   ),
    // );

    // return AppResult.success(
    //   AccountTransactionHistory.data(
    //     baseDateTime: '2020/1/1 00:00:00',
    //     itemCount: sortedItems.length,
    //     totalIncome: 21100,
    //     totalExpenditure: 15200,
    //     items: sortedItems,
    //   ),
    // );

    return const AppResult.success(
      AccountTransactionHistory.data(
        items: [],
      ),
    );
  }
}
