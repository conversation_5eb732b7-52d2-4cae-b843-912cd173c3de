import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_identification_api_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_oauth_api_repository.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_sdk_repository.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/jpki_identification_api/jpki_identification_api.dart';
import 'package:dtp_app/repositories/jpki_oauth_api/jpki_oauth_api_repository_impl.dart';
import 'package:dtp_app/repositories/jpki_sdk/jpki_sdk_repository_impl.dart';
import 'package:dtp_app/repositories/mynapocket_api_client.dart';
import 'package:dtp_app/utils/api_parameter.dart';
import 'package:dtp_app/utils/sdk_parameter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JpkiIdentificationApiRepositoryImpl
    implements JpkiIdentificationApiRepository {
  JpkiIdentificationApiRepositoryImpl(
    this.jpkiOAuthAPIRepository,
    this.jpkiSdkRepository,
    this.mynaPocketBaseUrl,
    this._api,
  );

  final JpkiOAuthAPIRepository jpkiOAuthAPIRepository;
  final JpkiSdkRepository jpkiSdkRepository;
  final String mynaPocketBaseUrl;
  final MynaPocketApiClient _api;

  // 本人確認API呼び出し
  @override
  Future<AppResult<JpkiIdentificationApi>> jpkiIdentificationApiAccess({
    required String referenceNumber,
    required String uuid,
    required String userTypeNumber,
  }) async {
    try {
      const procNum = ApiParameter.procNum;
      const contractCd = ApiParameter.contractCd;
      const compCd = ApiParameter.compCd;

      // 署名対象ドキュメントを取得
      final docData = SdkParameter.getEncodedData();

      // メモリデータマネージャーからアクセストークンを取得
      final authorization = await jpkiOAuthAPIRepository.getJpkiAccessToken();

      // メモリデータマネージャーから署名済みドキュメントを取得
      final sigDoc = await jpkiSdkRepository.getSigDoc();

      // メモリデータマネージャーから署名用電子証明書を取得
      final certForSig = await jpkiSdkRepository.getCertForSig();

      final body = jsonEncode({
        'procNum': procNum,
        'contractCd': contractCd,
        'compCd': compCd,
        'docData': docData,
        'sigDoc': sigDoc,
        'certForSig': certForSig,
        // 顧客識別番号に、お手続き番号+手続き者属性を結合したものを送信
        'identifiNum': referenceNumber + userTypeNumber,
        // uuidは汎用項目として送信
        'coopItem1': uuid,
      });

      final res = await _api.postJsonData(
        endpoint: 'identification',
        headers: {
          'Authorization': 'Bearer $authorization',
        },
        body: body,
      );

      final json = parseJsonMap(res);
      final jpkiIdentificationApi = JpkiIdentificationApi.fromJson(json);

      if (jpkiIdentificationApi.status != '0' &&
          jpkiIdentificationApi.status.isNotEmpty) {
        return AppResult.failure('status error'.toAppError());
      }
      return AppResult.success(jpkiIdentificationApi);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final jpkiIdentificationApiRepositoryProvider =
    Provider<JpkiIdentificationApiRepository>((ref) {
  final jpkiOAuthAPIRepository = ref.watch(jpkiOAuthAPIRepositoryProvider);
  final jpkiSdkRepository = ref.watch(jpkiSdkRepositoryProvider);
  final buildConfig = ref.watch(buildConfigProvider);
  final api = ref.watch(mynaPocketApiClientProvider);
  return JpkiIdentificationApiRepositoryImpl(
    jpkiOAuthAPIRepository,
    jpkiSdkRepository,
    buildConfig.mynaPocketBaseUrl,
    api,
  );
});
