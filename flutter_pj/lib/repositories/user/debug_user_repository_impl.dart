import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/user_info/user_info.dart';
import 'package:dtp_app/models/users/users.dart';

class DebugUserRepositoryImpl implements UserRepository {
  @override
  Future<AppResult<Users>> getUsers() async {
    // return const AppResult.failure(AppError(message: 'error'));
    return AppResult.success(
      Users(
        count: 3,
        accountList: [
          AccountList(
            accountId: '*************',
            account: '************',
            branchNameKana: 'ﾄｳｷｮｳ支店',
            branchNameKanji: '東京支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ABCｼｮｳｼﾞ',
            displayAccountName: '振込用口座1',
            isHidden: false,
            displayOrder: 0,
            bankName: '東京銀行',
            accountApiType: 'web21',
          ),
          AccountList(
            accountId: '*************',
            account: '************',
            branchNameKana: 'ﾄｳｷｮｳ支店2',
            branchNameKanji: '東京支店2',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ABCｼｮｳｼ2ﾞ',
            displayAccountName: '振込用口座2',
            isHidden: false,
            displayOrder: 1,
            bankName: '三井住友銀行',
            accountApiType: 'web21',
          ),
          AccountList(
            accountId: '*************',
            account: '************',
            branchNameKana: 'ﾄｳｷｮｳ支店3',
            branchNameKanji: '東京支店3',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ABCｼｮｳｼ3ﾞ',
            displayAccountName: '振込用口座3',
            isHidden: false,
            displayOrder: 2,
            bankName: '三井住友銀行',
            accountApiType: 'web21',
          ),
        ],
        balanceAuthorityStatus: 'ON',
        serverDate: '2023/9/1',
      ),
    );
  }

  @override
  Future<AppResult<UserInfo>> getUserInfo() async {
    return const AppResult.success(
      UserInfo(
        vdId: '********',
        ninsyoKbn: '01',
        userSeiMei: '東京さむる太郎',
        userType: '01',
        compName: '東京SAML企業',
        branchCode: '115',
        accountType: '01',
        accountNumber: '1234567',
        email: '<EMAIL>',
        dtpId: '',
        //dtpId: '<EMAIL>',
      ),
    );
  }

  @override
  Future<AppResult<void>> updateDtpId() async {
    return const AppResult.success(null);
  }

  @override
  Future<AppResult<String>> issueAndLinkDtpId(
    EncryptedCookie encryptedCookie,
  ) async {
    return const AppResult.success('<EMAIL>');
  }

  @override
  Future<AppResult<void>> deleteDtpIdLink() async {
    return const AppResult.success(null);
  }

  @override
  List<AccountList> getUserAccounts() {
    return const [
      AccountList(
        accountId: '*************',
        account: '************',
        branchNameKana: 'ﾄｳｷｮｳ支店',
        branchNameKanji: '東京支店',
        accountType: '普通',
        accountNumber: '1100001',
        remitterName: 'ABCｼｮｳｼﾞ',
        displayAccountName: '振込用口座1',
        isHidden: false,
        displayOrder: 0,
        bankName: '東京銀行',
        accountApiType: 'web21',
      ),
      AccountList(
        accountId: '*************',
        account: '************',
        branchNameKana: 'ﾄｳｷｮｳ支店2',
        branchNameKanji: '東京支店2',
        accountType: '普通',
        accountNumber: '1100002',
        remitterName: 'ABCｼｮｳｼ2ﾞ',
        displayAccountName: '振込用口座2',
        isHidden: false,
        displayOrder: 1,
        bankName: '三井住友銀行',
        accountApiType: 'web21',
      ),
      AccountList(
        accountId: '*************',
        account: '************',
        branchNameKana: 'ﾄｳｷｮｳ支店3',
        branchNameKanji: '東京支店3',
        accountType: '普通',
        accountNumber: '1100003',
        remitterName: 'ABCｼｮｳｼ3ﾞ',
        displayAccountName: '振込用口座3',
        isHidden: false,
        displayOrder: 2,
        bankName: '三井住友銀行',
        accountApiType: 'web21',
      ),
      AccountList(
        accountId: '*************',
        account: '************',
        branchNameKana: 'ﾄｳｷｮｳ支店2',
        branchNameKanji: '東京支店2',
        accountType: '普通',
        accountNumber: '1100002',
        remitterName: 'ABCｼｮｳｼ2ﾞ',
        displayAccountName: '振込用口座2',
        isHidden: false,
        displayOrder: 1,
        bankName: '三井住友銀行',
        accountApiType: 'web21',
      ),
      AccountList(
        accountId: '*************',
        account: '************',
        branchNameKana: 'ﾄｳｷｮｳ支店3',
        branchNameKanji: '東京支店3',
        accountType: '普通',
        accountNumber: '1100003',
        remitterName: 'ABCｼｮｳｼ3ﾞ',
        displayAccountName: '振込用口座3',
        isHidden: false,
        displayOrder: 2,
        bankName: '三井住友銀行',
        accountApiType: 'web21',
      ),
    ];
  }

  @override
  String getServerDate() {
    return '2023/9/1';
  }

  @override
  AppError? getError() {
    return null;
  }
}
