import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/user_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/app_error.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/encrypted_cookie/encrypted_cookie.dart';
import 'package:dtp_app/models/user_info/user_info.dart';
import 'package:dtp_app/models/user_info_response/user_info_response.dart';
import 'package:dtp_app/models/users/users.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/repositories/serial_call_api_client.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class UserRepositoryImpl implements UserRepository {
  UserRepositoryImpl(
    this._baseUrl,
    this._apiVersion,
    this._api,
    this._serialApi,
    this._mdManager,
  );

  final String _baseUrl;
  final String _apiVersion;
  final ApiClient _api;

  /// 直列呼び出し用のクライアント
  final ApiClient _serialApi;
  final MemoryDataManager _mdManager;

  List<AccountList> _userAccounts = [];
  String? _serverDate;

  //キャッシュ削除、利用者情報取得APIが失敗した場合に使用する変数
  AppError? _error;

  @override
  Future<AppResult<Users>> getUsers() async {
    try {
      //アプリ内に保存している利用者情報を削除
      _clearLocal();

      final sessionId = await _mdManager.sessionId.load();
      final res = await _serialApi.get(
        url: '$_baseUrl/api/$_apiVersion/bank/users',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final users = Users.fromJson(json);

      // 表示口座数をMax5にする
      _userAccounts = users.accountList.updateIsHiddenFlag(max: 5);
      _serverDate = users.serverDate;

      //　利用者情報を更新して返却
      return AppResult.success(users.copyWith(accountList: _userAccounts));
    } on Exception catch (error) {
      _error = error.toAppError();
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<UserInfo>> getUserInfo() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      final res = await _api.get(
        url: '$_baseUrl/api/$_apiVersion/bank/userInfo',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      final json = parseJsonMap(res.body);
      final response = UserInfoResponse.fromJson(json);
      // response.compId(14桁)を以下の3つに分解する
      //  - 支店コード(3or4桁)  最初が0の場合は取り除く 例)0115→115, 9999→9999, 0001→001
      //  - 科目(2桁) 画面側で対応する文字列に変換 例)01, 02, 03
      //  - 口座番号(7桁) 後半8桁のうち最初の1桁(0)を省いて7桁にする 例)********→1234567
      return AppResult.success(
        UserInfo(
          vdId: response.vdId,
          ninsyoKbn: response.ninsyoKbn,
          userSeiMei: response.userSeiMei,
          userType: response.userType,
          compName: response.compName,
          // 支店コード(4桁)
          branchCode: _removeLeadingZero(response.compId.substring(0, 4)),
          // 科目(2桁)
          accountType: response.compId.substring(4, 6),
          // 口座番号(7桁)
          accountNumber: response.compId.substring(7),
          email: response.email,
          dtpId: response.dtpId,
          internalId: response.userId,
          contractType: response.keiyakuType,
          userAuths: response.userAuths,
        ),
      );
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<void>> updateDtpId() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      await _api.put(
        endpoint: 'api/$_apiVersion/dtpId',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        body: {},
      );

      return AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  Future<AppResult<String>> issueAndLinkDtpId(
    EncryptedCookie encryptedCookie,
  ) async {
    return encryptedCookie.when(
      (ticket, domain, path, secure, httpOnly) async {
        try {
          final sessionId = await _mdManager.sessionId.load();
          final body = jsonEncode(
            {
              'encryptedCookie': {
                'ticket': ticket,
                'domain': domain,
                'path': path,
                'secure': secure,
                'httpOnly': httpOnly,
              },
            },
          );

          final res = await _api.postJsonData(
            endpoint: 'api/$_apiVersion/dtpId/link',
            headers: {
              'Authorization': 'Bearer $sessionId',
            },
            body: body,
          );

          final json = parseJsonMap(res.body);

          final receivedEncryptedCookie = json['encryptedCookie'];
          final dtpId = json['dtpId'] as String;

          if (receivedEncryptedCookie != null) {
            // 受信した暗号化Cookieを保存
            final encryptedCookie = EncryptedCookie(
              ticket: receivedEncryptedCookie['ticket'] as String,
              domain: receivedEncryptedCookie['domain'] as String,
              path: receivedEncryptedCookie['path'] as String,
              secure: receivedEncryptedCookie['secure'] as String,
              httpOnly: receivedEncryptedCookie['httpOnly'] as String,
            );

            await _mdManager.encryptedCookie.save(encryptedCookie);
          }
          return AppResult.success(dtpId);
        } on Exception catch (error) {
          return AppResult.failure(error.toAppError());
        }
      },
      empty: () => const AppResult.success(''),
    );
  }

  @override
  Future<AppResult<void>> deleteDtpIdLink() async {
    try {
      final sessionId = await _mdManager.sessionId.load();
      await _api.delete(
        endpoint: 'api/$_apiVersion/dtpId/link',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
      );

      return const AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }

  @override
  List<AccountList> getUserAccounts() {
    return _userAccounts;
  }

  @override
  String getServerDate() {
    return _serverDate ?? '';
  }

  @override
  AppError? getError() {
    return _error;
  }

  void _clearLocal() {
    _userAccounts = [];
    _error = null;
  }

  String _removeLeadingZero(String value) {
    // 最初の文字が'0'の場合は取り除く
    return value.isNotEmpty && value[0] == '0' ? value.substring(1) : value;
  }
}

final userRepositoryProvider = Provider<UserRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final serialApi = ref.watch(serialCallApiClientProvider);
  final md = ref.watch(mdManagerProvider);

  return UserRepositoryImpl(bc.baseUrl, bc.apiVersion, api, serialApi, md);
});
