import 'dart:convert';

import 'package:dtp_app/business_logics/interfaces/jpki_service_check_api_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/jpki_service_check_api_response/jpki_service_check_api_response.dart';
import 'package:dtp_app/repositories/mynapocket_api_client.dart';
import 'package:dtp_app/utils/api_parameter.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JpkiServiceCheckApiRepositoryImpl
    implements JpkiServiceCheckApiRepository {
  JpkiServiceCheckApiRepositoryImpl(
    this.procNum,
    this.contractCd,
    this.compCd,
    this._api,
    this._memoryDataManager,
  );

  final String procNum;
  final String contractCd;
  final String compCd;

  final MynaPocketApiClient _api;
  final MemoryDataManager _memoryDataManager;

  @override
  Future<String> getJpkiAccessToken() async {
    return _memoryDataManager.jpkiAccessToken.load();
  }

  // Servicecheck呼び出し
  @override
  Future<AppResult<String>> jpkiServiceCheckApiAccess(
    String jpkiAccessToken,
  ) async {
    try {
      // API仕様書定義通り、「Bearer + 半角スペース」の後にアクセストークンを付与
      final headers = {'Authorization': 'Bearer $jpkiAccessToken'};
      final body = jsonEncode({
        'procNum': procNum,
        'contractCd': contractCd,
        'compCd': compCd,
      });

      final res = await _api.postJsonData(
        endpoint: 'servicecheck',
        headers: headers,
        body: body,
      );

      final json = parseJsonMap(res);
      final jpkiServiceCheckApiResponse =
          JpkiServiceCheckApiResponse.fromJson(json);
      if (jpkiServiceCheckApiResponse.status != '0') {
        return AppResult.failure('status error'.toAppError());
      }

      return AppResult.success(json.toString());
    } on Exception catch (error) {
      Log.d('error :$error');
      return AppResult.failure(error.toAppError());
    }
  }
}

final jpkiServiceCheckApiRepositoryProvider =
    Provider<JpkiServiceCheckApiRepository>((ref) {
  final memoryDataManager = ref.watch(mdManagerProvider);
  final api = ref.watch(mynaPocketApiClientProvider);
  return JpkiServiceCheckApiRepositoryImpl(
    ApiParameter.procNum,
    ApiParameter.contractCd,
    ApiParameter.compCd,
    api,
    memoryDataManager,
  );
});
