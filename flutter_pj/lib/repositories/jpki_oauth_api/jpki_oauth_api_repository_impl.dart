import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/jpki_oauth_api_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/models/app_error/object_to_app_error.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/jpki_oauth_api/jpki_oauth_api.dart';
import 'package:dtp_app/repositories/mynapocket_api_client.dart';
import 'package:dtp_app/utils/api_parameter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class JpkiOAuthAPIRepositoryImpl implements JpkiOAuthAPIRepository {
  JpkiOAuthAPIRepositoryImpl(
    this.mynaPocketBaseUrl,
    this._api,
    this._memoryDataManager,
  );

  final String mynaPocketBaseUrl;
  final MynaPocketApiClient _api;
  final MemoryDataManager _memoryDataManager;

  @override
  Future<String> getJpkiAccessToken() async {
    return _memoryDataManager.jpkiAccessToken.load();
  }

  @override
  Future<void> setJpkiAccessToken(String accessToken) async {
    await _memoryDataManager.jpkiAccessToken.save(accessToken);
  }

  @override
  Future<void> saveClientId(String clientId) async {
    await _memoryDataManager.clientId.save(clientId);
  }

  @override
  Future<void> saveClientSecret(String clientSecret) async {
    await _memoryDataManager.clientSecret.save(clientSecret);
  }

  // CC_OAuthAPI呼び出し
  @override
  Future<AppResult<void>> jpkiOAuthApiAccess() async {
    try {
      const contentType = ApiParameter.oAuthApiContentType;
      const scope = ApiParameter.oAuthApiScope;
      const grantType = ApiParameter.oAuthApiGrantType;

      final clientId = await _memoryDataManager.clientId.load();
      final clientSecret = await _memoryDataManager.clientSecret.load();

      final res = await _api.post(
        endpoint: 'cc/oauth2/token',
        headers: {
          'Content-Type': contentType,
        },
        body: {
          'client_id': clientId,
          'client_secret': clientSecret,
          'scope': scope,
          'grant_type': grantType,
        },
      );

      final json = parseJsonMap(res);
      final jpkiOAuthApi = JpkiOAuthApi.fromJson(json);

      // 取得したアクセストークンをセキュアストレージに設定
      await setJpkiAccessToken(jpkiOAuthApi.accessToken);

      // アクセストークンが取得できたためclientIdとclientSecretを削除
      await _memoryDataManager.clientId.delete();
      await _memoryDataManager.clientSecret.delete();

      return const AppResult.success(null);
    } on Exception catch (error) {
      return AppResult.failure(error.toAppError());
    }
  }
}

final jpkiOAuthAPIRepositoryProvider = Provider<JpkiOAuthAPIRepository>((ref) {
  final buildConfig = ref.watch(buildConfigProvider);
  final api = ref.watch(mynaPocketApiClientProvider);
  final memoryDataManager = ref.watch(mdManagerProvider);
  return JpkiOAuthAPIRepositoryImpl(
    buildConfig.mynaPocketBaseUrl,
    api,
    memoryDataManager,
  );
});
