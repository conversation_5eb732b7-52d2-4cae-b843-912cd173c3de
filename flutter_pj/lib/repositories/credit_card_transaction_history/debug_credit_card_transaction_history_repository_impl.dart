import 'package:dtp_app/business_logics/interfaces/credit_card_transaction_history_repository.dart';
import 'package:dtp_app/models/account_transaction_history/account_transaction_history.dart';
import 'package:dtp_app/models/app_result/app_result.dart';
import 'package:dtp_app/models/freee_transaction_history/freee_transaction_history.dart';
import 'package:dtp_app/models/transaction_filter_and_sort/transaction_filter_and_sort.dart';

class DebugCreditCardTransactionHistoryRepositoryImpl
    implements CreditCardTransactionHistoryRepository {
  static const in_1_10000 = 0; // [入金] No.1 ¥10,000
  static const ex_2_00100 = 1; // [出金] No.2    ¥100
  static const in_3_10000 = 2; // [入金] No.3 ¥10,000
  static const ex_4_05000 = 3; // [出金] No.4  ¥5,000
  static const in_5_00100 = 4; // [入金] No.5    ¥100
  static const ex_6_10000 = 5; // [出金] No.6 ¥10,000
  static const in_7_01000 = 6; // [入金] No.7  ¥1,000
  static const ex_8_00100 = 7; // [出金] No.8    ¥100

  var isCreditCardExpenseVisible = true;

  // クレジットカード利用情報の表示/非表示状態を取得
  @override
  Future<bool> getCreditCardExpenseVisible() async {
    return isCreditCardExpenseVisible;
  }

  // クレジットカード利用情報の表示/非表示状態を設定
  @override
  Future<bool> setCreditCardExpenseVisible(bool value) async {
    isCreditCardExpenseVisible = value;
    return true;
  }

  List<FreeeTransactionHistoryDetail> items = [
    // [入金] No.1 ¥10,000
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/8',
      amount: 10000,
    ),
    // [出金] No.2 ¥100
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/7',
      amount: 100,
    ),
    // [入金] No.3 ¥10,000
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/6',
      amount: 10000,
    ),
    // [出金] No.4 ¥5,000
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/5',
      amount: 5000,
    ),
    // [入金] No.5 ¥100
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/4',
      amount: 100,
    ),
    // [出金] No.6 ¥10,000
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/3',
      amount: 10000,
    ),
    // [入金] No.7 ¥1,000
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/2',
      amount: 1000,
    ),
    // [出金] No.8 ¥100
    const FreeeTransactionHistoryDetail(
      type: FreeeTransactionHistoryType.income,
      transactionType: FreeeTransactionsType.creditCard,
      tradingDate: '2020/1/1',
      amount: 100,
    ),
  ];

  @override
  Future<AppResult<FreeeTransactionHistory>>
      getFreeeCreditCardTransactionHistory({
    required int walletableId,
    required FreeeTransactionsType walletableType,
    bool forceRefresh = false,
    TransactionHistoryFilterType filterType = TransactionHistoryFilterType.all,
    TransactionHistorySortOrder order = TransactionHistorySortOrder.descending,
    required startDateFrom,
    required startDateTo,
    DateTime? tradingDateFrom,
    DateTime? tradingDateTo,
    TransactionHistorySortType sortType = TransactionHistorySortType.date,
    double? minAmount,
    double? maxAmount,
    double? minAmountWithSign,
    double? maxAmountWithSign,
    String? descriptionSearch,
  }) async {
    List<FreeeTransactionHistoryDetail> sortedItems;
    switch (filterType) {
      case TransactionHistoryFilterType.income:
        // 入金
        // 金額
        sortedItems = order == TransactionHistorySortOrder.ascending
            ?
            // 昇順
            [
                items[in_5_00100], // [入金] No.5    ¥100
                items[in_7_01000], // [入金] No.7  ¥1,000
                items[in_3_10000], // [入金] No.3 ¥10,000
                items[in_1_10000], // [入金] No.1 ¥10,000
              ]
            :
            // 降順
            [
                items[in_3_10000], // [入金] No.3 ¥10,000
                items[in_1_10000], // [入金] No.1 ¥10,000
                items[in_7_01000], // [入金] No.7  ¥1,000
                items[in_5_00100], // [入金] No.5    ¥100
              ];
        break;
      case TransactionHistoryFilterType.expenditure:
        // 出金
        // 金額
        sortedItems = order == TransactionHistorySortOrder.ascending
            ?
            // 昇順
            [
                items[ex_8_00100], // [出金] No.8    ¥100
                items[ex_2_00100], // [出金] No.2    ¥100
                items[ex_4_05000], // [出金] No.4  ¥5,000
                items[ex_6_10000], // [出金] No.6 ¥10,000
              ]
            :
            // 降順
            [
                items[ex_6_10000], // [出金] No.6 ¥10,000
                items[ex_4_05000], // [出金] No.4  ¥5,000
                items[ex_8_00100], // [出金] No.8    ¥100
                items[ex_2_00100], // [出金] No.2    ¥100
              ];
        break;
      default:
        // すべて
        // 金額
        sortedItems = order == TransactionHistorySortOrder.ascending
            ?
            // 昇順
            [
                items[ex_8_00100], // [出金] No.8    ¥100
                items[in_5_00100], // [入金] No.5    ¥100
                items[ex_2_00100], // [出金] No.2    ¥100
                items[in_7_01000], // [入金] No.7  ¥1,000
                items[ex_4_05000], // [出金] No.4  ¥5,000
                items[ex_6_10000], // [出金] No.6 ¥10,000
                items[in_3_10000], // [入金] No.3 ¥10,000
                items[in_1_10000], // [入金] No.1 ¥10,000
              ]
            :
            // 降順
            [
                items[ex_6_10000], // [出金] No.6 ¥10,000
                items[in_3_10000], // [入金] No.3 ¥10,000
                items[in_1_10000], // [入金] No.1 ¥10,000
                items[ex_4_05000], // [出金] No.4  ¥5,000
                items[in_7_01000], // [入金] No.7  ¥1,000
                items[ex_8_00100], // [出金] No.8    ¥100
                items[in_5_00100], // [入金] No.5    ¥100
                items[ex_2_00100], // [出金] No.2    ¥100
              ];
        break;
    }

    return AppResult.success(
      FreeeTransactionHistory.data(
        items: sortedItems,
      ),
    );
  }
}
