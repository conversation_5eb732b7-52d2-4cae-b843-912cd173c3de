import 'dart:convert';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/interfaces/operation_log_repository.dart';
import 'package:dtp_app/datas/shared_preferences/memory_data_manager.dart';
import 'package:dtp_app/repositories/api_client.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class OperationLogRepositoryImpl implements OperationLogRepository {
  OperationLogRepositoryImpl(
    this._apiVersion,
    this._api,
    this._mdManager,
  );

  final String _apiVersion;
  final ApiClient _api;
  final MemoryDataManager _mdManager;

  @override
  Future<void> sendOperationLog({
    required String? functionLog,
    required String? operationLog,
    required String? resultLog,
    required String? errorIdLog,
  }) async {
    try {
      final sessionId = await _mdManager.sessionId.load();

      final body = jsonEncode({
        'clientFunction': functionLog,
        'operation': operationLog,
        'result': resultLog,
        'errorId': errorIdLog,
      });

      await _api.postJsonData(
        endpoint: 'api/$_apiVersion/clientActionLog',
        headers: {
          'Authorization': 'Bearer $sessionId',
        },
        body: body,
      );
    } on Exception catch (error) {
      Log.e(
        'ログ送信時に例外発生: $error',
      );
    }
  }
}

final operationLogRepositoryProvider = Provider<OperationLogRepository>((ref) {
  final bc = ref.watch(buildConfigProvider);
  final api = ref.watch(apiClientProvider);
  final md = ref.watch(mdManagerProvider);

  return OperationLogRepositoryImpl(bc.apiVersion, api, md);
});
