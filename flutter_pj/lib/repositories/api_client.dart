import 'dart:convert';
import 'dart:io';

import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/models/api_response_with_date/api_response_with_date.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:http/io_client.dart';

typedef JsonMap = Map<String, dynamic>;
typedef JsonList = List<dynamic>;

JsonMap parseJsonMap(String input) => json.decode(input) as JsonMap;

JsonList parseJsonList(String input) => json.decode(input) as JsonList;

final apiClientProvider = Provider<ApiClient>((ref) {
  final bc = ref.watch(buildConfigProvider);
  return ApiClientImpl(
    isHttpAllowed: bc.isHttpAllowed == 'YES',
    baseUrl: bc.baseUrl,
  );
});

/// ValueDoorへの接続用（主にログイン関連で利用）
final valueDoorApiClientProvider = Provider<ApiClient>((ref) {
  final bc = ref.read(buildConfigProvider);
  return ApiClientImpl(
    baseUrl: bc.valueDoorUrl,
    isHttpAllowed: bc.isHttpAllowed == 'YES',
  );
});

abstract class ApiClient {
  Future<ApiResponseWithDate> get({
    String? url,
    Map<String, String>? headers,
  });

  Future<ApiResponseWithDate> post({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  });

  Future<ApiResponseWithDate> postJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  });

  /// ValueDoor宛のAPIでのみ利用
  Future<ApiResponseWithDate> postUrlEncodeData({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, String> body,
  });

  Future<ApiResponseWithDate> put({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  });

  Future<ApiResponseWithDate> putJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  });

  Future<ApiResponseWithDate> delete({
    required String endpoint,
    Map<String, String>? headers,
  });
}

class ApiClientImpl implements ApiClient {
  ApiClientImpl({
    required this.baseUrl,
    required this.isHttpAllowed,
    String? proxyIPAddress,
  }) {
    final httpClient = HttpClient();
    _client = IOClient(httpClient);
  }

  final String baseUrl;
  final bool isHttpAllowed;
  late http.Client _client;

  @override
  Future<ApiResponseWithDate> get({
    String? url,
    Map<String, String>? headers,
  }) {
    _checkIsHttpsRequest(url ?? baseUrl);
    return _safeApiCall(
      () async => _client.get(
        Uri.parse(url ?? baseUrl),
        headers: headers,
      ),
    );
  }

  @override
  Future<ApiResponseWithDate> post({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    // null項目の削除
    body.removeWhere((_, value) => value == null);
    // 値をStringに変換
    final Map<String, String> mapString =
        body.map((key, value) => MapEntry(key, value.toString()));
    return _safeApiCall(
      () async => _client.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
        body: mapString,
      ),
    );
  }

  @override
  Future<ApiResponseWithDate> postJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    final jsonHeaders = {'Content-Type': 'application/json'};
    if (headers != null) {
      jsonHeaders.addAll(headers);
    }
    return _safeApiCall(
      () async => _client.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: jsonHeaders,
        body: body,
      ),
    );
  }

  @override
  Future<ApiResponseWithDate> postUrlEncodeData({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, String> body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    // User-AgentをapiClientにて設定
    final userAgent = Platform.isIOS ? 'iPhone' : 'Android';
    final urlEncodedHeaders = {'User-Agent': userAgent};
    if (headers != null) {
      urlEncodedHeaders.addAll(headers);
    }
    return _safeApiCall(() async {
      final response = await _client.post(
        Uri.parse('$baseUrl/$endpoint'),
        headers: urlEncodedHeaders,
        body: body,
      );

      // ValueDoorからのレスポンスはcharset=utf8が設定されているものの
      // dartのhttpパッケージはcharset=utf-8(間にハイフンあり)の場合のみデコードする仕様なので
      // 本箇所にて無理やりデコードすることで文字化けしないよう処理している
      final decodedBody = utf8.decode(response.bodyBytes);

      // 受け取ったレスポンスヘッダーのcharsetを手動でutf-8に変更する
      // 変更せずutf8のままだとResponseインスタンス生成時にエラーが発生してしまう
      final modifiedHeaders = Map<String, String>.from(response.headers);
      modifiedHeaders['content-type'] = 'application/json; charset=utf-8';

      return Response(
        decodedBody,
        response.statusCode,
        headers: modifiedHeaders,
      );
    });
  }

  @override
  Future<ApiResponseWithDate> put({
    required String endpoint,
    Map<String, String>? headers,
    required Map<String, dynamic> body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    // null項目の削除
    body.removeWhere((_, value) => value == null);
    // 値をStringに変換
    final Map<String, String> mapString =
        body.map((key, value) => MapEntry(key, value.toString()));
    return _safeApiCall(
      () async => _client.put(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
        body: mapString,
      ),
    );
  }

  @override
  Future<ApiResponseWithDate> putJsonData({
    required String endpoint,
    Map<String, String>? headers,
    required String body,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    final jsonHeaders = {'Content-Type': 'application/json'};
    if (headers != null) {
      jsonHeaders.addAll(headers);
    }
    return _safeApiCall(
      () async => _client.put(
        Uri.parse('$baseUrl/$endpoint'),
        headers: jsonHeaders,
        body: body,
      ),
    );
  }

  @override
  Future<ApiResponseWithDate> delete({
    required String endpoint,
    Map<String, String>? headers,
  }) {
    _checkIsHttpsRequest('$baseUrl/$endpoint');
    return _safeApiCall(
      () async => _client.delete(
        Uri.parse('$baseUrl/$endpoint'),
        headers: headers,
      ),
    );
  }

  void _printResponse({
    required String url,
    required int statusCode,
    required String body,
    Map<String, String>? responseHeaders,
  }) {
    if (statusCode == 200) {
      Log.i(
        'url:$url\nstatus: $statusCode\nbody:$body\nheaders:$responseHeaders',
      );
    } else {
      Log.e(
        'url:$url\nstatus: $statusCode\nbody:$body\nheaders:$responseHeaders',
      );
    }
  }

  /// http通信制御用メソッド
  /// セキュリティガイドライン3-3対応
  void _checkIsHttpsRequest(String url) {
    // dev環境のみはhttp通信を許容
    if (isHttpAllowed) return;
    // それ以外の場合にはhttps通信のみ許容
    if (!url.startsWith('https://')) {
      Log.e('http通信は許容されていません。');
      throw ApiClientException(
        statusCode: 400,
        hasFaq: false,
        message: ErrorInfo.defaultErrorMessage,
        code: ErrorInfo.defaultErrorCode,
      );
    }
  }

  Future<ApiResponseWithDate> _safeApiCall(Function callback) async {
    try {
      // ignore: avoid_dynamic_calls
      final response = await callback() as http.Response;
      _printResponse(
        url: response.request?.url.toString() ?? '',
        statusCode: response.statusCode,
        body: response.body,
        responseHeaders: response.headers,
      );
      return _parseResponse(
        statusCode: response.statusCode,
        response: response,
      );
    } on SocketException catch (error) {
      throw Exception('No Internet Connection: $error');
    } on HttpException catch (error) {
      throw Exception('HttpException: $error');
    }
  }

  ApiResponseWithDate _parseResponse({
    required int statusCode,
    required http.Response response,
  }) {
    // BFF定義済みエラーのステータスコードはここに定義する
    final apiErrorMessages = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      405: 'Method Not Allowed',
      409: 'Conflict',
      500: 'Internal Server Error',
      504: 'Gateway Timeout',
    };

    switch (statusCode) {
      case 200:
      case 201:
        // ステータスコードが200かつ、ヘッダに「sorryscreen」が含まれる場合、基盤系エラー
        if (response.headers.containsKey('sorryscreen')) {
          throw const SorryScreenException();
        }
        return ApiResponseWithDate(
          body: response.body,
          header: response.headers,
          baseDate: response.headers['x-base-date'],
          baseTime: response.headers['x-base-time'],
        );
      default:
        throw _generateExceptionFromHttpResponse(
          statusCode: statusCode,
          response: response,
          defaultMessage: apiErrorMessages[statusCode] ?? 'Unknown StatusCode',
        );
    }
  }

  /// HTTPレスポンスから例外を生成する
  Exception _generateExceptionFromHttpResponse({
    required int statusCode,
    required http.Response response,
    required String defaultMessage,
  }) {
    try {
      final json = parseJsonMap(response.body);

      // hasFaqフラグはデフォルトfalseで設定し、レスポンスにhasFaqが存在すれば、その値で上書き
      final hasFaq = json['hasFaq'] ?? false;

      // response.bodyがjson形式の場合はApiClientExceptionを返す
      return ApiClientException(
        statusCode: statusCode,
        message: json['errorMessage'] as String,
        code: json['errorCode'] as String,
        hasFaq: hasFaq as bool,
        baseDate: response.headers['x-base-date'],
        baseTime: response.headers['x-base-time'],
      );
    } catch (error) {
      // response.bodyがjson形式でない場合はデフォルトメッセージを設定したExceptionを返す
      return Exception('$statusCode $defaultMessage');
    }
  }
}

/// ApiClient独自例外
class ApiClientException implements Exception {
  final int statusCode;
  final String message;
  final String? code;
  final bool hasFaq;
  final String? baseDate;
  final String? baseTime;

  const ApiClientException({
    required this.statusCode,
    required this.message,
    this.code,
    required this.hasFaq,
    this.baseDate,
    this.baseTime,
  });

  @override
  String toString() {
    return 'ApiClientException: $statusCode [${code ?? '-'}] $message $baseDate $baseTime';
  }
}

/// 基盤系エラー画面を表示する場合の例外
class SorryScreenException implements Exception {
  const SorryScreenException();
}
