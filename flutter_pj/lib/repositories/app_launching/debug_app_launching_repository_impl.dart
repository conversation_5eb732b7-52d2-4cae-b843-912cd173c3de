import 'package:dtp_app/business_logics/interfaces/app_launching_repository.dart';
import 'package:dtp_app/datas/remote_config/force_update/response/force_update_config_response.dart';
import 'package:dtp_app/models/app_information/app_information.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';

class DebugAppLaunchingRepositoryImpl implements AppLaunchingRepository {
  bool _isFirstTimeStartup = true;
  bool _isUsingBiometrics = false;
  bool _hasLoggedInWithIDPW = true;
  String _valueDoorId = '1234567890';
  String _valueDoorPassword = 'password';

  @override
  Future<ForceUpdateConfig> checkForceUpdate({
    required AppInformation appInformation,
  }) async {
    await Future<void>.delayed(const Duration(seconds: 1));

    const force = Force(
      minVersion: '0.0.3',
      //message: 'ストアでインストールをしてください',
      message: null,
      //buttonLink: 'https://apps.apple.com/jp/app/594457652',);
      buttonLink:
          'https://play.google.com/store/apps/details?id=jp.co.smbc.direct',
    );

    const optional = Optional(
      newVersion: '0.0.4',
      //message: 'ストアでインストールをしてください',
      message: null,
      //buttonLink: 'https://apps.apple.com/jp/app/594457652',);
      buttonLink:
          'https://play.google.com/store/apps/details?id=jp.co.smbc.direct',
    );

    const updateAlert = UpdateAlert(force: force, optional: optional);

    const forceUpdateConfigResponse =
        ForceUpdateConfigResponse(updateAlert: updateAlert);

    const updateType = UpdateType.none;
    // const updateType = UpdateType.force;
    // const updateType = UpdateType.optional;
    // const updateType = UpdateType.error;

    ForceUpdateConfig forceUpdateConfig;
    switch (updateType) {
      case UpdateType.none:
        // アップデート不要
        forceUpdateConfig = const ForceUpdateConfig(
          updateType: UpdateType.none,
        );
        break;
      case UpdateType.force:
        // 強制アップデート
        forceUpdateConfig = const ForceUpdateConfig(
          updateType: UpdateType.force,
          forceUpdateConfigResponse: forceUpdateConfigResponse,
        );
        break;
      case UpdateType.optional:
        // 任意アップデート
        forceUpdateConfig = const ForceUpdateConfig(
          updateType: UpdateType.optional,
          forceUpdateConfigResponse: forceUpdateConfigResponse,
        );
        break;
      case UpdateType.error:
        // エラー
        forceUpdateConfig = const ForceUpdateConfig(
          updateType: UpdateType.error,
          updateError: UpdateError(
            errorCode: '1234',
            message: '大変申し訳ありません。しばらく時間をおいてから再度お試しください。',
          ),
        );
        break;
    }
    return forceUpdateConfig;
  }

  @override
  Future<bool> isFirstTimeStartup() {
    return Future.value(_isFirstTimeStartup);
  }

  @override
  Future<bool> firstTimeStartupDone() {
    _isFirstTimeStartup = false;
    return Future.value(true);
  }

  @override
  Future<void> deleteAllSecureData() async {
    // 初期値をセット
    _setSecureData(
      _isUsingBiometrics,
      _hasLoggedInWithIDPW,
      _valueDoorId,
      _valueDoorPassword,
    );
    await Future<void>.delayed(const Duration(microseconds: 100));
    // 削除
    _isUsingBiometrics = false;
    _hasLoggedInWithIDPW = false;
    _valueDoorId = '';
    _valueDoorPassword = '';
  }

  void _setSecureData(
    bool isUsingBiometrics,
    bool hasLoggedInWithIDPW,
    String valueDoorId,
    String valueDoorPassword,
  ) {
    _isUsingBiometrics = isUsingBiometrics;
    _hasLoggedInWithIDPW = hasLoggedInWithIDPW;
    _valueDoorId = valueDoorId;
    _valueDoorPassword = valueDoorPassword;
  }
}
