import 'package:dtp_app/business_logics/interfaces/app_launching_repository.dart';
import 'package:dtp_app/datas/remote_config/force_update/force_update_config_manager.dart';
import 'package:dtp_app/datas/remote_config/force_update/response/force_update_config_response.dart';
import 'package:dtp_app/datas/secure_storage/secure_storage_manager.dart';
import 'package:dtp_app/datas/shared_preferences/shared_preferences_manager.dart';
import 'package:dtp_app/models/app_information/app_information.dart';
import 'package:dtp_app/models/force_update_config/force_update_config.dart';
import 'package:dtp_app/utils/error_info.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:version/version.dart';

class AppLaunchingRepositoryImpl implements AppLaunchingRepository {
  AppLaunchingRepositoryImpl(
    this._forceUpdateConfigManager,
    this._spManager,
    this._ssManager,
  );

  final ForceUpdateConfigManager _forceUpdateConfigManager;
  final SharedPreferencesManager _spManager;
  final SecureStorageManager _ssManager;

  @override
  Future<ForceUpdateConfig> checkForceUpdate({
    required AppInformation appInformation,
  }) async {
    try {
      return await appInformation.map(
        undefined: (_) async {
          throw StateError('undefined OS');
        },
        ios: (appInfo) async {
          // iOSの場合
          final response = await _forceUpdateConfigManager.loadIOS();
          return toConfig(
            appCurrentVersion: appInfo.versionName,
            response: response,
          );
        },
        android: (appInfo) async {
          // Androidの場合
          final response = await _forceUpdateConfigManager.loadAndroid();
          return toConfig(
            appCurrentVersion: appInfo.versionName,
            response: response,
          );
        },
      );
    } catch (e) {
      // 通信エラーが発生した場合（エラーダイアログ表示）
      return ForceUpdateConfig(
        updateType: UpdateType.error,
        updateError: UpdateError(
          errorCode: ErrorInfo.forceUpdateConnectionErrorCode,
          message: ErrorInfo.getErrorMessage(
            ErrorInfo.forceUpdateConnectionErrorCode,
          ),
        ),
      );
    }
  }

  ForceUpdateConfig toConfig({
    required String appCurrentVersion,
    required ForceUpdateConfigResponse response,
  }) {
    try {
      final force = response.updateAlert.force;
      final optional = response.updateAlert.optional;

      // レスポンスのデータが欠損している場合（エラーダイアログ表示）
      if (force == null ||
          optional == null ||
          force.minVersion == null ||
          optional.newVersion == null) {
        return ForceUpdateConfig(
          updateType: UpdateType.error,
          updateError: UpdateError(
            errorCode: ErrorInfo.brokenResponseErrorCode,
            message: ErrorInfo.getErrorMessage(
              ErrorInfo.brokenResponseErrorCode,
            ),
          ),
        );
      }

      final currentVersion = Version.parse(appCurrentVersion);
      final latestVersionOfForce = Version.parse(force.minVersion!);
      final latestVersionOfOptional = Version.parse(optional.newVersion!);

      // 強制の場合（強制アップデートダイアログ表示）
      if (currentVersion < latestVersionOfForce) {
        return ForceUpdateConfig(
          updateType: UpdateType.force,
          forceUpdateConfigResponse: response,
        );
      }

      // 任意の場合（任意アップデートダイアログ表示）
      if (currentVersion < latestVersionOfOptional) {
        return ForceUpdateConfig(
          updateType: UpdateType.optional,
          forceUpdateConfigResponse: response,
        );
      }

      // 強制でも任意でもない場合（アップデートダイアログ非表示）
      return const ForceUpdateConfig(
        updateType: UpdateType.none,
      );
    } catch (e) {
      // 内部処理エラーが発生した場合（エラーダイアログ表示）
      return ForceUpdateConfig(
        updateType: UpdateType.error,
        updateError: UpdateError(
          errorCode: ErrorInfo.forceUpdateInternalErrorCode,
          message: ErrorInfo.getErrorMessage(
            ErrorInfo.forceUpdateInternalErrorCode,
          ),
        ),
      );
    }
  }

  @override
  Future<bool> isFirstTimeStartup() async {
    return await _spManager.isFirstTimeStartup.load();
  }

  @override
  Future<bool> firstTimeStartupDone() async {
    return await _spManager.isFirstTimeStartup.save(false);
  }

  @override
  Future<void> deleteAllSecureData() async {
    // セキュアデータを全削除する
    await _ssManager.deleteAllData();
  }
}

final appLaunchingRepositoryProvider = Provider<AppLaunchingRepository>((ref) {
  final config = ref.watch(forceUpdateConfigProvider);
  final sp = ref.watch(spManagerProvider);
  final ss = ref.watch(secureStorageManagerProvider);
  return AppLaunchingRepositoryImpl(
    config,
    sp,
    ss,
  );
});
