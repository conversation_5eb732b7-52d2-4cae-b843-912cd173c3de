import 'dart:io';

import 'package:device_preview/device_preview.dart';
import 'package:dtp_app/app.dart';
import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/build_config/dev.dart';
import 'package:dtp_app/build_config/env/dot_env_params.dart';
import 'package:dtp_app/build_config/pro.dart';
import 'package:dtp_app/build_config/stg.dart';
import 'package:dtp_app/flavor.dart';
import 'package:dtp_app/platforms/platform_method_channels.dart';
import 'package:dtp_app/repositories/app_launching/app_launching_repository_impl.dart';
import 'package:dtp_app/repositories/debug_repository_provider_overrides.dart';
import 'package:dtp_app/utils/log.dart';
import 'package:dtp_app/views/routers/app_router.dart';
import 'package:dtp_app/views/screens/background_image/background_image_screen.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

void main() async {
  final widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isIOS) {
    // iOSのクラウドバックアップを無効化する
    try {
      await disableIosCloudBackup();
    } catch (e) {
      // iOSネイティブ側の実装が不足している場合は無視する
      print('Warning: Could not disable iOS cloud backup: $e');
    }
  }

  // ビルドフレーバーを取得
  String flavorString = 'dev'; // デフォルト値
  try {
    flavorString = await loadAppFlavor();
  } catch (e) {
    // ネイティブ側の実装が不足している場合は無視する
    print('Warning: Could not load app flavor: $e');
  }
  final flavor = Flavor.from(flavorString);
  // スタブ利用フラグを取得
  const useStub = bool.fromEnvironment('useStub');
  // デバッグメニュー表示フラグを取得
  const allowDebugMenu = bool.fromEnvironment('allowDebugMenu');
  // ロギングフィルターフラグを取得
  const filterFlags = String.fromEnvironment('loggingFilter', defaultValue: '');
  // ログ1行表示フラグを取得
  const simpleMode =
      bool.fromEnvironment('loggingSimpleMode', defaultValue: false);

  if (filterFlags.isNotEmpty || simpleMode) {
    // ログの表示モードを設定
    setLoggingMode(filterFlags: filterFlags, simpleMode: simpleMode);
  }

  Log.i('flavor: $flavor\nuseStub: $useStub\nallowDebugMenu: $allowDebugMenu');

  // フレーバーごとに異なるenvファイルを読み込む
  try {
    // 環境ファイルの読み込みをスキップする
    print('Skipping environment file loading for now.');
    // 実際のアプリでは以下のようにファイルを読み込む
    // switch (flavor) {
    //   case Flavor.dev:
    //     await dotenv.load(fileName: 'dev.env');
    //     break;
    //   case Flavor.stg:
    //     await dotenv.load(fileName: 'stg.env');
    //     break;
    //   case Flavor.pro:
    //     await dotenv.load(fileName: 'pro.env');
    //     break;
    // }
  } catch (e) {
    // 環境ファイルの読み込みに失敗した場合は無視する
    print('Warning: Could not load environment file: $e');
  }

  // 空のMapを使用する（実際のアプリでは dotenv.env を使用）
  final envMap = <String, String>{};

  // 画面表示を縦方向に固定
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  // Firebase初期化を一時的にスキップ
  // await Firebase.initializeApp();
  print('Skipping Firebase initialization as requested.');

  final appOverrides = _overrides(
    flavor: flavor,
    envMap: envMap,
    useStub: useStub,
    allowDebugMenu: allowDebugMenu,
  );

  await _appInitializing(appOverrides);

  runApp(
    ProviderScope(
      overrides: appOverrides,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: BackgroundImageScreen(
          child: flavor.isDebug
              ? DevicePreview(
                  enabled: !kReleaseMode,
                  builder: (context) => const App(enableDevicePreview: true),
                )
              : const App(),
        ),
      ),
    ),
  );
}

/// 起動時引数をもとにProviderを上書きする
List<Override> _overrides({
  required Flavor flavor,
  required Map<String, String> envMap,
  required bool useStub,
  required bool allowDebugMenu,
}) {
  final overrides = <Override>[];

  if (useStub) {
    // リポジトリクラスをスタブに入れ替える
    overrides.addAll(debugRepositoryProviderOverrides);
  }

  if (allowDebugMenu) {
    // アプリ内ルーターをデバッグメニュー用に入れ替える
    overrides.add(appRouterProvider.overrideWithValue(const AppRouter.debug()));
  }

  // フレーバーに合わせてそれぞれのBuildConfigインスタンスを注入
  final buildConfig = switch (flavor) {
    Flavor.dev => Dev(DotEnvParams(envMap)),
    Flavor.stg => Stg(DotEnvParams(envMap)),
    Flavor.pro => Pro(DotEnvParams(envMap)),
  };
  overrides.add(buildConfigProvider.overrideWithValue(buildConfig));

  return overrides;
}

/// アプリの起動前に実行しておくべき処理
Future<void> _appInitializing(List<Override> overrides) async {
  final container = ProviderContainer(
    overrides: overrides,
  );

  final appLaunchRepository = container.read(appLaunchingRepositoryProvider);

  final isFirstTimeStartup = await appLaunchRepository.isFirstTimeStartup();

  if (isFirstTimeStartup) {
    // 初回起動時であれば、SecureStorageの中身を空にする
    // iOSの場合はアプリを削除してもKeyChainにデータが残り続けるため初回起動のタイミングで全削除を行う
    await appLaunchRepository.deleteAllSecureData();
  }

  container.dispose();
}
