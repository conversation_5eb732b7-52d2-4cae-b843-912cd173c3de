import 'package:device_preview/device_preview.dart';
import 'package:dtp_app/views/routers/app_router.dart';
import 'package:dtp_app/views/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// 画面遷移を監視
final routeObserver = RouteObserver();

class App extends ConsumerWidget {
  const App({
    super.key,
    this.enableDevicePreview = false,
  });

  final bool enableDevicePreview;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);

    return MaterialApp(
      locale: enableDevicePreview ? DevicePreview.locale(context) : null,
      builder: (context, child) {
        return MediaQuery(
          data:
              MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling),
          child: (enableDevicePreview
              ? DevicePreview.appBuilder(context, child)
              : child!),
        );
      },
      theme: theme,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      initialRoute: router.initialRoute,
      onGenerateRoute: router.onGenerateRoute,
    );
  }
}
