{"@@locale": "ja", "home": "ホーム", "forceUpdateDialogTitle": "新しいアップデートがあります。", "errorDialogTitle": "エラーコード:{errorCode}", "ok": "OK", "cancel": "キャンセル", "yes": "はい", "no": "いいえ", "defaultForceUpdateDialogMessage": "ストアでインストールをしてください。", "defaultOptionalUpdateDialogMessage": "ストアでインストールを行いますか？", "rootDetectionDialog": "root化検知ダイアログ", "jailbreakDetectionDialog": "jailbreak検知ダイアログ", "jailbreakDetection": "デバッグモードのため、本アプリはご利用いただけません。ストアよりアプリを再インストールしてください。", "forceUpdateDialog": "強制アップデートダイアログ", "optionalUpdateDialog": "任意アップデートダイアログ", "showStore": "Storeを表示する", "accountBalance": "口座残高", "total": "合計", "displaySettings": "表示設定", "incomeAndExpenditureIn7days": "直近7日間の入出金推移", "monday": "月", "tuesday": "火", "wednesday": "水", "thursday": "木", "friday": "金", "saturday": "土", "sunday": "日", "man": "万", "oku": "億", "cho": "兆", "lastUpdated": "最終更新", "yen": "¥", "yenJp": "円", "statement": "明細", "excessBalanceErrorMessage": "桁数超過エラー", "accountInquiry": "口座照会", "help": "ヘルプ", "helpIcon": "ヘルプアイコン", "notification": "お知らせ", "inquiry": "照会", "payment": "振込", "paymentMessage": "振込は三井住友銀行「法人用アプリ」から実施いただくことが可能です。", "task": "タスク", "myPage": "マイページ", "customerInformation": "お客さま情報", "idInformation": "ID情報", "idManagement": "ID管理", "userIdAddition": "利用者ID追加", "canOnlyBeOperatedOnPc": "PCでのみ操作が可能です。", "question": "よくあるご質問", "goToFaqTop": "FAQトップへ", "pleaseUseAPcForVariousProcedures": "その他、各種お手続の際はPCをご利用ください。", "faqAboutIdManagement": "ID管理に関するFAQ", "changeOrDeleteOfIdPermissions": "IDの権限変更・削除", "addOrReissueOfOneTimePassword": "ワンタイムパスワードの追加・再発行", "registrationOfWithdrawalProcessor": "出金手続者の登録", "faqAboutStatementsAndInquiries": "各種明細・照会に関するFAQ", "viewStatementsAndStatementsOfAccount": "振込明細・手数料計算書の閲覧", "issuanceOfBalanceCertificates": "残高証明書の発行", "customerInformationFaq": "お客さま情報に関するFAQ", "changeOrAuthorizationOfYourInformation": "お届け情報の変更・承認", "listOfProceduresInProgress": "お手続の進捗状況一覧", "inquiryOfContractDetails": "ご契約内容の照会", "requestOrDownloadOfDocuments": "各種お手続書類のご請求・ダウンロード", "management": "管理", "freeeLinkageAccount": "他の金融機関の口座・カード連携用ID管理", "freeeLinkageAccountMessage": "連携用IDの新規作成・権限設定ができます。（freeeに遷移します）", "creditCardStatementInquiry": "クレジットカード利用明細照会", "creditCardStatement": "クレジットカード利用明細", "switchCardNameAccordion": "カード名称アコーディオン切り替え", "switchTransactionDetailsAccordion": "取引内容アコーディオン切り替え", "approval": "承認", "creditCardNarrowDownModal": "クレジットカード絞り込み", "bankAccountDisplayConfig": "口座表示設定", "switchAccountDisplayFlag": "口座表示切り替え", "decide": "決定", "display": "表示", "hidden": "非表示", "amountDisplaySwitching": "金額表示切替", "displayName": "表示名", "reorder": "並び替え", "bankAccount": "口座", "displayNameUndefined": "未設定（15文字以内）", "errorAllSMBCAccountInvisible": "三井住友銀行の口座を1つ以上選択してください。", "errorAllOtherBankAccountInvisible": "連携口座を1つ以上選択してください。", "errorDisplaySMBCAccountExceeded": "三井住友銀行口座は最大5口座選択可能です。", "errorDisplayOtherBankAccountExceeded": "連携口座は最大15口座選択可能です。", "validationErrorLength15": "15文字以内で入力してください。", "validationErrorEmoji": "表示名は、漢字、ひらがな、カタカナ、英数字、記号で設定してください。", "incomeAndExpenditure": "入出金明細", "incomeAndExpenditureRefinement": "入出金絞込", "incomeAndExpenditureLinkedAccount": "入出金明細連携口座", "incomeAndExpenditureNarrowDown": "入出金明細絞り込み", "incomeAndExpenditureNarrowDownLinkedAccount": "入出金明細絞り込み連携口座", "fetchedDateTime": "照会日時", "periodTotal": "期間合計", "itemCountUnit": "件", "income": "入金", "expenditure": "出金", "detail": "詳細", "startingDate": "起算日", "tradingDate": "取引日", "tradingType": "取引区分", "tradingContent": "取引内容", "depositNumber": "預入番号", "referenceNumber": "照会番号", "balanceDetails": "残高詳細", "balance": "残高", "overdraftLimit": "貸越限度額", "accountInformation": "口座情報", "totalAccountBalance": "口座残高合計", "withdrawableBalance": "支払可能残高", "statementDetailsInquiry": "入出金明細", "switchAccordion": "アコーディオン切り替え", "cardDetail": "クレジットカード利用明細", "cardSwitch": "カード切替", "cardDetailTitle": "カード利用明細", "switchOver": "切り替える", "cardAdditionMessageInHomeScreen": "カードを追加する場合はホーム画面で操作してください", "back": "戻る", "login": "ログイン", "logout": "ログアウト", "logoutDialog": "ログアウト（ダイアログ）", "incomeAndExpenditureDetail": "入出金明細詳細", "incomeAndExpenditureInquiry": "入出金明細照会", "payerName": "依頼人名/", "payerNamePerCode": "依頼人名/\n依頼人コード", "clientName": "依頼人名", "remitterCode": "依頼人コード", "incomeAmount": "入金額", "expenditureAmount": "出金額", "checksIssuedByOtherBanksAmount": "内他店手形", "billAndCheckTypeName": "手形小切手区分/\n番号", "ediInfo": "EDI情報", "abstract": "摘要", "interestRate": "利率", "maturityDateAd": "満期日", "incomeAccount": "入金口座", "expenditureAccount": "出金口座", "startingDateTooltipMessage": "起算日とは、お取引の申し込みがあった日付です（土日祝日等を含む）。", "tradingDateTooltipMessage": "取引日とは、お取引の処理が完了した日付です（土日祝日等を除く）。", "startingDateTooltip": "起算日ツールチップ", "tradingDateTooltip": "取引日ツールチップ", "fullWidthHyphen": "ー", "accountInformationDetails": "口座情報詳細", "openAccountInformationDetails": "口座情報詳細開く", "closeAccountInformationDetails": "口座情報詳細閉じる", "all": "すべて", "orderByNewTransactionDate": "新しい取引日順", "orderByOldTransactionDate": "古い取引日順", "orderByDescendingAmount": "金額が大きい順", "orderByAscendingAmount": "金額が小さい順", "orderByNewDateOfUse": "新しい利用日順", "orderByOldDateOfUse": "古い利用日順", "sortCriteriaButtonOrderByNewTransactionDate": "並び替え基準（新しい取引順）", "sortCriteriaButtonOrderByOldTransactionDate": "並び替え基準（古い取引順）", "sortCriteriaButtonOrderByDescendingAmount": "並び替え基準（金額が大きい順）", "sortCriteriaButtonOrderByAscendingAmount": "並び替え基準（金額が小さい順）", "sortCriteriaButtonOrderByNewDateOfUse": "並び替え基準（新しい利用日順）", "sortCriteriaButtonOrderByOldDateOfUse": "並び替え基準（古い利用日順）", "gamenBangou": "画面番号：", "kanriSenyouID": "管理専用ID", "kanriSenyouIDFuku": "管理専用ID（副）", "riyoushaID": "利用者ID", "denshiKeiyakuSenyouID": "電子契約専用ID", "valueDoorID": "ValueDoorID", "mailAddress": "メールアドレス", "passwordNinshou": "パスワード認証", "icCardNinshou": "ICカード認証", "representativeAccount": "申込代表口座", "accountType": "科目", "accountNumber": "口座番号", "termsOfUse": "SMBC BUSINESS利用規定", "auth": "認可", "privacyPolicy": "プライバシーポリシー", "license": "ライセンス", "logoutDialogMessage": "ログアウトしますか？", "futsu": "普通", "touza": "当座", "nouzeiJunbiYokin": "納税準備預金", "tyochikuYokin": "貯蓄預金", "tsuchiYokin": "通知預金", "teikiYokin": "定期預金", "tsumitateTeikiYokin": "積立定期預金", "teikiTsumikin": "定期積金", "sonota": "その他", "narrowDown": "絞り込み", "unfocusKeyboard": "キーボードフォーカス解除", "narrowingDown": "絞り込む", "dateSegment": "日付区分", "selectDate": "日付選択", "notSpecified": "指定しない", "period": "期間", "dateOfUse": "利用日", "usageDetails": "利用明細", "cash": "現金", "exchange": "交換", "transfer": "振替", "continuation": "継続", "otherBanksTicketDeposit": "他店券入金", "correction": "訂正", "densai": "でんさい", "caution": "※ {content}", "enterHalf": "半角ｶﾅで入力してください。", "enterEightHalfNumbers": "半角数字8桁で入力してください。", "amount": "金額", "reset": "リセット", "example": "例：{content}", "wakakusaHanako": "ﾜｶｸｻﾊﾅｺ", "zero": "0", "wakakusaShouji": "若草商事", "fiveHundredMillion": "500,000,000", "oneToEight": "********", "narrowDownResult": "絞り込み結果", "changeConditionsTryAgain": "条件を変えて再度実行してください。", "changeConditions": "絞り込み条件を変更", "enterNotEmoji": "絵文字は使用できません", "enterDigit": "数字を入力してください。", "enterNumberGreaterThanOrEqualTo0": "0以上の数字を入力してください。", "enterHalfWidthSpaceWordFirst": "『-（半角マイナス）』は一文字目のみ使用可能です。", "setAnAmountGreaterThanTheLowerLimit": "下限の金額以上の数字を入力してください。", "enter8HalfWidthDigits": "半角数字8桁で入力してください。", "transactionAccountInquiry": "取引口座照会", "noName": "名称未設定", "smbcBankAccount": "三井住友銀行口座", "otherBankAccount": "連携口座", "smbcBankAccountDescription": "ホームに表示する口座の選択・並び替えと、口座名称の付与ができます。", "otherBankAccountDescription": "ホームに表示する口座の選択・並び替えができます。", "otherBankAccountsEmptyDescription": "連携口座はホーム画面から追加できます。", "otherBankAccountLinkedDescription": "連携設定はホーム画面から実施できます。", "save": "保存", "checkList": "確認事項", "jpkiCheckList": "同意事項", "contentAgreementMessage": "以下の内容を確認し、同意してください。", "termOfService": "SMBC BUSINESS利用規定", "jpkiTermOfService": "SMBC BUSINESS利用規定", "explainTerm": "規定に同意いただいた場合、SMBC BUSINESSに含まれるサービスに関するご案内メールをお送りします。配信停止は", "unsubscribeHere": "こちら", "aboutPersonalInformation": "個人情報の取扱いについて", "term": "利用規定同意", "jpkiTerm": "利用規定同意（本人確認）", "pushTermWebViewScreen": "利用規定WebView画面遷移", "agree": "同意して進む", "disagree": "同意しない", "corporateBankAccountMessage": "法人口座開設のお手続中の方", "corporateBankAccountDetailMessage": "法人口座開設について、くわしくは", "smbcBusinessInfoMessage": "複数の銀行口座や\nビジネスカードをまとめて管理できます", "privacyPolicyMessage1": "本サービスの初回ご利用時、", "privacyPolicyMessage2": "個人情報の取扱いについ\nて", "privacyPolicyMessage3": "の内容を理解し同意したものとみなします。", "next": "次へ", "relatedInformation": "関連情報", "useCookie": "クッキー（<PERSON><PERSON>）等の使用について", "creditCard": "クレジットカード", "freeeLink": "freee連携", "linkingWithFreee": "クレジットカードを表示", "otherLinkingWithFreee": "他の金融機関口座を表示", "addAccount": "口座を追加", "linksToOtherBanksExplain3": "更新する", "linkedAccount": "連携口座", "relatedFAQ": "関連するFAQは", "here": "こちら", "sessionTimeoutErrorTitle": "一定時間操作がなかったため、自動でログアウトいたしました", "sessionTimeoutErrorMessage": "引き続きご利用になる場合は、再度ログインしてください。", "sessionTimeoutErrorButton": "ホームに戻る", "sorryScreenErrorTitle": "ただいま繋がりにくい状態となっています。", "sorryScreenErrorMessage": "しばらく時間をあけてからアクセスしてください。振込をお急ぎの場合は、三井住友銀行の「法人用アプリ」をご利用ください。", "sorryScreenErrorButton1": "アプリを閉じる", "sorryScreenErrorButton2": "法人用アプリへ", "okSorryScreen": "Sorry画面 OK", "cancelSorryScreen": "Sorry画面 キャンセル", "failedToProcess": "処理に失敗しました", "totalUsageAmount": "利用額合計", "addCard": "クレジットカードを追加", "noTransactionHistoryFound": "該当する入出金明細がありません", "grantAccess": "アクセスを許可する", "confirmFreee": "確認する", "confirmFreeeOtherBank": "確認する(他行)", "confirmFreeeCreditCard": "確認する(クレジットカード)", "freeeUpdate": "更新する", "reConnectFreee": "再連携する", "reConnectFreeeButton": "freee再連携", "identificationDocument": "本人確認書類を選択してください", "identificationReferenceNumber": "お手続番号を入力してください", "readingQrCodeWithCamera": "カメラで二次元コードを読み取る", "enterSignatureCertPassword": "署名用電子証明書用暗証番号を入力してください", "passwordComplexityWarning": "半角英数字（大文字）6〜16桁で入力してください。", "passwordComplexityRequirementStart": "※ 半角英数字（大文字）", "passwordComplexityRequirementMiddle": "6〜16桁", "passwordComplexityRequirementEnd": "で入力してください。", "notFourDigitCertPassword": "※ 4桁の利用者証明用電子証明書暗証番号ではありません。", "otherIdentityVerificationMethod": "他の方法で本人確認をする", "moveToExternalSite": "外部サイトに移動します", "confirmExternalSiteNavigation": "よろしいですか？", "urlOpenErrorMessage": "URLを開けませんでした。", "myNumberCard": "マイナンバーカード", "togglePasswordVisibility": "パスワード表示切り替え", "cardNotReadingExplain": "上記を試してもエラーになる場合は", "startReading": "読み取り開始", "cardReaderSetup": "読み取りの準備ができました", "myNumberCardHoldUp": "マイナンバーカードをかざしてください。", "cardReaderReading": "読み取り中", "myNumberCardNotMove": "動かさず、しばらくお待ちください。", "cardReaderError": "読み取りに失敗しました", "cardReaderErrorButton": "読み取りをやり直す", "cardReaderSuccess": "読み取りが完了しました。", "identification": "本人確認", "identificationDocumentScreen": "証明書選択", "passwordInputScreen": "マイナンバーカードパスワード入力", "myNumberCardReadScreen": "カード読み取り", "passwordInputExplanationScreen": "署名用電子証明書暗証番号説明", "ekycExplanationScreen": "eKYC概要説明", "cardReaderSetupModal": "カード読み取り準備完了モーダル", "cardReaderReadingModal": "カード読み取り中モーダル", "cardReaderSuccessModal": "カード読み取り完了モーダル", "scanAttentionInfoModal": "読み取り時の注意事項説明モーダル", "creditCardSwitchModal": "クレジットカード切替", "referenceNumberProcess": "お手続番号はご本人確認ページ内、またはご依頼のメール内に記載がございます。", "beforeLogin": "ログイン前ホーム", "beforeLoginFooter": "(ログイン前)", "pushExternalBrowserBanner": "外部ブラウザ遷移 (関連情報バナー)", "balanceUpdate": "残高更新", "reflectsOtherBankAccountSettings": "他行口座設定反映", "updateAccountInformationOfOtherBank": "口座情報更新（他行）", "incomeAndExpenditureOfOtherBank": "入出金明細（他行）", "reflectsCreditCardSettingsReflected": "クレジットカード設定反映", "updateCreditCardInformation": "クレジットカード情報更新", "footer": "フッター", "threeLinesIcon": "3本アイコン", "header": "ヘッダー", "displayYearMonth": "表示年月", "selectYearMonth": "年月選択", "selectYearMonthList": "年月リスト選択", "selectSortType": "ソートタイプ選択", "close": "閉じる", "batsu": "X（閉じる）", "close0CasesModal": "0件モーダルを閉じる", "enteringInfo": "情報入力", "name": "氏名", "lastNameKanji": "姓", "firstNameKanji": "名", "surnameKana": "セイ", "firstNameKana": "メイ", "lastName": "Last Name", "firstName": "First Name", "position": "役職名", "men": "男性", "address": "住所", "postCode": "郵便番号", "autofill": "自動入力", "exampleSeiKanji": "例：若草", "exampleMeiKanji": "例：太郎", "exampleSei": "例：ワカクサ", "exampleMei": "例：タロウ", "exampleLastName": "例：WAKAKUSA", "exampleFirstName": "例：TARO", "examplePosition": "例：代表取締役", "year": "年", "month": "月", "day": "日", "prefecture": "都道府県", "examplePostCode": "例：1040028", "examplePrefecture": "例：東京都", "streetAddress": "町名・番地・号・建物名", "cityName": "市区町村", "exampleCityName": "例：中央区", "exampleStreetAddress": "例：八重洲　２丁目　SMBCビル１０階", "send": "送信する", "birthDay": "生年月日", "requiredError": "を入力してください。", "requiredErrorForButton": "を選択してください。", "validationErrorFullWidthKana": "は全角で入力してください。", "validationErrorFullWidthKatakana": "は全角カナで入力してください。", "validationErrorHalfWidthNumber": "は半角数字で入力してください。", "validationErrorHalfWidthAlphanumericUpper": "は半角英大文字で入力してください。", "validationErrorHalfWidthAlphanumericWithSymbol": "は半角英数記号で入力してください。", "validationErrorMixedAlphanumeric": "は半角の英字・数字を両方含む文字で入力してください。", "validationErrorNoneVdIdError": "ValueDoorIDを入力してください。", "validationErrorNonePasswordError": "パスワードを入力してください。", "validationErrorIsVdTypeError": "入力されたValueDoorIDまたはパスワードが正しくありません。", "validationErrorNoneDtpIdError": "ID（メールアドレス）を入力してください。", "validationErrorIsDtpTypeError": "入力されたID（メールアドレス）またはパスワードが正しくありません。", "validationErrorIsDtpIdAndPasswordBrank": "ID（メールアドレス）とパスワードを入力してください。", "validationErrorIsVdIdAndPasswordBrank": "ValueDoorIDとパスワードを入力してください。", "emailStructureError": "の形式が正しくありません。", "emailCorrelationError": "メールアドレスが一致していません。", "mobileNumberStructureError": "携帯電話番号の形式が正しくありません。", "addressError": "該当する郵便番号が見つかりませんでした。郵便番号を確認して再度検索を行ってください。", "dateError": "には存在する日付を入力してください。", "sumMaxAddressLength": "都道府県・市区町村、番地・号、マンション名の合計", "alphabetDigitErrorFirstHalf": "が入力可能な文字数を超えています。", "alphabetDigitErrorSecondHalf": "文字以下で入力してください。", "digitError": "{itemName}は{digit}桁で入力してください。", "referenceNumberDigitError": "お手続番号は{minLength}桁もしくは{maxLength}桁の数字を入力してください。", "emailDomainError": "入力いただいたメールアドレスは使用できません。", "validationErrorPasswordEquals": "パスワードが一致していません", "identityReferenceNumber": "お手続き番号入力", "identifyQrReader": "QRコード読み取り", "accountOpeningSubmitted": "口座開設申込済み", "navigateToLoginScreen": "ログイン画面へ", "accountOpeningSubmittedTitle1": "口座開設申込済みです", "accountOpeningSubmittedTitle2": "審査完了までお待ちください", "accountOpeningSubmittedMessage1": "口座開設後の方は、「SMBC BUSINESS」アプリのご利用が可能です。", "accountOpeningSubmittedMessage2": "ログイン画面より以下のいずれかの方法でログインしてください。", "accountOpeningSubmittedMessage3": "お申込時に設定いただいた", "accountOpeningSubmittedMessage4": "「メールアドレス」と「パスワード」", "accountOpeningSubmittedMessage5": "「ValueDoorID」と「パスワード」", "verifiedUser": "本人確認実施済み", "verifiedUserTitle1": "本人確認実施済みです", "noAccountErrorMessage": "口座情報へのアクセスが許可されていません。", "variousInfoRegister": "各種ご登録", "register": "登録", "passwordExpirationNotice": "パスワード有効期限超過のお知らせ", "useBiometric": "生体認証を利用", "notUseBiometric": "生体認証を利用しない", "loginBiometric": "生体認証でログイン", "dtpLogin": "DTPログイン", "vdLogin": "VDログイン", "passwordChange": "パスワード変更", "newPassword": "新しいパスワード", "idaasResetPWInputEmailWebView": "IDaaS PW再設定メールアドレス入力WebView", "idaasResetPWWebView": "IDaaS PW再設定WebView", "idaasResetEmailWebView": "IDaaS メールアドレス変更WebView", "otpInputWebView": "OTP認証WebView", "idaasWebView": "IDaaS WebView", "freeeWebView": "freeeサインアップ WebView", "confirmPassword": "新しいパスワード（確認用）", "registration": "登録", "okLoginFailure": "OK（ログイン失敗）", "change": "変更", "rest": "残り", "count": "回", "ekycExplanationTitle": "運転免許証と顔写真の撮影で本人確認をします", "passwordInputExplanationTitle": "次画面で署名用電子証明書用暗証番号の入力が必要です", "passwordInputExplanationContent": "ご自身が、マイナンバーカードを市区町村の窓口で受け取った際に設定した、6〜16桁の英大文字と数字を組み合わせた暗証番号です。", "passwordForgotTitle": "署名用電子証明書用暗証番号を忘れた場合は", "jpkiErrorPattern1Title": "現在サービスが利用できません\n", "jpkiErrorPattern1Message": "利用時間外のためご利用できません。時間をおいて再度お試しください。", "jpkiErrorPattern1Action1": "他の方法で本人確認をする", "jpkiErrorPattern1Action2": "はじめに戻る", "jpkiErrorPattern2Title": "処理に失敗しました\n", "jpkiErrorPattern2Message": "マイナンバーカードをご確認頂き、再度読み込みをお試し頂くか、他の方法で本人確認をお試しください。", "jpkiErrorPattern2Action1": "もう一度カードを読み取る", "jpkiErrorPattern2Action2": "他の方法で本人確認をする", "jpkiErrorPattern5Title": "パスワードが間違っています", "jpkiErrorPattern5Message": "続けて5回間違えるとパスワードがロックされ、お住いの市区町村窓口でのお手続きが必要になります。本人確認のお手続きは、運転免許証と顔写真を複数枚撮影する方法でも可能です。", "jpkiErrorPattern5Action1": "パスワードを再入力", "jpkiErrorPattern5Action2": "他の方法で本人確認をする", "jpkiErrorPattern6Title": "マイナンバーカードがロックされています", "jpkiErrorPattern6Message": "パスワード入力失敗により、マイナンバーカードがロックされています。お住いの市区町村窓口でロック解除の手続きを行ってください。", "jpkiErrorPattern6Action1": "他の方法で本人確認をする", "jpkiErrorPattern6Action2": "はじめに戻る", "jpkiErrorPattern9Title": "処理に失敗しました\n", "jpkiErrorPattern9Message": "処理中に問題が発生しました。時間をおいて再度お試しください。", "jpkiErrorPattern9Action": "OK", "jpkiErrorPattern10Title": "本人確認が未完了です\n", "jpkiErrorPattern10Message": "画面を閉じると入力内容が破棄されます。閉じてもよろしいですか？", "jpkiErrorPattern10Action1": "入力を続ける", "jpkiErrorPattern10Action2": "破棄して閉じる", "jpkiErrorPattern11Title": "メンテナンス中です\n", "jpkiErrorPattern11Message": "ただいま当該業務サービスはメンテナンス中のためお取り扱いできません。", "jpkiErrorPattern12Title": "NFC設定がオフになっています\n", "jpkiErrorPattern12Message": "端末のNFC設定をオンにしてから再度お試しください。", "jpkiErrorPattern13Title": "ただいま繋がりにくい状態となっています\n", "jpkiErrorPattern13Message": "しばらく時間をあけてアクセスしてください。", "jpkiErrorPattern13ButtonMessage": "アプリを閉じる", "identificationAppBarTitle": "本人確認", "identityDocumentScreenTitle": "マイナンバーカードを読み取り本人確認をします", "identityDocumentScreenNotHaveCard": "マイナンバーカードをお持ちでない場合", "identityDocumentScreenCatchphrase": "ICチップから情報を読みとることで完了します。", "jpkiCardReaderScreenContentTitle": "マイナンバーカードを読み取ります", "jpkiCardReaderScreenContentExplanation": "以下の情報をマイナンバーカードから読み取り、三井住友銀行に送信します。", "jpkiNotesOnReading": "読み取り時の注意事項", "jpkiPersonalInfo": "氏名、生年月日、性別、住所", "nameScreen": "本人情報（名前）入力画面", "ekycEnteringNameMessage": "氏名の漢字・フリガナ・英字を入力してください", "enterRegisteredNameMessage": "※ 戸籍上の氏名を入力してください。", "jpkiEnteringNameMessage": "氏名のフリガナを入力し、英字を確認してください", "checkBoxMessage": "英字表記はヘボン式で表示されます。", "checkBoxMessage2": "変更が必要な場合はチェックボックス", "checkBoxMessage3": "を押して編集してください。", "positionScreen": "本人情報（役職）入力画面", "dateOfBirthScreen": "本人情報（生年月日）入力画面", "enterPosition": "役職名を入力してください", "enterDateOfBirth": "生年月日を入力してください", "enterValidDOB": "生年月日を正しく入力してください", "enterAddress": "ご自宅住所を入力してください", "enterPostcode": "郵便番号を入力してください", "dateOfBirth": "YYYY/MM/DD", "addressScreen": "本人情報（自宅）入力画面", "clearSearchAddressAPIError": "エラークリア (住所検索)", "postCodeScreen": "郵便番号入力画面", "mobileNumber": "携帯電話番号", "enterHalfWidthNumberWithoutHyphen": "※ ハイフンなしでご入力ください。", "enterLisenceAddress": "※ 運転免許証に記載のご住所を入力してください。", "finished": "完了", "finishedToSend": "送信完了", "wellDone": "本人確認が完了しました。", "continueOnWeb": "申込TOP画面に戻り、お手続を再開してください。", "noteSymbol": "※", "continueOnWebNote": "本人確認のSTEPが完了にならない場合は申込TOP画面をリロードしてください。", "continueOnWebIsBeneficiary": "法人口座開設のお申込されている方へ、完了した旨をお伝えください。", "finishedTosendInfoScreen": "本人情報送信完了画面(手続き者)", "finishedTosendInfoBeneficiaryScreen": "本人情報送信完了画面(実質的支配者)", "settings": "設定", "idLinkageSettings": "ID管理画面", "unassociated": "未連携", "digitalTouchPointIDMessage": "法人向けの複数のサービスで使える共通IDです。", "valueDoorIDMessage": "法人インターネットバンキングがご利用いただけるIDです。", "idMailAddress": "ID（メールアドレス）", "idMailAddressExample": "例：<EMAIL>", "password": "パスワード", "idLinkage": "IDを連携する", "forgotPassword": "パスワードをお忘れの場合", "doNotHaveAnId": "IDをお持ちでない方", "unlinked": "連携解除", "unlinkedDialog": "連携解除（ダイアログ）", "unlinkedConfirmDialogMessage": "ID（メールアドレス）の連携を解除します。よろしいでしょうか？", "unlinkedCompleteDialogMessage": "連携解除が完了しました。ログアウトし、ValueDoorIDでログインする画面に戻ります。", "welcomeBack": "おかえりなさい", "freeeLinkageCompletionQuestion": "freeeとの連携は完了しましたか。", "notCompleted": "完了していない", "linkageNotCompletedMessage": "連携設定が完了していません", "checkFreeeSettingsMessage": "freeeの設定画面をご確認いただくか、ホーム画面からもう一度やり直してください。", "valueDoor": "ValueDoor", "cardReadingPositionExplain": "機種ごとのカード読み取り位置", "jpkiAccessoryInfo": "スマホカバー、充電器、イヤホン、取り付け型リングを外してください。", "jpkiDeskInfo": "金属製の机の上に置かないでください。", "jpkiSmartPhoneHoldUpInfo": "スマートフォンをかざす位置を変えてお試しください。", "jpkiSmartPhoneWaitInfo": "スマートフォンを密着させ、読み取れるまでしばらくお待ちください。", "jpkiWifiInfo": "Wi-Fi機能をオフにしてください。（モバイル回線や別のネット回線を利用してください。）", "jpkiSmartPhoneOsUpdateInfo": "マイナポータルアプリ、ブラウザアプリ、端末のOSのアップデートを行ってください。", "jpkiCardCheckInfo": "カードが磁気不良になっていないかご確認ください。", "sendingInfo": "情報を送信中です", "freeeLinkCompleted": "完了（freee連携）", "freeeLinkNotCompleted": "完了していない（freee連携）", "freeeLinkClose": "閉じる（freee連携）", "freeeLinkageModalNext": "次へ（freee連携モーダル）", "ekycWebViewNext": "次へ進む", "freeeLinkageModalClose": "閉じる（freee連携モーダル）", "jpkiAndroidCardReaderInfo": "Androidの場合、読み取り位置は機種により異なるため、下記リンクを参照ください。", "otpEnabled": "OTP有効化", "otpEnabledComplete": "OTP有効化完了", "otp": "ワンタイムパスワード", "openLibrary": "ライブラリ起動", "identificationOpeningAcount": "本人確認へ", "creditCardExpenseDisplaySwitching": "クレジットカード利用額表示切替", "dtpIdCreationErrorDialogTitle": "処理に失敗しました", "dtpIdCreationErrorDialogTitlePositiveButton": "SMBC BUSINESSのWEBへ", "dtpIdCreationErrorDialogTitleNegativeButton": "ログイン画面に戻る", "dtpIdCreationErrorDialogPositive": "SMBC BUSINESSのWEBへ_ダイアログ", "dtpIdCreationErrorDialogNegative": "ログイン画面に戻る_ダイアログ", "noCreditCardStatementFound": "該当する利用明細がありません", "updateScreen": "画面を更新いたします", "updateScreenDescription": "freeeでの設定が完了した後、画面を更新し設定した口座・クレジットカード情報が反映されているかご確認ください。", "update": "更新する", "closeAdditionAccount": "閉じる (口座を追加 更新処理)", "updateAdditionAccount": "更新する (口座を追加)", "closeAdditionCard": "閉じる (カードを追加 更新処理)", "updateAdditionCard": "更新する (カードを追加)", "okForMovingErrorWhileTransaction": "OK(OTP入力時の画面移動)", "digitalTouchPoint": "SMBC BUSINESS", "smbcBusiness": "SMBC BUSINESS", "vdInquiry": "ValueDoorIDに関するよくある問い合わせ", "loginVDID": "ValueDoorIDでログイン", "digitalTouchPointLogin": "SMBC BUSINESS IDでログイン", "authenticationMethod": "認証方法", "electronicAndIcCardAuthenticaionAreNotAvailable": "※ 電子認証・ICカード認証はご利用いただけません。", "vdIdExample": "例：********90", "changeIdAndMailAddress": "変更（ID_メールアドレス）", "changePassword": "変更（パスワード）", "id": "ID", "idColon": "ID：", "asterisks8": "********", "dtpCreationRequired": "発行手続きが必要です", "createNewDtpId": "発行する", "checkBiometricsLogin": "次回以降生体認証を利用しますか?", "cancelBiometricLogin": "生体認証をオフにしますか?", "off": "オフ", "allowBiometricLogin": "生体認証でのログインを許可", "use": "利用する", "freeeLinkageFlowStep0Title": "口座やクレジットカード", "freeeLinkageFlowStep1and2Title": "口座やクレジットカードの\n表示方法", "freeeLinkageFlowStep0Message": "フリー株式会社のサービス（以下「freee」)に申し込むことで三井住友銀行以外の口座やクレジットカードをまとめて表示できます。", "freeeLinkageFlowStep1Title": "freeeの申込及び連携（無料）", "freeeLinkageFlowStep1BeginnerTitle": "はじめてfreeeを利用される方", "freeeLinkageFlowStep1BeginnerMessage": "本画面の遷移に従ってfreeeでアカウントを作成し、本サービスと連携してください。", "freeeLinkageFlowStep1ExistingTitle": "すでにfreeeをご利用の方", "freeeLinkageFlowStep1ExistingMessage": "本画面の遷移に従ってお持ちのfreeeのアカウントを本サービスに連携してください。", "freeeLinkageFlowStep2Title": "freeeで口座やクレジットカードを追加", "freeeLinkageFlowStep2Message": "freeeの設定画面から、口座やクレジットカードの情報を登録してください。", "freeeLinkageFlowStep2Attention1": "【ご留意事項】フリー株式会社より提供された情報（銀行口座明細、クレジットカード明細等）は、三井住友フィナンシャルグループのグループ各社に共有し、利用させていただきます。本事項は、", "freeeLinkageFlowStep2Attention2": "における情報共有の範囲に含みます。", "freeeLinkageFlowFinishButton": "freeeと連携する", "freeeLinkageFlowFinishButtonAttention": "外部サイトに移動します。既定のブラウザで開きます。", "freeeLinkageFlowEmployeeTitle": "口座やクレジットカード\nの表示方法", "employeeMessage": "フリー株式会社のサービス（以下「freee」）に申し込むことで三井住友銀行以外の口座やクレジットカードをまとめて表示できます。", "notHaveFreeeId": "freee IDをお持ちでない方", "ifYouHaveFreeeId": "freee IDをお持ちの方はログインへお進みください。", "freeeLoginButton": "freee IDでログインする", "employeeFreeeIdCreateRequest": "管理者にfreee IDの発行を依頼してください。\nくわしい手順は", "checkOverwriteIdPassword": "生体認証で利用するログイン情報を上書きしますか？", "overwriteOk": "OK（上書き保存）", "overwriteCancel": "キャンセル（上書き保存）", "toFaq": "FAQへ", "freeeFAQ": "こちら (freeeFAQ)", "bankAccountFAQ": "こちら (DTP専用FAQ)", "faq": "こちら (よくある質問)", "closeErrorDialog": "エラーダイアログ閉じる", "okErrorDialog": "エラーダイアログ OK", "clearFreeeLinksByClientIdError": "取引先ID紐付け確認エラー クリア", "switchIsEnglishEditMode": "英字表記編集チェックボックス 切り替え", "availableForFree": "無料でご利用いただけます", "web21AuthWebViewScreen": "web21認可画面", "idaasResetPWInputEmailWebViewScreen": "PW再設定メールアドレス入力画面", "idaasOtpInputWebViewScreen": "OTP入力画面", "idaasAccountCreateWebViewScreen": "アカウント作成画面", "idaasPasswordChangeWebViewScreen": "PW変更画面", "idaasEmailChangeWebViewScreen": "メールアドレス変更画面", "chatbot": "AIチャットボット", "chatbotButton": "AIチャットボット", "chatbotLoadingError": "チャットボットの読み込みに失敗しました", "chatbotRetry": "再試行"}