import 'package:dtp_app/build_config/build_config.dart';
import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/web_view/customized/allganize_chatbot_web_view_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'allganize_chatbot_web_view_screen_test.mocks.dart';

// モックの生成
@GenerateMocks([
  AnalyticsLogController,
  BuildConfig,
  InAppWebViewController,
])
void main() {
  late MockAnalyticsLogController mockAnalyticsLogController;
  late MockBuildConfig mockBuildConfig;
  late VoidCallback mockOnClose;

  setUp(() {
    mockAnalyticsLogController = MockAnalyticsLogController();
    mockBuildConfig = MockBuildConfig();
    mockOnClose = () {};

    // モックの振る舞いを設定
    when(mockBuildConfig.baseUrl).thenReturn('https://example.com');
  });

  // テスト用のラッパーウィジェット
  Widget createTestableWidget(Widget child) {
    return ProviderScope(
      overrides: [
        analyticsLogControllerProvider.overrideWithValue(mockAnalyticsLogController),
        buildConfigProvider.overrideWithValue(mockBuildConfig),
      ],
      child: MaterialApp(
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: child,
      ),
    );
  }

  group('AllganizeChatbotWebViewScreen', () {
    testWidgets('renders correctly with loading indicator', (WidgetTester tester) async {
      // InAppWebViewのモックは複雑なため、このテストではウィジェットが正しくレンダリングされることのみを確認
      await tester.pumpWidget(createTestableWidget(
        AllganizeChatbotWebViewScreen(onClose: mockOnClose),
      ));
      
      // 初期状態ではローディングインジケーターが表示されていることを確認
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      
      // AppBarが表示されていることを確認
      expect(find.byType(AppBar), findsOneWidget);
      
      // 閉じるボタンが表示されていることを確認
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('close button calls onClose callback and sends analytics logs', (WidgetTester tester) async {
      // ウィジェットをビルド
      bool closeCalled = false;
      mockOnClose = () {
        closeCalled = true;
      };

      await tester.pumpWidget(createTestableWidget(
        AllganizeChatbotWebViewScreen(onClose: mockOnClose),
      ));
      
      // ボタンをタップする前にモックの振る舞いを設定
      when(mockAnalyticsLogController.sendButtonLog(
        buttonName: anyNamed('buttonName'),
        screenName: anyNamed('screenName'),
      )).thenReturn(null);
      
      when(mockAnalyticsLogController.sendTrack(
        buttonName: anyNamed('buttonName'),
        screenIdNumber: anyNamed('screenIdNumber'),
      )).thenReturn(null);

      // 閉じるボタンをタップ
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // onCloseコールバックが呼ばれたことを確認
      expect(closeCalled, true);

      // FirebaseAnalyticsログが送信されたことを確認
      verify(mockAnalyticsLogController.sendButtonLog(
        buttonName: anyNamed('buttonName'),
        screenName: anyNamed('screenName'),
      )).called(1);

      // KARTEログが送信されたことを確認
      verify(mockAnalyticsLogController.sendTrack(
        buttonName: anyNamed('buttonName'),
        screenIdNumber: ScreenIdNumber.homeScreenId,
      )).called(1);
    });

    // 注：InAppWebViewの詳細なテストは、モックの複雑さとプラットフォーム依存の問題から、
    // 実際のアプリケーションでは統合テストやE2Eテストで行うことが多いです。
    // 以下は、JavaScriptハンドラーの基本的な動作をテストする例です。

    test('JavaScript handlers are registered correctly', () {
      // このテストはウィジェットテストではなく単体テストとして実装
      // 実際のInAppWebViewControllerの代わりにモックを使用
      
      // 注：このテストは概念的なものであり、実際の実装ではさらに複雑になる可能性があります
      final mockController = MockInAppWebViewController();
      
      // ハンドラーが登録されることを確認
      expect(() {
        mockController.addJavaScriptHandler(
          handlerName: 'onChatbotLoaded',
          callback: (args) {},
        );
        
        mockController.addJavaScriptHandler(
          handlerName: 'onChatbotError',
          callback: (args) {},
        );
        
        mockController.addJavaScriptHandler(
          handlerName: 'onChatbotRetry',
          callback: (args) {},
        );
      }, returnsNormally);
    });
  });

  group('showAllganizeChatbotDialog', () {
    testWidgets('shows dialog with AllganizeChatbotWebViewScreen', (WidgetTester tester) async {
      // ダイアログを表示するためのコンテキストが必要
      await tester.pumpWidget(createTestableWidget(
        Builder(
          builder: (BuildContext context) {
            return ElevatedButton(
              onPressed: () => showAllganizeChatbotDialog(context),
              child: const Text('Show Dialog'),
            );
          },
        ),
      ));

      // ボタンをタップしてダイアログを表示
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // ダイアログが表示されていることを確認
      expect(find.byType(Dialog), findsOneWidget);
      
      // ダイアログ内にAllganizeChatbotWebViewScreenが表示されていることを確認
      expect(find.byType(AllganizeChatbotWebViewScreen), findsOneWidget);
    });
  });
}
