import 'package:dtp_app/business_logics/analytics_log/analytics_log_controller.dart';
import 'package:dtp_app/utils/screen_id_number.dart';
import 'package:dtp_app/views/components/chatbot/chatbot_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

import 'chatbot_button_test.mocks.dart';

// モックの生成
@GenerateMocks([AnalyticsLogController])
void main() {
  late MockAnalyticsLogController mockAnalyticsLogController;

  setUp(() {
    mockAnalyticsLogController = MockAnalyticsLogController();
  });

  // テスト用のラッパーウィジェット
  Widget createTestableWidget(Widget child) {
    return ProviderScope(
      overrides: [
        analyticsLogControllerProvider.overrideWithValue(mockAnalyticsLogController),
      ],
      child: MaterialApp(
        localizationsDelegates: AppLocalizations.localizationsDelegates,
        supportedLocales: AppLocalizations.supportedLocales,
        home: Scaffold(
          body: child,
        ),
      ),
    );
  }

  group('ChatbotButton', () {
    testWidgets('renders correctly with default properties', (WidgetTester tester) async {
      // ウィジェットをビルド
      await tester.pumpWidget(createTestableWidget(const ChatbotButton()));
      await tester.pumpAndSettle();

      // ボタンが表示されていることを確認
      expect(find.byType(ChatbotButton), findsOneWidget);
      
      // アイコンが表示されていることを確認
      expect(find.byIcon(Icons.chat), findsOneWidget);
    });

    testWidgets('renders with custom properties', (WidgetTester tester) async {
      // カスタムプロパティでウィジェットをビルド
      await tester.pumpWidget(createTestableWidget(const ChatbotButton(
        backgroundColor: Colors.red,
        iconColor: Colors.yellow,
        size: 80.0,
      )));
      await tester.pumpAndSettle();

      // ボタンが表示されていることを確認
      expect(find.byType(ChatbotButton), findsOneWidget);
    });

    testWidgets('tapping the button sends analytics logs and shows dialog', (WidgetTester tester) async {
      // ウィジェットをビルド
      await tester.pumpWidget(createTestableWidget(const ChatbotButton()));
      await tester.pumpAndSettle();

      // ボタンをタップする前にモックの振る舞いを設定
      when(mockAnalyticsLogController.sendButtonLog(
        buttonName: anyNamed('buttonName'),
        screenName: anyNamed('screenName'),
      )).thenReturn(null);
      
      when(mockAnalyticsLogController.sendTrack(
        buttonName: anyNamed('buttonName'),
        screenIdNumber: anyNamed('screenIdNumber'),
      )).thenReturn(null);

      // ボタンをタップ
      await tester.tap(find.byType(ChatbotButton));
      await tester.pumpAndSettle();

      // FirebaseAnalyticsログが送信されたことを確認
      verify(mockAnalyticsLogController.sendButtonLog(
        buttonName: anyNamed('buttonName'),
        screenName: anyNamed('screenName'),
      )).called(1);

      // KARTEログが送信されたことを確認
      verify(mockAnalyticsLogController.sendTrack(
        buttonName: anyNamed('buttonName'),
        screenIdNumber: ScreenIdNumber.homeScreenId,
      )).called(1);

      // ダイアログが表示されることを確認（注：実際のダイアログ表示はモック化が必要かもしれません）
      // この部分はモックの複雑さによって異なるため、実装は省略
    });
  });
}
