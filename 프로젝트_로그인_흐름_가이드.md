# 프로젝트 전체 로그인 흐름 가이드

## 목차
1. [로그인 방식 개요](#1-로그인-방식-개요)
2. [상세 로그인 흐름](#2-상세-로그인-흐름)
3. [세션 관리 구조](#3-세션-관리-구조)
4. [인증 미들웨어](#4-인증-미들웨어)
5. [생체 인증 로그인](#5-생체-인증-로그인)
6. [OAuth 토큰 관리](#6-oauth-토큰-관리)
7. [위험도 판정 시스템](#7-위험도-판정-시스템)
8. [로그아웃 처리](#8-로그아웃-처리)
9. [세션 유효성 검증](#9-세션-유효성-검증)
10. [챗봇에서의 인증](#10-챗봇에서의-인증)

---

## 1. 로그인 방식 개요

이 프로젝트는 **2가지 로그인 방식**을 지원합니다:

### A. VDID 로그인 (ValueDoor ID)
- 기본적인 ID/Password 로그인
- ValueDoor 인증 시스템 사용
- SAML 기반 인증 프로세스

### B. DTPID 로그인 (DTP ID)
- 디지털 신원 인증 로그인
- DTP API를 통한 권한 취득
- 더 높은 보안 수준의 인증

---

## 2. 상세 로그인 흐름

### A. VDID 로그인 흐름

```
Flutter App → Back Server Stub → ValueDoor API → DynamoDB
```

**단계별 프로세스:**

1. **사용자 입력**: Flutter 앱에서 ID/Password 입력
2. **로그인 요청**: `POST /api/auth/login`
3. **SAML 생성**: 서버에서 SAML Response 생성
4. **SAML 반환**: samlResponse를 클라이언트에 반환
5. **SAML 검증**: `POST /api/auth/saml-response-verification`
6. **위험도 판정**: Caulis API를 통한 보안 검증
7. **세션 생성**: UUID 기반 sessionId 생성
8. **세션 저장**: DynamoDB에 세션 정보 저장
9. **응답 반환**: sessionId 또는 highRiskUserId 반환
10. **로컬 저장**: MemoryDataManager에 sessionId 저장
11. **상태 업데이트**: 로그인 상태 플래그 업데이트

### B. DTPID 로그인 흐름

```
Flutter App → Back Server Stub → DTP API → DynamoDB
```

**단계별 프로세스:**

1. **DTP ID 입력**: Flutter 앱에서 DTP ID 입력
2. **권한 요청**: `POST /api/dtpid/authority-acquisition`
3. **DTP API 호출**: DTP 권한 취득 API 호출
4. **사용자 정보**: DTP에서 사용자 정보 반환
5. **세션 생성**: UUID 기반 sessionId 생성
6. **세션 저장**: DynamoDB에 세션 정보 저장
7. **응답 반환**: sessionId + encryptedCookie 반환
8. **로컬 저장**: MemoryDataManager에 sessionId 저장
9. **상태 업데이트**: 로그인 상태 플래그 업데이트

---

## 3. 세션 관리 구조

### A. 클라이언트 측 (Flutter)

**MemoryDataManager를 통한 세션 관리:**

```dart
// sessionId 저장
await _mdManager.sessionId.save(sessionId);

// 로그인 상태 확인
final loginFlagProvider = FutureProvider<bool>((ref) {
  final mdManager = ref.read(mdManagerProvider);
  return mdManager.sessionId.load().then((value) => value.isNotEmpty);
});

// sessionId 로드
final sessionId = await memoryDataManager.sessionId.load();
```

**주요 특징:**
- 암호화된 메모리 저장
- 앱 종료 시 자동 삭제
- Riverpod Provider 패턴 사용

### B. 서버 측 (Node.js)

**DynamoDB를 통한 세션 관리:**

```typescript
// 세션 데이터 구조
const sessionData: Session = {
  sessionId: uuidv4(),           // 고유 세션 ID
  vdId: user.vdId,              // ValueDoor ID
  dtpId: user.dtpId,            // DTP ID
  userId: user.userId,          // 사용자 ID
  userInfo: userInfoData,       // 사용자 상세 정보
  expirationTime: expirationTime(), // 만료 시간
  createdAt: jstTime(),         // 생성 시간
  updatedAt: jstTime()          // 업데이트 시간
};

// DynamoDB에 저장
await saveSession(sessionData);
```

**세션 테이블 구조:**
- **Primary Key**: sessionId
- **TTL**: expirationTime (자동 만료)
- **사용자 정보**: vdId, dtpId, userId, userInfo
- **메타데이터**: createdAt, updatedAt

---

## 4. 인증 미들웨어

### A. 요청 인증 과정

```typescript
export const authenticateUser = async (req: Request, res: Response, next: NextFunction) => {
  // 1. Authorization 헤더 확인
  const authHeader = req.headers?.authorization;
  if (!authHeader?.startsWith('Bearer ')) {
    return next(new AuthorizationError('인증 헤더 없음'));
  }

  // 2. sessionId 추출
  const sessionId = authHeader.split(' ')[1];
  
  // 3. DynamoDB에서 세션 조회
  const session = await getSession(sessionId);
  if (!session) {
    return next(new AuthorizationError('세션 없음'));
  }

  // 4. 세션 만료 확인
  if (Number(session.expirationTime) < unixTime()) {
    return next(new AuthorizationError('세션 만료'));
  }

  // 5. 요청에 세션 정보 추가
  req.session = session;
  next();
};
```

### B. 보호된 엔드포인트

```typescript
// 인증이 필요한 모든 라우트에 적용
router.use(asyncHandler(authenticateUser));

// 예외: 인증이 불필요한 엔드포인트
router.get('/allganize_chatbot', /* 인증 미들웨어 이전에 정의 */);
router.post('/auth/login', /* 로그인 엔드포인트 */);
```

---

## 5. 생체 인증 로그인

### A. 생체 인증 프로세스

```dart
Future<void> biometricsLogin() async {
  // 1. 생체 인증 실행
  final result = await biometricManager.didAuthenticate();
  
  await result.when(
    success: (isCompleted) async {
      if (!isCompleted) {
        // 생체 인증 실패 처리
        state = state.copyWith(
          error: AppError(
            message: LoginErrorInfo.biometricsFailureErrorMessage,
            code: LoginErrorInfo.biometricsFailureErrorCode,
          )
        );
        return;
      }
      
      // 2. 저장된 로그인 정보 로드
      final id = await loginRepository.getLoginId();
      final password = await loginRepository.getPassword();

      // 3. 로그인 방식에 따라 분기
      state.isDtpLogin
          ? await loginDtpId(id, password, false)
          : await login(id, password, false);
    },
    failure: (error) => {
      // 에러 처리
    }
  );
}
```

### B. 로그인 정보 저장/로드

```dart
// 로그인 성공 시 정보 저장 (생체 인증용)
await loginRepository.saveLoginId(vdId);
await loginRepository.savePassword(password);

// 생체 인증 시 정보 로드
final savedId = await loginRepository.getLoginId();
final savedPassword = await loginRepository.getPassword();
```

---

## 6. OAuth 토큰 관리

### A. OAuth 인증 흐름

```dart
// 1. 인증 코드 수신 후 토큰 요청
Future<bool> getToken(bool isReauthorization) async {
  if (_code.isEmpty && _state.isEmpty) {
    if (!isReauthorization && !state.isRefusalFlag) await logout();
    return false;
  }

  // 2. 토큰 요청 API 호출
  final res = await loginRepository.getToken(_code, _state);
  
  // 3. 결과 처리
  return res.when(
    success: (_) {
      // 세션 정보 업데이트
      await _mdManager.session.save(
        const Session.identified(
          permissionStatus: SessionPermissionStatus.permitted,
        ),
      );
      return true;
    },
    failure: (_) => false,
  );
}
```

### B. 토큰 검증

```typescript
// 서버에서 토큰 검증
router.post('/token', async (req: Request, res: Response) => {
  const { code, state } = req.query;
  const sessionId = req.headers.authorization?.split(' ')[1];
  
  // OAuth 토큰 검증 로직
  // ...
  
  res.status(200).json({ success: true });
});
```

---

## 7. 위험도 판정 시스템

### A. Caulis API를 통한 위험도 판정

```typescript
// 로그인 성공 시 위험도 판정
const userHash = createHash('sha256')
  .update(response.data.ids.valueDoorId + salt)
  .digest('hex');

const relativeSuspiciousValue = await apiClient(req, {
  headers: {
    caulisSessionId,
    userHash,
    userAgent: req.header('user-agent'),
    userHashOfNewAccountCreation
  }
}).post('api/caulis/login-suspicious-detection', {
  isLoginSucceed: true,
  loginKind: '2'
});

const judgeRiskResponse = relativeSuspiciousValue.data.relativeSuspiciousValue;
```

### B. 고위험 사용자 처리

```typescript
if (judgeRiskResponse === 'H') {
  // 고위험 사용자는 별도 테이블에 저장
  const highRiskUserId = uuidv4();
  
  const highRiskUserData = {
    highRiskUserId,
    vdId: response.data.ids.valueDoorId,
    dtpId: response.data.ids.dtpId,
    userId: response.data.ids.userUid,
    issueInstant: response.data.issueInstant,
    userInfo: userInfoData,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };
  
  await saveHighRiskUser(highRiskUserData);
  
  // sessionId 대신 highRiskUserId 반환
  return res.status(200).json({ 
    highRiskUserId,
    encryptedCookie 
  });
} else {
  // 일반 사용자는 sessionId 발급
  const sessionId = uuidv4();
  await saveSession(sessionData);
  
  return res.status(200).json({ 
    sessionId,
    encryptedCookie 
  });
}
```

### C. 로그인 실패 시 위험도 판정

```typescript
// 로그인 실패 시에도 위험도 판정 실행
await apiClient(req, {
  headers: {
    caulisSessionId,
    userHash,
    userAgent: req.header('user-agent')
  }
}).post('api/caulis/login-suspicious-detection', {
  isLoginSucceed: false,
  loginErrorCode: errorMessageId,
  loginKind: '2'
});
```

---

## 8. 로그아웃 처리

### A. 클라이언트 측 로그아웃

```dart
Future<void> logout() async {
  try {
    // 1. 서버에 로그아웃 요청 (선택적)
    await _api.post(
      endpoint: 'api/$_apiVersion/auth/logout',
      headers: {
        'Authorization': 'Bearer ${await _mdManager.sessionId.load()}',
      },
    );
  } catch (e) {
    // 서버 요청 실패해도 로컬 정리는 진행
  }
  
  // 2. 로컬 데이터 삭제
  await _mdManager.sessionId.delete();
  await _mdManager.session.delete();
  await _mdManager.encryptedCookie.delete();
  await _mdManager.dtpId.delete();
  await _mdManager.vdId.delete();
  
  // 3. 생체 인증 정보 삭제
  await loginRepository.deleteLoginId();
  await loginRepository.deletePassword();
  
  // 4. 로그인 상태 업데이트
  _onLoginStatusChanged();
}
```

### B. 서버 측 세션 무효화

```typescript
// 로그아웃 API
router.post('/auth/logout', authenticateUser, async (req: Request, res: Response) => {
  const sessionId = req.session.sessionId;
  
  // DynamoDB에서 세션 삭제
  await deleteSession(sessionId);
  
  res.status(200).json({ message: 'Logout successful' });
});
```

---

## 9. 세션 유효성 검증

### A. API 요청 시마다 검증

```dart
// 모든 API 요청에 sessionId 포함
Future<ApiResponse> makeApiRequest(String endpoint) async {
  final sessionId = await _mdManager.sessionId.load();
  
  if (sessionId.isEmpty) {
    // 로그인되지 않은 상태
    throw UnauthorizedException('로그인이 필요합니다');
  }
  
  final response = await _api.get(
    url: '$_baseUrl/api/$_apiVersion/$endpoint',
    headers: {
      'Authorization': 'Bearer $sessionId',
      'Content-Type': 'application/json',
    },
  );
  
  if (response.statusCode == 401) {
    // 세션 만료 또는 무효
    await logout();
    throw SessionExpiredException('세션이 만료되었습니다');
  }
  
  return response;
}
```

### B. 자동 세션 갱신

```dart
// 세션 만료 전 자동 갱신 (선택적 구현)
Future<void> refreshSessionIfNeeded() async {
  final session = await _mdManager.session.load();
  
  if (session.isNearExpiry) {
    try {
      final newSession = await _api.post(
        endpoint: 'api/$_apiVersion/auth/refresh',
        headers: {
          'Authorization': 'Bearer ${await _mdManager.sessionId.load()}',
        },
      );
      
      await _mdManager.sessionId.save(newSession.sessionId);
    } catch (e) {
      // 갱신 실패 시 로그아웃
      await logout();
    }
  }
}
```

---

## 10. 챗봇에서의 인증

### A. WebView에서 sessionId 전달

```dart
// 챗봇 WebView 초기화 시 sessionId 헤더 설정
Future<void> initializeChatbot() async {
  final sessionId = await memoryDataManager.sessionId.load();
  
  if (sessionId.isEmpty) {
    // 로그인되지 않은 상태 처리
    Log.w('User not logged in, cannot access chatbot');
    return;
  }
  
  // WebView에 sessionId 헤더 설정
  final webView = InAppWebView(
    initialUrlRequest: URLRequest(
      url: WebUri('$baseUrl/api/allganize_chatbot'),
      headers: {
        'sessionId': sessionId,
        'X-Session-ID': sessionId,
        'Authorization': 'Bearer $sessionId',
      },
    ),
    onWebViewCreated: (controller) {
      Log.i('Chatbot WebView created with sessionId: ${sessionId.substring(0, 8)}...');
    },
  );
}
```

### B. 서버에서 챗봇 인증 처리

```typescript
// 챗봇 HTML 반환 시 세션 확인
router.get('/allganize_chatbot', async (req: Request, res: Response) => {
  // 헤더에서 sessionId 추출
  const sessionId = req.headers['sessionid'] || 
                   req.headers['x-session-id'] || 
                   req.headers['authorization']?.toString().replace('Bearer ', '');
  
  if (sessionId) {
    console.log('Chatbot accessed by sessionId:', sessionId);
    
    // 선택적: 세션 유효성 검증
    const session = await getSession(sessionId);
    if (!session || Number(session.expirationTime) < unixTime()) {
      return res.status(401).json({ error: 'Invalid or expired session' });
    }
  }
  
  // HTML 파일 반환 (환경변수 포함)
  const htmlPath = path.join(__dirname, '../../public/allganize_chatbot.html');
  const html = await fs.readFile(htmlPath, 'utf8');
  const htmlWithEnv = html.replace('__ALLGANIZE_SDK_KEY__', allganizeSdkKey);
  
  res.setHeader('Content-Type', 'text/html');
  res.send(htmlWithEnv);
});
```

### C. 챗봇에서 사용자 컨텍스트 활용

```javascript
// HTML 내 JavaScript에서 사용자 정보 활용
const SESSION_ID = "__SESSION_ID__"; // 서버에서 치환

if (SESSION_ID) {
  // 사용자별 맞춤 설정 로드
  fetch('/api/user/chatbot-settings', {
    headers: {
      'Authorization': `Bearer ${SESSION_ID}`
    }
  })
  .then(response => response.json())
  .then(settings => {
    // 챗봇 설정 적용
    initializeChatbotWithSettings(settings);
  });
}
```

---

## 보안 고려사항

### 1. 세션 보안
- **암호화**: MemoryDataManager에서 sessionId 암호화 저장
- **TTL**: DynamoDB에서 자동 세션 만료
- **HTTPS**: 모든 통신은 HTTPS로 암호화

### 2. 인증 토큰
- **Bearer Token**: 표준 OAuth 2.0 Bearer Token 방식
- **헤더 검증**: 서버에서 Authorization 헤더 필수 검증
- **만료 시간**: 적절한 세션 만료 시간 설정

### 3. 위험도 판정
- **실시간 모니터링**: Caulis API를 통한 실시간 위험도 판정
- **고위험 사용자**: 별도 처리 프로세스
- **로그인 패턴 분석**: 사용자 행동 패턴 기반 보안

### 4. 데이터 보호
- **로컬 암호화**: 클라이언트 측 민감 정보 암호화
- **메모리 저장**: 앱 종료 시 자동 삭제
- **최소 권한**: 필요한 최소한의 정보만 저장

---

## 트러블슈팅

### 1. 401 Unauthorized 에러
- sessionId가 비어있는지 확인
- 세션이 만료되었는지 확인
- 서버의 DynamoDB에 해당 세션이 존재하는지 확인

### 2. 로그인 실패
- ValueDoor API 연결 상태 확인
- SAML Response 형식 검증
- 위험도 판정 API 응답 확인

### 3. 챗봇 접근 불가
- sessionId가 WebView 헤더에 포함되었는지 확인
- 챗봇 라우트가 인증 미들웨어 이전에 정의되었는지 확인
- 환경변수 설정 확인

---

이 가이드는 프로젝트의 전체 로그인 및 인증 시스템에 대한 포괄적인 설명을 제공합니다. 각 단계별 구현 세부사항과 보안 고려사항을 참고하여 시스템을 이해하고 유지보수하시기 바랍니다.
