{"name": "back-server-app", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"watch": "tsc --watch", "build": "tsc", "start:local": "cross-env DYNAMODB_ENDPOINT=http://localhost:8000 nodemon src/index.ts", "start:dev": "nodemon -w src -x ts-node-dev src/index.ts", "test:unit": "jest tests/unit --coverage --verbose", "test:integration": "jest tests/integration --verbose --runInBand", "lint": "npx eslint --ext .ts src/", "lint:fix": "npx eslint --ext .ts src/ --fix", "deploy:dev": "node -r @contrast/agent dist/index.js", "deploy": "node dist/index.js", "generate:schemas": "openapi-typescript mobile_bff_api.yaml -o src/schemas/schema.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/aws-sdk": "^2.7.0", "@types/cookie-parser": "^1.4.6", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.16.11", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@types/xml-crypto": "^1.4.3", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "eslint": "^8.44.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.2", "eslint-plugin-no-relative-import-paths": "^1.5.2", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "jest-html-reporters": "^3.1.7", "nodemon": "^3.0.2", "openapi-typescript": "^6.7.3", "prettier": "^3.1.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.6.2"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.577.0", "@aws-sdk/lib-dynamodb": "^3.577.0", "axios": "^1.6.5", "cookie-parser": "^1.4.6", "cross-env": "^7.0.3", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "dynamodb-admin": "^4.5.0", "eslint-config-airbnb-typescript": "^17.0.0", "express": "^4.19.2", "express-validator": "^7.0.1", "iconv-lite": "^0.6.3", "jest-date-mock": "^1.0.8", "jet-logger": "^1.3.1", "jsonwebtoken": "^9.0.2", "log4js": "^6.9.1", "npm": "^10.2.4", "uuid": "^9.0.1", "xml-crypto": "^4.1.0"}}