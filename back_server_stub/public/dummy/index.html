<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cookie Display</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        #cookie-info {
            margin: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Cookie情報表示</h1>
    <div id="cookie-info">
        Cookie情報がここに表示されます。
    </div>

    <script>
        // Cookieの取得関数
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
        }

        // 取得したCookie情報を表示する関数
        function displayCookie() {
            // Cookieの名前（例: "username"）を指定して取得
            const cookieName = 'encrypt';
            const cookieValue = getCookie(cookieName);

            // 表示する要素を取得
            const cookieInfoDiv = document.getElementById('cookie-info');

            if (cookieValue) {
                // Cookieが存在する場合、その値を表示
                cookieInfoDiv.textContent = `Cookie "${cookieName}" の値: ${cookieValue}`;
            } else {
                // Cookieが存在しない場合のメッセージ
                cookieInfoDiv.textContent = `Cookie "${cookieName}" は存在しません。`;
            }
        }

        // ページが読み込まれたときにCookieを表示
        document.addEventListener('DOMContentLoaded', displayCookie);
    </script>
</body>
</html>
