<!DOCTYPE html>
<html>
  <head>
    <title>test</title>
    <meta charset="UTF-8" />
    <script>
      let from_vd_param = '';
      const onClickGetSaml = async function () {
        const val = document.getElementById('inputLoginId').value;

        const result = await fetch('/api/v1/oauth2/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: val }),
        });
        const resJson = await result.json();
        const from_vd_param = resJson.from_vd_param;
        document.getElementById('from_vd_paramInput').value = from_vd_param;
      };
      const submitClick = function () {
        const val = document.getElementById('inputSendTarget').value;
        document.getElementById('formFor').action = val;
      };
    </script>
  </head>

  <body>
    <h2>モックログイン</h2>
    <div>
      <input placeholder="ログインID" id="inputLoginId" value="0000000001" />
      <button onclick="onClickGetSaml()">ログインSAML取得</button>
    </div>
    <form action="./mobile/webview/close">
      <input name="from_vd_param" id="from_vd_paramInput" type="text" value="" />
      <button type="submit">ログイン</button>
    </form>
    <div class="links">
      <a href="https://www.smbc.co.jp/ln/direct/LinksServlet?id=WA03_01I">外部サイトへのリンク</a>
    </div>
  </body>
</html>
