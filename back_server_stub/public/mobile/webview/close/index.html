<html lang="ja">
  <head>
    <title></title>
    <meta charset="UTF-8" />
    <script type="text/javascript">
      // URLからクエリパラメータを取得する関数
      function getQueryParams() {
        let query = window.location.search.substring(1);
        let vars = query.split("&");
        let params = {};
        for (let i = 0; i < vars.length; i++) {
            let pair = vars[i].split("=");
            params[pair[0]] = decodeURIComponent(pair[1]);
        }
        return params;
      }

      var fw = {
        onSubmit: function (action) {
          if (fw.ajaxSending) {
            return false;
          }
          if (fw.submitCount >= 1) {
            fw.clearEventTriggerName();
            return false;
          }
          fw.setEventTriggerName();
          var form = $('form').eq(0);
          form.attr('target', '_self');
          form.attr('data-submit', false);
          if (!fw.validate(form)) {
            fw.clearEventTriggerName();
            return false;
          }
          if (!fw.correlatedValidate(fw.triggerElementId)) {
            fw.clearEventTriggerName();
            if (fw.showErrorFlg) {
              fw.showErrorFlg = false;
              return false;
            }
            fw.showClientError(fw.errorList, validationRules.popup);
            return false;
          }
          if (!fw.beforeSubmit(fw.triggerElementId)) {
            fw.clearEventTriggerName();
            return false;
          }
          form.attr('action', action);
          form.attr('data-submit', true);
          fw.setWindowName(form);
          fw.removeChildWindowName();
          if (loading.submit) {
            fw.loadingImage(true, loading.delayTime);
          }
          form.submit();
        }
      };

      window.onload = function () {
        let params = getQueryParams();
        
        let otp_type_param = params['otp_type_param'];
        document.getElementById('otp_type_paramInput').value = otp_type_param;
    
        let input_type_param = params['input_type_param'];
        document.getElementById('input_type_paramInput').value = input_type_param;
        
        let vd_id_param = params['vd_id_param'];
        document.getElementById('vd_id_paramInput').value = vd_id_param;
        
        let from_vd_param = params['from_vd_param'];
        document.getElementById('from_vd_paramInput').value = from_vd_param;
        
        let encrypt = params['encrypt'];
        document.getElementById('encrypt').value = encrypt;

        let ticket = params['ticket'];
        document.getElementById('ticket').value = ticket;
        let domain = params['domain'];
        document.getElementById('domain').value = domain;
        let path = params['path'];
        document.getElementById('path').value = path;
        let secure = params['secure'];
        document.getElementById('secure').value = secure;
        let httponly = params['httponly'];
        document.getElementById('httponly').value = httponly;

        // アプリケーション内の制御に用いる不要なリクエストパラメータは付与しないため空でoverrideする
        fw.setWindowName = function (form, windowName) {};
        fw.setToken = function (form) {};

        fw.onSubmit('./mobile/webview/close');
      };
    </script>
  </head>

  <body>
    <div id="ev_wrapper">
      <div id="ev_container">
        <div id="ev_contents" class="clearfix">
          <div id="ev_main">
            <div id="wrapper">
              <div id="container">
                <form
                  id="response"
                  name="response"
                  action="./mobile/webview/close"
                  method="POST"
                  target="_self"
                  data-submit="true"
                  novalidate="novalidate"
                >
                  <input type="text" name="otp_type_param" id="otp_type_paramInput" value="" />
                  <input type="text" name="input_type_param" id="input_type_paramInput" value="" />
                  <input type="text" name="encrypt" id="encrypt" value="" />
                  <input type="hidden" name="vd_id_param" id="vd_id_paramInput" value="" />
                  <input type="hidden" name="from_vd_param" id="from_vd_paramInput" value="" />
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
