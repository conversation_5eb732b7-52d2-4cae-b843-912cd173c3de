<html lang="ja">
  <head>
    <title></title>
    <meta charset="UTF-8" />
    <script type="text/javascript">
      // URLからクエリパラメータを取得する関数
      function getQueryParams(key) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(key);
      }

      // Cookieの保存関数
      function setCookieParam(name, value) {
        document.cookie = name + "=" + value + ";";
        console.log("Cookie param set : " + name + "=" + value + ";");
      }

      window.onload = function () {
        const param = getQueryParams("encrypt");
        console.log("encrypt param = " + param);
        setCookieParam("encrypt", param);
        // 処理完了後のリダイレクト
        window.location.href = './dummy/index.html';
      };
    </script>
  </head>

  <body>
    <div>リダイレクトしています…</div>
  </body>
</html>
