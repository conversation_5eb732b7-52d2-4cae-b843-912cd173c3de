<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Allganize AI Chatbot</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            height: 100vh;
            width: 100vw;
            display: flex;
            flex-direction: column;
            background-color: #f5f5f5;
        }
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 100;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .error-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.9);
            z-index: 100;
            text-align: center;
        }
        .error-message {
            font-size: 16px;
            color: #333;
            margin-bottom: 16px;
        }
        .retry-button {
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Flutter側のローディングアイコンを使用するため、HTML側のローディングは非表示に設定 -->
    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
    </div>
    <div id="error-container" class="error-container">
        <div class="error-message">チャットボットの読み込みに失敗しました。</div>
        <button class="retry-button" onclick="retryInitialization()">再試行</button>
    </div>

    <script type="module">
        import Alli from 'https://sdk.alli.ai/2/alli.esm.min.js';

        // Allganize SDK V2 APIキー（サーバーから環境変数として渡される）
        const ALLGANIZE_SDK_KEY = "__ALLGANIZE_SDK_KEY__"; // サーバーサイドで環境変数の値に置換される

        // セッションID（サーバーから渡される）
        const SESSION_ID = "__SESSION_ID__"; // サーバーサイドでsessionIdの値に置換される

        // セッションIDをログに出力（デバッグ用）
        if (SESSION_ID) {
            console.log('Chatbot initialized with sessionId:', SESSION_ID);
        } else {
            console.log('Chatbot initialized without sessionId');
        }

        // グローバルスコープに関数を公開（再試行ボタン用）
        window.retryInitialization = async () => {
            // Flutter側のローディングアイコンを表示するために通知
            try {
                if (window.flutter_inappwebview && typeof window.flutter_inappwebview.callHandler === 'function') {
                    window.flutter_inappwebview.callHandler('onChatbotRetry');
                }
            } catch (e) {
                console.log('Flutter InAppWebView not available, running in browser mode');
                // ブラウザモードの場合はHTML側のローディングを表示
                document.getElementById('loading').style.display = 'flex';
            }

            document.getElementById('error-container').style.display = 'none';
            initChatbot();
        };

        // ネットワーク接続状態を確認する関数
        const checkNetworkConnection = () => {
            return navigator.onLine;
        };

        // ネットワークエラーを表示する関数
        const showNetworkError = (message) => {
            console.error('Network error:', message);
            // Flutter側のローディングアイコンを使用するため、HTML側のローディング非表示はコメントアウト
            // document.getElementById('loading').style.display = 'none';
            document.getElementById('error-container').style.display = 'flex';
            document.querySelector('.error-message').textContent = message || 'インターネット接続がありません。接続を確認して再試行してください。';

            // Flutterアプリにエラーを通知
            try {
                if (window.flutter_inappwebview && typeof window.flutter_inappwebview.callHandler === 'function') {
                    window.flutter_inappwebview.callHandler('onChatbotError', message || 'インターネット接続がありません');
                }
            } catch (e) {
                console.log('Flutter InAppWebView not available, running in browser mode');
            }
        };

        // チャットボットの初期化
        const initChatbot = async () => {
            // ネットワーク接続を確認
            if (!checkNetworkConnection()) {
                showNetworkError('インターネット接続がありません。接続を確認して再試行してください。');
                return;
            }

            // ローディングタイムアウトを設定（15秒）
            const loadingTimeout = setTimeout(() => {
                showNetworkError('読み込みがタイムアウトしました。インターネット接続を確認して再試行してください。');
            }, 15000);

            try {
                // SDK V2を初期化
                await Alli.initialize({
                    sdkKey: ALLGANIZE_SDK_KEY,
                    fullscreen: true, // 全画面表示
                    hideCloseButton: false, // 閉じるボタンを表示
                    // 初期化完了時のコールバックを追加
                    onLoad: () => {
                        console.log('Allganize SDK loaded successfully');
                        // Flutterアプリに読み込み完了を通知（onLoad内でも呼び出し）
                        try {
                            if (window.flutter_inappwebview && typeof window.flutter_inappwebview.callHandler === 'function') {
                                window.flutter_inappwebview.callHandler('onChatbotLoaded');
                            }
                        } catch (e) {
                            console.log('Flutter InAppWebView not available in onLoad, running in browser mode');
                        }
                    }
                });

                // タイムアウトをクリア
                clearTimeout(loadingTimeout);

                // 読み込み完了時の処理
                // Flutter側のローディングアイコンを使用するため、HTML側のローディング非表示はコメントアウト
                // document.getElementById('loading').style.display = 'none';

                // Flutterアプリに読み込み完了を通知
                try {
                    if (window.flutter_inappwebview && typeof window.flutter_inappwebview.callHandler === 'function') {
                        window.flutter_inappwebview.callHandler('onChatbotLoaded');
                    }
                } catch (e) {
                    console.log('Flutter InAppWebView not available, running in browser mode');
                }

                // 少し遅延させてからチャットボットを表示（初期化完了を確実にするため）
                setTimeout(() => {
                    try {
                        // チャットボットを表示
                        Alli.show();
                    } catch (e) {
                        console.error('Error showing chatbot:', e);
                        showNetworkError('チャットボットの表示に失敗しました。再試行してください。');
                    }
                }, 500);

            } catch (error) {
                // タイムアウトをクリア
                clearTimeout(loadingTimeout);

                console.error('Allganize initialization error:', error);
                // Flutter側のローディングアイコンを使用するため、HTML側のローディング非表示はコメントアウト
                // document.getElementById('loading').style.display = 'none';
                document.getElementById('error-container').style.display = 'flex';

                // Flutterアプリにエラーを通知
                try {
                    if (window.flutter_inappwebview && typeof window.flutter_inappwebview.callHandler === 'function') {
                        window.flutter_inappwebview.callHandler('onChatbotError',
                            error.message || 'チャットボットの初期化に失敗しました');
                    }
                } catch (e) {
                    console.log('Flutter InAppWebView not available, running in browser mode');
                }
            }
        };

        // ページ読み込み時にチャットボットを初期化
        // テスト用にエラーを発生させる（テスト後はコメントアウトする）
        // document.addEventListener('DOMContentLoaded', () => { throw new Error('テスト用のエラー'); });
        document.addEventListener('DOMContentLoaded', initChatbot);

        // ネットワーク状態の変化を監視
        window.addEventListener('online', () => {
            // オンラインになったときに、エラー表示中であれば再試行
            if (document.getElementById('error-container').style.display === 'flex') {
                retryInitialization();
            }
        });

        window.addEventListener('offline', () => {
            // オフラインになったときに、ローディング中であれば中断してエラー表示
            // Flutter側のローディングアイコンを使用するため、HTML側のローディング状態チェックはコメントアウト
            // if (document.getElementById('loading').style.display === 'flex') {
                showNetworkError('インターネット接続が切断されました。接続を確認して再試行してください。');
            // }
        });
    </script>
</body>
</html>
