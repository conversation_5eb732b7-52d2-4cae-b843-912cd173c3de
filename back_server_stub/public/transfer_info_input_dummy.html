<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Web21 振込先情報入力</title>
  <style>
    body {
      background-color: white;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }
    .transfer-info-form {
      text-align: center;
    }
    .button-container {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .description {
      margin-left: 10px;
    }  
    .separator {
      border-top: 1px solid #ccc;
      margin: 20px 0;
      width: 80%;
    }
    input[type='text'] {
      padding: 10px;
      margin: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      width: 200px;
    }
    button[type='submit'] {
      padding: 10px 30px;
      background-color: rgb(3, 53, 15);
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
  </style>
</head>

<body>
  <div class="transfer-info-form">
    <form action="./mobile/webview/close">
      
      <div class="separator"></div>
      <p>以下はパラメータを手動で設定したい場合に利用</p>

      <input type="text" placeholder="OTP種別" id="otp_type_paramInput" name="otp_type_param" value="otpType" /><br />
      <input type="text" placeholder="入力方式" id="input_type_paramInput" name="input_type_param" value="inputType" /><br />
      <input type="text" placeholder="VDID" id="vd_id_paramInput" name="vd_id_param" value="VDID" /><br />
      <input type="hidden" name="encrypt" value="encrypt" />
      <button type="submit" id="otpButton">OTP発行</button>
      <button type="button" onclick="location.href='./otp_input_dummy.html'" id="otpButton">OTP入力画面へ</button>
    </form>
  </div>

  <script>
    function setParams(otpType, inputType, vdId, encrypt = '') {
      const form = document.createElement('form');
      form.action = './mobile/webview/close';
      form.method = 'GET';
  
      // パラメータが空でない場合のみフォームに追加
      if (otpType) {
        const otpInput = document.createElement('input');
        otpInput.type = 'hidden';
        otpInput.name = 'otp_type_param';
        otpInput.value = otpType;
        form.appendChild(otpInput);
      }
      if (inputType) {
        const inputTypeInput = document.createElement('input');
        inputTypeInput.type = 'hidden';
        inputTypeInput.name = 'input_type_param';
        inputTypeInput.value = inputType;
        form.appendChild(inputTypeInput);
      }
      if (vdId) {
        const vdIdInput = document.createElement('input');
        vdIdInput.type = 'hidden';
        vdIdInput.name = 'vd_id_param';
        vdIdInput.value = vdId;
        form.appendChild(vdIdInput);
      }
      if (encrypt) {
        const encryptInput = document.createElement('input');
        encryptInput.type = 'hidden';
        encryptInput.name = 'encrypt';
        encryptInput.value = encrypt;
        form.appendChild(encryptInput);
      }
  
      document.body.appendChild(form);
      form.submit();
    }
  </script>
  
</body>
</html>
