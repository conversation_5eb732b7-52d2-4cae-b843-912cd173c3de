<!DOCTYPE html>

<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>認可画面</title>
    <style>
      body {
        background-color: white;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
      .login-form {
        text-align: center;
      }
      .links {
        margin-top: 30px;
      }
    </style>
  </head>

  <body>
    <div class="login-form">
      <div class="links">
        <a
          href="" id="auth-link"
          >認可する</a
        >
      </div>
    </div>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const query = decodeURIComponent(location.search.substring(1))
        const params = new URLSearchParams(query);
        const stateValue = params.get('from_ebiz_param');

        const linkElement = document.getElementById("auth-link");
        linkElement.href = `./mobile/api/bank/auth?code=cb46420e53c24580a4c4e0fe8f888888&state=${stateValue}`;
      });
    </script>
  </body>
</html>
