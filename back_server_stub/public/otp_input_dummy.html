<!DOCTYPE html>

<html lang="ja">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OTP入力画面</title>
    <script>
      function inputTimeOtp(text) {
        var textField = document.getElementById('otp_input');
        textField.value = text;
      } 
      function approveTxOtp() {
        var button = document.getElementById('otpButton');
        button.click();
      }
      function sendUniversalLink() {
        window.location.href = 'https://valuedoort.smbc.co.jp/w21sp/otpcall';
      }
      function sendOtpLink(otpType, notInputOTP, encrypt) {
        let url = 'https://valuedoort.smbc.co.jp/w21sp/otpcall';
        if (otpType === 'time') {
          url += `?otp=${otpType}&notInputOTP=${notInputOTP}`;
        } else if (otpType === 'signature') {
          url += `?otp=${otpType}&encrypt=${encrypt}`;
        }
        window.location.href = url;
      }
    </script>
    <style>
      body {
        background-color: white;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
      }
      .transfer-info-form {
        text-align: center;
      }
      input[type='text'] {
        padding: 10px;
        margin: 10px;
        border: 1px solid #ccc;
        border-radius: 5px;
        width: 200px;
      }
      button[type='button'] {
        padding: 10px 30px;
        margin-top: 10px;
        background-color: rgb(3, 53, 15);
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      }
      .links {
        margin-top: 30px;
      }
      .parameter-display-vertical {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 20px;
    }
    .parameter-box {
      border: 1px solid #ccc;
      border-radius: 5px;
      padding: 10px;
      margin: 5px;
      width: 120px;
      text-align: center;
      font-size: 0.8em;
    }
    </style>
  </head>

  <body>
    <div class="transfer-info-form">
      <form action="./mobile/webview/close">
        <input
          type="text"
          placeholder="OTP"
          id="otp_input"
          name="otp_type"
        /><br />
        <button type="button" onclick="location.href='./otp_input_finished_dummy.html'" id="otpButton">確認</button>
        <button type="button" onclick="inputTimeOtp('test')">JS動作確認</button>
        <button type="button" onclick="approveTxOtp()">JS動作確認（approveTxOtp）</button>

        <br />
        <span>法人用アプリのユニバーサルリンク起動</span>
        <br />

        <div class="buttons-container">
          <button type="button" onclick="sendOtpLink('time', 0, '')">時刻OTP自動</button>
          <button type="button" onclick="sendOtpLink('time', 1, '')">時刻OTP手動</button>
          <button type="button" onclick="sendOtpLink('signature', '', 'encrypt')">トランザクション</button>
        </div>

      </form>
    </div>
  </body>
</html>
