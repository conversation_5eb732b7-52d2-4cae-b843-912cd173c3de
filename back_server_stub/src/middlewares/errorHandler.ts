/* eslint-disable @typescript-eslint/restrict-template-expressions */
import { Request, Response, NextFunction, response } from 'express';
import { BackendErrorResponse, ErrorResponse } from '../models/errorResponse';
import { errorCodes } from '../errors/errorCodes';
import {
  InternalServerError,
  BadRequestError,
  AuthorizationError,
  ForbiddenError,
  NotFoundError
} from '../errors/httpErrors';
import { CustomError } from '../errors/customError';

function isBackendErrorResponse(error: ErrorResponse): error is BackendErrorResponse {
  return (
    (error as BackendErrorResponse).response !== undefined
    && (error as BackendErrorResponse).response.data !== undefined
    && (error as BackendErrorResponse).response.data.errorDetail !== undefined
  );
}

// エラーマスタとのマッピング
async function errorCodeMapping(targetErrorCode: string) {
  const errorMaster = await response.errorMaster;
  const errorObject = errorMaster?.find((data) => {
    let error;
    if (data.errorCode.S! === targetErrorCode) {
      error = data.errorMessage.S!;
    }
    return error;
  });

  console.log('[errorObject]');
  console.log(`[errorCode] ${errorObject ? errorObject.errorCode.S! : errorCodes.NOT_ERROR_CODE}`);
  console.log(
    `[errorMessage] ${errorObject ? errorObject.errorMessage.S! : 'Internal Server Error'}`
  );
  console.log(`[hasFaq] ${errorObject && errorObject.hasFaq ? errorObject.hasFaq.BOOL! : false}`);

  const errorCode = errorObject ? errorObject.errorCode.S! : errorCodes.NOT_ERROR_CODE;
  const errorMessage = errorObject ? errorObject.errorMessage.S! : 'Internal Server Error';
  // hasFaqフラグを取得。レコードが存在していない場合はfalseで設定。
  const hasFaq = errorObject && errorObject.hasFaq ? errorObject.hasFaq.BOOL! : false;

  console.log(`[返却前のhasFaq] ${hasFaq}`);

  return { errorCode, errorMessage, hasFaq };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars, max-len
const errorHandler = async (
  err: ErrorResponse,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.dir('リクエスト内容：', req);
  console.error('エラーが発生しました', err);

  if (!('errorCode' in err) && !isBackendErrorResponse(err)) {
    return res.status(500).json({
      errorCode: errorCodes.NOT_ERROR_CODE,
      errorMessage: 'Internal Server Error',
      hasFaq: false
    });
  }

  let customError;
  if (isBackendErrorResponse(err)) {
    if (!err.response.data.errorCode || !err.response.data.code) {
      const { errorCode, errorMessage } = await errorCodeMapping(errorCodes.NOT_ERROR_CODE);
      return res.status(500).json({
        errorCode,
        errorMessage
      });
    }
    // バックエンドのエラーハンドリング
    const { errorCode, errorMessage } = await errorCodeMapping(err.response.data.errorCode);

    switch (err.response.data.code) {
      case 400:
        customError = new BadRequestError(errorCode, err.response.data.errorDetail);
        break;
      case 401:
        customError = new AuthorizationError(errorCode, err.response.data.errorDetail);
        break;
      case 403:
        customError = new ForbiddenError(errorCode, err.response.data.errorDetail);
        break;
      case 404:
        customError = new NotFoundError(errorCode, err.response.data.errorDetail);
        break;
      case 500:
        customError = new InternalServerError(errorCode, err.response.data.errorDetail);
        break;
      default:
        customError = new CustomError(
          err.response.data.code,
          errorCode,
          err.response.data.errorDetail
        );
    }
    return res.status(customError.statusCode).json({
      errorCode: customError.errorCode,
      errorMessage
    });
  }

  const { errorCode, errorMessage, hasFaq } = await errorCodeMapping(err.errorCode);

  // BFF内部のエラーを返す
  return res.status(err.statusCode).json({
    errorCode,
    errorMessage,
    hasFaq
  });
};

export default errorHandler;
