import { Request, Response, NextFunction } from 'express';
import { expirationTime, jstTime } from '../utils/dateFormat';
import { Session } from '../models/session';
import { updateExpirationTime } from '../services/sessionService';

export const onSuccess = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { session } = req;
    // セッション有効期限更新
    const sessionData: Session = {
      sessionId: session.sessionId,
      expirationTime: expirationTime(),
      updatedAt: jstTime()
    };
    // セッションIDに紐づけてexpirationTimeを更新
    await updateExpirationTime(sessionData);
    res.on('finish', () => {
      console.log('API終了');
    });
  } catch (error) {
    console.log('エラーが発生しました', error);
    next(error);
  }
};

export const onEnd = (req: Request, res: Response, next: NextFunction) => {
  try {
    res.on('finish', () => {
      console.log('API終了');
    });
  } catch (error) {
    next(error);
  }
};
