import dotenv from 'dotenv';
import { response } from 'express';
import app from './server';
import { getErrorMaster } from './services/errorService';
import type { } from './types/global';

dotenv.config();

const PORT = process.env.PORT || 3501;

// APIサーバ起動
const server = app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
  // エラーマスタ取得
  response.errorMaster = getErrorMaster();
});

export const closeServer = () => {
  server.close();
};
