/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response, NextFunction } from 'express';
import { components } from '../schemas/schema';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const clientActionController = {
  saveClientActionLog: (
    req: Request<components['schemas']['ClientActionRequest']>,
    res: Response,
    next: NextFunction
  ) => {
    console.log('操作履歴保存開始');
    // 正常系のコード開始
    res.status(200).json({ message: 'OK' });
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  }
};

export default clientActionController;
