/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response, NextFunction } from 'express';
import crypto from 'crypto';
import { jstTime, unixTimestampInMinutes } from '../utils/dateFormat';
import { components } from '../schemas/schema';
import { BadRequestError, InternalServerError, ValidationError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

// タイムスタンプに基づく暗号化/復号化キーを生成
function generateKey(timestamp: string): string {
  const sharedKey = process.env.SHARED_KEY_FOR_TRANSFER ?? '';
  return sharedKey + timestamp;
}

// AES-256、ECB方式で暗号化を行う
function encryptData(data: string, unixTimeInMinutes: number): string {
  // 暗号化キーの生成（固定値+現時点のtimestamp(分まで)）
  const key = generateKey(unixTimeInMinutes.toString());
  const cipher = crypto.createCipheriv('aes-256-ecb', Buffer.from(key), null);
  // 暗号化してからbase64エンコード
  let encrypted = cipher.update(data, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  return encrypted;
}

// AES-256、ECB方式で復号化を行う
function decryptData(encryptedData: string, unixTimeInMinutes: number): string {
  try {
    // 復号化キーの生成（固定値+現時点のtimestamp(分まで)）
    const key = generateKey(unixTimeInMinutes.toString());
    const decipher = crypto.createDecipheriv('aes-256-ecb', Buffer.from(key), null);
    // base64デコードしてから復号化
    let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    // 復号化失敗時は１分前のtimestamp(分まで)の復号化キーで再度復号化
    // bad decryptの例外エラーがスローされるので、try-catchで実装

    // 復号化キーの生成（固定値+現時点のtimestamp(分まで)-1）
    const previousKey = generateKey((unixTimeInMinutes - 1).toString());
    const previousDecipher = crypto.createDecipheriv('aes-256-ecb', Buffer.from(previousKey), null);
    // base64デコードしてから復号化
    let previousDecrypted = previousDecipher.update(encryptedData, 'base64', 'utf8');
    previousDecrypted += previousDecipher.final('utf8');

    return previousDecrypted;
  }
}

const web21OtpController = {
  getDecryptWeb21Otp: (req: Request, res: Response, next: NextFunction) => {
    // 正常系（bff-appと同様の処理）のコード終了
    // const otp = req.query.encryptedOtp as string;
    // // 「分まで」のタイムスタンプ(UNIXタイム)を取得
    // const timestamp = unixTimestampInMinutes();

    // // AES-256、ECB方式で復号化
    // let decryptedOtp;
    // try {
    //   decryptedOtp = decryptData(otp, timestamp);
    // } catch (error) {
    //   const internalServerError = new InternalServerError(
    //     errorCodes.DECRYPTION_FAILED,
    //     'OTPの復号化に失敗しました。'
    //   );
    //   return next(internalServerError);
    // }

    // // 長さ、半角英数字チェック
    // if (decryptedOtp.length !== 6 || !/^[A-Za-z0-9]*$/.exec(decryptedOtp)) {
    //   const validationError = new ValidationError(
    //     errorCodes.NOT_CORRECT_OTP,
    //     'ワンタイムパスワードの設定に失敗しました。パスワードをご確認の上、手動でご入力ください。'
    //   );
    //   return next(validationError);
    // }

    // res.status(200).json({ otp: decryptedOtp });
    // return next();
    // 正常系（bff-appと同様の処理）のコード終了

    // 正常系（ダミー処理）のコード開始
    res.status(200).json({ otp: '12345' });
    return next();
    // 正常系（ダミー処理）のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // return next(errorDetail);
    // 異常系のコード終了

    // エラーダイアログ(繋がりいにくい)開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_CORRECT_OTP, 'エラー発生');
    // return next(errorDetail);
    // エラーダイアログ(繋がりいにくい)終了

    // エラーダイアログ(E005-00007)開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_FOUND_ADDRESS, 'エラー発生');
    // return next(errorDetail);
    // エラーダイアログ(E005-00007)終了

    // エラーダイアログ(E005-00006)開始
    // const errorDetail = new BadRequestError(errorCodes.ACCOUNT_NOT_AUTHORIZATION_WITH_BACKEND, 'エラー発生');
    // return next(errorDetail);
    // エラーダイアログ(E005-00006)終了
  },
  checkEncryptedVdid: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    res.status(200).json({ message: 'VDIDの認証に成功しました。' });
    return next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(
    //   errorCodes.NOT_ERROR_CODE,
    //   'ワンタイムパスワードの設定に失敗しました。パスワードをご確認の上、手動でご入力ください。'
    // );
    // return next(errorDetail);
    // 異常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // return next(errorDetail);
    // セッションタイムアウトエラーの終了

    // 処理エラーコード(繋がりにくい状態)の開始
    // const errorDetail = new BadRequestError(
    // errorCodes.NOT_CORRECT_VDID, 'エラー発生');
    // return next(errorDetail);
    // 処理エラーコードの開始の終了

    // エラーコード(データ取得なし)の開始
    // const errorDetail = new BadRequestError(
    // errorCodes.DECRYPTION_FAILED, 'エラー発生');
    // return next(errorDetail);
    // エラーコード(データ取得なし)の終了
  },
  getEncryptedVdid: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    // セッションからVDIDを取得（存在しない場合は想定外であり、後続の暗号化処理にてinternalServerErrorを返す）
    const vdId = req.session.vdId ?? '';

    // 「分まで」のタイムスタンプ(UNIXタイム)を取得
    const timestamp = unixTimestampInMinutes();

    // AES-256、ECB方式で暗号化
    let encryptedVdId;
    try {
      encryptedVdId = encryptData(vdId, timestamp);
    } catch (error) {
      const internalServerError = new InternalServerError(
        errorCodes.ENCRYPTION_FAILED,
        'VDIDの暗号化に失敗しました。'
      );
      return next(internalServerError);
    }

    // 暗号化したVDIDを返却する
    res.status(200).json({ encryptedVdId });
    return next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // return next(errorDetail);
    // 異常系のコード終了
  }
};

export default web21OtpController;
