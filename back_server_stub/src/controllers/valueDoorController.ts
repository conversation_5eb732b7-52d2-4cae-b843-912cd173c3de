/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { NextFunction, Request, Response } from 'express';
import {
  getValueDoorChangePasswordData,
  getValueDoorEnableOtpLaterData,
  getValueDoorEnableOtpNowData,
  getValueDoorExpirationData,
  getValueDoorLoginData,
  getValueDoorLogoutData,
  getValueDoorNavigateSystemData
} from '../data/valueDoorData';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const valueDoorController = {
  login: (req: Request, res: Response, next: NextFunction) => {
    try {
      const { User, Pwd1 } = req.body;
      // Set-CookieヘッダーにVDSESSIONIDを設定
      res.cookie('VDSESSIONID', 'jjjjjyyyyyyyyyyyyyyyyyyy1', {});
      // 正常系開始
      return res.status(200).json(getValueDoorLoginData(User, Pwd1));

      // 正常系終了
      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.INQUIRY_PERIOD_EXCEEDED, 'エラー発生');
      // return next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  },
  informExpiration: (req: Request, res: Response, next: NextFunction) => {
    try {
      // Set-CookieヘッダーにVDSESSIONIDを設定
      res.cookie('VDSESSIONID', 'jjjjjyyyyyyyyyyyyyyyyyyy2', {});
      // 正常系開始
      const { User } = req.body;
      return res.status(200).json(getValueDoorExpirationData(User));
      // 正常系終了
      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.INQUIRY_PERIOD_EXCEEDED, 'エラー発生');
      // return next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  },
  changePassword: (req: Request, res: Response, next: NextFunction) => {
    try {
      const { User } = req.body;
      // Set-CookieヘッダーにVDSESSIONIDを設定
      res.cookie('VDSESSIONID', 'jjjjjyyyyyyyyyyyyyyyyyyy3', {});
      // 正常系開始
      return res.status(200).json(getValueDoorChangePasswordData(User));
      // 正常系終了
    } catch (error) {
      return next(error);
    }
  },
  navigateVdSystem: (req: Request, res: Response, next: NextFunction) => {
    try {
      // Set-CookieヘッダーにVDSESSIONIDを設定
      res.cookie('VDSESSIONID', 'jjjjjyyyyyyyyyyyyyyyyyyy5', {});

      // 正常系開始
      // リクエストボディーからto_vd_param（スタブの場合、遷移先）を取得
      const { to_vd_param } = req.body;
      // isTransfer：振込かどうか、振込：true、承認：falseを設定
      const isTransfer = to_vd_param === 'transfer';
      // return res.status(200).json(getValueDoorNavigateSystemData(isTransfer));
      // 正常系終了

      // ValueDoorのcharset再現
      res.set({ 'Content-Type': 'application/json; charset=utf8' });
      return res.status(200).send(getValueDoorNavigateSystemData(isTransfer));
      // ValueDoorのcharset再現終了

      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
      // return next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  },
  enableOtpNow: (req: Request, res: Response, next: NextFunction) => {
    try {
      // Set-CookieヘッダーにVDSESSIONIDを設定
      res.cookie('VDSESSIONID', 'jjjjjyyyyyyyyyyyyyyyyyyy6', {});
      // 正常系開始
      return res.status(200).json(getValueDoorEnableOtpNowData());
      // 正常系終了

      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
      // next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  },
  enableOtpLater: (req: Request, res: Response, next: NextFunction) => {
    try {
      // Set-CookieヘッダーにVDSESSIONIDを設定
      res.cookie('VDSESSIONID', 'jjjjjyyyyyyyyyyyyyyyyyyy7', {});
      // 正常系開始
      return res.status(200).json(getValueDoorEnableOtpLaterData());
      // 正常系終了

      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
      // next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  },
  logout: (req: Request, res: Response, next: NextFunction) => {
    try {
      // 正常系開始
      return res.status(200).json(getValueDoorLogoutData());
      // 正常系終了

      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.INQUIRY_PERIOD_EXCEEDED, 'エラー発生');
      // return next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  }
};
export default valueDoorController;
