/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response, NextFunction } from 'express';
import { jstTime, serverDateTime } from '../utils/dateFormat';
import {
  usedTotalAmountData1,
  usedTotalAmountData2,
  usedTotalAmountData3,
  getFreeeTransactionsData,
  getFreeeTransactionsCreditData,
  usedTotalAmountData4,
  usedTotalAmountData5,
  usedTotalAmountData6,
  usedTotalAmountData7
} from '../data/freeeTransactionsData';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const freeeTransactionsController = {
  getFreeeTransactions: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    console.log('freeeTransactionリクエスト:', req.query);
    const walletType: string = req.query.walletable_type?.toString() ?? '';
    const walletId: string = req.query.walletable_id?.toString() ?? '';
    const start_date = req.query.start_date as String;
    const dateParts = start_date.split('-');
    if (walletType === 'bank_account') {
      console.log(getFreeeTransactionsData(dateParts[1]));
      res.status(200).json(getFreeeTransactionsData(dateParts[1]));
      next();
    }
    if (walletType === 'credit_card') {
      console.log(getFreeeTransactionsData(dateParts[1]));
      res.status(200).json(getFreeeTransactionsCreditData(dateParts[1], walletId));
      next();
    }

    console.log(getFreeeTransactionsData(dateParts[1]));
    res.status(200).json(getFreeeTransactionsData(dateParts[1]));
    next();
    // 正常系のコード終了

    // 異常系(連携切れ)のコード開始
    // const errorDetail = new BadRequestError(errorCodes.FREEE_EXPIRED, 'エラー発生');
    // next(errorDetail);
    // 異常系(連携切れ)のコード終了
    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  },
  getUsedTotalAmount: (req: Request, res: Response, next: NextFunction) => {
    let usedTotalAmountData = usedTotalAmountData1;
    const walletId: string = req.query.walletable_id?.toString() ?? '';

    if (walletId === '2') {
      usedTotalAmountData = usedTotalAmountData2;
    }

    if (walletId === '3') {
      usedTotalAmountData = usedTotalAmountData3;
    }

    if (walletId === '4') {
      usedTotalAmountData = usedTotalAmountData4;
    }

    if (walletId === '5') {
      usedTotalAmountData = usedTotalAmountData5;
    }

    if (walletId === '6') {
      usedTotalAmountData = usedTotalAmountData6;
    }

    if (walletId === '7') {
      usedTotalAmountData = usedTotalAmountData7;
    }

    console.log(usedTotalAmountData);

    // サーバー日時を追加
    usedTotalAmountData.serverDateTime = serverDateTime();

    // 正常系開始
    res.status(200).json(usedTotalAmountData);
    next();
    // 正常系のコード終了

    // 異常系(部分エラー)のコード開始
    // if (walletId === '2') {
    //   usedTotalAmountData = usedTotalAmountData3;
    //   const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    //   next(errorDetail);
    // } else {
    //   res.status(200).json(usedTotalAmountData);
    //   next();
    // }
    // 異常系(部分エラー)のコード終了
    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.FREEE_EXPIRED, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.dEXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
    // }
  }
};

export default freeeTransactionsController;
