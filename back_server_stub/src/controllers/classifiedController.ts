// import { errorCodes } from 'errors/errorCodes';
// import { InternalServerError } from 'errors/httpErrors';
import { Request, Response, NextFunction } from 'express';

const classifiedController = {
  getSaltValue: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ mobileEncryptSalt: 'saltKey' });
    return next();
    // 正常系のコード終了

    // 異常系取得失敗開始
    // res.status(200).json({ mobileEncryptSalt: '' });
    // return next();
    // 異常系取得失敗終了

    // 異常系開始
    // const errorDetail = new InternalServerError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了
  },

  getClientInfo: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ clientId: 'clientId', clientSecret: 'clientSecret' });
    return next();
    // 正常系のコード終了

    // 異常系取得失敗開始
    // res.status(200).json({ clientId: '', clientSecret: '' });
    // return next();
    // 異常系取得失敗終了

    // 異常系開始
    // const errorDetail = new InternalServerError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了
  }
};

export default classifiedController;
