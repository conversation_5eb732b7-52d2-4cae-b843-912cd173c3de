/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-unused-vars */
import dotenv from 'dotenv';
import { Request, Response, NextFunction } from 'express';
import { updateFreeeOauthState } from '../services/sessionService';
import { jstTime } from '../utils/dateFormat';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { Session } from '../models/session';

dotenv.config();

const freeeLinkController = {
  checkFreeeLinkStatus: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ isLinked: true });
    next();
    // 正常系のコード終了

    // リンクしていない場合のコード開始
    // res.status(200).json({ isLinked: false });
    // next();
    // リンクしていない場合のコード終了

    // 異常系開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了

    // INVALID_REQUESTエラー開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // INVALID_REQUESTエラー終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  },
  linkToFreee: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ message: 'freeeアクセストークンを保存しました。' });
    next();
    // 正常系のコード終了

    // 異常系開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了

    // INVALID_REQUESTエラー開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // INVALID_REQUESTエラー終了
  },
  getFreeeSsoParams: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({
      state: 'state',
      partnerId: 'partnerId',
      exists: false
    });
    next();
    // 正常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了

    // 異常系開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了
  },
  getUrlToFreeeReLinkPage: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始（midori環境向け再連携URL生成）
    const { session } = req;

    const sessionData: Session = {
      sessionId: session.sessionId,
      freeeOauthState: '02a222665b-17b9-43fe-9149-d50d74604719', // ダミー値
      updatedAt: jstTime()
    };

    // セッションIDに紐づけてfreeeOauthStateをDynamoDBに保存
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    updateFreeeOauthState(sessionData);

    // frontに返却するfreee再連携用URLの作成
    const baseParams = new URLSearchParams({
      provider: 'smbc',
      redirect_service_name: 'accounts',
      redirect_end_point: 'public_api'
    }).toString();
    const authorizeParams = new URLSearchParams({
      client_id: process.env.FREEE_CLIENT_ID!,
      prompt: 'select_company',
      redirect_uri: process.env.FREEE_REDIRECT_URI!,
      response_type: 'code',
      state: '02a222665b-17b9-43fe-9149-d50d74604719', // ダミー値
      back_url: process.env.FREEE_BACK_URL!
    }).toString();

    // freee SSOのため、redirect_end_point以降はURLエンコードした値を設定する
    const encodedAuthorize = `%2Fauthorize%3F${encodeURIComponent(`${authorizeParams}`)})`;

    res.status(200).json({
      redirectUrl: `${process.env.FREEE_RELINK_BASE_URL!}?${baseParams}${encodedAuthorize}`
    });
    next();
    // 正常系のコード終了（midori環境向け再連携URL生成）

    // 正常系開始（dev環境ダミーURL返却）
    // res.status(200).json({
    //   redirectUrl: `${process.env
    //     .FREEE_SIGNUP_BASE_URL!}?state=021mhugbFEtLzz5CXQSsPfgBavI7MFt6QRH4LgTT7ennwnXo`
    // });
    // 正常系のコード終了（dev環境ダミーURL返却）

    // 異常系開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了
  },
  checkFreeeLinksByClientId: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({
      exists: false
    });
    next();
    // 正常系のコード終了

    // 異常系開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系終了
  },
  completeFreeeLink: (req: Request, res: Response, next: NextFunction) => {
    res.status(200).json({ message: 'OK' });
    next();
  }
};

export default freeeLinkController;
