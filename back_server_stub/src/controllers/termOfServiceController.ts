/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { Request, Response, NextFunction } from 'express';
import { BadRequestError, InternalServerError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { jstTime } from '../utils/dateFormat';

const termOfServiceController = {
  checkTOSConsentStatus: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    try {
      const tosConsentTypes = [
        {
          valueDoorID: 'ABCDE12345',
          agreeTerm: 'DTP',
          channel: '02',
          createdAt: '2023-12-01T10:00:00.000Z',
          updatedAt: '2023-12-11T10:00:00.000Z'
        }
      ];

      // 利用規定画面表示
      res.status(200).json([]);

      // 利用規定画面非表示(同意済み)
      // res.status(200).json(tosConsentTypes);

      next();
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new InternalServerError(errorCodes.INTERNAL_SERVER_ERROR, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  },

  registerTOSConsentStatus: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    try {
      res.status(200).json({ message: 'ok' });
      next();
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
    // 正常系のコード終了

    // 異常系のコード開始
    //     const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    //     next(errorDetail);
    // 異常系のコード終了
  }
};

export default termOfServiceController;
