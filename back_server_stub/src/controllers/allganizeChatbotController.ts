import { Request, Response, NextFunction } from 'express';
import fs from 'fs/promises';
import path from 'path';
import { allganizeSdkKey } from '../config/config';

const allganizeChatbotController = {
  /**
   * Allganize AIチャットボットHTMLを返却
   * 環境変数からSDKキーを取得してHTMLに埋め込む
   */
  getAllganizeChatbotHtml: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // リクエストヘッダーからsessionIdを取得
      const sessionId = req.headers['sessionid'] || req.headers['x-session-id'] || req.headers['authorization']?.toString().replace('Bearer ', '');

      // sessionIdがある場合の処理
      if (sessionId) {
        console.log('Chatbot accessed by sessionId:', sessionId);

        // TODO: 以下の機能を必要に応じて実装
        // 1. セッション有効性の検証
        // const isValidSession = await validateSession(sessionId);
        // if (!isValidSession) {
        //   return res.status(401).json({ error: 'Invalid session' });
        // }

        // 2. ユーザー別設定の取得
        // const userSettings = await getUserChatbotSettings(sessionId);

        // 3. アクセスログの記録
        // await logChatbotAccess(sessionId, req.ip, req.headers['user-agent']);
      } else {
        console.log('Chatbot accessed without sessionId');
      }

      // HTMLファイルを読み込む
      const htmlPath = path.join(__dirname, '../../public/allganize_chatbot.html');
      const html = await fs.readFile(htmlPath, 'utf8');

      // 環境変数をHTMLに埋め込む
      let htmlWithEnv = html.replace('__ALLGANIZE_SDK_KEY__', allganizeSdkKey);

      // sessionIdをHTMLに埋め込む（JavaScriptで使用可能にする）
      if (sessionId) {
        htmlWithEnv = htmlWithEnv.replace('__SESSION_ID__', sessionId);
      } else {
        htmlWithEnv = htmlWithEnv.replace('__SESSION_ID__', '');
      }

      // レスポンスを送信
      res.setHeader('Content-Type', 'text/html');
      res.send(htmlWithEnv);
    } catch (error) {
      console.error('Error serving chatbot HTML:', error);
      res.status(500).send('Internal Server Error');
      next(error);
    }
  },

  /**
   * Allganize AIチャットボット画面へのリダイレクト
   */
  redirectToAllganizeChatbot: (req: Request, res: Response) => {
    res.redirect('/api/allganize_chatbot');
  }
};

export default allganizeChatbotController;
