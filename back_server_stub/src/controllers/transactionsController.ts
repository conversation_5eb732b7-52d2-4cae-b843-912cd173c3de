/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { NextFunction, Request, Response } from 'express';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import {
  transactionsAprilData,
  transactionsDailyTotalsMockData10,
  transactionsDecemberData,
  transactionsFebraryData,
  transactionsJanuaryData,
  transactionsJulyData,
  transactionsJuneData,
  transactionsMarchData,
  transactionsMayData,
  transactionsMockData,
  transactionsMockDataAugust,
  transactionsMockDataOctober,
  transactionsMockDataSeptember,
  transactionsNovemberData
} from '../data/transactionsData';
import { jstTime } from '../utils/dateFormat';

type RequestQuery = {
  dateFrom: string;
  dateTo: string;
  from: string;
  to: string;
};

const transactionsController = {
  getTransactions: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    let mockResponse;
    const { dateFrom } = req.query as RequestQuery;
    const dateParts = dateFrom.split('-');
    if (dateParts[1] === '01') {
      mockResponse = transactionsJanuaryData;
    } else if (dateParts[1] === '02') {
      mockResponse = transactionsFebraryData;
    } else if (dateParts[1] === '03') {
      mockResponse = transactionsMarchData;
    } else if (dateParts[1] === '04') {
      mockResponse = transactionsAprilData;
    } else if (dateParts[1] === '05') {
      mockResponse = transactionsMayData;
    } else if (dateParts[1] === '06') {
      mockResponse = transactionsJuneData;
    } else if (dateParts[1] === '07') {
      mockResponse = transactionsJulyData;
    } else if (dateParts[1] === '08') {
      mockResponse = transactionsMockDataAugust;
    } else if (dateParts[1] === '09') {
      mockResponse = transactionsMockDataSeptember;
    } else if (dateParts[1] === '10') {
      mockResponse = transactionsMockDataOctober;
    } else if (dateParts[1] === '11') {
      mockResponse = transactionsNovemberData;
    } else if (dateParts[1] === '12') {
      mockResponse = transactionsDecemberData;
    }

    console.log(mockResponse);
    res.status(200).json(mockResponse);
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了

    // バックエンドエラーの開始
    // const errorDetail = new BadRequestError('E001-00001', 'エラー発生');
    // next(errorDetail);
    // バックエンドエラーの終了
  },
  getTransactionsDailyTotals: (req: Request, res: Response, next: NextFunction) => {
    try {
      // 正常系開始
      console.log(transactionsDailyTotalsMockData10);

      res.status(200).json(transactionsDailyTotalsMockData10);
      next();
      // 正常系のコード終了

      // 異常系のコード開始
      // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
      // next(errorDetail);
      // 異常系のコード終了

      // セッションタイムアウトエラーの開始
      // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
      // next(errorDetail);
      // セッションタイムアウトエラーの終了
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
  }
};

export default transactionsController;
