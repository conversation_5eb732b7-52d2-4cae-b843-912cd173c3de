/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response, NextFunction } from 'express';
import { jstTime, serverDateTime } from '../utils/dateFormat';
import {
  freeeWalletablesData,
  freeeCreditData,
  freeeWalletablesAllData
} from '../data/freeeWalletablesData';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const freeeWalletablesController = {
  getWalletables: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    let freeeBankAccountData = freeeWalletablesAllData;
    const walletType: string = req.query.type?.toString() ?? '';

    // typeによって返却データ変更
    if (walletType === 'bank_account') {
      freeeBankAccountData = freeeWalletablesData;
    }

    if (walletType === 'credit_card') {
      freeeBankAccountData = freeeCreditData;
    }
    // サーバーの日時を追加
    freeeBankAccountData.walletables = freeeBankAccountData.walletables!.map((data) => ({
      ...data,
      serverDateTime: serverDateTime()
    }));

    console.log(freeeBankAccountData);
    res.status(200).json(freeeBankAccountData);
    next();
    // 正常系のコード終了

    // freee有効期限切れエラーのコード開始
    // const errorDetail = new BadRequestError(errorCodes.FREEE_EXPIRED, 'エラー発生');
    // next(errorDetail);
    // freee有効期限切れエラーのコード終了

    // 金融機関切れエラーのコード開始(エラーコードはおそらくであり、仮。フロント側はfreee有効期限切エラーでない場合のロジックを入れている)
    // const errorDetail = new BadRequestError('I003-00004', 'エラー発生');
    // next(errorDetail);
    // 金融機関切れエラーのコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  }
};

export default freeeWalletablesController;
