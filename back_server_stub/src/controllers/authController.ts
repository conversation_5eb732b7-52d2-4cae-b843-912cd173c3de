/* eslint-disable max-len */
/* eslint-disable operator-linebreak */
/* eslint-disable @typescript-eslint/require-await */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { components } from '../schemas/schema';
import { saveSession, updateState } from '../services/sessionService';
import { AuthorizationError, BadRequestError, InternalServerError } from '../errors/httpErrors';
import { Session, UserInfo } from '../models/session';
import { expirationTime, jstTime } from '../utils/dateFormat';
import { errorCodes } from '../errors/errorCodes';
import { samlResponse as samlResponseMethod } from '../utils/samlResponse';
import { getIdaasEncryptedCookieValue } from '../utils/getIdaasEncryptedCookieValue';
import { HighRiskUser } from '../models/highRiskUser';
import { saveHighRiskUser } from '../services/highRiskUserService';
import { Ids } from '../types/ids';
import { judgeRisk } from '../services/judgeRiskService';
import { ValueDoorUser } from '../types/valueDoorUser';

const authController = {
  verifySamlResponse: async (
    req: Request<components['schemas']['SamlResponse']>,
    res: Response,
    next: NextFunction
  ) => {
    // // 正常系のコード開始
    const { samlResponse, caulisSessionId } = req.body as components['schemas']['SamlResponse'];
    console.log(`caulisSessionId : ${caulisSessionId ?? ''}\n`);

    // VDIDに紐づくメールアドレスがない場合エラーを返す
    // スタブでは場合わけが困難なためカウリスセッションIDの値でエラーを返却する
    if (caulisSessionId === '123456789') {
      throw new AuthorizationError(
        errorCodes.NOT_EMAIL_WITH_VDID,
        'VDIDに紐づくメールアドレス存在チェックエラー'
      );
    }

    // 暗号化クッキー生成
    // ID紐付け有りの場合の暗号化Cookie生成 開始
    const encryptedCookie = getIdaasEncryptedCookieValue(
      '5d8a59b6-fb58-4c61-be13-5992c695986a', // スタブデータ
      '<EMAIL>' // スタブデータ
      // 'e9b9f048-9f23-4e42-9270-41dbfe823eab', // userId (IDaaS STG環境)(<EMAIL>)
      // '<EMAIL>' // dtpId (IDaaS STG環境)
      // '5d8a59b6-fb58-4c61-be13-5992c695986a', // userId (IDaaS STG環境)(<EMAIL>)
      // '<EMAIL>' // dtpId (IDaaS STG環境)
      // 'adadc73c-c0f1-44fa-a3e6-3f18cba362bd', // userId (IDaaS Dev環境)
      // '<EMAIL>' // dtpId (IDaaS Dev環境)
    );
    // ID紐付け有りの場合 終了

    // 怪しさ判定API判定結果（数値は仮）
    // H:ログイン不可
    // const judgeRiskResponse = 'H';
    // M:OTP認証を要求
    // const judgeRiskResponse = 'M';
    // L:ログイン可能
    const judgeRiskResponse = 'L';

    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const date = new Date();
    const issueInstant = dayjs(date).tz().toISOString();

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '**************',
      clientId: '**********',
      companyName: '取引先名',
      userName: '利用者名',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'リヨウシャ',
      email: '<EMAIL>',
      telNo: '**********',
      companyCode: '**************',
      loginType: '214',
      contractType: '1',
      authorizations: {
        web21: {
          accountInquiryAuth: true,
          generalTransferAuth: true,
          salaryTransferAuth: true,
          localTaxPaymentAuth: true,
          approvalAuth: true,
          transferAuth: true,
          fileSendingAuth: true,
          taxesOrFeesPaymentAuth: true,
          accountTransferAuth: true,
          transferDestinationCheckAuth: true,
          dataConversionAuth: true,
          accountInquiryOnSmartPhoneAuth: true,
          transferOnSmartPhoneAuth: true,
          approvalOnSmartPhoneAuth: true,
          preSettingAccountInfoOnSmartPhoneAuth: true,
          settingSecurityOnSmartPhoneAuth: true,
          preSettingAccountInfoAuth: true,
          settingSecurityAuth: true
        },
        gets: {
          inboundTransferAuth: true,
          lcExportAuth: true,
          exportBillHandlingAuth: true,
          outboundTransferAuth: true,
          lcImportAuth: true,
          forexInfoExchangeAuth: true,
          corpoForexDeliveryAuth: true,
          parentChildContractAuth: true,
          selfSecurityAccessAuth: true,
          generalSecurityAccessAuth: true
        }
      },
      otpId: '0',
      otpSerialNo: '01-2345678-9',
      usesOtp: true,
      otpKind: '01',
      otpDeviceStatus: '04',
      isOtpReissuing: true,
      otpSerialNoReissue: '02-3456789-0',
      otpKindReissue: '01',
      otpDeviceStatusReissue: '01',
      otpReissueAction: '2',
      otpNonOtpExpiredDate: '********',
      otpServiceCode: '0'
    };

    // 危険度判定
    const judgeRiskResult = await judgeRisk(
      ids,
      new Date(issueInstant),
      valueDoorUser,
      judgeRiskResponse
    );

    if (judgeRiskResult) {
      const { highRiskUserId } = judgeRiskResult;

      res.status(200).json({ highRiskUserId, encryptedCookie });
      return next();
    }

    // セッションID払い出し
    const sessionId: string = uuidv4();

    // // OTP関連の権限は必要になった際に項目追加
    const userInfoData: UserInfo = {
      ninsyoKbn: '01',
      compId: '123456789**********0',
      compName: '取引先名',
      userSeiMei: '利用者名',
      userTyp: '01',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      keiyakuType: '1',
      telNo: '***********',
      kigyoCd: 'KIGYOCODE12345',
      userAuths: {
        web21: {
          accountInquiryAuth: true,
          generalTransferAuth: true,
          salaryTransferAuth: true,
          localTaxPaymentAuth: true,
          approvalAuth: true,
          transferAuth: true,
          fileSendingAuth: true,
          taxesOrFeesPaymentAuth: true,
          accountTransferAuth: true,
          transferDestinationCheckAuth: true,
          dataConversionAuth: true,
          accountInquiryOnSmartPhoneAuth: true,
          transferOnSmartPhoneAuth: true,
          approvalOnSmartPhoneAuth: true,
          preSettingAccountInfoOnSmartPhoneAuth: true,
          settingSecurityOnSmartPhoneAuth: true,
          preSettingAccountInfoAuth: true,
          settingSecurityAuth: true
        },
        gets: {
          inboundTransferAuth: true,
          lcExportAuth: true,
          exportBillHandlingAuth: true,
          outboundTransferAuth: true,
          lcImportAuth: true,
          forexInfoExchangeAuth: true,
          corpoForexDeliveryAuth: true,
          parentChildContractAuth: true,
          selfSecurityAccessAuth: true,
          generalSecurityAccessAuth: true
        }
      },
      loginType: '217'
    };

    const sessionData: Session = {
      sessionId,
      vdId: '**********',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant,
      userInfo: userInfoData,
      state: null,
      freeeOauthState: null,
      expirationTime: expirationTime(),
      createdAt: jstTime(),
      updatedAt: jstTime()
    };

    // セッションIDをDynamoDBに保存
    await saveSession(sessionData);

    res.status(200).json({ sessionId, encryptedCookie });
    return next();
    // // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  },

  createAuthScreenInfo: async (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    try {
      // セッション情報格納
      const { session } = req;

      const sessionData: Session = {
        sessionId: session.sessionId,
        state: 'dummy state',
        updatedAt: jstTime()
        // 正常系のコード終了

        // 異常系のコード開始
        // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
        // next(errorDetail);
        // 異常系のコード終了

        // 異常系の際にはここも消す
      };

      // セッションIDに紐づけてstateをDynamoDBに保存
      await updateState(sessionData);

      res
        .status(200)
        .json({ fromEbizParam: '44GU5Yip55So44GC44KK44GM44Go44GG44GU44GW44GE44G+44GZ44CC' });
      next();
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
    // ここまで
  },

  getAccessToken: async (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    try {
      res.status(200).json({ message: 'アクセストークンを取得しました。' });
      next();
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new InternalServerError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  },

  checkAuthorization: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    try {
      //  認可ステータス「02:認可済」を返却
      res.status(200).json({
        hasKigyoCd: true,
        hasNoAuth: false,
        hasInquiryAuth: true,
        hasTransferAuth: true,
        hasApprovalAuth: true,
        authorizationStatus: '02'
      });
      next();
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
    // 正常系のコード終了
    // 正常系（企業コードなしの場合）のコード開始
    // try {
    //   res.status(200).json({
    //     hasKigyoCd: false,
    //     errorCode: errorCodes.ACCOUNT_NOT_KIGYO_CD,
    //     errorMessage: 'サービス時間外です。',
    //     hasFaq: false,
    //     hasNoAuth: false,
    //     hasInquiryAuth: true,
    //     hasTransferAuth: true,
    //     hasApprovalAuth: true,
    //     authorizationStatus: '02'
    //   });
    //   next();
    // } catch (error) {
    //   console.log('エラーが発生しました', error);
    //   next(error);
    // }
    // 正常系（企業コードなしの場合）のコード終了
    // 03:認可不可の異常系コード開始
    // const authorizationError = new AuthorizationError(
    //   errorCodes.ACCOUNT_NOT_AUTHORIZATION,
    //   '口座情報へのアクセスするための権限がありません。口座照会権限設定を見直してください。'
    // );
    // next(authorizationError);
    // 権限エラーの異常系コード終了
    // 03:認可不可の異常系コード開始
    //     const authorizationError = new AuthorizationError(
    //       errorCodes.ACCOUNT_NOT_AUTHORIZATION,
    //       '口座情報へのアクセスするための権限がありません。口座照会権限設定を見直してください。'
    //     );
    //     next(authorizationError);
    // 03:認可不可の異常系コード終了
    // 04:異常発生のコード開始
    // const abnormalAuthorizationError = new AuthorizationError(
    //   errorCodes.ABNORMAL_AUTHORIZATION_ERROR,
    //   '口座情報へのアクセスするための権限がありません。口座照会権限設定を見直してください。'
    // );
    // next(abnormalAuthorizationError);
    // 04:異常発生の異常系コード終了
    // 企業コード、Web21参照権限チェックがNGの場合
    // const authorizationError = new AuthorizationError(
    //  errorCodes.ACCOUNT_NOT_KIGYO_CD,
    //  'ログインに必要な権限がありませんでした。権限設定を見直してください。'
    // );
    // next(authorizationError);
    // 企業コード、Web21参照権限チェックがNGの場合終了
    // 認可拒否履歴ありの場合
    // const authorizationError = new AuthorizationError(
    //   errorCodes.AUTHORIZATION_DENIAL_HISTORY,
    //   '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。'
    // );
    // next(authorizationError);
    // 認可拒否履歴ありの場合終了
    // 異常系のコード開始
    //  const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    //  next(errorDetail);
    // 異常系のコード終了
  },

  checkGetsAuthorization: (req: Request, res: Response, next: NextFunction) => {
    res.status(200).json({ getsAuthStatus: '' });
    next();
  },

  login: (req: Request, res: Response, next: NextFunction) => {
    try {
      const { vdId, password } = req.body;
      if (password === 'a') {
        // 異常系開始;
        const authorizationError = new AuthorizationError(
          errorCodes.ACCOUNT_NOT_AUTHORIZATION,
          '口座情報へのアクセスするための権限がありません。口座照会権限設定を見直してください。'
        );
        return next(authorizationError);
        // 異常系終了
      }
      // 正常系開始
      return res.status(200).json({
        samlResponse: btoa(samlResponseMethod({ valueDoorId: vdId }))
      });
      // 正常系終了
    } catch (error) {
      return next(error);
    }
  },

  loginSuspiciousDetection: (req: Request, res: Response, next: NextFunction) => {
    try {
      // 正常系開始
      res
        .status(200)
        .json({ message: 'ValueDoorログイン失敗時の怪しさ判定リクエストを実行しました。' });
      // 正常系終了

      // 異常系のコード開始
      // const errorDetail = new InternalServerError(errorCodes.INTERNAL_SERVER_ERROR, 'エラー発生');
      // next(errorDetail);
      // 異常系のコード終了
      next();
    } catch (error) {
      next(error);
    }
  },

  getWeb21SsoSaml: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 正常系開始
      // 遷移先の取得（振込：'web21-transfer-top'、承認：'web21-approval-top'）
      const { service } = req.params;
      // 後続の正常系処理にて表示画面を出し分けるため、パスパラメータによって返却値を変更
      if (service === 'web21-transfer-top') {
        res.status(200).json({ toWeb21Param: 'transfer' });
      } else {
        res.status(200).json({ toWeb21Param: 'approval' });
      }
      // 正常系終了

      // セッションタイムアウトエラーの開始
      // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
      // next(errorDetail);
      // セッションタイムアウトエラーの終了

      // 異常系のコード開始
      // const errorDetail = new InternalServerError(errorCodes.INTERNAL_SERVER_ERROR, 'エラー発生');
      // next(errorDetail);
      // 異常系のコード終了
      next();
    } catch (error) {
      next(error);
    }
  },

  getEncryptedCookieForSso: (req: Request, res: Response, next: NextFunction) => {
    try {
      const { session } = req;
      // 暗号化クッキー生成
      const isDtpIdLinked = session.dtpId;
      const userId = isDtpIdLinked
        ? session.userId // dtpId紐付け有りの場合、userUidを使用
        : session.vdId; // dtpId紐付け無しの場合、valueDoorIdを使用

      const emailAddress = isDtpIdLinked // dtpId紐付け有りの場合、dtpIdを使用
        ? session.dtpId
        : session.userInfo?.Email; // dtpId紐付け無しの場合、VDIDに紐づくメールアドレスを使用

      const encryptedCookie = getIdaasEncryptedCookieValue(userId ?? '', emailAddress ?? '');
      res.status(200).json({ encryptedCookie });
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default authController;
