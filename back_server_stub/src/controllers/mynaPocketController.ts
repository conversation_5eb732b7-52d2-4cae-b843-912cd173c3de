/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { NextFunction, Request, Response } from 'express';
import { jstTime } from '../utils/dateFormat';
import {
  oAuthData,
  serviceCheckData,
  identificationData,
  errorData1,
  errorData2,
  errorData3,
  errorData4,
  errorData5,
  errorData6,
  errorData7,
  errorData8,
  errorData9,
  errorData10,
  errorData11,
  errorData12,
  errorData13,
  errorData14,
  errorData15,
  errorData16,
  errorData17,
  errorData18,
  errorData19,
  errorData20,
  errorData21,
  errorData22,
  errorData23,
  errorData24,
  errorData25,
  errorData26,
  errorData29,
  errorData30,
  errorData31,
  errorData32,
  errorData33,
  errorData37,
  errorData39,
  errorData49
  // errorData50
} from '../data/mynaPocketData';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

// エラー時のステータスコード
const errorStatusCode400 = 400;
const errorStatusCode401 = 401;
const errorStatusCode404 = 404;
const errorStatusCode405 = 405;
const errorStatusCode406 = 406;
const errorStatusCode422 = 422;
const errorStatusCode429 = 429;
const errorStatusCode500 = 500;
const errorStatusCode503 = 503;

const mynaPocketController = {
  // CC_OAuthAPI
  getCcOauth: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    console.log(`[Request] ${req.method} ${req.url} ${res.statusCode} [${jstTime()}]\n`, oAuthData);

    res.status(200).json(oAuthData);
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // res.status(errorStatusCode503).json(errorData8);
    // next();
    // 異常系のコード終了
  },

  // サービス利用情報提供API
  checkServiceStatus: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    console.log(
      `[Request] ${req.method} ${req.url} ${res.statusCode} [${jstTime()}]\n`,
      serviceCheckData
    );

    res.status(200).json(serviceCheckData);
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // res.status(errorStatusCode503).json(errorData8);
    // next();
    // 異常系のコード終了
  },

  // 本人確認API
  verifyIdentification: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    console.log(`[Request] ${req.method} ${req.url} ${res.statusCode} [${jstTime()}]\n`, req.body);

    res.status(200).json(identificationData);
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // res.status(errorStatusCode503).json(errorData2);
    // next();
    // 異常系のコード終了
  }
};

export default mynaPocketController;
