/* eslint-disable consistent-return */
/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { NextFunction, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { Ids } from 'types/ids';
import { errorCodes } from '../errors/errorCodes';
import { BadRequestError, NotFoundError } from '../errors/httpErrors';
import { Session, UserInfo } from '../models/session';
import { saveSession, updateDtpId, updateDtpIdAndUserId } from '../services/sessionService';
import { getIdaasEncryptedCookieValue } from '../utils/getIdaasEncryptedCookieValue';
import { components } from '../schemas/schema';
import { expirationTime, jstTime } from '../utils/dateFormat';
import { saveHighRiskUser } from '../services/highRiskUserService';
import { HighRiskUser } from '../models/highRiskUser';
import { judgeRisk } from '../services/judgeRiskService';
import { ValueDoorUser } from '../types/valueDoorUser';
import { getIdaasDecryptCookieValue } from '../utils/getIdaasDecryptCookieValue';

const dtpIdController = {
  loginDtpId: async (
    req: Request<components['schemas']['DtpIdLoginRequest']>,
    res: Response,
    next: NextFunction
  ) => {
    try {
      // 正常系開始
      // DTPIDに紐づくVDID・userIDを取得
      const { dtpId, password, caulisSessionId } =
        req.body as components['schemas']['DtpIdLoginRequest'];
      console.log(`caulisSessionId : ${caulisSessionId ?? ''}\n`);

      const vdId = '0000000001';

      // VDID存在しない
      // const vdId = '';

      // DEV環境IDaaS接続可能userId
      let userId = 'adadc73c-c0f1-44fa-a3e6-3f18cba362bd';
      // TEST環境IDaaS接続可能userId
      if (dtpId === '<EMAIL>') {
        userId = 'e9b9f048-9f23-4e42-9270-41dbfe823eab';
        // IDAAS STG環境
      } else if (dtpId === '<EMAIL>') {
        userId = '5d8a59b6-fb58-4c61-be13-5992c695986a';
      }

      // VDIDが空の場合は口座開設中のエラーを返却
      if (!vdId) {
        const notFoundError = new NotFoundError(
          errorCodes.ACCOUNT_CREATION_IN_PROGRESS,
          '口座開設中のため、ログインできません。口座開設後にログインしてください。'
        );
        return next(notFoundError);
      }

      const date = new Date();
      const notBefore = dayjs(date).tz().toISOString(); // 現在時刻

      // VD権限情報を取得（ph05_SP8時点でバックエンド未実装のためベタ書きスタブ）
      // バックエンドからの返却値がxml形式の場合はextractDataFromXmlと同様に値を取得すること
      const vdAuthorityAcquisitionResponseData = {
        errCode: '',
        errMsg: '',
        ninsyoKbn: '01',
        compId: '**************',
        compName: '取引先名',
        userSeiMei: '利用者名',
        userTyp: '01',
        compAtrbt: '01',
        userKn: 'リヨウシャ',
        Email: '<EMAIL>',
        telNo: '**********',
        otpId: 0,
        otpSerialNo: '01-2345678-9',
        otpAplKbn: true,
        otpKind: '01',
        otpDeviceStatus: '04',
        otpReissueKbn: true,
        otpSerialNoReissue: '02-3456789-0',
        otpKindReissue: '01',
        otpDeviceStsReissue: '01',
        otpReissueAction: '2',
        otpNonOtpExpiredDate: '********',
        otpServiceCode: '0',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: {
            inboundTransferAuth: true,
            lcExportAuth: true,
            exportBillHandlingAuth: true,
            outboundTransferAuth: true,
            lcImportAuth: true,
            forexInfoExchangeAuth: true,
            corpoForexDeliveryAuth: true,
            parentChildContractAuth: true,
            selfSecurityAccessAuth: true,
            generalSecurityAccessAuth: true
          }
        },
        loginType: '217',
        kigyoCd: '**************',
        keiyakuType: '1',
        issueInstant: notBefore
      };
      const vdAuthorityAcquisitionResponse = vdAuthorityAcquisitionResponseData;

      const valueDoorUser: ValueDoorUser = {
        id: vdId,
        authenticationMethod: '01',
        representativeAccount: vdAuthorityAcquisitionResponseData.compId,
        clientId: '**********',
        companyName: vdAuthorityAcquisitionResponseData.compName,
        userName: vdAuthorityAcquisitionResponseData.userSeiMei,
        userType: '01',
        accessLevel: '01',
        usernameKana: vdAuthorityAcquisitionResponseData.userKn,
        email: undefined,
        telNo: vdAuthorityAcquisitionResponseData.telNo,
        companyCode: vdAuthorityAcquisitionResponseData.kigyoCd,
        loginType: '214',
        contractType: '1',
        authorizations: vdAuthorityAcquisitionResponseData.userAuths,
        otpId: vdAuthorityAcquisitionResponseData.otpId.toString(),
        otpSerialNo: vdAuthorityAcquisitionResponseData.otpSerialNo,
        usesOtp: true,
        otpKind: '01',
        otpDeviceStatus: '04',
        isOtpReissuing: true,
        otpSerialNoReissue: '02-3456789-0',
        otpKindReissue: '01',
        otpDeviceStatusReissue: '01',
        otpReissueAction: '2',
        otpNonOtpExpiredDate: '********',
        otpServiceCode: '0'
      };

      // 暗号化クッキー生成 DTPIDログインの際は暗号化クッキーに渡すのは必ずuserIdとdtpIdになる
      const encryptedCookie = getIdaasEncryptedCookieValue(userId, dtpId);

      // 怪しさ判定API判定結果（数値は仮）
      // H:ログイン不可
      // const judgeRiskResponse = 'H';
      // M:OTP認証を要求
      // const judgeRiskResponse = 'M';
      // L:ログイン可能
      const judgeRiskResponse = 'L';

      const ids: Ids = {
        dtpId,
        userUid: userId,
        valueDoorId: vdId
      };

      // 危険度判定
      const judgeRiskResult = await judgeRisk(
        ids,
        new Date(vdAuthorityAcquisitionResponseData.issueInstant),
        valueDoorUser,
        judgeRiskResponse
      );

      if (judgeRiskResult) {
        const { highRiskUserId } = judgeRiskResult;

        res.status(200).json({ highRiskUserId, encryptedCookie });
        return next();
      }

      // セッションID払い出し
      const sessionId: string = uuidv4();

      // DTPIDログイン時の利用者情報クラスに格納
      const userInfoData: UserInfo = {
        ninsyoKbn: vdAuthorityAcquisitionResponse.ninsyoKbn,
        compId: vdAuthorityAcquisitionResponse.compId,
        compName: vdAuthorityAcquisitionResponse.compName,
        userSeiMei: vdAuthorityAcquisitionResponse.userSeiMei,
        userTyp: vdAuthorityAcquisitionResponse.userTyp,
        compAtrbt: vdAuthorityAcquisitionResponse.compAtrbt,
        userKn: vdAuthorityAcquisitionResponse.userKn,
        Email: vdAuthorityAcquisitionResponse.Email,
        telNo: vdAuthorityAcquisitionResponse.telNo,
        otpId: vdAuthorityAcquisitionResponse.otpId,
        otpSerialNo: vdAuthorityAcquisitionResponse.otpSerialNo,
        otpAplKbn: vdAuthorityAcquisitionResponse.otpAplKbn,
        otpKind: vdAuthorityAcquisitionResponse.otpKind,
        otpDeviceStatus: vdAuthorityAcquisitionResponse.otpDeviceStatus,
        otpReissueKbn: vdAuthorityAcquisitionResponse.otpReissueKbn,
        otpSerialNoReissue: vdAuthorityAcquisitionResponse.otpSerialNoReissue,
        otpKindReissue: vdAuthorityAcquisitionResponse.otpKindReissue,
        otpDeviceStsReissue: vdAuthorityAcquisitionResponse.otpDeviceStsReissue,
        otpReissueAction: vdAuthorityAcquisitionResponse.otpReissueAction,
        otpNonOtpExpiredDate: vdAuthorityAcquisitionResponse.otpNonOtpExpiredDate,
        otpServiceCode: vdAuthorityAcquisitionResponse.otpServiceCode,
        userAuths: vdAuthorityAcquisitionResponse.userAuths,
        loginType: vdAuthorityAcquisitionResponse.loginType,
        kigyoCd: vdAuthorityAcquisitionResponse.kigyoCd,
        keiyakuType: vdAuthorityAcquisitionResponse.keiyakuType
      };

      // セッション情報クラスに格納;
      const sessionData: Session = {
        sessionId,
        vdId,
        dtpId,
        userId,
        issueInstant: vdAuthorityAcquisitionResponse.issueInstant,
        userInfo: userInfoData,
        state: null,
        freeeOauthState: null,
        expirationTime: expirationTime(),
        createdAt: jstTime(),
        updatedAt: jstTime()
      };

      // セッションIDをDynamoDBに保存
      await saveSession(sessionData);

      res.status(200).json({
        sessionId,
        encryptedCookie
      });
      return next();
      // 正常系終了

      // 異常系のコード開始
      // const errorDetail = new NotFoundError(
      //   errorCodes.Not_Login_DutTO_VdId,
      //   '口座開設中のため、ログインできません。口座開設後にログインしてください。'
      // );
      // next(errorDetail);
      // 異常系のコード終了
    } catch (error) {
      return next(error);
    }
  },
  updateDtpId: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // セッション情報クラスに格納
      const sessionData: Session = {
        sessionId: req.session.sessionId,
        dtpId: '<EMAIL>',
        updatedAt: jstTime()
      };
      // DynamoDBのセッション情報（dtpId）を更新
      await updateDtpId(sessionData);

      res.status(200).json({ message: 'DTPIDを更新しました' });
      return next();
    } catch (error) {
      return next(error);
    }
  },
  issueAndLinkDtpId: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // リクエストボディーから暗号化クッキーを取得
      const { encryptedCookie } = req.body as components['schemas']['IssueAndLinkDtpIdRequest'];

      // 暗号化クッキーを復号し、ユーザーIDを取得
      const userId = getIdaasDecryptCookieValue(encryptedCookie.ticket!, 1);

      // セッションからvdIdを取得(DTPO-6976で実装)(いったんベタがき)
      const valueDoorId = '1234567890';
      // ID紐付け情報登録API実行（w/ userId,vdId）(DTPO-6976で実装)

      // ID紐付け情報リクエストを実行
      // ID紐付け情報レスポンスからdtpIdを取得(ベタがき)
      const dtpId = '<EMAIL>';

      // セッション情報クラスに格納
      const sessionData: Session = {
        sessionId: req.session.sessionId,
        dtpId,
        userId,
        updatedAt: jstTime()
      };

      // DynamoDBのセッション情報（dtpId,userId）を更新
      await updateDtpIdAndUserId(sessionData);

      res.status(200).json({ dtpId, userId, valueDoorId, encryptedCookie });
      return next();
      // 異常系開始
      // const errorDetail = new BadRequestError(errorCodes.NOT_LINK_ERROR, 'エラー発生');
      // next(errorDetail);
      // 異常系終了
    } catch (error) {
      return next(error);
    }
  },
  deleteDtpIdLink: (req: Request, res: Response, next: NextFunction) => {
    try {
      // 正常系のコード開始
      res.status(200).json({ message: '紐づけ解除に成功しました。' });
      next();
      // 正常系のコード終了

      // 異常系開始
      // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
      // next(errorDetail);
      // 異常系終了

      // セッションタイムアウトエラーの開始
      // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
      // next(errorDetail);
      // セッションタイムアウトエラーの終了
    } catch (error) {
      return next(error);
    }
  }
};
export default dtpIdController;
