/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { NextFunction, Request, Response } from 'express';
import { accountSettingsData } from '../data/accountSettingsData';
import { jstTime } from '../utils/dateFormat';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const accountDisplayController = {
  getAccountsDisplay: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    // console.log(accountSettingsData);
    // res.status(200).json(accountSettingsData);
    // next();
    // 正常系のコード終了
    // 異常系のコード開始
    const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    next(errorDetail);
    // 異常系のコード終了
    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  },

  updateAccountsDisplay: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    res.status(200).json({ message: '口座表示設定を更新しました。' });
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  }
};

export default accountDisplayController;
