/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Request, Response, NextFunction } from 'express';
import { jstTime } from '../utils/dateFormat';
import { BadRequestError, NotFoundError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { searchAddressResponseData } from '../data/searchAddressResponsData';
import {
  getOpenAccountApplicationTemporaryResponseData01,
  getOpenAccountApplicationTemporaryResponseData02,
  getOpenAccountApplicationTemporaryResponseData03,
  getOpenAccountApplicationTemporaryResponseData04,
  getOpenAccountApplicationTemporaryResponseData05,
  getOpenAccountApplicationTemporaryResponseData06,
  getOpenAccountApplicationTemporaryResponseData07,
  getOpenAccountApplicationTemporaryResponseData08,
  getOpenAccountApplicationTemporaryResponseData09,
  getOpenAccountApplicationTemporaryResponseData10,
  getOpenAccountApplicationTemporaryResponseData11,
  getOpenAccountApplicationTemporaryResponseData3a1,
  getOpenAccountApplicationTemporaryResponseData3a2,
  getOpenAccountApplicationTemporaryResponseData3b1,
  getOpenAccountApplicationTemporaryResponseData3b2,
  getScreeningStatusesResponseData,
  getScreeningStatusesResponseData10,
  getScreeningStatusesResponseData2,
  getScreeningStatusesResponseData3,
  getScreeningStatusesResponseData4,
  getScreeningStatusesResponseData5,
  getScreeningStatusesResponseData6,
  getScreeningStatusesResponseData7,
  getScreeningStatusesResponseData8,
  getScreeningStatusesResponseData9
} from '../data/identityVerificationData';
import { checkClosingStatus } from '../utils/checkClosingStatus';
import { closingTime } from '../config/timeConfig';

const identityVerificationController = {
  getOpenAccountApplicationTemporary: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    try {
      const referenceNumber = req.query.referenceNumber as string;
      let getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData3a1;

      // STEP1 代表者 本人確認実施済み
      if (req.query.referenceNumber === '*************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData3a1;
      }

      // STEP1 代表者 本人確認実施済み
      if (req.query.referenceNumber === '*************0') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData3a1;
      }

      // STEP2 代表者 本人確認未実施
      if (req.query.referenceNumber === '*************1') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData3a2;
      }

      // STEP2 代表者 本人確認実施済み
      if (req.query.referenceNumber === '*************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData3b1;
      }

      // STEP2 代理人 本人確認未実施
      if (req.query.referenceNumber === '*************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData3b2;
      }

      // STEP2 代理人 本人確認実施済み
      if (req.query.referenceNumber === '*************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData01;
      }

      // STEP3 代理人 本人確認実施済み
      if (req.query.referenceNumber === '*************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData02;
      }

      // STEP4 実質的支配者1 本人確認実施済み
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData03;
      }

      // STEP4 実質的支配者1 本人確認未実施
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData04;
      }

      // STEP4 実質的支配者2 本人確認実施済み
      if (req.query.referenceNumber === '*************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData05;
      }

      // STEP4 実質的支配者2 本人確認未実施
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData06;
      }

      // STEP4 実質的支配者3 本人確認実施済み
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData07;
      }

      // STEP4 実質的支配者3 本人確認未実施
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData08;
      }

      // STEP4 実質的支配者4 本人確認実施済み
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData09;
      }

      // STEP4 実質的支配者4 本人確認未実施
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData10;
      }

      // STEP5 代表者 本人確認未実施
      if (req.query.referenceNumber === '**************') {
        getOpenAccountApplicationTemporary = getOpenAccountApplicationTemporaryResponseData11;
      }

      res.status(200).json(getOpenAccountApplicationTemporary);
      next();
    } catch (error) {
      next(error);
    }
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  },
  getOpenAccountApplicationScreeningStatuses: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始

    // 口座開設済みかつ不備情報がない場合
    // let getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData;

    // 口座開設未実施かつ不備情報がない場合
    let getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ代表者の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData3;
    }

    // 口座開設未実施かつ代理人の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData4;
    }

    // 口座開設未実施かつ実質的支配者1の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData5;
    }

    // 口座開設未実施かつ実質的支配者2の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData6;
    }

    // 口座開設未実施かつ実質的支配者3の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData7;
    }

    // 口座開設未実施かつ実質的支配者4の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData8;
    }

    // 口座開設実施済みかつ代表者の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData9;
    }

    // 正常系開始 口座開設済みかつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設実施済みかつ代理人の不備情報がある場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData10;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '**************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '**************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '*************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '**************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // 口座開設未実施かつ不備情報がない場合
    if (req.query.referenceNumber === '**************') {
      getOpenAccountApplicationScreeningStatuses = getScreeningStatusesResponseData2;
    }

    // デフォルトのレスポンス（お手続き番号が有効かつ、口座開設の申請未済かつ、本人確認未実施の場合）
    res.status(200).json(getOpenAccountApplicationScreeningStatuses);
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  },
  searchAddress: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    try {
      const postcode = req.query.postcode as string;
      if (postcode === '9999999') {
        const notFoundError = new NotFoundError(
          errorCodes.NOT_FOUND_ADDRESS,
          '該当する住所が見つかりません。'
        );
        next(notFoundError);
      } else {
        res.status(200).json(searchAddressResponseData);
        next();
      }
    } catch (error) {
      next(error);
    }
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_FOUND_ADDRESS, '該当する住所が見つかりませんでした。お手数ですが、正しい郵便番号をご確認の上、再度ご入力ください。');
    // next(errorDetail);
    // 異常系のコード終了
  },
  saveIdentityVerification: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ message: '本人確認情報を保存しました。' });
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError('E005-00008', 'エラー発生');
    // // errorCodes.INVALID_REQUEST
    // next(errorDetail);
    // 異常系のコード終了
  },
  requestEkycUrl: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ ekycUrl: process.env.REQUEST_EKYC_URL_ENDPOINT });
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_FOUND_UUID, 'エラー発生');
    // next(errorDetail);
    // // 異常系のコード終了
  },
  getUuid: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    res.status(200).json({ uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' });
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_FOUND_UUID, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  },
  checkCorporateWebStatus: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    const response = checkClosingStatus(closingTime);
    res.status(200).json({ isOpened: response });
    return next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_FOUND_UUID, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  }
};

export default identityVerificationController;
