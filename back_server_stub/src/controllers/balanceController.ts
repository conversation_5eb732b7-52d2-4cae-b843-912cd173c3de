/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { NextFunction, Request, Response } from 'express';
import {
  balanceData1,
  balanceData2,
  balanceData3,
  balanceData4,
  balanceData5,
  balanceData6,
  balanceData7
} from '../data/balanceData';
import { DATE_FORMAT_SLASH, serverDate, serverTime, jstTime } from '../utils/dateFormat';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const balanceController = {
  getBalance: (req: Request, res: Response, next: NextFunction) => {
    let bankAccountData = balanceData1;
    const accountId = req.params.accountIds;

    if (accountId === '*************') {
      bankAccountData = balanceData2;
    }

    if (accountId === '*************') {
      bankAccountData = balanceData3;
    }

    if (accountId === '*************') {
      bankAccountData = balanceData4;
    }

    if (accountId === '*************') {
      bankAccountData = balanceData5;
    }

    if (accountId === '*************') {
      bankAccountData = balanceData6;
    }

    if (accountId === '*************') {
      bankAccountData = balanceData7;
    }

    // サーバーの日時を追加
    bankAccountData.accounts = bankAccountData.accounts!.map((data) => ({
      ...data,
      baseDate: serverDate(DATE_FORMAT_SLASH),
      baseTime: serverTime()
    }));

    // 正常系開始
    console.log(bankAccountData);
    res.status(200).json(bankAccountData);
    next();
    // 正常系のコード終了

    // 異常系(部分エラー)のコード開始;
    // if (accountId === '*************' || accountId === '*************') {
    // const errorDetail = new BadRequestError(errorCodes.INVALID_REQUEST, 'エラー発生');
    // next(errorDetail);
    // } else {
    // res.status(200).json(bankAccountData);
    // next();
    // }
    // 異常系(部分エラー)のコード終了;

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  }
};

export default balanceController;
