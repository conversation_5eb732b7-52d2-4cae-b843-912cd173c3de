/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { Request, Response, NextFunction } from 'express';
import { deleteSession } from '../services/sessionService';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { jstTime } from '../utils/dateFormat';

const workBlockageController = {
  getWorkBlockageStatus: (req: Request, res: Response, next: NextFunction) => {
    // 正常系のコード開始
    try {
      res.status(200).json({ status: '0' });
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了
  }
};

export default workBlockageController;
