/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Request, Response, NextFunction } from 'express';
import { getUserInfo } from '../services/sessionService';
import { components } from '../schemas/schema';
import { userInfoData, usersData } from '../data/usersData';
import { jstTime } from '../utils/dateFormat';
import { BadRequestError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';

const usersController = {
  getUsers: (req: Request, res: Response, next: NextFunction) => {
    // 正常系開始
    console.log(usersData);
    res.status(200).json(usersData);
    next();
    // 正常系のコード終了

    // 異常系のコード開始
    // const errorDetail = new BadRequestError(errorCodes.INQUIRY_PERIOD_EXCEEDED, 'エラー発生');
    // next(errorDetail);
    // 異常系のコード終了

    // アクセストークンエラーのコード開始
    //   const errorDetail = new BadRequestError(errorCodes.NOT_ERROR_CODE, 'エラー発生');
    //   next(errorDetail);
    // 異常系のコード終了

    // セッションタイムアウトエラーの開始
    // const errorDetail = new BadRequestError(errorCodes.EXPIRED_SESSION, 'エラー発生');
    // next(errorDetail);
    // セッションタイムアウトエラーの終了
  },

  getUserInfo: async (
    req: Request,
    res: Response<components['schemas']['UserInfo']>,
    next: NextFunction
    // eslint-disable-next-line @typescript-eslint/require-await
  ) => {
    try {
      // 正常系開始
      console.log(userInfoData);

      // モックデータを返却する
      res.status(200).json(userInfoData);
      next();
      // // 正常系のコード終了

      // 異常系のコード開始
    //   const errorDetail = new BadRequestError(errorCodes.NOT_FOUND_USER_INFO, 'エラー発生');
    //   next(errorDetail);
      // 異常系のコード終了

      // セッションタイムアウトエラーの開始
      // const errorDetail = new BadRequestError(errorCodes.AUTHORIZATION_ERROR, 'エラー発生');
      // next(errorDetail);
      // セッションタイムアウトエラーの終了
    } catch (error) {
      console.log('エラーが発生しました', error);
      next(error);
    }
  }
};

export default usersController;
