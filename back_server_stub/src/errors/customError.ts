export class CustomError extends Error {
  statusCode: number;

  errorCode: string;

  errorDetail: Record<string, unknown>;

  /**
   * カスタムエラーのインスタンス生成
   * @param statusCode  レスポンスステータスコード
   * @param errorObject エラーメッセージとエラーコードを含むオブジェクト
   * @param detail      エラー詳細情報
   */
  constructor(statusCode: number, code: string, detail = {}) {
    super(code);
    this.statusCode = statusCode;
    this.errorCode = code;
    this.errorDetail = detail;
  }

  /**
   * エラーレスポンスJSONを生成し返す
   *
   * @returns エラーレスポンスのbody情報
   */
  createResponseJson(): Record<string, unknown> {
    return {
      error: {
        statusCode: this.statusCode,
        errorCode: this.errorCode,
        message: this.message,
        errorDetail: this.errorDetail
      }
    };
  }
}
