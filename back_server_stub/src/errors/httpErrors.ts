/* eslint-disable max-classes-per-file */
import { CustomError } from './customError';

export class BadRequestError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(400, code, errorDetail);
  }
}

export class ValidationError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(400, code, errorDetail);
  }
}

export class AuthorizationError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(401, code, errorDetail);
  }
}

export class ForbiddenError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(403, code, errorDetail);
  }
}

export class NotFoundError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(404, code, errorDetail);
  }
}

export class DBDuplicationError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(409, code, errorDetail);
  }
}
export class InternalServerError extends CustomError {
  /**
   * @param code エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(code: string, errorDetail: string) {
    super(500, code, errorDetail);
  }
}
