/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Router, Request, Response, NextFunction } from 'express';
import classifiedController from '../controllers/classifiedController';
import allganizeChatbotController from '../controllers/allganizeChatbotController';
import web21OtpController from '../controllers/web21OtpController';
import { onEnd, onSuccess } from '../middlewares/successHandler';
import mynaPocketController from '../controllers/mynaPocketController';
import accountDisplayController from '../controllers/accountDisplayController';
import transactionsController from '../controllers/transactionsController';
import usersController from '../controllers/usersController';
import authController from '../controllers/authController';
import balanceController from '../controllers/balanceController';
import sessionController from '../controllers/sessionController';
import termOfServiceController from '../controllers/termOfServiceController';
import { validate } from '../middlewares/requestValidator';
import {
  accessTokenValidationRules,
  accountDisplayValidationRules,
  balanceValidationRules,
  consentStatusValidationRules,
  freeeTransactionsRules,
  requestEkycUrlValidationRules,
  getOpenAccountApplicationScreeningStatusesValidationRules,
  getOpenAccountApplicationTemporaryValidationRules,
  getWorkBlockageStatusValidationRules,
  identityVerificationValidationRules,
  loginDtpIdValidationRules,
  loginValidationRules,
  searchAddressValidationRules,
  transactionsDailyTotalsValidationRules,
  transactionsValidationRules,
  usedTotalAmountValidationRules,
  walletablesValidationRules,
  linkToFreeeValidationRules,
  getUuidRules,
  loginSuspiciousDetectionValidationRules
} from '../validators/validationRules';
import freeeLinkController from '../controllers/freeeLinkController';
import freeeWalletablesController from '../controllers/freeeWalletablesController';
import freeeTransactionsController from '../controllers/freeeTransactionsController';
import { authenticateUser } from '../middlewares/authMiddleware';
import clientActionController from '../controllers/clientActionController';
import { samlResponse } from '../utils/samlResponse';
import identityVerificationController from '../controllers/identityVerificationController';
import { onStart } from '../middlewares/startHandler';
import workBlockageController from '../controllers/workBlockageController';
import valueDoorController from '../controllers/valueDoorController';
import dtpIdController from '../controllers/dtpIdController';

const router = Router();

function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * @description
 * valuedoorのログインAPIのモック
 * id = 0000000001 の時、正常系
 * id = 0000000002 の時、電子署名エラー
 * id = 0000000003 の時、有効期限エラー
 */
router.post('/v1/oauth2/login', (req, res) => {
  try {
    const { id } = req.body;

    // base64変換してから返却
    if (id === '0000000002') {
      // 電子署名エラー
      res.status(200).json({
        from_vd_param: btoa(samlResponse({ hasInvalidSignature: true, valueDoorId: id }))
      });
    } else if (id === '0000000003') {
      // 有効期限エラー
      res
        .status(200)
        .json({ from_vd_param: btoa(samlResponse({ isExpired: true, valueDoorId: id })) });
    } else {
      // 正常系
      res.status(200).json({ from_vd_param: btoa(samlResponse({ valueDoorId: id })) });
    }
  } catch (error) {
    console.log(error);
    throw error;
  }
});

// VD認証APIスタブ
router.post(
  '/v1/login',
  loginValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(authController.login),
  asyncHandler(onEnd)
);

// VD認証API
router.post(
  '/v1/authentication/ex66w06',
  // '/v1/avve9h901/loginidspforward',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.login),
  asyncHandler(onEnd)
);

// 有効期限切れAPI
router.post(
  '/v1/avve9h025/changesp',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.informExpiration),
  asyncHandler(onEnd)
);

// パスワード変更API
router.post(
  '/v1/avve9h003/changesp',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.changePassword),
  asyncHandler(onEnd)
);

// VDログアウトAPI
router.post(
  '/v1/avve9h901/logoutdtpsp',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.logout),
  asyncHandler(onEnd)
);

// VD傘下システム遷移API
router.post(
  '/v1/authentication/ex66w07',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.navigateVdSystem),
  asyncHandler(onEnd)
);

// OTP有効化API（即時）
router.post(
  '/v1/avve9h211/validsp',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.enableOtpNow),
  asyncHandler(onEnd)
);

// OTP有効化API（スキップ）
router.post(
  '/v1/avve9h211/latersp',
  asyncHandler(onStart),
  asyncHandler(valueDoorController.enableOtpLater),
  asyncHandler(onEnd)
);

// DTPID認証API
router.post(
  '/v1/loginDtpId',
  loginDtpIdValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(dtpIdController.loginDtpId),
  asyncHandler(onEnd)
);

// 怪しさ判定API
router.post(
  '/v1/loginSuspiciousDetection',
  loginSuspiciousDetectionValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(authController.loginSuspiciousDetection),
  asyncHandler(onEnd)
);

// SAML検証API
router.post(
  '/v1/saml/verify',
  asyncHandler(onStart),
  asyncHandler(authController.verifySamlResponse),
  asyncHandler(onEnd)
);

// 口座開設申込情報一時保存確認API
router.get(
  '/v1/identityVerification/openAccountApplication/temporary',
  getOpenAccountApplicationTemporaryValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.getOpenAccountApplicationTemporary),
  asyncHandler(onEnd)
);

// 口座開設行内審査ステータス確認API
router.get(
  '/v1/identityVerification/openAccountApplication/screeningStatuses',
  getOpenAccountApplicationScreeningStatusesValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.getOpenAccountApplicationScreeningStatuses),
  asyncHandler(onEnd)
);

// 住所検索API
router.get(
  '/v1/identityVerification/address',
  searchAddressValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.searchAddress),
  asyncHandler(onEnd)
);

// 本人確認情報保存API
router.post(
  '/v1/identityVerification',
  identityVerificationValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.saveIdentityVerification),
  asyncHandler(onEnd)
);

// eKYC URL生成要求API;
router.post(
  '/v1/identityVerification/ekycUrl',
  requestEkycUrlValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.requestEkycUrl),
  asyncHandler(onEnd)
);

// 法人Web開局状況確認
router.get(
  '/v1/identityVerification/corporateWebStatus',
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.checkCorporateWebStatus),
  asyncHandler(onEnd)
);

// 本人確認情報UUID取得API;
router.get(
  '/v1/identityVerification/uuid',
  getUuidRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.getUuid),
  asyncHandler(onEnd)
);

// 機能閉塞設定取得API
router.get(
  '/v1/work/blockage',
  getWorkBlockageStatusValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(workBlockageController.getWorkBlockageStatus),
  asyncHandler(onEnd)
);

// freee連携完了API
router.get(
  '/freee/auth',
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.completeFreeeLink),
  asyncHandler(onEnd)
);

// セッションID取得API
router.get(
  '/v1/sessionId',
  asyncHandler(onStart),
  asyncHandler(sessionController.getSessionId),
  asyncHandler(onEnd)
);

// 以下3つのAPIはJPKIチームのAPI
// CC_OAuthAPI
router.post('/v1/cc/oauth2/token', asyncHandler(mynaPocketController.getCcOauth));

// サービス利用情報提供API
router.post('/v1/servicecheck', asyncHandler(mynaPocketController.checkServiceStatus));

// 本人確認API
router.post('/v1/identification', asyncHandler(mynaPocketController.verifyIdentification));

// SALT値取得API
router.get(
  '/v1/classified/salt',
  asyncHandler(onStart),
  asyncHandler(classifiedController.getSaltValue),
  asyncHandler(onEnd)
);
// クライアント情報取得API
router.get(
  '/v1/classified/client',
  asyncHandler(onStart),
  asyncHandler(classifiedController.getClientInfo),
  asyncHandler(onEnd)
);

// ユーザー認証ミドルウェアを適用
router.use(asyncHandler(authenticateUser));

// 利用者情報取得API
router.get(
  '/v1/bank/users',
  asyncHandler(onStart),
  asyncHandler(usersController.getUsers),
  asyncHandler(onSuccess)
);

// ユーザー情報取得API
router.get(
  '/v1/bank/userInfo',
  asyncHandler(onStart),
  asyncHandler(usersController.getUserInfo),
  asyncHandler(onSuccess)
);

// 指定口座残高取得API
router.get(
  '/v1/bank/balance/:accountIds',
  balanceValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(balanceController.getBalance),
  asyncHandler(onSuccess)
);

// 口座表示設定取得API
router.get(
  '/v1/accountSettings',
  asyncHandler(onStart),
  asyncHandler(accountDisplayController.getAccountsDisplay),
  asyncHandler(onSuccess)
);

// 口座表示設定更新API
router.put(
  '/v1/accountSettings',
  accountDisplayValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(accountDisplayController.updateAccountsDisplay),
  asyncHandler(onSuccess)
);

// 入出金明細取得API
router.get(
  '/v1/bank/accounts/:accountId/transactions',
  transactionsValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(transactionsController.getTransactions),
  asyncHandler(onSuccess)
);

// 入出金日次合計額取得API
router.get(
  '/v1/bank/accounts/:accountId/transactions/dailyTotals',
  asyncHandler(onStart),
  transactionsDailyTotalsValidationRules,
  validate,
  asyncHandler(transactionsController.getTransactionsDailyTotals),
  asyncHandler(onSuccess)
);

// 認可画面呼出情報生成API
router.post(
  '/v1/authScreenInfo/create',
  asyncHandler(onStart),
  asyncHandler(authController.createAuthScreenInfo),
  asyncHandler(onSuccess)
);

// トークン要求API
router.post(
  '/v1/token',
  asyncHandler(onStart),
  accessTokenValidationRules,
  validate,
  asyncHandler(authController.getAccessToken),
  asyncHandler(onSuccess)
);

// 認可要否確認API
router.get(
  '/v1/authorization/check',
  asyncHandler(onStart),
  asyncHandler(authController.checkAuthorization),
  asyncHandler(onSuccess)
);

// Gets権限確認API
router.get(
  '/v1/getsAuthorization/check',
  asyncHandler(onStart),
  asyncHandler(authController.checkGetsAuthorization),
  asyncHandler(onSuccess)
);

// Web21SSO用SAML取得
router.get(
  '/v1/sso/:service',
  asyncHandler(onStart),
  asyncHandler(authController.getWeb21SsoSaml),
  asyncHandler(onSuccess)
);

// IDaaS・freeeSSO用Cookie取得
router.get(
  '/v1/idaas/sso/cookie',
  asyncHandler(onStart),
  asyncHandler(authController.getEncryptedCookieForSso),
  asyncHandler(onSuccess)
);

// セッション情報削除API
router.delete('/v1/session', asyncHandler(onStart), asyncHandler(sessionController.deleteSession));

// 利用規定同意状況確認API
router.get(
  '/v1/tos/consentStatus/check',
  asyncHandler(onStart),
  asyncHandler(termOfServiceController.checkTOSConsentStatus),
  asyncHandler(onSuccess)
);

// 利用規定同意状況登録API
router.post(
  '/v1/tos/consentStatus/register',
  asyncHandler(onStart),
  consentStatusValidationRules,
  validate,
  asyncHandler(termOfServiceController.registerTOSConsentStatus),
  asyncHandler(onSuccess)
);

// 顧客操作履歴保存API
router.post(
  '/v1/clientActionLog',
  asyncHandler(onStart),
  asyncHandler(clientActionController.saveClientActionLog),
  asyncHandler(onSuccess)
);

// freee連携確認API
router.get(
  '/v1/freee/link',
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.checkFreeeLinkStatus),
  asyncHandler(onSuccess)
);

// freee連携API
router.post(
  '/v1/freee/link',
  linkToFreeeValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.linkToFreee),
  asyncHandler(onSuccess)
);

// freeeサインアップ画面URL取得API
router.get(
  '/v1/freee/sso',
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.getFreeeSsoParams),
  asyncHandler(onSuccess)
);

// freee再連携画面URL取得API
router.get(
  '/v1/freee/relink',
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.getUrlToFreeeReLinkPage),
  asyncHandler(onSuccess)
);

// 取引先ID紐付け確認API
router.get(
  '/v1/freee/links/check/partnerId',
  asyncHandler(freeeLinkController.checkFreeeLinksByClientId),
  asyncHandler(onSuccess)
);

// freee口座一覧取得API
router.get(
  '/v1/freee/walletables',
  walletablesValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(freeeWalletablesController.getWalletables),
  asyncHandler(onSuccess)
);

// freee口座明細取得API
router.get(
  '/v1/freee/transactions',
  freeeTransactionsRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(freeeTransactionsController.getFreeeTransactions),
  asyncHandler(onSuccess)
);

// freee口座利用合計金額取得API
router.get(
  '/v1/freee/transactions/totalExpense',
  usedTotalAmountValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(freeeTransactionsController.getUsedTotalAmount),
  asyncHandler(onSuccess)
);

// DTPID更新API
router.put(
  '/v1/dtpId',
  validate,
  asyncHandler(dtpIdController.updateDtpId),
  asyncHandler(onSuccess)
);

// DTPID紐づけ情報登録API
router.post(
  '/v1/dtpId/link',
  // issueAndLinkDtpIdValidationRules,
  // validate,
  asyncHandler(onStart),
  asyncHandler(dtpIdController.issueAndLinkDtpId),
  asyncHandler(onSuccess)
);

// DTPID紐づけ解除API
router.delete(
  '/v1/dtpId/link',
  asyncHandler(onStart),
  asyncHandler(dtpIdController.deleteDtpIdLink),
  asyncHandler(onSuccess)
);

// Web21Otp復号API
router.get(
  '/v1/web21Otp/decrypt',
  asyncHandler(onStart),
  asyncHandler(web21OtpController.getDecryptWeb21Otp),
  asyncHandler(onSuccess)
);

// 暗号化VDID検証API
router.post(
  '/v1/encryptedVdid/check',
  asyncHandler(onStart),
  asyncHandler(web21OtpController.checkEncryptedVdid),
  asyncHandler(onSuccess)
);

// VDID暗号化API
router.get(
  '/v1/encryptedVdid/encrypt',
  asyncHandler(onStart),
  asyncHandler(web21OtpController.getEncryptedVdid),
  asyncHandler(onSuccess)
);

// Allganize AIチャットボット画面へのリダイレクト
router.get(
  '/allganize-chatbot',
  asyncHandler(allganizeChatbotController.redirectToAllganizeChatbot)
);

// Allganize AIチャットボットHTMLを返却
router.get(
  '/allganize_chatbot',
  asyncHandler(allganizeChatbotController.getAllganizeChatbotHtml)
);

// プロキシAPIは不要になりました - 環境変数を直接HTMLに渡す方式に変更

export default router;
