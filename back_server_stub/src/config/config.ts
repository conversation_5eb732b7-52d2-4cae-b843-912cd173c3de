export const awsConfig = {
  region: 'ap-northeast-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
};

export const sessionTableName = process.env.SESSION_TABLE_NAME || 'dtp-mobile-local-session';
export const highRiskUserName = process.env.HIGH_RISK_USER_TABLE_NAME || 'dtp-mobile-local-high-risk-device-user';
export const errorMasterTableName = process.env.ERROR_MASTER_TABLE_NAME || 'dtp-local-error-master';

// Allganize SDK APIキー
export const allganizeSdkKey = process.env.ALLGANIZE_SDK_KEY || 'sfgtertjughjgjhfgd';
