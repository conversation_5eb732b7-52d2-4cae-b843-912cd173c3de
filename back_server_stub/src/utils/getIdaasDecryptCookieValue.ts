import { createDecipheriv } from 'crypto';

// 暗号化クッキー（ticket）を復号化して対象の値をindexで指定して取得
export const getIdaasDecryptCookieValue = (ticket: string, index: number) => {
  // 環境変数から共通鍵を取得
  const KEY = Buffer.from(process.env.IDAAS_COOKIE_ENCRYPTION_KEY ?? '', 'hex');

  // KEYが32バイト（256ビット）であることを確認
  if (KEY.length !== 32) {
    throw new Error('Invalid encryption key length. Expected 32 bytes for AES-256.');
  }

  // Base64URLデコードして暗号化データを取得
  let base64 = ticket.replace(/-/g, '+').replace(/_/g, '/');
  if (base64.length % 4 !== 0) {
    // パディングの補充
    base64 += '='.repeat(4 - (base64.length % 4));
  }
  const encryptedData = Buffer.from(base64, 'base64');

  // 共通鍵をインプットにSHA-256(ECB)復号化インスタンス生成
  const decipher = createDecipheriv('aes-256-ecb', KEY, null);
  // 自動パディングを有効化
  decipher.setAutoPadding(true);

  // データ復号化
  const decryptedData = Buffer.concat([decipher.update(encryptedData), decipher.final()]);
  // 復号化されたデータから対象の値をindexを指定して取得
  // nonce：0, userUid: 1, emailAddress: 2, timeStamp: 3
  const value = decryptedData.toString().split('|')[index];

  return value;
};
