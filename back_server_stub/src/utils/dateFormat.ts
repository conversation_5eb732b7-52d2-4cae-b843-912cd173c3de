import dotenv from 'dotenv';
import { format, addMinutes, getUnixTime } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

dotenv.config();

export const DATE_FORMAT_HYPHEN = 'yyyy-M-d HH:mm:ss zzz';
export const DATE_FORMAT_SLASH = 'yyyy/M/d';
export const DATE_FORMAT_YYYY_MM_DD = 'yyyy-MM-dd';
export const TIME_FORMAT = 'H:mm';
export const JST = 'Asia/Tokyo';

export const nowDate = (): Date => new Date();
export const jstTime = (): string => format(utcToZonedTime(nowDate(), JST), DATE_FORMAT_HYPHEN);
export const jstTimeDate = (): Date => utcToZonedTime(nowDate(), JST);
export const serverDate = (dateFormat: string): string => {
  let dateString = '';
  if (dateFormat === DATE_FORMAT_SLASH) {
    dateString = format(utcToZonedTime(nowDate(), JST), DATE_FORMAT_SLASH);
  }
  if (dateFormat === DATE_FORMAT_YYYY_MM_DD) {
    dateString = format(utcToZonedTime(nowDate(), JST), DATE_FORMAT_YYYY_MM_DD);
  }
  return dateString;
};
export const serverTime = (): string => format(utcToZonedTime(nowDate(), JST), TIME_FORMAT);
export const serverDateTime = (): string => `${serverDate(DATE_FORMAT_SLASH)} ${serverTime()}`;
export const unixTime = (): number => getUnixTime(nowDate());

// セッション有効期限
export const expirationTime = (): string => {
  const addMin = Number(process.env.ADD_MINUTES) ? Number(process.env.ADD_MINUTES) : 10;
  return getUnixTime(addMinutes(nowDate(), addMin)).toString();
};

export const yyyymmddhhmmss = (date: Date): string => `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, '0')}${date
  .getDate()
  .toString()
  .padStart(2, '0')}${date.getHours().toString().padStart(2, '0')}${date
  .getMinutes()
  .toString()
  .padStart(2, '0')}${date.getSeconds().toString().padStart(2, '0')}`;

// baseTimeの表示制御
export const options: Intl.DateTimeFormatOptions = {
  timeStyle: 'short'
};

// 「分まで」のタイムスタンプ(UNIXタイム)を取得
export const unixTimestampInMinutes = (): number => {
  // 現在のUNIXタイムスタンプ（秒）
  const currentUnixTime = Math.floor(Date.now() / 1000);
  // 60で割って「分まで」のUNIXタイムを取得
  return Math.floor(currentUnixTime / 60);
};
