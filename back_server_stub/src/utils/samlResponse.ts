/* server.ts */
import { SignedXml } from 'xml-crypto';
import { KeyLike, randomFillSync, publicEncrypt, createCipheriv, randomBytes } from 'crypto';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import valueDoorUsers, { ValueDoorUserAttributes } from '../data/valueDoorUsersData';

dayjs.extend(timezone);
dayjs.extend(utc);

const getRandomId = (digit: number) => {
  const S = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const N = digit;
  return Array.from(randomFillSync(new Uint8Array(N)))
    .map((n) => S[n % S.length])
    .join('');
};

const getAssertion = (
  responseId: string,
  valueDoorId: string,
  notBefore: string,
  notOnOrAfter: string,
  userInfo: ValueDoorUserAttributes
) => {
  let assertion: string = `<saml2:Assertion xmlns:saml2="urn:oasis:names:tc:SAML:2.0:assertion" ID="VV${responseId}AS" IssueInstant="${notBefore}"><saml2:Subject><saml2:NameID>${valueDoorId}</saml2:NameID><saml2:SubjectConfirmation><saml2:SubjectConfirmationData InResponseTo="VUolMa1CJpHnXJ87" NotBefore="${notBefore}" NotOnOrAfter="${notOnOrAfter}" Recipient="'https://valuedoor.smbc.co.jp/api/saml/valuedoor"/></saml2:SubjectConfirmation></saml2:Subject><saml2:AttributeStatement>`;
  const attributesXmlInner = Object.entries(userInfo)
    .map(([attrName, attrValue]) => {
      const valueAsArray = Array.isArray(attrValue) ? attrValue : [attrValue];
      const valuesXml = valueAsArray
        .map((value) => `<saml2:AttributeValue>${value}</saml2:AttributeValue>`)
        .join('');
      return `<saml2:Attribute Name='${attrName}'>${valuesXml}</saml2:Attribute>`;
    })
    .join('');

  assertion += attributesXmlInner;
  assertion += '</saml2:Attribute></saml2:AttributeStatement></saml2:Assertion>';
  return assertion;
};

const getSamlResponse = (
  responseId: string,
  authnRequestID: string,
  notBefore: string,
  destinationUrl: string,
  encryptedAesKey: string,
  encryptedAssertionInfoByAesKey: string
) => {
  const defaultSamlAuthnResponse = `<saml2p:Response xmlns:saml2p="urn:oasis:names:tc:SAML:2.0:protocol" Destination="${destinationUrl}" ID="VV${responseId}" InResponseTo="VL${authnRequestID}" IssueInstant="${notBefore}" Version="2.0"><saml2:Issuer xmlns:saml2="urn:oasis:names:tc:SAML:2.0:assertion">https://valuedoor.smbc.co.jp/oauth2</saml2:Issuer><saml2p:Status><saml2p:StatusCode Value="urn:oasis:names:tc:SAML:2.0:status:Success"/></saml2p:Status><saml2:EncryptedAssertion xmlns:saml2="urn:oasis:names:tc:SAML:2.0:assertion"><xenc:EncryptedData xmlns:xenc="http://www.w3.org/2001/04/xmlenc#" Id="_e3ec8fffff3d628f924968cf98d23657" Type="http://www.w3.org/2001/04/xmlenc#Element"><xenc:EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#aes128-cbc"/><ds:KeyInfo xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><xenc:EncryptedKey Id="_86151cc85532c7516d3e570cb02359f1" Recipient="https://valuedoor.smbc.co.jp/mankan"><xenc:EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#rsa-oaep-mgf1p"><ds:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/></xenc:EncryptionMethod><xenc:CipherData><xenc:CipherValue>${encryptedAesKey}</xenc:CipherValue></xenc:CipherData></xenc:EncryptedKey></ds:KeyInfo><xenc:CipherData><xenc:CipherValue>${encryptedAssertionInfoByAesKey}</xenc:CipherValue></xenc:CipherData></xenc:EncryptedData></saml2:EncryptedAssertion></saml2p:Response>`;
  return defaultSamlAuthnResponse;
};

export const samlResponse = ({
  isExpired = false,
  hasInvalidSignature = false,
  breakOnIvalidId = false,
  valueDoorId
}: {
  isExpired?: boolean;
  hasInvalidSignature?: boolean;
  breakOnIvalidId?: boolean;
  valueDoorId: string;
}) => {
  const date = new Date();

  const responseId = getRandomId(14);
  const authnRequestID = getRandomId(14);
  // console.log(responseId);
  // console.log(authnRequestID);

  const notBefore = isExpired
    ? dayjs(date).add(-1, 'month').tz().toISOString()
    : dayjs(date).tz().toISOString(); // 現在時刻
  const notOnOrAfter = isExpired
    ? dayjs(date).add(10, 'm').add(-1, 'month').tz().toISOString()
    : dayjs(date).add(10, 'm').tz().toISOString(); // 現在時刻 ＋ 10分

  // リダイレクト先URL
  const destinationUrl = 'https://dtp-smbc.co.jp/api/login';

  if (breakOnIvalidId && !valueDoorUsers[valueDoorId]) {
    throw Error('invalid id');
  }

  const userInfo = valueDoorUsers[valueDoorId] ?? {
    ninsyoKbn: '01',
    compId: '99992105067486',
    compName: 'デジタルタッチポイント企業１',
    userSeiMei: '利用者１',
    userTyp: '03',
    compAtrbt: '01',
    userKn: 'ﾘﾖｳｼｬ1',
    Email: '<EMAIL>',
    telNo: '0120444444',
    loginType: '214',
    kigyoCd: '20230828010101',
    kigyoCdResultCd: '',
    userAuths: [
      '04:11111111111111111111',
      '05:11111111111111111111',
      '1:11110111111111111011',
      '11:1100000000000000011'
    ],
    keiyakuType: '2'
  };

  // Assert部の作成（ユーザデータの範囲）
  const assertionXml = getAssertion(responseId, valueDoorId, notBefore, notOnOrAfter, userInfo);
  // console.log(`assertionXML is ${assertionXml}`);

  // 電子署名の作成(VDの秘密鍵で署名)
  const sig = new SignedXml();
  sig.addReference({
    xpath: "//*[local-name()='Assertion']",
    transforms: [
      'http://www.w3.org/2000/09/xmldsig#enveloped-signature',
      'http://www.w3.org/2001/10/xml-exc-c14n#'
    ],
    digestAlgorithm: 'http://www.w3.org/2001/04/xmlenc#sha256'
  });
  sig.signatureAlgorithm = 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256';
  sig.privateKey = !hasInvalidSignature
    ? (process.env.VALUEDOOR_PRIVATEKEY_DEV as KeyLike)
    : (process.env.DTP_PRIVATEKEY_DEV as KeyLike); // 不正な鍵を仕込んでcheckSignatureに失敗させる
  sig.computeSignature(assertionXml, { prefix: 'ds' });
  const signedXml = sig.getSignedXml();

  // deprecatedと出るが、指定された暗号化アルゴリズムが「aes-128-cbc」のため使用。
  // 共通鍵の生成(Assert部を暗号化する鍵)
  const aesKey = randomBytes(16);
  const iv = randomBytes(16);
  const cipher = createCipheriv('aes-128-cbc', aesKey, iv);

  // cipher処理はupdate以外にfinalメソッドを用いて暗号化を行う。
  // updateメソッドのみだと256bitの塊ごとに暗号化するため、暗号化されない残りが出てしまうためらしい。
  // Assert部分の暗号化（共通鍵で）
  const encryptedAssertionFirst = cipher.update(signedXml); // 電子署名の付与
  const finalEncryptedAssertion = cipher.final();
  const encryptedAssertion = Buffer.concat([iv, encryptedAssertionFirst, finalEncryptedAssertion]);

  // 共通鍵はDTPの公開鍵で暗号化する。
  // 共通鍵の暗号化(DTOの公開鍵で暗号化)

  const publicKey = process.env.DTP_CERTIFICATE_DEV as KeyLike;
  const encryptedAesKey = publicEncrypt(
    {
      key: publicKey,
      oaepHash: 'sha1'
    },
    Buffer.from(aesKey)
  );

  const result = getSamlResponse(
    responseId,
    authnRequestID,
    notBefore,
    destinationUrl,
    encryptedAesKey.toString('base64'),
    encryptedAssertion.toString('base64')
  );
  return result;
};
