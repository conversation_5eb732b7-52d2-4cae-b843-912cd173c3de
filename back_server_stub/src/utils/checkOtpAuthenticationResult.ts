/* eslint-disable operator-linebreak */
import { getIdaasDecryptCookieValue } from './getIdaasDecryptCookieValue';

// OTP認証結果検証
export const checkOtpAuthenticationResult = (
  emailAddress: string,
  ticket: string,
  idaasTicket: string
): boolean => {
  const emailAddressOfIdaasTicket = getIdaasDecryptCookieValue(idaasTicket, 2);
  const idaasTimeStamp = getIdaasDecryptCookieValue(idaasTicket, 3);
  const timeStamp = getIdaasDecryptCookieValue(ticket, 3);
  console.log(emailAddress);
  console.log(ticket);
  console.log(idaasTicket);
  console.log(emailAddressOfIdaasTicket);
  console.log(idaasTimeStamp);
  console.log(timeStamp);

  console.log(emailAddress === emailAddressOfIdaasTicket);
  console.log(ticket !== idaasTicket);
  console.log(idaasTimeStamp > timeStamp);

  return (
    emailAddress === emailAddressOfIdaasTicket && // メールアドレスが一致
    ticket !== idaasTicket && // 暗号化Cookieのticketが同一でない
    idaasTimeStamp > timeStamp // IDaaSで発行した暗号化Cookieのタイムスタンプが新しい
  );
};
