/* eslint-disable @typescript-eslint/restrict-template-expressions */
import { createCipheriv, randomBytes } from 'crypto';

// Base64URLエンコード関数
const base64UrlEncode = (buffer: Buffer): string => buffer.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/g, '');

// 乱数生成関数
const generateNonce = (length: number) => {
  const characters = '0123456789!@#$%^&*()_+-=[]{};:\'",.<>?';

  return Array.from(randomBytes(length), (byte) => characters[byte % characters.length]).join('');
};

// 暗号化クッキー生成関数
export const getIdaasEncryptedCookieValue = (userUid: string, emailAddress: string) => {
  // 環境変数から共通鍵を取得
  // const KEY = Buffer.from('42d0567952a374bd9919b995ed20eb86916fc0aa372ea326134b4a4bb805f9c8' ?? '', 'hex');
  const KEY = Buffer.from(process.env.IDAAS_COOKIE_ENCRYPTION_KEY ?? '', 'hex');
  console.log(`IDAAS_COOKIE_ENCRYPTION_KEY: ${process.env.IDAAS_COOKIE_ENCRYPTION_KEY}`);
  console.log(`IDAAS_COOKIE_DOMAIN: ${process.env.IDAAS_COOKIE_DOMAIN}`);
  console.log(`userUid: ${userUid}`);
  console.log(`emailAddress: ${emailAddress}`);
  // 共通化鍵をインプットにSHA-256(ECB)暗号化インスタンス生成
  const cipher = createCipheriv('aes-256-ecb', KEY, null);
  // UNIXタイムスタンプ生成
  const timeStamp = Math.round(Date.now() / 1000);
  // 半角数字記号10文字の乱数を生成
  const nonce = generateNonce(10);
  // 平文ticket生成
  const data = `${nonce}|${userUid}|${emailAddress}|${timeStamp}`;
  // 暗号化ticket生成
  const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
  // Base64URLエンコードした暗号化ticket（文字列）を格納
  const ticket = base64UrlEncode(encryptedData);

  const domain = process.env.IDAAS_COOKIE_DOMAIN ?? '';
  const path = '/';
  const secure = 'true';
  const httponly = 'true';

  // 各クッキー情報をオブジェクトとして保存
  const cookieInfo = {
    ticket,
    domain,
    path,
    secure,
    httponly
  };

  console.log(cookieInfo);

  return cookieInfo;
};
