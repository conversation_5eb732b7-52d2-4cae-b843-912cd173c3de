/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */


export interface paths {
  "/api/v1/bank/users": {
    /**
     * 利用者情報取得
     * @description DTPバックエンドから利用者情報を取得する
     */
    get: operations["getUsers"];
  };
  "/api/v1/bank/userInfo": {
    /**
     * ユーザー情報取得
     * @description DynamoDBからセッションIDに紐づくユーザー情報を取得する
     */
    get: operations["getUserInfo"];
  };
  "/api/v1/bank/balance/{accountIds}": {
    /**
     * 指定口座残高取得
     * @description DTPバックエンドから口座識別子に紐づく法人口座の残高を取得する
     */
    get: operations["getBalance"];
  };
  "/api/v1/accountSettings": {
    /**
     * 口座表示設定取得
     * @description DTPバックエンドから口座表示設定を取得する
     */
    get: operations["getAccountsDisplay"];
    /**
     * 口座表示設定更新
     * @description DTPバックエンドへ口座の表示設定情報を連携する
     */
    put: operations["updateAccountsDisplay"];
  };
  "/api/v1/bank/accounts/{accountId}/transactions": {
    /**
     * 入出金明細取得
     * @description DTPバックエンドから口座識別子に紐づく入出金明細情報を取得する
     */
    get: operations["getTransactions"];
  };
  "/api/v1/bank/accounts/{accountId}/transactions/dailyTotals": {
    /**
     * 入出金日次合計額取得
     * @description DTPバックエンドから口座識別子に紐づく入出金の日次合計額を取得する
     */
    get: operations["getTransactionsDailyTotals"];
  };
  "/api/v1/saml/verify": {
    /**
     * SAML検証
     * @description ValueDoorログイン時に発行されるSAMLレスポンスをバックエンドに連携する
     */
    post: operations["verifySamlResponse"];
  };
  "/api/v1/loginDtpId": {
    /**
     * DTPID認証API
     * @description DTPID/PWを元にバックエンドから権限情報を取得し、セッションID・暗号化クッキーを返却する
     */
    post: operations["loginDtpId"];
  };
  "/api/v1/loginSuspiciousDetection": {
    /**
     * 怪しさ判定
     * @description ValueDoorログイン失敗時にフロント側から受け取るVDIDとポップアップエラーメッセージ（ErrMesID）を元にカウリスの怪しさ判定を行う
     */
    post: operations["loginSuspiciousDetection"];
  };
  "/api/v1/dtpId": {
    /**
     * DTPID更新API
     * @description DTPバックエンド経由でIDaaSのVDID紐付け情報を取得し、セッション情報を更新する
     */
    put: operations["updateDtpId"];
  };
  "/api/v1/dtpId/link": {
    /**
     * DTPID紐づけ情報登録API
     * @description DTPバックエンド経由でIDaaSにてDTPIDの発行および、VDIDとの紐付け情報を登録する
     */
    post: operations["issueAndLinkDtpId"];
    /**
     * DTPID紐づけ解除API
     * @description VDIDに紐づくDTPIDの連携を解除する
     */
    delete: operations["deleteDtpIdLink"];
  };
  "/api/v1/sessionId": {
    /**
     * セッションID取得API
     * @description 高リスクユーザーIDをもとにセッションの詰め替えを行い、セッションIDを取得する
     */
    get: operations["getSessionId"];
  };
  "/api/v1/authScreenInfo/create": {
    /**
     * 認可画面呼出情報生成
     * @description セッションIDをもとにバックエンドからリクエストパラメータを生成する
     */
    post: operations["createAuthScreenInfo"];
  };
  "/api/v1/token": {
    /**
     * トークン要求
     * @description バックエンドからAnserBizSOLのアクセストークンを取得する
     */
    post: operations["getAccessToken"];
  };
  "/api/v1/authorization/check": {
    /**
     * 認可要否確認
     * @description バックエンドに認可要否を確認する
     */
    get: operations["checkAuthorization"];
  };
  "/api/v1/getsAuthorization/check": {
    /**
     * Get権限確認
     * @description Get権限有無を確認する
     */
    get: operations["checkGetsAuthorization"];
  };
  "/api/v1/sso/{service}": {
    /**
     * Web21SSO用SAML取得
     * @description Web21にSSOするためSAML情報をDTPバックエンド経由でvalueDoorから取得する
     */
    get: operations["getWeb21SsoSaml"];
  };
  "/api/v1/session": {
    /**
     * セッション情報削除
     * @description DynamoDBに保存されているセッション情報を削除する
     */
    delete: operations["deleteSession"];
  };
  "/api/v1/tos/consentStatus/check": {
    /**
     * 利用規定同意状況確認
     * @description 利用規定の同意状況を確認する
     */
    get: operations["checkTOSConsentStatus"];
  };
  "/api/v1/tos/consentStatus/register": {
    /**
     * 利用規定同意状況登録
     * @description 利用規定の同意状況を登録する
     */
    post: operations["registerTOSConsentStatus"];
  };
  "/api/v1/clientActionLog": {
    /**
     * 顧客操作履歴保存
     * @description 顧客の操作履歴を保存する
     */
    post: operations["saveClientActionLog"];
  };
  "/api/v1/freee/link": {
    /**
     * freee連携確認
     * @description freee連携の有無を取得
     */
    get: operations["checkFreeeLinkStatus"];
    /**
     * freee連携
     * @description DTPバックエンド経由でfreeeからアクセストークン・リフレッシュトークンを取得し、AuroraDBに保存する
     */
    post: operations["linkToFreee"];
  };
  "/api/v1/freee/sso": {
    /**
     * freeeSsoパラメータ取得API
     * @description freeeにSSOする為の情報をバックエンドから取得して返却
     */
    get: operations["getFreeeSsoParams"];
  };
  "/api/v1/freee/relink": {
    /**
     * freeeサインアップ画面URL取得
     * @description freeeサインアップ画面URLにバックエンドで払い出したfreee用のstateを結合させて返却する
     */
    get: operations["getUrlToFreeeReLinkPage"];
  };
  "/api/freee/auth": {
    /**
     * freee連携完了API
     * @description freee連携の認可画面のコールバックURLとして呼ばれるのでハンドリングを行う
     */
    get: operations["completeFreeeLink"];
  };
  "/api/v1/freee/links/check/partnerId": {
    /**
     * 取引先ID紐付け確認
     * @description 取引先ID(clientId)に紐づくデータがfreee_linksテーブルに存在するか確認。紐づくデータがある場合はtrue,ない場合はfalse
     */
    get: operations["checkFreeeLinksByClientId"];
  };
  "/api/v1/freee/walletables": {
    /**
     * freee口座一覧取得
     * @description freeeに登録された口座の情報を取得する。queryパラメーターで対象を設定可能
     */
    get: operations["getWalletables"];
  };
  "/api/v1/freee/transactions": {
    /**
     * freee口座明細取得
     * @description freeeの口座明細一覧を取得する。
     */
    get: operations["getFreeeTransactions"];
  };
  "/api/v1/freee/transactions/totalExpense": {
    /**
     * freee口座利用合計金額取得
     * @description 口座に使用した金額合計を取得。出金はプラス、入金はマイナス
     */
    get: operations["getUsedTotalAmount"];
  };
  "/api/v1/identityVerification/openAccountApplication/temporary": {
    /**
     * 口座開設申込情報一時保存確認
     * @description 口座開設申込の一時保存情報を確認する
     */
    get: operations["getOpenAccountApplicationTemporary"];
  };
  "/api/v1/identityVerification/openAccountApplication/screeningStatuses": {
    /**
     * 口座開設行内審査ステータス確認
     * @description お手続き番号のAurora保存有無および、口座開設申込の各種ステータスを確認する
     */
    get: operations["getOpenAccountApplicationScreeningStatuses"];
  };
  "/api/v1/identityVerification/address": {
    /**
     * 住所検索
     * @description 郵便番号をキーにバックエンドから住所を取得する
     */
    get: operations["searchAddress"];
  };
  "/api/v1/identityVerification": {
    /**
     * 本人確認情報保存
     * @description 本人確認情報を保存する
     */
    post: operations["saveIdentityVerification"];
  };
  "/api/v1/identityVerification/ekycUrl": {
    /**
     * eKYC URL生成要求
     * @description お手続き番号に紐づくUUIDをAuroraから取得する。UUIDが改ざんされていないことを確認し、eKYCのWebViewURLを取得する。
     */
    post: operations["requestEkycUrl"];
  };
  "/api/v1/identityVerification/uuid": {
    /**
     * 本人確認情報UUID取得
     * @description お手続き番号確認時にUUIDをAuroraから取得する。
     */
    get: operations["getUuid"];
  };
  "/api/v1/identityVerification/corporateWebStatus": {
    /**
     * 法人Web開局状況確認
     * @description 法人Webが開局しているかを確認する
     */
    get: operations["checkCorporateWebStatus"];
  };
  "/api/v1/work/blockage": {
    /**
     * 機能閉塞設定取得
     * @description 機能IDを引数として機能閉塞ステータスを取得する
     */
    get: operations["getWorkBlockageStatus"];
  };
  "/api/v1/web21Otp/decrypt": {
    /**
     * Web21OTP復号化API
     * @description 法人用アプリから受け取った暗号化OTPを復号化する
     */
    post: operations["decryptWeb21Otp"];
  };
  "/api/v1/encryptedVdid/check": {
    /**
     * VDID復号化API
     * @description 法人用アプリから受け取った暗号化VDIDを復号化し、セッション情報のものと一致しているかを確認する。
     */
    post: operations["checkEncryptedVdid"];
  };
  "/api/v1/encryptedVdid/encrypt": {
    /**
     * 暗号化VDID取得API
     * @description セッションに紐づくVDIDを暗号化してfrontに返却する
     */
    get: operations["getEncryptedVdid"];
  };
  "/api/v1/classified/salt": {
    /**
     * SALT値取得API
     * @description ROSAのシークレット上に保存してあるsecretManagerのSALT値を取得する
     */
    get: operations["getSaltValue"];
  };
  "/api/v1/classified/client": {
    /**
     * クライアント情報取得API
     * @description ROSAのシークレット上に保存してあるsecretManagerのSALT値を取得する
     */
    get: operations["getClientInfo"];
  };
  "/api/v1/freee/redirect": {
    /**
     * freeeリダイレクトURL取得API
     * @description パラメーターに応じてfreeeの遷移先URLを指定
     */
    get: operations["getFreeeRedirectUrl"];
  };
  "/api/v1/idaas/sso/cookie": {
    /**
     * freee/IDaaSSSO用暗号化Cokie取得
     * @description セッションの情報を基に暗号化Cookieを生成
     */
    get: operations["getEncryptedCookieForSso"];
  };
  "/api/v1/refusalFlag": {
    /**
     * 認可拒否履歴登録API
     * @description 認可拒否履歴を登録するAPI
     */
    post: operations["registerRefusalFlag"];
    /**
     * 認可拒否履歴削除API
     * @description 認可拒否履歴を削除するAPI
     */
    delete: operations["deleteRefusalFlag"];
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    Users: {
      /**
       * @description 口座数
       *
       * @example 1
       */
      count?: number;
      /**
       * @description ※口座一覧（リスト）のソート順は以下とする。
       *   口座識別子（昇順）
       * ※ただしSMBCの場合、以下とする。
       *   ①支店コード（昇順）
       *   ②預金種目コード（昇順）
       *   ③口座番号（昇順）
       *   ※最大3,000回繰り返し。
       */
      accountList?: components["schemas"]["AccountList"][];
      /**
       * @description "OFF" "ON"
       *  残高照会権限保有状態
       *  OFF：保有なし
       *  ON ：保有あり
       *  ※ONの場合、下記2つのAPIが実行可能。
       *  ・残高照会（全口座）
       *  ・残高照会（口座指定）
       */
      balanceAuthorityStatus?: string | null;
      /**
       * @description サーバー日付
       * @example 2023/9/1
       */
      serverDate?: string;
    };
    AccountList: {
      /**
       * Format: 数字
       * @description 口座識別子
       *
       * @example *************
       */
      accountId?: string | null;
      /**
       * Format: 数字
       * @description 口座
       * 口座情報（支店コード、預金種目コード、口座番号を連結した値）を設定。
       * ※支店コード：3桁固定、預金種目コード：2桁固定、口座番号：1～7桁の桁数可変
       *
       * @example ************
       */
      account?: string | null;
      /**
       * Format: 半角全角
       * @description 支店名（カナ）
       * カナ支店名を設定。
       * ※半角15文字以内＋店舗属性（全角4文字以内）。
       *
       * @example ﾄｳｷｮｳ支店
       */
      branchNameKana?: string | null;
      /**
       * Format: 全角
       * @description 支店名（漢字）
       * 漢字支店名を設定。
       * ※全角15文字以内＋店舗属性（全角4文字以内）。
       *
       * @example 東京支店
       */
      branchNameKanji?: string | null;
      /**
       * Format: 全角
       * @description 預金種目名
       *
       * @example 普通
       */
      accountType?: string | null;
      /**
       * Format: 数字
       * @description 口座番号
       *
       * @example 1100001
       */
      accountNumber?: string | null;
      /**
       * Format: 半角
       * @description 振込依頼人名
       * 委託者名を設定。
       *
       * @example ABCｼｮｳｼﾞ
       */
      remitterName?: string | null;
      /**
       * @description 金融機関名
       * @example 三井住友銀行
       */
      bankName?: string | null;
      /**
       * @description 口座表示名
       * @example 振込用口座1
       */
      displayAccountName?: string | null;
      /**
       * @description 口座残高カード表示フラグ(true:非表示, false:表示)
       * @example false
       */
      isHidden?: boolean;
      /**
       * @description 一意の番号。並び順に使用
       * @example 1
       */
      index?: number;
      /**
       * @description 口座種別
       * @example web21
       * @enum {string}
       */
      accountApiType?: "web21" | "freee";
    };
    UserInfo: {
      /**
       * @description 利用者ID(VDID)
       * @example ABCDE12345
       */
      vdId?: string;
      /**
       * @description DTPID
       * @example <EMAIL>
       */
      dtpId?: string;
      /**
       * @description ユーザーID
       * @example id12345
       */
      userId?: string;
      /**
       * @description 認証区分
       * @example 01
       */
      ninsyoKbn?: string;
      /**
       * @description 利用者名
       * @example 東京さむる太郎
       */
      userSeiMei?: string;
      /**
       * @description 利用者区分
       * @example 01
       */
      userTyp?: string;
      /**
       * @description 取引先名
       * @example 東京SAML企業
       */
      compName?: string;
      /**
       * @description 申込代表口座
       * (店番+科目+口座番号)
       *
       * @example ********901234
       */
      compId?: string;
      /**
       * @description メールアドレス
       * @example <EMAIL>
       */
      Email?: string;
      /**
       * @description web21契約種別
       *   1:エキスパート
       *   2:Web21-X
       *   3:デビュー
       *   4:ライト
       *
       * @example 2
       */
      keiyakuType?: string;
      /**
       * @description 利用者権限
       *   0:両方権限なし
       *   1:スマホ承認のみ
       *   2:承認権限のみ
       *   3:両方の権限あり
       *
       * @example 3
       */
      userAuths?: string;
    };
    Balance: {
      /**
       * @description 口座件数
       * @example 1
       */
      count?: number;
      accounts?: components["schemas"]["BalanceDetail"][];
      /**
       * @description サーバー日付
       * @example 2023/9/1
       */
      serverDate?: string;
    };
    BalanceDetail: {
      /**
       * @description 連絡先名
       * @example 振込用口座
       */
      contactName?: string | null;
      /**
       * @description 支店名
       * @example 東京支店
       */
      branchName?: string | null;
      /**
       * @description 支店コード
       * @example 123
       */
      branchCode?: string | null;
      /**
       * @description 預金種目名
       * @example 普通
       */
      accountType?: string | null;
      /**
       * @description 口座番号
       * @example 1234567
       */
      accountNumber?: string | null;
      /**
       * @description 口座識別子
       * @example *************
       */
      accountId?: string | null;
      /**
       * @description 基準日
       * @example 2023/8/1
       */
      baseDate?: string | null;
      /**
       * @description 基準時刻
       * @example 12:00
       */
      baseTime?: string | null;
      /**
       * @description 残高確定（照会）日時
       * @example 2023-08-01-12:00:00
       */
      baseDateTime?: string | null;
      /**
       * @description 現在残高
       * @example 1000000
       */
      currentBalance?: number | null;
      /**
       * @description 他店券残高
       * @example 1000000
       */
      checksIssuedByOtherBanks?: number | null;
      /**
       * @description 貸越極度額
       * @example 1000000
       */
      overdraftLimit?: number | null;
      /**
       * @description 支払可能残高
       * @example 1000000
       */
      withdrawableBalance?: number | null;
      /**
       * @description 金融機関名
       * @example 三井住友銀行
       */
      bankName?: string | null;
      /**
       * Format: 半角
       * @description 振込依頼人名
       * 委託者名を設定。
       *
       * @example ABCｼｮｳｼﾞ
       */
      remitterName?: string | null;
      /**
       * @description 口座表示名
       * @example 振込用口座1
       */
      displayAccountName?: string | null;
      /**
       * @description 口座表示フラグ(true:表示, false:非表示)
       * @example true
       */
      displayAccountFlag?: boolean | null;
    };
    Account: {
      accounts?: components["schemas"]["AccountDetail"][];
    };
    AccountDetail: {
      /**
       * @description 口座識別子
       * @example *************
       */
      accountId?: string;
      /**
       * @description 口座表示名
       * @example 振込用口座1
       */
      displayAccountName?: string;
      /**
       * @description 口座表示フラグ(true:表示, false:非表示)
       * @example true
       */
      displayAccountFlag?: boolean | null;
      /**
       * @description 一意の番号。並び順に使用
       * @example 1
       */
      index?: number;
    };
    Transactions: {
      /**
       * @description 照会結果
       *   maxItemNumberExceeded：最大明細件数超過（継続取得不可）
       *   withInquiryData：照会データあり
       *   inquiryComplete：照会完了
       *   noInquiryData：照会データなし
       *
       * @example inquiryComplete
       * @enum {string}
       */
      inquiryResult?: "maxItemNumberExceeded" | "withInquiryData" | "inquiryComplete" | "noInquiryData";
      /**
       * @description 種別コード
       *   03：入出金取引明細
       *
       * @example 03
       * @enum {string|null}
       */
      categoryCode?: "03" | null;
      /**
       * Format: date
       * @description 作成日（和暦）
       * データ作成日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      createdDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 作成日（西暦）
       * データ作成日をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      createdDateAd?: string | null;
      /**
       * Format: date
       * @description 基準日
       * BFF内で設定。
       *
       * @example 2017/1/1
       */
      baseDate?: string;
      /**
       * Format: date
       * @description 基準時刻
       * BFF内で設定。
       *
       * @example 8:00
       */
      baseTime?: string;
      /**
       * Format: date
       * @description 取引日（自）（和暦）
       * 取引日（自）をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      transactionDateFromJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 取引日（自）（西暦）
       * 取引日（自）（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      transactionDateFromAd?: string | null;
      /**
       * Format: date
       * @description 取引日（至）（和暦）
       * 取引日（至）をYY/MM/DD形式で設定。
       *
       * @example 29/01/31
       */
      transactionDateToJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 取引日（至）（西暦）
       * 取引日（至）（西暦）をYYYY/MM/DD形式で設定。
       *
       * @example 2017/01/31
       */
      transactionDateToAd?: string | null;
      /**
       * Format: 数字
       * @description 金融機関コード
       *
       * @example 1234
       */
      bankCode?: string | null;
      /**
       * Format: 半角
       * @description 金融機関名（カナ）
       *
       * @example 1234ｷﾞﾝｺｳ
       */
      bankNameKana?: string | null;
      /**
       * Format: 数字
       * @description 支店コード
       *
       * @example 123
       */
      branchCode?: string | null;
      /**
       * Format: 半角
       * @description 支店名（カナ）
       *
       * @example ﾄｳｷｮｳｼﾃﾝ
       */
      branchNameKana?: string | null;
      /**
       * Format: 数字
       * @description 預金種目コード
       *
       * @example 01
       */
      accountTypeCode?: string | null;
      /**
       * Format: 全角
       * @description 預金種目名
       *
       * @example 普通
       */
      accountType?: string | null;
      /**
       * Format: 数字
       * @description 口座番号
       *
       * @example **********
       */
      accountNumber?: string | null;
      /**
       * Format: 数字
       * @description 口座識別子
       *
       * @example *************
       */
      accountId?: string | null;
      /**
       * Format: 半角
       * @description 口座名
       *
       * @example 123ｼﾖｳｶｲ
       */
      accountName?: string | null;
      /**
       * @description 貸越区分（ヘッダー）
       * 貸越区分を設定。
       *   1：プラス
       *   2：マイナス
       *
       * @example 1
       * @enum {string|null}
       */
      overdraftTypeHeader?: "1" | "2" | null;
      /**
       * @description 通帳・証書区分
       *   1：通帳
       *   2：証書
       *
       * @example 1
       * @enum {string|null}
       */
      passbookOrCertificateType?: "1" | "2" | null;
      /**
       * @description 取引前残高
       *
       * @example ***********
       */
      balanceBeforeTransaction?: number | null;
      transactions1?: components["schemas"]["TransactionsDetail"][];
      transactions2?: components["schemas"]["TransactionsDetailSmbc"][];
      /**
       * @description 入金件数
       *
       * @example 7
       */
      depositCount?: number | null;
      /**
       * @description 入金額合計
       *
       * @example 300000
       */
      totalDepositAmount?: number | null;
      /**
       * @description 出金件数
       *
       * @example 3
       */
      withdrawalCount?: number | null;
      /**
       * @description 出金額合計
       *
       * @example 5000
       */
      totalWithdrawalAmount?: number | null;
      /**
       * @description 貸越区分（トレーラー）
       * 貸越区分を設定。
       *   1：プラス
       *   2：マイナス
       *
       * @example 1
       * @enum {string|null}
       */
      overdraftTypeTrailer?: "1" | "2" | null;
      /**
       * @description 取引後残高
       *
       * @example 100000
       */
      transactionBalance?: number | null;
      /**
       * @description 明細データ件数
       *
       * @example 6
       */
      transactionDataCount?: number | null;
      /**
       * @description 取引総件数
       *
       * @example 5
       */
      totalCount?: number | null;
      /**
       * @description 口座数
       *
       * @example 1
       */
      count?: number | null;
      /**
       * @description 継続情報
       * 金融機関と継続情報のありなしにより、true又はfalseを応答する。
       * 金融機関が「SMBC」かつ照会条件に該当する入出金明細が最大件数を超過したため、全て返却出来なかった場合に継続情報ありとなり、継続して情報の取得が可能となる。
       * true：継続情報あり
       * false：継続情報なし
       *
       * @example false
       * @enum {boolean}
       */
      hasNext?: true | false;
      /**
       * Format: 数字
       * @description 明細キー
       * 返却済み明細件数を設定。
       * 明細キーは「照会結果」とあわせて以下のように設定。
       *   例）55,000明細の入出金明細情報を取得する場合
       *      1電文目：照会結果＝照会データあり、明細キー＝20000
       *      2電文目：照会結果＝照会データあり、明細キー＝40000
       *      3電文目：照会結果＝照会完了、明細キー＝0
       * ※SMBCのみ設定。
       *
       * @example 20000
       */
      itemKey?: string | null;
      /**
       * Format: 英数字
       * @description セッション情報
       * セッション情報は「照会結果」がwithInquiryData:照会データあり時のみ設定。
       * ※SMBCのみ設定。
       *
       * @example BZDBIB112150951480831001
       */
      sessionInfo?: string | null;
      /**
       * @description 通貨コード
       * ”JPY”固定。
       *
       * @example JPY
       * @enum {string}
       */
      currencyCode?: "JPY";
    };
    TransactionsDetail: {
      /**
       * Format: 数字
       * @description 照会番号
       *
       * @example 03000001
       */
      inquiryNumber?: string | null;
      /**
       * Format: date
       * @description 取引日（和暦）
       * 取引日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      transactionDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 取引日（西暦）
       * 取引日（西暦）をYYYY/M/D形式で設定。
       *
       * @example 2023/1/1
       */
      transactionDateAd?: string | null;
      /**
       * Format: date
       * @description 起算日（和暦）
       * 起算日（和暦）をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      valueDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 起算日（西暦）
       * 起算日（西暦）をYYYY/M/D形式で設定。
       *
       * @example 2023/1/1
       */
      valueDateAd?: string | null;
      /**
       * @description 入払区分
       *   1：入金
       *   2：出金
       *
       * @example 1
       * @enum {string}
       */
      depositCreditType?: "1" | "2";
      /**
       * @description 取引区分
       *   10：現金
       *   11：振込
       *   12：他店券入金
       *   13：交換（取立入金および交換払）
       *   14：振替
       *   15：継続
       *   18：その他
       *   19：訂正
       *   31：でんさい
       *
       * @example 12
       * @enum {string|null}
       */
      transactionType?: "10" | "11" | "12" | "13" | "14" | "15" | "18" | "19" | "31" | null;
      /**
       * @description 取引区分名
       *
       * @example 他店券入金
       * @enum {string|null}
       */
      transactionTypeName?: "現金" | "振込" | "他店券入金" | "交換（取立入金および交換払）" | "振替" | "継続" | "その他" | "訂正" | "でんさい" | null;
      /**
       * @description 取引金額
       *
       * @example 60000
       */
      amount?: number | null;
      /**
       * @description うち他店券金額
       *
       * @example 10000
       */
      checksIssuedByOtherBanksAmount?: number | null;
      /**
       * Format: date
       * @description 交換呈示日（和暦）
       * 交換呈示日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      exchangePresentationDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 交換呈示日（西暦）
       * 交換呈示日（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      exchangePresentationDateAd?: string | null;
      /**
       * Format: date
       * @description 不渡返還日（和暦）
       * 不渡返還日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      dishonoredReturnDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 不渡返還日（西暦）
       * 不渡返還日（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      dishonoredReturnDateAd?: string | null;
      /**
       * @description 手形・小切手区分
       *   1：小切手
       *   2：約束手形
       *   3：為替手形
       *
       * @example 1
       * @enum {string|null}
       */
      billAndCheckType?: "1" | "2" | "3" | null;
      /**
       * @description 手形・小切手区分名
       *
       * @example 小切手
       * @enum {string|null}
       */
      billAndCheckTypeName?: "小切手" | "約束手形" | "為替手形" | null;
      /**
       * Format: 数字
       * @description 手形・小切手番号
       *
       * @example 1234567
       */
      billAndCheckNumber?: string | null;
      /**
       * Format: 数字
       * @description 支店コード
       *
       * @example 123
       */
      branchCode?: string | null;
      /**
       * Format: 半角
       * @description 振込依頼人コード
       *
       * @example abcdefgHIJ
       */
      remitterCode?: string | null;
      /**
       * Format: 半角
       * @description 振込依頼人名または契約者番号
       *
       * @example ABCｼｮｳｼﾞ
       */
      remitterNameContractorNumber?: string | null;
      /**
       * Format: 半角
       * @description 仕向金融機関名
       *
       * @example 1234ｷﾞﾝｺｳ
       */
      remittingBankName?: string | null;
      /**
       * Format: 半角
       * @description 仕向支店名
       *
       * @example ﾄｳｷｮｳｼﾃﾝ
       */
      remittingBankBranchName?: string | null;
      /**
       * Format: 半角
       * @description 摘要内容
       *
       * @example ﾌﾘｺﾐ
       */
      abstract?: string | null;
      /**
       * Format: 半角
       * @description EEDI情報
       *
       * @example 01209315034789264425
       */
      ediInfo?: string | null;
      /**
       * Format: 半角
       * @description EDIキー
       * 本項目が設定された場合にEDI情報照会にてXML対応形式のEDI情報を照会可能。
       *
       * @example ﾞﾞﾟ*****************
       */
      ediKey?: string | null;
    };
    TransactionsDetailSmbc: {
      /**
       * Format: 数字
       * @description 識別番号
       *
       * @example ********
       */
      identificationNumber?: string | null;
      /**
       * Format: date
       * @description 取引日（和暦）
       * 取引日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      transactionDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 取引日（西暦）
       * 取引日（西暦）をYYYY/M/D形式で設定。
       *
       * @example 2023/1/1
       */
      transactionDateAd?: string | null;
      /**
       * Format: date
       * @description 起算日（和暦）
       * 起算日（和暦）をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      valueDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 起算日（西暦）
       * 起算日（西暦）をYYYY/M/D形式で設定。
       *
       * @example 2023/1/1
       */
      valueDateAd?: string | null;
      /**
       * @description 入払区分
       *   1：入金
       *   2：出金
       *
       * @example 1
       * @enum {string}
       */
      depositCreditType?: "1" | "2";
      /**
       * @description 取引区分
       *   10：現金
       *   11：振込
       *   12：他店券入金
       *   13：交換（取立入金および交換払）
       *   14：振替
       *   15：継続
       *   18：その他
       *   19：訂正
       *   31：でんさい
       *
       * @example 10
       * @enum {string|null}
       */
      transactionType?: "10" | "11" | "12" | "13" | "14" | "15" | "18" | "19" | "31" | null;
      /**
       * @description 取引区分名
       *
       * @example 現金
       * @enum {string|null}
       */
      transactionTypeName?: "現金" | "振込" | "他店券入金" | "交換（取立入金および交換払）" | "振替" | "継続" | "その他" | "訂正" | "でんさい" | null;
      /**
       * @description 取引金額
       *
       * @example 60000
       */
      amount?: number | null;
      /**
       * @description うち他店券金額
       *
       * @example 15000
       */
      checksIssuedByOtherBanksAmount?: number | null;
      /**
       * Format: date
       * @description 交換呈示日（和暦）
       * 交換呈示日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      exchangePresentationDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 交換呈示日（西暦）
       * 交換呈示日（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      exchangePresentationDateAd?: string | null;
      /**
       * Format: date
       * @description 不渡返還日（和暦）
       * 不渡返還日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      dishonoredReturnDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 不渡返還日（西暦）
       * 不渡返還日（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      dishonoredReturnDateAd?: string | null;
      /**
       * @description 手形・小切手区分
       *   1：小切手
       *   2：約束手形
       *   3：為替手形
       *
       * @example 1
       * @enum {string|null}
       */
      billAndCheckType?: "1" | "2" | "3" | null;
      /**
       * @description 手形・小切手区分名
       *
       * @example 小切手
       * @enum {string|null}
       */
      billAndCheckTypeName?: "小切手" | "約束手形" | "為替手形" | null;
      /**
       * Format: 半角
       * @description 手形・小切手番号
       *
       * @example 1234567
       */
      billAndCheckNumber?: string | null;
      /**
       * Format: 数字
       * @description 支店コード
       *
       * @example 123
       */
      branchCode?: string | null;
      /**
       * Format: date
       * @description 当初預入日（和暦）
       * 当初預入日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      originalDepositDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 当初預入日（西暦）
       * 当初預入日（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2017-01-01
       */
      originalDepositDateAd?: string | null;
      /**
       * Format: 数字
       * @description 利率
       * ※上2桁整数、下4桁小数部。
       *
       * @example 020000
       */
      interestRate?: string | null;
      /**
       * Format: date
       * @description 満期日（和暦）
       * 満期日をYY-MM-DD形式で設定。
       *
       * @example 29-01-01
       */
      maturityDateJapaneseCalendar?: string | null;
      /**
       * Format: date
       * @description 満期日（西暦）
       * 満期日（西暦）をYYYY/M/D形式で設定。
       *
       * @example 2027/9/1
       */
      maturityDateAd?: string | null;
      /**
       * Format: 数字
       * @description 期間（１）
       * 期間（１）をYMMDDDD（Y：年表示　MM：月表示　DDDD：日表示）で設定。
       *
       * @example 9010131
       */
      period1?: string | null;
      /**
       * @description 期間利息
       *
       * @example 5000
       */
      periodInterest?: number | null;
      /**
       * Format: 数字
       * @description 中間払利率
       * ※上2桁整数、下4桁小数部。
       *
       * @example 015000
       */
      interimPaymentInterestRate?: string | null;
      /**
       * @description 中間払区分
       *   1：現金
       *   2：指定口座への振替
       *   3：定期預金の作成
       *
       * @example 1
       * @enum {string|null}
       */
      interimPaymentType?: "1" | "2" | "3" | null;
      /**
       * Format: 数字
       * @description 期後期間
       *
       * @example 0100
       */
      periodAfterTerm?: string | null;
      /**
       * Format: 数字
       * @description 期後利率
       * ※上2桁整数、下4桁小数部。
       *
       * @example 017000
       */
      interestRateAfterTerm?: string | null;
      /**
       * @description 期後利息
       *
       * @example 7000
       */
      interestAfterTerm?: number | null;
      /**
       * @description 合計利息
       *
       * @example 12000
       */
      totalInterest?: number | null;
      /**
       * @description 税区分
       *   1：総合課金
       *   2：分割課税
       *   3：マル優
       *   4：マル財
       *   5：非居住者
       *   6：特別マル財
       *   9：その他
       *
       * @example 1
       * @enum {string|null}
       */
      taxType?: "1" | "2" | "3" | "4" | "5" | "6" | "9" | null;
      /**
       * @description 税区分名
       *
       * @example 総合課金
       * @enum {string|null}
       */
      taxTypeName?: "総合課金" | "分割課税" | "マル優" | "マル財" | "非居住者" | "特別マル財" | "その他" | null;
      /**
       * Format: 数字
       * @description 税率
       * ※上2桁整数、下2桁小数部。
       *
       * @example 1000
       */
      taxRate?: string | null;
      /**
       * @description 税額
       *
       * @example 1200
       */
      taxAmount?: number | null;
      /**
       * @description 税引後利息
       *
       * @example 10800
       */
      aftertaxInterest?: number | null;
      /**
       * Format: 半角
       * @description 摘要内容
       *
       * @example ﾌﾘｺﾐ
       */
      abstract?: string | null;
      /**
       * Format: 数字
       * @description 期間（２）
       * 期間（２）をYYMMM（YY：年表示　MMM：月表示）を設定。
       *
       * @example 29112
       */
      period2?: string | null;
      /**
       * @description 期間利息正負表示
       *   1：正
       *   2：負
       *
       * @example 1
       * @enum {string|null}
       */
      periodInterestPositiveAndNegativeDisplay?: "1" | "2" | null;
    };
    TransactionsDailyTotals: {
      /** @description 入出金日次合計額のリスト。 */
      transactionsDailyTotals?: components["schemas"]["TransactionsDailyTotal"][];
    };
    TransactionsDailyTotal: {
      /**
       * Format: date
       * @description 取得日付（西暦）
       * 取得日付（西暦）をYYYY-MM-DD形式で設定。
       *
       * @example 2023-01-01
       */
      date?: string | null;
      /**
       * @description 入金額日次合計
       *
       * @example ********
       */
      totalDeposit?: number | null;
      /**
       * @description 出金額日次合計
       *
       * @example ********
       */
      totalWithdrawal?: number;
    };
    SamlResponse: {
      /**
       * @description SAMLレスポンス
       * @example PHNhbWwycDpSZXNwb25zZSB4bWxuczpz...(省略)
       */
      samlResponse: string;
      /**
       * @description カウリス用セッションID
       * @example 550e8400-e29b-41d4-a716-446655440000
       */
      caulisSessionId: string;
    };
    DtpIdLoginRequest: {
      /**
       * @description DTPID(メールアドレス形式)
       * @example <EMAIL>
       */
      dtpId: string;
      /**
       * @description パスワード
       * @example ********
       */
      password: string;
      /**
       * @description カウリス用セッションID
       * @example 550e8400-e29b-41d4-a716-446655440000
       */
      caulisSessionId: string;
    };
    DtpIdLoginResponse: {
      /** @example sessionId */
      sessionId?: string;
      /** @example 550e8400-e29b-41d4-a716-446655440000 */
      highRiskUserId?: string;
      encryptedCookie?: components["schemas"]["EncryptedCookie"];
    };
    LoginSuspiciousDetectionRequest: {
      /**
       * @description カウリス用セッションID
       * @example 550e8400-e29b-41d4-a716-446655440000
       */
      caulisSessionId: string;
      /**
       * @description 利用者ID(VDID)
       * @example ABCDE12345
       */
      vdId: string;
      /**
       * @description ポップアップメッセージID
       * @example C0007
       */
      errorMessageId: string;
    };
    /** @description ユーザーが同意した規定の種類 */
    TosConsentTypes: {
      /** @example ABCDE12345 */
      valueDoorID: string;
      /** @enum {string} */
      agreeTerm: "DTP";
      /**
       * @description 01: Web
       * 02: Mobile
       *
       * @enum {string}
       */
      channel: "01" | "02";
      /** @example 2023-12-01T10:00:00.000Z */
      createdAt: string;
      /** @example 2023-12-11T10:00:00.000Z */
      updatedAt?: string | null;
    };
    AuthorizationStatus: {
      /**
       * @description 企業コードの存在有無
       * @example true
       */
      hasKigyoCd: boolean;
      /**
       * @description エラーコード
       * @example I005-00006
       */
      errorCode?: string;
      /**
       * @description エラーメッセージ
       * @example サービス時間外です。
       */
      errorMessage?: string;
      /**
       * @description FAQを持っているか
       * @example false
       */
      hasFaq?: boolean;
      /**
       * @description 全てのWeb21権保有していないか
       * @example false
       */
      hasNoAuth: boolean;
      /**
       * @description 照会権限の有無
       * @example true
       */
      hasInquiryAuth: boolean;
      /**
       * @description 振込、振替権限の有無
       * @example true
       */
      hasTransferAuth: boolean;
      /**
       * @description 承認権限の有無
       * @example true
       */
      hasApprovalAuth: boolean;
      /**
       * @description 01:要認可, 02:認可済
       * @example true
       */
      authorizationStatus: string;
    };
    ClientActionRequest: {
      /**
       * @description 機能
       * @example 他行口座出金明細
       */
      clientFunction?: string;
      /**
       * @description 操作
       * @example 照会
       */
      operation?: string;
      /**
       * @description 結果
       * @example 正常
       */
      result?: string;
      /**
       * @description エラーID
       * @example E005-00001
       */
      errorId?: string;
    };
    FreeeWalletables: {
      walletables?: components["schemas"]["FreeeWalletable"][];
    };
    FreeeWalletable: {
      /**
       * @description 口座ID
       * @example 1
       */
      id: number;
      /**
       * @description 口座名 (255文字以内)
       * @example freee銀行
       */
      name: string;
      /**
       * @description サービスID
       * @example 3
       */
      bankId: number | null;
      /**
       * @description 口座区分 (銀行口座: bank_account, クレジットカード: credit_card, 現金: wallet)
       * @example bank_account
       * @enum {string}
       */
      type: "bank_account" | "credit_card" | "wallet";
      /**
       * @description 同期残高
       * @example 1565583
       */
      lastBalance?: number;
      /**
       * @description 登録残高
       * @example 1340261
       */
      walletableBalance?: number;
      /**
       * @description BFFで付与したサーバー日時
       * @example 2023/1/1 9:00
       */
      serverDateTime?: string;
      /**
       * @description 最終同期日時
       * @example 2023/1/1 9:00
       */
      lastSyncedAt?: string | null;
      /**
       * @description 金融機関名
       * @example 他行銀行
       */
      bankName?: string | null;
      /**
       * @description 口座残高カード表示フラグ(true:非表示, false:表示)
       * @example false
       */
      isHidden?: boolean;
      /**
       * @description 一意の番号。並び順に使用
       * @example 1
       */
      displayOrder?: number;
      /**
       * @description 口座種別
       * @example web21
       * @enum {string}
       */
      accountApiType?: "web21" | "freee";
      /**
       * @description freee同期状態 (同期成功: 0, その他: 1)
       * @example 0
       * @enum {string}
       */
      syncStatus?: "0" | "1";
    };
    FreeeTransactions: {
      transactions: components["schemas"]["FreeeTransaction"][];
      /**
       * Format: date
       * @description 基準日
       * BFF内で設定。
       *
       * @example 2017/1/1
       */
      baseDate?: string;
      /**
       * Format: date
       * @description 基準時刻
       * BFF内で設定。
       *
       * @example 8:00
       */
      baseTime?: string;
    };
    FreeeTransaction: {
      /**
       * @description 明細ID
       * @example 1
       */
      id: number;
      /**
       * @description 取引日(yyyy/m/d)
       * @example 2024/4/18
       */
      date: string;
      /**
       * @description 取引金額
       * @example 5000
       */
      amount: number;
      /**
       * @description 入金／出金 (入金: income, 出金: expense)
       * @example income
       * @enum {string}
       */
      entrySide: "income" | "expense";
      /**
       * @description 取引内容
       * @example 振込　カ) ABC
       */
      description: string;
    };
    FreeeTransactionsTotalResponse: {
      /**
       * @description 取引絞り込みで使用した開始日（yyyy-mm-dd）
       * @example 2023-08-01
       */
      startDate: string;
      /**
       * @description 取引絞り込みで使用した開始日（yyyy-mm-dd）
       * @example 2023-08-31
       */
      endDate: string;
      /**
       * @description 使用した金額合計（出金の合計 - 入金の合計）。マイナス値も可能
       * @example 76543
       */
      totalExpense: number;
      /**
       * @description サーバー日時
       * @example 2024/1/1 9:00
       */
      serverDateTime?: string;
    };
    AccountSettings: {
      accounts: components["schemas"]["AccountSetting"][];
    };
    AccountSetting: {
      /**
       * @description 口座識別子
       * @example **********
       */
      accountId: string;
      /**
       * @description 口座残高カード表示フラグ(true:非表示, false:表示)
       * @example false
       */
      isHidden: boolean;
      /**
       * @description 口座種別
       * @example web21
       * @enum {string}
       */
      accountApiType: "web21" | "freee";
      /**
       * @description 口座表示名
       * @example 請求
       */
      displayName?: string;
    };
    IdentityVerification: {
      /**
       * @description お手続き番号
       * @example 5000001
       */
      referenceNumber: string;
      /**
       * @description REPRESENTATIVE(代表者)、AGENT(取引責任者(代理人))、BENEFICIARY(実質的支配者)
       * @example REPRESENTATIVE
       */
      role: string;
      /**
       * @description 本人確認の方法（JPKIまたはeKYC）
       * @example JPKI
       */
      dataSource: string;
      /**
       * @description 氏名-姓
       * @example 若草
       */
      familyName?: string;
      /**
       * @description 氏名-名
       * @example 太郎
       */
      givenName?: string;
      /**
       * @description 氏名-セイ
       * @example ワカクサ
       */
      familyNameKana?: string;
      /**
       * @description 氏名-メイ
       * @example タロウ
       */
      givenNameKana?: string;
      /**
       * @description 氏名-Last Name
       * @example WAKAKUSA
       */
      familyNameAlphabetic?: string;
      /**
       * @description 氏名-First Name
       * @example TARO
       */
      givenNameAlphabetic?: string;
      /**
       * @description ヘボン式編集の有無
       * @example true
       */
      hepburnStyle?: string;
      /**
       * @description 役職名
       * @example 部長
       */
      position?: string;
      /**
       * @description 生年月日-年
       * @example 1999
       */
      birthYear?: number;
      /**
       * @description 生年月日-月
       * @example 1
       */
      birthMonth?: number;
      /**
       * @description 生年月日-日
       * @example 1
       */
      birthDay?: number;
      /**
       * @description 郵便番号
       * @example 1350061
       */
      postCode?: string;
      /**
       * @description 住所-都道府県
       * @example 東京都
       */
      prefecture?: string;
      /**
       * @description 住所-市区町村
       * @example 港区六本木
       */
      city?: string;
      /**
       * @description 住所-丁目・番地・号・建物名
       * @example 1-2-3 SMBCビル12階
       */
      sectionNumberAndBuildingName?: string;
    };
    searchAddressResponse: {
      /**
       * @description 郵便番号
       * @example 1350061
       */
      postcode: string;
      /**
       * @description 都道府県
       * @example 東京都
       */
      prefecture?: string;
      /**
       * @description 市区町村名
       * @example 江東区
       */
      city?: string;
      /**
       * @description 町域名
       * @example 豊洲
       */
      street?: string;
    };
    WorkBlockageStatus: {
      /**
       * @description 閉塞状態ステータス（0:利用可能、1:利用不可）
       * @example 0
       */
      status: string;
    };
    OpenAccountApplicationionionTemporaryResponse: {
      /**
       * @description 本人確認実施者
       * @example REPRESENTATIVE
       */
      userType: string;
      /**
       * @description 本人確認実施済み判定フラグ
       * @example true
       */
      isIdentityVerified: boolean;
      /**
       * @description STEP数
       * @example STEP1
       */
      validationStatus: string;
    };
    OpenAccountApplicationScreeningStatusesResponse: {
      /**
       * @description 口座開設申込済みかどうか
       * @example true
       */
      isAccountOpeningRequestSubmitted: boolean;
      /**
       * @description 代表者：本人確認実施不備フラグ
       * @example true
       */
      isRepresentativeHasFault?: boolean;
      /**
       * @description 取引責任者（代理人）：本人確認実施不備フラグ
       * @example false
       */
      isAgentHasFault?: boolean;
      /**
       * @description 実質的支配者①：本人確認実施不備フラグ
       * @example true
       */
      isBeneficiary1HasFault: boolean;
      /**
       * @description 実質的支配者②：本人確認実施不備フラグ
       * @example true
       */
      isBeneficiary2HasFault: boolean;
      /**
       * @description 実質的支配者③：本人確認実施不備フラグ
       * @example false
       */
      isBeneficiary3HasFault: boolean;
      /**
       * @description 実質的支配者④：本人確認実施不備フラグ
       * @example false
       */
      isBeneficiary4HasFault: boolean;
    };
    EkycUrlRequest: {
      /**
       * @description お手続き番号
       * @example 0000005000001
       */
      referenceNumber: string;
      /**
       * @description UUID
       * @example 6ff7f45f-7caf-4d38-8410-c5b49a21c3d8
       */
      uuid: string;
      /**
       * @description 手続き者属性フラグ
       *   01:代表者
       *   02:取引責任者（代理人）
       *   03:実質的支配者①
       *   04:実質的支配者②
       *   05:実質的支配者③
       *   06:実質的支配者④
       *
       * @example 01
       * @enum {string}
       */
      userType: "01" | "02" | "03" | "04" | "05" | "06";
    };
    FreeeLinkRequest: {
      /**
       * @description 認可コード(freeeからのリダイレクトパラメータより取得)
       * @example cb46420e53c24580a4c4e0fe8f888888
       */
      code: string;
      /**
       * @description state(freeeからのリダイレクトパラメータより取得)
       * @example 02a9NcXyG8RwPk3LMsUj7QvH3Kb9Ys4WxTz2BdFq6ZlMn0R5
       */
      stateFromFreee: string;
    };
    IssueAndLinkDtpIdRequest: {
      encryptedCookie: components["schemas"]["EncryptedCookie"];
    };
    IssueAndLinkDtpIdResponse: {
      /**
       * @description DTPID
       * @example <EMAIL>
       */
      dtpId: string;
      /**
       * @description ユーザーID
       * @example cb46420e53c24580a4c4e0fe8f888888
       */
      userId: string;
      /**
       * @description VDID
       * @example 0**********
       */
      valueDoorId: string;
      encryptedCookie: components["schemas"]["EncryptedCookie"];
    };
    EncryptedCookie: {
      /**
       * @description DTPの認証済みユーザーIDを含む暗号化したデータ
       * @example -RcgELV8vxU2qQG4BIyMjHPvPsvUd02XOL9Rq_fmFlEger80NQDx2t53FrTAH2pt
       */
      ticket?: string;
      /**
       * @description Cookieを利用できるサブドメイン
       * @example dev.biztest.smbc.co.jp
       */
      domain?: string;
      /**
       * @description Cookieを利用できるパス
       * @example /
       */
      path?: string;
      /**
       * @description https通信の場合のみ送信するフラグ
       * @example true
       */
      secure?: string;
      /** @description javascriptなどのスクリプトからCookieへのアクセスを禁止するフラグ */
      httponly?: string;
    };
    /** @description web21の権限情報 */
    Web21Authorizations: {
      /** @description 01 取引口座照会 */
      accountInquiryAuth: boolean;
      /** @description 02 総合振込 */
      generalTransferAuth: boolean;
      /** @description 03 給与/賞与振込 */
      salaryTransferAuth: boolean;
      /** @description 04 個人地方税納付 */
      localTaxPaymentAuth: boolean;
      /** @description 05 承認 */
      approvalAuth: boolean;
      /** @description 06 振込振替 */
      transferAuth: boolean;
      /** @description 07 外部ファイル送信 */
      fileSendingAuth: boolean;
      /** @description 08 税金・各種料金の払込 */
      taxesOrFeesPaymentAuth: boolean;
      /** @description 09 口座振替 */
      accountTransferAuth: boolean;
      /** @description 10 振込先口座確認 */
      transferDestinationCheckAuth: boolean;
      /** @description 11 データ変換 */
      dataConversionAuth: boolean;
      /** @description 12 スマホ取引口座照会 */
      accountInquiryOnSmartPhoneAuth: boolean;
      /** @description 13 スマホ振込 */
      transferOnSmartPhoneAuth: boolean;
      /** @description 14 スマホ承認 */
      approvalOnSmartPhoneAuth: boolean;
      /** @description 15 スマホ口座情報事前設定 */
      preSettingAccountInfoOnSmartPhoneAuth: boolean;
      /** @description 16 スマホセキュリティ設定 */
      settingSecurityOnSmartPhoneAuth: boolean;
      /** @description 19 口座情報事前設定 */
      preSettingAccountInfoAuth: boolean;
      /** @description 20 セキュリティ設定 */
      settingSecurityAuth: boolean;
    } | null;
    /** @description web21の権限情報 */
    GetsAuthorizations: {
      /** @description 01 被仕向送金 */
      inboundTransferAuth: boolean;
      /** @description 02 輸出LC到着案内 */
      lcExportAuth: boolean;
      /** @description 03 輸出手形買取・取立依頼 */
      exportBillHandlingAuth: boolean;
      /** @description 04 仕向送金 */
      outboundTransferAuth: boolean;
      /** @description 05 輸入LC */
      lcImportAuth: boolean;
      /** @description 06 外為取引情報 */
      forexInfoExchangeAuth: boolean;
      /** @description 15 法人外貨直送 */
      corpoForexDeliveryAuth: boolean;
      /** @description 18 親子契約 */
      parentChildContractAuth: boolean;
      /** @description 19 セキュリティー管理（本人のユーザ権限設定） */
      selfSecurityAccessAuth: boolean;
      /** @description 20 セキュリティー管理 */
      generalSecurityAccessAuth: boolean;
    } | null;
    DecryptWeb21Otp: {
      /**
       * @description 暗号化されたOTP
       * @example xxxxxxxxxx
       */
      encryptedOtp: string;
    };
    CheckEncryptedVdid: {
      /**
       * @description 暗号化されたVDID
       * @example xxxxxxxxxx
       */
      encryptedVdid: string;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: {
    /** @description BFF内のサーバー日付をYYYY/M/Dで表示 */
    "X-Base-Date": string;
    /** @description BFF内のサーバー時間をH:mmで表示 */
    "X-Base-Time": string;
  };
  pathItems: never;
}

export type external = Record<string, never>;

export interface operations {

  /**
   * 利用者情報取得
   * @description DTPバックエンドから利用者情報を取得する
   */
  getUsers: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["Users"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * ユーザー情報取得
   * @description DynamoDBからセッションIDに紐づくユーザー情報を取得する
   */
  getUserInfo: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["UserInfo"];
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description セッションIDに紐づくユーザー情報がありませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example セッションIDに紐づくユーザー情報がありませんでした。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 指定口座残高取得
   * @description DTPバックエンドから口座識別子に紐づく法人口座の残高を取得する
   */
  getBalance: {
    parameters: {
      query: {
        /** @description 取得日付 */
        date: string;
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
      path: {
        /** @description 口座識別子 */
        accountIds: string[];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["Balance"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 該当する情報が見つかりませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example not_found */
            errorCode?: string;
            /** @example 該当する情報が見つかりませんでした。 */
            errorMessage?: string;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 口座表示設定取得
   * @description DTPバックエンドから口座表示設定を取得する
   */
  getAccountsDisplay: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["AccountSettings"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 口座表示設定更新
   * @description DTPバックエンドへ口座の表示設定情報を連携する
   */
  updateAccountsDisplay: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    /** @description 各口座の表示名、および表示/非表示設定情報をバックエンドへ連携 */
    requestBody: {
      content: {
        "application/json:": components["schemas"]["AccountSettings"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example 口座表示設定を更新しました。 */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 入出金明細取得
   * @description DTPバックエンドから口座識別子に紐づく入出金明細情報を取得する
   */
  getTransactions: {
    parameters: {
      query: {
        /**
         * @description 対象期間（from）
         * 対象期間（from）をYYYY-MM-DD形式で設定。
         * ※金融機関ごとに照会可能期間が異なるため、詳細確認には仕様開示契約が必要（SMBCは対象外）。
         * ※SMBCかつC00Aの場合は必須、SMBCかつA00Aの場合は設定不可。
         */
        dateFrom: string;
        /**
         * @description 対象期間（to）
         * 対象期間（to）をYYYY-MM-DD形式で設定。
         * ※SMBCかつC00Aの場合は必須、SMBCかつA00Aの場合は設定不可。
         */
        dateTo: string;
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
      path: {
        /** @description 口座識別子 */
        accountId: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["Transactions"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 該当する情報が見つかりませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example not_found */
            errorCode?: string;
            /** @example 該当する情報が見つかりませんでした。 */
            errorMessage?: string;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 入出金日次合計額取得
   * @description DTPバックエンドから口座識別子に紐づく入出金の日次合計額を取得する
   */
  getTransactionsDailyTotals: {
    parameters: {
      query: {
        /**
         * @description 取得日付（from）
         * 取得日付（from）をYYYY-MM-DD形式で設定。
         */
        from: string;
        /**
         * @description 取得日付（to）
         * 取得日付（to）をYYYY-MM-DD形式で設定。
         */
        to?: string;
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
      path: {
        /** @description 口座識別子 */
        accountId: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["TransactionsDailyTotals"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 該当する情報が見つかりませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example not_found */
            errorCode?: string;
            /** @example 該当する情報が見つかりませんでした。 */
            errorMessage?: string;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * SAML検証
   * @description ValueDoorログイン時に発行されるSAMLレスポンスをバックエンドに連携する
   */
  verifySamlResponse: {
    requestBody?: {
      content: {
        "application/json:": components["schemas"]["SamlResponse"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example 550e8400-e29b-41d4-a716-446655440000 */
            sessionId?: string;
            /** @example 550e8400-e29b-41d4-a716-446655440000 */
            highRiskUserId?: string;
            encryptedCookie?: components["schemas"]["EncryptedCookie"];
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * DTPID認証API
   * @description DTPID/PWを元にバックエンドから権限情報を取得し、セッションID・暗号化クッキーを返却する
   */
  loginDtpId: {
    requestBody?: {
      content: {
        "application/json:": components["schemas"]["DtpIdLoginRequest"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["DtpIdLoginResponse"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 該当する情報が見つかりませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example I005-00008 */
            errorCode?: string;
            /** @example 口座開設中のため、ログインできません。口座開設後にログインしてください。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 怪しさ判定
   * @description ValueDoorログイン失敗時にフロント側から受け取るVDIDとポップアップエラーメッセージ（ErrMesID）を元にカウリスの怪しさ判定を行う
   */
  loginSuspiciousDetection: {
    requestBody?: {
      content: {
        "application/json:": components["schemas"]["LoginSuspiciousDetectionRequest"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example ValueDoorログイン失敗時の怪しさ判定リクエストを実行しました。 */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * DTPID更新API
   * @description DTPバックエンド経由でIDaaSのVDID紐付け情報を取得し、セッション情報を更新する
   */
  updateDtpId: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description DTPIDを更新しました
             * @example DTPIDを更新しました
             */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * DTPID紐づけ情報登録API
   * @description DTPバックエンド経由でIDaaSにてDTPIDの発行および、VDIDとの紐付け情報を登録する
   */
  issueAndLinkDtpId: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    requestBody?: {
      content: {
        "application/json:": components["schemas"]["IssueAndLinkDtpIdRequest"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            encryptedCookie?: components["schemas"]["IssueAndLinkDtpIdResponse"];
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * DTPID紐づけ解除API
   * @description VDIDに紐づくDTPIDの連携を解除する
   */
  deleteDtpIdLink: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description OK
             * @example 紐づけ解除に成功しました。
             */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * セッションID取得API
   * @description 高リスクユーザーIDをもとにセッションの詰め替えを行い、セッションIDを取得する
   */
  getSessionId: {
    parameters: {
      query: {
        /** @description DTPログイン認証でBFFからフロントに返却した暗号化Cookieのticket */
        ticket: string;
        /** @description フロントのIDaaSOTP認証画面（WebView）にて取得した暗号化Cookieのticket */
        idaasTicket: string;
      };
      header: {
        /** @description Bearerトークン（高リスクユーザーID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description セッションID
             * @example 9474656f-faf5-4aa2-a326-ec739d044e65
             */
            sessionId?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example I005-00009 */
            errorCode?: string;
            /** @example お客さまのアカウントは現在ログインできません。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 高リスクユーザーの有効期限が切れています。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00011 */
            errorCode?: string;
            /** @example お客さまのアカウントは現在ログインできません。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 高リスクユーザーIDに紐づく高リスクユーザー情報がありませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example I005-00010 */
            errorCode?: string;
            /** @example お客さまのアカウントは現在ログインできません。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 認可画面呼出情報生成
   * @description セッションIDをもとにバックエンドからリクエストパラメータを生成する
   */
  createAuthScreenInfo: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * Format: byte
             * @example 44GU5Yip55So44GC44KK44GM44Go44GG44GU44GW44GE44G+44GZ44CC
             */
            fromEbizParam?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * トークン要求
   * @description バックエンドからAnserBizSOLのアクセストークンを取得する
   */
  getAccessToken: {
    parameters: {
      query: {
        /** @description 認可コード */
        code: string;
        /** @description state */
        state: string;
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example アクセストークンを取得しました。 */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 認可要否確認
   * @description バックエンドに認可要否を確認する
   */
  checkAuthorization: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["AuthorizationStatus"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。他バックエンドからのエラーも全て401になる */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * Get権限確認
   * @description Get権限有無を確認する
   */
  checkGetsAuthorization: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description 0 or 1（0:権限なし1:権限あり）
             * @example 1
             */
            getsAuthStatus?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * Web21SSO用SAML取得
   * @description Web21にSSOするためSAML情報をDTPバックエンド経由でvalueDoorから取得する
   */
  getWeb21SsoSaml: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
      path: {
        /** @description 遷移先 */
        service: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @description Web21SSO用SAML */
            toWeb21Param?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W001-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * セッション情報削除
   * @description DynamoDBに保存されているセッション情報を削除する
   */
  deleteSession: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example セッション情報を削除しました。 */
            message?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 利用規定同意状況確認
   * @description 利用規定の同意状況を確認する
   */
  checkTOSConsentStatus: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["TosConsentTypes"][];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 利用規定同意状況登録
   * @description 利用規定の同意状況を登録する
   */
  registerTOSConsentStatus: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    /** @description ユーザーが何に同意したか（現在はデジタルタッチポイントの同意規約のみを想定）、どのチャネル(01:Web, 02:Mobile)で同意したかを管理 */
    requestBody: {
      content: {
        "application/json:": {
          /** @enum {string} */
          agreeTerm: "DTP";
          /** @enum {string} */
          channel: "01" | "02";
        };
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example 利用規定同意状況を登録しました。 */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 顧客操作履歴保存
   * @description 顧客の操作履歴を保存する
   */
  saveClientActionLog: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    requestBody?: {
      content: {
        "application/json:": components["schemas"]["ClientActionRequest"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description OK or NG
             * @example OK
             */
            message?: string;
          };
        };
      };
    };
  };
  /**
   * freee連携確認
   * @description freee連携の有無を取得
   */
  checkFreeeLinkStatus: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @description freee連携有無フラグ */
            isLinked?: boolean;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freee連携
   * @description DTPバックエンド経由でfreeeからアクセストークン・リフレッシュトークンを取得し、AuroraDBに保存する
   */
  linkToFreee: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    requestBody?: {
      content: {
        "application/json:": components["schemas"]["FreeeLinkRequest"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example freeeアクセストークンを保存しました。 */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freeeSsoパラメータ取得API
   * @description freeeにSSOする為の情報をバックエンドから取得して返却
   */
  getFreeeSsoParams: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @description freeeとのSSOに使用される情報 */
            state: string;
            /** @description freeeの連携先情報 */
            partnerId: string;
            /** @description freee連携済みの企業であるか */
            exists: boolean;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W001-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freeeサインアップ画面URL取得
   * @description freeeサインアップ画面URLにバックエンドで払い出したfreee用のstateを結合させて返却する
   */
  getUrlToFreeeReLinkPage: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description freeeサインアップ画面URL+freee用のstate
             * @example https://freee.example.com?state=02XXXXXX
             */
            redirectUrl?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W001-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freee連携完了API
   * @description freee連携の認可画面のコールバックURLとして呼ばれるのでハンドリングを行う
   */
  completeFreeeLink: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description コールバック処理OKを返却
             * @example OK
             */
            message?: string;
          };
        };
      };
    };
  };
  /**
   * 取引先ID紐付け確認
   * @description 取引先ID(clientId)に紐づくデータがfreee_linksテーブルに存在するか確認。紐づくデータがある場合はtrue,ない場合はfalse
   */
  checkFreeeLinksByClientId: {
    parameters: {
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @description 取引先ID(clientID)に紐づくデータ有無フラグ */
            exists?: boolean;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freee口座一覧取得
   * @description freeeに登録された口座の情報を取得する。queryパラメーターで対象を設定可能
   */
  getWalletables: {
    parameters: {
      query: {
        /** @description 口座種別（bank_account:銀行口座、credit_card:クレジットカード、walletその他の決済口座） */
        type: "bank_account" | "credit_card" | "wallet";
        /** @description 残高情報を含める */
        with_balance?: boolean;
        /** @description freee連携状態確認ステータスの取得有無 */
        with_sync_status?: boolean;
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["FreeeWalletables"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freee口座明細取得
   * @description freeeの口座明細一覧を取得する。
   */
  getFreeeTransactions: {
    parameters: {
      query: {
        /** @description 口座Id */
        walletable_id: number;
        /** @description 口座種別（銀行、クレジットカード、その他） */
        walletable_type: "bank_account" | "credit_card" | "wallet";
        /**
         * @description 取引日（西暦）開始日
         * 取引日（西暦）をYYYY-MM-DD形式で設定。
         */
        start_date: string;
        /**
         * @description 取引日（西暦）終了日
         * 取引日（西暦）をYYYY-MM-DD形式で設定。
         */
        end_date: string;
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["FreeeTransactions"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * freee口座利用合計金額取得
   * @description 口座に使用した金額合計を取得。出金はプラス、入金はマイナス
   */
  getUsedTotalAmount: {
    parameters: {
      query: {
        /** @description 口座Id */
        walletable_id: number;
        /** @description 口座種別（銀行、クレジットカード、その他） */
        walletable_type: "bank_account" | "credit_card" | "wallet";
      };
      header: {
        /** @description Bearerトークン（セッションID） */
        Authorization: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["FreeeTransactionsTotalResponse"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 口座開設申込情報一時保存確認
   * @description 口座開設申込の一時保存情報を確認する
   */
  getOpenAccountApplicationTemporary: {
    parameters: {
      query: {
        /** @description お手続き番号 */
        referenceNumber: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["OpenAccountApplicationionionTemporaryResponse"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 口座開設行内審査ステータス確認
   * @description お手続き番号のAurora保存有無および、口座開設申込の各種ステータスを確認する
   */
  getOpenAccountApplicationScreeningStatuses: {
    parameters: {
      query: {
        /** @description お手続き番号 */
        referenceNumber: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["OpenAccountApplicationScreeningStatusesResponse"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 住所検索
   * @description 郵便番号をキーにバックエンドから住所を取得する
   */
  searchAddress: {
    parameters: {
      query: {
        /** @description 郵便番号 */
        postcode: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["searchAddressResponse"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 該当する住所が見つかりません。 */
      404: {
        content: {
          "application/json": {
            /** @example I005-00007 */
            errorCode?: string;
            /** @example 該当する住所が見つかりませんでした。お手数ですが、正しい郵便番号をご確認の上、再度ご入力ください。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 本人確認情報保存
   * @description 本人確認情報を保存する
   */
  saveIdentityVerification: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["IdentityVerification"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example 本人確認情報を保存しました。 */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * eKYC URL生成要求
   * @description お手続き番号に紐づくUUIDをAuroraから取得する。UUIDが改ざんされていないことを確認し、eKYCのWebViewURLを取得する。
   */
  requestEkycUrl: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["EkycUrlRequest"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example https://www.ekyc */
            ekycUrl?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description お手続き番号またはUUIDが見つかりませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example お手続き番号またはUUIDが不正です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 本人確認情報UUID取得
   * @description お手続き番号確認時にUUIDをAuroraから取得する。
   */
  getUuid: {
    parameters: {
      query: {
        /** @description お手続き番号 */
        referenceNumber: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description UUID
             * @example 6ff7f45f-7caf-4d38-8410-c5b49a21c3d8
             */
            uuid?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description お手続き番号が見つかりませんでした。 */
      404: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example お手続き番号が不正です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 法人Web開局状況確認
   * @description 法人Webが開局しているかを確認する
   */
  checkCorporateWebStatus: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @description 法人web開局フラグ */
            isOpened?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 機能閉塞設定取得
   * @description 機能IDを引数として機能閉塞ステータスを取得する
   */
  getWorkBlockageStatus: {
    parameters: {
      query: {
        /** @description 機能ID */
        functionId: string;
      };
      header?: {
        /** @description Bearerトークン（セッションID） */
        Authorization?: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": components["schemas"]["WorkBlockageStatus"];
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * Web21OTP復号化API
   * @description 法人用アプリから受け取った暗号化OTPを復号化する
   */
  decryptWeb21Otp: {
    parameters: {
      header?: {
        /** @description Bearerトークン（セッションID） */
        Authorization?: string;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["DecryptWeb21Otp"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description 復号されたOTP
             * @example ********
             */
            otp?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * VDID復号化API
   * @description 法人用アプリから受け取った暗号化VDIDを復号化し、セッション情報のものと一致しているかを確認する。
   */
  checkEncryptedVdid: {
    parameters: {
      header?: {
        /** @description Bearerトークン（セッションID） */
        Authorization?: string;
      };
    };
    requestBody: {
      content: {
        "application/json": components["schemas"]["CheckEncryptedVdid"];
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description 認証成功メッセージ
             * @example VDIDの認証に成功しました
             */
            message?: string;
          };
        };
      };
      /** @description リクエストが不正です。 */
      400: {
        content: {
          "application/json": {
            /** @example W005-00002 */
            errorCode?: string;
            /** @example リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 暗号化VDID取得API
   * @description セッションに紐づくVDIDを暗号化してfrontに返却する
   */
  getEncryptedVdid: {
    parameters: {
      header?: {
        /** @description Bearerトークン（セッションID） */
        Authorization?: string;
      };
    };
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description 暗号化されたVDID
             * @example encryptedVdId
             */
            encryptedVdid?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * SALT値取得API
   * @description ROSAのシークレット上に保存してあるsecretManagerのSALT値を取得する
   */
  getSaltValue: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description secretManagerに格納されたSALT値
             * @example mobileEncryptSalt
             */
            mobileEncryptSalt?: string;
          };
        };
      };
    };
  };
  /**
   * クライアント情報取得API
   * @description ROSAのシークレット上に保存してあるsecretManagerのSALT値を取得する
   */
  getClientInfo: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /**
             * @description secretManagerに格納されたクライアントID
             * @example clientId
             */
            clientId?: string;
            /**
             * @description secretManagerに格納されたクライアントシークレット
             * @example clientSecret
             */
            clientSecret?: string;
          };
        };
      };
    };
  };
  /**
   * freeeリダイレクトURL取得API
   * @description パラメーターに応じてfreeeの遷移先URLを指定
   */
  getFreeeRedirectUrl: {
    responses: {
      /** @description リダイレクト実施 */
      200: never;
    };
  };
  /**
   * freee/IDaaSSSO用暗号化Cokie取得
   * @description セッションの情報を基に暗号化Cookieを生成
   */
  getEncryptedCookieForSso: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            encryptedCookie?: components["schemas"]["EncryptedCookie"];
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 認可拒否履歴登録API
   * @description 認可拒否履歴を登録するAPI
   */
  registerRefusalFlag: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example 認可拒否履歴を登録しました。 */
            message?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
  /**
   * 認可拒否履歴削除API
   * @description 認可拒否履歴を削除するAPI
   */
  deleteRefusalFlag: {
    responses: {
      /** @description successful operation */
      200: {
        content: {
          "application/json": {
            /** @example 認可拒否履歴を削除しました。 */
            message?: string;
          };
        };
      };
      /** @description 認証エラーが発生しました。 */
      401: {
        content: {
          "application/json": {
            /** @example I005-00001 */
            errorCode?: string;
            /** @example 認証エラー */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
      /** @description サーバー内部エラーが発生しました。 */
      500: {
        content: {
          "application/json": {
            /** @example E005-00009 */
            errorCode?: string;
            /** @example 現在データが取得できません。暫く経ってから再度取得お願いいたします。 */
            errorMessage?: string;
            /** @example false */
            hasFaq?: boolean;
          };
        };
      };
    };
  };
}
