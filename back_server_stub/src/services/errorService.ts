/* eslint-disable no-useless-catch */
import { ScanCommand } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { errorMasterTableName } from '../config/config';
import dynamoDBClient from '../utils/dynamoDB';

/**
 * エラーマスタを取得する
 */
export async function getErrorMaster() {
  const command = new ScanCommand({
    ProjectionExpression: '#ErrorCode, #ErrorMessage, #HasFaq',
    ExpressionAttributeNames: { '#ErrorCode': 'errorCode', '#ErrorMessage': 'errorMessage', '#HasFaq': 'hasFaq' },
    TableName: errorMasterTableName
  });

  try {
    const docClient = DynamoDBDocumentClient.from(dynamoDBClient);
    const dynamoDbResponse = (await docClient.send(command)).Items;
    return dynamoDbResponse;
  } catch (error) {
    throw error;
  }
}
