/* eslint-disable max-len */
/* eslint-disable no-useless-catch */
import {
  GetItemCommand,
  GetItemCommandInput,
  GetItemCommandOutput,
  PutItemCommand,
  PutItemCommandInput,
  UpdateItemCommand,
  UpdateItemCommandInput,
  DeleteItemCommand,
  DeleteItemCommandInput
} from '@aws-sdk/client-dynamodb';
import dynamoDBClient from '../utils/dynamoDB';
import { Session, UserInfo, UserInfoWithIds } from '../models/session';
import { sessionTableName } from '../config/config';
import { getOrDefault } from '../utils/createObject';

/**
 * セッション情報を保存する
 * @param session セッション情報
 */
export async function saveSession(session: Session): Promise<void> {
  const params: PutItemCommandInput = {
    TableName: sessionTableName,
    Item: {
      sessionId: { S: session.sessionId },
      vdId: { S: session.vdId! },
      dtpId: session.dtpId ? { S: session.dtpId } : { NULL: true },
      userId: session.userId ? { S: session.userId } : { NULL: true },
      issueInstant: { S: session.issueInstant! },
      userInfo: { S: JSON.stringify(session.userInfo) },
      state: session.state ? { S: session.state } : { NULL: true },
      freeeOauthState: session.freeeOauthState ? { S: session.freeeOauthState } : { NULL: true },
      expirationTime: { N: session.expirationTime! },
      createdAt: { S: session.createdAt! },
      updatedAt: { S: session.updatedAt! }
    }
  };

  try {
    const command = new PutItemCommand(params);
    console.log('request parameter', params);
    const result = await dynamoDBClient.send(command);
    console.log('Successfully saved Session', result);
  } catch (error) {
    throw error;
  }
}

/**
 * セッション情報を取得する
 * @param sessionId セッションID
 * @returns セッションIDに紐づくセッション情報
 */
export async function getSession(sessionId: string): Promise<Session | null> {
  const params: GetItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: sessionId }
    }
  };

  try {
    const command = new GetItemCommand(params);
    const result: GetItemCommandOutput = await dynamoDBClient.send(command);

    if (!result.Item) {
      return null;
    }
    const session: Session = {
      sessionId: result.Item.sessionId.S!,
      vdId: result.Item.vdId.S!,
      dtpId: result.Item.dtpId.S!,
      userId: result.Item.userId.S!,
      issueInstant: result.Item.issueInstant.S!,
      userInfo: {
        ninsyoKbn: (JSON.parse(result.Item.userInfo.S!) as UserInfo).ninsyoKbn,
        compId: (JSON.parse(result.Item.userInfo.S!) as UserInfo).compId,
        compName: (JSON.parse(result.Item.userInfo.S!) as UserInfo).compName,
        userSeiMei: (JSON.parse(result.Item.userInfo.S!) as UserInfo).userSeiMei,
        userTyp: (JSON.parse(result.Item.userInfo.S!) as UserInfo).userTyp,
        compAtrbt: (JSON.parse(result.Item.userInfo.S!) as UserInfo).compAtrbt,
        userKn: (JSON.parse(result.Item.userInfo.S!) as UserInfo).userKn,
        Email: (JSON.parse(result.Item.userInfo.S!) as UserInfo).Email,
        telNo: (JSON.parse(result.Item.userInfo.S!) as UserInfo).telNo,
        kigyoCd: (JSON.parse(result.Item.userInfo.S!) as UserInfo).kigyoCd,
        userAuths: {
          web21: {
            accountInquiryAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21?.accountInquiryAuth
            ),
            generalTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.generalTransferAuth
            ),
            salaryTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21?.salaryTransferAuth
            ),
            localTaxPaymentAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.localTaxPaymentAuth
            ),
            approvalAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21?.approvalAuth
            ),
            transferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21?.transferAuth
            ),
            fileSendingAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21?.fileSendingAuth
            ),
            taxesOrFeesPaymentAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.taxesOrFeesPaymentAuth
            ),
            accountTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.accountTransferAuth
            ),
            transferDestinationCheckAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.transferDestinationCheckAuth
            ),
            dataConversionAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21?.dataConversionAuth
            ),
            accountInquiryOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.accountInquiryOnSmartPhoneAuth
            ),
            transferOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.transferOnSmartPhoneAuth
            ),
            approvalOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.approvalOnSmartPhoneAuth
            ),
            preSettingAccountInfoOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.preSettingAccountInfoOnSmartPhoneAuth
            ),
            settingSecurityOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.settingSecurityOnSmartPhoneAuth
            ),
            preSettingAccountInfoAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.preSettingAccountInfoAuth
            ),
            settingSecurityAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.settingSecurityAuth
            )
          },
          gets: {
            inboundTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets?.inboundTransferAuth
            ),
            lcExportAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets?.lcExportAuth
            ),
            exportBillHandlingAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.exportBillHandlingAuth
            ),
            outboundTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.outboundTransferAuth
            ),
            lcImportAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets?.lcImportAuth
            ),
            forexInfoExchangeAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.forexInfoExchangeAuth
            ),
            corpoForexDeliveryAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.corpoForexDeliveryAuth
            ),
            parentChildContractAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.parentChildContractAuth
            ),
            selfSecurityAccessAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.selfSecurityAccessAuth
            ),
            generalSecurityAccessAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.generalSecurityAccessAuth
            )
          }
        },
        loginType: (JSON.parse(result.Item.userInfo.S!) as UserInfo).loginType
        // OTP権限回りの取得は実装時に追加
      },
      state: result.Item.state.S!,
      freeeOauthState: result.Item.freeeOauthState.S!,
      expirationTime: result.Item.expirationTime.N!,
      createdAt: result.Item.createdAt.S!,
      updatedAt: result.Item.updatedAt.S!
    };

    return session;
  } catch (error) {
    throw error;
  }
}

/**
 * セッション情報を削除する
 * @param sessionId セッションID
 */
export async function deleteSession(sessionId: string): Promise<void> {
  const params: DeleteItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: sessionId }
    },
    ConditionExpression: 'attribute_exists(sessionId)'
  };

  try {
    const command = new DeleteItemCommand(params);
    console.log('request parameter', params);
    const result = await dynamoDBClient.send(command);
    console.log('Successfully delete Session', result);
  } catch (error) {
    throw error;
  }
}

/**
 * stateを更新する
 * @param session セッション情報
 */
export async function updateState(session: Session): Promise<void> {
  const params: UpdateItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: session.sessionId }
    },
    ExpressionAttributeNames: {
      '#s': 'state',
      '#u': 'updatedAt'
    },
    ExpressionAttributeValues: {
      ':newState': session.state ? { S: session.state } : { NULL: true },
      ':newUpdatedAt': { S: session.updatedAt! }
    },
    UpdateExpression: 'SET #s = :newState,  #u = :newUpdatedAt'
  };

  try {
    const command = new UpdateItemCommand(params);
    console.log('request parameter', params);
    const result = await dynamoDBClient.send(command);
    console.log('Successfully updated state', result);
  } catch (error) {
    throw error;
  }
}

/**
 * freeeOauthStateを更新する
 * @param session セッション情報
 */
export async function updateFreeeOauthState(session: Session): Promise<void> {
  const params: UpdateItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: session.sessionId }
    },
    ExpressionAttributeNames: {
      '#s': 'freeeOauthState',
      '#u': 'updatedAt'
    },
    ExpressionAttributeValues: {
      ':newFreeeOauthState': session.freeeOauthState
        ? { S: session.freeeOauthState }
        : { NULL: true },
      ':newUpdatedAt': { S: session.updatedAt! }
    },
    UpdateExpression: 'SET #s = :newFreeeOauthState,  #u = :newUpdatedAt'
  };

  try {
    const command = new UpdateItemCommand(params);
    console.log('request parameter', params);
    await dynamoDBClient.send(command);
  } catch (error) {
    throw error;
  }
}

/**
 * dtpIdを更新する
 * @param session セッション情報
 */
export async function updateDtpId(session: Session): Promise<void> {
  const params: UpdateItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: session.sessionId }
    },
    ExpressionAttributeNames: {
      '#dtp': 'dtpId',
      '#u': 'updatedAt'
    },
    ExpressionAttributeValues: {
      ':dtpId': session.dtpId ? { S: session.dtpId } : { NULL: true },
      ':newUpdatedAt': { S: session.updatedAt! }
    },
    UpdateExpression: 'SET #dtp = :dtpId, #u = :newUpdatedAt'
  };

  try {
    const command = new UpdateItemCommand(params);
    console.log('request parameter', params);
    await dynamoDBClient.send(command);
  } catch (error) {
    throw error;
  }
}

/**
 * dtpId,userIdを更新する
 * @param session セッション情報
 */
export async function updateDtpIdAndUserId(session: Session): Promise<void> {
  const params: UpdateItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: session.sessionId }
    },
    ExpressionAttributeNames: {
      '#dtp': 'dtpId',
      '#user': 'userId',
      '#u': 'updatedAt'
    },
    ExpressionAttributeValues: {
      ':dtpId': session.dtpId ? { S: session.dtpId } : { NULL: true },
      ':userId': session.userId ? { S: session.userId } : { NULL: true },
      ':newUpdatedAt': { S: session.updatedAt! }
    },
    UpdateExpression: 'SET #dtp = :dtpId,  #user = :userId,  #u = :newUpdatedAt'
  };

  try {
    const command = new UpdateItemCommand(params);
    console.log('request parameter', params);
    await dynamoDBClient.send(command);
  } catch (error) {
    throw error;
  }
}

/**
 * 有効期限を更新する
 * @param session セッション情報
 */
export async function updateExpirationTime(session: Session): Promise<void> {
  const params: UpdateItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: session.sessionId }
    },
    ExpressionAttributeNames: {
      '#e': 'expirationTime',
      '#u': 'updatedAt'
    },
    ExpressionAttributeValues: {
      ':newExpirationTime': { N: session.expirationTime! },
      ':newUpdatedAt': { S: session.updatedAt! }
    },
    UpdateExpression: 'SET #e = :newExpirationTime,  #u = :newUpdatedAt'
  };

  try {
    const command = new UpdateItemCommand(params);
    // console.log('request parameter', params);
    // const result =
    await dynamoDBClient.send(command);
    // console.log('Successfully updated expirationTime', result);
  } catch (error) {
    throw error;
  }
}

/**
 * ユーザー情報を取得する
 * @param sessionId セッションID
 * @returns セッションIDに紐づくユーザー情報
 */
export async function getUserInfo(sessionId: string): Promise<UserInfoWithIds | null> {
  const params: GetItemCommandInput = {
    TableName: sessionTableName,
    Key: {
      sessionId: { S: sessionId }
    }
  };

  try {
    const command = new GetItemCommand(params);
    const result: GetItemCommandOutput = await dynamoDBClient.send(command);

    if (result.Item) {
      const userInfo: UserInfoWithIds = {
        vdId: result.Item.vdId.S!,
        dtpId: result.Item.dtpId.S!,
        userId: result.Item.userId.S!,
        ninsyoKbn: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).ninsyoKbn,
        userSeiMei: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).userSeiMei,
        userTyp: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).userTyp,
        compName: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).compName,
        compId: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).compId,
        Email: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).Email,
        keiyakuType: (JSON.parse(result.Item.userInfo.S!) as UserInfoWithIds).keiyakuType
      };
      console.log('Successfully get UserInfo', userInfo);
      return userInfo;
    }
    return null;
  } catch (error) {
    throw error;
  }
}
