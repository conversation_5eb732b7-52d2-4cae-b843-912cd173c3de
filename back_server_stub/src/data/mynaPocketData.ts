// CC_AuthAPIの正常値のレスポンス
const oAuthData = {
  token_type: 'Bearer',
  access_token:
    'AAIgZjVkYmNhZjgyMmY3MzYxODRkYmI1OTkxMjJhNzA5NmSaXKpuPTf3hngYVQ8jzTdgzA8k_o6jdLC3cV6eLX9Qs_OcJpH_W6yLMlcrAaZaZ15P08RMgVjUjoOCYx8yMQdjeYLRDdAEixgi6Og94M4XX7cNikp0jJZ5QfWl5ahHzsw',
  scope: 'cc:ekyc',
  expires_in: 3600,
  consented_on: 1718166544
};

// サービス利用情報提供APIの正常値のレスポンス
const serviceCheckData = {
  status: '0'
};

// 本人確認APIの正常値のレスポンス
const identificationData = {
  status: '0',
  detail: '詳細',
  uuId: 'UUID',
  ifid: 'インターフェースID',
  inTransactionId: '入力トランザクションID',
  outTransactionId: '出力トランザクションID',
  resultCode: '0',
  resultDetailMessage: '結果詳細メッセージ',
  picoId: 'PICOID',
  authrizNum: '本人確認番号',
  reqAuthDate: '20240601 120000',
  authDepart: '本人確認責任部書',
  authPerson: '本人確認責任者',
  authImpMethod: '公的個人認証',
  nameBp: '山田太郎',
  dobBp: '420000101',
  genderBp: '1',
  addressBp: '東京都江東区江東町1-1-11',
  authDoc: '公的個人認証 署名用証明書',
  authTrailNum: '20240601120000111112222',
  authVerifyRecord: '本人確認記録'
};

// ここからエラー時のデータを記載する
// エラーデータはJPKIエラー定義一覧の番号と対応
const errorData1 = {
  httpCode: '400',
  httpMessage: 'Bad Request',
  moreInformation: 'Unauthorized'
};

const errorData2 = {
  httpCode: '401',
  httpMessage: 'Unauthorized',
  moreInformation: 'Unauthorized'
};

const errorData3 = {
  httpCode: '401',
  httpMessage: 'Unauthorized',
  moreInformation:
    'Cannot pass the security checks that are required by the target API or operation, Enable debug headers for more details.'
};

const errorData4 = {
  httpCode: '401',
  httpMessage: 'Unauthorized',
  moreInformation: 'Invalid client id or secret.'
};

const errorData5 = {
  httpCode: '401',
  httpMessage: 'Unauthorized',
  moreInformation: 'Cannot find valid subscription for the incoming API request.'
};

const errorData6 = {
  httpCode: '401',
  httpMessage: 'Unauthorized',
  moreInformation:
    'Cannot pass the security checks that are required by the target API or operation, Enable debug headers for more details.'
};

const errorData7 = {
  httpCode: '401',
  httpMessage: 'Unauthorized',
  moreInformation:
    'Cannot pass the security checks that are required by the target API or operation, Enable debug headers for more details.'
};

const errorData8 = {
  httpCode: '404',
  httpMessage: 'Not Found',
  moreInformation: 'No resources match requested URI'
};

const errorData9 = {
  httpCode: '405',
  httpMessage: 'Method Not Allowed',
  moreInformation: 'The method is not allowed for the requested URL'
};

const errorData10 = {};

const errorData11 = {
  httpCode: '422',
  httpMessage: 'Unprocessable Entity',
  moreInformation: 'Unauthorized'
};

const errorData12 = {
  httpCode: '429',
  httpMessage: 'Too Many Requests',
  moreInformation: 'Assembly Rate Limit exceeded'
};

const errorData13 = {
  httpCode: '500',
  httpMessage: 'Internal Server Error',
  moreInformation: 'Unauthorized'
};

const errorData14 = {
  httpCode: '500',
  httpMessage: 'URL Open error',
  moreInformation: 'Could not connect to endpoint'
};

const errorData15 = {
  httpCode: '503',
  httpMessage: 'Service Unavailable',
  moreInformation: ''
};

const errorData16 = {
  httpCode: '503',
  httpMessage: 'Service Unavailable',
  moreInformation: 'The Service is temporarily unavailable'
};

const errorData17 = {
  error: 'Invalid_request',
  error_description: 'Unknown grant type to the token endpoint'
};

const errorData18 = {
  httpCode: '400',
  httpMessage: 'Bad Request',
  moreInformation: 'ne or more required API parameters are missing in the API request.'
};

const errorData19 = {
  error: 'Invalid_request',
  error_description: 'Missing client ID'
};

const errorData20 = {
  error: 'Invalid_request',
  error_description: 'Scope is not allowed'
};

const errorData21 = {
  error: 'unauthorized_clien',
  error_description: 'Invalid client ID or secret, or client not subscribed to this API'
};

const errorData22 = {
  httpCode: '503',
  httpMessage: 'Service Unavailable',
  moreInformation: '現在サービスを利用できません。'
};

const errorData23 = {
  httpCode: '422',
  httpMessage: 'Invalid',
  moreInformation: 'Invalid data '
};

const errorData24 = {
  httpCode: '422',
  httpMessage: 'Invalid',
  moreInformation: 'Invalid data '
};

const errorData25 = {
  httpCode: '500',
  httpMessage: 'Internal Server Error',
  moreInformation: 'Internal Server Error'
};

const errorData26 = {
  httpCode: '500',
  httpMessage: 'Internal Server Error',
  moreInformation: 'Internal Server Error'
};

const errorData29 = {
  httpCode: '400',
  httpMessage: '203',
  moreInformation: ''
};

const errorData30 = {
  httpCode: '400',
  httpMessage: '203',
  moreInformation: ''
};

const errorData31 = {
  httpCode: '500',
  httpMessage: '500',
  moreInformation: ''
};
const errorData32 = {
  httpCode: '500',
  httpMessage: '500',
  moreInformation: ''
};

const errorData33 = {
  httpCode: '500',
  httpMessage: '500',
  moreInformation: ''
};
const errorData37 = {
  httpCode: '503',
  httpMessage: '503',
  moreInformation: ''
};

const errorData39 = {
  httpCode: '400',
  httpMessage: '400',
  moreInformation: ''
};

const errorData49 = {
  httpCode: '503',
  httpMessage: '503',
  moreInformation: ''
};

export {
  oAuthData,
  serviceCheckData,
  identificationData,
  errorData1,
  errorData2,
  errorData3,
  errorData4,
  errorData5,
  errorData6,
  errorData7,
  errorData8,
  errorData9,
  errorData10,
  errorData11,
  errorData12,
  errorData13,
  errorData14,
  errorData15,
  errorData16,
  errorData17,
  errorData18,
  errorData19,
  errorData20,
  errorData21,
  errorData22,
  errorData23,
  errorData24,
  errorData25,
  errorData26,
  errorData29,
  errorData30,
  errorData31,
  errorData32,
  errorData33,
  errorData37,
  errorData39,
  errorData49
  // errorData50
};
