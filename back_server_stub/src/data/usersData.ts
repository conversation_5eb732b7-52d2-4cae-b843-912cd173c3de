import { components } from '../schemas/schema';
import { DATE_FORMAT_SLASH, serverDate } from '../utils/dateFormat';

/*
  利用者情報取得API実行時にモバイルアプリ側へ返却するモックデータを定義する
*/

// 利用者情報1
export const accountData1: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ﾄｳｷｮｳﾁｭｳｵｳ支店',
  branchNameKanji: '東京中央支店',
  accountType: '普通',
  accountNumber: '0000001',
  remitterName: 'ｲﾀｸｼｬﾒｲｲﾀｸｼｬﾒｲｲﾀｸｼｬﾒｲｲﾀｸｼｬﾒｲｲﾀｸｼｬﾒｲｲﾀｸｼｬﾒｲ',
  bankName: '三井住友銀行',
  displayAccountName: '口座１０字口座１０字',
  isHidden: true,
  index: 0,
  accountApiType: 'web21'
};

// 利用者情報2
export const accountData2: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ｵｵｻｶ支店',
  branchNameKanji: '大阪支店',
  accountType: '当座',
  accountNumber: '0000002',
  remitterName: 'ｲﾀｸｼｬﾒｲ2',
  bankName: '三井住友銀行',
  displayAccountName: null,
  isHidden: true,
  index: 1,
  accountApiType: 'web21'
};

// 利用者情報3
export const accountData3: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ﾄｳｷｮｳﾃﾞｨｽﾞﾆｰﾗﾝﾄﾞ出張所',
  branchNameKanji: '東京ディズニーランド出張所',
  accountType: '通知',
  accountNumber: '0000003',
  remitterName: 'ｲﾀｸｼｬﾒｲ3',
  bankName: '三井住友銀行',
  displayAccountName: 'ああ',
  isHidden: false,
  index: 2,
  accountApiType: 'web21'
};

// 利用者情報4
export const accountData4: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ﾅｺﾞﾔ支店',
  branchNameKanji: '名古屋支店',
  accountType: '定期',
  accountNumber: '0000004',
  remitterName: 'ｲﾀｸｼｬﾒｲ4',
  bankName: '三井住友銀行',
  displayAccountName: 'A銀行B支店',
  isHidden: false,
  index: 3,
  accountApiType: 'web21'
};

// 利用者情報5
export const accountData5: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ﾌｸｵｶ支店',
  branchNameKanji: '福岡支店',
  accountType: '積立',
  accountNumber: '0000005',
  remitterName: 'ｲﾀｸｼｬﾒｲ5',
  bankName: '三井住友銀行',
  displayAccountName: '口座１３字口座１３字口座１',
  isHidden: false,
  index: 4,
  accountApiType: 'web21'
};

// 利用者情報6
export const accountData6: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ｱﾂｷﾞ支店',
  branchNameKanji: '厚木支店',
  accountType: '普通',
  accountNumber: '0000006',
  remitterName: 'ｲﾀｸｼｬﾒｲ6',
  bankName: '三井住友銀行',
  displayAccountName: '口座１４字口座１４字口座１４',
  isHidden: false,
  index: 5,
  accountApiType: 'web21'
};

// 利用者情報7
export const accountData7: components['schemas']['AccountList'] = {
  accountId: '*************',
  account: '************',
  branchNameKana: 'ｼﾝﾕﾘｶﾞｵｶ支店',
  branchNameKanji: '新百合ヶ丘支店',
  accountType: '普通',
  accountNumber: '0000007',
  remitterName: 'ｲﾀｸｼｬﾒｲ7',
  bankName: '三井住友銀行',
  displayAccountName: '口座１５字口座１５字口座１５字',
  isHidden: false,
  index: 6,
  accountApiType: 'web21'
};

// 利用者情報
export const usersData: components['schemas']['Users'] = {
  count: 7,
  accountList: [
    accountData1,
    accountData2,
    accountData3,
    accountData4,
    accountData5,
    accountData6,
    accountData7
  ],
  balanceAuthorityStatus: 'ON',
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// ユーザー情報
export const userInfoData: components['schemas']['UserInfo'] = {
  ninsyoKbn: '01',
  vdId: '**********',
  dtpId: '<EMAIL>',
  // dtpId: '',
  userId: 'id12345',
  userSeiMei: '若草かおる',
  userTyp: '01',
  compName: '株式会社若草製作所株式会社若草製作所株式会社若草製作所株式会社若草製作',
  compId: '**************',
  Email: '<EMAIL>',
  keiyakuType: '1',
  userAuths: '1'

  // 空の値
  //   ninsyoKbn: '',
  //   vdId: '',
  //   dtpId: '',
  //   dtpId: '',
  //   userId: '',
  //   userSeiMei: '',
  //   userTyp: '',
  //   compName: '',
  //   compId: '',
  //   Email: '',
  //   keiyakuType: '',
  //   userAuths: ''
};
