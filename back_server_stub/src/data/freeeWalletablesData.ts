/* eslint-disable max-len */
import { serverDateTime } from '../utils/dateFormat';
import { components } from '../schemas/schema';

// freee他行口座情報
export const freeeWalletablesData: components['schemas']['FreeeWalletables'] = {
  walletables: [
    {
      id: 1,
      name: 'みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 100000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      isHidden: false,
      displayOrder: 0,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 2,
      name: 'ゆうちょ銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 20000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 1,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 3,
      name: 'みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行みずほ銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 300,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 2,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 4,
      name: 'りそな銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 400000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 3,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 5,
      name: 'セブン銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 5000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 4,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 6,
      name: 'あおぞら銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 600,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 5,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 7,
      name: '住信SBIネット銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 700,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 6,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 8,
      name: '三菱UFJ銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 80000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 7,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 9,
      name: '秋田銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 90,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 8,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 10,
      name: 'きらやか銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 9,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 11,
      name: 'ソニー銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1100,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 10,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 12,
      name: '北海道銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 12000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 11,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 13,
      name: '横浜銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1300,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: false,
      displayOrder: 12,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 14,
      name: '東日本銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 14000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 13,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 15,
      name: '七十七銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 15000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 14,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 16,
      name: '静岡銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1600,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: true,
      displayOrder: 15,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 17,
      name: '富山銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1700,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: false,
      displayOrder: 16,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 18,
      name: '大和ネクスト銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 18000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: true,
      displayOrder: 17,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 19,
      name: 'もみじ銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 19000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: true,
      displayOrder: 18,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 20,
      name: 'ＳＢＩ新生銀行',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 2000,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: false,
      displayOrder: 19,
      accountApiType: 'freee',
      syncStatus: '1'
    }
  ]
};

// freeeクレジットカード情報
export const freeeCreditData: components['schemas']['FreeeWalletables'] = {
  walletables: [
    {
      id: 1,
      name: '三井住友VISAカード',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      isHidden: false,
      displayOrder: 0,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 2,
      name: 'セゾンカード',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      isHidden: false,
      displayOrder: 1,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 3,
      name: 'MasterCard',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      isHidden: false,
      displayOrder: 2,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 4,
      name: 'JCBCard',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      isHidden: false,
      displayOrder: 3,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 5,
      name: 'AmericanExpressCard',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      isHidden: false,
      displayOrder: 3,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 6,
      name: 'DinersClubCard',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      isHidden: false,
      displayOrder: 4,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 7,
      name: '楽天カード',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      isHidden: false,
      displayOrder: 5,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 8,
      name: 'エポスカード',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      isHidden: false,
      displayOrder: 6,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 9,
      name: 'オリコカード',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      isHidden: false,
      displayOrder: 7,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 10,
      name: 'ビューゴールドプラスカード',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 0,
      walletableBalance: 0,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      isHidden: false,
      displayOrder: 8,
      accountApiType: 'freee',
      syncStatus: '0'
    }
    // クレジットカード枚数を増やしたい時は以下のコメントアウト外してください
    // {
    //   id: 1,
    //   name: '三井住友VISAカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: 1234567,
    //   walletableBalance: 1234567,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 9:00',
    //   isHidden: false,
    //   displayOrder: 0,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 2,
    //   name: 'セゾンカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: 1234567,
    //   walletableBalance: 1234567,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 9:00',
    //   isHidden: false,
    //   displayOrder: 1,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 3,
    //   name: 'MasterCard',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 9:00',
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 4,
    //   name: 'JCBCard',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 9:00',
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 5,
    //   name: 'AmericanExpressCard',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 10:00',
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 6,
    //   name: 'DinersClubCard',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 10:00',
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 7,
    //   name: '楽天カード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: '2023/1/1 10:00',
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 8,
    //   name: 'エポスカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: null,
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 9,
    //   name: 'オリコカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: null,
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 10,
    //   name: 'ビューゴールドプラスカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   lastSyncedAt: null,
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // // クレジットカード枚数を増やしたい時は以下のコメントアウト外してください
    // // {
    // //   id: 11,
    // //   name: 'ミライノカード',
    // //   bankId: 3,
    // //   type: 'credit_card',
    // //   lastBalance: 1234567,
    // //   walletableBalance: 1234567,
    // //   serverDateTime: serverDateTime(),
    // //   isHidden: false,
    // //   displayOrder: 2,
    // //   accountApiType: 'freee'
    // // },
    // // {
    //   id: 12,
    //   name: 'イオンカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: 1234567,
    //   walletableBalance: 1234567,
    //   serverDateTime: serverDateTime(),
    //   isHidden: false,
    //   displayOrder: 2,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 13,
    //   name: 'MUFGカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: 1234567,
    //   walletableBalance: 1234567,
    //   serverDateTime: serverDateTime(),
    //   isHidden: false,
    //   displayOrder: 2,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 14,
    //   name: 'みずほカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: **********,
    //   walletableBalance: **********,
    //   serverDateTime: serverDateTime(),
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
    // {
    //   id: 15,
    //   name: 'りそなカード',
    //   bankId: 3,
    //   type: 'credit_card',
    //   lastBalance: ********,
    //   walletableBalance: ********,
    //   serverDateTime: serverDateTime(),
    //   isHidden: false,
    //   displayOrder: 3,
    //   accountApiType: 'freee'
    // },
  ]
};

// freee口座一覧情報
export const freeeWalletablesAllData: components['schemas']['FreeeWalletables'] = {
  walletables: [
    {
      id: 1,
      name: 'あいうえおえかきくあああああああああああああああけここ銀行あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほ',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      isHidden: true,
      displayOrder: 0,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 2,
      name: '12345678901234567890123456789012345',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 1,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 3,
      name: 'abcdefghijklmnopqrstuvwxyzabcdefghij',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 2,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 4,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 3,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 5,
      name: 'freee銀行5',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 4,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 6,
      name: 'freee銀行6',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 5,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 7,
      name: 'freee銀行7',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 6,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 8,
      name: 'freee銀行8',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: false,
      displayOrder: 7,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 9,
      name: 'freee銀行9',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 8,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 10,
      name: 'freee銀行10',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      bankName: '',
      isHidden: true,
      displayOrder: 9,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 11,
      name: 'freee銀行11',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 10,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 12,
      name: 'freee銀行12',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 11,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 13,
      name: 'freee銀行13',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: false,
      displayOrder: 12,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 14,
      name: 'freee銀行14',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 13,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 14,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: true,
      displayOrder: 15,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 10:00',
      bankName: '',
      isHidden: false,
      displayOrder: 16,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: true,
      displayOrder: 17,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: true,
      displayOrder: 18,
      accountApiType: 'freee',
      syncStatus: '1'
    },
    {
      id: 20,
      name: 'freee銀行5',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: null,
      bankName: '',
      isHidden: false,
      displayOrder: 19,
      accountApiType: 'freee',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '1'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 15,
      name: 'freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行1501234567890123456789012345678901234567890freee銀行150123456789012345678901234567890123456789012345',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 16,
      name: 'freee銀行1',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: '2024/1/1 1:01',
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 17,
      name: 'freee銀行2',
      bankId: 3,
      type: 'bank_account',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 18,
      name: 'freee銀行3',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    },
    {
      id: 19,
      name: 'freee銀行4',
      bankId: 3,
      type: 'credit_card',
      lastBalance: 1234567,
      walletableBalance: 1234567,
      serverDateTime: serverDateTime(),
      lastSyncedAt: '2023/1/1 9:00',
      syncStatus: '0'
    }
  ]
};
