/* eslint-disable max-len */
import { components } from '../schemas/schema';

// STEP1 代表者 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData3a1: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'REPRESENTATIVE',
  isIdentityVerified: true,
  validationStatus: 'STEP1'
};

// STEP2 代表者 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData3a2: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'REPRESENTATIVE',
  isIdentityVerified: false,
  validationStatus: 'STEP2'
};

// STEP2 代表者 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData3b1: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'REPRESENTATIVE',
  isIdentityVerified: true,
  validationStatus: 'STEP2'
};

// STEP2 代理人 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData3b2: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'AGENT',
  isIdentityVerified: false,
  validationStatus: 'STEP2'
};
// STEP2 代理人 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData01: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'AGENT',
  isIdentityVerified: true,
  validationStatus: 'STEP2'
};

// STEP3 代理人 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData02: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'AGENT',
  isIdentityVerified: true,
  validationStatus: 'STEP3'
};

// STEP4 実質的支配者1 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData03: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY1',
  isIdentityVerified: true,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者1 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData04: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY1',
  isIdentityVerified: false,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者2 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData05: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY2',
  isIdentityVerified: true,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者2 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData06: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY2',
  isIdentityVerified: false,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者3 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData07: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY3',
  isIdentityVerified: true,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者3 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData08: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY3',
  isIdentityVerified: false,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者4 本人確認実施済み
export const getOpenAccountApplicationTemporaryResponseData09: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY4',
  isIdentityVerified: true,
  validationStatus: 'STEP4'
};

// STEP4 実質的支配者4 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData10: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'BENEFICIARY4',
  isIdentityVerified: false,
  validationStatus: 'STEP4'
};

// STEP5 代表者 本人確認未実施
export const getOpenAccountApplicationTemporaryResponseData11: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] = {
  userType: 'REPRESENTATIVE',
  isIdentityVerified: false,
  validationStatus: 'STEP4'
};

// 口座開設済みかつ不備情報がない場合
export const getScreeningStatusesResponseData: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: true,
  isRepresentativeHasFault: false,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ不備情報がない場合
export const getScreeningStatusesResponseData2: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: false,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ代表者の不備情報がある場合
export const getScreeningStatusesResponseData3: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: true,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ代理人の不備情報がある場合
export const getScreeningStatusesResponseData4: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: false,
  isAgentHasFault: true,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ実質的支配者1の不備情報がある場合
export const getScreeningStatusesResponseData5: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: false,
  isAgentHasFault: false,
  isBeneficiary1HasFault: true,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ実質的支配者2の不備情報がある場合
export const getScreeningStatusesResponseData6: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: false,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: true,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ実質的支配者3の不備情報がある場合
export const getScreeningStatusesResponseData7: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: false,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: true,
  isBeneficiary4HasFault: false
};

// 口座開設未実施かつ実質的支配者4の不備情報がある場合
export const getScreeningStatusesResponseData8: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: false,
  isRepresentativeHasFault: false,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: true
};

// 口座開設実施済みかつ代表者の不備情報がある場合
export const getScreeningStatusesResponseData9: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: true,
  isRepresentativeHasFault: true,
  isAgentHasFault: false,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: true
};

// 口座開設実施済みかつ代理人の不備情報がある場合
export const getScreeningStatusesResponseData10: components['schemas']['OpenAccountApplicationScreeningStatusesResponse'] = {
  isAccountOpeningRequestSubmitted: true,
  isRepresentativeHasFault: false,
  isAgentHasFault: true,
  isBeneficiary1HasFault: false,
  isBeneficiary2HasFault: false,
  isBeneficiary3HasFault: false,
  isBeneficiary4HasFault: true
};
