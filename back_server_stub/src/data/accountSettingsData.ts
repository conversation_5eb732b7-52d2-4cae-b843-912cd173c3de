/* eslint-disable max-len */
import { components } from '../schemas/schema';

/*
  口座表示設定取得API実行時にモバイルアプリ側へ返却するモックデータを定義する
*/

/*
  BFFスタブでは固定値で設定を返すため、口座表示設定の挙動を確認する場合はバックエンドスタブorステージング環境で行うこと
*/

// 口座表示設定1
export const accountSettingData1: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  // 正常系
  displayName: '振込用口座1',
  // 文字数一行超過
  // displayName: '123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890',
  isHidden: false,
  accountApiType: 'web21'
};

// 口座表示設定2
export const accountSettingData2: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  displayName: '口座１５字口座１５字口座１５字',
  isHidden: false,
  accountApiType: 'web21'
};

// 口座表示設定3
export const accountSettingData3: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  displayName: '口座１０字口座１０字',
  isHidden: true,
  accountApiType: 'web21'
};

// 口座表示設定4
export const accountSettingData4: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  displayName: '口座１１字口座１１字口',
  isHidden: false,
  accountApiType: 'web21'
};

// 口座表示設定5
export const accountSettingData5: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  displayName: 'AA銀行B支店',
  isHidden: false,
  accountApiType: 'web21'
};

// 口座表示設定6
export const accountSettingData6: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  displayName: '口座１３字口座１３字口座１',
  isHidden: false,
  accountApiType: 'web21'
};

// 口座表示設定7
export const accountSettingData7: components['schemas']['AccountSetting'] = {
  accountId: '*************',
  displayName: '口座１４字口座１４字口座１４',
  isHidden: false,
  accountApiType: 'web21'
};

// 口座表示設定8
export const accountSettingData8: components['schemas']['AccountSetting'] = {
  accountId: '1',
  isHidden: false,
  accountApiType: 'freee',
};

// 口座表示設定9
export const accountSettingData9: components['schemas']['AccountSetting'] = {
  accountId: '2',
  isHidden: true,
  accountApiType: 'freee',
};

// 口座表示設定10
export const accountSettingData10: components['schemas']['AccountSetting'] = {
  accountId: '3',
  isHidden: false,
  accountApiType: 'freee',
};

// 口座表示設定11
export const accountSettingData11: components['schemas']['AccountSetting'] = {
  accountId: '4',
  isHidden: true,
  accountApiType: 'freee',
};

// 口座表示設定
export const accountSettingsData: components['schemas']['AccountSettings'] = {
  accounts: [
    accountSettingData1,
    accountSettingData2,
    accountSettingData3,
    accountSettingData4,
    accountSettingData5,
    accountSettingData6,
    accountSettingData7,
    accountSettingData8,
    accountSettingData9,
    accountSettingData10,
    accountSettingData11,
  ]
};
