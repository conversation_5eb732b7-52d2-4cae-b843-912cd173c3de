/**
  * For optional attributes:
    - omit the key entirely so that the <attribute> tag is not added in the SAML
    - or set the value to '' so that the attribute value is empty

  * For enum attributes
    - keeping the expected enum values in the TS definition for reference
    - if you want to test unexpected values, please use the // @ts-ignore annotation
*/
export type ValueDoorUserAttributes = {
  ninsyoKbn: '01' | '02' | '03';
  compId: string;
  compName: string;
  userSeiMei: string;
  userTyp: '01' | '02' | '03' | '04' | '05';
  compAtrbt: '01' | '09';
  userKn: string;
  Email?: string;
  telNo?: string;
  loginType: '214' | '215' | '216';
  kigyoCd?: string;
  otpId?: string;
  otpSerialNo?: string;
  otpSerialNoReissue?: string;
  otpAplKbn?: '0' | '1' | '';
  otpReissueKbn?: '0' | '1' | '';
  otpKind?: '01' | '02' | '03' | '';
  otpKindReissue?: '01' | '02' | '03' | '';
  otpDeviceStatus?: '01' | '02' | '03' | '04' | '90' | '91' | '99' | '';
  otpDeviceStsReissue?: '01' | '02' | '03' | '91' | '';
  otpReissueAction?: '1' | '2' | '3' | '';
  otpNonOtpExpiredDate?: string;
  otpServiceCode?: '0' | '1' | '';
  keiyakuType?: '1' | '2' | '3' | '4' | '';
  clientId: string;
  userAuths?: string[];
};

const valueDoorUsers: {
  [vdid: string]: ValueDoorUserAttributes;
} = {
  '0000000001': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName:
      '最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先',
    userSeiMei:
      '最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email:
      'maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000004': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName:
      '最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先',
    userSeiMei:
      '最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email:
      'maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000005': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName:
      '最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先',
    userSeiMei:
      '最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email:
      'maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000006': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName:
      '最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先',
    userSeiMei:
      '最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email:
      'maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000007': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName:
      '最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先',
    userSeiMei:
      '最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email:
      'maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000008': {
    // @ts-ignore: testing unexpected value on purpose
    ninsyoKbn: '04',
    compId: '0000230000000',
    compName: 'web21取引口座照会権限なし',
    userSeiMei: '利用者8',
    // @ts-ignore: testing unexpected value on purpose
    userTyp: '06',
    compAtrbt: '01',
    userKn: 'ﾘﾖｳｼｬ1',
    Email: '',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    kigyoCdResultCd: '',
    userAuths: ['05:01110111111111111011'],
    clientId: '0123456789'
  },
  '0000000009': {
    ninsyoKbn: '01',
    compId: '98765432109876',
    compName: '北海道太郎企業',
    userSeiMei: 'ホッカイドウタロウ',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'ホッカイドウタロウ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789'
  },
  '0000000010': {
    ninsyoKbn: '01',
    compId: '0011223344556677',
    compName: 'アクセス権限あり-非仕向送金',
    userSeiMei: 'アクセスケンゲンアリ-ヒシムケソウキン',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'ヒシムケソウキン',
    Email: '<EMAIL>',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:10000000000000000000'],
    clientId: '0123456789'
  },
  '0000000011': {
    ninsyoKbn: '01',
    compId: '99992101234567',
    compName: 'アクセス権限あり-非仕向送金',
    userSeiMei: 'アクセスケンゲンアリ-ヒシムケソウキン',
    userTyp: '03',
    compAtrbt: '01',
    userKn: 'ヒシムケソウキン',
    Email: '<EMAIL>',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:10000000000000000000'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000012': {
    ninsyoKbn: '01',
    compId: '1234567890123',
    compName: 'アクセス権限あり-仕向送金',
    userSeiMei: 'アクセスケンゲンアリ-シムケソウキン',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'シムケソウキン',
    Email: '',
    telNo: '',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:00010000000000000000'],
    clientId: '0123456789'
  },
  '0000000013': {
    ninsyoKbn: '01',
    compId: '1234567890123',
    compName: 'アクセス権限あり-仕向送金',
    userSeiMei: 'アクセスケンゲンアリ-シムケソウキン',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'シムケソウキン',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:00010000000000000000'],
    clientId: '0123456789'
  },
  '0000000020': {
    ninsyoKbn: '01',
    compId: '12345678901234',
    compName: '大阪SAML企業',
    userSeiMei: '大阪さむる太郎',
    userTyp: '04',
    compAtrbt: '01',
    userKn: 'オオサカサムルタロウ',
    Email: '<EMAIL>',
    telNo: '09033334444',
    loginType: '214',
    kigyoCd: '',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789'
  },
  '0000000021': {
    ninsyoKbn: '01',
    compId: '3344556677889910',
    compName: 'アクセス権限あり-セキュリティ管理(本人のユーザ権限設定)',
    userSeiMei: 'アクセスケンゲンアリ-セキュリティカンリ(ホンニンノユーザケンゲンセッテイ)',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'セキュリティカンリ-ホンニンノユーザケンゲンセッテイ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:00000000000000000010'],
    clientId: '0123456789'
  },
  '0000000034': {
    ninsyoKbn: '01',
    compId: '4455667788991011',
    compName: 'アクセス権限あり-セキュリティ管理',
    userSeiMei: 'アクセスケンゲンアリ-セキュリティカンリ',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'セキュリティカンリ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:00000000000000000001'],
    clientId: '0123456789'
  },
  '0000000031': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName:
      '最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先名９６最大値取引先',
    userSeiMei:
      '最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者名６０最大値利用者',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email:
      'maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail_address128maximume_mail',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  },
  '0000000035': {
    ninsyoKbn: '01',
    compId: '5566778899101112',
    compName: 'アクセス権限あり-非仕向送金・仕向送金',
    userSeiMei: 'アクセスケンゲンアリ-ヒシムケソウキン・シムケソウキン',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'ヒシムケソウキン・シムケソウキン',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:10010000000000000000'],
    clientId: '0123456789'
  },
  '0000000036': {
    ninsyoKbn: '01',
    compId: '6677889910111213',
    compName: 'アクセス権限あり-仕向送金・セキュリティ管理(本人のユーザ権限設定)・セキュリティ管理',
    userSeiMei:
      'アクセスケンゲンアリ-シムケソウキン・セキュリティカンリ(ホンニンノユーザケンゲンセッテイ)・セキュリティカンリ',
    userTyp: '01',
    compAtrbt: '01',
    userKn:
      'シムケソウキン・セキュリティカンリ(ホンニンノユーザケンゲンセッテイ)・セキュリティカンリ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:00010000000000000011'],
    clientId: '0123456789'
  },
  '0000000037': {
    ninsyoKbn: '01',
    compId: '7788991011121314',
    compName:
      'アクセス権限あり-非仕向送金・仕向送金・セキュリティ管理(本人のユーザ権限設定)・セキュリティ管理',
    userSeiMei:
      'アクセスケンゲンアリ-ヒシムケソウキン・シムケソウキン・セキュリティカンリ(ホンニンノユーザケンゲンセッテイ)・セキュリティカンリ',
    userTyp: '01',
    compAtrbt: '01',
    userKn:
      'ヒシムケソウキン・シムケソウキン・セキュリティカンリ(ホンニンノユーザケンゲンセッテイ)・セキュリティカンリ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:10010000000000000011'],
    clientId: '0123456789'
  },
  '0000000038': {
    ninsyoKbn: '01',
    compId: '8899101112131415',
    compName: 'アクセス権限なし-全権限なし',
    userSeiMei: 'アクセスケンゲンナシ-ゼンケンゲンナシ',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'ゼンケンゲンナシ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:00000000000000000000'],
    clientId: '0123456789'
  },
  '0000000039': {
    ninsyoKbn: '01',
    compId: '9910111213141516',
    compName: 'アクセス権限なし-必要4権限なし',
    userSeiMei: 'アクセスケンゲンナシ-ヒツヨウ4ケンゲンナシ',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'ヒツヨウ4ケンゲンナシ',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11110111111111111011', '09:01101111111111111100'],
    clientId: '0123456789'
  },
  '0000000040': {
    ninsyoKbn: '01',
    compId: '11112201111111',
    compName: '会社名',
    userSeiMei: 'ユーザー名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'test1',
    Email: '<EMAIL>',
    telNo: '09012345678',
    loginType: '214',
    kigyoCd: '20230828010101',
    userAuths: ['05:11111111111111111111', '09:11111111111111111111'],
    clientId: '0123456789',
    otpId: 'otpId',
    otpAplKbn: '1',
    otpDeviceStatus: '99',
    otpDeviceStsReissue: '91',
    otpKind: '03',
    otpKindReissue: '02',
    otpNonOtpExpiredDate: '20240312',
    otpReissueAction: '2',
    otpReissueKbn: '1',
    otpSerialNo: 'ABC1234567',
    otpSerialNoReissue: 'BHFF567456847',
    otpServiceCode: '1',
    keiyakuType: '2'
  }
};

export default valueDoorUsers;
