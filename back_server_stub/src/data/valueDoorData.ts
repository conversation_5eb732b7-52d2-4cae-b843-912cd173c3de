import { samlResponse as samlResponseMethod } from '../utils/samlResponse';

export const getValueDoorLoginData = (vdId: string, password: string) => {
  let valueDoorLoginData;
  if (password === '11111111') {
    // (削除) 各種ご登録画面画面
    // -> APNextScrID: 'AVVE9H220'を返却するパターンは廃止されたので、異常系パターンに変更
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'XXXXXXXXX',
      OutPageId: '0',
      ErrMesUmuF: '0',
      ErrMesID: '',
      ErrTaisyo: '',
      _W_CurSessionID: 'aaaaayyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: '<EMAIL>'
    };
  } else if (password === '22222222') {
    // パスワード変更画面
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVVE9H003',
      OutPageId: '0',
      ErrMesUmuF: '1',
      ErrMesID: 'C0028',
      ErrTaisyo: 'パスワードを変更してください。',
      _W_CurSessionID: 'bbbbbyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  } else if (password === '33333333') {
    // 有効期限切お知らせ画面
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVVE9H025',
      OutPageId: '0',
      ErrMesUmuF: '1',
      ErrMesID: 'C0008',
      ErrTaisyo:
        'パスワードがお客さま指定の有効期限を超えました。パスワードの変更を行ってください。',
      _W_CurSessionID: 'cccccyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  } else if (password === '44444444') {
    // パスワード不正 (業務エラー)
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVVE999002',
      OutPageId: '1',
      ErrMesUmuF: '1',
      ErrMesID: 'C0007',
      ErrTaisyo: 'パスワードが正しくありません。',
      _W_CurSessionID: 'dddddyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  } else if (password === '55555555') {
    // OTP有効化
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVVE9H211',
      // APNextScrID: '',
      OutPageId: '0',
      ErrMesUmuF: '0',
      ErrMesID: '',
      ErrTaisyo: '',
      // _W_CurSessionID: 'eeeeeyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '00', // 他の項目に応じてエラーメッセージ変更
      // _W_ActivateButtonFlag: '0', // OTP有効化(スキップ)API実行
      // _W_ActivateButtonFlag: '1', // OTP有効化(スキップ)API実行
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '1234567890',
      // OtpKind: '01', // 不着理由カード
      OtpKind: '02', // 不着理由アプリ
      // OtpKind: '03', //不着理由カメラ付きトークン
      OtpActivateKigen: '20291111',
      OtpHuchakueKbn: '0', // 不着でない用のエラー文言
      // OtpHuchakueKbn: '1', // 不着用のエラー文言
      OtpHuchakueRsn: 'OTP不着理由',
      Email: ''
    };
  } else if (password === '66666666') {
    // OTP再発行エラー
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVVE9H219',
      OutPageId: '0',
      ErrMesUmuF: '1',
      ErrMesID: 'C0064',
      ErrTaisyo:
        'ご利用のValueDoorIDは一時的にご利用いただけない状態です。ワンタイムパスワードの到着をお待ちいただいた上で、お取引店にお問い合わせください。',
      _W_CurSessionID: 'fffffyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  } else if (password === '77777777') {
    // システムエラー、業務エラー
    valueDoorLoginData = {
      UkeID: '',
      GymCd: '',
      SessionId: '',
      APNextScrID: '',
      OutPageId: '2', // システムエラー
      // OutPageId: '1', //業務エラー
      ErrMesUmuF: '1',
      ErrMesID: 'S2001', // システムエラー
      // ErrMesID: 'C2001', //業務エラー
      ErrTaisyo: '処理に失敗しました。しばらく経ってから再度実行してください。',
      _W_CurSessionID: 'gggggyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  } else if (password === '88888888') {
    // otp有効化エラー
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVV999110',
      OutPageId: '0',
      ErrMesUmuF: '0',
      ErrMesID: '',
      ErrTaisyo:
        'ご利用のValueDoorIDは一時的にご利用いただけない状態です。ワンタイムパスワードの到着をお待ちください。',
      _W_CurSessionID: 'dddddyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: 'xxxxx',
      OtpSerialNo: '1234567890',
      OtpKind: '',
      OtpActivateKigen: '20240111',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  } else {
    // Web21転送
    valueDoorLoginData = {
      UkeID: 'AVVE9H901',
      GymCd: '00',
      SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
      APNextScrID: 'AVV999100',
      OutPageId: '0',
      ErrMesUmuF: '0',
      ErrMesID: '',
      ErrTaisyo: '',
      _W_CurSessionID: 'hhhhhyyyyyyyyyyyyyyyyyyy',
      _W_ActivateButtonFlag: '0',
      User: vdId,
      from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId })),
      OtpSerialNo: '',
      OtpKind: '',
      OtpActivateKigen: '',
      OtpHuchakueKbn: '',
      OtpHuchakueRsn: '',
      Email: ''
    };
  }
  return valueDoorLoginData;
};

export const getValueDoorExpirationData = (vdId: string) => {
  // パスワード変更画面
  const valueDoorExpirationData = {
    UkeID: 'AVVE9H025',
    GymCd: '00',
    SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    // 異常系
    // APNextScrID: 'XXXXXXXXX',
    // 正常系
    APNextScrID: 'AVVE9H003',
    // APNextScrID: '',
    // OutPageId: '1', //業務エラー
    // OutPageId: '2', // システムエラー
    ErrMesUmuF: '1',
    // ErrMesID: 'C0028', //業務エラー
    // ErrMesID: 'S0028', // システムエラー
    ErrTaisyo: 'getValueDoorExpirationDataのErrTaisyo',
    _W_CurSessionID: 'iiiiiyyyyyyyyyyyyyyyyyyy',
    _W_ActivateButtonFlag: '0',
    User: vdId,
    OtpSerialNo: '',
    OtpKind: '',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: '',
    from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId }))
  };
  return valueDoorExpirationData;
};

export const getValueDoorChangePasswordData = (vdId: string) => {
  // Web21転送
  const valueDoorChangePasswordData = {
    UkeID: 'AVVE9H903',
    GymCd: '00',
    SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    APNextScrID: '',
    OutPageId: '0',
    // OutPageId: '1', // 業務エラー
    // OutPageId: '2', //システムエラー
    ErrMesUmuF: '1',
    ErrMesID: '',
    // ErrMesID: 'C000', // 業務エラー
    // ErrMesID: 'S000', //システムエラー
    ErrTaisyo: 'getValueDoorRegisterVariousDataのErrTaisyo',
    _W_CurSessionID: 'jjjjjyyyyyyyyyyyyyyyyyyy',
    _W_ActivateButtonFlag: '0',
    User: vdId,
    OtpSerialNo: '',
    OtpKind: '',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: '',
    from_vd_param: btoa(samlResponseMethod({ valueDoorId: vdId }))
  };
  return valueDoorChangePasswordData;
};

export const getValueDoorRegisterVariousData = () => {
  const valueDoorRegisterVariousData = {
    // Web21転送
    UkeID: 'AVVE9H220',
    GymCd: '00',
    SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    APNextScrID: 'AVV999100',
    OutPageId: '0',
    // OutPageId: '1', //業務エラー
    // OutPageId: '2', // システムエラー
    ErrMesUmuF: '',
    // ErrMesUmuF: '1',
    ErrMesID: '',
    // ErrMesID: 'C000', //業務エラー
    // ErrMesID: 'S000', // システムエラー
    ErrTaisyo: 'getValueDoorRegisterVariousDataのErrTaisyo',
    _W_CurSessionID: 'kkkkkyyyyyyyyyyyyyyyyyyy',
    User: '',
    OtpSerialNo: '',
    OtpKind: '',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: '',
    from_vd_param: '',
    RedirectURL:
      'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/transfer_info_input_dummy.html',
    Email: ''
  };

  //   // 有効期限切れ
  //   UkeID: 'AVVE9H901',
  //   GymCd: '00',
  //   SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
  //   APNextScrID: 'AVVE9H025',
  //   OutPageId: '0',
  //   ErrMesUmuF: '1',
  //   ErrMesID: 'C0008',
  //   ErrTaisyo: 'パスワードがお客さま指定の有効期限を超えました。パスワードの変更を行ってください。',
  //   _W_CurSessionID: 'cccccyyyyyyyyyyyyyyyyyyy',
  //   _W_ActivateButtonFlag: '0',
  //   User: '',
  //   from_vd_param: btoa(samlResponseMethod({ valueDoorId: '' })),
  //   OtpSerialNo: '',
  //   OtpKind: '',
  //   OtpActivateKigen: '',
  //   OtpHuchakueKbn: '',
  //   OtpHuchakueRsn: '',
  //   Email: ''
  // };

  // OTP有効化
  // UkeID: 'AVVE9H901',
  // GymCd: '00',
  // SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
  // // APNextScrID: 'AVVE9H211',
  // APNextScrID: '',
  // OutPageId: '1',
  // ErrMesUmuF: '1',
  // ErrMesID: 'C000',
  // ErrTaisyo: 'ErrTaisyo',
  // _W_CurSessionID: 'dddddyyyyyyyyyyyyyyyyyyy',
  // User: 'xxxxx',
  // OtpSerialNo: '1234567890',
  // OtpKind: '01',
  // OtpActivateKigen: '20240111',
  // OtpHuchakueKbn: '0',
  // OtpHuchakueRsn: '',
  // Email: ''
  // };
  return valueDoorRegisterVariousData;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const getValueDoorNavigateSystemData = (isTransfer: boolean) => {
  const valueDoorNavigateSystemData = {
    // 後続システム遷移（正常系）
    UkeID: 'AVVE9H901',
    GymCd: '00',
    SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    APNextScrID: 'AVV999100',
    // APNextScrID: 'AVVE9H211',
    OutPageId: '0',
    ErrMesUmuF: '0',
    ErrMesID: '',
    ErrTaisyo: '',
    _W_CurSessionID: 'xxxxxyyyyyyyyyyyyyyyyyyy',
    _W_ActivateButtonFlag: '00',
    // isTransfer：振込かどうかによって、遷移先のurlを設定する
    RedirectURL: isTransfer
      ? 'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/transfer_info_input_dummy.html'
      : 'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/web21_approval_dummy.html',
    from_vd_param: btoa(samlResponseMethod({ valueDoorId: 'xxxxx' })),
    OtpSerialNo: '',
    OtpKind: '01',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: 'OtpHuchakueRsn',
    Email: ''

    // 初回ログインのため、PW変更を求める
    // UkeID: 'AVVE9H901',
    // GymCd: '00',
    // SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    // APNextScrID: 'AVVE9H003',
    // OutPageId: '0',
    // ErrMesUmuF: '1',
    // ErrMesID: 'C0028',
    // ErrTaisyo: 'パスワードを変更してください。',
    // _W_CurSessionID: 'bbbbbyyyyyyyyyyyyyyyyyy',
    // _W_ActivateButtonFlag: '0',
    // OtpSerialNo: '',
    // OtpKind: '',
    // OtpActivateKigen: '',
    // OtpHuchakueKbn: '',
    // OtpHuchakueRsn: '',
    // Email: ''

    // PW有効期限切お知らせ画面（準正常系）
    // UkeID: 'AVVE9H901',
    // GymCd: '00',
    // SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    // APNextScrID: 'AVVE9H025',
    // OutPageId: '0',
    // ErrMesUmuF: '1',
    // ErrMesID: 'C0008',
    // ErrTaisyo: 'パスワードがお客さま指定の有効期限を超えました。パスワードの変更を行ってください。',
    // _W_CurSessionID: 'cccccyyyyyyyyyyyyyyyyyyy',
    // _W_ActivateButtonFlag: '0',
    // OtpSerialNo: '',
    // OtpKind: '02',
    // OtpActivateKigen: '',
    // OtpHuchakueKbn: '',
    // OtpHuchakueRsn: '',
    // Email: ''

    // OTP有効化画面
    // UkeID: 'AVVE9H901',
    // GymCd: '00',
    // SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    // APNextScrID: 'AVVE9H211',
    // OutPageId: '0',
    // ErrMesUmuF: '1',
    // ErrMesID: 'C0064',
    // ErrTaisyo: 'ご利用のValueDoorIDは一時的にご利用いただけない状態です。ワンタイムパスワードの到着をお待ちください。',
    // _W_CurSessionID: 'dddddyyyyyyyyyyyyyyyyyyy',
    // _W_ActivateButtonFlag: '00',
    // // _W_ActivateButtonFlag: '0',
    // // _W_ActivateButtonFlag: '1',
    // User: 'xxxxx',
    // OtpSerialNo: '1234567890',
    // OtpKind: '01',
    // // OtpKind: '02',
    // // OtpKind: '03',
    // OtpActivateKigen: '20240111',
    // OtpHuchakueKbn: '0',
    // // OtpHuchakueKbn: '1',
    // OtpHuchakueRsn: 'OTP不着理由',
    // Email: ''

    // otp有効化エラー
    // UkeID: 'AVVE9H901',
    // GymCd: '00',
    // SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    // APNextScrID: 'AVV999110',
    // OutPageId: '1',
    // ErrMesUmuF: '1',
    // ErrMesID: 'C0064',
    // ErrTaisyo:
    //   'ご利用のValueDoorIDは一時的にご利用いただけない状態です。ワンタイムパスワードの到着をお待ちいただいた上で、お取引店にお問い合わせください。',
    // _W_CurSessionID: 'dddddyyyyyyyyyyyyyyyyyyy',
    // _W_ActivateButtonFlag: '0',
    // User: 'xxxxx',
    // OtpSerialNo: '1234567890',
    // OtpKind: '01',
    // OtpActivateKigen: '20240111',
    // OtpHuchakueKbn: '0',
    // OtpHuchakueRsn: '',
    // Email: ''
  };

  // VDログインエラー
  // UkeID: 'AVVE9H901',
  //   GymCd: '00',
  //   SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
  //   APNextScrID: 'AVV999100',
  //   // APNextScrID: 'AVVE9H211',
  //   OutPageId: '2',
  //   ErrMesUmuF: '1',
  //   ErrMesID: 'S',
  //   ErrTaisyo: '',
  //   _W_CurSessionID: 'xxxxxyyyyyyyyyyyyyyyyyyy',
  //   _W_ActivateButtonFlag: '00',
  //   // isTransfer：振込かどうかによって、遷移先のurlを設定する
  //   RedirectURL: isTransfer
  //     ? 'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/transfer_info_input_dummy.html'
  //     : 'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/web21_approval_dummy.html',
  //   from_vd_param: btoa(samlResponseMethod({ valueDoorId: 'xxxxx' })),
  //   OtpSerialNo: '',
  //   OtpKind: '01',
  //   OtpActivateKigen: '',
  //   OtpHuchakueKbn: '',
  //   OtpHuchakueRsn: 'OtpHuchakueRsn',
  //   Email: ''
  // };
  return valueDoorNavigateSystemData;
};

export const getValueDoorEnableOtpNowData = () => {
  // OTP有効化完了画面
  const valueDoorEnableOtpNowData = {
    UkeID: 'AVVE9H901',
    GymCd: '00',
    SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    APNextScrID: 'AVVE9H212',
    OutPageId: '0',
    ErrMesUmuF: '0',
    ErrMesID: '',
    ErrTaisyo: '',
    _W_CurSessionID: 'aaaaayyyyyyyyyyyyyyyyyyy',
    _W_ActivateButtonFlag: '0', // OTP有効化(スキップ)API実行
    User: 'xxxxx',
    OtpSerialNo: '',
    OtpKind: '',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: ''
  };
  return valueDoorEnableOtpNowData;
};

export const getValueDoorEnableOtpLaterData = () => {
  // Web21転送
  const valueDoorEnableOtpLaterData = {
    UkeID: 'AVVE9H901',
    GymCd: '00',
    SessionId: 'xxxxxxxxxxxxxxxxxxxxxxxx',
    // APNextScrID: 'AVV999100',
    APNextScrID: '',
    // OutPageId: '0',
    OutPageId: '1', // 業務エラー
    // OutPageId: '2', // システムエラー
    ErrMesUmuF: '1',
    // ErrMesUmuF: '0',
    // ErrMesID: '',
    ErrMesID: 'C000', // 業務エラー
    // ErrMesID: 'S000', // システムエラー
    ErrTaisyo: 'getValueDoorEnableOtpLaterDataのErrTaisyo',
    _W_CurSessionID: 'xxxxxyyyyyyyyyyyyyyyyyyy',
    _W_ActivateButtonFlag: '0',
    RedirectURL:
      'http://dtp-dv-mobile-bff-stub.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/transfer_info_input_dummy.html',
    User: 'xxxxx',
    from_vd_param: btoa(samlResponseMethod({ valueDoorId: 'xxxxx' })),
    OtpSerialNo: '',
    OtpKind: '',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: ''
  };
  return valueDoorEnableOtpLaterData;
};

export const getValueDoorLogoutData = () => {
  // ログイン前ホーム画面
  const valueDoorLogoutData = {
    UkeID: '',
    GymCd: '',
    SessionId: '',
    APNextScrID: '',
    OutPageId: '0',
    ErrMesUmuF: '1',
    ErrMesID: 'S1219',
    ErrTaisyo: 'ログアウトしました。',
    _W_CurSessionID: '',
    _W_ActivateButtonFlag: '0',
    User: '',
    OtpSerialNo: '',
    OtpKind: '',
    OtpActivateKigen: '',
    OtpHuchakueKbn: '',
    OtpHuchakueRsn: '',
    from_vd_param: ''
  };
  return valueDoorLogoutData;
};
