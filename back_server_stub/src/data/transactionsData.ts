/* eslint-disable @typescript-eslint/no-loss-of-precision */
/* eslint-disable operator-linebreak */
import { components } from '../schemas/schema';
import { options, nowDate } from '../utils/dateFormat';

/*
  入出金明細取得API実行時にモバイルアプリ側へ返却するモックデータを定義する
*/

function formatJapaneseCalendarDate(n: number): string {
  const startDate = new Date('2005-07-01'); // 初めの日付
  const endDate = new Date('2005-08-31'); // 最後の日付
  const diffInDays = Math.min(Math.max(1, n), 100); // nを1から100の範囲に制限する

  const targetDate = new Date(
    startDate.getTime() + (endDate.getTime() - startDate.getTime()) * (diffInDays / 100)
  );

  // const year = targetDate.getFullYear().toString().slice(-2);
  const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
  const day = targetDate.getDate().toString().padStart(2, '0');

  return `05/${month}/${day}`;
}

function formatCalendarDate(n: number): string {
  const startDate = new Date('2023-07-01'); // 初めの日付
  const endDate = new Date('2023-08-31'); // 最後の日付
  const diffInDays = Math.min(Math.max(1, n), 100); // nを1から100の範囲に制限する

  const targetDate = new Date(
    startDate.getTime() + (endDate.getTime() - startDate.getTime()) * (diffInDays / 100)
  );

  const year = targetDate.getFullYear().toString();
  const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
  const day = targetDate.getDate().toString().padStart(2, '0');

  return `${year}/${month}/${day}`;
}

function transactionTypeJudge(
  number: number
): components['schemas']['TransactionsDetailSmbc']['transactionType'] {
  const remainder = number % 8;
  let transactionType: components['schemas']['TransactionsDetailSmbc']['transactionType'];

  switch (remainder) {
    case 7:
      transactionType = '10';
      break;
    case 6:
      transactionType = '11';
      break;
    case 5:
      transactionType = '12';
      break;
    case 4:
      transactionType = '13';
      break;
    case 3:
      transactionType = '14';
      break;
    case 2:
      transactionType = '18';
      break;
    case 1:
      transactionType = '19';
      break;
    default:
      transactionType = '31';
      break;
  }
  return transactionType;
}

function transactionTypeNameJudge(
  number: number
): components['schemas']['TransactionsDetailSmbc']['transactionTypeName'] {
  const remainder = number % 8;
  let transactionTypeName: components['schemas']['TransactionsDetailSmbc']['transactionTypeName'];

  switch (remainder) {
    case 7:
      transactionTypeName = '現金';
      break;
    case 6:
      transactionTypeName = '振込';
      break;
    case 5:
      transactionTypeName = '他店券入金';
      break;
    case 4:
      transactionTypeName = '交換（取立入金および交換払）';
      break;
    case 3:
      transactionTypeName = '振替';
      break;
    case 2:
      transactionTypeName = 'その他';
      break;
    case 1:
      transactionTypeName = '訂正';
      break;
    default:
      transactionTypeName = 'でんさい';
      break;
  }
  return transactionTypeName;
}

// 明細データ1
const createTransactionsDetailMock = () => {
  const transactionsDetailMock: components['schemas']['TransactionsDetail'][] = [];

  // eslint-disable-next-line no-plusplus
  for (let i = 100; i > 0; i--) {
    const createTransactionsDetailMockData: components['schemas']['TransactionsDetail'] = {
      //   inquiryNumber: null,
      //   transactionDateJapaneseCalendar: null,
      //   transactionDateAd: null,
      //   valueDateJapaneseCalendar: null,
      //   valueDateAd: null,
      //   depositCreditType: i % 2 === 0 ? '2' : '1',
      //   transactionType: null,
      //   transactionTypeName: null,
      //   amount: null,
      //   checksIssuedByOtherBanksAmount: null,
      //   exchangePresentationDateJapaneseCalendar: null,
      //   exchangePresentationDateAd: null,
      //   dishonoredReturnDateJapaneseCalendar: null,
      //   dishonoredReturnDateAd: null,
      //   // eslint-disable-next-line no-nested-ternary
      //   billAndCheckType: null,
      //   // eslint-disable-next-line no-nested-ternary
      //   billAndCheckTypeName: null,
      //   billAndCheckNumber: null,
      //   branchCode: null,
      //   remitterCode: null,
      //   remitterNameContractorNumber: null,
      //   remittingBankName: null,
      //   remittingBankBranchName: null,
      //   abstract: null,
      //   ediInfo: null,
      //   ediKey: null,
      inquiryNumber: i.toString().padStart(8, '0'),
      transactionDateJapaneseCalendar: formatJapaneseCalendarDate(i),
      transactionDateAd: new Date(formatCalendarDate(i)).toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: formatJapaneseCalendarDate(i),
      valueDateAd: new Date(formatCalendarDate(i)).toLocaleDateString('ja-JP'),
      depositCreditType: i % 2 === 0 ? '2' : '1',
      transactionType: '13',
      transactionTypeName: '交換（取立入金および交換払）',
      //   amount: ************,
      amount: i * 1000,
      checksIssuedByOtherBanksAmount: 50000,
      exchangePresentationDateJapaneseCalendar: null,
      exchangePresentationDateAd: null,
      dishonoredReturnDateJapaneseCalendar: null,
      dishonoredReturnDateAd: null,
      // eslint-disable-next-line no-nested-ternary
      billAndCheckType: null,
      // eslint-disable-next-line no-nested-ternary
      billAndCheckTypeName: '約束手形',
      billAndCheckNumber: '1234567',
      branchCode: null,
      remitterCode: null,
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞABCABCｼｮｳｼﾞABCABCｼｮｳｼﾞABC',
      remittingBankName: null,
      remittingBankBranchName: null,
      abstract: null,
      ediInfo: 'ﾌﾘｺﾐ',
      ediKey: null
    };
    transactionsDetailMock.push(createTransactionsDetailMockData);
  }
  return transactionsDetailMock;
};

// 明細データ2 (SMBCのみ設定)
const createTransactionsDetailSmbcMock = () => {
  const transactionsDetailSmbcMock: components['schemas']['TransactionsDetailSmbc'][] = [];

  // eslint-disable-next-line no-plusplus
  for (let i = 8; i > 0; i--) {
    const createTransactionsDetailSmbcMockData: components['schemas']['TransactionsDetailSmbc'] = {
      identificationNumber: i.toString().padStart(8, '0'),
      transactionDateJapaneseCalendar: formatJapaneseCalendarDate(i),
      transactionDateAd: new Date(formatCalendarDate(i)).toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: formatJapaneseCalendarDate(i),
      valueDateAd: new Date(formatCalendarDate(i)).toLocaleDateString('ja-JP'),
      depositCreditType: i % 2 === 0 ? '2' : '1',
      transactionType: transactionTypeJudge(i),
      transactionTypeName: transactionTypeNameJudge(i),
      amount: i * 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      // eslint-disable-next-line no-nested-ternary
      billAndCheckType: i % 3 === 0 ? '1' : i % 2 === 0 ? '2' : '3',
      // eslint-disable-next-line no-nested-ternary
      billAndCheckTypeName: i % 3 === 0 ? '小切手' : i % 2 === 0 ? '約束手形' : '為替手形',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    };
    transactionsDetailSmbcMock.push(createTransactionsDetailSmbcMockData);
  }
  return transactionsDetailSmbcMock;
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsMockData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2024/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '01',
  // accountTypeCode: '05',
  accountType: '普通',
  // accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  // transactions1: [],
  transactions1: [
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    }
  ],
  transactions2: [],
  // transactions2: [
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-01',
  //     transactionDateAd: new Date('2024-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-01',
  //     valueDateAd: new Date('2024-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '交換（取立入金および交換払）',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2024-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2024-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2024-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2024/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     abstract: 'ﾌﾘｺﾐ',
  //     // abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-02',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-02',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date().toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date().toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-06-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2024-06-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-06-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2024-06-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2023-02-08').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2023-02-08').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 80000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   }
  // ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsJanuaryData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-01-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-01-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: ***************,
      checksIssuedByOtherBanksAmount: ***************,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-01-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-01-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-01-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-01-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-01-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-01-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-01-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-01-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-01-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-01-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-01-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-01-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-01-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsFebraryData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-02-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-02-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-02-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-02-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-02-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-02-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-02-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-02-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-02-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-02-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-02-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-02-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-02-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-02-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-02-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsMarchData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-03-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-03-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-03-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-03-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-03-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-03-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-03-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-03-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-03-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-03-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-03-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-03-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-03-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-03-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-03-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-03-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};
// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsAprilData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-04-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-04-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-04-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-04-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-04-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-04-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-04-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-04-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-04-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-04-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-04-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-04-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-04-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-04-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-04-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-04-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsMayData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-05-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-05-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-05-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-05-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-05-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-05-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-05-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-05-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-05-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-05-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-05-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-05-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-05-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-05-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-05-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-05-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};
// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsJuneData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-06-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-06-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-06-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-06-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-06-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-06-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-06-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-06-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-06-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-06-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-06-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-06-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-06-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-06-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-06-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-06-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};
// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsJulyData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-07-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-07-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-07-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-07-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-07-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-07-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-07-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-07-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-07-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-07-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-07-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-07-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-07-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-07-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-07-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-07-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsNovemberData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-01',
      transactionDateAd: new Date('2024-11-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-01',
      valueDateAd: new Date('2024-11-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-02',
      transactionDateAd: new Date('2024-11-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-02',
      valueDateAd: new Date('2024-11-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-11-15').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-11-15').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-11-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-11-17').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-11-20').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-11-21').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-11-16').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2024-11-16').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2024-11-18').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-11-07').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-09-03',
      transactionDateAd: new Date('2023-11-08').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-09-03',
      valueDateAd: new Date('2023-11-08').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 80000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通の時、transaction1にデータあり
// 通知預金の時、transaction2にデータあり
export const transactionsDecemberData: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  // baseDate: null,
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  // baseTime: null,
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '01',
  // accountTypeCode: '05',
  accountType: '普通',
  // accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  // transactions1: [],
  transactions1: [
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-09-30').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '交換（取立入金および交換払）',
      amount: ***************67,
      checksIssuedByOtherBanksAmount: ***************67,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-03').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 30000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 40000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-04').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 50000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-05').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 60000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-06').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    },
    {
      inquiryNumber: '********',
      transactionDateJapaneseCalendar: '29-01-01',
      transactionDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '29-01-01',
      valueDateAd: new Date('2023-02-07').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 70000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '29-01-01',
      exchangePresentationDateAd: '2017-01-01',
      dishonoredReturnDateJapaneseCalendar: '29-01-01',
      dishonoredReturnDateAd: '2017-01-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      remitterCode: 'abcdefgHIJ',
      remitterNameContractorNumber: 'ABCｼｮｳｼﾞ',
      remittingBankName: '1234ｷﾞﾝｺｳ',
      remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
      abstract: 'ﾌﾘｺﾐ',
      ediInfo: '01209315034789264425',
      ediKey: 'ﾞﾞﾟ*****************'
    }
  ],
  transactions2: [],
  // transactions2: [
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-01',
  //     transactionDateAd: new Date('2024-12-30').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-01',
  //     valueDateAd: new Date('2024-12-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '交換（取立入金および交換払）',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-08-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-08-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     abstract: 'ﾌﾘｺﾐ',
  //     // abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-02',
  //     transactionDateAd: new Date('2024-12-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-02',
  //     valueDateAd: new Date('2024-12-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-08-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-08-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-12-15').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2024-12-15').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-12-16').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2024-12-17').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 40000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-12-20').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2024-12-21').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 50000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-12-16').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2024-12-16').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 60000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2024-12-18').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2023-12-07').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 70000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   },
  //   {
  //     identificationNumber: '********',
  //     transactionDateJapaneseCalendar: '05-09-03',
  //     transactionDateAd: new Date('2023-12-08').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '05-09-03',
  //     valueDateAd: new Date('2023-12-08').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 80000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '05-07-01',
  //     exchangePresentationDateAd: '2023-07-01',
  //     dishonoredReturnDateJapaneseCalendar: '05-07-01',
  //     dishonoredReturnDateAd: '2023-07-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     originalDepositDateJapaneseCalendar: '05-07-01',
  //     originalDepositDateAd: '2023-07-01',
  //     interestRate: '020000',
  //     maturityDateJapaneseCalendar: '05-07-01',
  //     maturityDateAd: '2023/7/1',
  //     period1: '9010131',
  //     periodInterest: 5000,
  //     interimPaymentInterestRate: '015000',
  //     interimPaymentType: '1',
  //     periodAfterTerm: '0100',
  //     interestRateAfterTerm: '017000',
  //     interestAfterTerm: 7000,
  //     totalInterest: 12000,
  //     taxType: '1',
  //     taxTypeName: '総合課金',
  //     taxRate: '1000',
  //     taxAmount: 1200,
  //     aftertaxInterest: 10800,
  //     //   abstract: 'ﾌﾘｺﾐ',
  //     abstract: null,
  //     period2: '29112',
  //     periodInterestPositiveAndNegativeDisplay: '1'
  //   }
  // ],
  //   transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};
// 入出金明細情報
// 普通 8月分の時、transaction1にデータあり
// 通知預金 8月分の時、transaction2にデータあり
export const transactionsMockDataAugust: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  //   baseDate: "2017-01-01",
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  //   baseTime: "12:20:30+09:00",
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  // transactions1: createTransactionsDetailMock(),
  // transactions2: [],
  transactions1: [],
  transactions2: [],
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通 9月分の時、transaction1にデータあり
// 通知預金 9月分の時、transaction2にデータあり
export const transactionsMockDataSeptember: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  //   baseDate: "2017-01-01",
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  //   baseTime: "12:20:30+09:00",
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-09-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ｷｶﾝｼﾃｲﾃｽﾄ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-02').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-09-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ｷｶﾝｼﾃｲﾃｽﾄ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-09-03').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-09-03').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 30000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ｷｶﾝｼﾃｲﾃｽﾄ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  // transactions2: createTransactionsDetailSmbcMock(),
  transactions2: [
    // {
    //   identificationNumber: '********',
    //   transactionDateJapaneseCalendar: '05-09-01',
    //   transactionDateAd: new Date('2023-09-01').toLocaleDateString('ja-JP'),
    //   valueDateJapaneseCalendar: '05-09-01',
    //   valueDateAd: new Date('2023-09-01').toLocaleDateString('ja-JP'),
    //   depositCreditType: '1',
    //   transactionType: '12',
    //   transactionTypeName: '他店券入金',
    //   amount: 10000,
    //   checksIssuedByOtherBanksAmount: 10000,
    //   exchangePresentationDateJapaneseCalendar: '05-07-01',
    //   exchangePresentationDateAd: '2023-07-01',
    //   dishonoredReturnDateJapaneseCalendar: '05-07-01',
    //   dishonoredReturnDateAd: '2023-07-01',
    //   billAndCheckType: '1',
    //   billAndCheckTypeName: '小切手',
    //   billAndCheckNumber: '1234567',
    //   branchCode: '123',
    //   originalDepositDateJapaneseCalendar: '05-07-01',
    //   originalDepositDateAd: '2023-07-01',
    //   interestRate: '020000',
    //   maturityDateJapaneseCalendar: '05-07-01',
    //   maturityDateAd: '2023/7/1',
    //   period1: '9010131',
    //   periodInterest: 5000,
    //   interimPaymentInterestRate: '015000',
    //   interimPaymentType: '1',
    //   periodAfterTerm: '0100',
    //   interestRateAfterTerm: '017000',
    //   interestAfterTerm: 7000,
    //   totalInterest: 12000,
    //   taxType: '1',
    //   taxTypeName: '総合課金',
    //   taxRate: '1000',
    //   taxAmount: 1200,
    //   aftertaxInterest: 10800,
    //   abstract: 'ﾌﾘｺﾐ',
    //   // abstract: null,
    //   period2: '29112',
    //   periodInterestPositiveAndNegativeDisplay: '1'
    // },
    // {
    //   identificationNumber: '********',
    //   transactionDateJapaneseCalendar: '05-09-02',
    //   transactionDateAd: new Date('2023-09-02').toLocaleDateString('ja-JP'),
    //   valueDateJapaneseCalendar: '05-09-02',
    //   valueDateAd: new Date('2023-09-02').toLocaleDateString('ja-JP'),
    //   depositCreditType: '2',
    //   transactionType: '12',
    //   transactionTypeName: '他店券入金',
    //   amount: 20000,
    //   checksIssuedByOtherBanksAmount: 10000,
    //   exchangePresentationDateJapaneseCalendar: '05-07-01',
    //   exchangePresentationDateAd: '2023-07-01',
    //   dishonoredReturnDateJapaneseCalendar: '05-07-01',
    //   dishonoredReturnDateAd: '2023-07-01',
    //   billAndCheckType: '1',
    //   billAndCheckTypeName: '小切手',
    //   billAndCheckNumber: '1234567',
    //   branchCode: '123',
    //   originalDepositDateJapaneseCalendar: '05-07-01',
    //   originalDepositDateAd: '2023-07-01',
    //   interestRate: '020000',
    //   maturityDateJapaneseCalendar: '05-07-01',
    //   maturityDateAd: '2023/7/1',
    //   period1: '9010131',
    //   periodInterest: 5000,
    //   interimPaymentInterestRate: '015000',
    //   interimPaymentType: '1',
    //   periodAfterTerm: '0100',
    //   interestRateAfterTerm: '017000',
    //   interestAfterTerm: 7000,
    //   totalInterest: 12000,
    //   taxType: '1',
    //   taxTypeName: '総合課金',
    //   taxRate: '1000',
    //   taxAmount: 1200,
    //   aftertaxInterest: 10800,
    //   //   abstract: 'ﾌﾘｺﾐ',
    //   abstract: null,
    //   period2: '29112',
    //   periodInterestPositiveAndNegativeDisplay: '1'
    // },
    // {
    //   identificationNumber: '********',
    //   transactionDateJapaneseCalendar: '05-09-03',
    //   transactionDateAd: new Date('2023-09-03').toLocaleDateString('ja-JP'),
    //   valueDateJapaneseCalendar: '05-09-03',
    //   valueDateAd: new Date('2023-09-03').toLocaleDateString('ja-JP'),
    //   depositCreditType: '1',
    //   transactionType: '12',
    //   transactionTypeName: '他店券入金',
    //   amount: 30000,
    //   checksIssuedByOtherBanksAmount: 10000,
    //   exchangePresentationDateJapaneseCalendar: '05-07-01',
    //   exchangePresentationDateAd: '2023-07-01',
    //   dishonoredReturnDateJapaneseCalendar: '05-07-01',
    //   dishonoredReturnDateAd: '2023-07-01',
    //   billAndCheckType: '1',
    //   billAndCheckTypeName: '小切手',
    //   billAndCheckNumber: '1234567',
    //   branchCode: '123',
    //   originalDepositDateJapaneseCalendar: '05-07-01',
    //   originalDepositDateAd: '2023-07-01',
    //   interestRate: '020000',
    //   maturityDateJapaneseCalendar: '05-07-01',
    //   maturityDateAd: '2023/7/1',
    //   period1: '9010131',
    //   periodInterest: 5000,
    //   interimPaymentInterestRate: '015000',
    //   interimPaymentType: '1',
    //   periodAfterTerm: '0100',
    //   interestRateAfterTerm: '017000',
    //   interestAfterTerm: 7000,
    //   totalInterest: 12000,
    //   taxType: '1',
    //   taxTypeName: '総合課金',
    //   taxRate: '1000',
    //   taxAmount: 1200,
    //   aftertaxInterest: 10800,
    //   //   abstract: 'ﾌﾘｺﾐ',
    //   abstract: null,
    //   period2: '29112',
    //   periodInterestPositiveAndNegativeDisplay: '1'
    // }
  ],
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 入出金明細情報
// 普通 10月分の時、transaction1にデータあり
// 通知預金 10月分の時、transaction2にデータあり
export const transactionsMockDataOctober: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  //   baseDate: "2017-01-01",
  baseTime: new Date('2023-10-10T08:00:00').toLocaleTimeString('ja-JP', options),
  //   baseTime: "12:20:30+09:00",
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  // accountTypeCode: '01',
  accountTypeCode: '05',
  // accountType: '普通',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  //   transactions1: createTransactionsDetailMock(),
  transactions1: [],
  // transactions1: [
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
  //     depositCreditType: '1',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 10000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ｷｶﾝｼﾃｲﾃｽﾄ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   },
  //   {
  //     inquiryNumber: '********',
  //     transactionDateJapaneseCalendar: '29-01-01',
  //     transactionDateAd: new Date('2023-10-02').toLocaleDateString('ja-JP'),
  //     valueDateJapaneseCalendar: '29-01-01',
  //     valueDateAd: new Date('2023-10-02').toLocaleDateString('ja-JP'),
  //     depositCreditType: '2',
  //     transactionType: '12',
  //     transactionTypeName: '他店券入金',
  //     amount: 20000,
  //     checksIssuedByOtherBanksAmount: 10000,
  //     exchangePresentationDateJapaneseCalendar: '29-01-01',
  //     exchangePresentationDateAd: '2017-01-01',
  //     dishonoredReturnDateJapaneseCalendar: '29-01-01',
  //     dishonoredReturnDateAd: '2017-01-01',
  //     billAndCheckType: '1',
  //     billAndCheckTypeName: '小切手',
  //     billAndCheckNumber: '1234567',
  //     branchCode: '123',
  //     remitterCode: 'abcdefgHIJ',
  //     remitterNameContractorNumber: 'ｷｶﾝｼﾃｲﾃｽﾄ',
  //     remittingBankName: '1234ｷﾞﾝｺｳ',
  //     remittingBankBranchName: 'ﾄｳｷｮｳｼﾃﾝ',
  //     abstract: 'ﾌﾘｺﾐ',
  //     ediInfo: '01209315034789264425',
  //     ediKey: 'ﾞﾞﾟ*****************'
  //   }
  // ],
  // transactions2: [],
  // transactions2: createTransactionsDetailSmbcMock(),
  transactions2: [
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-10-01',
      transactionDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-10-01',
      valueDateAd: new Date('2023-10-01').toLocaleDateString('ja-JP'),
      depositCreditType: '1',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 10000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      abstract: 'ﾌﾘｺﾐ',
      // abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    },
    {
      identificationNumber: '********',
      transactionDateJapaneseCalendar: '05-10-02',
      transactionDateAd: new Date('2023-10-02').toLocaleDateString('ja-JP'),
      valueDateJapaneseCalendar: '05-10-02',
      valueDateAd: new Date('2023-10-02').toLocaleDateString('ja-JP'),
      depositCreditType: '2',
      transactionType: '12',
      transactionTypeName: '他店券入金',
      amount: 20000,
      checksIssuedByOtherBanksAmount: 10000,
      exchangePresentationDateJapaneseCalendar: '05-07-01',
      exchangePresentationDateAd: '2023-07-01',
      dishonoredReturnDateJapaneseCalendar: '05-07-01',
      dishonoredReturnDateAd: '2023-07-01',
      billAndCheckType: '1',
      billAndCheckTypeName: '小切手',
      billAndCheckNumber: '1234567',
      branchCode: '123',
      originalDepositDateJapaneseCalendar: '05-07-01',
      originalDepositDateAd: '2023-07-01',
      interestRate: '020000',
      maturityDateJapaneseCalendar: '05-07-01',
      maturityDateAd: '2023/7/1',
      period1: '9010131',
      periodInterest: 5000,
      interimPaymentInterestRate: '015000',
      interimPaymentType: '1',
      periodAfterTerm: '0100',
      interestRateAfterTerm: '017000',
      interestAfterTerm: 7000,
      totalInterest: 12000,
      taxType: '1',
      taxTypeName: '総合課金',
      taxRate: '1000',
      taxAmount: 1200,
      aftertaxInterest: 10800,
      //   abstract: 'ﾌﾘｺﾐ',
      abstract: null,
      period2: '29112',
      periodInterestPositiveAndNegativeDisplay: '1'
    }
  ],
  depositCount: 7,
  //   totalDepositAmount: ***************,
  totalDepositAmount: 21100,
  withdrawalCount: 3,
  //   totalWithdrawalAmount: ***************,
  totalWithdrawalAmount: 15200,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 8,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 当座
export const transactionsMockData2: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '02',
  accountType: '当座',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: createTransactionsDetailMock(),
  transactions2: [],
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 納税準備預金
export const transactionsMockData3: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '03',
  accountType: '納税準備預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: [],
  transactions2: [],
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 貯蓄預金
export const transactionsMockData4: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '04',
  accountType: '貯蓄預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: createTransactionsDetailMock(),
  transactions2: [],
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 通知預金
export const transactionsMockData5: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '05',
  accountType: '通知預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: [],
  transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 定期預金
export const transactionsMockData6: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '06',
  accountType: '定期預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: [],
  transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 積立定期預金
export const transactionsMockData7: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '07',
  accountType: '積立定期預金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: [],
  transactions2: createTransactionsDetailSmbcMock(),
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// 定期積金
export const transactionsMockData8: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '08',
  accountType: '定期積金',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: [],
  transactions2: [],
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// その他
export const transactionsMockData9: components['schemas']['Transactions'] = {
  inquiryResult: 'inquiryComplete',
  categoryCode: '03',
  createdDateJapaneseCalendar: '05-07-01',
  createdDateAd: '2023-07-01',
  baseDate: nowDate().toLocaleDateString('ja-JP'),
  baseTime: nowDate().toLocaleTimeString('ja-JP', options),
  transactionDateFromJapaneseCalendar: '05/07/01',
  transactionDateFromAd: '2023/07/01',
  transactionDateToJapaneseCalendar: '29/01/31',
  transactionDateToAd: '2017/01/31',
  bankCode: '1234',
  bankNameKana: '1234ｷﾞﾝｺｳ',
  branchCode: '123',
  branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
  accountTypeCode: '09',
  accountType: 'その他',
  accountNumber: '**********',
  accountId: '*************',
  accountName: '123ｼﾖｳｶｲ',
  overdraftTypeHeader: '1',
  passbookOrCertificateType: '1',
  balanceBeforeTransaction: ***********,
  transactions1: [],
  transactions2: [],
  depositCount: 7,
  totalDepositAmount: ***************,
  withdrawalCount: 3,
  totalWithdrawalAmount: ***************,
  overdraftTypeTrailer: '1',
  transactionBalance: 100000,
  transactionDataCount: 6,
  totalCount: 5,
  count: 1,
  hasNext: false,
  itemKey: '20000',
  sessionInfo: 'BZDBIB112150951480831001',
  currencyCode: 'JPY'
};

// transactionsDailyTotalsのモックデータ
export const transactionsDailyTotalsMockData: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-02',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-03',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-04',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-05',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-06',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-07',
      totalDeposit: ********,
      totalWithdrawal: ********
    }
  ]
};

// transactionsDailyTotalsのモックデータ：全部ゼロ
export const transactionsDailyTotalsMockData1: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 0,
      totalWithdrawal: 0
    },
    {
      date: '2023-01-02',
      totalDeposit: 0,
      totalWithdrawal: 0
    },
    {
      date: '2023-01-03',
      totalDeposit: 0,
      totalWithdrawal: 0
    },
    {
      date: '2023-01-04',
      totalDeposit: 0,
      totalWithdrawal: 0
    },
    {
      date: '2023-01-05',
      totalDeposit: 0,
      totalWithdrawal: 0
    },
    {
      date: '2023-01-06',
      totalDeposit: 0,
      totalWithdrawal: 0
    },
    {
      date: '2023-01-07',
      totalDeposit: 0,
      totalWithdrawal: 0
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で4
export const transactionsDailyTotalsMockData2: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 1,
      totalWithdrawal: 4
    },
    {
      date: '2023-01-02',
      totalDeposit: 2,
      totalWithdrawal: 3
    },
    {
      date: '2023-01-03',
      totalDeposit: 3,
      totalWithdrawal: 2
    },
    {
      date: '2023-01-04',
      totalDeposit: 4,
      totalWithdrawal: 1
    },
    {
      date: '2023-01-05',
      totalDeposit: 3,
      totalWithdrawal: 2
    },
    {
      date: '2023-01-06',
      totalDeposit: 2,
      totalWithdrawal: 3
    },
    {
      date: '2023-01-07',
      totalDeposit: 1,
      totalWithdrawal: 4
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で5
export const transactionsDailyTotalsMockData3: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 1,
      totalWithdrawal: 5
    },
    {
      date: '2023-01-02',
      totalDeposit: 2,
      totalWithdrawal: 4
    },
    {
      date: '2023-01-03',
      totalDeposit: 3,
      totalWithdrawal: 3
    },
    {
      date: '2023-01-04',
      totalDeposit: 4,
      totalWithdrawal: 2
    },
    {
      date: '2023-01-05',
      totalDeposit: 3,
      totalWithdrawal: 1
    },
    {
      date: '2023-01-06',
      totalDeposit: 2,
      totalWithdrawal: 2
    },
    {
      date: '2023-01-07',
      totalDeposit: 1,
      totalWithdrawal: 3
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で49
export const transactionsDailyTotalsMockData4: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 10,
      totalWithdrawal: 49
    },
    {
      date: '2023-01-02',
      totalDeposit: 20,
      totalWithdrawal: 40
    },
    {
      date: '2023-01-03',
      totalDeposit: 30,
      totalWithdrawal: 30
    },
    {
      date: '2023-01-04',
      totalDeposit: 40,
      totalWithdrawal: 20
    },
    {
      date: '2023-01-05',
      totalDeposit: 30,
      totalWithdrawal: 10
    },
    {
      date: '2023-01-06',
      totalDeposit: 20,
      totalWithdrawal: 20
    },
    {
      date: '2023-01-07',
      totalDeposit: 10,
      totalWithdrawal: 30
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で50
export const transactionsDailyTotalsMockData5: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 10,
      totalWithdrawal: 50
    },
    {
      date: '2023-01-02',
      totalDeposit: 20,
      totalWithdrawal: 40
    },
    {
      date: '2023-01-03',
      totalDeposit: 30,
      totalWithdrawal: 30
    },
    {
      date: '2023-01-04',
      totalDeposit: 40,
      totalWithdrawal: 20
    },
    {
      date: '2023-01-05',
      totalDeposit: 30,
      totalWithdrawal: 10
    },
    {
      date: '2023-01-06',
      totalDeposit: 20,
      totalWithdrawal: 20
    },
    {
      date: '2023-01-07',
      totalDeposit: 10,
      totalWithdrawal: 30
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で499
export const transactionsDailyTotalsMockData6: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 100,
      totalWithdrawal: 499
    },
    {
      date: '2023-01-02',
      totalDeposit: 200,
      totalWithdrawal: 400
    },
    {
      date: '2023-01-03',
      totalDeposit: 300,
      totalWithdrawal: 300
    },
    {
      date: '2023-01-04',
      totalDeposit: 400,
      totalWithdrawal: 200
    },
    {
      date: '2023-01-05',
      totalDeposit: 300,
      totalWithdrawal: 100
    },
    {
      date: '2023-01-06',
      totalDeposit: 200,
      totalWithdrawal: 200
    },
    {
      date: '2023-01-07',
      totalDeposit: 100,
      totalWithdrawal: 300
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で500
export const transactionsDailyTotalsMockData7: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 100,
      totalWithdrawal: 500
    },
    {
      date: '2023-01-02',
      totalDeposit: 200,
      totalWithdrawal: 400
    },
    {
      date: '2023-01-03',
      totalDeposit: 300,
      totalWithdrawal: 300
    },
    {
      date: '2023-01-04',
      totalDeposit: 400,
      totalWithdrawal: 200
    },
    {
      date: '2023-01-05',
      totalDeposit: 300,
      totalWithdrawal: 100
    },
    {
      date: '2023-01-06',
      totalDeposit: 200,
      totalWithdrawal: 200
    },
    {
      date: '2023-01-07',
      totalDeposit: 100,
      totalWithdrawal: 300
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で4999
export const transactionsDailyTotalsMockData8: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 1000,
      totalWithdrawal: 4999
    },
    {
      date: '2023-01-02',
      totalDeposit: 2000,
      totalWithdrawal: 4000
    },
    {
      date: '2023-01-03',
      totalDeposit: 3000,
      totalWithdrawal: 3000
    },
    {
      date: '2023-01-04',
      totalDeposit: 4000,
      totalWithdrawal: 2000
    },
    {
      date: '2023-01-05',
      totalDeposit: 3000,
      totalWithdrawal: 1000
    },
    {
      date: '2023-01-06',
      totalDeposit: 2000,
      totalWithdrawal: 2000
    },
    {
      date: '2023-01-07',
      totalDeposit: 1000,
      totalWithdrawal: 3000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で5000
export const transactionsDailyTotalsMockData9: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 1000,
      totalWithdrawal: 5000
    },
    {
      date: '2023-01-02',
      totalDeposit: 2000,
      totalWithdrawal: 4000
    },
    {
      date: '2023-01-03',
      totalDeposit: 3000,
      totalWithdrawal: 3000
    },
    {
      date: '2023-01-04',
      totalDeposit: 4000,
      totalWithdrawal: 2000
    },
    {
      date: '2023-01-05',
      totalDeposit: 3000,
      totalWithdrawal: 1000
    },
    {
      date: '2023-01-06',
      totalDeposit: 2000,
      totalWithdrawal: 2000
    },
    {
      date: '2023-01-07',
      totalDeposit: 1000,
      totalWithdrawal: 3000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で499999
export const transactionsDailyTotalsMockData10: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: 100000,
      totalWithdrawal: 499999
    },
    {
      date: '2023-01-02',
      totalDeposit: 200000,
      totalWithdrawal: 400000
    },
    {
      date: '2023-01-03',
      totalDeposit: 300000,
      totalWithdrawal: 300000
    },
    {
      date: '2023-01-04',
      totalDeposit: 400000,
      totalWithdrawal: 200000
    },
    {
      date: '2023-01-05',
      totalDeposit: 300000,
      totalWithdrawal: 100000
    },
    {
      date: '2023-01-06',
      totalDeposit: 200000,
      totalWithdrawal: 200000
    },
    {
      date: '2023-01-07',
      totalDeposit: 100000,
      totalWithdrawal: 300000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で50000000
export const transactionsDailyTotalsMockData11: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: ********,
      totalWithdrawal: 50000000
    },
    {
      date: '2023-01-02',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-03',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-04',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-05',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-06',
      totalDeposit: ********,
      totalWithdrawal: ********
    },
    {
      date: '2023-01-07',
      totalDeposit: ********,
      totalWithdrawal: ********
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で********9999
export const transactionsDailyTotalsMockData12: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-11-29',
      totalDeposit: ************,
      totalWithdrawal: ********9999
    },
    {
      date: '2023-11-30',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-12-01',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-12-02',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-12-03',
      totalDeposit: ********0000,
      totalWithdrawal: ************
    },
    {
      date: '2023-12-04',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-12-05',
      totalDeposit: ************,
      totalWithdrawal: ********0000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で500000000000
export const transactionsDailyTotalsMockData13: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: ************,
      totalWithdrawal: 500000000000
    },
    {
      date: '2023-01-02',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-01-03',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-01-04',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-01-05',
      totalDeposit: ********0000,
      totalWithdrawal: ************
    },
    {
      date: '2023-01-06',
      totalDeposit: ********0000,
      totalWithdrawal: ********0000
    },
    {
      date: '2023-01-07',
      totalDeposit: ************,
      totalWithdrawal: ********0000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で********99999
export const transactionsDailyTotalsMockData14: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: ************0000,
      totalWithdrawal: ********99999999
    },
    {
      date: '2023-01-02',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-03',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-04',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-05',
      totalDeposit: ********00000000,
      totalWithdrawal: ************0000
    },
    {
      date: '2023-01-06',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-07',
      totalDeposit: ************0000,
      totalWithdrawal: ********00000000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で5000000000000
export const transactionsDailyTotalsMockData15: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: ************0000,
      totalWithdrawal: 5000000000000000
    },
    {
      date: '2023-01-02',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-03',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-04',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-05',
      totalDeposit: ********00000000,
      totalWithdrawal: ************0000
    },
    {
      date: '2023-01-06',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-07',
      totalDeposit: ************0000,
      totalWithdrawal: ********00000000
    }
  ]
};

// transactionsDailyTotalsのモックデータ：最大で9999999999999999
export const transactionsDailyTotalsMockData16: components['schemas']['TransactionsDailyTotals'] = {
  transactionsDailyTotals: [
    {
      date: '2023-01-01',
      totalDeposit: ************0000,
      totalWithdrawal: 9999999999999999
    },
    {
      date: '2023-01-02',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-03',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-04',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-05',
      totalDeposit: ********00000000,
      totalWithdrawal: ************0000
    },
    {
      date: '2023-01-06',
      totalDeposit: ********00000000,
      totalWithdrawal: ********00000000
    },
    {
      date: '2023-01-07',
      totalDeposit: ************0000,
      totalWithdrawal: ********00000000
    }
  ]
};
