/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { DATE_FORMAT_SLASH, serverDate, serverTime } from '../utils/dateFormat';
import { components } from '../schemas/schema';

// freee口座一覧情報
export const usedTotalAmountData1: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2023-12-12',
  endDate: '2024-01-11',
  totalExpense: 100000
};

// freee口座一覧情報
export const usedTotalAmountData2: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2024-03-12',
  endDate: '2024-04-11',
  totalExpense: 123456789
};

// freee口座一覧情報
export const usedTotalAmountData3: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2024-05-15',
  endDate: '2024-06-14',
  totalExpense: -12345678
};

// freee口座一覧情報
export const usedTotalAmountData4: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2024-05-15',
  endDate: '2024-06-14',
  totalExpense: 1234567890
};

// freee口座一覧情報
export const usedTotalAmountData5: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2024-05-15',
  endDate: '2024-06-14',
  totalExpense: -123456789
};

// freee口座一覧情報
export const usedTotalAmountData6: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2024-05-15',
  endDate: '2024-06-14',
  totalExpense: 123456789012345
};

// freee口座一覧情報
export const usedTotalAmountData7: components['schemas']['FreeeTransactionsTotalResponse'] = {
  startDate: '2024-05-15',
  endDate: '2024-06-14',
  totalExpense: -12345678901234
};

export const getFreeeTransactionsData = (stringMonth: string) => {
  const month = Number(stringMonth).toString();
  const transactionsData = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 30000,
        entrySide: 'income',
        description: '振込　カ）長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列長い文字列',
      },
      {
        id: 2,
        date: `2024/${month}/25`,
        amount: 30000,
        entrySide: 'expense',
        description: '振込　カ）ABC',
      },
      {
        id: 3,
        date: `2024/${month}/26`,
        amount: 322,
        entrySide: 'income',
        description: '振込　カ）ABC',
      },
      {
        id: 4,
        date: `2024/${month}/27`,
        amount: 300,
        entrySide: 'expense',
        description: '振込　カ）ABC',
      },
      {
        id: 5,
        date: `2024/${month}/28`,
        amount: 100,
        entrySide: 'income',
        description: '振込　カ）ABC',
      },
      {
        id: 6,
        date: `2024/${month}/29`,
        amount: 322,
        entrySide: 'income',
        description: '振込　カ）ABC',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  return transactionsData;
};

export const getFreeeTransactionsCreditData = (stringMonth: string, walletId: string) => {
  const month = Number(stringMonth).toString();
  // 5件表示
  const transactionsData1 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 30000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/25`,
        amount: 300000000000000,
        entrySide: 'expense',
        description: 'ファミリーマート大手町店',
      },
      {
        id: 3,
        date: `2024/${month}/26`,
        amount: 322,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 4,
        date: `2024/${month}/27`,
        amount: 300,
        entrySide: 'expense',
        description: 'ローソン大崎店',
      },
      {
        id: 5,
        date: `2024/${month}/28`,
        amount: 100,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 6,
        date: `2024/${month}/29`,
        amount: 322,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 絞り込み（利用日用）
  const transactionsData2 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/5`,
        amount: 30000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/25`,
        amount: 300000,
        entrySide: 'expense',
        description: 'ファミリーマート大手町店',
      },
      {
        id: 3,
        date: `2024/${month}/3`,
        amount: 200000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 4,
        date: `2024/${month}/28`,
        amount: 20000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 5,
        date: `2024/${month}/2`,
        amount: 100000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 6,
        date: `2024/${month}/29`,
        amount: 10000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 7,
        date: `2024/${month}/7`,
        amount: 60000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 8,
        date: `2024/${month}/29`,
        amount: 890000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 9,
        date: `2024/${month}/9`,
        amount: 290000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 10,
        date: `2024/${month}/20`,
        amount: 980000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 11,
        date: `2024/${month}/1`,
        amount: 20000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 12,
        date: `2024/${month}/30`,
        amount: 290000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 13,
        date: `2024/${month}/4`,
        amount: 30000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 14,
        date: `2024/${month}/30`,
        amount: 3000000,
        entrySide: 'expense',
        description: 'ファミリーマート大手町店',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 絞り込み（金額用)
  const transactionsData3 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 1,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/25`,
        amount: 20,
        entrySide: 'expense',
        description: 'ファミリーマート大手町店',
      },
      {
        id: 3,
        date: `2024/${month}/26`,
        amount: 300,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 4,
        date: `2024/${month}/27`,
        amount: 4000,
        entrySide: 'expense',
        description: 'ローソン大崎店',
      },
      {
        id: 5,
        date: `2024/${month}/28`,
        amount: 50000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 6,
        date: `2024/${month}/28`,
        amount: 600000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 7,
        date: `2024/${month}/28`,
        amount: 7000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 8,
        date: `2024/${month}/28`,
        amount: 80000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 9,
        date: `2024/${month}/28`,
        amount: 900000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 10,
        date: `2024/${month}/28`,
        amount: 1000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 11,
        date: `2024/${month}/28`,
        amount: 11000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 12,
        date: `2024/${month}/28`,
        amount: 120000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 13,
        date: `2024/${month}/28`,
        amount: 1300000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 14,
        date: `2024/${month}/28`,
        amount: 14000000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 15,
        date: `2024/${month}/28`,
        amount: 150000000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 桁数最小値最大値
  const transactionsData4 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 10000000000000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/25`,
        amount: 100000000000000,
        entrySide: 'expense',
        description: 'ファミリーマート大手町店',
      },
      {
        id: 3,
        date: `2024/${month}/26`,
        amount: 1,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 4,
        date: `2024/${month}/26`,
        amount: 1,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 明細情報0件
  const transactionsData5 = {
    transactions: [
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 合計0円
  const transactionsData6 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 30000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/25`,
        amount: 30000,
        entrySide: 'expense',
        description: 'ファミリーマート大手町店',
      },
      {
        id: 3,
        date: `2024/${month}/26`,
        amount: 322,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 4,
        date: `2024/${month}/27`,
        amount: 322,
        entrySide: 'expense',
        description: 'ローソン大崎店',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 桁数超過（16桁）
  const transactionsData7 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 1000000000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 桁数超過（- + 15桁）
  const transactionsData8 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 100000000000000,
        entrySide: 'income',
        description: 'AMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 1000件データ生成処理
  const list = [...Array(1000)].map((index: number) => ({
    id: index,
    date: `2024/${month}/24`,
    amount: 10000,
    entrySide: 'expense',
    description: 'AMAZON.CO.JP',
  }));

  // 1000件のデータ
  const transactionsData9 = {
    transactions: list,
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 半角と全角が混じってる and 小文字と大文字が混じってる
  const transactionsData10 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 100000000,
        entrySide: 'expense',
        description: 'AMAZONあ.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/24`,
        amount: 100000000,
        entrySide: 'expense',
        description: 'aAMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 同じ金額且つ別日付のものを複数用意する
  const transactionsData11 = {
    transactions: [
      {
        id: 1,
        date: `2024/${month}/24`,
        amount: 100000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 2,
        date: `2024/${month}/21`,
        amount: 100000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 3,
        date: `2024/${month}/24`,
        amount: 100000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 4,
        date: `2024/${month}/23`,
        amount: 100000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
      {
        id: 5,
        date: `2024/${month}/22`,
        amount: 100000000000,
        entrySide: 'expense',
        description: 'AMAZON.CO.JP',
      },
    ],
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
  };

  // 絞り込み（利用日用）2024年7月〜12月
  if (walletId === '2') {
    return transactionsData2;
  }

  // 絞り込み（金額用)
  if (walletId === '3') {
    return transactionsData3;
  }

  // 桁数最小値最大値
  if (walletId === '4') {
    return transactionsData4;
  }

  // 明細情報0件
  if (walletId === '5') {
    return transactionsData5;
  }

  // 合計0円
  if (walletId === '6') {
    return transactionsData6;
  }

  // 桁数超過（16桁）
  if (walletId === '7') {
    return transactionsData7;
  }

  // 桁数超過（- + 15桁）
  if (walletId === '8') {
    return transactionsData8;
  }

  // 1000件のデータ
  if (walletId === '9') {
    return transactionsData9;
  }

  // 半角と全角が混じってる and 小文字と大文字が混じってる
  if (walletId === '10') {
    return transactionsData10;
  }

  // 同じ金額且つ別日付のものを複数用意する
  if (walletId === '11') {
    return transactionsData11;
  }

  return transactionsData1;
};
