/* eslint-disable @typescript-eslint/no-unused-vars */
import { components } from '../schemas/schema';
import { DATE_FORMAT_SLASH, serverDate } from '../utils/dateFormat';

/*
  指定口座残高取得API実行時にモバイルアプリ側へ返却するモックデータを定義する
*/

// 残高情報1
const balanceDetailData1: components['schemas']['BalanceDetail'] = {
  contactName: 'ｴｽｴﾑﾋﾞｰｼｰｰｰｴｽｴﾑﾋﾞｰｼｰｰｰｴｽｴﾑﾋﾞｰｼｰｰｰｴｽｴﾑﾋﾞｰｼｰｰｰ',
  accountId: '*************',
  branchName: '東京中央支店',
  branchCode: '015',
  accountType: '普通',
  accountNumber: '0000001',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: 1000000,
  checksIssuedByOtherBanks: 20000,
  overdraftLimit: 300000,
  withdrawableBalance: 10000,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ1',
  displayAccountName: '振込用口座1',
  displayAccountFlag: true
};

// 残高情報2
const balanceDetailData2: components['schemas']['BalanceDetail'] = {
  contactName: 'ﾃﾞｨｰﾃｨｰﾋﾟｰ',
  accountId: '*************',
  branchName: '大阪支店',
  branchCode: '101',
  accountType: '当座',
  accountNumber: '0000002',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: 200000,
  checksIssuedByOtherBanks: 10000,
  overdraftLimit: 3000,
  withdrawableBalance: 100000,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ2',
  displayAccountName: '',
  displayAccountFlag: true
};

// 残高情報3
const balanceDetailData3: components['schemas']['BalanceDetail'] = {
  contactName: 'ﾃﾞｨｰﾃｨｰﾋﾟｰ',
  accountId: '*************',
  branchName:
    '東京ディズニーランド',
  branchCode: '593',
  accountType: '通知',
  accountNumber: '0000003',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: -30000,
  checksIssuedByOtherBanks: *****************,
  overdraftLimit: *****************,
  withdrawableBalance: *****************,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ3',
  displayAccountName: '振込用口座3',
  displayAccountFlag: true
};

// 残高情報4
const balanceDetailData4: components['schemas']['BalanceDetail'] = {
  contactName: 'ﾃﾞｨｰﾃｨｰﾋﾟｰ',
  accountId: '*************',
  branchName: '名古屋支店',
  branchCode: '481',
  accountType: '定期',
  accountNumber: '0000004',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: -4000000,
  checksIssuedByOtherBanks: 35000,
  overdraftLimit: 125000,
  withdrawableBalance: 900,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ4',
  displayAccountName: '',
  displayAccountFlag: true
};

// 残高情報5
const balanceDetailData5: components['schemas']['BalanceDetail'] = {
  contactName: 'ﾃﾞｨｰﾃｨｰﾋﾟｰ',
  accountId: '*************',
  branchName: '福岡支店',
  branchCode: '701',
  accountType: '積立',
  accountNumber: '0000005',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: 8000,
  checksIssuedByOtherBanks: 35000,
  overdraftLimit: 125000,
  withdrawableBalance: 9000,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ5',
  displayAccountName: '振込用口座5',
  displayAccountFlag: true
};

// 残高情報6
const balanceDetailData6: components['schemas']['BalanceDetail'] = {
  contactName: 'ﾃﾞｨｰﾃｨｰﾋﾟｰ',
  accountId: '*************',
  branchName: '厚木支店',
  branchCode: '595',
  accountType: '普通',
  accountNumber: '0000006',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: 600000,
  checksIssuedByOtherBanks: 350000,
  overdraftLimit: 12500,
  withdrawableBalance: 7000,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ6',
  displayAccountName: '',
  displayAccountFlag: true
};

// 残高情報7
const balanceDetailData7: components['schemas']['BalanceDetail'] = {
  contactName: 'ﾃﾞｨｰﾃｨｰﾋﾟｰ',
  accountId: '*************',
  branchName: '新百合ヶ丘支店',
  branchCode: '360',
  accountType: '普通',
  accountNumber: '0000007',
  baseDate: '2024/10/24',
  baseTime: '12:00',
  currentBalance: 700000,
  checksIssuedByOtherBanks: 350000,
  overdraftLimit: 12500,
  withdrawableBalance: 7000,
  bankName: '三井住友銀行',
  remitterName: 'ｲﾀｸｼｬﾒｲ7',
  displayAccountName: '',
  displayAccountFlag: true
};

// 口座残高情報1
export const balanceData1: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData1],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// 口座残高情報2
export const balanceData2: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData2],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// 口座残高情報3
export const balanceData3: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData3],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// 口座残高情報4
export const balanceData4: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData4],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// 口座残高情報5
export const balanceData5: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData5],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// 口座残高情報6
export const balanceData6: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData6],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};

// 口座残高情報7
export const balanceData7: components['schemas']['Balance'] = {
  count: 1,
  accounts: [balanceDetailData7],
  serverDate: serverDate(DATE_FORMAT_SLASH)
};
