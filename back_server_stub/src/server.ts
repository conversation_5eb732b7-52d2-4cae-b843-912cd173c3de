import express, { Request, Response, NextFunction } from 'express';
import { CustomError } from './errors/customError';
import router from './routes/router';
import errorHandler from './middlewares/errorHandler';
import { DATE_FORMAT_SLASH, serverDate, serverTime } from './utils/dateFormat';
import { NotFoundError } from './errors/httpErrors';
import { errorCodes } from './errors/errorCodes';

const app: express.Express = express();

// Expressが生成するX-Powered-Byヘッダを削除
app.disable('x-powered-by');

app.use(express.static('public'));

// body-parserに基づいた着信リクエストの解析
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use((req, res, next) => {
  // ヘッダーにbaseDateとbaseTimeを付与
  res.setHeader('X-Base-Date', serverDate(DATE_FORMAT_SLASH));
  res.setHeader('X-Base-Time', serverTime());
  // 基盤系エラー
  // res.setHeader('sorryscreen', '');
  next();
});

// 認可完了API
// 認可確認を打鍵するときに必要
// フロント側でリダイレクト時のBASE URLを確認しているため、環境ごとに用意
// ただし、stateが異なりますのエラーが表示される（state自体はモバイルで発行しているものの、発行してDynamoDBに保存したstateを取得する方法がない。auth_dummy.htmlにstateを送ることができれば疎通できる）
// 本来はLambdaを通ることで実施しているが、auth_dummy.htmlを通っているため上手くいかない
app.get('/mobile/api/bank/auth', (req, res, next) => {
  const stateValue = req.query.state as string;

  // DEV環境用
  res.redirect(
    // 'http://dtp-dv-mobile-bff-backend-stub-dtp-dv-mobile-bff.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/mobile/api/bank/auth?code=cb46420e53c24580a4c4e0fe8f888888&state=021mhugbFEtLzz5CXQSsPfgBavI7MFt6QRH4LgTT7ennwnXo'
    'http://dtp-dv-mobile-bff-backend-stub-dtp-dv-mobile-bff.apps.dtp-dv-os-cl-01.8n10.p1.openshiftapps.com/mobile/api/bank/auth?error=access_denied&error_description=authorization+processing+was+stopped+by+user.+%5B739%5D&state=02fJes86bH1Y4vOiIiwcbdgxuXvzWwRGpr1lfTwU3MDUnqMC<'
  );
  // TEST環境用
  // TEST環境用
  // res.redirect(
  //   `https://test.biztest.smbc.co.jp/mobile/api/bank/auth?code=cb46420e53c24580a4c4e0fe8f888888&state=${stateValue}`
  // );
  // TEST環境用
  // MIRROR環境用
  // res.redirect(
  //   'https://mirror.biztest.smbc.co.jp/mobile/api/bank/auth?code=cb46420e53c24580a4c4e0fe8f888888&state=021mhugbFEtLzz5CXQSsPfgBavI7MFt6QRH4LgTT7ennwnXo'
  // );
  // MIRROR環境用
});

app.get('/health', (req, res) => {
  // 必要なヘルスチェックロジックをここに実装
  // 正常であれば200 OKを返す
  res.status(200).json({ status: 'ok' });
});

// ダミーの認可画面
app.post('/auth_dummy.html', (req, res) => {
  res.redirect('/auth_dummy.html');
});

// 認可リダイレクト検証
app.get('/redirect', (req, res) => {
  res.redirect(
    301,
    'https://dtp-dc-s3-apne1-work-contents.s3.ap-northeast-1.amazonaws.com/portal/system_error.html'
  );
});

// Web21振込情報入力画面（ダミー）のWebView返却
app.post('/transfer_info_input_dummy.html', (req, res) => {
  res.redirect(302, '/transfer_info_input_dummy.html');
});

// Web21承認画面（ダミー）のWebView返却
app.post('/web21_approval_dummy.html', (req, res) => {
  res.redirect(302, '/web21_approval_dummy.html');
});

// IDaaSのOTP入力画面（ダミー）のWebView返却
app.get('/otpInput', (req, res) => {
  res.redirect('/idaas_otp_input_dummy.html');
});

// IDaaSのOTP入力画面（ダミー）完了後の返却
app.get('/web/api/idaas-otp-login', (req, res) => {
  res.status(200).json({ message: 'ok' });
});

// IDaaSのPW再設定メールアドレス入力画面（ダミー）のWebView返却
app.get('/resetPasswordInputEmail', (req, res) => {
  res.redirect('/idaas_reset_password_dummy.html');
});

// IDaaSのPW再設定メールアドレス入力画面（ダミー）完了後の返却
app.get('/id/login', (req, res) => {
  res.status(200).json({ message: 'ok' });
});

// IDaaSのアカウント作成画面（ダミー）のWebView返却
app.get('/signUpInputEmail', (req, res) => {
  res.redirect('/idaas_signup_input_dummy.html');
});

// IDaaSのアカウント作成画面（ダミー）完了後の返却
app.get('/api/user/link-new-ids', (req, res) => {
  res.status(200).json({ message: 'ok' });
});

// IDaaSのメールアドレス変更画面（ダミー）のWebView返却
app.get('/changeEmailInput', (req, res) => {
  res.redirect('/idaas_change_email_dummy.html');
});

// IDaaSのメールアドレス変更画面（ダミー）完了後の返却
app.get('/dtpid-email-change-complete', (req, res) => {
  res.status(200).json({ message: 'ok' });
});

// IDaaSのPW変更画面（ダミー）のWebView返却
app.get('/changePasswordInput', (req, res) => {
  res.redirect('/idaas_change_password_dummy.html');
});

// IDaaSのPW変更画面（ダミー）完了後の返却
app.get('/id-connect', (req, res) => {
  res.status(200).json({ message: 'ok' });
});

// Allganize AIチャットボット関連のルートはrouter.tsに移動しました

// ログのセット内容
// アクセス開始のログはAPI開始のタイミングに移譲
// app.use((req, res, next) => {
//   console.log('アクセス開始');
//   res.on('finish', () => {
//     console.log('アクセス終了');
//   });
//   next();
// });

// Routerの設定
app.use('/api', router);

// 404ハンドラー
app.all('*', (req, res, next) => {
  console.log('NotURLリクエスト', req);
  const notFoundError = new NotFoundError(errorCodes.NOT_FOUND, 'Contents is Not Found');
  next(notFoundError);
});

// エラーハンドラミドルウェアの適用
app.use((err: CustomError, req: Request, res: Response, next: NextFunction) => {
  errorHandler(err, req, res, next).catch(next);
});

export default app;
