/**
 * バックエンドからのエラーレスポンスのデータモデル
 */
export interface BackendErrorResponse {
  response: {
    data: {
      code?: number;
      subCode?: string;
      message: string;
      errorDetail: string;
      errorCode?: string;
      hasFaq?: boolean;
    };
  };
}

/**
 * BFF内部で発生したエラーのデータモデル
 */
export interface BffError {
  statusCode: number;
  errorCode: string;
  errorDetail: Record<string, unknown>;
}

export type ErrorResponse = BackendErrorResponse | BffError | Error;
