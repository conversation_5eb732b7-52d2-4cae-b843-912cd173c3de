import { check, body } from 'express-validator';

/**
 * getBalance API用のバリデーションルール
 */
export const balanceValidationRules = [
  check('date')
    .exists()
    .withMessage('dateが存在しません')
    .isISO8601()
    .withMessage('dateが不正です'),
  check('accountIds')
    .exists()
    .withMessage('accountIdsが存在しません')
    .custom((value: string) => {
      const accountIds = value.split(',');
      if (accountIds.length > 5) {
        throw new Error('accountIdは最大5件まで指定可能です。');
      }
      // eslint-disable-next-line consistent-return
      accountIds.forEach((accountId) => {
        if (!/^\d{13}$/.test(accountId)) {
          throw new Error('accountIdが13桁ではありません。');
        }
      });
      return true;
    })
    .withMessage('accountIdsが不正です')
];

/**
 * getTransactions API用のバリデーションルール
 */
export const transactionsValidationRules = [
  check('dateFrom')
    .exists()
    .withMessage('dateFromが存在しません')
    .isISO8601()
    .withMessage('dateFromが不正です'),
  check('dateTo')
    .exists()
    .withMessage('dateToが存在しません')
    .isISO8601()
    .withMessage('dateToが不正です'),
  check('accountId')
    .exists()
    .withMessage('accountIdが存在しません')
    .isLength({ min: 13, max: 13 })
    .withMessage('accountIdが不正です')
];

/**
 * getTransactionsDailyTotals API用のバリデーションルール
 */
export const transactionsDailyTotalsValidationRules = [
  check('from')
    .exists()
    .withMessage('dateFromが存在しません')
    .isISO8601()
    .withMessage('dateFromが不正です'),
  check('to')
    .exists()
    .withMessage('dateToが存在しません')
    .isISO8601()
    .withMessage('dateToが不正です'),
  check('accountId')
    .exists()
    .withMessage('accountIdが存在しません')
    .isLength({ min: 13, max: 13 })
    .withMessage('accountIdが不正です')
];

/**
 * updateAccountsDisplay API用のバリデーションルール
 */
export const accountDisplayValidationRules = [
  body('accounts').isArray().withMessage('accountsが不正です'),
  body('accounts.*.accountId')
    .exists()
    .isString()
    .isLength({ max: 13 })
    .withMessage('accountIdが不正です'),
  body('accounts.*.displayName').isString().withMessage('displayNameが不正です'),
  body('accounts.*.isHidden').exists().isBoolean().withMessage('isHidenが不正です'),
  body('accounts.*.accountApiType')
    .exists()
    .withMessage('accountApiTypeが存在しません')
    .isIn(['web21', 'freee'])
    .withMessage('accountApiTypeが不正です')
];

/**
 * getAccessToken API用のバリデーションルール
 */
export const accessTokenValidationRules = [
  check('code').exists().withMessage('codeが存在しません'),
  check('state').exists().withMessage('stateが存在しません')
];

/**
 * getWalletables API用のバリデーションルール
 */
export const walletablesValidationRules = [
  check('type')
    .exists()
    .withMessage('typeが存在しません')
    .isIn(['bank_account', 'credit_card', 'wallet'])
    .withMessage('typeが不正です'),
  check('with_balance').isBoolean().withMessage('with_balanceが不正です')
];

/**
 * registerTOSConsentStatus API用のバリデーションルール
 */
export const consentStatusValidationRules = [
  body('agreeTerm').exists().withMessage('agreeTermが存在しません'),
  body('channel').exists().withMessage('channelが存在しません')
];

/**
 * getFreeeTransactions API用のバリデーションルール
 */
export const freeeTransactionsRules = [
  check('walletable_id').exists().withMessage('walletable_idが存在しません'),
  check('walletable_type')
    .exists()
    .withMessage('walletable_typeが存在しません')
    .isIn(['bank_account', 'credit_card', 'wallet'])
    .withMessage('walletable_typeが不正です'),
  check('start_date')
    .exists()
    .withMessage('start_dateが存在しません')
    .isISO8601()
    .withMessage('start_dateが不正です'),
  check('end_date')
    .exists()
    .withMessage('end_dateが存在しません')
    .isISO8601()
    .withMessage('end_dateが不正です')
];

/**
 * getUsedTotalAmount API用のバリデーションルール
 */
export const usedTotalAmountValidationRules = [
  check('walletable_id').exists().withMessage('walletable_idが存在しません'),
  check('walletable_type')
    .exists()
    .withMessage('walletable_typeが存在しません')
    .isIn(['bank_account', 'credit_card', 'wallet'])
    .withMessage('walletable_typeが不正です')
];

/**
 * login API用のバリデーションルール
 */
export const loginValidationRules = [
  body('vdId')
    .exists()
    .withMessage('vdIdが存在しません')
    .isString()
    .isLength({ min: 10, max: 10 })
    .withMessage('vdIdが不正です'),
  body('password')
    .exists()
    .withMessage('passwordが存在しません')
    .isString()
    .withMessage('passwordが不正です')
];

/**
 * DTPIDLogin API用のバリデーションルール
 */
export const loginDtpIdValidationRules = [
  body('dtpId')
    .exists()
    .withMessage('dtpIdが存在しません')
    .isString()
    .isLength({ min: 1, max: 255 })
    .withMessage('dtpIdが不正です'),
  body('password')
    .exists()
    .withMessage('passwordが存在しません')
    .isString()
    .withMessage('passwordが不正です')
];

/**
 * loginSuspiciousDetection API用のバリデーションルール
 */
export const loginSuspiciousDetectionValidationRules = [
  body('caulisSessionId')
    .exists()
    .withMessage('caulisSessionIdが存在しません')
    .isString()
    .withMessage('caulisSessionIdが不正です'),
  body('errorMessageId')
    .exists()
    .withMessage('errorMessageIdが存在しません')
    .isString()
    .withMessage('errorMessageIdが不正です')
];

/**
 * getOpenAccountApplicationTemporary API用のバリデーションルール
 */
export const getOpenAccountApplicationTemporaryValidationRules = [
  check('referenceNumber')
    .exists()
    .withMessage('referenceNumberが存在しません')
    .isString()
    .matches(/^\d+$/)
    .isLength({ min: 13, max: 14 })
    .withMessage('お手続き番号は13桁もしくは14桁の数字を入力してください。')
    .custom((value: string) => {
      // 7文字目が5であること
      if (value.charAt(6) !== '5') {
        throw new Error('7文字目が5ではありません');
      }
      return true;
    })
];

/**
 * getOpenAccountApplicationScreeningStatuse API用のバリデーションルール
 */
export const getOpenAccountApplicationScreeningStatusesValidationRules = [
  check('referenceNumber')
    .exists()
    .withMessage('referenceNumberが存在しません')
    .isString()
    .matches(/^\d+$/)
    .isLength({ min: 13, max: 14 })
    .withMessage('お手続き番号は13桁もしくは14桁の数字を入力してください。')
    .custom((value: string) => {
      // 7文字目が5であること
      if (value.charAt(6) !== '5') {
        throw new Error('7文字目が5ではありません');
      }
      return true;
    })
];

/**
 * searchAddress API用のバリデーションルール
 */
export const searchAddressValidationRules = [
  check('postcode')
    .exists()
    .withMessage('postcodeが存在しません')
    .isString()
    .matches(/^\d+$/)
    .isLength({ min: 7, max: 7 })
    .withMessage('postcodeの形式が不正です')
];

/**
 * saveIdentityVerification API用のバリデーションルール
 */
export const identityVerificationValidationRules = [
  body('referenceNumber')
    .exists()
    .withMessage('referenceNumberが存在しません')
    .isString()
    .matches(/^\d+$/)
    .isLength({ min: 13, max: 14 })
    .withMessage('お手続き番号は13桁もしくは14桁の数字を入力してください。')
    .custom((value: string) => {
      // 7文字目が5であること
      if (value.charAt(6) !== '5') {
        throw new Error('7文字目が5ではありません');
      }
      return true;
    }),
  body('role')
    .exists()
    .withMessage('roleが存在しません')
    .isString()
    .withMessage('roleが不正です'),
  body('dataSource')
    .exists()
    .withMessage('dataSourceが存在しません')
    .isString()
    .withMessage('dataSourceが不正です'),
  body('familyName')
    .optional()
    .isString()
    .isLength({ max: 28 })
    .withMessage('familyNameが不正です'),
  body('givenName')
    .optional()
    .isString()
    .isLength({ max: 28 })
    .withMessage('givenNameが不正です'),
  body('familyNameKana')
    .optional()
    .isString()
    .isLength({ max: 39 })
    .withMessage('familyNameKanaが不正です'),
  body('givenNameKana')
    .optional()
    .isString()
    .isLength({ max: 39 })
    .withMessage('givenNameKanaが不正です'),
  body('familyNameAlphabetic')
    .optional()
    .isString()
    .isLength({ max: 34 })
    .withMessage('lfamilyNameAlphabeticが不正です'),
  body('givenNameAlphabetic')
    .optional()
    .isString()
    .isLength({ max: 34 })
    .withMessage('givenNameAlphabeticが不正です'),
  body('position')
    .optional()
    .isString()
    .isLength({ max: 30 })
    .withMessage('positionが不正です'),
  body('birthYear')
    .optional()
    .isNumeric()
    .isLength({ max: 4 })
    .withMessage('birthYearが不正です'),
  body('birthMonth')
    .optional()
    .isNumeric()
    .isLength({ max: 2 })
    .withMessage('birthYearが不正です'),
  body('birthDay')
    .optional()
    .isNumeric()
    .isLength({ max: 2 })
    .withMessage('birthYearが不正です'),
  body('postCode')
    .optional()
    .isString()
    .isLength({ min: 7, max: 7 })
    .withMessage('postcodeが不正です'),
  body('prefecture')
    .optional()
    .isString()
    .isLength({ max: 10 })
    .withMessage('prefectureが不正です'),
  body('city')
    .optional()
    .isString()
    .isLength({ max: 20 })
    .withMessage('cityが不正です'),
  body('sectionNumberAndBuildingName')
    .optional()
    .isString()
    .isLength({ max: 300 })
    .withMessage('sectionNumberAndBuildingNameが不正です')
];

/**
 * requestEkycUrl API用のバリデーションルール
 */
export const requestEkycUrlValidationRules = [
  body('referenceNumber')
    .exists()
    .withMessage('referenceNumberが存在しません')
    .isString()
    .matches(/^\d+$/)
    .isLength({ min: 13, max: 14 })
    .withMessage('お手続き番号は13桁もしくは14桁の数字を入力してください。')
    .custom((value: string) => {
      // 7文字目が5であること
      if (value.charAt(6) !== '5') {
        throw new Error('7文字目が5ではありません');
      }
      return true;
    }),
  body('uuid').exists().withMessage('uuidが存在しません').isString()
    .withMessage('uuidが不正です'),
  body('userType')
    .exists()
    .withMessage('userTypeが存在しません')
    .isString()
    .isIn(['01', '02', '03', '04', '05', '06'])
    .withMessage('userTypeが不正です')
];

/**
 * getUuid API用のバリデーションルール
 */
export const getUuidRules = [
  check('referenceNumber')
    .exists()
    .withMessage('referenceNumberが存在しません')
    .isString()
    .matches(/^\d+$/)
    .isLength({ min: 13, max: 14 })
    .withMessage('お手続き番号は13桁もしくは14桁の数字を入力してください。')
    .custom((value: string) => {
      // 7文字目が5であること
      if (value.charAt(6) !== '5') {
        throw new Error('7文字目が5ではありません');
      }
      return true;
    })
];

/**
 * getWorkBlockageStatus API用のバリデーションルール
 */
export const getWorkBlockageStatusValidationRules = [
  check('functionId')
    .exists()
    .withMessage('functionIdが存在しません')
    .isString()
    .withMessage('rfunctionIdが不正です')
];

/**
 * getDecryptWeb21Otp API用のバリデーションルール
 */
export const getDecryptWeb21OtpValidationRules = [
  check('encryptedOtp').exists().withMessage('encryptedOtpが存在しません')
];

/**
 * linkToFreee API用のバリデーションルール
 */
export const linkToFreeeValidationRules = [
  body('code').exists().withMessage('codeが存在しません'),
  body('stateFromFreee').exists().withMessage('stateFromFreeeが存在しません')
];

/**
 * issueAndLinkDtpId API用のバリデーションルール
 */
export const issueAndLinkDtpIdValidationRules = [
  body('encryptedCookie').exists().withMessage('encryptedCookieが存在しません')
];
