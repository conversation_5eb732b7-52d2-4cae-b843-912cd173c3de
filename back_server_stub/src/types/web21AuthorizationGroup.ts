// cf. components['schemas']['Web21Authorizations']
export type Web21AuthorizationGroup = {
  /** @description 01 取引口座照会 */
  accountInquiryAuth: boolean;
  /** @description 02 総合振込 */
  generalTransferAuth: boolean;
  /** @description 03 給与/賞与振込 */
  salaryTransferAuth: boolean;
  /** @description 04 個人地方税納付 */
  localTaxPaymentAuth: boolean;
  /** @description 05 承認 */
  approvalAuth: boolean;
  /** @description 06 振込振替 */
  transferAuth: boolean;
  /** @description 07 外部ファイル送信 */
  fileSendingAuth: boolean;
  /** @description 08 税金・各種料金の払込 */
  taxesOrFeesPaymentAuth: boolean;
  /** @description 09 口座振替 */
  accountTransferAuth: boolean;
  /** @description 10 振込先口座確認 */
  transferDestinationCheckAuth: boolean;
  /** @description 11 データ変換 */
  dataConversionAuth: boolean;
  /** @description 12 スマホ取引口座照会 */
  accountInquiryOnSmartPhoneAuth: boolean;
  /** @description 13 スマホ振込 */
  transferOnSmartPhoneAuth: boolean;
  /** @description 14 スマホ承認 */
  approvalOnSmartPhoneAuth: boolean;
  /** @description 15 スマホ口座情報事前設定 */
  preSettingAccountInfoOnSmartPhoneAuth: boolean;
  /** @description 16 スマホセキュリティ設定 */
  settingSecurityOnSmartPhoneAuth: boolean;
  /** @description 19 口座情報事前設定 */
  preSettingAccountInfoAuth: boolean;
  /** @description 20 セキュリティ設定 */
  settingSecurityAuth: boolean;
};
