{"globals": {}, "moduleNameMapper": {}, "moduleDirectories": ["src", "node_modules"], "moduleFileExtensions": ["js", "ts"], "preset": "ts-jest", "testEnvironment": "node", "collectCoverage": true, "collectCoverageFrom": ["src/**/*.ts"], "coverageReporters": ["text", "lcov", "clover", "html"], "setupFiles": ["./jest.setup-js"], "reporters": ["default", ["jest-html-reporters", {"publicPath": "./coverage", "filename": "coverage.html", "expand": true}]]}