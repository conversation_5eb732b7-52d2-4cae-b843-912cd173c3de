import { validationResult } from 'express-validator';
import {
  balanceValidationRules,
  transactionsValidationRules,
  accountDisplayValidationRules,
  accessTokenValidationRules,
  transactionsDailyTotalsValidationRules,
  walletablesValidationRules,
  consentStatusValidationRules,
  freeeTransactionsRules,
  usedTotalAmountValidationRules,
  searchAddressValidationRules,
  identityVerificationValidationRules,
  getWorkBlockageStatusValidationRules,
  loginDtpIdValidationRules,
  getOpenAccountApplicationScreeningStatusesValidationRules,
  requestEkycUrlValidationRules,
  getOpenAccountApplicationTemporaryValidationRules,
  decryptWeb21OtpValidationRules,
  issueAndLinkDtpIdValidationRules,
  getUuidRules,
  checkEncryptedVdidValidationRules
} from '../../../src/validators/validationRules';
import { logger } from '../../../src/utils/logger';

describe('validationRules', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  describe('balanceValidationRules', () => {
    it('正常系_dateがISO8601形式であること', async () => {
      const req = {
        query: {
          date: '2023-10-13'
        },
        params: {
          accountIds: '*************'
        }
      };

      await balanceValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_dateが不足している場合、エラーが発生すること', async () => {
      const req = {
        params: {
          accountIds: '*************'
        }
      };

      await balanceValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dateがISO8601形式でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          date: '2023-10-3'
        },
        params: {
          accountIds: '*************'
        }
      };

      await balanceValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountIdsが13桁であること', async () => {
      const req = {
        query: {
          date: '2023-10-13'
        },
        params: {
          accountIds: '*************'
        }
      };

      await balanceValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountIdsが不足している場合、エラーが発生すること', async () => {
      const req = {
        query: {
          date: '2023-10-13'
        }
      };

      await balanceValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_accountIdsが6つ以上の要素を持つ場合、エラーが発生すること', async () => {
      const req = {
        query: {
          date: '2023-10-13'
        },
        params: {
          accountIds:
            '*************,*************,*************,*************,*************,*************'
        }
      };

      await balanceValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_accountIdsが13桁ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          date: '2023-10-13'
        },
        params: {
          accountIds: '1'
        }
      };

      await balanceValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('transactionsValidationRules', () => {
    it('正常系_dateFromがISO8601形式であること', async () => {
      const req = {
        query: {
          dateFrom: '2023-10-13'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_dateFromが不足している場合、エラーが発生すること', async () => {
      const req = {
        params: {
          accountIds: '*************'
        }
      };

      await transactionsValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dateFromがISO8601形式ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          dateFrom: '2023-10-3'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_dateToがISO8601形式であること', async () => {
      const req = {
        query: {
          dateTo: '2023-10-13'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_dateToが不足している場合、エラーが発生すること', async () => {
      const req = {
        params: {
          accountIds: '*************'
        }
      };

      await transactionsValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dateToがISO8601形式ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          dateTo: '2023-10-3'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountIdが13桁であること', async () => {
      const req = {
        query: {
          dateFrom: '2023-10-10',
          dateTo: '2023-10-13'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountIdが不足している場合、エラーが発生すること', async () => {
      const req = {
        query: {
          dateFrom: '2023-10-10',
          dateTo: '2023-10-13'
        }
      };

      await transactionsValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_accountIdが13桁ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          dateFrom: '2023-10-10',
          dateTo: '2023-10-13'
        },
        params: {
          accountId: '************'
        }
      };

      await transactionsValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('transactionsDailyTotalsValidationRules', () => {
    it('正常系_fromがISO8601形式であること', async () => {
      const req = {
        query: {
          from: '2023-10-13'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_fromが不足している場合、エラーが発生すること', async () => {
      const req = {
        params: {
          accountIds: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_fromがISO8601形式ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          from: '2023-10-3'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_toがISO8601形式であること', async () => {
      const req = {
        query: {
          to: '2023-10-13'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_toが不足している場合、エラーが発生すること', async () => {
      const req = {
        params: {
          accountIds: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_toがISO8601形式ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          to: '2023-10-3'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountIdが13桁であること', async () => {
      const req = {
        query: {
          from: '2023-10-10',
          to: '2023-10-13'
        },
        params: {
          accountId: '*************'
        }
      };

      await transactionsDailyTotalsValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountIdが不足している場合、エラーが発生すること', async () => {
      const req = {
        query: {
          from: '2023-10-10',
          to: '2023-10-13'
        }
      };

      await transactionsDailyTotalsValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_accountIdが13桁ではない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          from: '2023-10-10',
          to: '2023-10-13'
        },
        params: {
          accountId: '************'
        }
      };

      await transactionsDailyTotalsValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('accountDisplayValidationRules', () => {
    it('正常系_accountsが存在すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountsが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await accountDisplayValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountsが配列であること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountsが配列ではない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: {
            accountId: '*************',
            displayName: '振込用口座1',
            isHidden: false,
            accountApiType: 'web21'
          }
        }
      };

      await accountDisplayValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountIdがString型かつ13桁であること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountIdがString型ではない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: *************,
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_accountIdがString型かつMax13桁を超える場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************4',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_displayNameがString型であること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_displayNameがString型ではない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: 123,
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_isHiddenがBoolean型であること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[3].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_isHiddenが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[3].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_isHiddenがBoolean型ではない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: 'ABC',
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[3].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountApiTypeが存在すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[4].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountApiTypeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false
            }
          ]
        }
      };

      await accountDisplayValidationRules[4].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('正常系_accountApiTypeが特定の文言であること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'web21'
            }
          ]
        }
      };

      await accountDisplayValidationRules[4].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_accountApiTypeが特定の文言でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          accounts: [
            {
              accountId: '*************',
              displayName: '振込用口座1',
              isHidden: false,
              accountApiType: 'SMBC'
            }
          ]
        }
      };

      await accountDisplayValidationRules[4].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('accessTokenValidationRules', () => {
    it('正常系_codeが存在すること', async () => {
      const req = {
        query: {
          code: 'code'
        }
      };

      await accessTokenValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('正常系_stateが存在すること', async () => {
      const req = {
        query: {
          state: 'state'
        }
      };

      await accessTokenValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_codeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          state: 'state'
        }
      };

      await accessTokenValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_stateが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          code: 'code'
        }
      };

      await accessTokenValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('consentStatusValidationRules', () => {
    it('正常系_agreeTermが存在すること', async () => {
      const req = {
        body: {
          agreeTerm: 'DTP',
          channel: '02'
        }
      };

      await consentStatusValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('正常系_channelが存在すること', async () => {
      const req = {
        body: {
          agreeTerm: 'DTP',
          channel: '02'
        }
      };

      await consentStatusValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_agreeTermが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          channel: '02'
        }
      };

      await consentStatusValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_channelが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          agreeTerm: 'DTP'
        }
      };

      await consentStatusValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('walletablesValidationRules', () => {
    it('正常系_typeが存在すること', async () => {
      const req = {
        query: {
          type: 'bank_account'
        }
      };

      await walletablesValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('正常系_with_balanceがBoolean型であること', async () => {
      const req = {
        query: {
          type: 'bank_account',
          with_balance: true
        }
      };

      await walletablesValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('正常系_with_sync_statusがBoolean型であること', async () => {
      const req = {
        query: {
          type: 'bank_account',
          with_balance: true,
          with_sync_status: true
        }
      };

      await walletablesValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_typeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {}
      };

      await walletablesValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_typeが特定の文言でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          type: 'bank_accounts'
        }
      };

      await walletablesValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_with_balanceがBoolean型でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          type: 'bank_account',
          with_balance: 'with_balance'
        }
      };

      await walletablesValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_with_sync_statusがBoolean型でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          type: 'bank_account',
          with_balance: 'with_balance',
          with_sync_status: 'with_sync_status'
        }
      };

      await walletablesValidationRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('freeeTransactionsRules', () => {
    it('正常系_walletable_idが存在すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_walletable_typeが存在すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_start_dateが存在すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_start_dateがISO8601形式であること', async () => {
      const req = {
        query: {
          start_date: '2024-01-01'
        }
      };

      await freeeTransactionsRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_end_dateが存在すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[3].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_end_dateがISO8601形式であること', async () => {
      const req = {
        query: {
          end_date: '2024-01-01'
        }
      };

      await freeeTransactionsRules[3].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_walletable_idが存在しない場合、エラーが発生することと', async () => {
      const req = {
        query: {
          walletable_type: 'bank_account',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_walletable_typeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_walletable_typeが特定の文言でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_accounts',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_start_dateが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          end_date: '2024-04-01'
        }
      };

      await freeeTransactionsRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_start_dateがISO8601形式でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          start_date: '2024-01-1'
        }
      };

      await freeeTransactionsRules[2].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_end_dateが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2024-01-01'
        }
      };

      await freeeTransactionsRules[3].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_end_dateがISO8601形式でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          end_date: '2024-01-1'
        }
      };

      await freeeTransactionsRules[3].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  it('異常系_end_dateがISO8601形式でない場合、エラーが発生すること', async () => {
    const req = {
      query: {
        end_date: '2024-01-1'
      }
    };

    await freeeTransactionsRules[3].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  describe('usedTotalAmountValidationRules', () => {
    it('正常系_walletable_idが存在すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'credit_card'
        }
      };

      await usedTotalAmountValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_walletable_typeが存在すること', async () => {
      const req = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account'
        }
      };

      await usedTotalAmountValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_walletable_idが存在しない場合、エラーが発生することと', async () => {
      const req = {
        query: {
          walletable_type: 'bank_account'
        }
      };

      await usedTotalAmountValidationRules[0].run(req);
    });

    it('異常系_walletable_typeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          walletable_id: 1
        }
      };

      await usedTotalAmountValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_typeが特定の文言でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          walletable_type: 'bank_accounts'
        }
      };

      await usedTotalAmountValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('loginDtpIdValidationRules', () => {
    it('正常系_dtpIdが存在すること', async () => {
      const req = {
        body: {
          dtpId: '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('正常系_dtpIdのローカル部分が64文字以内の場合', async () => {
      const req = {
        body: {
          dtpId:
            '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('正常系_passwordが存在すること', async () => {
      const req = {
        body: {
          dtpId: '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_dtpIdが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_passwordが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '1234567890'
        }
      };

      await loginDtpIdValidationRules[1].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdがString型でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: 1234567890,
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdが1桁未満の場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdが128桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId:
            '*************4567890*************4567890*************4567890*************4567890*************4567890*************4567890123456',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのローカル部分が64文字より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId:
            '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdの先頭がドットの場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '.<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdで.@が続いた場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのローカル部分に無効な文字を含む場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: 'test:<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdに@を含まない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: 'testAabc.com',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのドメイン部分でドットが続いた場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのドメイン部分の先頭がハイフンの場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '<EMAIL>',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのドメイン部分の末尾がハイフンの場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: '<EMAIL>-',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのドメイン部分にドットを含まない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: 'test@abc-com',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_dtpIdのドメイン部分に無効な文字を含む場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dtpId: 'test@abc&com',
          password: 'password'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('getOpenAccountApplicationTemporaryValidationRules', () => {
    it('正常系_referenceNumberが正しい形式の場合', async () => {
      const req = {
        query: {
          referenceNumber: '************1'
        }
      };

      await getOpenAccountApplicationTemporaryValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_referenceNumberが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {}
      };

      await getOpenAccountApplicationTemporaryValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_referenceNumberが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          referenceNumber: *************
        }
      };

      await getOpenAccountApplicationTemporaryValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_referenceNumberに数字以外の文字が含まれている場合、エラーが発生すること', async () => {
      const req = {
        query: {
          referenceNumber: 'ABC1235467890'
        }
      };

      await getOpenAccountApplicationTemporaryValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  it('異常系__referenceNumberが13桁未満の場合、エラーが発生すること', async () => {
    const req = {
      body: {
        referenceNumber: '************'
      }
    };

    await loginDtpIdValidationRules[0].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  it('異常系__referenceNumberが13桁より多い場合、エラーが発生すること', async () => {
    const req = {
      body: {
        referenceNumber: '************12'
      }
    };

    await loginDtpIdValidationRules[0].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  it('異常系__referenceNumberの7文字目が5でない場合、エラーが発生すること', async () => {
    const req = {
      body: {
        referenceNumber: '*************'
      }
    };

    await loginDtpIdValidationRules[0].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  describe('getOpenAccountApplicationScreeningStatusesValidationRules', () => {
    it('正常系_referenceNumberが正しい形式の場合', async () => {
      const req = {
        query: {
          referenceNumber: '************1'
        }
      };

      await getOpenAccountApplicationScreeningStatusesValidationRules[0].run(
        req
      );
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_referenceNumberが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {}
      };

      await getOpenAccountApplicationScreeningStatusesValidationRules[0].run(
        req
      );
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_referenceNumberが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          referenceNumber: *************
        }
      };

      await getOpenAccountApplicationScreeningStatusesValidationRules[0].run(
        req
      );
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_referenceNumberに数字以外の文字が含まれている場合、エラーが発生すること', async () => {
      const req = {
        query: {
          referenceNumber: 'ABC1235467890'
        }
      };

      await getOpenAccountApplicationScreeningStatusesValidationRules[0].run(
        req
      );
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  it('異常系__referenceNumberが13桁未満の場合、エラーが発生すること', async () => {
    const req = {
      body: {
        referenceNumber: '************'
      }
    };

    await loginDtpIdValidationRules[0].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  it('異常系__referenceNumberが13桁より多い場合、エラーが発生すること', async () => {
    const req = {
      body: {
        referenceNumber: '************12'
      }
    };

    await loginDtpIdValidationRules[0].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  it('異常系__referenceNumberの7文字目が5でない場合、エラーが発生すること', async () => {
    const req = {
      body: {
        referenceNumber: '*************'
      }
    };

    await loginDtpIdValidationRules[0].run(req);

    const result = validationResult(req);
    expect(result.isEmpty()).toBeFalsy();
  });

  describe('searchAddressValidationRules', () => {
    it('正常系_postcodeが正しい形式の場合', async () => {
      const req = {
        query: {
          postcode: '1234567'
        }
      };

      await searchAddressValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_postcodeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {}
      };

      await searchAddressValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_postcodeが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          postcode: 1000001
        }
      };

      await searchAddressValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_postcodeに数字以外の文字が含まれている場合、エラーが発生すること', async () => {
      const req = {
        query: {
          postcode: 'ABCDFFG'
        }
      };

      await searchAddressValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_postcodeが7桁でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          postcode: '12345678'
        }
      };

      await searchAddressValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('identityVerificationValidationRules', () => {
    it('正常系_referenceNumberが正しい形式の場合', async () => {
      const req = {
        body: {
          referenceNumber: '************1'
        }
      };

      await identityVerificationValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_referenceNumberが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_referenceNumberが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: *************
        }
      };
      await identityVerificationValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberが13桁未満の場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '************'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberが13桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '************12'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberの7文字目が5でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '*************'
        }
      };

      await loginDtpIdValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_roleが正しい形式の場合', async () => {
      const req = {
        body: {
          role: 'REPRESENTATIVE'
        }
      };

      await identityVerificationValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_roleが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_roleが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          role: 1000001
        }
      };

      await identityVerificationValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_dataSourceが正しい形式の場合', async () => {
      const req = {
        body: {
          dataSource: 'JPKI'
        }
      };

      await identityVerificationValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_dataSourceが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_dataSourceが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          dataSource: 1000001
        }
      };

      await identityVerificationValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_familyNameが正しい形式の場合', async () => {
      const req = {
        body: {
          familyName: '若草若草若草若草若草若草若草若草若草若草若草若草若草若草'
        }
      };

      await identityVerificationValidationRules[3].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_familyNameが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[3].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_familyNameが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          familyName: 1234
        }
      };

      await identityVerificationValidationRules[3].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_familyNameが28桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          familyName:
            '若草若草若草若草若草若草若草若草若草若草若草若草若草若草1'
        }
      };

      await identityVerificationValidationRules[3].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_givenNameが正しい形式の場合', async () => {
      const req = {
        body: {
          givenName: '太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎'
        }
      };

      await identityVerificationValidationRules[4].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('正常系_givenNameが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[4].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_givenNameが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          givenName: 1234
        }
      };

      await identityVerificationValidationRules[4].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_givenNameが28桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          givenName: '太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎太郎1'
        }
      };

      await identityVerificationValidationRules[4].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_familyNameKanaが正しい形式の場合', async () => {
      const req = {
        body: {
          familyNameKana:
            'ワカクサワカクサワカクサワカクサワカクサワカクサワカクサワカクサワカクサワカク'
        }
      };

      await identityVerificationValidationRules[5].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_familyNameKanaが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          familyNameKana: 1234
        }
      };

      await identityVerificationValidationRules[5].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_familyNameKanaが39桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          familyNameKana:
            'ワカクサワカクサワカクサワカクサワカクサワカクサワカクサワカクサワカクサワカクサ'
        }
      };

      await identityVerificationValidationRules[5].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_givenNameKanaが正しい形式の場合', async () => {
      const req = {
        body: {
          givenNameKana:
            'タロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタロウ'
        }
      };

      await identityVerificationValidationRules[6].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_givenNameKanaが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          givenNameKana: 1234
        }
      };

      await identityVerificationValidationRules[6].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_givenNameKanaが39桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          givenNameKana:
            'タロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタロウタ'
        }
      };

      await identityVerificationValidationRules[6].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_familyNameAlphabeticが正しい形式の場合', async () => {
      const req = {
        body: {
          familyNameAlphabetic: 'WAKAKUSAWAKAKUSAWAKAKUSAWAKAKUSA'
        }
      };

      await identityVerificationValidationRules[7].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_familyNameAlphabeticが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          familyNameAlphabetic: 1234
        }
      };

      await identityVerificationValidationRules[7].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_familyNameAlphabeticが34桁より大きい場合、エラーが発生すること', async () => {
      const req = {
        body: {
          familyNameAlphabetic: 'WAKAKUSAWAKAKUSAWAKAKUSAWAKAKUSAWAK'
        }
      };

      await identityVerificationValidationRules[7].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_givenNameAlphabeticが正しい形式の場合', async () => {
      const req = {
        body: {
          givenNameAlphabetic: 'TAROTAROTAROTAROTAROTAROTAROTAROTA'
        }
      };

      await identityVerificationValidationRules[8].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_givenNameAlphabeticが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          givenNameAlphabetic: 1234
        }
      };

      await identityVerificationValidationRules[8].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_givenNameAlphabeticが34桁より大きい場合、エラーが発生すること', async () => {
      const req = {
        body: {
          givenNameAlphabetic: 'TAROTAROTAROTAROTAROTAROTAROTAROTAR'
        }
      };

      await identityVerificationValidationRules[8].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_positionが正しい形式の場合', async () => {
      const req = {
        body: {
          position: '部長'
        }
      };

      await identityVerificationValidationRules[9].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_positionが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          position: 1234
        }
      };

      await identityVerificationValidationRules[9].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_positionが30桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          position:
            '部長部長部長部長部長部長部長部長部長部長部長部長部長部長部長部'
        }
      };

      await identityVerificationValidationRules[9].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_birthYearが正しい形式の場合', async () => {
      const req = {
        body: {
          birthYear: 1999
        }
      };

      await identityVerificationValidationRules[10].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_birthYearが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[10].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_birthYearが数字でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          birthYear: 'aaaa'
        }
      };

      await identityVerificationValidationRules[10].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_birthYearが4桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          birthYear: 19990
        }
      };

      await identityVerificationValidationRules[10].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_birthMonthが正しい形式の場合', async () => {
      const req = {
        body: {
          birthMonth: 12
        }
      };

      await identityVerificationValidationRules[11].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_birthMonthが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[11].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_birthMonthが数字でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          birthMonth: 'aa'
        }
      };

      await identityVerificationValidationRules[11].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_birthMonthが2桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          birthMonth: 121
        }
      };

      await identityVerificationValidationRules[11].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_birthDayが正しい形式の場合', async () => {
      const req = {
        body: {
          birthDay: 10
        }
      };

      await identityVerificationValidationRules[12].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_birthDayが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[12].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_birthDayが数字でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          birthDay: 'aa'
        }
      };

      await identityVerificationValidationRules[12].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_birthDayが2桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          birthDay: 101
        }
      };

      await identityVerificationValidationRules[12].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_postCodeが正しい形式の場合', async () => {
      const req = {
        body: {
          postCode: '1234567'
        }
      };

      await identityVerificationValidationRules[13].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_postcodeが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          postCode: 1234567
        }
      };

      await identityVerificationValidationRules[13].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_postCodeが7桁より少ない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          postCode: '123456'
        }
      };

      await identityVerificationValidationRules[13].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_postCodeが7桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          postCode: '12345678'
        }
      };

      await identityVerificationValidationRules[13].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_prefectureが正しい形式の場合', async () => {
      const req = {
        body: {
          prefecture: '東京都'
        }
      };

      await identityVerificationValidationRules[14].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_prefectureが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[14].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_prefectureが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          prefecture: 1000001
        }
      };

      await identityVerificationValidationRules[14].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_prefectureが10桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          prefecture: '東京都東京都東京都東京'
        }
      };

      await identityVerificationValidationRules[14].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_cityが正しい形式の場合', async () => {
      const req = {
        body: {
          city: '千代田区'
        }
      };

      await identityVerificationValidationRules[15].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_cityが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[15].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_cityが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          city: 12
        }
      };

      await identityVerificationValidationRules[15].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_cityが20桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          city: '千代田区千代田区千代田区千代田区千代田区千'
        }
      };

      await identityVerificationValidationRules[15].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_sectionNumberAndBuildingNameが正しい形式の場合', async () => {
      const req = {
        body: {
          sectionNumberAndBuildingName: '1-2-3 SMBCビル12階'
        }
      };

      await identityVerificationValidationRules[16].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_sectionNumberAndBuildingNameが存在しない場合、エラーが発生しないこと', async () => {
      const req = {
        body: {}
      };

      await identityVerificationValidationRules[16].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_sectionNumberAndBuildingNameが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          sectionNumberAndBuildingName: 1234
        }
      };

      await identityVerificationValidationRules[16].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_sectionNumberAndBuildingNameが300桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          sectionNumberAndBuildingName:
            '1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階1-2-3 SMBCビル12階S'
        }
      };

      await identityVerificationValidationRules[16].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('requestEkycUrlValidationRules', () => {
    it('正常系_referenceNumberが正しい形式の場合', async () => {
      const req = {
        body: {
          referenceNumber: '************1'
        }
      };

      await requestEkycUrlValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_referenceNumberが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await requestEkycUrlValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_referenceNumberが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: *************
        }
      };
      await requestEkycUrlValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberが13桁未満の場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '************'
        }
      };

      await requestEkycUrlValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberが14桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '************123'
        }
      };

      await requestEkycUrlValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberの7文字目が5でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '*************'
        }
      };

      await requestEkycUrlValidationRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_uuidが正しい形式の場合', async () => {
      const req = {
        body: {
          uuid: '3825cf2e-653c-445f-bf92-30f802c99b2e'
        }
      };

      await requestEkycUrlValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_uuidが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await requestEkycUrlValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_uuidが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          uuid: 12345
        }
      };

      await requestEkycUrlValidationRules[1].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('正常系_userTypeが正しい形式の場合', async () => {
      const req = {
        body: {
          userType: '01'
        }
      };

      await requestEkycUrlValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_userTypeが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await requestEkycUrlValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_userTypeが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          userType: 12345
        }
      };

      await requestEkycUrlValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_userTypeが特定の文言でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          userType: '1'
        }
      };

      await requestEkycUrlValidationRules[2].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('getWorkBlockageStatusValidationRules', () => {
    it('正常系_functionIdが正しい形式の場合', async () => {
      const req = {
        query: {
          functionId: '0101'
        }
      };

      await getWorkBlockageStatusValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_functionIdが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {}
      };

      await getWorkBlockageStatusValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });

    it('異常系_functionIdが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          functionId: 1234567890
        }
      };

      await getWorkBlockageStatusValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('decryptWeb21OtpValidationRules', () => {
    it('正常系_encryptedOtpが正しい形式の場合', async () => {
      const req = { body: { encryptedOtp: 'XXXXXXXX' } };

      await decryptWeb21OtpValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_encryotedOtpが存在しない場合、エラーが発生すること', async () => {
      const req = { body: {} };

      await decryptWeb21OtpValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('checkEncryptedVdidValidationRules', () => {
    it('正常系_encryptedVdidが正しい形式の場合', async () => {
      const req = { body: { encryptedVdid: 'XXXXXXX' } };

      await checkEncryptedVdidValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });

    it('異常系_encryotedOtpが存在しない場合、エラーが発生すること', async () => {
      const req = { body: {} };

      await checkEncryptedVdidValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('issueAndLinkDtpIdValidationRules', () => {
    it('正常系_encryptedCookieが正しい形式の場合', async () => {
      const req = {
        body: {
          encryptedCookie: {}
        }
      };

      await issueAndLinkDtpIdValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_encryptedCookieが存在しない場合、エラーが発生すること', async () => {
      const req = {
        body: {}
      };

      await issueAndLinkDtpIdValidationRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });

  describe('getUuidRules', () => {
    it('正常系_referenceNumberが正しい形式の場合', async () => {
      const req = {
        query: {
          referenceNumber: '************1'
        }
      };

      await getUuidRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeTruthy();
    });
    it('異常系_referenceNumberが存在しない場合、エラーが発生すること', async () => {
      const req = {
        query: {}
      };

      await getUuidRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_referenceNumberが文字列でない場合、エラーが発生すること', async () => {
      const req = {
        query: {
          referenceNumber: *************
        }
      };

      await getUuidRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系_referenceNumberに数字以外の文字が含まれている場合、エラーが発生すること', async () => {
      const req = {
        query: {
          referenceNumber: 'ABC1235467890'
        }
      };

      await getUuidRules[0].run(req);
      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberが13桁未満の場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '************'
        }
      };

      await getUuidRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberが14桁より多い場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '************123'
        }
      };

      await getUuidRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
    it('異常系__referenceNumberの7文字目が5でない場合、エラーが発生すること', async () => {
      const req = {
        body: {
          referenceNumber: '*************'
        }
      };

      await getUuidRules[0].run(req);

      const result = validationResult(req);
      expect(result.isEmpty()).toBeFalsy();
    });
  });
});
