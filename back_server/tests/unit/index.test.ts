import request from 'supertest';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import dotenv from 'dotenv';
import app from '../../src/server';
import { closeServer } from '../../src/index';
import { logger } from '../../src/utils/logger';

jest.mock('@aws-sdk/lib-dynamodb');

jest.mock('dotenv', () => ({
  config: jest.fn()
}));

// 各々のRouteのテストは結合試験で実施
describe('index', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });
  afterAll(() => {
    closeServer();
  });

  it('dotenv.configが呼び出されること', () => {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    expect(dotenv.config).toHaveBeenCalled();
  });

  it('appが正常に動作しているときhealthが応答すること', async () => {
    // モックデータ
    const mockScanCommandResponse = {
      Items: [
        { errorCode: '001', errorMessage: 'Error 001' },
        { errorCode: '002', errorMessage: 'Error 002' }
      ]
    };
    const mockDocClientInstance = {
      send: jest.fn().mockResolvedValue(mockScanCommandResponse)
    };
    (DynamoDBDocumentClient.from as jest.Mock).mockReturnValue(
      mockDocClientInstance
    );

    const res = await request(app).get('/mobile/health');

    expect(res.status).toBe(200);
    expect(res.text).toBe('{"status":"ok"}');
  });
});
