import { jstTimeDate } from '../../../src/utils/dateFormat';
import { checkClosingStatus } from '../../../src/utils/checkClosingStatus';
import { ClosingTime } from '../../../src/models/closingTime';

jest.mock('../../../src/utils/dateFormat');

describe('checkClosingStatus', () => {
  it('checkClosingStatus(開局時間:1日目)', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 0,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-15T17:59:59')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(true);
  });
  it('checkClosingStatus(開局時間:2日目)', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 0,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-16T08:00:01')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(true);
  });

  it('checkClosingStatus(閉局時間:1日目)', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 0,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-15T18:00:00')
    );

    const result = checkClosingStatus(closingTime);

    expect(result).toEqual(false);
  });

  it('checkClosingStatus(閉局時間:2日目)', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 0,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-16T08:00:00')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
  it('checkClosingStatus(開局時間:startTime > endTime(1日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 0,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-14T17:59:59')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(true);
  });
  it('checkClosingStatus(開局時間:startTime > endTime(2日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 0,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-15T08:00:01')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(true);
  });
  it('checkClosingStatus(閉局時間:startTime > endTime(1日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 0,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-14T18:00:01')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
  it('checkClosingStatus(閉局時間:startTime > endTime(1日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 0,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-15T8:00:00')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
  it('checkClosingStatus(閉局時間:startTime > endTime(1日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 0,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-15T8:00:00')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
  it('checkClosingStatus(閉局時間:複数日(1日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-14T18:00:00')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
  it('checkClosingStatus(閉局時間:複数日(2日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-15T12:00:00')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
  it('checkClosingStatus(閉局時間:複数日(3日目))', () => {
    const closingTime: ClosingTime = {
      start: {
        day: 6,
        time: '18:00'
      },
      end: {
        day: 1,
        time: '08:00'
      }
    };
    (jstTimeDate as jest.MockedFunction<typeof jstTimeDate>).mockReturnValue(
      new Date('2024-09-16T08:00:00')
    );

    const result = checkClosingStatus(closingTime);
    expect(result).toEqual(false);
  });
});
