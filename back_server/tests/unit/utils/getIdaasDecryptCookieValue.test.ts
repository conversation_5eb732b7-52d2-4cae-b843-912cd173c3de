import { createCipheriv, randomBytes } from 'crypto';
import { getIdaasDecryptCookieValue } from '../../../src/utils/getIdaasDecryptCookieValue';

describe('getIdaasDecryptCookieValue()', () => {
  // 復号化の対象となる暗号化ticketを作成する（getIdaasEncryptedCookieValueメソッドの処理と同じ）
  const userUid = 'Id12345';
  const emailAddress = '<EMAIL>';
  // Base64URLエンコード関数
  const base64UrlEncode = (buffer: Buffer): string =>
    buffer
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/g, '');
  // 乱数生成関数
  const generateNonce = (length: number) => {
    const characters = '0123456789!@#$%^&*()_+-=[]{};:\'",.<>?';
    return Array.from(
      randomBytes(length),
      (byte) => characters[byte % characters.length]
    ).join('');
  };
  const KEY = Buffer.from(process.env.IDAAS_COOKIE_ENCRYPTION_KEY ?? '', 'hex');
  // 共通化鍵をインプットにSHA-256(ECB)暗号化インスタンス生成
  const cipher = createCipheriv('aes-256-ecb', KEY, null);
  // UNIXタイムスタンプ生成
  const timeStamp = Math.round(Date.now() / 1000);
  // 半角数字記号10文字の乱数を生成
  const nonce = generateNonce(10);
  // 平文ticket生成
  const data = `${nonce}|${userUid}|${emailAddress}|${timeStamp}`;
  // 暗号化ticket生成
  const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
  // Base64URLエンコードした暗号化ticket（文字列）を格納
  const ticket = base64UrlEncode(encryptedData);
  // 暗号化ticketを作成完了

  it('getIdaasDecryptCookieValue() - 正常にnonceが復号されること', () => {
    const result = getIdaasDecryptCookieValue(ticket, 0);
    expect(result).toBe(nonce);
  });

  it('getIdaasDecryptCookieValue() - 正常にuserUidが復号されること', () => {
    const result = getIdaasDecryptCookieValue(ticket, 1);
    expect(result).toBe(userUid);
  });

  it('getIdaasDecryptCookieValue() - 正常にemailAddressが復号されること', () => {
    const result = getIdaasDecryptCookieValue(ticket, 2);
    expect(result).toBe(emailAddress);
  });

  it('getIdaasDecryptCookieValue() - 正常にtimeStampが復号されること', () => {
    const result = getIdaasDecryptCookieValue(ticket, 3);
    expect(result).toBe(timeStamp.toString());
  });
});
