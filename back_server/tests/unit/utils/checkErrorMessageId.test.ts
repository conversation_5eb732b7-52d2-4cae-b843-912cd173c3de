import { checkErrorMessageId } from '../../../src/utils/checkErrorMessageId';

describe('checkErrorMessageId', () => {
  it('trueErrorMessageIdsに含まれるIDの場合、trueを返す', () => {
    const trueIds = [
      'D0989',
      'C0025',
      'C0048',
      'C0049',
      'C0002',
      'C0036',
      'C0046',
      'C0005'
    ];
    trueIds.forEach((id) => {
      expect(checkErrorMessageId(id)).toBe(true);
    });
  });

  it('falseErrorMessageIdsに含まれるIDの場合、falseを返す', () => {
    const falseIds = [
      'E5110001',
      'E5110004',
      'E5110015',
      'E5110006',
      'C0007',
      'C0001',
      'C0026',
      'D1068',
      'S2001'
    ];
    falseIds.forEach((id) => {
      expect(checkErrorMessageId(id)).toBe(false);
    });
  });

  it('どちらのリストにも含まれないIDの場合、エラーを投げる', () => {
    const unknownId = 'X9999';
    expect(() => checkErrorMessageId(unknownId)).toThrow(
      `Unknown error message ID: ${unknownId}`
    );
  });
});
