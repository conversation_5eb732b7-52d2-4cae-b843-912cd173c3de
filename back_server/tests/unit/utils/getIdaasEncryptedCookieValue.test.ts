import { createCipheriv, randomBytes } from 'crypto';
import { getIdaasEncryptedCookieValue } from '../../../src/utils/getIdaasEncryptedCookieValue';

jest.mock('crypto', () => ({
  createCipheriv: jest.fn(),
  randomBytes: jest.fn()
}));

describe('getIdaasEncryptedCookieValue()', () => {
  // 各変数のモック値を設定
  const mockKey =
    '123456789012345678901234567890123456789012345678901234567890ABCD'; // 共通鍵（64バイトの半角英数字）
  const mockDomain = 'dev.biztest.smbc.co.jp'; // ドメイン
  const mockUserUid = 'testUserUid'; // userId
  const mockEmailAddress = 'testEmailAddress'; // emailAddress
  const mockTimeStamp = 1627380000; // UNIXタイムスタンプ
  const mockBytes = '1234567890'; // randomBytesの返却値
  const mockNonce = '#$%^&*()_@'; // 乱数
  // 暗号化クッキーに設定する固定値
  const path = '/';
  const secure = 'true';
  const httponly = 'true';

  let originalDateNow: () => number;

  beforeAll(() => {
    originalDateNow = Date.now;
    global.Date.now = jest.fn(() => mockTimeStamp * 1000);

    (randomBytes as jest.Mock).mockReturnValue(Buffer.from(mockBytes));
  });

  afterAll(() => {
    global.Date.now = originalDateNow;
    jest.resetAllMocks();
  });

  it('getIdaasEncryptedCookieValue() - 正常に暗号化クッキーが生成されること', () => {
    // cipherのモック化
    const mockCipherUpdate = jest
      .fn()
      .mockReturnValue(Buffer.from('encryptedDataPart1='));
    const mockCipherFinal = jest
      .fn()
      .mockReturnValue(Buffer.from('encryptedDataPart2'));
    const mockCipher = {
      update: mockCipherUpdate,
      final: mockCipherFinal
    };
    (createCipheriv as jest.Mock).mockReturnValue(mockCipher);

    // 平文ticketの期待値
    const expectedPlainTextTicket = `${mockNonce}|${mockUserUid}|${mockEmailAddress}|${mockTimeStamp}`;
    // 暗号化ticketの期待値
    const expectedEncryptedData = Buffer.concat([
      Buffer.from('encryptedDataPart1='),
      Buffer.from('encryptedDataPart2')
    ]);
    // 暗号化ticket(Base64URLエンコード後)の期待値
    const expectedTicket = expectedEncryptedData
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=*$/, '');

    // 各クッキー情報をオブジェクトとして保存
    const expectedCookieInfo = {
      ticket: expectedTicket,
      domain: mockDomain,
      path,
      secure,
      httponly
    };

    // 暗号化クッキー生成関数を実行
    const result = getIdaasEncryptedCookieValue(mockUserUid, mockEmailAddress);

    expect(createCipheriv).toHaveBeenCalledWith(
      'aes-256-ecb',
      Buffer.from(mockKey, 'hex'),
      null
    );
    expect(mockCipherUpdate).toHaveBeenCalledWith(expectedPlainTextTicket);
    expect(mockCipherFinal).toHaveBeenCalled();
    expect(result).toEqual(expectedCookieInfo);
  });
});
