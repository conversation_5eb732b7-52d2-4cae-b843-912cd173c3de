import { createCipheriv, randomBytes } from 'crypto';
import { EncryptedCookie } from '../../../src/models/encryptedCookie';
import { checkOtpAuthenticationResult } from '../../../src/utils/checkOtpAuthenticationResult';

describe('checkOtpAuthenticationResult()', () => {
  // テスト用の暗号化Cookie生成関数
  // UNIXタイムスタンプ生成のためのdayを引数で渡す
  const generateEncryptedCookie = (day: number): EncryptedCookie => {
    // 復号化の対象となる暗号化ticketを作成する（getIdaasEncryptedCookieValueメソッドの処理と同じ）
    const userUid = 'Id12345';
    const emailAddress = '<EMAIL>';
    // Base64URLエンコード関数
    const base64UrlEncode = (buffer: Buffer): string =>
      buffer
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/g, '');
    // 乱数生成関数
    const generateNonce = (length: number) => {
      const characters = '0123456789!@#$%^&*()_+-=[]{};:\'",.<>?';
      return Array.from(
        randomBytes(length),
        (byte) => characters[byte % characters.length]
      ).join('');
    };
    const KEY = Buffer.from(
      process.env.IDAAS_COOKIE_ENCRYPTION_KEY ?? '',
      'hex'
    );
    // 共通化鍵をインプットにSHA-256(ECB)暗号化インスタンス生成
    const cipher = createCipheriv('aes-256-ecb', KEY, null);
    // UNIXタイムスタンプ生成
    // const timeStamp = Math.round(Date.now() / 1000);
    const timeStamp = Math.round(day / 1000);

    // 半角数字記号10文字の乱数を生成
    const nonce = generateNonce(10);
    // 平文ticket生成
    const data = `${nonce}|${userUid}|${emailAddress}|${timeStamp}`;
    // 暗号化ticket生成
    const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
    // Base64URLエンコードした暗号化ticket（文字列）を格納
    const ticket = base64UrlEncode(encryptedData);
    // 暗号化ticketを作成完了

    const domain = process.env.IDAAS_COOKIE_DOMAIN ?? '';
    const path = '/';
    const secure = 'true';
    const httponly = 'true';

    const encryptedCookie: EncryptedCookie = {
      ticket: ticket,
      domain: domain,
      path: path,
      secure: secure,
      httponly: httponly
    };

    return encryptedCookie;
  };

  const today = Date.now();
  const yesterday = Date.now() - 24 * 60 * 60 * 1000;

  it('checkOtpAuthenticationResult() - 正常の場合、trueが返されること', () => {
    const dtpId = '<EMAIL>';
    const ticket = generateEncryptedCookie(yesterday).ticket!;
    const idassTicket = generateEncryptedCookie(today).ticket!;

    const result = checkOtpAuthenticationResult(dtpId, ticket, idassTicket);

    expect(result).toBe(true);
  });

  it('checkOtpAuthenticationResult() - メールアドレスが一致していない場合、falseが返されること', () => {
    const dtpId = '<EMAIL>';
    const ticket = generateEncryptedCookie(yesterday).ticket!;
    const idassTicket = generateEncryptedCookie(today).ticket!;

    const result = checkOtpAuthenticationResult(dtpId, ticket, idassTicket);

    expect(result).toBe(false);
  });

  it('checkOtpAuthenticationResult() - 暗号化Cookieのticketが同一の場合、falseが返されること', () => {
    const dtpId = '<EMAIL>';
    const ticket = generateEncryptedCookie(today).ticket!;
    const idassTicket = generateEncryptedCookie(today).ticket!;

    const result = checkOtpAuthenticationResult(dtpId, ticket, idassTicket);

    expect(result).toBe(false);
  });

  it('checkOtpAuthenticationResult() - IDaaSで発行した暗号化Cookieのticketのタイムスタンプが古い場合、falseが返されること', () => {
    const dtpId = '<EMAIL>';
    const ticket = generateEncryptedCookie(today).ticket!;
    const idassTicket = generateEncryptedCookie(yesterday).ticket!;

    const result = checkOtpAuthenticationResult(dtpId, ticket, idassTicket);

    expect(result).toBe(false);
  });
});
