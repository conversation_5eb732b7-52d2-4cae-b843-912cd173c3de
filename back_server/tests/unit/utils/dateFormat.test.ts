import { addMinutes, format, getUnixTime } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import { advanceTo, clear } from 'jest-date-mock';
import {
  DATE_FORMAT_HYPHEN,
  jstTime,
  JST,
  serverDate,
  DATE_FORMAT_YYYY_MM_DD,
  DATE_FORMAT_SLASH,
  serverTime,
  TIME_FORMAT,
  serverDateTime,
  unixTime,
  expirationTime,
  yyyymmddhhmmss,
  changeDateFormat,
  convertToDateTimeSlash
} from '../../../src/utils/dateFormat';

describe('dateFormat', () => {
  const { env } = process;
  beforeEach(() => {
    jest.resetModules(); // importのキャッシュを削除
    process.env = { ...env }; // 環境変数を退避
    advanceTo(new Date(2024, 1, 1, 0, 0, 0)); // テスト前に日付をモック化(2024-02-01 00:00:00に設定)
  });

  afterEach(() => {
    clear(); // テスト後に日付のモックをクリア
  });

  it('jstTime()がJST時刻を返すこと', () => {
    const mockDate = new Date();
    const expected = format(utcToZonedTime(mockDate, JST), DATE_FORMAT_HYPHEN);
    const result = jstTime();

    expect(result).toEqual(expected);
  });

  it('serverDate()の引数にDATE_FORMAT_SLASHを渡した場合、yyyy/M/d形式を返すこと', () => {
    const mockDate = new Date();
    const expected = format(utcToZonedTime(mockDate, JST), DATE_FORMAT_SLASH);
    const result = serverDate(DATE_FORMAT_SLASH);

    expect(result).toEqual(expected);
  });

  it('serverDate()の引数にDATE_FORMAT_YYYY_MM_DDを渡した場合、yyyy-MM-dd形式を返すこと', () => {
    const mockDate = new Date();
    const expected = format(
      utcToZonedTime(mockDate, JST),
      DATE_FORMAT_YYYY_MM_DD
    );
    const result = serverDate(DATE_FORMAT_YYYY_MM_DD);

    expect(result).toEqual(expected);
  });

  it('convertToDateTimeSlash()の引数に時刻情報を渡した場合、YYYY/M/D H:mm形式を返すこと', () => {
    const mockDate = '2025-01-01T09:00:00+09:00';
    const result = convertToDateTimeSlash(mockDate);
    const expected = '2025/1/1 9:00';

    expect(result).toEqual(expected);
  });

  it('convertToDateTimeSlash()の引数にnullを渡した場合、nullを返すこと', () => {
    const mockDate = null;
    const result = convertToDateTimeSlash(mockDate);

    expect(result).toEqual(null);
  });

  it('convertToDateTimeSlash()の引数に時刻以外の情報を渡した場合、nullを返すこと', () => {
    const mockDate = 'エラー';
    const result = convertToDateTimeSlash(mockDate);

    expect(result).toEqual(null);
  });

  it('changeDateFormat()の引数にDATE_FORMAT_SLASHを渡した場合、yyyy/M/d形式を返すこと', () => {
    const mockDate = new Date();
    const expected = format(utcToZonedTime(mockDate, JST), DATE_FORMAT_SLASH);
    const result = changeDateFormat(DATE_FORMAT_SLASH, mockDate);

    expect(result).toEqual(expected);
  });

  it('changeDateFormat()の引数にDATE_FORMAT_YYYY_MM_DDを渡した場合、yyyy-MM-dd形式を返すこと', () => {
    const mockDate = new Date();
    const expected = format(
      utcToZonedTime(mockDate, JST),
      DATE_FORMAT_YYYY_MM_DD
    );
    const result = changeDateFormat(DATE_FORMAT_YYYY_MM_DD, mockDate);

    expect(result).toEqual(expected);
  });

  it('serverTime()がH:mm形式を返すこと', () => {
    const mockDate = new Date();
    const expected = format(utcToZonedTime(mockDate, JST), TIME_FORMAT);
    const result = serverTime();

    expect(result).toEqual(expected);
  });

  it('serverDateTime()がyyyy/M/d H:mm形式を返すこと', () => {
    const mockDate = new Date();
    const expectedServerTime = format(
      utcToZonedTime(mockDate, JST),
      TIME_FORMAT
    );
    const expected = `${format(
      utcToZonedTime(mockDate, JST),
      DATE_FORMAT_SLASH
    )} ${expectedServerTime}`;
    const result = serverDateTime();

    expect(result).toEqual(expected);
  });

  it('unixTime()がUNIX時刻を返すこと', () => {
    const mockDate = new Date();
    const expected = getUnixTime(mockDate);
    const result = unixTime();
    expect(result).toEqual(expected);
  });

  it('環境変数ADD_MINUTESが取得できた場合、expirationTime()が「ADD_MINUTESの値」分後のUNIX時刻を返すこと', async () => {
    process.env.ADD_MINUTES = '10';
    await import('../../../src/utils/dateFormat');
    const mockDate = new Date();
    const expected = getUnixTime(addMinutes(mockDate, 10)).toString();
    const result = expirationTime();
    expect(result).toEqual(expected);
  });

  it('環境変数LONGER_ADD_MINUTESが取得できた場合、expirationTime(ture)が「LONGER_ADD_MINUTESの値」分後のUNIX時刻を返すこと', async () => {
    process.env.LONGER_ADD_MINUTES = '30';
    await import('../../../src/utils/dateFormat');
    const mockDate = new Date();
    const expected = getUnixTime(addMinutes(mockDate, 30)).toString();
    const result = expirationTime(true);
    expect(result).toEqual(expected);
  });

  it('環境変数ADD_MINUTESが取得できなかった場合、expirationTime()が10分後のUNIX時刻を返すこと', async () => {
    delete process.env.ADD_MINUTES;
    delete process.env.LONGER_ADD_MINUTES;
    await import('../../../src/utils/dateFormat');
    const mockDate = new Date();
    const expected = getUnixTime(addMinutes(mockDate, 10)).toString();
    const result = expirationTime();
    expect(result).toEqual(expected);
  });

  it('yyyymmddhhmmss()が文字列でyyyymmddhhmmss形式を返すこと', () => {
    const mockDate = new Date();
    const expected = `${mockDate.getFullYear()}${(mockDate.getMonth() + 1)
      .toString()
      .padStart(2, '0')}${mockDate
      .getDate()
      .toString()
      .padStart(2, '0')}${mockDate
      .getHours()
      .toString()
      .padStart(2, '0')}${mockDate
      .getMinutes()
      .toString()
      .padStart(2, '0')}${mockDate.getSeconds().toString().padStart(2, '0')}`;
    const result = yyyymmddhhmmss(mockDate);

    expect(result).toEqual(expected);
  });
});
