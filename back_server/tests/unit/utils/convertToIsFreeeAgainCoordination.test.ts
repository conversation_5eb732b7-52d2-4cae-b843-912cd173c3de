import { convertToIsFreeeReConnect } from '../../../src/utils/convertToIsFreeeReConnect';

describe('convertToIsFreeeReConnect', () => {
  it('syncStatusがsuccess場合、0を返す', () => {
    const falseSyncStatus = 'success';
    expect(convertToIsFreeeReConnect(falseSyncStatus)).toBe('0');
  });

  it('syncStatusがdisabled場合、0を返す', () => {
    const falseSyncStatus = 'disabled';
    expect(convertToIsFreeeReConnect(falseSyncStatus)).toBe('0');
  });

  it('syncStatusがsyncingの場合、0を返す', () => {
    const falseSyncStatus = 'syncing';
    expect(convertToIsFreeeReConnect(falseSyncStatus)).toBe('0');
  });

  it('syncStatusがtoken_refresh_errorの場合、1を返す', () => {
    const trueSyncStatus = 'token_refresh_error';
    expect(convertToIsFreeeReConnect(trueSyncStatus)).toBe('1');
  });

  it('syncStatusがother_errorの場合、1を返す', () => {
    const trueSyncStatus = 'other_error';
    expect(convertToIsFreeeReConnect(trueSyncStatus)).toBe('1');
  });

  it('syncStatusがnullの場合、0を返す', () => {
    const falseSyncStatus = null;
    expect(convertToIsFreeeReConnect(falseSyncStatus)).toBe('0');
  });
});
