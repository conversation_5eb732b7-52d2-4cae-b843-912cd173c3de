import { convertToUserAuthStatus } from '../../../src/utils/convertToUserAuthStatus';

describe('convertToUserAuthStatus', () => {
  it('approvalAuth=true,approvalOnSmartPhoneAuth=true場合、3を返す', () => {
    expect(convertToUserAuthStatus(true, true)).toBe('3');
  });

  it('approvalAuth=true,approvalOnSmartPhoneAuth=false場合、2を返す', () => {
    expect(convertToUserAuthStatus(true, false)).toBe('2');
  });

  it('approvalAuth=false,approvalOnSmartPhoneAuth=true場合、1を返す', () => {
    expect(convertToUserAuthStatus(false, true)).toBe('1');
  });

  it('approvalAuth=false,approvalOnSmartPhoneAuth=false場合、0を返す', () => {
    expect(convertToUserAuthStatus(false, false)).toBe('0');
  });
});
