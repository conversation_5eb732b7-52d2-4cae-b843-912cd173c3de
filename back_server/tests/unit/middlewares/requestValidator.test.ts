/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { validationResult } from 'express-validator';
import { Request, Response } from 'express';
import { validate } from '../../../src/middlewares/requestValidator';
import { ValidationError } from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';
import { logger } from '../../../src/utils/logger';

jest.mock('express-validator', () => ({
  validationResult: jest.fn()
}));

describe('requestValidator', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('バリデーションエラーが発生していない場合はnext()が呼ばれること', () => {
    const mockReq = {} as Request;
    const mockRes = {} as Response;
    const mockNext = jest.fn();

    // validationResultがエラーを返さないように設定
    (validationResult as unknown as jest.Mock).mockReturnValue({
      isEmpty: () => true
    });

    validate(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalled();
  });

  it('バリデーションエラーが発生した場合はINVALID_REQUESTのエラーをnext()に渡すこと', () => {
    const mockReq = {} as Request;
    const mockRes = {} as Response;
    const mockNext = jest.fn();

    // validationResultがエラーを返すように設定
    (validationResult as unknown as jest.Mock).mockReturnValue({
      isEmpty: () => false,
      array: () => ['error1', 'error2']
    });

    validate(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(ValidationError);
    expect(mockNext.mock.calls[0][0].errorCode).toEqual(
      errorCodes.INVALID_REQUEST
    );
  });
});
