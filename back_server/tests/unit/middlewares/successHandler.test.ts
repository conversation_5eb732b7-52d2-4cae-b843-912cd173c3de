import { Request, Response, NextFunction } from 'express';
import { onEnd, onSuccess } from '../../../src/middlewares/successHandler';
import { updateExpirationTime } from '../../../src/services/sessionService';
import { Session, UserInfo } from '../../../src/models/session';
import { expirationTime, jstTime } from '../../../src/utils/dateFormat';
import { logger } from '../../../src/utils/logger';

// sessionServiceをモック
jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('successHandler', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onSuccess', () => {
    it('正常系_エラーが発生していない場合、updateExpirationTimeが呼ばれること', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockUpdateExpirationTime: Session = {
        sessionId: 'sessionId',
        expirationTime: '1699507328',
        updatedAt: '2023-10-02 10:00:00 GMT+0'
      };

      (
        expirationTime as jest.MockedFunction<typeof expirationTime>
      ).mockReturnValue('1699507328');
      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-02 10:00:00 GMT+0'
      );

      await onSuccess(mockRequest, mockResponse, mockNext);

      expect(updateExpirationTime).toHaveBeenCalledWith(
        mockUpdateExpirationTime
      );
    });

    it('異常系_有効期限更新に失敗した場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      (updateExpirationTime as jest.Mock).mockRejectedValue(
        new Error('DB Error')
      );

      await onSuccess(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('DB Error'));
    });
  });
  describe('onEnd', () => {
    it('正常系_onEndが呼ばれた場合、logger.writeMsgが呼ばれること', () => {
      const mockRequest = {} as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        on: jest.fn((event: string, callback: Function) => {
          if (event === 'finish') {
            callback();
          }
        })
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      onEnd(mockRequest, mockResponse, mockNext);

      expect(logger.writeMsg).toHaveBeenCalledWith('ICOM000001', ['終了']);
    });

    it('異常系_エラーが発生した場合、エラーを返す', () => {
      const mockRequest = {} as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
        on: jest.fn((event: string) => {
          if (event === 'finish') {
            throw new Error('my Error');
          }
        })
      } as unknown as Response;
      const mockNext = jest.fn((error: any) => {
        expect(error).toBeInstanceOf(Error);
      }) as NextFunction;

      onEnd(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
