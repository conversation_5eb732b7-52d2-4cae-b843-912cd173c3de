import { Request, Response, NextFunction } from 'express';
import { logger } from '../../../src/utils/logger';
import { onStart } from '../../../src/middlewares/startHandler';

// sessionServiceをモック
jest.mock('../../../src/utils/logger');

describe('successHandler', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onStart', () => {
    it('正常系_onStartが呼ばれた場合、logger.writeMsgが呼ばれること', () => {
      const mockRequest = {
        header: jest.fn().mockReturnValue('123.123.123.123')
      } as unknown as Request;
      const mockResponse = {} as Response;
      const mockNext = jest.fn() as NextFunction;

      const wtiteMsgMock = jest
        .spyOn(logger, 'writeMsg')
        .mockImplementation(() => {});

      onStart(mockRequest, mockResponse, mockNext);

      expect(wtiteMsgMock).toHaveBeenCalledWith('ICOM000001', ['開始']);
      expect(mockNext).toHaveBeenCalled();
      wtiteMsgMock.mockRestore();
    });

    it('異常系_エラーが発生した場合、エラーを返す', () => {
      const mockRequest = {
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {} as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const wtiteMsgMock = jest
        .spyOn(logger, 'writeMsg')
        .mockImplementation(() => {
          throw new Error('my Error');
        });

      onStart(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
      wtiteMsgMock.mockRestore();
    });
  });
});
