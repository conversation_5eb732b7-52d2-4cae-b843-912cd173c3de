/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction } from 'express';
import { authenticateUser } from '../../../src/middlewares/authMiddleware';
import dynamoDBClient from '../../../src/utils/dynamoDB';
import { getSession } from '../../../src/services/sessionService';
import { Session, UserInfo } from '../../../src/models/session';
import { unixTime } from '../../../src/utils/dateFormat';
import { AuthorizationError } from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';
import { logger } from '../../../src/utils/logger';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('authMiddleware', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('正常系_バリデーションエラーが発生していない場合はnext()が呼ばれること', async () => {
    const mockReq = {
      headers: {
        authorization: 'Bearer mockSessionId'
      }
    } as unknown as Request;
    const mockRes = {} as Response;
    const mockNext = jest.fn() as NextFunction;

    // モックデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '09011111111',
      kigyoCd: '12345678912345',
      userAuths: {
        web21: null,
        gets: null
      }
    };

    const mockSession: Session = {
      sessionId: 'mocked-uuid',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '1699507328',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    // モックデータを返却
    (getSession as jest.Mock).mockResolvedValueOnce(mockSession);
    (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
      1699507328
    );

    await authenticateUser(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalled();
  });

  it('異常系_authHeaderがない場合はAUTHORIZATION_ERRORのエラーがnext()に渡されること', async () => {
    const mockReq = { originalUrl: '/some/endpoint' } as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn();

    await authenticateUser(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(AuthorizationError);
    expect(mockNext.mock.calls[0][0].errorCode).toEqual(
      errorCodes.AUTHORIZATION_ERROR
    );
  });
  it('異常系_sessionがない場合はNOT_AUTHORIZATIONのエラーがnext()に渡されること', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    const mockReq = {
      headers: {
        authorization: 'Bearer mockSessionId'
      },
      originalUrl: '/some/endpoint'
    } as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn();

    // モックデータ
    const mockResponse = {};

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    await authenticateUser(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(AuthorizationError);
    expect(mockNext.mock.calls[0][0].errorCode).toEqual(
      errorCodes.NOT_AUTHORIZATION
    );
  });
  it('異常系_expirationTimeが現在時刻よりも前の場合はEXPIRED_SESSIONのエラーがnext()に渡されること', async () => {
    const mockReq = {
      headers: {
        authorization: 'Bearer mockSessionId'
      },
      originalUrl: '/some/endpoint'
    } as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn();

    // モックデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '09011111111',
      kigyoCd: '12345678912345',
      userAuths: {
        web21: null,
        gets: null
      }
    };

    const mockSession: Session = {
      sessionId: 'mocked-uuid',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '1699507328',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    // モックデータを返却
    (getSession as jest.Mock).mockResolvedValueOnce(mockSession);
    (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
      1699507329
    );

    await authenticateUser(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0]).toBeInstanceOf(AuthorizationError);
    expect(mockNext.mock.calls[0][0].errorCode).toEqual(
      errorCodes.EXPIRED_SESSION
    );
  });
});
