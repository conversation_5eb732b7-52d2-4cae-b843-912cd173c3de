import { Request, Response, NextFunction, response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import errorHandler from '../../../src/middlewares/errorHandler';
import { ErrorResponse } from '../../../src/models/errorResponse';
import { logger } from '../../../src/utils/logger';

jest.mock('../../../src/index');

declare global {
  namespace Express {
    interface Response {
      // errorMasterを拡張
      errorMaster: Promise<Record<string, AttributeValue>[] | undefined>;
    }
  }
}

describe('errorHandler', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('異常系_バックエンドから400エラーが返ってきた場合、HTTPステータスコード400とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 400
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(400);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage:
        '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
    });
  });

  it('異常系_バックエンドから401エラーが返ってきた場合、HTTPステータスコード401とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 401
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(401);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage:
        '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
    });
  });

  it('異常系_バックエンドから403エラーが返ってきた場合、HTTPステータスコード403とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 403
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(403);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage:
        '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
    });
  });

  it('異常系_バックエンドから404エラーが返ってきた場合、HTTPステータスコード404とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 404
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(404);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage:
        '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
    });
  });

  it('異常系_バックエンドから409エラーが返ってきた場合、HTTPステータスコード409とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 409
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(409);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage:
        '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
    });
  });

  it('異常系_バックエンドから500エラーが返ってきた場合、HTTPステータスコード500とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 500
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(500);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage:
        '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
    });
  });

  it('異常系_バックエンドから必須項目のみのエラーが返ってきた場合、HTTPステータスコード500とエラーコード、メッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message'
        },
        status: 500
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: '現在データが取得できません。暫く経ってから再度取得お願いいたします。'
        },
        errorCode: { S: 'E005-00009' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(500);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: 'E005-00009',
      errorMessage:
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
    });
  });

  it('異常系_BFF内部で400エラーが発生した場合、HTTPステータスコード400とエラーコード、メッセージを返すこと', async () => {
    const bffError = {
      statusCode: 400,
      errorCode: '*********',
      errorDetail: {}
    };
    const mockErr = bffError as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    // エラーマスタデータを設定
    response.errorMaster = [
      {
        errorMessage: {
          S: 'バリデーションエラー'
        },
        errorCode: { S: '*********' }
      }
    ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(400);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: '*********',
      errorMessage: 'バリデーションエラー',
      hasFaq: false
    });
  });

  it('異常系_バックエンドから返却されたエラーコードとDynamoDBのエラーコードが一致しない場合、特定のエラーコードとメッセージを返すこと', async () => {
    const backendErrorResponse = {
      response: {
        data: {
          code: '*********',
          message: 'message',
          details: 'errorDetail'
        },
        status: 500
      }
    };
    const mockErr = backendErrorResponse as ErrorResponse;
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(500);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: 'E005-00009',
      errorMessage:
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
    });
  });

  it('異常系_BFF内部でもバックエンドでもないエラーの場合、特定のエラーコードとメッセージを返すこと', async () => {
    const mockErr = new Error('my Error');
    const mockReq = {} as unknown as Request;
    const mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    await errorHandler(mockErr, mockReq, mockRes);

    expect(mockRes.status).toHaveBeenCalledWith(500);
    expect(mockRes.json).toHaveBeenCalledWith({
      errorCode: 'E005-00009',
      errorMessage:
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。',
      hasFaq: false
    });
  });
});
