import { v4 as mockUuidv4 } from 'uuid';
import { logger } from '../../../src/utils/logger';
import { saveHighRiskUser } from '../../../src/services/highRiskUserService';
import { judgeRisk } from '../../../src/services/judgeRiskService';
import { Ids } from '../../../src/types/ids';
import { ValueDoorUser } from '../../../src/types/valueDoorUser';
import { UserInfo } from '../../../src/models/session';
import { HighRiskUser } from '../../../src/models/highRiskUser';
import { expirationTime, jstTime } from '../../../src/utils/dateFormat';
import { AuthorizationError } from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';

jest.mock('uuid');
jest.mock('../../../src/services/highRiskUserService');
jest.mock('../../../src/utils/dateFormat');

describe('judgeRiskService', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('正常系_judgeRisk_DTPIDログイン_危険度がLの時何も返却しない', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企s業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '<EMAIL>',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    const judgeRiskResult = 'L';
    const loginKind = '1';

    const result = await judgeRisk(
      ids,
      issueInstant,
      valueDoorUser,
      judgeRiskResult,
      loginKind
    );

    // 何も返却されていないことの確認
    expect(result).toBeUndefined();
  });

  it('正常系_judgeRisk_VDIDログイン_危険度がLの時何も返却しない', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企s業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '<EMAIL>',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    const judgeRiskResult = 'L';
    const loginKind = '2';

    const result = await judgeRisk(
      ids,
      issueInstant,
      valueDoorUser,
      judgeRiskResult,
      loginKind
    );

    // 何も返却されていないことの確認
    expect(result).toBeUndefined();
  });

  it('正常系_judgeRisk_DTPIDログイン_危険度がMの時highRiskUserIdを返却する', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '<EMAIL>',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    // モックデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '*************',
      clientId: '**********',
      compName: '東京SAML企業',
      userSeiMei: '東京さむる太郎',
      compAtrbt: '01',
      userKn: 'トウキョウ サムルタロウ',
      userTyp: '01',
      Email: '<EMAIL>',
      telNo: '***********',
      kigyoCd: '**************',
      keiyakuType: '2',
      userAuths: {
        web21: null,
        gets: null
      },
      loginType: '214'
    };

    const mockHighRiskUser: HighRiskUser = {
      highRiskUserId: 'mockHighRiskUserId',
      vdId: '**********',
      dtpId: '<EMAIL>',
      userId: 'userId',
      issueInstant: new Date('2024-07-16T12:40:00.000Z').toString(),
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '1699507328',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    const expectResult = {
      highRiskUserId: 'mockHighRiskUserId'
    };

    const judgeRiskResult = 'M';
    const loginKind = '1';

    // ランダムに払い出されてしまう部分のみモック
    const mockedUUID = 'mockHighRiskUserId';
    (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
      mockedUUID
    );
    (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
      '2023-10-01 10:00:00 GMT+0'
    );
    (
      expirationTime as jest.MockedFunction<typeof expirationTime>
    ).mockReturnValue('1699507328');

    const result = await judgeRisk(
      ids,
      issueInstant,
      valueDoorUser,
      judgeRiskResult,
      loginKind
    );

    expect(saveHighRiskUser).toHaveBeenCalledWith(mockHighRiskUser);
    expect(result).toEqual(expectResult);
  });

  it('正常系_judgeRisk_VDIDログイン_危険度がMの時highRiskUserIdを返却する', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '<EMAIL>',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    // モックデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '*************',
      clientId: '**********',
      compName: '東京SAML企業',
      userSeiMei: '東京さむる太郎',
      compAtrbt: '01',
      userKn: 'トウキョウ サムルタロウ',
      userTyp: '01',
      Email: '<EMAIL>',
      telNo: '***********',
      kigyoCd: '**************',
      keiyakuType: '2',
      userAuths: {
        web21: null,
        gets: null
      },
      loginType: '214'
    };

    const mockHighRiskUser: HighRiskUser = {
      highRiskUserId: 'mockHighRiskUserId',
      vdId: '**********',
      dtpId: '<EMAIL>',
      userId: 'userId',
      issueInstant: new Date('2024-07-16T12:40:00.000Z').toString(),
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '1699507328',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    const expectResult = {
      highRiskUserId: 'mockHighRiskUserId'
    };

    const judgeRiskResult = 'M';
    const loginKind = '2';

    // ランダムに払い出されてしまう部分のみモック
    const mockedUUID = 'mockHighRiskUserId';
    (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
      mockedUUID
    );
    (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
      '2023-10-01 10:00:00 GMT+0'
    );
    (
      expirationTime as jest.MockedFunction<typeof expirationTime>
    ).mockReturnValue('1699507328');

    const result = await judgeRisk(
      ids,
      issueInstant,
      valueDoorUser,
      judgeRiskResult,
      loginKind
    );

    expect(saveHighRiskUser).toHaveBeenCalledWith(mockHighRiskUser);
    expect(result).toEqual(expectResult);
  });

  it('異常系_judgeRisk_VDIDログイン_危険度がMかつVDIDのメールアドレスがない場合、エラーを返却する', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    const judgeRiskResult = 'M';
    const loginKind = '2';

    // ランダムに払い出されてしまう部分のみモック
    const mockedUUID = 'mockHighRiskUserId';
    (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
      mockedUUID
    );
    (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
      '2023-10-01 10:00:00 GMT+0'
    );
    (
      expirationTime as jest.MockedFunction<typeof expirationTime>
    ).mockReturnValue('1699507328');

    try {
      await judgeRisk(
        ids,
        issueInstant,
        valueDoorUser,
        judgeRiskResult,
        loginKind
      );
    } catch (error) {
      expect(error).toBeInstanceOf(AuthorizationError);
      expect((error as AuthorizationError).statusCode).toBe(401);
      expect((error as AuthorizationError).errorCode).toEqual(
        errorCodes.NOT_EMAIL_WITH_VDID
      );
      expect((error as AuthorizationError).errorDetail).toBe(
        'VDIDに紐づくメールアドレス存在チェックエラー'
      );
    }
  });

  it('異常系_judgeRisk_DTPIDログイン_危険度がHの時エラーを返却する', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企s業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '<EMAIL>',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    const judgeRiskResult = 'H';
    const loginKind = '1';

    try {
      await judgeRisk(
        ids,
        issueInstant,
        valueDoorUser,
        judgeRiskResult,
        loginKind
      );
    } catch (error) {
      expect(error).toBeInstanceOf(AuthorizationError);
      expect((error as AuthorizationError).statusCode).toBe(401);
      expect((error as AuthorizationError).errorCode).toEqual('I005-00012');
      expect((error as AuthorizationError).errorDetail).toBe(
        '怪しさ判定APIによるエラー'
      );
    }
  });

  it('異常系_judgeRisk_VDIDログイン_危険度がHの時エラーを返却する', async () => {
    // テストデータ
    const ids: Ids = {
      dtpId: '<EMAIL>',
      userUid: 'userId',
      valueDoorId: '**********'
    };

    const issueInstant = new Date('2024-07-16T12:40:00.000Z');

    const valueDoorUser: ValueDoorUser = {
      id: '**********',
      authenticationMethod: '01',
      representativeAccount: '*************',
      clientId: '**********',
      companyName: '東京SAML企s業',
      userName: '東京さむる太郎',
      userType: '01',
      accessLevel: '01',
      usernameKana: 'トウキョウ サムルタロウ',
      email: '<EMAIL>',
      telNo: '***********',
      companyCode: '**************',
      loginType: '214',
      contractType: '2',
      authorizations: {
        web21: null,
        gets: null
      },
      otpId: undefined,
      otpSerialNo: undefined,
      usesOtp: undefined,
      otpKind: undefined,
      otpDeviceStatus: undefined,
      isOtpReissuing: undefined,
      otpSerialNoReissue: undefined,
      otpKindReissue: undefined,
      otpDeviceStatusReissue: undefined,
      otpReissueAction: undefined,
      otpNonOtpExpiredDate: undefined,
      otpServiceCode: undefined
    };

    const judgeRiskResult = 'H';
    const loginKind = '2';

    try {
      await judgeRisk(
        ids,
        issueInstant,
        valueDoorUser,
        judgeRiskResult,
        loginKind
      );
    } catch (error) {
      expect(error).toBeInstanceOf(AuthorizationError);
      expect((error as AuthorizationError).statusCode).toBe(401);
      expect((error as AuthorizationError).errorCode).toEqual('I005-00012');
      expect((error as AuthorizationError).errorDetail).toBe(
        '怪しさ判定APIによるエラー'
      );
    }
  });
});
