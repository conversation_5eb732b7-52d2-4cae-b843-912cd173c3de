import { GetItemCommand } from '@aws-sdk/client-dynamodb';
import { getWorkBlockage } from '../../../src/services/workBlockageServices';
import dynamoDBClient from '../../../src/utils/dynamoDB';
import { workBlockageMaintenanceTableName } from '../../../src/config/config';
import { logger } from '../../../src/utils/logger';
import { WorkBlockage } from '../../../src/models/workBlockage';

// Commandをモック化
jest.mock('@aws-sdk/client-dynamodb', () => ({
  PutItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn()
}));
// dynamoDBClientのsendメソッドをモック化
jest.mock('../../../src/utils/dynamoDB', () => ({
  send: jest.fn()
}));

describe('sessionService', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('正常系_getWorkBlockage_機能閉塞情報を取得', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const functionId = '0101';

    // 返却値を定義
    const workBlockage: WorkBlockage = {
      status: '0',
      functionId,
      functionName: 'テスト'
    };
    const mockResponse = {
      Item: {
        status: { S: workBlockage.status },
        function_id: { S: functionId },
        function_name: { S: workBlockage.functionName }
      }
    };

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    const response = await getWorkBlockage(functionId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: workBlockageMaintenanceTableName,
      Key: {
        function_id: { S: functionId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(response).toStrictEqual(workBlockage);
  });

  it('正常系_getWorkBlockage_DynamoDBからユーザー情報を取得できなかった場合にはnullを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const functionId = '0101';

    // モックデータ
    const mockResponse = {};

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    const response = await getWorkBlockage(functionId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: workBlockageMaintenanceTableName,
      Key: {
        function_id: { S: functionId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(response).toStrictEqual(null);
  });

  it('異常系_getWorkBlockage_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const functionId = '0101';

    // エラーがthrowされることを確認
    await expect(getWorkBlockage(functionId)).rejects.toThrow('DB Error');

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: workBlockageMaintenanceTableName,
      Key: {
        function_id: { S: functionId }
      }
    });
  });
});
