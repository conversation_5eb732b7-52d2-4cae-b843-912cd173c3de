import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { getErrorMaster } from '../../../src/services/errorService';
import { errorMasterTableName } from '../../../src/config/config';
import dynamoDBClient from '../../../src/utils/dynamoDB';
import { logger } from '../../../src/utils/logger';

jest.mock('@aws-sdk/lib-dynamodb');
jest.mock('../../../src/utils/dynamoDB');

describe('errorService', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('正常系_getErrorMaster_DynamoDBからerrorMasterを取得する', async () => {
    // テストデータ
    const mockScanCommandResponse = {
      Items: [
        { errorCode: '001', errorMessage: 'Error 001', hasFaq: true },
        { errorCode: '002', errorMessage: 'Error 002', hasFaq: false }
      ]
    };
    // モックデータ
    const mockDocClientInstance = {
      send: jest.fn().mockResolvedValue(mockScanCommandResponse)
    };
    (DynamoDBDocumentClient.from as jest.Mock).mockReturnValue(
      mockDocClientInstance
    );

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const result = await getErrorMaster();

    // 返却値の確認
    expect(result).toEqual(mockScanCommandResponse.Items);
    // fromが正しく呼び出されたことを確認
    expect(DynamoDBDocumentClient.from).toHaveBeenCalledWith(dynamoDBClient);
    // sendが正しく呼び出されたことを確認
    expect(mockDocClientInstance.send).toHaveBeenCalledWith(
      expect.objectContaining({
        input: {
          TableName: errorMasterTableName,
          ProjectionExpression: '#ErrorCode, #ErrorMessage, #HasFaq',
          ExpressionAttributeNames: {
            '#ErrorCode': 'errorCode',
            '#ErrorMessage': 'errorMessage',
            '#HasFaq': 'hasFaq'
          }
        },
        // middlwareの返却値はanyで回避
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        middlewareStack: expect.any(Object)
      })
    );
  });

  it('異常系_getErrorMaster_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    // モックデータ
    const mockError = new Error('DynamoDB Error');
    const mockDocClientInstance = {
      send: jest.fn().mockRejectedValue(mockError)
    };
    (DynamoDBDocumentClient.from as jest.Mock).mockReturnValue(
      mockDocClientInstance
    );

    // エラーがthrowされることを確認
    await expect(getErrorMaster()).rejects.toThrow(mockError);

    // fromが正しく呼び出されたことを確認
    expect(DynamoDBDocumentClient.from).toHaveBeenCalledWith(dynamoDBClient);
    // sendが正しく呼び出されたことを確認
    expect(mockDocClientInstance.send).toHaveBeenCalled();
  });
});
