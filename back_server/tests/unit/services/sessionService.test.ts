import {
  PutItemCommand,
  GetItemCommand,
  UpdateItemCommand,
  DeleteItemCommand
} from '@aws-sdk/client-dynamodb';
import {
  getUserInfo,
  getSession,
  saveSession,
  updateState,
  deleteSession,
  updateExpirationTime,
  updateFreeeOauthState,
  updateDtpIdAndUserId,
  updateDtpId
} from '../../../src/services/sessionService';
import { Session, UserInfo } from '../../../src/models/session';
import dynamoDBClient from '../../../src/utils/dynamoDB';
import { sessionTableName } from '../../../src/config/config';
import { logger } from '../../../src/utils/logger';

// Commandをモック化
jest.mock('@aws-sdk/client-dynamodb', () => ({
  PutItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn()
}));
// dynamoDBClientのsendメソッドをモック化
jest.mock('../../../src/utils/dynamoDB', () => ({
  send: jest.fn()
}));

describe('sessionService', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('正常系_saveSession_DynamoDBにセッションIDを保存する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '***********',
      kigyoCd: '**************',
      userAuths: {
        web21: null,
        gets: null
      }
    };

    const sessionData: Session = {
      sessionId: 'test-session',
      vdId: 'ABCDE12345',
      dtpId: undefined,
      userId: undefined,
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '**********',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await saveSession(sessionData);

    // PutItemCommandが正しく呼び出されたことを確認
    expect(PutItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Item: {
        sessionId: { S: sessionData.sessionId },
        vdId: { S: sessionData.vdId! },
        dtpId: { NULL: true },
        userId: { NULL: true },
        issueInstant: { S: sessionData.issueInstant },
        userInfo: { S: JSON.stringify(sessionData.userInfo) },
        state: { NULL: true },
        freeeOauthState: { NULL: true },
        expirationTime: { N: sessionData.expirationTime },
        createdAt: { S: sessionData.createdAt },
        updatedAt: { S: sessionData.updatedAt }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(PutItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });

  it('正常系_updateState_セッションIDをもとにDynamoDBにstateを保存する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const sessionData: Session = {
      sessionId: 'session',
      state: '45we65r76tiuh',
      updatedAt: new Date().toISOString()
    };

    await updateState(sessionData);

    // UpdateItemCommandが正しく呼び出されたことを確認
    expect(UpdateItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionData.sessionId }
      },
      ExpressionAttributeNames: {
        '#s': 'state',
        '#u': 'updatedAt'
      },
      ExpressionAttributeValues: {
        ':newState': { S: sessionData.state },
        ':newUpdatedAt': { S: sessionData.updatedAt }
      },
      UpdateExpression: 'SET #s = :newState,  #u = :newUpdatedAt'
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });

  it('正常系_updateFreeeOauthState_セッションIDをもとにDynamoDBにstateを保存する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const sessionData: Session = {
      sessionId: 'session',
      freeeOauthState: '0245we65r76tiuh',
      updatedAt: new Date().toISOString()
    };

    await updateFreeeOauthState(sessionData);

    // UpdateItemCommandが正しく呼び出されたことを確認
    expect(UpdateItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionData.sessionId }
      },
      ExpressionAttributeNames: {
        '#s': 'freeeOauthState',
        '#u': 'updatedAt'
      },
      ExpressionAttributeValues: {
        ':newFreeeOauthState': { S: sessionData.freeeOauthState },
        ':newUpdatedAt': { S: sessionData.updatedAt }
      },
      UpdateExpression: 'SET #s = :newFreeeOauthState,  #u = :newUpdatedAt'
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });

  it('正常系_updateExpirationTime_セッションIDをもとにDynamoDBにExpirationTimeを更新する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const sessionData: Session = {
      sessionId: 'session',
      expirationTime: '**********',
      updatedAt: new Date().toISOString()
    };

    await updateExpirationTime(sessionData);

    // UpdateItemCommandが正しく呼び出されたことを確認
    expect(UpdateItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionData.sessionId }
      },
      ExpressionAttributeNames: {
        '#e': 'expirationTime',
        '#u': 'updatedAt'
      },
      ExpressionAttributeValues: {
        ':newExpirationTime': { N: sessionData.expirationTime },
        ':newUpdatedAt': { S: sessionData.updatedAt }
      },
      UpdateExpression: 'SET #e = :newExpirationTime,  #u = :newUpdatedAt'
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });

  it('正常系_getSession_セッションIDをもとにSessionを取得する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const sessionId = 'test-session';

    const sessionData: Session = {
      sessionId: 'test-session',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        clientId: '012345',
        compName: '取引先名',
        userSeiMei: '利用者名',
        userTyp: '01',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        loginType: '217',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: false,
            generalTransferAuth: false,
            salaryTransferAuth: false,
            localTaxPaymentAuth: false,
            approvalAuth: false,
            transferAuth: false,
            fileSendingAuth: false,
            taxesOrFeesPaymentAuth: false,
            accountTransferAuth: false,
            transferDestinationCheckAuth: false,
            dataConversionAuth: false,
            accountInquiryOnSmartPhoneAuth: false,
            transferOnSmartPhoneAuth: false,
            approvalOnSmartPhoneAuth: false,
            preSettingAccountInfoOnSmartPhoneAuth: false,
            settingSecurityOnSmartPhoneAuth: false,
            preSettingAccountInfoAuth: false,
            settingSecurityAuth: false
          },
          gets: {
            inboundTransferAuth: false,
            lcExportAuth: false,
            exportBillHandlingAuth: false,
            outboundTransferAuth: false,
            lcImportAuth: false,
            forexInfoExchangeAuth: false,
            corpoForexDeliveryAuth: false,
            parentChildContractAuth: false,
            selfSecurityAccessAuth: false,
            generalSecurityAccessAuth: false
          }
        }
      },
      state: 'state',
      freeeOauthState: 'freeeOauthState',
      expirationTime: '**********',
      createdAt: '2023-9-21 17:33:28 GMT+9',
      updatedAt: '2023-9-21 17:33:28 GMT+9'
    };

    // モックデータ
    const mockResponse = {
      Item: {
        createdAt: { S: '2023-9-21 17:33:28 GMT+9' },
        expirationTime: { N: '**********' },
        userInfo: {
          S: '{"ninsyoKbn":"01","compId":"12345678901234567890","clientId":"012345","compName":"取引先名","userSeiMei":"利用者名","userTyp":"01","compAtrbt":"01","userKn":"リヨウシャメイ","Email":"<EMAIL>","loginType":"217","telNo":"***********","kigyoCd":"**************","userAuths":""}'
        },
        sessionId: { S: 'test-session' },
        state: { S: 'state' },
        freeeOauthState: { S: 'freeeOauthState' },
        issueInstant: { S: '2023-11-02T06:24:49.727Z' },
        vdId: { S: 'ABCDE12345' },
        dtpId: { S: '<EMAIL>' },
        userId: { S: 'id12345' },
        updatedAt: { S: '2023-9-21 17:33:28 GMT+9' }
      }
    };

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    const response = await getSession(sessionId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(response).toStrictEqual(sessionData);
  });

  it('正常系_getSession_セッション情報を取得できなかった場合にnullを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const sessionId = 'test-session';

    // モックデータ
    const mockResponse = {};

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    const response = await getSession(sessionId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(response).toStrictEqual(null);
  });

  it('正常系_deleteSession_セッションIDをもとにセッション情報を削除する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const sessionId = 'test-session';

    await deleteSession(sessionId);

    // DeleteItemCommandが正しく呼び出されたことを確認
    expect(DeleteItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      },
      ConditionExpression: 'attribute_exists(sessionId)'
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(DeleteItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });

  it('正常系_getUserInfo_DynamoDBからユーザー情報を取得する', async () => {
    // テストデータ
    const sessionData: Session = {
      sessionId: 'test-session',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        userTyp: '01',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        keiyakuType: '2',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: {
            inboundTransferAuth: true,
            lcExportAuth: true,
            exportBillHandlingAuth: true,
            outboundTransferAuth: true,
            lcImportAuth: true,
            forexInfoExchangeAuth: true,
            corpoForexDeliveryAuth: true,
            parentChildContractAuth: true,
            selfSecurityAccessAuth: true,
            generalSecurityAccessAuth: true
          }
        }
      },
      state: null,
      expirationTime: '**********',
      createdAt: new Date().toISOString()
    };

    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // モック関数を定義
    const mockResolvedValue = jest.fn().mockResolvedValue({
      Item: {
        sessionId: { S: sessionData.sessionId },
        vdId: { S: sessionData.vdId },
        dtpId: { S: sessionData.dtpId },
        userId: { S: sessionData.userId },
        issueInstant: { S: sessionData.issueInstant },
        userInfo: {
          S: JSON.stringify(sessionData.userInfo)
        },
        state: { S: sessionData.state },
        expirationTime: { N: sessionData.expirationTime },
        createdAt: { S: sessionData.createdAt },
        updatedAt: { S: sessionData.updatedAt }
      }
    });
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockResolvedValue);

    const sessionId = 'dummySessionId';
    const result = await getUserInfo(sessionId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: { sessionId: { S: 'dummySessionId' } }
    });
    // 取得結果が正しいことを確認
    expect(result).toEqual({
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      ninsyoKbn: '01',
      userSeiMei: '利用者名',
      userTyp: '01',
      keiyakuType: '2',
      compName: '取引先名',
      compId: '12345678901234567890',
      Email: '<EMAIL>',
      userAuths: '3'
    });
  });

  it('正常系_getUserInfo_DynamoDBからユーザー情報を取得できなかった場合にnullを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // モック関数を定義
    const mockResolvedValue = jest.fn().mockResolvedValue({});
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockResolvedValue);

    const sessionId = 'dummySessionId';
    const result = await getUserInfo(sessionId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(result).toStrictEqual(null);
  });

  it('異常系_saveSession_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const userInfoData = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      userTyp: '01',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '***********',
      kigyoCd: '**************',
      userAuths: {
        web21: {
          accountInquiryAuth: true,
          generalTransferAuth: true,
          salaryTransferAuth: true,
          localTaxPaymentAuth: true,
          approvalAuth: true,
          transferAuth: true,
          fileSendingAuth: true,
          taxesOrFeesPaymentAuth: true,
          accountTransferAuth: true,
          transferDestinationCheckAuth: true,
          dataConversionAuth: true,
          accountInquiryOnSmartPhoneAuth: true,
          transferOnSmartPhoneAuth: true,
          approvalOnSmartPhoneAuth: true,
          preSettingAccountInfoOnSmartPhoneAuth: true,
          settingSecurityOnSmartPhoneAuth: true,
          preSettingAccountInfoAuth: true,
          settingSecurityAuth: true
        },
        gets: {
          inboundTransferAuth: true,
          lcExportAuth: true,
          exportBillHandlingAuth: true,
          outboundTransferAuth: true,
          lcImportAuth: true,
          forexInfoExchangeAuth: true,
          corpoForexDeliveryAuth: true,
          parentChildContractAuth: true,
          selfSecurityAccessAuth: true,
          generalSecurityAccessAuth: true
        }
      }
    };

    const sessionData: Session = {
      sessionId: 'test-session',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: userInfoData,
      state: 'state',
      freeeOauthState: 'freeeOauthState',
      expirationTime: '**********',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // エラーがthrowされることを確認
    await expect(saveSession(sessionData)).rejects.toThrow('DB Error');

    // PutItemCommandが正しく呼び出されたことを確認
    expect(PutItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Item: {
        sessionId: { S: sessionData.sessionId },
        vdId: { S: sessionData.vdId! },
        dtpId: { S: sessionData.dtpId! },
        userId: { S: sessionData.userId! },
        issueInstant: { S: sessionData.issueInstant },
        userInfo: { S: JSON.stringify(sessionData.userInfo) },
        state: { S: sessionData.state },
        freeeOauthState: { S: sessionData.freeeOauthState },
        expirationTime: { N: sessionData.expirationTime },
        createdAt: { S: sessionData.createdAt },
        updatedAt: { S: sessionData.updatedAt! }
      }
    });
  });

  it('異常系_updateState_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const sessionData: Session = {
      sessionId: 'test-session',
      state: null,
      updatedAt: new Date().toISOString()
    };

    // エラーがthrowされることを確認
    await expect(updateState(sessionData)).rejects.toThrow('DB Error');

    // UpdateItemCommandが正しく呼び出されたことを確認
    expect(UpdateItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionData.sessionId }
      },
      ExpressionAttributeNames: {
        '#s': 'state',
        '#u': 'updatedAt'
      },
      ExpressionAttributeValues: {
        ':newState': { NULL: true },
        ':newUpdatedAt': { S: sessionData.updatedAt }
      },
      UpdateExpression: 'SET #s = :newState,  #u = :newUpdatedAt'
    });
  });

  it('異常系_updateFreeeOauthState_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const sessionData: Session = {
      sessionId: 'test-session',
      freeeOauthState: null,
      updatedAt: new Date().toISOString()
    };

    // エラーがthrowされることを確認
    await expect(updateFreeeOauthState(sessionData)).rejects.toThrow(
      'DB Error'
    );

    // UpdateItemCommandが正しく呼び出されたことを確認
    expect(UpdateItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionData.sessionId }
      },
      ExpressionAttributeNames: {
        '#s': 'freeeOauthState',
        '#u': 'updatedAt'
      },
      ExpressionAttributeValues: {
        ':newFreeeOauthState': { NULL: true },
        ':newUpdatedAt': { S: sessionData.updatedAt }
      },
      UpdateExpression: 'SET #s = :newFreeeOauthState,  #u = :newUpdatedAt'
    });
  });

  it('異常系_updateExpirationTime_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const sessionData: Session = {
      sessionId: 'test-session',
      expirationTime: '**********',
      updatedAt: new Date().toISOString()
    };

    // エラーがthrowされることを確認
    await expect(updateExpirationTime(sessionData)).rejects.toThrow('DB Error');

    // UpdateItemCommandが正しく呼び出されたことを確認
    expect(UpdateItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionData.sessionId }
      },
      ExpressionAttributeNames: {
        '#e': 'expirationTime',
        '#u': 'updatedAt'
      },
      ExpressionAttributeValues: {
        ':newExpirationTime': { N: sessionData.expirationTime },
        ':newUpdatedAt': { S: sessionData.updatedAt }
      },
      UpdateExpression: 'SET #e = :newExpirationTime,  #u = :newUpdatedAt'
    });
  });

  it('異常系_getSession_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const sessionId = 'test-session';

    // エラーがthrowされることを確認
    await expect(getSession(sessionId)).rejects.toThrow('DB Error');

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      }
    });
  });

  it('異常系_deleteSession_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const sessionId = 'test-session';

    // エラーがthrowされることを確認
    await expect(deleteSession(sessionId)).rejects.toThrow('DB Error');

    // DeleteItemCommandが正しく呼び出されたことを確認
    expect(DeleteItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      },
      ConditionExpression: 'attribute_exists(sessionId)'
    });
  });

  it('異常系_getUserInfo_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const sessionId = 'test-session';

    // エラーがthrowされることを確認
    await expect(getUserInfo(sessionId)).rejects.toThrow('DB Error');

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: sessionTableName,
      Key: {
        sessionId: { S: sessionId }
      }
    });
  });

  describe('updateDtpId', () => {
    const mockSessionId = 'mockSessionId';
    const mockDtpId = 'mockDtpId';
    const mockUserId = 'mockUserId';

    let mockSend: jest.SpyInstance;

    beforeEach(() => {
      mockSend = jest.spyOn(dynamoDBClient, 'send');
    });

    it('正常系_セッションIDをもとにDynamoDBにdtpIdを保存する', async () => {
      // 正常な返却値をモック
      mockSend.mockResolvedValue({});

      const sessionData: Session = {
        sessionId: mockSessionId,
        dtpId: mockDtpId,
        updatedAt: new Date().toISOString()
      };

      await updateDtpId(sessionData);

      // UpdateItemCommandが正しく呼び出されたことを確認
      expect(UpdateItemCommand).toHaveBeenCalledWith({
        TableName: sessionTableName,
        Key: {
          sessionId: { S: sessionData.sessionId }
        },
        ExpressionAttributeNames: {
          '#dtp': 'dtpId',
          '#u': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':dtpId': { S: sessionData.dtpId },
          ':newUpdatedAt': { S: sessionData.updatedAt }
        },
        UpdateExpression: 'SET #dtp = :dtpId, #u = :newUpdatedAt'
      });
      // DynamoDBClientのsendメソッドが呼び出されたことを確認
      expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
      // 正常に呼び出された場合、エラーがthrowされないことを確認
      expect(mockSend).not.toThrow();
    });
    it('正常系_dtpId,userIdがundefinedでも登録可能であること', async () => {
      // 正常な返却値をモック
      mockSend.mockResolvedValue({});

      const sessionData: Session = {
        sessionId: mockSessionId,
        dtpId: undefined,
        updatedAt: new Date().toISOString()
      };

      await updateDtpId(sessionData);

      // UpdateItemCommandが正しく呼び出されたことを確認
      expect(UpdateItemCommand).toHaveBeenCalledWith({
        TableName: sessionTableName,
        Key: {
          sessionId: { S: sessionData.sessionId }
        },
        ExpressionAttributeNames: {
          '#dtp': 'dtpId',
          '#u': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':dtpId': { NULL: true },
          ':newUpdatedAt': { S: sessionData.updatedAt }
        },
        UpdateExpression: 'SET #dtp = :dtpId, #u = :newUpdatedAt'
      });
      // DynamoDBClientのsendメソッドが呼び出されたことを確認
      expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
      // 正常に呼び出された場合、エラーがthrowされないことを確認
      expect(mockSend).not.toThrow();
    });
    it('異常系_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
      // エラーをthrowするモック関数を定義
      const mockRejectedValue = jest
        .fn()
        .mockRejectedValue(new Error('DB Error'));
      mockSend.mockImplementation(mockRejectedValue);

      // メソッド引数のモック
      const sessionData: Session = {
        sessionId: mockSessionId,
        dtpId: mockDtpId,
        updatedAt: new Date().toISOString()
      };

      // エラーがthrowされることを確認
      await expect(updateDtpId(sessionData)).rejects.toThrow('DB Error');

      // UpdateItemCommandが正しく呼び出されたことを確認
      expect(UpdateItemCommand).toHaveBeenCalledWith({
        TableName: sessionTableName,
        Key: {
          sessionId: { S: sessionData.sessionId }
        },
        ExpressionAttributeNames: {
          '#dtp': 'dtpId',
          '#u': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':dtpId': { S: sessionData.dtpId },
          ':newUpdatedAt': { S: sessionData.updatedAt }
        },
        UpdateExpression: 'SET #dtp = :dtpId, #u = :newUpdatedAt'
      });
    });
  });

  describe('updateDtpIdAndUserId', () => {
    const mockSessionId = 'mockSessionId';
    const mockDtpId = 'mockDtpId';
    const mockUserId = 'mockUserId';

    let mockSend: jest.SpyInstance;

    beforeEach(() => {
      mockSend = jest.spyOn(dynamoDBClient, 'send');
    });

    it('正常系_セッションIDをもとにDynamoDBにdtpId,userIdを保存する', async () => {
      // 正常な返却値をモック
      mockSend.mockResolvedValue({});

      const sessionData: Session = {
        sessionId: mockSessionId,
        dtpId: mockDtpId,
        userId: mockUserId,
        updatedAt: new Date().toISOString()
      };

      await updateDtpIdAndUserId(sessionData);

      // UpdateItemCommandが正しく呼び出されたことを確認
      expect(UpdateItemCommand).toHaveBeenCalledWith({
        TableName: sessionTableName,
        Key: {
          sessionId: { S: sessionData.sessionId }
        },
        ExpressionAttributeNames: {
          '#dtp': 'dtpId',
          '#user': 'userId',
          '#u': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':dtpId': { S: sessionData.dtpId },
          ':userId': { S: sessionData.userId },
          ':newUpdatedAt': { S: sessionData.updatedAt }
        },
        UpdateExpression:
          'SET #dtp = :dtpId,  #user = :userId,  #u = :newUpdatedAt'
      });
      // DynamoDBClientのsendメソッドが呼び出されたことを確認
      expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
      // 正常に呼び出された場合、エラーがthrowされないことを確認
      expect(mockSend).not.toThrow();
    });
    it('正常系_dtpId,userIdがundefinedでも登録可能であること', async () => {
      // 正常な返却値をモック
      mockSend.mockResolvedValue({});

      const sessionData: Session = {
        sessionId: mockSessionId,
        dtpId: undefined,
        userId: undefined,
        updatedAt: new Date().toISOString()
      };

      await updateDtpIdAndUserId(sessionData);

      // UpdateItemCommandが正しく呼び出されたことを確認
      expect(UpdateItemCommand).toHaveBeenCalledWith({
        TableName: sessionTableName,
        Key: {
          sessionId: { S: sessionData.sessionId }
        },
        ExpressionAttributeNames: {
          '#dtp': 'dtpId',
          '#user': 'userId',
          '#u': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':dtpId': { NULL: true },
          ':userId': { NULL: true },
          ':newUpdatedAt': { S: sessionData.updatedAt }
        },
        UpdateExpression:
          'SET #dtp = :dtpId,  #user = :userId,  #u = :newUpdatedAt'
      });
      // DynamoDBClientのsendメソッドが呼び出されたことを確認
      expect(mockSend).toHaveBeenCalledWith(expect.any(UpdateItemCommand));
      // 正常に呼び出された場合、エラーがthrowされないことを確認
      expect(mockSend).not.toThrow();
    });
    it('異常系_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
      // エラーをthrowするモック関数を定義
      const mockRejectedValue = jest
        .fn()
        .mockRejectedValue(new Error('DB Error'));
      mockSend.mockImplementation(mockRejectedValue);

      // メソッド引数のモック
      const sessionData: Session = {
        sessionId: mockSessionId,
        dtpId: mockDtpId,
        userId: mockUserId,
        updatedAt: new Date().toISOString()
      };

      // エラーがthrowされることを確認
      await expect(updateDtpIdAndUserId(sessionData)).rejects.toThrow(
        'DB Error'
      );

      // UpdateItemCommandが正しく呼び出されたことを確認
      expect(UpdateItemCommand).toHaveBeenCalledWith({
        TableName: sessionTableName,
        Key: {
          sessionId: { S: sessionData.sessionId }
        },
        ExpressionAttributeNames: {
          '#dtp': 'dtpId',
          '#user': 'userId',
          '#u': 'updatedAt'
        },
        ExpressionAttributeValues: {
          ':dtpId': { S: sessionData.dtpId },
          ':userId': { S: sessionData.userId },
          ':newUpdatedAt': { S: sessionData.updatedAt }
        },
        UpdateExpression:
          'SET #dtp = :dtpId,  #user = :userId,  #u = :newUpdatedAt'
      });
    });
  });
});
