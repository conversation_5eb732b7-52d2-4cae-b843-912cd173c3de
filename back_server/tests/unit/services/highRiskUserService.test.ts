import { PutItemCommand, GetItemCommand } from '@aws-sdk/client-dynamodb';
import {
  saveHighRiskUser,
  getHighRiskUser
} from '../../../src/services/highRiskUserService';
import { UserInfo } from '../../../src/models/session';
import dynamoDBClient from '../../../src/utils/dynamoDB';
import { highRiskUseTablerName } from '../../../src/config/config';
import { logger } from '../../../src/utils/logger';
import { HighRiskUser } from '../../../src/models/highRiskUser';

// Commandをモック化
jest.mock('@aws-sdk/client-dynamodb', () => ({
  PutItemCommand: jest.fn(),
  UpdateItemCommand: jest.fn(),
  GetItemCommand: jest.fn(),
  DeleteItemCommand: jest.fn()
}));
// dynamoDBClientのsendメソッドをモック化
jest.mock('../../../src/utils/dynamoDB', () => ({
  send: jest.fn()
}));

describe('highRiskUserService', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('正常系_saveHighRiskUser_DynamoDBに高リスクユーザー情報を保存する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '***********',
      kigyoCd: '**************',
      keiyakuType: '2',
      userAuths: {
        web21: null,
        gets: null
      }
    };

    const sessionData: HighRiskUser = {
      highRiskUserId: 'test-session',
      vdId: 'ABCDE12345',
      dtpId: undefined,
      userId: undefined,
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '**********',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    await saveHighRiskUser(sessionData);

    // PutItemCommandが正しく呼び出されたことを確認
    expect(PutItemCommand).toHaveBeenCalledWith({
      TableName: highRiskUseTablerName,
      Item: {
        highRiskUserId: { S: sessionData.highRiskUserId },
        vdId: { S: sessionData.vdId! },
        dtpId: { NULL: true },
        userId: { NULL: true },
        issueInstant: { S: sessionData.issueInstant },
        userInfo: { S: JSON.stringify(sessionData.userInfo) },
        state: { NULL: true },
        freeeOauthState: { NULL: true },
        expirationTime: { N: sessionData.expirationTime },
        createdAt: { S: sessionData.createdAt },
        updatedAt: { S: sessionData.updatedAt }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(PutItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });

  it('異常系_saveHighRiskUser_DynamoDBへの接続でエラーが発生した場合にエラーを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('DB Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    // テストデータ
    const userInfoData = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      userTyp: '01',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '***********',
      kigyoCd: '**************',
      keiyakuType: '2',
      userAuths: {
        web21: {
          accountInquiryAuth: true,
          generalTransferAuth: true,
          salaryTransferAuth: true,
          localTaxPaymentAuth: true,
          approvalAuth: true,
          transferAuth: true,
          fileSendingAuth: true,
          taxesOrFeesPaymentAuth: true,
          accountTransferAuth: true,
          transferDestinationCheckAuth: true,
          dataConversionAuth: true,
          accountInquiryOnSmartPhoneAuth: true,
          transferOnSmartPhoneAuth: true,
          approvalOnSmartPhoneAuth: true,
          preSettingAccountInfoOnSmartPhoneAuth: true,
          settingSecurityOnSmartPhoneAuth: true,
          preSettingAccountInfoAuth: true,
          settingSecurityAuth: true
        },
        gets: {
          inboundTransferAuth: true,
          lcExportAuth: true,
          exportBillHandlingAuth: true,
          outboundTransferAuth: true,
          lcImportAuth: true,
          forexInfoExchangeAuth: true,
          corpoForexDeliveryAuth: true,
          parentChildContractAuth: true,
          selfSecurityAccessAuth: true,
          generalSecurityAccessAuth: true
        }
      }
    };

    const sessionData: HighRiskUser = {
      highRiskUserId: 'test-highRiskUserId',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: userInfoData,
      state: 'state',
      freeeOauthState: 'freeeOauthState',
      expirationTime: '**********',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // エラーがthrowされることを確認
    await expect(saveHighRiskUser(sessionData)).rejects.toThrow('DB Error');

    // PutItemCommandが正しく呼び出されたことを確認
    expect(PutItemCommand).toHaveBeenCalledWith({
      TableName: highRiskUseTablerName,
      Item: {
        highRiskUserId: { S: sessionData.highRiskUserId },
        vdId: { S: sessionData.vdId! },
        dtpId: { S: sessionData.dtpId! },
        userId: { S: sessionData.userId! },
        issueInstant: { S: sessionData.issueInstant },
        userInfo: { S: JSON.stringify(sessionData.userInfo) },
        state: { S: sessionData.state },
        freeeOauthState: { S: sessionData.freeeOauthState },
        expirationTime: { N: sessionData.expirationTime },
        createdAt: { S: sessionData.createdAt },
        updatedAt: { S: sessionData.updatedAt! }
      }
    });
  });

  it('正常系_getSession_セッションIDをもとにSessionを取得する', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const highRiskUserId = 'test-highRiskUserId';

    const highRiskUserData: HighRiskUser = {
      highRiskUserId: 'test-highRiskUserId',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: {
        clientId: 'clientId',
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        userTyp: '01',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        loginType: '217',
        telNo: '***********',
        kigyoCd: '**************',
        keiyakuType: '2',
        userAuths: {
          web21: {
            accountInquiryAuth: false,
            generalTransferAuth: false,
            salaryTransferAuth: false,
            localTaxPaymentAuth: false,
            approvalAuth: false,
            transferAuth: false,
            fileSendingAuth: false,
            taxesOrFeesPaymentAuth: false,
            accountTransferAuth: false,
            transferDestinationCheckAuth: false,
            dataConversionAuth: false,
            accountInquiryOnSmartPhoneAuth: false,
            transferOnSmartPhoneAuth: false,
            approvalOnSmartPhoneAuth: false,
            preSettingAccountInfoOnSmartPhoneAuth: false,
            settingSecurityOnSmartPhoneAuth: false,
            preSettingAccountInfoAuth: false,
            settingSecurityAuth: false
          },
          gets: {
            inboundTransferAuth: false,
            lcExportAuth: false,
            exportBillHandlingAuth: false,
            outboundTransferAuth: false,
            lcImportAuth: false,
            forexInfoExchangeAuth: false,
            corpoForexDeliveryAuth: false,
            parentChildContractAuth: false,
            selfSecurityAccessAuth: false,
            generalSecurityAccessAuth: false
          }
        }
      },
      state: 'state',
      freeeOauthState: 'freeeOauthState',
      expirationTime: '**********',
      createdAt: '2023-9-21 17:33:28 GMT+9',
      updatedAt: '2023-9-21 17:33:28 GMT+9'
    };

    // モックデータ
    const mockResponse = {
      Item: {
        createdAt: { S: '2023-9-21 17:33:28 GMT+9' },
        expirationTime: { N: '**********' },
        userInfo: {
          S: '{"clientId":"clientId","ninsyoKbn":"01","compId":"12345678901234567890","compName":"取引先名","userSeiMei":"利用者名","userTyp":"01","compAtrbt":"01","userKn":"リヨウシャメイ","Email":"<EMAIL>","loginType":"217","telNo":"***********","kigyoCd":"**************","keiyakuType":"2","userAuths":""}'
        },
        highRiskUserId: { S: 'test-highRiskUserId' },
        state: { S: 'state' },
        freeeOauthState: { S: 'freeeOauthState' },
        issueInstant: { S: '2023-11-02T06:24:49.727Z' },
        vdId: { S: 'ABCDE12345' },
        dtpId: { S: '<EMAIL>' },
        userId: { S: 'id12345' },
        updatedAt: { S: '2023-9-21 17:33:28 GMT+9' }
      }
    };

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    const response = await getHighRiskUser(highRiskUserId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: highRiskUseTablerName,
      Key: {
        highRiskUserId: { S: highRiskUserId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(response).toStrictEqual(highRiskUserData);
  });

  it('正常系_getSession_セッション情報を取得できなかった場合にnullを返す', async () => {
    const mockSend = jest.spyOn(dynamoDBClient, 'send');

    // テストデータ
    const highRiskUserId = 'test-highRiskUserId';

    // モックデータ
    const mockResponse = {};

    // モックデータを返却
    const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
    mockSend.mockImplementation(mockReturnedValue);

    const response = await getHighRiskUser(highRiskUserId);

    // GetItemCommandが正しく呼び出されたことを確認
    expect(GetItemCommand).toHaveBeenCalledWith({
      TableName: highRiskUseTablerName,
      Key: {
        highRiskUserId: { S: highRiskUserId }
      }
    });
    // DynamoDBClientのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetItemCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
    // 正常に呼び出された場合、データが正しいかどうか確認
    expect(response).toStrictEqual(null);
  });
});
