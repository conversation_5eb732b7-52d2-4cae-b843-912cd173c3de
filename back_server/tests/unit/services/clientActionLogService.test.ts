import { SendMessageCommand } from '@aws-sdk/client-sqs';
import { saveClientActionLog } from '../../../src/services/clientActionLogService';
import { ClientActionLog } from '../../../src/models/clientActionLog';
import sqsClient from '../../../src/utils/sqs';
import { logger } from '../../../src/utils/logger';
import { unixTime } from '../../../src/utils/dateFormat';

// dateFormatをモック化
jest.mock('../../../src/utils/dateFormat');
// Commandをモック化
jest.mock('@aws-sdk/client-sqs', () => ({
  SendMessageCommand: jest.fn()
}));
// sqsClientのsendメソッドをモック化
jest.mock('../../../src/utils/sqs', () => ({
  send: jest.fn()
}));

describe('clientActionLogService', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('正常系_saveClientActionLog_SQSに顧客操作履歴を送る', async () => {
    const mockSend = jest.spyOn(sqsClient, 'send');

    // テストデータ
    const mockClientActionLog: ClientActionLog = {
      opDate: '2024-04-01 00:45:49.971',
      opChannel: 'MOBILE',
      ip: '1024.1344.1055.7788.9900.3344.5566',
      dtpId: 'dtpId',
      vdId: '**********',
      userName: 'テストユーザー',
      accountNumber: '**********',
      clientFunction: 'テスト機能',
      operation: 'テスト操作',
      result: '異常',
      errorId: 'E001-MBF00001'
    };

    (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
      **********
    );

    await saveClientActionLog('mockSessionId', mockClientActionLog);

    // SendMessageCommandが正しく呼び出されたことを確認
    expect(SendMessageCommand).toHaveBeenCalledWith({
      QueueUrl: 'QUEUE_URL',
      MessageGroupId: 'mockSessionId',
      MessageDeduplicationId: 'mockSessionId-**********',
      MessageBody:
        '{"OpDate":"2024-04-01 00:45:49.971","OpChannel":"MOBILE","IP":"1024.1344.1055.7788.9900.3344.5566","DTPID":"dtpId","VDID":"**********","UserName":"テストユーザー","AccountNumber":"**********","Function":"テスト機能","Operation":"テスト操作","Result":"異常","ErrorID":"E001-MBF00001"}'
    });
    // SQSのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(SendMessageCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });
  it('正常系_saveClientActionLog_SQSに顧客操作履歴を送る_IPアドレスにカンマが含まれていた場合、カンマの前までの文字列が送られる', async () => {
    const mockSend = jest.spyOn(sqsClient, 'send');

    // テストデータ
    const mockClientActionLog: ClientActionLog = {
      opDate: '2024-04-01 00:45:49.971',
      opChannel: 'MOBILE',
      ip: '************, 10.245.34.563',
      dtpId: 'dtpId',
      vdId: '**********',
      userName: 'テストユーザー',
      accountNumber: '**********',
      clientFunction: 'テスト機能',
      operation: 'テスト操作',
      result: '異常',
      errorId: 'E001-MBF00001'
    };

    (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
      **********
    );

    await saveClientActionLog('mockSessionId', mockClientActionLog);

    // SendMessageCommandが正しく呼び出されたことを確認
    expect(SendMessageCommand).toHaveBeenCalledWith({
      QueueUrl: 'QUEUE_URL',
      MessageGroupId: 'mockSessionId',
      MessageDeduplicationId: 'mockSessionId-**********',
      MessageBody:
        '{"OpDate":"2024-04-01 00:45:49.971","OpChannel":"MOBILE","IP":"************","DTPID":"dtpId","VDID":"**********","UserName":"テストユーザー","AccountNumber":"**********","Function":"テスト機能","Operation":"テスト操作","Result":"異常","ErrorID":"E001-MBF00001"}'
    });
    // SQSのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(SendMessageCommand));
    // 正常に呼び出された場合、エラーがthrowされないことを確認
    expect(mockSend).not.toThrow();
  });
  it('異常系_saveClientActionLog_顧客操作履歴のバリデーションチェックでエラーが発生した場合エラーを返す', async () => {
    const mockSend = jest.spyOn(sqsClient, 'send');

    // テストデータ
    const mockClientActionLog: ClientActionLog = {
      opDate: '',
      opChannel: '',
      ip: '',
      dtpId: 'dtpId',
      vdId: '**********',
      userName: 'テストユーザー',
      accountNumber: '**********',
      clientFunction: 'テスト機能',
      operation: 'テスト操作',
      result: '異常',
      errorId: 'E001-MBF00001'
    };

    (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
      **********
    );

    // エラーがthrowされることを確認
    await expect(
      saveClientActionLog('mockSessionId', mockClientActionLog)
    ).rejects.toThrow('操作日時,操作チャネル,IPアドレスが存在しません');
    // DynamoDBClientのsendメソッドが呼ばれないことを確認
    expect(mockSend).not.toHaveBeenCalledWith(expect.any(SendMessageCommand));
  });

  it('異常系_saveClientActionLog_SQSへの接続でエラーが発生した場合エラーを返す', async () => {
    // モックデータ
    const mockSend = jest.spyOn(sqsClient, 'send');
    // エラーをthrowするモック関数を定義
    const mockRejectedValue = jest
      .fn()
      .mockRejectedValue(new Error('SQS Error'));
    // sendメソッドがモック関数を返すようにする
    mockSend.mockImplementation(mockRejectedValue);

    const mockClientActionLog: ClientActionLog = {
      opDate: '2024-04-01 00:45:49.971',
      opChannel: 'MOBILE',
      ip: '************',
      dtpId: 'dtpId',
      vdId: '**********',
      userName: 'テストユーザー',
      accountNumber: '**********',
      clientFunction: 'テスト機能',
      operation: 'テスト操作',
      result: '異常',
      errorId: 'E001-MBF00001'
    };

    (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
      **********
    );

    // エラーがthrowされることを確認
    await expect(
      saveClientActionLog('mockSessionId', mockClientActionLog)
    ).rejects.toThrow('SQS Error');

    // SendMessageCommandが正しく呼び出されたことを確認
    expect(SendMessageCommand).toHaveBeenCalledWith({
      QueueUrl: 'QUEUE_URL',
      MessageGroupId: 'mockSessionId',
      MessageDeduplicationId: 'mockSessionId-**********',
      MessageBody:
        '{"OpDate":"2024-04-01 00:45:49.971","OpChannel":"MOBILE","IP":"************","DTPID":"dtpId","VDID":"**********","UserName":"テストユーザー","AccountNumber":"**********","Function":"テスト機能","Operation":"テスト操作","Result":"異常","ErrorID":"E001-MBF00001"}'
    });
    // SQSのsendメソッドが呼び出されたことを確認
    expect(mockSend).toHaveBeenCalledWith(expect.any(SendMessageCommand));
  });
});
