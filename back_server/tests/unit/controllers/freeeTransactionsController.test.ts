import { Request, Response, NextFunction } from 'express';
import freeeTransactionsController from '../../../src/controllers/freeeTransactionsController';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import {
  DATE_FORMAT_YYYY_MM_DD,
  changeDateFormat,
  serverDate,
  serverDateTime,
  serverTime
} from '../../../src/utils/dateFormat';
import backendRequest from '../../../src/common/api/backendRequest';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('freeeTransactionsController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getFreeeTransactions', () => {
    it('正常系_バックエンドからfreee口座明細一覧を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2024-01-01',
          end_date: '2024-04-01'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // baseDateのモック化
      (
        serverDate as jest.MockedFunction<typeof serverDate>
      ).mockReturnValueOnce('2024/5/23');
      // baseTimeのモック化
      (
        serverTime as jest.MockedFunction<typeof serverTime>
      ).mockReturnValueOnce('9:00');
      // transactionsのdateのモック化
      (
        changeDateFormat as jest.MockedFunction<typeof changeDateFormat>
      ).mockReturnValueOnce('2024/1/11');
      (
        changeDateFormat as jest.MockedFunction<typeof changeDateFormat>
      ).mockReturnValueOnce('2023/12/31');
      (
        changeDateFormat as jest.MockedFunction<typeof changeDateFormat>
      ).mockReturnValueOnce('2024/1/10');

      // freee明細一覧のモックデータ
      const mockFreeeTransactionsData = {
        transactions: [
          {
            id: 1,
            date: '2024-01-11',
            amount: 300000,
            entrySide: 'income',
            description: '振込　カ）ABC'
          },
          {
            id: 2,
            date: '2023-12-31',
            amount: 300000000,
            entrySide: 'income',
            description: '振込　カ）ABC'
          },
          {
            id: 3,
            date: '2024-01-10',
            amount: 322337,
            entrySide: 'income',
            description: '振込　カ）ABC'
          }
        ]
      };

      // 期待値
      const expectFreeeTransactionsData = {
        transactions: [
          {
            id: 1,
            date: '2024/1/11',
            amount: 300000,
            entrySide: 'income',
            description: '振込　カ）ABC'
          },
          {
            id: 2,
            date: '2023/12/31',
            amount: 300000000,
            entrySide: 'income',
            description: '振込　カ）ABC'
          },
          {
            id: 3,
            date: '2024/1/10',
            amount: 322337,
            entrySide: 'income',
            description: '振込　カ）ABC'
          }
        ],
        baseDate: '2024/5/23',
        baseTime: '9:00'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockFreeeTransactionsData });

      await freeeTransactionsController.getFreeeTransactions(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/freee/transactions',
        {
          params: {
            walletable_id: 1,
            walletable_type: 'bank_account',
            start_date: '2024-01-01',
            end_date: '2024-04-01'
          },
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectFreeeTransactionsData
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_freee口座明細一覧がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        query: {
          walletable_id: 1,
          walletable_type: 'bank_account',
          start_date: '2020-01-01',
          end_date: '2024-01-11'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeTransactionsController.getFreeeTransactions(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('getUsedTotalAmount', () => {
    it('正常系_バックエンドからfreee口座利用合計金額情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        query: {
          walletable_id: 1,
          walletable_type: 'credit_card'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2023-02-11'
      );
      (
        serverDateTime as jest.MockedFunction<typeof serverDateTime>
      ).mockReturnValue('2024/1/1 9:00');

      const mockUsedTotalAmountData = {
        startDate: '2023-02-01',
        endDate: '2023-02-28',
        totalExpense: 76543
      };

      const expectUsedTotalAmountData = {
        startDate: '2023-02-01',
        endDate: '2023-02-28',
        totalExpense: 76543,
        serverDateTime: '2024/1/1 9:00'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockUsedTotalAmountData });

      await freeeTransactionsController.getUsedTotalAmount(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/freee/transactions/total-expense',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {
            walletable_id: 1,
            walletable_type: 'credit_card',
            period: 'one_month',
            end_date: serverDate(DATE_FORMAT_YYYY_MM_DD)
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectUsedTotalAmountData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_freee口座利用合計金額情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        query: {
          type: 'balance_account',
          with_balance: true
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeTransactionsController.getUsedTotalAmount(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
