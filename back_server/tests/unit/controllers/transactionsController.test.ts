/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction } from 'express';
import { nowDate } from '../../../src/utils/dateFormat';
import transactionsController from '../../../src/controllers/transactionsController';
import { components } from '../../../src/schemas/schema';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import backendRequest from '../../../src/common/api/backendRequest';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

function formatJapaneseCalendarDate(n: number): string {
  const startDate = new Date('2005-07-01'); // 初めの日付
  const endDate = new Date('2005-08-31'); // 最後の日付
  const diffInDays = Math.min(Math.max(1, n), 100); // nを1から100の範囲に制限する

  const targetDate = new Date(
    startDate.getTime() +
      (endDate.getTime() - startDate.getTime()) * (diffInDays / 100)
  );

  const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
  const day = targetDate.getDate().toString().padStart(2, '0');

  return `05/${month}/${day}`;
}

function formatCalendarDate(n: number): string {
  const startDate = new Date('2023-07-01'); // 初めの日付
  const endDate = new Date('2023-08-31'); // 最後の日付
  const diffInDays = Math.min(Math.max(1, n), 100); // nを1から100の範囲に制限する

  const targetDate = new Date(
    startDate.getTime() +
      (endDate.getTime() - startDate.getTime()) * (diffInDays / 100)
  );

  const year = targetDate.getFullYear().toString();
  const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
  const day = targetDate.getDate().toString().padStart(2, '0');

  return `${year}/${month}/${day}`;
}

function transactionTypeJudge(
  number: number
): components['schemas']['TransactionsDetailSmbc']['transactionType'] {
  const remainder = number % 8;
  let transactionType: components['schemas']['TransactionsDetailSmbc']['transactionType'];

  switch (remainder) {
    case 7:
      transactionType = '10';
      break;
    case 6:
      transactionType = '11';
      break;
    case 5:
      transactionType = '12';
      break;
    case 4:
      transactionType = '13';
      break;
    case 3:
      transactionType = '14';
      break;
    case 2:
      transactionType = '18';
      break;
    case 1:
      transactionType = '19';
      break;
    default:
      transactionType = '31';
      break;
  }
  return transactionType;
}

function transactionTypeNameJudge(
  number: number
): components['schemas']['TransactionsDetailSmbc']['transactionTypeName'] {
  const remainder = number % 8;
  let transactionTypeName: components['schemas']['TransactionsDetailSmbc']['transactionTypeName'];

  switch (remainder) {
    case 7:
      transactionTypeName = '現金';
      break;
    case 6:
      transactionTypeName = '振込';
      break;
    case 5:
      transactionTypeName = '他店券入金';
      break;
    case 4:
      transactionTypeName = '交換（取立入金および交換払）';
      break;
    case 3:
      transactionTypeName = '振替';
      break;
    case 2:
      transactionTypeName = 'その他';
      break;
    case 1:
      transactionTypeName = '訂正';
      break;
    default:
      transactionTypeName = 'でんさい';
      break;
  }
  return transactionTypeName;
}

// 明細データ1
const createTransactionsDetailMock = () => {
  const transactionsDetailMock: components['schemas']['TransactionsDetail'][] =
    [];

  // eslint-disable-next-line no-plusplus
  for (let i = 100; i > 0; i--) {
    const createTransactionsDetailMockData: components['schemas']['TransactionsDetail'] =
      {
        inquiryNumber: i.toString().padStart(8, '0'),
        transactionDateJapaneseCalendar: formatJapaneseCalendarDate(i),
        transactionDateAd: new Date(formatCalendarDate(i)).toLocaleDateString(
          'ja-JP'
        ),
        valueDateJapaneseCalendar: formatJapaneseCalendarDate(i),
        valueDateAd: new Date(formatCalendarDate(i)).toLocaleDateString(
          'ja-JP'
        ),
        depositCreditType: i % 2 === 0 ? '2' : '1',
        transactionType: '13',
        transactionTypeName: '交換（取立入金および交換払）',
        amount: ************,
        checksIssuedByOtherBanksAmount: 50000,
        exchangePresentationDateJapaneseCalendar: null,
        exchangePresentationDateAd: null,
        dishonoredReturnDateJapaneseCalendar: null,
        dishonoredReturnDateAd: null,
        billAndCheckType: null,
        billAndCheckTypeName: '約束手形',
        billAndCheckNumber: '1234567',
        branchCode: null,
        remitterCode: null,
        remitterNameContractorNumber: 'ABCｼｮｳｼﾞABCABCｼｮｳｼﾞABCABCｼｮｳｼﾞABC',
        remittingBankName: null,
        remittingBankBranchName: null,
        abstract: null,
        ediInfo: 'ﾌﾘｺﾐ',
        ediKey: null
      };
    transactionsDetailMock.push(createTransactionsDetailMockData);
  }
  return transactionsDetailMock;
};

// 明細データ2
const createTransactionsDetailSmbcMock = () => {
  const transactionsDetailSmbcMock: components['schemas']['TransactionsDetailSmbc'][] =
    [];

  // eslint-disable-next-line no-plusplus
  for (let i = 5; i > 0; i--) {
    const createTransactionsDetailSmbcMockData: components['schemas']['TransactionsDetailSmbc'] =
      {
        identificationNumber: i.toString().padStart(8, '0'),
        transactionDateJapaneseCalendar: formatJapaneseCalendarDate(i),
        transactionDateAd: new Date(formatCalendarDate(i)).toLocaleDateString(
          'ja-JP'
        ),
        valueDateJapaneseCalendar: formatJapaneseCalendarDate(i),
        valueDateAd: new Date(formatCalendarDate(i)).toLocaleDateString(
          'ja-JP'
        ),
        depositCreditType: i % 2 === 0 ? '2' : '1',
        transactionType: transactionTypeJudge(i),
        transactionTypeName: transactionTypeNameJudge(i),
        amount: i * 10000,
        checksIssuedByOtherBanksAmount: 10000,
        exchangePresentationDateJapaneseCalendar: '05-07-01',
        exchangePresentationDateAd: '2023-07-01',
        dishonoredReturnDateJapaneseCalendar: '05-07-01',
        dishonoredReturnDateAd: '2023-07-01',
        // eslint-disable-next-line no-nested-ternary
        billAndCheckType: i % 3 === 0 ? '1' : i % 2 === 0 ? '2' : '3',
        // eslint-disable-next-line no-nested-ternary
        billAndCheckTypeName:
          i % 3 === 0 ? '小切手' : i % 2 === 0 ? '約束手形' : '為替手形',
        billAndCheckNumber: '1234567',
        branchCode: '123',
        originalDepositDateJapaneseCalendar: '05-07-01',
        originalDepositDateAd: '2023-07-01',
        interestRate: '020000',
        maturityDateJapaneseCalendar: '05-07-01',
        maturityDateAd: '2023/7/1',
        period1: '9010131',
        periodInterest: 5000,
        interimPaymentInterestRate: '015000',
        interimPaymentType: '1',
        periodAfterTerm: '0100',
        interestRateAfterTerm: '017000',
        interestAfterTerm: 7000,
        totalInterest: 12000,
        taxType: '1',
        taxTypeName: '総合課金',
        taxRate: '1000',
        taxAmount: 1200,
        aftertaxInterest: 10800,
        abstract: null,
        period2: '29112',
        periodInterestPositiveAndNegativeDisplay: '1'
      };
    transactionsDetailSmbcMock.push(createTransactionsDetailSmbcMockData);
  }
  return transactionsDetailSmbcMock;
};

describe('transactionsController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
  describe('getTransactions', () => {
    it('正常系_バックエンドから送られてきた預金種目が普通の場合、Web21の入出金明細情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '***************67890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const accountId = '12345';
      const mockRequest = {
        params: {
          accountId
        },
        query: {
          dateFrom: '2023-09-01',
          dateTo: '2023-09-30'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // テストデータ
      const transactionsMockData: components['schemas']['Transactions'] = {
        inquiryResult: 'inquiryComplete',
        categoryCode: '03',
        createdDateJapaneseCalendar: '05-07-01',
        createdDateAd: '2023-07-01',
        baseDate: '2023/10/1',
        baseTime: '10:00',
        transactionDateFromJapaneseCalendar: '05/07/01',
        transactionDateFromAd: '2023/07/01',
        transactionDateToJapaneseCalendar: '29/01/31',
        transactionDateToAd: '2017/01/31',
        bankCode: '1234',
        bankNameKana: '1234ｷﾞﾝｺｳ',
        branchCode: '123',
        branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
        accountTypeCode: '01',
        //   accountTypeCode: '05',
        accountType: '普通',
        accountNumber: '**********',
        accountId: '*************',
        accountName: '123ｼﾖｳｶｲ',
        overdraftTypeHeader: '1',
        passbookOrCertificateType: '1',
        balanceBeforeTransaction: ***********,
        transactions1: createTransactionsDetailMock(),
        //   transactions1: [],
        transactions2: [],
        //   transactions2: createTransactionsDetailSmbcMock(),
        depositCount: 7,
        totalDepositAmount: ***************,
        withdrawalCount: 3,
        totalWithdrawalAmount: ***************,
        overdraftTypeTrailer: '1',
        transactionBalance: 100000,
        transactionDataCount: 6,
        totalCount: 5,
        count: 1,
        hasNext: false,
        itemKey: '20000',
        sessionInfo: 'BZDBIB112150951480831001',
        currencyCode: 'JPY'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: transactionsMockData });

      (nowDate as jest.MockedFunction<typeof nowDate>).mockReturnValue(
        new Date('2023-10-01T10:00:00')
      );

      await transactionsController.getTransactions(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/statement/12345',
        {
          params: {
            from: '2023-09-01',
            to: '2023-09-30'
          },
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(transactionsMockData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドから送られてきた預金種目が通知預金の場合、バックエンドからWeb21の入出金明細情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '***************67890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const accountId = '12345';
      const mockRequest = {
        params: {
          accountId
        },
        query: {
          dateFrom: '2023-09-01',
          dateTo: '2023-09-30'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // テストデータ
      const transactionsMockData: components['schemas']['Transactions'] = {
        inquiryResult: 'inquiryComplete',
        categoryCode: '03',
        createdDateJapaneseCalendar: '05-07-01',
        createdDateAd: '2023-07-01',
        baseDate: '2023/10/1',
        baseTime: '10:00',
        transactionDateFromJapaneseCalendar: '05/07/01',
        transactionDateFromAd: '2023/07/01',
        transactionDateToJapaneseCalendar: '29/01/31',
        transactionDateToAd: '2017/01/31',
        bankCode: '1234',
        bankNameKana: '1234ｷﾞﾝｺｳ',
        branchCode: '123',
        branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
        accountTypeCode: '05',
        accountType: '通知預金',
        accountNumber: '**********',
        accountId: '*************',
        accountName: '123ｼﾖｳｶｲ',
        overdraftTypeHeader: '1',
        passbookOrCertificateType: '1',
        balanceBeforeTransaction: ***********,
        transactions1: [],
        transactions2: createTransactionsDetailSmbcMock(),
        depositCount: 7,
        totalDepositAmount: ***************,
        withdrawalCount: 3,
        totalWithdrawalAmount: ***************,
        overdraftTypeTrailer: '1',
        transactionBalance: 100000,
        transactionDataCount: 6,
        totalCount: 5,
        count: 1,
        hasNext: false,
        itemKey: '20000',
        sessionInfo: 'BZDBIB112150951480831001',
        currencyCode: 'JPY'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: transactionsMockData });

      (nowDate as jest.MockedFunction<typeof nowDate>).mockReturnValue(
        new Date('2023-10-01T10:00:00')
      );

      await transactionsController.getTransactions(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/statement/12345',
        {
          params: {
            from: '2023-09-01',
            to: '2023-09-30'
          },
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(transactionsMockData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_入出金明細情報がサーバーエラーで取得できなかった場合エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '***************67890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const accountId = '12345';
      const mockRequest = {
        params: {
          accountId
        },
        query: {
          dateFrom: '2023-09-01',
          dateTo: '2023-09-30'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      (nowDate as jest.MockedFunction<typeof nowDate>).mockReturnValue(
        new Date('2023-10-01T10:00:00')
      );

      await transactionsController.getTransactions(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('getTransactionsDailyTotals', () => {
    it('正常系_バックエンドから入出金日次合計額を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '***************67890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const accountId = '12345';
      const mockRequest = {
        params: {
          accountId
        },
        query: {
          from: '2023-09-01',
          to: '2023-09-30'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({
        data: {
          summary: [
            {
              date: '2023-01-01',
              withdrawal: 2000000,
              deposit: 1000000
            },
            {
              date: '2023-01-02',
              withdrawal: 4000000,
              deposit: 3000000
            },
            {
              date: '2023-01-03',
              withdrawal: 6000000,
              deposit: 5000000
            }
          ]
        }
      });

      // 期待値データ
      const expectData: components['schemas']['TransactionsDailyTotals'] = {
        transactionsDailyTotals: [
          {
            date: '2023-01-01',
            totalDeposit: 1000000,
            totalWithdrawal: 2000000
          },
          {
            date: '2023-01-02',
            totalDeposit: 3000000,
            totalWithdrawal: 4000000
          },
          {
            date: '2023-01-03',
            totalDeposit: 5000000,
            totalWithdrawal: 6000000
          }
        ]
      };

      await transactionsController.getTransactionsDailyTotals(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/statement/12345/summary',
        {
          params: {
            from: '2023-09-01',
            to: '2023-09-30'
          },
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_入出金日次合計額情報がサーバーエラーで取得できなかった場合エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '***************67890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const accountId = '12345';
      const mockRequest = {
        params: {
          accountId
        },
        query: {
          dateFrom: '2023-09-01',
          dateTo: '2023-09-30'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await transactionsController.getTransactionsDailyTotals(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
