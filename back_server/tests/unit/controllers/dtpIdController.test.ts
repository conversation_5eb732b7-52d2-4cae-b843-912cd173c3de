/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction } from 'express';
import { v4 as mockUuidv4 } from 'uuid';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { logger } from '../../../src/utils/logger';
import backendRequest from '../../../src/common/api/backendRequest';
import dtpIdController from '../../../src/controllers/dtpIdController';
import { errorCodes } from '../../../src/errors/errorCodes';
import {
  AuthorizationError,
  InternalServerError,
  NotFoundError
} from '../../../src/errors/httpErrors';
import { getIdaasEncryptedCookieValue } from '../../../src/utils/getIdaasEncryptedCookieValue';
import { judgeRisk } from '../../../src/services/judgeRiskService';
import { Session, UserInfo } from '../../../src/models/session';
import { getIdaasDecryptCookieValue } from '../../../src/utils/getIdaasDecryptCookieValue';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock('../../../src/utils/dateFormat');
jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/services/judgeRiskService');
jest.mock('../../../src/utils/getIdaasEncryptedCookieValue');
jest.mock('../../../src/utils/getIdaasDecryptCookieValue');
jest.mock('uuid');

// dayjsをモック
jest.mock('dayjs', () => {
  const originalDayjs: typeof dayjs = jest.requireActual('dayjs');
  const mockedDayjs = jest.fn(() => {
    const instance: Dayjs = originalDayjs();
    instance.toISOString = jest
      .fn()
      .mockReturnValue('2024-07-16T12:40:00.000Z');
    return instance;
  });

  return Object.assign(mockedDayjs, originalDayjs, {
    extend: originalDayjs.extend,
    tz: originalDayjs.tz
  });
});

// ランダムに払い出されてしまう部分のみモック
const mockedUUID = 'mockSessionId';
(mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
  mockedUUID
);

describe('dtpIdController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('loginDtpId', () => {
    it('正常系_DTPID/PWを元にバックエンドから権限情報を取得し、怪しさ判定でエラーの時、セッションID・暗号化クッキーを返却する', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockCaulisRequestBody = {
        isLoginSucceed: true
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '********************',
        clientId: '0********9',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '********912345',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      // バックエンド宛API（dtpid-login）の返却値のモックデータ
      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********',
        valueDoorId: '**********',
        valueDoorUser: mockUserInfo
      };

      // バックエンド宛API（login-suspicious-detection）の返却値のモックデータ
      const mockCaulisResponse = {
        relativeSuspiciousValue: 'L'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({ data: mockDtpIdLoginResponse });
      backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

      // BFFからフロントへの返却値の期待値
      const expectDtpIdLoginResponse = {
        sessionId: 'mockSessionId',
        encryptedCookie: 'mockEncryptedCookie'
      };

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject(
        mockCaulisRequestBody
      );
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': '',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '6f6b52ffb1e54ba539523d9791e4a32a5ca8523d0390311a965c6c0deda75b01'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectDtpIdLoginResponse);
    });
    it('正常系_DTPID/PWを元にバックエンドから権限情報を取得し、怪しさ判定がLの時、セッションID・暗号化クッキーを返却する', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockCaulisRequestBody = {
        isLoginSucceed: true
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // バックエンドから返却されるVD権限情報
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '********************',
        clientId: '0********9',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '********912345',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      // バックエンド宛API（dtpid-login）の返却値のモックデータ
      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********',
        valueDoorId: '**********',
        valueDoorUser: mockUserInfo
      };

      // バックエンド宛API（login-suspicious-detection）の返却値のモックデータ
      const mockCaulisResponse = {
        relativeSuspiciousValue: 'L'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({ data: mockDtpIdLoginResponse });
      backendRequestSpy.mockResolvedValueOnce({ data: mockCaulisResponse });

      // BFFからフロントへの返却値の期待値
      const expectDtpIdLoginResponse = {
        sessionId: 'mockSessionId',
        encryptedCookie: 'mockEncryptedCookie'
      };

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject(
        mockCaulisRequestBody
      );
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '6f6b52ffb1e54ba539523d9791e4a32a5ca8523d0390311a965c6c0deda75b01'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectDtpIdLoginResponse);
    });
    it('正常系_DTPID/PWを元にバックエンドから権限情報を取得し、怪しさ判定がMの時、高リスクユーザーID・暗号化クッキーを返却する', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockCaulisRequestBody = {
        isLoginSucceed: true
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // バックエンド宛API（dtpid-login）の返却値のモックデータ
      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********',
        valueDoorId: '**********'
      };

      // バックエンド宛API（login-suspicious-detection）の返却値のモックデータ
      const mockCaulisResponse = {
        relativeSuspiciousValue: 'M'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({ data: mockDtpIdLoginResponse });
      backendRequestSpy.mockResolvedValueOnce({ data: mockCaulisResponse });

      // BFFからフロントへの返却値の期待値
      const expectDtpIdLoginResponse = {
        highRiskUserId: 'highRiskUserId',
        encryptedCookie: 'mockEncryptedCookie'
      };

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      // 危険度判定メソッドをモック
      const mockResult = { highRiskUserId: 'highRiskUserId' };
      (judgeRisk as jest.Mock).mockReturnValue(mockResult);

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject(
        mockCaulisRequestBody
      );
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '6f6b52ffb1e54ba539523d9791e4a32a5ca8523d0390311a965c6c0deda75b01'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectDtpIdLoginResponse);
    });
    it('異常系_DTPID/PWを元にバックエンドから権限情報を取得し、怪しさ判定がHの時、エラーを返却する', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockCaulisRequestBody = {
        isLoginSucceed: true
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // バックエンド宛API（dtpid-login）の返却値のモックデータ
      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********',
        valueDoorId: '**********'
      };

      // バックエンド宛API（login-suspicious-detection）の返却値のモックデータ
      const mockCaulisResponse = {
        relativeSuspiciousValue: 'H'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({ data: mockDtpIdLoginResponse });
      backendRequestSpy.mockResolvedValueOnce({ data: mockCaulisResponse });

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      // 危険度判定メソッドをモック
      const mockResult = new AuthorizationError(
        errorCodes.JUDGE_RISK_RESPONSE_ERROR,
        '怪しさ判定APIによるエラー'
      );
      (judgeRisk as jest.Mock).mockRejectedValue(mockResult);

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject(
        mockCaulisRequestBody
      );
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '6f6b52ffb1e54ba539523d9791e4a32a5ca8523d0390311a965c6c0deda75b01'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockNext).toHaveBeenCalledWith(
        new AuthorizationError(
          errorCodes.JUDGE_RISK_RESPONSE_ERROR,
          '怪しさ判定APIによるエラー'
        )
      );
    });
    it('異常系_DTPID情報がサーバーエラーで返却できなかった場合、エラーを返す_怪しさ判定リクエストが呼ばれない場合', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const errorData = {
        data: {
          code: 'I011-00003',
          message: 'Request error',
          httpStatusCode: 401,
          details: 'Request error'
        },
        status: 401
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });

      // POST /api/user/auth/dtpid-login APIのエラーレスポンスの期待値
      const expectErrorResponse = {
        isAxiosError: true,
        response: {
          data: {
            code: 'I011-00003',
            details: 'Request error',
            httpStatusCode: 401,
            message: 'Request error'
          },
          status: 401
        }
      };

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(1);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(mockNext).toHaveBeenCalledWith(expectErrorResponse);
    });
    it('異常系_DTPID情報がサーバーエラーで返却できなかった場合、エラーを返す_怪しさ判定リクエストで成功が返却された場合', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockCaulisRequestBody = {
        isLoginSucceed: false
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const errorData = {
        data: {
          code: 'I011-00020',
          message: 'Request error',
          httpStatusCode: 500,
          details: 'Request error'
        },
        status: 500
      };

      // バックエンド宛API（login-suspicious-detection）の返却値のモックデータ
      const mockCaulisResponse = {
        relativeSuspiciousValue: 'H'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({ data: mockCaulisResponse });

      // POST /api/user/auth/dtpid-login APIのエラーレスポンスの期待値
      const expectErrorResponse = {
        isAxiosError: true,
        response: {
          data: {
            code: 'I011-00020',
            details: 'Request error',
            httpStatusCode: 500,
            message: 'Request error'
          },
          status: 500
        }
      };

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject(
        mockCaulisRequestBody
      );
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockNext).toHaveBeenCalledWith(expectErrorResponse);
    });
    it('異常系_DTPID情報がサーバーエラーで返却できなかった場合、エラーを返す_怪しさ判定リクエストでもエラーが返却された場合', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockDtpIdLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockCaulisRequestBody = {
        isLoginSucceed: false
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const errorData = {
        data: {
          code: 'I011-00020',
          message: 'Request error',
          httpStatusCode: 500,
          details: 'Request error'
        },
        status: 500
      };
      const suspiciousErrorData = {
        data: {
          code: 'I001-00001',
          message: 'login-suspicious-detection error',
          httpStatusCode: 500,
          details: 'login-suspicious-detection error'
        },
        status: 500
      };
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockRejectedValueOnce({
        data: {
          response: suspiciousErrorData
        }
      });

      // POST /api/user/auth/dtpid-login APIのエラーレスポンスの期待値
      const expectErrorResponse = {
        isAxiosError: true,
        response: {
          data: {
            code: 'I011-00020',
            details: 'Request error',
            httpStatusCode: 500,
            message: 'Request error'
          },
          status: 500
        }
      };

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValue(
        'mockEncryptedCookie'
      );

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject(
        mockDtpIdLoginRequestBody
      );
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'x-valuedoor-id': ''
        },
        params: {},
        responseType: 'json'
      });

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject(
        mockCaulisRequestBody
      );
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockNext).toHaveBeenCalledWith(expectErrorResponse);
    });
    it('異常系_VDIDが空の場合、口座開設中エラーを返すこと', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********',
        valueDoorId: ''
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: mockDtpIdLoginResponse });

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        new NotFoundError(
          errorCodes.ACCOUNT_CREATION_IN_PROGRESS,
          '口座開設中のため、ログインできません。口座開設後にログインしてください。'
        )
      );
    });
    it('異常系_VDIDがundefinedの場合、口座開設中エラーを返すこと', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: mockDtpIdLoginResponse });

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(
        new NotFoundError(
          errorCodes.ACCOUNT_CREATION_IN_PROGRESS,
          '口座開設中のため、ログインできません。口座開設後にログインしてください。'
        )
      );
    });
    it('異常系_暗号化クッキー生成にてエラー発生時、defaultのエラーを返すこと', async () => {
      const mockRequestBody = {
        dtpId: '<EMAIL>',
        password: '********'
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // バックエンド宛API（dtpid-login）の返却値のモックデータ
      const mockDtpIdLoginResponse = {
        dtpId: '<EMAIL>',
        userUid: '**********',
        valueDoorId: '**********'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: mockDtpIdLoginResponse });

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // 暗号化クッキー生成にてエラーを返すようにモック設定
      const error = new InternalServerError(
        'E005-00009',
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
      );
      (getIdaasEncryptedCookieValue as jest.Mock).mockImplementation(() => {
        throw error;
      });

      await dtpIdController.loginDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/user/auth/dtpid-login',
        mockRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );

      expect(mockNext).toHaveBeenCalledWith(error);
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_I011-00020', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'I011-00020',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '1001'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'I011-00020'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_E011-00021', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'E011-00021',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '1002'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'E011-00021'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_I011-00022', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'I011-00022',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '1003'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'I011-00022'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_I011-00023', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'I011-00023',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '1012'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'I011-00023'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_I011-00024', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'I011-00024',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '2001'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'I011-00024'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_I011-00025', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'I011-00025',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '2002'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'I011-00025'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 バックエンドに怪しさ判定APIをリクエストする_エラーコード_I011-00026', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'I011-00026',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: {
            backendErrorCode: '2003'
          }
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();

      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: false,
        loginErrorCode: 'I011-00026'
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '9dfc6ec3812538941aa3ac32c9922180979dd54b235ad52452a2264b8bb4327a'
        },
        params: {},
        responseType: 'json'
      });

      expect(mockLoginNext).toHaveBeenCalled();
    });
    it('異常系_ログイン認証APIがステータスコード500で失敗した場合、 エラーコードが対象外の場合はバックエンドに怪しさ判定APIをリクエストしないこと', async () => {
      const mockLoginRequestBody = {
        dtpId: '<EMAIL>',
        password: '********',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockLoginRequest = {
        body: mockLoginRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockLoginResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockLoginNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockLoginRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
      const errorData = {
        data: {
          code: 'E011-00011',
          message: 'anserBizSol responded with an error',
          httpStatusCode: 500,
          details: 'Bad Request'
        },
        status: 500
      };
      backendRequestSpy.mockRejectedValueOnce({
        isAxiosError: true,
        response: errorData
      });
      // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      await dtpIdController.loginDtpId(
        mockLoginRequest,
        mockLoginResponse,
        mockLoginNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(1);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/dtpid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        dtpId: '<EMAIL>',
        password: '********'
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockLoginResponse.status).not.toHaveBeenCalled();
      expect(mockLoginNext).toHaveBeenCalled();
    });
  });
  describe('updateDtpId', () => {
    const mockSessionId = 'mockSessionId';
    const mockUserId = 'id12345';
    const mockRequestId = 'mockRequestId';
    const mockError = 'my Error';

    const mockRequest = {
      session: {
        sessionId: mockSessionId,
        userId: mockUserId,
        vdId: '**********'
      } as Session,
      headers: { requestId: mockRequestId },
      header: jest.fn()
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    it('正常系_DTPバックエンド経由VDIDとの紐付け情報を取得してセッションを更新する', async () => {
      // バックエンド宛API（api/user/id-links）の返却値のモックデータ
      const mockIdLinksResponse = {
        dtpId: '<EMAIL>',
        userUid: 'id12345',
        valueDoorId: '**********'
      };

      // backendRequest(get)をモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockIdLinksResponse });

      // BFFからフロントへの返却値の期待値
      const expectGetDtpIdResponse = {
        message: 'DTPIDを更新しました'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await dtpIdController.updateDtpId(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/user/id-links',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: mockRequestId,
            sessionId: mockSessionId,
            'user-uid': 'id12345',
            'x-valuedoor-id': '**********',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectGetDtpIdResponse);
    });
    it('異常系_ID紐付け情報リクエストがサーバーエラーで返却されなかった場合、エラーを返す', async () => {
      // backendRequest(get)をモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error(mockError));

      await dtpIdController.updateDtpId(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error(mockError));
    });
  });
  describe('issueAndLinkDtpId', () => {
    const mockSessionId = 'mockSessionId';
    const mockRequestId = 'mockRequestId';
    const mockUserId = 'id12345';
    const mockEncryptedCookie = {
      ticket: 'ticket',
      domain: 'domain',
      path: '/',
      secure: 'true',
      httponly: 'true'
    };
    const mockError = 'my Error';

    const mockRequest = {
      session: {
        sessionId: mockSessionId,
        userId: mockUserId,
        vdId: '**********'
      } as Session,
      headers: { requestId: mockRequestId },
      body: { encryptedCookie: mockEncryptedCookie },
      header: jest.fn()
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    it('正常系_DTPバックエンド経由VDIDとの紐付け情報を確認する', async () => {
      // バックエンド宛API（api/user/id-links）の返却値のモックデータ
      const mockIdLinksResponse = {
        dtpId: '<EMAIL>',
        userUid: 'id12345',
        valueDoorId: '**********'
      };

      const mockBackendRequestBody = { userId: 'id12345' };

      // 復号化メソッドをモック
      (getIdaasDecryptCookieValue as jest.Mock).mockReturnValue('id12345');

      // backendRequest(post)をモック
      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockResolvedValue({ data: {} });

      // backendRequest(get)をモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockIdLinksResponse });

      // BFFからフロントへの返却値の期待値
      const expectissueAndLinkDtpIdResponse = {
        dtpId: mockIdLinksResponse.dtpId,
        userId: mockIdLinksResponse.userUid,
        valueDoorId: mockIdLinksResponse.valueDoorId,
        encryptedCookie: mockEncryptedCookie
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await dtpIdController.issueAndLinkDtpId(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestPostSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/user/link-new-ids',
        mockBackendRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: mockRequestId,
            sessionId: mockSessionId,
            'user-uid': 'id12345',
            'x-valuedoor-id': '**********',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/user/id-links',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: mockRequestId,
            sessionId: mockSessionId,
            'user-uid': 'id12345',
            'x-valuedoor-id': '**********',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectissueAndLinkDtpIdResponse
      );
    });
    it('異常系_ID紐付け情報登録リクエストがサーバーエラーで返却されなかった場合、エラーを返す', async () => {
      // backendRequest(post)をモック
      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockRejectedValue(new Error(mockError));

      await dtpIdController.issueAndLinkDtpId(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error(mockError));
    });
    it('異常系_ID紐付け情報リクエストがサーバーエラーで返却されなかった場合、エラーを返す', async () => {
      // backendRequest(post)をモック
      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockResolvedValue({ data: {} });

      // backendRequest(get)をモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error(mockError));

      await dtpIdController.issueAndLinkDtpId(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error(mockError));
    });
  });
  describe('deleteDtpIdLink', () => {
    it('正常系_バックエンドにセッション情報を渡し、連携解除成功メッセージを返却する', async () => {
      const mockUserId = '**********';

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: { userId: mockUserId } });

      const mockRequest = {
        session: {
          userId: mockUserId
        },
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockRequestBody = {
        userId: '**********'
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // BFFからフロントへの返却値の期待値
      const expectDtpIdLoginResponse = {
        message: '紐づけ解除に成功しました。'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await dtpIdController.deleteDtpIdLink(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/user/unlink-vdid',
        mockRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '**********',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectDtpIdLoginResponse);
    });
    it('異常系_紐づけ情報取得に失敗した場合、エラーを返す', async () => {
      const mockUserId = '<EMAIL>';

      const mockRequest = {
        session: {
          userId: mockUserId
        },
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('DB Error'));

      await dtpIdController.deleteDtpIdLink(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('DB Error'));
    });
  });
});
