import { Request, Response, NextFunction } from 'express';
import classifiedController from '../../../src/controllers/classifiedController';
import {
  mobileEncryptSalt,
  clientId,
  clientSecret
} from '../../../src/config/config';

describe('getSaltValue', () => {
  it('正常系_SALT値取得', () => {
    const mockRequest = {} as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    const mockSecret = {
      mobileEncryptSalt
    };

    classifiedController.getSaltValue(mockRequest, mockResponse, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(mockSecret);
    expect(mockNext).toHaveBeenCalled();
  });
});

describe('getClientInfo', () => {
  it('正常系_クライアントID、クライアントシークレット取得', () => {
    const mockRequest = {} as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    const mockSecret = {
      clientId,
      clientSecret
    };

    classifiedController.getClientInfo(mockRequest, mockResponse, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(mockSecret);
    expect(mockNext).toHaveBeenCalled();
  });
});
