/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction } from 'express';
import usersController from '../../../src/controllers/usersController';
import { getUserInfo } from '../../../src/services/sessionService';
import { Session, UserInfo } from '../../../src/models/session';
import { serverDate } from '../../../src/utils/dateFormat';
import { logger } from '../../../src/utils/logger';
import { NotFoundError } from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';
import backendRequest from '../../../src/common/api/backendRequest';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('usersController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUsers', () => {
    it('正常系_バックエンドから口座表示設定を持つ利用者情報のみを取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // 利用者情報のモックデータ
      const mockUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1'
          }
        ],
        balanceAuthorityStatus: 'ON'
      };

      // 口座表示設定のモックデータ
      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'web21',
            displayName: '振り込み用口座2'
          },
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座3'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'web21',
            displayName: '振り込み用口座4'
          }
        ]
      };

      // 期待値
      const expectUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            bankName: '三井住友銀行',
            displayAccountName: '振り込み用口座1',
            isHidden: false,
            index: 0,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2',
            bankName: '三井住友銀行',
            displayAccountName: '振り込み用口座2',
            isHidden: true,
            index: 1,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3',
            bankName: '三井住友銀行',
            displayAccountName: '振り込み用口座3',
            isHidden: false,
            index: 2,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4',
            bankName: '三井住友銀行',
            displayAccountName: '振り込み用口座4',
            isHidden: true,
            index: 3,
            accountApiType: 'web21'
          }
        ],
        balanceAuthorityStatus: 'ON',
        serverDate: '2024/1/1'
      };

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2024/1/1'
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // 利用者情報と口座表示設定取得のbackendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      backendRequestSpy.mockResolvedValueOnce({ data: mockUsersData });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      await usersController.getUsers(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/accounts'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/common/account-settings'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectUsersData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドから口座表示設定を持つ利用者情報と口座表示設定を持たない利用者情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // 利用者情報のモックデータ
      const mockUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1'
          }
        ],
        balanceAuthorityStatus: 'ON'
      };

      // 口座表示設定のモックデータ
      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'web21',
            displayName: '振り込み用口座2'
          },
          {
            accountId: '1',
            isHidden: false,
            accountType: 'freee'
          },
          {
            accountId: '2',
            isHidden: true,
            accountType: 'freee'
          }
        ]
      };

      // 期待値
      const expectUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            bankName: '三井住友銀行',
            displayAccountName: '振り込み用口座1',
            isHidden: false,
            index: 0,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2',
            bankName: '三井住友銀行',
            displayAccountName: '振り込み用口座2',
            isHidden: true,
            index: 1,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3',
            bankName: '三井住友銀行',
            displayAccountName: null,
            isHidden: false,
            index: 2,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4',
            bankName: '三井住友銀行',
            displayAccountName: null,
            isHidden: false,
            index: 3,
            accountApiType: 'web21'
          }
        ],
        balanceAuthorityStatus: 'ON',
        serverDate: '2024/1/1'
      };

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2024/1/1'
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // 利用者情報と口座表示設定取得のbackendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      backendRequestSpy.mockResolvedValueOnce({ data: mockUsersData });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      await usersController.getUsers(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/accounts'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/common/account-settings'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectUsersData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドから口座表示設定を持たない利用者情報のみを取得する(初回ログイン時)', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // 利用者情報のモックデータ
      const mockUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1'
          }
        ],
        balanceAuthorityStatus: 'ON'
      };

      // 口座表示設定のモックデータ
      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '1',
            isHidden: false,
            accountType: 'freee'
          },
          {
            accountId: '2',
            isHidden: true,
            accountType: 'freee'
          }
        ]
      };

      // 期待値
      const expectUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3',
            bankName: '三井住友銀行',
            displayAccountName: null,
            isHidden: false,
            index: 0,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2',
            bankName: '三井住友銀行',
            displayAccountName: null,
            isHidden: false,
            index: 1,
            accountApiType: 'web21'
          },

          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4',
            bankName: '三井住友銀行',
            displayAccountName: null,
            isHidden: false,
            index: 2,
            accountApiType: 'web21'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            bankName: '三井住友銀行',
            displayAccountName: null,
            isHidden: false,
            index: 3,
            accountApiType: 'web21'
          }
        ],
        balanceAuthorityStatus: 'ON',
        serverDate: '2024/1/1'
      };

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2024/1/1'
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // 利用者情報と口座表示設定取得のbackendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      backendRequestSpy.mockResolvedValueOnce({ data: mockUsersData });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      await usersController.getUsers(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/accounts'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/common/account-settings'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectUsersData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_利用者情報がサーバーエラーで取得できなかった場合エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await usersController.getUsers(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });

    it('異常系_口座表示設定がサーバーエラーで取得できなかった場合エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      // 利用者情報のモックデータ
      const mockUsersData = {
        count: 4,
        accountList: [
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100003',
            remitterName: 'ｲﾀｸｼｬﾒｲ3'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100002',
            remitterName: 'ｲﾀｸｼｬﾒｲ2'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100004',
            remitterName: 'ｲﾀｸｼｬﾒｲ4'
          },
          {
            accountId: '**************',
            account: '************',
            branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
            branchNameKanji: '秋葉原支店',
            accountType: '普通',
            accountNumber: '1100001',
            remitterName: 'ｲﾀｸｼｬﾒｲ1'
          }
        ],
        balanceAuthorityStatus: 'ON'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({ data: mockUsersData });
      backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

      await usersController.getUsers(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('getUserInfo', () => {
    it('正常系_セッションIDをsessionServiceに引き渡し、ユーザー情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockUserInfoData = {
        vdId: 'ABCDE12345',
        userSeiMei: '東京さむる太郎',
        userTyp: '01',
        compName: '東京SAML企業',
        compId: '*********01234',
        Email: '<EMAIL>',
        dtpId: '<EMAIL>',
        userId: 'id12345'
      };

      (getUserInfo as jest.Mock).mockResolvedValueOnce(mockUserInfoData);

      await usersController.getUserInfo(mockRequest, mockResponse, mockNext);

      expect(getUserInfo).toHaveBeenCalledWith('sessionId');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockUserInfoData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_DynamoDBからユーザー情報が取得できなかった場合にエラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      (getUserInfo as jest.Mock).mockResolvedValueOnce(null);

      await usersController.getUserInfo(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(NotFoundError);
      expect(mockNext.mock.calls[0][0].errorCode).toEqual(
        errorCodes.NOT_FOUND_USER_INFO
      );
    });

    it('異常系_DynamoDBからエラーを取得した場合にエラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*********0*********0',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      (getUserInfo as jest.Mock).mockRejectedValue(new Error('DB Error'));

      await usersController.getUserInfo(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('DB Error'));
    });
  });
});
