import { Request, Response, NextFunction } from 'express';
import backendRequest from '../../../src/common/api/backendRequest';
import balanceController from '../../../src/controllers/balanceController';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import { serverDate, serverTime } from '../../../src/utils/dateFormat';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('balanceController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getBalance', () => {
    it('正常系_バックエンドからWeb21の指定口座残高情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        params: {
          accountIds: '12345'
        },
        query: {
          date: '2023-09-01'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockBalanceData = {
        count: 1,
        accounts: [
          {
            contactName: 'test',
            accountId: '**************',
            branchName: '東京支店',
            branchCode: '111',
            accountType: '普通',
            accountNumber: '1234567',
            baseDate: '2023-12-01',
            baseTime: '08:00:30+09:00',
            currentBalance: 1000000,
            checksIssuedByOtherBanks: 2000000,
            overdraftLimit: 3000000,
            withdrawableBalance: 4000000,
            bankName: '三井住友銀行',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            displayAccountName: '振込用口座1',
            displayAccountFlag: true
          }
        ],
        serverDate: '2024/1/1'
      };

      const expectBalanceData = {
        count: 1,
        accounts: [
          {
            contactName: 'test',
            accountId: '**************',
            branchName: '東京支店',
            branchCode: '111',
            accountType: '普通',
            accountNumber: '1234567',
            baseDate: '2023/12/1',
            baseTime: '8:00',
            baseDateTime: '2023/12/1 8:00',
            currentBalance: 1000000,
            checksIssuedByOtherBanks: 2000000,
            overdraftLimit: 3000000,
            withdrawableBalance: 4000000,
            bankName: '三井住友銀行',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            displayAccountName: '振込用口座1',
            displayAccountFlag: true
          }
        ],
        serverDate: '2024/1/1'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2024/1/1'
      );

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockBalanceData });

      await balanceController.getBalance(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/balance/12345',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {
            date: mockRequest.query.date
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectBalanceData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドからbaseDateがnullで返ってきた場合、baseDate、baseDateTimeにサーバー日付を設定して返却すること', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        params: {
          accountIds: '12345'
        },
        query: {
          date: '2023-09-01'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockBalanceData = {
        count: 1,
        accounts: [
          {
            contactName: 'test',
            accountId: '**************',
            branchName: '東京支店',
            branchCode: '111',
            accountType: '普通',
            accountNumber: '1234567',
            baseDate: null,
            baseTime: '08:00:00+09:00',
            currentBalance: 1000000,
            checksIssuedByOtherBanks: 2000000,
            overdraftLimit: 3000000,
            withdrawableBalance: 4000000,
            bankName: '三井住友銀行',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            displayAccountName: '振込用口座1',
            displayAccountFlag: true
          }
        ],
        serverDate: '2024/1/1'
      };

      const expectBalanceData = {
        count: 1,
        accounts: [
          {
            contactName: 'test',
            accountId: '**************',
            branchName: '東京支店',
            branchCode: '111',
            accountType: '普通',
            accountNumber: '1234567',
            baseDate: '2024/1/1',
            baseTime: '8:00',
            baseDateTime: '2024/1/1 8:00',
            currentBalance: 1000000,
            checksIssuedByOtherBanks: 2000000,
            overdraftLimit: 3000000,
            withdrawableBalance: 4000000,
            bankName: '三井住友銀行',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            displayAccountName: '振込用口座1',
            displayAccountFlag: true
          }
        ],
        serverDate: '2024/1/1'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2024/1/1'
      );

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockBalanceData });

      await balanceController.getBalance(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/balance/12345',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {
            date: mockRequest.query.date
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectBalanceData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドからbaseTimeがnullで返ってきた場合、baseTime、baseDateTimeにサーバー日付を設定してnullで返却すること', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        params: {
          accountIds: '12345'
        },
        query: {
          date: '2023-09-01'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockBalanceData = {
        count: 1,
        accounts: [
          {
            contactName: 'test',
            accountId: '**************',
            branchName: '東京支店',
            branchCode: '111',
            accountType: '普通',
            accountNumber: '1234567',
            baseDate: '2023-12-01',
            baseTime: null,
            currentBalance: 1000000,
            checksIssuedByOtherBanks: 2000000,
            overdraftLimit: 3000000,
            withdrawableBalance: 4000000,
            bankName: '三井住友銀行',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            displayAccountName: '振込用口座1',
            displayAccountFlag: true
          }
        ],
        serverDate: '2024/1/1'
      };

      const expectBalanceData = {
        count: 1,
        accounts: [
          {
            contactName: 'test',
            accountId: '**************',
            branchName: '東京支店',
            branchCode: '111',
            accountType: '普通',
            accountNumber: '1234567',
            baseDate: '2023/12/1',
            baseTime: '9:00',
            baseDateTime: '2023/12/1 9:00',
            currentBalance: 1000000,
            checksIssuedByOtherBanks: 2000000,
            overdraftLimit: 3000000,
            withdrawableBalance: 4000000,
            bankName: '三井住友銀行',
            remitterName: 'ｲﾀｸｼｬﾒｲ1',
            displayAccountName: '振込用口座1',
            displayAccountFlag: true
          }
        ],
        serverDate: '2024/1/1'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (serverDate as jest.MockedFunction<typeof serverDate>).mockReturnValue(
        '2024/1/1'
      );
      (serverTime as jest.MockedFunction<typeof serverTime>).mockReturnValue(
        '9:00'
      );

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockBalanceData });

      await balanceController.getBalance(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/balance/12345',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {
            date: mockRequest.query.date
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectBalanceData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_指定口座残高情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        params: {
          accountIds: '12345'
        },
        query: {
          date: '2023-09-01'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await balanceController.getBalance(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/balance/12345',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {
            date: mockRequest.query.date
          },
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
