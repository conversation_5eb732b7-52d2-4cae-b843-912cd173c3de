import { Request, Response, NextFunction } from 'express';
import { getWorkBlockage } from '../../../src/services/workBlockageServices';
import { WorkBlockage } from '../../../src/models/workBlockage';
import workBlockageController from '../../../src/controllers/workBlockageController';

// workBlockageServicesをモック
jest.mock('../../../src/services/workBlockageServices');

describe('getWorkBlockageStatus', () => {
  it('正常系_functionIdをDynamoDBに引き渡し、機能の閉塞状況を取得する', async () => {
    const mockFunctionId = '0101';
    const mockRequest = {
      query: {
        functionId: mockFunctionId
      }
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    const mockWorkBlockageStatus = { status: '0' };

    const mockWorkBlockage: WorkBlockage = {
      status: '0',
      functionId: mockFunctionId,
      functionName: 'テスト'
    };

    (getWorkBlockage as jest.Mock).mockResolvedValueOnce(mockWorkBlockage);

    await workBlockageController.getWorkBlockageStatus(
      mockRequest,
      mockResponse,
      mockNext
    );

    expect(getWorkBlockage).toHaveBeenCalledWith(mockFunctionId);
    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(mockWorkBlockageStatus);
    expect(mockNext).toHaveBeenCalled();
  });

  it('正常系_DynamoDBからユーザー情報が取得できなかった場合に0を返す', async () => {
    const mockFunctionId = '0101';
    const mockWorkBlockageStatus = { status: '0' };
    const mockRequest = {
      query: {
        functionId: mockFunctionId
      }
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    (getWorkBlockage as jest.Mock).mockResolvedValueOnce(null);

    await workBlockageController.getWorkBlockageStatus(
      mockRequest,
      mockResponse,
      mockNext
    );

    expect(mockNext).toHaveBeenCalled();
    expect(mockResponse.json).toHaveBeenCalledWith(mockWorkBlockageStatus);
  });

  it('異常系_DynamoDBからエラーを取得した場合にエラーを返す', async () => {
    const mockFunctionId = '0101';
    const mockRequest = {
      query: {
        functionId: mockFunctionId
      }
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    (getWorkBlockage as jest.Mock).mockRejectedValue(new Error('DB Error'));

    await workBlockageController.getWorkBlockageStatus(
      mockRequest,
      mockResponse,
      mockNext
    );

    expect(mockNext).toHaveBeenCalledWith(new Error('DB Error'));
  });
});
