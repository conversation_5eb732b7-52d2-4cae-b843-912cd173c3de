/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction } from 'express';
import { v4 as mockUuidv4 } from 'uuid';
import sessionController from '../../../src/controllers/sessionController';
import {
  deleteSession,
  saveSession
} from '../../../src/services/sessionService';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import { unixTime } from '../../../src/utils/dateFormat';
import { getHighRiskUser } from '../../../src/services/highRiskUserService';
import { HighRiskUser } from '../../../src/models/highRiskUser';
import {
  AuthorizationError,
  BadRequestError,
  NotFoundError
} from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';
import dynamoDBClient from '../../../src/utils/dynamoDB';
import { checkOtpAuthenticationResult } from '../../../src/utils/checkOtpAuthenticationResult';
import { body } from 'express-validator';

jest.mock('uuid');
jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/services/highRiskUserService');
jest.mock('../../../src/utils/dateFormat');
jest.mock('../../../src/utils/checkOtpAuthenticationResult');

describe('sessionController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('deleteSession', () => {
    it('正常系_削除対象のセッションIDをsessionServiceに引き渡し、正常レスポンスを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockResponseData = {
        message: 'セッション情報を削除しました。'
      };

      await sessionController.deleteSession(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(deleteSession).toHaveBeenCalledWith('sessionId');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResponseData);
    });

    it('異常系_セッション削除に失敗した場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'sessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      (deleteSession as jest.Mock).mockRejectedValue(new Error('DB Error'));

      await sessionController.deleteSession(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('DB Error'));
    });
  });

  describe('getSessionId', () => {
    it('正常系_高リスクユーザーデータを取得し、セッション情報を登録する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockHighRiskUser: HighRiskUser = {
        highRiskUserId: 'highRiskUserId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          authorization: 'Bearer highRiskUserId'
        },
        query: {
          encryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          },
          idaasEncryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          }
        }
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // モックデータを返却
      (getHighRiskUser as jest.Mock).mockResolvedValueOnce(mockHighRiskUser);
      (checkOtpAuthenticationResult as jest.Mock).mockResolvedValueOnce(true);
      (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
        1699507328
      );
      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      const mockResponseData = {
        sessionId: 'mockSessionId'
      };

      await sessionController.getSessionId(mockRequest, mockResponse, mockNext);

      expect(saveSession).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResponseData);
    });
    it('異常系_authHeaderに値が存在しない場合、エラーを返す', async () => {
      const mockRequest = {
        query: {
          encryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          },
          idaasEncryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          }
        }
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      await sessionController.getSessionId(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(BadRequestError);
      expect(mockNext.mock.calls[0][0].errorCode).toEqual(
        errorCodes.AUTH_HEADER_EMPTY_ERROR
      );
    });
    it('異常系_HighRiskUserがない場合はNOT_HIGH_RISK_USERのエラーがnext()に渡されること', async () => {
      const mockSend = jest.spyOn(dynamoDBClient, 'send');

      const mockReq = {
        headers: {
          authorization: 'Bearer highRiskUserId'
        },
        query: {
          encryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          },
          idaasEncryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          }
        },
        originalUrl: '/some/endpoint'
      } as unknown as Request;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      // モックデータ
      const mockResponse = {};

      // モックデータを返却
      const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
      mockSend.mockImplementation(mockReturnedValue);

      await sessionController.getSessionId(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(NotFoundError);
      expect(mockNext.mock.calls[0][0].errorCode).toEqual(
        errorCodes.NOT_HIGH_RISK_USER
      );
    });
    it('異常系_expirationTimeが現在時刻よりも前の場合はEXPIRED_HIGH_RISK_USERのエラーがnext()に渡されること', async () => {
      const mockReq = {
        headers: {
          authorization: 'Bearer highRiskUserId'
        },
        query: {
          encryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          },
          idaasEncryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          }
        }
      } as unknown as Request;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockHighRiskUser: HighRiskUser = {
        highRiskUserId: 'highRiskUserId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      // モックデータを返却
      (getHighRiskUser as jest.Mock).mockResolvedValueOnce(mockHighRiskUser);
      (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
        1699507329
      );

      await sessionController.getSessionId(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(AuthorizationError);
      expect(mockNext.mock.calls[0][0].errorCode).toEqual(
        errorCodes.EXPIRED_HIGH_RISK_USER
      );
    });
    it('異常系otpAuthenticationResultがfalseの場合はJUDGE_RISK_RESPONSE_ERRORのエラーがnext()に渡されること', async () => {
      const mockReq = {
        headers: {
          authorization: 'Bearer highRiskUserId'
        },
        query: {
          encryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          },
          idaasEncryptedCookie: {
            ticket: 'ticket',
            domain: 'domain',
            path: 'path',
            secure: 'secure',
            httponly: 'httponly'
          }
        },
        originalUrl: '/some/endpoint'
      } as unknown as Request;
      const mockRes = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockHighRiskUser: HighRiskUser = {
        highRiskUserId: 'highRiskUserId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      // モックデータを返却
      (getHighRiskUser as jest.Mock).mockResolvedValueOnce(mockHighRiskUser);
      (unixTime as jest.MockedFunction<typeof unixTime>).mockReturnValue(
        1699507328
      );

      await sessionController.getSessionId(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(AuthorizationError);
      expect(mockNext.mock.calls[0][0].errorCode).toEqual(
        errorCodes.JUDGE_RISK_RESPONSE_ERROR
      );
    });
  });
});
