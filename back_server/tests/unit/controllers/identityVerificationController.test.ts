/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction } from 'express';
import { logger } from '../../../src/utils/logger';
import { checkClosingStatus } from '../../../src/utils/checkClosingStatus';
import backendRequest from '../../../src/common/api/backendRequest';
import identityVerificationController from '../../../src/controllers/identityVerificationController';
import {
  InternalServerError,
  NotFoundError
} from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';

jest.mock('../../../src/utils/dateFormat');
jest.mock('../../../src/utils/checkClosingStatus');

describe('identityVerificationController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getOpenAccountApplicationTemporary', () => {
    it('正常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が代表者、STEP1、本人確認実施済みの場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [
          {
            role: 'REPRESENTATIVE',
            name: {
              familyName: '若草',
              givenName: '太郎',
              familyNameKana: 'ワカクサ',
              givenNameKana: 'タロウ',
              familyNameAlphabetic: 'WAKAKUSA',
              givenNameAlphabetic: 'TARO'
            },
            position: '部長',
            birthDate: {
              year: 1999,
              month: 1,
              day: 1
            },
            address: {
              postcode: '1350061',
              prefecture: '東京都',
              city: '江東区',
              street: '豊洲'
            }
          }
        ],
        validationStatus: {
          isPriorConfirmationApplicationDataValid: false,
          isIdentificationApplicationDataValid: false,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: true,
          isDocumentSubmissionApplicationDataValid: true
        },
        preConfirmData: {
          applicantRole: 'REPRESENTATIVE'
        }
      };

      const expectResponseData = {
        isIdentityVerified: true,
        userType: 'REPRESENTATIVE',
        validationStatus: 'STEP1'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が代理人、STEP2、本人確認実施済みでない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [
          {
            role: 'BENEFICIARY1',
            name: {
              familyNameKana: 'サトウ',
              givenNameKana: 'ジロウ'
            },
            position: '課長'
          }
        ],
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: false,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: true,
          isDocumentSubmissionApplicationDataValid: true
        },
        preConfirmData: {
          applicantRole: 'AGENT'
        }
      };

      const expectResponseData = {
        isIdentityVerified: false,
        userType: 'AGENT',
        validationStatus: 'STEP2'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が代理人、STEP3、本人確認実施済みでない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [
          {
            role: 'BENEFICIARY1',
            name: {
              familyNameKana: 'サトウ',
              givenNameKana: 'ジロウ'
            },
            position: '課長'
          }
        ],
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: false,
          isIdentificationBeneficiaryApplicationDataValid: true,
          isDocumentSubmissionApplicationDataValid: true
        },
        preConfirmData: {
          applicantRole: 'AGENT'
        }
      };

      const expectResponseData = {
        isIdentityVerified: false,
        userType: 'AGENT',
        validationStatus: 'STEP3'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が実質的支配者、STEP5、本人確認実施済みでない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************3'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [
          {
            role: 'BENEFICIARY2',
            name: {
              familyName: '若草',
              givenName: '太郎',
              familyNameKana: 'ワカクサ',
              givenNameKana: 'タロウ',
              familyNameAlphabetic: 'WAKAKUSA',
              givenNameAlphabetic: 'TARO'
            },
            position: '部長'
          },
          {
            role: 'BENEFICIARY1',
            name: {
              familyNameKana: 'サトウ',
              givenNameKana: 'ジロウ'
            },
            position: '課長'
          }
        ],
        beneficiaryIdentificationData: {
          3: {
            name: '佐藤 三郎',
            status: 'NECESSARY'
          }
        },
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: true,
          isDocumentSubmissionApplicationDataValid: false
        }
      };

      const expectResponseData = {
        isIdentityVerified: false,
        userType: 'BENEFICIARY3',
        validationStatus: 'STEP5'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が代理人、STEP5、本人確認実施済みでない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [
          {
            role: 'BENEFICIARY2',
            name: {
              familyName: '若草',
              givenName: '太郎',
              familyNameKana: 'ワカクサ',
              givenNameKana: 'タロウ',
              familyNameAlphabetic: 'WAKAKUSA',
              givenNameAlphabetic: 'TARO'
            },
            position: '部長'
          },
          {
            role: 'BENEFICIARY1',
            name: {
              familyNameKana: 'サトウ',
              givenNameKana: 'ジロウ'
            },
            position: '課長'
          }
        ],
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true
        },
        preConfirmData: {
          applicantRole: 'AGENT'
        }
      };

      const expectResponseData = {
        isIdentityVerified: false,
        userType: 'AGENT',
        validationStatus: 'STEP5'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号14桁を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が実質的支配者、STEP4、本人確認実施済みの場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************1'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [
          {
            role: 'BENEFICIARY1',
            name: {
              familyNameKana: 'サトウ',
              givenNameKana: 'ジロウ'
            },
            position: '課長'
          }
        ],
        beneficiaryIdentificationData: {
          1: {
            name: '佐藤 二郎',
            status: 'COMPLETED_IN_STEP2'
          }
        },
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: false,
          isDocumentSubmissionApplicationDataValid: true
        }
      };

      const expectResponseData = {
        isIdentityVerified: true,
        userType: 'BENEFICIARY1',
        validationStatus: 'STEP4'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号14桁を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が実質的支配者、STEP4、本人確認実施済みでない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************2'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [],
        beneficiaryIdentificationData: {
          2: {
            name: '佐藤 二郎',
            status: 'NECESSARY'
          }
        },
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: false,
          isDocumentSubmissionApplicationDataValid: true
        }
      };

      const expectResponseData = {
        isIdentityVerified: false,
        userType: 'BENEFICIARY2',
        validationStatus: 'STEP4'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('正常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_本人確認実施者が代理人、undefined、本人確認実施済みでない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [],
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: true,
          isDocumentSubmissionApplicationDataValid: true
        },
        preConfirmData: {
          applicantRole: 'AGENT'
        }
      };

      const expectResponseData = {
        isIdentityVerified: false,
        userType: 'AGENT',
        validationStatus: 'undefined'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponseData);
    });
    it('異常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_バックエンドからの項目がない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {};

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      try {
        await identityVerificationController.getOpenAccountApplicationTemporary(
          mockRequest,
          mockResponse,
          mockNext
        );
      } catch (error) {
        expect(backendRequestSpy).toHaveBeenCalledWith(
          'http://localhost:9999/api/open-bank-account/applications/*************',
          {
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              channel: 'MOBILE',
              requestId: 'mockRequestId',
              sessionId: '',
              'user-uid': '',
              'x-valuedoor-id': '',
              ip: 'mockIp'
            },
            params: {},
            responseType: 'json'
          }
        );
        expect(error).toBeInstanceOf(NotFoundError);
        expect((error as NotFoundError).statusCode).toBe(404);
        expect((error as NotFoundError).errorCode).toEqual('I005-00015');
        expect((error as NotFoundError).errorDetail).toBe(
          '該当する本人確認実施者が見つかりませんでした。手続き番号をお確かめの上、再入力してください。'
        );
      }
    });
    it('異常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_実質的支配者_バックエンドからの項目がない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************1'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {};

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      try {
        await identityVerificationController.getOpenAccountApplicationTemporary(
          mockRequest,
          mockResponse,
          mockNext
        );
      } catch (error) {
        expect(backendRequestSpy).toHaveBeenCalledWith(
          'http://localhost:9999/api/open-bank-account/applications/*************',
          {
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              channel: 'MOBILE',
              requestId: 'mockRequestId',
              sessionId: '',
              'user-uid': '',
              'x-valuedoor-id': '',
              ip: 'mockIp'
            },
            params: {},
            responseType: 'json'
          }
        );
        expect(error).toBeInstanceOf(NotFoundError);
        expect((error as NotFoundError).statusCode).toBe(404);
        expect((error as NotFoundError).errorCode).toEqual('I005-00015');
        expect((error as NotFoundError).errorDetail).toBe(
          '該当する本人確認実施者が見つかりませんでした。手続き番号をお確かめの上、再入力してください。'
        );
      }
    });
    it('異常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_実質的支配者番号に対応する実質的支配者の項目がない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************1'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [],
        beneficiaryIdentificationData: {
          2: {
            name: '佐藤 二郎',
            status: 'NECESSARY'
          }
        },
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: false,
          isDocumentSubmissionApplicationDataValid: true
        }
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      try {
        await identityVerificationController.getOpenAccountApplicationTemporary(
          mockRequest,
          mockResponse,
          mockNext
        );
      } catch (error) {
        expect(backendRequestSpy).toHaveBeenCalledWith(
          'http://localhost:9999/api/open-bank-account/applications/*************',
          {
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              channel: 'MOBILE',
              requestId: 'mockRequestId',
              sessionId: '',
              'user-uid': '',
              'x-valuedoor-id': '',
              ip: 'mockIp'
            },
            params: {},
            responseType: 'json'
          }
        );
        expect(error).toBeInstanceOf(NotFoundError);
        expect((error as NotFoundError).statusCode).toBe(404);
        expect((error as NotFoundError).errorCode).toEqual('I005-00016');
        expect((error as NotFoundError).errorDetail).toBe(
          '本人確認が不要な実質的支配者でした。'
        );
      }
    });
    it('異常系_お手続き番号を元に、バックエンドから口座開設申込情報一時保存確認情報を取得する_実質的支配者番号に対応する実質的支配者の本人確認が不要の場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************1'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mocKBackendResponseData = {
        identificationData: [],
        beneficiaryIdentificationData: {
          1: {
            name: '佐藤 二郎',
            status: 'UNNECESSARY'
          }
        },
        validationStatus: {
          isPriorConfirmationApplicationDataValid: true,
          isIdentificationApplicationDataValid: true,
          isMainApplicationDataValid: true,
          isIdentificationBeneficiaryApplicationDataValid: false,
          isDocumentSubmissionApplicationDataValid: true
        }
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      try {
        await identityVerificationController.getOpenAccountApplicationTemporary(
          mockRequest,
          mockResponse,
          mockNext
        );
      } catch (error) {
        expect(backendRequestSpy).toHaveBeenCalledWith(
          'http://localhost:9999/api/open-bank-account/applications/*************',
          {
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              channel: 'MOBILE',
              requestId: 'mockRequestId',
              sessionId: '',
              'user-uid': '',
              'x-valuedoor-id': '',
              ip: 'mockIp'
            },
            params: {},
            responseType: 'json'
          }
        );
        expect(error).toBeInstanceOf(NotFoundError);
        expect((error as NotFoundError).statusCode).toBe(404);
        expect((error as NotFoundError).errorCode).toEqual('I005-00016');
        expect((error as NotFoundError).errorDetail).toBe(
          '本人確認が不要な実質的支配者でした。'
        );
      }
    });
    it('異常系_口座開設申込情報一時保存確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await identityVerificationController.getOpenAccountApplicationTemporary(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
  describe('getOpenAccountApplicationScreeningStatuses', () => {
    it('正常系_バックエンドから口座開設行内審査ステータスを取得する_必須項目確認', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockScreeningStatusesData = {
        data: {
          referenceNumber: '*************',
          screeningStatus: 'SCREENING'
        }
      };
      const expectScreeningStatusesData = {
        isAccountOpeningRequestSubmitted: true,
        isRepresentativeHasFault: false,
        isAgentHasFault: false,
        isBeneficiary1HasFault: false,
        isBeneficiary2HasFault: false,
        isBeneficiary3HasFault: false,
        isBeneficiary4HasFault: false
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockScreeningStatusesData });

      await identityVerificationController.getOpenAccountApplicationScreeningStatuses(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/screening',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectScreeningStatusesData
      );
    });
    it('正常系_バックエンドから口座開設行内審査ステータスを取得する_口座開設ステータスが存在しない場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockScreeningStatusesData = {};
      const expectScreeningStatusesData = {
        isAccountOpeningRequestSubmitted: false,
        isRepresentativeHasFault: false,
        isAgentHasFault: false,
        isBeneficiary1HasFault: false,
        isBeneficiary2HasFault: false,
        isBeneficiary3HasFault: false,
        isBeneficiary4HasFault: false
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockScreeningStatusesData });

      await identityVerificationController.getOpenAccountApplicationScreeningStatuses(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/screening',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectScreeningStatusesData
      );
    });
    it('正常系_バックエンドから口座開設行内審査ステータスを取得する_口座開設ステータスが返却の場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockScreeningStatusesData = {
        data: {
          referenceNumber: '*************',
          screeningStatus: 'RETURNED',
          screeningResults: {
            identificationData: [
              {
                role: 'REPRESENTATIVE',
                hasFault: true
              },
              {
                role: 'AGENT',
                hasFault: true
              }
            ]
          }
        }
      };
      const expectScreeningStatusesData = {
        isAccountOpeningRequestSubmitted: false,
        isRepresentativeHasFault: true,
        isAgentHasFault: true,
        isBeneficiary1HasFault: false,
        isBeneficiary2HasFault: false,
        isBeneficiary3HasFault: false,
        isBeneficiary4HasFault: false
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockScreeningStatusesData });

      await identityVerificationController.getOpenAccountApplicationScreeningStatuses(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/screening',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectScreeningStatusesData
      );
    });
    it('正常系_バックエンドから口座開設行内審査ステータスを取得する_実質的支配者がいる場合', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************1'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockScreeningStatusesData = {
        data: {
          referenceNumber: '*************',
          screeningStatus: 'RETURNED',
          screeningResults: {
            identificationData: [
              {
                role: 'BENEFICIARY1',
                hasFault: true
              },
              {
                role: 'REPRESENTATIVE',
                hasFault: true
              },
              {
                role: 'AGENT',
                hasFault: true
              },
              {
                role: 'BENEFICIARY2',
                hasFault: false
              },
              {
                role: 'BENEFICIARY3',
                hasFault: true
              },
              {
                role: 'BENEFICIARY4',
                hasFault: false
              }
            ]
          }
        }
      };
      const expectScreeningStatusesData = {
        isAccountOpeningRequestSubmitted: false,
        isRepresentativeHasFault: true,
        isAgentHasFault: true,
        isBeneficiary1HasFault: true,
        isBeneficiary2HasFault: false,
        isBeneficiary3HasFault: true,
        isBeneficiary4HasFault: false
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockScreeningStatusesData });

      await identityVerificationController.getOpenAccountApplicationScreeningStatuses(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/screening',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectScreeningStatusesData
      );
    });
    it('異常系_口座開設行内審査ステータス情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await identityVerificationController.getOpenAccountApplicationScreeningStatuses(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
  describe('searchAddress', () => {
    it('正常系_バックエンドから郵便番号に合致する住所一覧を取得する', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          postcode: '1000001'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockSearchAddressResponse = [
        {
          entryId: 1,
          postcode: '1000001',
          prefectureKana: 'ﾄｳｷｮｳﾄ',
          cityKana: 'ﾁﾖﾀﾞｸ',
          streetKana: 'ﾁﾖﾀﾞ',
          prefecture: '東京都',
          city: '千代田区',
          street: '千代田'
        }
      ];
      const expectSearchAddressResponse = [
        {
          entryId: 1,
          postcode: '1000001',
          prefectureKana: 'ﾄｳｷｮｳﾄ',
          cityKana: 'ﾁﾖﾀﾞｸ',
          streetKana: 'ﾁﾖﾀﾞ',
          prefecture: '東京都',
          city: '千代田区',
          street: '千代田'
        }
      ];

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockSearchAddressResponse });

      await identityVerificationController.searchAddress(
        mockRequest,
        mockResponse,
        mockNext
      );

      const postcode = '1000001';
      expect(backendRequestSpy).toHaveBeenCalledWith(
        `http://localhost:9999/api/open-bank-account/address/${postcode}`,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectSearchAddressResponse
      );
    });
    it('異常系_バックエンドから郵便番号に合致する住所が存在しなかった時NotFoundErrorを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          postcode: '1000001'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const errorData = {
        code: 'E001-00004',
        message: 'Not Found',
        details: {
          errorDetail: 'there is no documentType: testType'
        }
      };

      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue({
        isAxiosError: true,
        response: {
          data: errorData,
          status: 404
        }
      });

      await identityVerificationController.searchAddress(
        mockRequest,
        mockResponse,
        mockNext
      );

      const postcode = '1000001';
      expect(backendRequestSpy).toHaveBeenCalledWith(
        `http://localhost:9999/api/open-bank-account/address/${postcode}`,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(
        new NotFoundError(
          errorCodes.NOT_FOUND_ADDRESS,
          '該当する住所が見つかりませんでした。お手数ですが、正しい郵便番号をご確認の上、再度ご入力ください。'
        )
      );
    });
    it('異常系_お手続き情報確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          postcode: '1000001'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await identityVerificationController.searchAddress(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
  describe('saveIdentityVerification', () => {
    it('正常系_バックエンドへ本人確認情報を送り保存結果を返却する(全項目) - お手続き番号13桁', async () => {
      const mockRequestBody = {
        referenceNumber: '*************',
        role: 'AGENT',
        dataSource: 'eKYC',
        familyName: '若草',
        givenName: '太郎',
        familyNameKana: 'ワカクサ',
        givenNameKana: 'タロウ',
        familyNameAlphabetic: 'WAKAKUSA',
        givenNameAlphabetic: 'TARO',
        hepburnStyle: 'true',
        position: '部長',
        birthYear: 1999,
        birthMonth: 11,
        birthDay: 1,
        postCode: '1234567',
        prefecture: '東京都',
        city: '港区六本木',
        sectionNumberAndBuildingName: '1-2-3 SMBCビル12階'
      } as unknown as Request;

      // バックエンド宛リクエストボディーの期待値
      const mockBackendRequestBody = {
        role: 'AGENT',
        dataSource: 'eKYC',
        name: {
          familyName: '若草',
          givenName: '太郎',
          familyNameKana: 'ワカクサ',
          givenNameKana: 'タロウ',
          familyNameAlphabetic: 'WAKAKUSA',
          givenNameAlphabetic: 'TARO',
          hepburnStyle: true
        },
        position: '部長',
        birthDate: {
          year: 1999,
          month: 11,
          day: 1
        },
        address: {
          postCode: '1234567',
          prefecture: '東京都',
          city: '港区六本木',
          sectionNumberAndBuildingName: '1-2-3 SMBCビル12階'
        }
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockSaveIdentityVerificationResponse = {
        message: '本人確認情報を保存しました。'
      };
      const expectSaveIdentityVerificationResponse = {
        message: '本人確認情報を保存しました。'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({
        data: mockSaveIdentityVerificationResponse
      });

      await identityVerificationController.saveIdentityVerification(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/identification',
        mockBackendRequestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectSaveIdentityVerificationResponse
      );
    });
    it('正常系_バックエンドへ本人確認情報を送り保存結果を返却する(必須項目のみ) - お手続き番号14桁', async () => {
      const mockRequestBody = {
        referenceNumber: '*************',
        role: 'BENEFICIARY',
        dataSource: 'eKYC'
      } as unknown as Request;
      const mockBackendRequestBody = {
        role: 'BENEFICIARY',
        dataSource: 'eKYC'
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockSaveIdentityVerificationResponse = {
        message: '本人確認情報を保存しました。'
      };
      const expectSaveIdentityVerificationResponse = {
        message: '本人確認情報を保存しました。'
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({
        data: mockSaveIdentityVerificationResponse
      });

      await identityVerificationController.saveIdentityVerification(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/identification',
        mockBackendRequestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectSaveIdentityVerificationResponse
      );
    });
    it('異常系_お手続き情報確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequestBody = {
        referenceNumber: '**********',
        familyName: '若草',
        givenName: '太郎',
        familyNameKana: 'ワカクサ',
        givenNameKana: 'タロウ',
        familyNameAlphabetic: 'WAKAKUSA',
        givenNameAlphabetic: 'TARO',
        position: '部長',
        birthYear: 1999,
        birthMonth: 1,
        birthDay: 1,
        postcode: '1234567',
        prefecture: '東京都',
        city: '江東区',
        sectionNumberAndBuildingName: '1-2-3 SMBCビル12階'
      } as unknown as Request;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await identityVerificationController.saveIdentityVerification(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        new InternalServerError(
          errorCodes.SAVE_IDENTITYVERIFICATION_FAILED,
          '本人確認情報の保存に失敗しました。'
        )
      );
    });
  });
  describe('requestEkycUrl', () => {
    it('正常系_お手続き番号が有効かどうかを確認し、バックエンドからeKYCのURLを取得する', async () => {
      const mockRequestBody = {
        referenceNumber: '*************',
        uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
        userType: '01'
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockNext = jest.fn() as NextFunction;

      const mockBackendRequestBody = { userType: '01' };

      const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

      const mockEkycUrlData = 'https://www.ekyc';
      const expectEkycUrlData = { ekycUrl: 'https://www.ekyc' };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockResolvedValueOnce({ data: mockEkycUrlData });

      await identityVerificationController.requestEkycUrl(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestGetSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/token',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(backendRequestPostSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/request-ekyc-url',
        mockBackendRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectEkycUrlData);
    });

    it('正常系_お手続き番号が14桁の時に13桁に変更しバックエンドからeKYCのURLを取得する', async () => {
      const mockRequestBody = {
        referenceNumber: '*************1',
        uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
        userType: '01'
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockNext = jest.fn() as NextFunction;

      const mockBackendRequestBody = { userType: '01' };

      const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

      const mockEkycUrlData = 'https://www.ekyc';
      const expectEkycUrlData = { ekycUrl: 'https://www.ekyc' };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockResolvedValueOnce({ data: mockEkycUrlData });

      await identityVerificationController.requestEkycUrl(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestGetSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/token',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(backendRequestPostSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/request-ekyc-url',
        mockBackendRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectEkycUrlData);
    });

    it('異常系_UUIDの値がDBの内容と一致しない場合、エラーを返す', async () => {
      const mockRequestBody = {
        referenceNumber: '*************',
        uuid: 'abcde',
        userType: '01'
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;

      const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      await identityVerificationController.requestEkycUrl(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(
        new NotFoundError(
          errorCodes.NOT_FOUND_UUID,
          'UUIDが見つかりませんでした。'
        )
      );
    });

    it('異常系_uuidがサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequestBody = {
        referenceNumber: '*************',
        uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
        userType: '01'
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockRejectedValueOnce(new Error('my Error'));

      await identityVerificationController.requestEkycUrl(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });

    it('異常系_eKYCのurlがサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequestBody = {
        referenceNumber: '*************',
        uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
        userType: '01'
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      const mockRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;

      const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockRejectedValueOnce(new Error('my Error'));

      await identityVerificationController.requestEkycUrl(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
  describe('getUuid', () => {
    it('正常系_お手続き番号が有効かどうかを確認し、バックエンドからUUIDを取得する', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************2'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      await identityVerificationController.getUuid(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestGetSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/token',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockUuidData);
    });

    it('異常系_お手続き番号が有効かどうかを確認し、存在しない場合エラーを返却する', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************2'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockUuidData = {};

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      await identityVerificationController.getUuid(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestGetSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/open-bank-account/applications/*************/token',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(
        new NotFoundError(
          errorCodes.NOT_FOUND_UUID,
          'UUIDが見つかりませんでした。'
        )
      );
    });

    it('異常系_uuidがサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        query: {
          referenceNumber: '*************'
        },
        header: jest.fn()
      } as unknown as Request;

      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockRejectedValueOnce(new Error('my Error'));

      await identityVerificationController.getUuid(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('checkClosingStatusockageStatus', () => {
    it('正常系_法人Webの開局状況を確認する', () => {
      const mockRequest = {} as unknown as Request;

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockCheckCorporateWebStatus = { isOpened: true };

      (
        checkClosingStatus as jest.MockedFunction<typeof checkClosingStatus>
      ).mockReturnValue(true);

      identityVerificationController.checkCorporateWebStatus(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        mockCheckCorporateWebStatus
      );
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
