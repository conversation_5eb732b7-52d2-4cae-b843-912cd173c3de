import { Request, Response, NextFunction } from 'express';
import accountDisplayController from '../../../src/controllers/accountDisplayController';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import backendRequest from '../../../src/common/api/backendRequest';

describe('accountDisplayController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAccountsDisplay', () => {
    it('正常系_バックエンドから口座表示設定を取得する', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        userTyp: '01',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        loginType: '214',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'freee',
            displayName: '振り込み用口座2'
          }
        ]
      };

      const expectAccountSettingsData = {
        accounts: [
          {
            accountId: '**************',
            isHidden: false,
            accountApiType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountApiType: 'freee',
            displayName: '振り込み用口座2'
          }
        ]
      };

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockAccountSettingsData });

      await accountDisplayController.getAccountsDisplay(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/common/account-settings',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectAccountSettingsData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_口座表示設定がサーバーエラーで取得できなかった場合エラーを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        userTyp: '01',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        loginType: '214',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await accountDisplayController.getAccountsDisplay(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  it('正常系_バックエンドへ口座表示設定情報を更新する', async () => {
    const requestBodyForBff = {
      accounts: [
        {
          accountId: '*************',
          isHidden: false,
          displayName: 'test 1',
          accountApiType: 'web21'
        },
        {
          accountId: '*************',
          isHidden: false,
          displayName: '',
          accountApiType: 'web21'
        }
      ]
    } as unknown as Request;

    const requestBodyForBackEnd = {
      accounts: [
        {
          accountId: '*************',
          isHidden: false,
          displayName: 'test 1',
          accountType: 'web21'
        },
        {
          accountId: '*************',
          isHidden: false,
          displayName: '',
          accountType: 'web21'
        }
      ]
    } as unknown as Request;
    // モックデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      userTyp: '01',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '***********',
      loginType: '214',
      kigyoCd: '**************',
      userAuths: {
        web21: null,
        gets: null
      }
    };

    const mockSession: Session = {
      sessionId: 'mockSessionId',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      userInfo: mockUserInfo,
      state: null,
      freeeOauthState: null,
      expirationTime: '**********',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    const mockRequest = {
      body: requestBodyForBff,
      headers: {
        requestId: 'mockRequestId'
      },
      session: mockSession,
      header: jest.fn()
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    const mockedIp = 'mockIp';
    (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

    // backendRequestをモック
    const backendRequestSpy = jest.spyOn(backendRequest, 'put');
    backendRequestSpy.mockResolvedValue({});

    await accountDisplayController.updateAccountsDisplay(
      mockRequest,
      mockResponse,
      mockNext
    );

    expect(backendRequestSpy).toHaveBeenCalledWith(
      'http://localhost:9999/api/common/account-settings',
      requestBodyForBackEnd,
      {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: 'mockSessionId',
          'user-uid': 'id12345',
          'x-valuedoor-id': 'ABCDE12345',
          ip: 'mockIp'
        },
        params: {},
        responseType: 'json'
      }
    );
    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith({
      message: '口座表示設定を更新しました。'
    });
    expect(mockNext).toHaveBeenCalled();
  });

  it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
    const requestBodyForBff = {
      accounts: [
        {
          accountId: '*************',
          isHidden: false,
          displayName: 'test 1',
          accountApiType: 'web21'
        },
        {
          accountId: '*************',
          isHidden: false,
          displayName: '',
          accountApiType: 'web21'
        }
      ]
    } as unknown as Request;

    // モックデータ
    const mockUserInfo: UserInfo = {
      ninsyoKbn: '01',
      compId: '12345678901234567890',
      compName: '取引先名',
      userSeiMei: '利用者名',
      userTyp: '01',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      telNo: '***********',
      loginType: '214',
      kigyoCd: '**************',
      userAuths: {
        web21: null,
        gets: null
      }
    };

    const mockSession: Session = {
      sessionId: 'mockSessionId',
      vdId: 'ABCDE12345',
      dtpId: '<EMAIL>',
      userId: 'id12345',
      userInfo: mockUserInfo,
      state: null,
      expirationTime: '**********',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };
    const mockRequest = {
      body: requestBodyForBff,
      headers: {
        requestId: 'mockRequestId'
      },
      session: mockSession,
      header: jest.fn()
    } as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;
    const mockNext = jest.fn() as NextFunction;

    // backendRequestをモック
    const backendRequestSpy = jest.spyOn(backendRequest, 'put');
    backendRequestSpy.mockRejectedValue(new Error('my Error'));

    await accountDisplayController.updateAccountsDisplay(
      mockRequest,
      mockResponse,
      mockNext
    );

    expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
  });
});
