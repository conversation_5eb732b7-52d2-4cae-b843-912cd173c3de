import { Request, Response, NextFunction } from 'express';
import { createCipheriv, createDecipheriv } from 'crypto';
import web21OtpController from '../../../src/controllers/web21OtpController';
import { Session, UserInfo } from '../../../src/models/session';

jest.mock('../../../src/services/sessionService');
jest.mock('crypto', () => ({
  createCipheriv: jest.fn(),
  createDecipheriv: jest.fn()
}));
// cipherのモック化
const mockCipherUpdate = jest.fn().mockReturnValue(Buffer.from('123'));
const mockCipherFinal = jest.fn().mockReturnValue(Buffer.from('456'));
const mockCipher = {
  update: mockCipherUpdate,
  final: mockCipherFinal
};
(createCipheriv as jest.Mock).mockReturnValue(mockCipher);
(createDecipheriv as jest.Mock).mockReturnValue(mockCipher);

beforeEach(() => {
  jest.clearAllMocks();
});

describe('decryptWeb21Otp', () => {
  const mockEncryptedOtp = 'XXXX';
  const mockExpectedResponse = { otp: '123456' };
  const requestBody = {
    encryptedOtp: mockEncryptedOtp
  } as unknown as Request;
  const mockRequest = { body: requestBody } as unknown as Request;
  // エラーを返却用モック設定
  const requestErrorBody = {
    encryptedOtp: ''
  } as unknown as Request;
  const mockErrorRequest = { body: requestErrorBody } as unknown as Request;
  const mockResponse = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn()
  } as unknown as Response;
  const mockNext = jest.fn() as NextFunction;

  it('正常系_値が正常に返却されることを確認', () => {
    web21OtpController.decryptWeb21Otp(mockRequest, mockResponse, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(mockExpectedResponse);
    expect(mockNext).toHaveBeenCalled();
  });
  it('異常系_リクエストエラー', () => {
    web21OtpController.decryptWeb21Otp(
      mockErrorRequest,
      mockResponse,
      mockNext
    );

    expect(mockNext).toHaveBeenCalledWith();
  });
  it('異常系_復号化失敗時に1分前のtimestamp(分まで)の復号化キーで再度復号化が実施されること', () => {
    // cipherのモック化（復号エラーとなるよう設定）
    (createDecipheriv as jest.Mock).mockReturnValue(Error);

    web21OtpController.decryptWeb21Otp(mockRequest, mockResponse, mockNext);

    // createDecipherivが2回呼ばれたことを確認
    expect(createDecipheriv).toHaveBeenCalledTimes(2);
  });
});

describe('checkEncryptedVdid', () => {
  const mockUserInfo: UserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    kigyoCd: '12345678912345',
    userAuths: {
      web21: null,
      gets: null
    }
  };
  const mockSession: Session = {
    sessionId: 'sessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: '1699507328',
    createdAt: '2023-10-01 10:00:00 GMT+0',
    updatedAt: '2023-10-01 10:00:00 GMT+0'
  };
  const mockResponse = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn()
  } as unknown as Response;
  const mockNext = jest.fn() as NextFunction;

  it('正常系_値が正常に返却されることを確認', () => {
    const mockEncryptedVdid = 'ABCDE12345';
    const requestBody = {
      encryptedVdid: mockEncryptedVdid
    } as unknown as Request;
    const mockRequest = {
      session: mockSession,
      body: requestBody
    } as unknown as Request;

    web21OtpController.checkEncryptedVdid(mockRequest, mockResponse, mockNext);
  });

  it('異常系_VDIDの不一致', () => {
    const mockEncryptedVdid = 'NOTCORRECT';
    const requestBody = {
      encryptedVdid: mockEncryptedVdid
    } as unknown as Request;
    const mockRequest = {
      session: mockSession,
      body: requestBody
    } as unknown as Request;

    web21OtpController.checkEncryptedVdid(mockRequest, mockResponse, mockNext);

    expect(mockNext).toHaveBeenCalled();
  });

  it('異常系_リクエストエラー', () => {
    const mockRequest = { query: { error: 'error' } } as unknown as Request;

    web21OtpController.decryptWeb21Otp(mockRequest, mockResponse, mockNext);

    expect(mockNext).toHaveBeenCalled();
  });
});

describe('getEncryptedVdid', () => {
  it('正常系_暗号化されたVDIDが返却されることを確認', () => {
    const mockSessionId = 'mockSessionId';
    const mockRequest = {
      session: {
        sessionId: mockSessionId,
        vdId: '1234567890'
      } as Session,
      header: jest.fn()
    } as unknown as Request;

    const expectedEncryptedVdId = '123456';
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    const mockNext = jest.fn() as NextFunction;

    // 対象関数の実行
    web21OtpController.getEncryptedVdid(mockRequest, mockResponse, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith({
      encryptedVdId: expectedEncryptedVdId
    });
    expect(mockNext).toHaveBeenCalled();
  });

  it('異常系_エラー発生時はnextが呼ばれることを確認', () => {
    const mockRequest = {} as unknown as Request;
    const mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    } as unknown as Response;

    const mockNext = jest.fn() as NextFunction;

    web21OtpController.getEncryptedVdid(mockRequest, mockResponse, mockNext);

    expect(mockNext).toHaveBeenCalled();
  });
});
