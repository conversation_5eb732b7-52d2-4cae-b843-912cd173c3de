/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Request, Response, NextFunction, response } from 'express';
import { v4 as mockUuidv4 } from 'uuid';
import {
  getSession,
  saveSession,
  updateState
} from '../../../src/services/sessionService';
import backendRequest from '../../../src/common/api/backendRequest';
import {
  jstTime,
  expirationTime,
  yyyymmddhhmmss
} from '../../../src/utils/dateFormat';
import authController from '../../../src/controllers/authController';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import {
  AuthorizationError,
  InternalServerError
} from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';
import { getIdaasEncryptedCookieValue } from '../../../src/utils/getIdaasEncryptedCookieValue';
import { judgeRisk } from '../../../src/services/judgeRiskService';
import { AttributeValue } from '@aws-sdk/client-dynamodb';

jest.mock('uuid');
jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/services/judgeRiskService');
jest.mock('../../../src/utils/dateFormat');
jest.mock('../../../src/utils/getIdaasEncryptedCookieValue');

describe('authController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('verifySamlResponse', () => {
    it('正常系_バックエンドへSAMLレスポンスをリクエストする、怪しさ判定でエラーの時、セッションID・暗号化クッキーを返却する', async () => {
      const requestBody = {
        samlResponse: 'samlResponse',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockRequest = {
        body: requestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*************',
        clientId: '**********',
        compName: '東京SAML企業',
        userSeiMei: '東京さむる太郎',
        compAtrbt: '01',
        userKn: 'トウキョウ サムルタロウ',
        userTyp: '01',
        Email: '<EMAIL>',
        keiyakuType: '1',
        telNo: '***********',
        kigyoCd: '0000**********',
        userAuths: {
          web21: null,
          gets: null
        },
        loginType: '214',
        otpId: 0,
        otpSerialNo: '**********1',
        otpAplKbn: true,
        otpKind: '01',
        otpDeviceStatus: '01',
        otpReissueKbn: true,
        otpSerialNoReissue: '************',
        otpKindReissue: '01',
        otpDeviceStsReissue: '1',
        otpReissueAction: '1',
        otpNonOtpExpiredDate: '********',
        otpServiceCode: '0'
      };

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        expirationTime as jest.MockedFunction<typeof expirationTime>
      ).mockReturnValue('**********');

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: '**********',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockResponseData = {
        sessionId: 'mockSessionId',
        encryptedCookie: 'mockEncryptedCookie'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          ids: {
            dtpId: '<EMAIL>',
            userUid: '**********',
            valueDoorId: 'ABCDE12345'
          },
          issueInstant: '2023-11-02T06:24:49.727Z',
          valueDoorUser: {
            id: '**********',
            authenticationMethod: '01',
            representativeAccount: '*************',
            clientId: '**********',
            companyName: '東京SAML企業',
            userName: '東京さむる太郎',
            userType: '01',
            accessLevel: '01',
            usernameKana: 'トウキョウ サムルタロウ',
            email: '<EMAIL>',
            telNo: '***********',
            companyCode: '0000**********',
            loginType: '214',
            contractType: '1',
            authorizations: {
              web21: null,
              gets: null
            },
            otpId: 0,
            otpSerialNo: '**********1',
            usesOtp: true,
            otpKind: '01',
            otpDeviceStatus: '01',
            isOtpReissuing: true,
            otpSerialNoReissue: '************',
            otpKindReissue: '01',
            otpDeviceStatusReissue: '1',
            otpReissueAction: '1',
            otpNonOtpExpiredDate: '********',
            otpServiceCode: '0'
          }
        }
      });
      backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValueOnce(
        'mockEncryptedCookie'
      );

      await authController.verifySamlResponse(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/vdid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        samlPayload: mockRequest.body.samlResponse
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          'Content-Type': 'application/json'
        },
        params: {},
        responseType: 'json'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: true
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '99c0bcd940fc4bed6c1bf07ed074118f2919855036b88c4080f742a58574046f'
        },
        params: {},
        responseType: 'json'
      });
      expect(saveSession).toHaveBeenCalledWith(mockSession);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResponseData);
    });
    it('正常系_バックエンドへSAMLレスポンスをリクエストする、怪しさ判定がLの時、セッションID・暗号化クッキーを返却する', async () => {
      const requestBody = {
        samlResponse: 'samlResponse',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockRequest = {
        body: requestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '*************',
        clientId: '**********',
        compName: '東京SAML企業',
        userSeiMei: '東京さむる太郎',
        compAtrbt: '01',
        userKn: 'トウキョウ サムルタロウ',
        userTyp: '01',
        Email: '<EMAIL>',
        keiyakuType: '1',
        telNo: '***********',
        kigyoCd: '0000**********',
        userAuths: {
          web21: null,
          gets: null
        },
        loginType: '214',
        otpId: 0,
        otpSerialNo: '**********1',
        otpAplKbn: true,
        otpKind: '01',
        otpDeviceStatus: '01',
        otpReissueKbn: true,
        otpSerialNoReissue: '************',
        otpKindReissue: '01',
        otpDeviceStsReissue: '1',
        otpReissueAction: '1',
        otpNonOtpExpiredDate: '********',
        otpServiceCode: '0'
      };

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        expirationTime as jest.MockedFunction<typeof expirationTime>
      ).mockReturnValue('**********');

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: '**********',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockResponseData = {
        sessionId: 'mockSessionId',
        encryptedCookie: 'mockEncryptedCookie'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          ids: {
            dtpId: '<EMAIL>',
            userUid: '**********',
            valueDoorId: 'ABCDE12345'
          },
          issueInstant: '2023-11-02T06:24:49.727Z',
          valueDoorUser: {
            id: '**********',
            authenticationMethod: '01',
            representativeAccount: '*************',
            clientId: '**********',
            companyName: '東京SAML企業',
            userName: '東京さむる太郎',
            userType: '01',
            accessLevel: '01',
            usernameKana: 'トウキョウ サムルタロウ',
            email: '<EMAIL>',
            telNo: '***********',
            companyCode: '0000**********',
            loginType: '214',
            contractType: '1',
            authorizations: {
              web21: null,
              gets: null
            },
            otpId: 0,
            otpSerialNo: '**********1',
            usesOtp: true,
            otpKind: '01',
            otpDeviceStatus: '01',
            isOtpReissuing: true,
            otpSerialNoReissue: '************',
            otpKindReissue: '01',
            otpDeviceStatusReissue: '1',
            otpReissueAction: '1',
            otpNonOtpExpiredDate: '********',
            otpServiceCode: '0'
          }
        }
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValueOnce(
        'mockEncryptedCookie'
      );

      await authController.verifySamlResponse(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/vdid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        samlPayload: mockRequest.body.samlResponse
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          'Content-Type': 'application/json'
        },
        params: {},
        responseType: 'json'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: true
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '99c0bcd940fc4bed6c1bf07ed074118f2919855036b88c4080f742a58574046f'
        },
        params: {},
        responseType: 'json'
      });
      expect(saveSession).toHaveBeenCalledWith(mockSession);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResponseData);
    });

    it('正常系_バックエンドへSAMLレスポンスをリクエストする、怪しさ判定がMの時、高リスクユーザーID・暗号化クッキーを返却する', async () => {
      const requestBody = {
        samlResponse: 'samlResponse',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockRequest = {
        body: requestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        expirationTime as jest.MockedFunction<typeof expirationTime>
      ).mockReturnValue('**********');

      const mockResponseData = {
        highRiskUserId: 'highRiskUserId',
        encryptedCookie: 'mockEncryptedCookie'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          ids: {
            dtpId: '<EMAIL>',
            userUid: '**********',
            valueDoorId: 'ABCDE12345'
          },
          issueInstant: '2023-11-02T06:24:49.727Z',
          valueDoorUser: {
            id: '**********',
            authenticationMethod: '01',
            representativeAccount: '*************',
            clientId: '**********',
            companyName: '東京SAML企業',
            userName: '東京さむる太郎',
            userType: '01',
            accessLevel: '01',
            usernameKana: 'トウキョウ サムルタロウ',
            email: '<EMAIL>',
            telNo: '***********',
            companyCode: '0000**********',
            loginType: '214',
            contractType: '1',
            authorizations: {
              web21: null,
              gets: null
            }
          }
        }
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'M'
        }
      });

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValueOnce(
        'mockEncryptedCookie'
      );

      // 危険度判定メソッドをモック
      const mockResult = { highRiskUserId: 'highRiskUserId' };
      (judgeRisk as jest.Mock).mockReturnValue(mockResult);

      await authController.verifySamlResponse(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/vdid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        samlPayload: mockRequest.body.samlResponse
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          'Content-Type': 'application/json'
        },
        params: {},
        responseType: 'json'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: true
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '99c0bcd940fc4bed6c1bf07ed074118f2919855036b88c4080f742a58574046f'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(mockResponseData);
    });

    it('異常系_バックエンドへSAMLレスポンスをリクエストする、怪しさ判定がHの時、エラーを返却する', async () => {
      const requestBody = {
        samlResponse: 'samlResponse',
        caulisSessionId: '550e8400-e29b-41d4-a716-************'
      } as unknown as Request;
      const mockRequest = {
        body: requestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        expirationTime as jest.MockedFunction<typeof expirationTime>
      ).mockReturnValue('**********');

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          ids: {
            dtpId: '<EMAIL>',
            userUid: '**********',
            valueDoorId: 'ABCDE12345'
          },
          issueInstant: '2023-11-02T06:24:49.727Z',
          valueDoorUser: {
            id: '**********',
            authenticationMethod: '01',
            representativeAccount: '*************',
            clientId: '**********',
            companyName: '東京SAML企業',
            userName: '東京さむる太郎',
            userType: '01',
            accessLevel: '01',
            usernameKana: 'トウキョウ サムルタロウ',
            email: '<EMAIL>',
            telNo: '***********',
            companyCode: '0000**********',
            loginType: '214',
            contractType: '1',
            authorizations: {
              web21: null,
              gets: null
            }
          }
        }
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          relativeSuspiciousValue: 'H'
        }
      });

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValueOnce(
        'mockEncryptedCookie'
      );

      // 危険度判定メソッドをモック
      const mockResult = new AuthorizationError(
        errorCodes.JUDGE_RISK_RESPONSE_ERROR,
        '怪しさ判定APIによるエラー'
      );
      (judgeRisk as jest.Mock).mockRejectedValue(mockResult);

      await authController.verifySamlResponse(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/user/auth/vdid-login'
      );
      expect(backendRequestSpy.mock.calls[0][1]).toMatchObject({
        samlPayload: mockRequest.body.samlResponse
      });
      expect(backendRequestSpy.mock.calls[0][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          'Content-Type': 'application/json'
        },
        params: {},
        responseType: 'json'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/caulis/login-suspicious-detection'
      );
      expect(backendRequestSpy.mock.calls[1][1]).toMatchObject({
        isLoginSucceed: true
      });
      expect(backendRequestSpy.mock.calls[1][2]).toMatchObject({
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          channel: 'MOBILE',
          requestId: 'mockRequestId',
          sessionId: '',
          'user-uid': '',
          'x-valuedoor-id': '',
          ip: 'mockIp',
          caulisSessionId: '550e8400-e29b-41d4-a716-************',
          userHash:
            '99c0bcd940fc4bed6c1bf07ed074118f2919855036b88c4080f742a58574046f'
        },
        params: {},
        responseType: 'json'
      });
      expect(mockNext).toHaveBeenCalledWith(
        new AuthorizationError(
          errorCodes.JUDGE_RISK_RESPONSE_ERROR,
          '怪しさ判定APIによるエラー'
        )
      );
    });

    it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
      const requestBody = {
        samlResponse: 'samlResponse'
      } as unknown as Request;
      const mockRequest = {
        body: requestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await authController.verifySamlResponse(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/user/auth/vdid-login',
        { samlPayload: mockRequest.body.samlResponse },
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            'x-valuedoor-id': '',
            ip: 'mockIp',
            'Content-Type': 'application/json'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('createAuthScreenInfo', () => {
    it('正常系_fromEbizParamを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '',
        userId: '',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mocKRequestUserInfo = {
        accessLevel: '01',
        authenticationMethod: '01',
        companyCode: '**************',
        companyName: '取引先名',
        email: '<EMAIL>',
        id: 'ABCDE12345',
        issueInstant: '**************',
        representativeAccount: '************34567890',
        telNo: '***********',
        userName: '利用者名',
        usernameKana: 'リヨウシャメイ',
        web21Authorizations: null
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        yyyymmddhhmmss as jest.MockedFunction<typeof yyyymmddhhmmss>
      ).mockReturnValue('**************');
      (getSession as jest.Mock).mockResolvedValueOnce(mockSession);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({
        data: {
          state: 'state',
          fromEbizParam: 'fromEbizParam'
        }
      });

      await authController.createAuthScreenInfo(
        mockRequest,
        mockResponse,
        mockNext
      );

      const mockUpdateSession: Session = {
        sessionId: 'mockSessionId',
        state: 'state',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/auth/generate-anserBizsol-authorization-parameter',
        mocKRequestUserInfo,
        {
          params: {
            from: '02'
          },
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(updateState).toHaveBeenCalledWith(mockUpdateSession);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        fromEbizParam: 'fromEbizParam'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mocKRequestUserInfo = {
        accessLevel: '01',
        authenticationMethod: '01',
        companyCode: '**************',
        companyName: '取引先名',
        email: '<EMAIL>',
        id: 'ABCDE12345',
        issueInstant: '**************',
        representativeAccount: '************34567890',
        telNo: '***********',
        userName: '利用者名',
        usernameKana: 'リヨウシャメイ',
        web21Authorizations: null
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
      (
        yyyymmddhhmmss as jest.MockedFunction<typeof yyyymmddhhmmss>
      ).mockReturnValue('**************');

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await authController.createAuthScreenInfo(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/auth/generate-anserBizsol-authorization-parameter',
        mocKRequestUserInfo,
        {
          params: {
            from: '02'
          },
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('getAccessToken', () => {
    it('正常系_バックエンドからアクセストークンを取得し、DynamoDBに保存後に返却する', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockRequestBody = {
        stateInSession: 'mockStateInSession',
        stateInQueryParam: 'state',
        code: 'code',
        userInfo: mockUserInfo
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        query: {
          code: 'code',
          state: 'state'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({});

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await authController.getAccessToken(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/auth/access-token',
        mockRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'アクセストークンを取得しました。'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequestBody = {
        stateInSession: 'mockStateInSession',
        stateInQueryParam: 'state',
        code: 'code',
        userInfo: mockUserInfo
      };
      const mockRequest = {
        query: {
          code: 'code',
          state: 'state'
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await authController.getAccessToken(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/auth/access-token',
        mockRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('checkAuthorization', () => {
    it('正常系_バックエンドへの認可状態確認でリフレッシュトークンが存在しない場合、認可ステータス「01:要認可」を返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: false
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '01',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドへの認可状態確認でリフレッシュトークンが存在する場合、アクセストークンを再発行し認可ステータス「02:認可済」を返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_口座照会権限がない場合、hasInquiry = faslse を返却', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: false,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: true,
        hasInquiryAuth: false
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_Web21権限がない場合(userAuthsにweb21がない場合)、hasInquiryAuth, hasApprovalAuth, hasTransferAuthをfalseで返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: true,
        hasApprovalAuth: false,
        hasTransferAuth: false,
        hasInquiryAuth: false
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_userAuthsのweb21のスマホ振込がfalseの場合、hasTransferAuth = false を返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: false,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });
      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: false,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_スマホ承認権限を保有していない場合 hasApprovalAuth = falseを返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: false,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });
      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: false,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_userAuthsのweb21の振込振替がfalseの場合、hasTransferAuth = falseを返却', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: false,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: false,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_userAuthsのweb21の承認がfalseの場合、hasApprovalAuth = false を返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: false,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });
      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: false,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_スマホ承認権限を保有していない場合 approvalAuth = falseを返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: false,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });
      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: false,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_全ての権限を保有していない場合 hasNoAuth = trueを返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: false,
            generalTransferAuth: false,
            salaryTransferAuth: false,
            localTaxPaymentAuth: false,
            approvalAuth: false,
            transferAuth: false,
            fileSendingAuth: false,
            taxesOrFeesPaymentAuth: false,
            accountTransferAuth: false,
            transferDestinationCheckAuth: false,
            dataConversionAuth: false,
            accountInquiryOnSmartPhoneAuth: false,
            transferOnSmartPhoneAuth: false,
            approvalOnSmartPhoneAuth: false,
            preSettingAccountInfoOnSmartPhoneAuth: false,
            settingSecurityOnSmartPhoneAuth: false,
            preSettingAccountInfoAuth: false,
            settingSecurityAuth: false
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });
      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: true,
        hasApprovalAuth: false,
        hasTransferAuth: false,
        hasInquiryAuth: false
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_企業コードがない場合、企業コードなしのエラーと権限情報を返却する', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: undefined,
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'Contents is Not Found'
          },
          errorCode: { S: 'W005-00001' }
        },
        {
          errorMessage: {
            S: 'サービス時間外です。'
          },
          errorCode: { S: 'I005-00006' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: false,
        errorCode: errorCodes.ACCOUNT_NOT_KIGYO_CD,
        errorMessage: 'サービス時間外です。',
        hasFaq: false,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_企業コードが14桁でない場合ない場合、企業コードなしのエラーと権限情報を返却する', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '************3',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'サービス時間外です。'
          },
          errorCode: { S: 'I005-00006' },
          hasFaq: { BOOL: true }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          hasToken: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        hasKigyoCd: false,
        errorCode: errorCodes.ACCOUNT_NOT_KIGYO_CD,
        errorMessage: 'サービス時間外です。',
        hasFaq: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasTransferAuth: true,
        hasInquiryAuth: true
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_バックエンドへの認可拒否履歴確認で認可拒否履歴がある場合、エラーを返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        clientId: '**********',
        compId: '************34567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: true
        }
      });

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(1);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(AuthorizationError);
      expect(mockNext.mock.calls[0][0].errorCode).toEqual(
        errorCodes.AUTHORIZATION_DENIAL_HISTORY
      );
    });

    it('異常系_バックエンドへの認可拒否履歴確認で異常が発生した場合、異常発生エラーを返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(1);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(Error);
    });

    it('異常系_バックエンドへの認可状態確認で異常が発生した場合、異常発生エラーを返す', async () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: {
            accountInquiryAuth: true,
            generalTransferAuth: true,
            salaryTransferAuth: true,
            localTaxPaymentAuth: true,
            approvalAuth: true,
            transferAuth: true,
            fileSendingAuth: true,
            taxesOrFeesPaymentAuth: true,
            accountTransferAuth: true,
            transferDestinationCheckAuth: true,
            dataConversionAuth: true,
            accountInquiryOnSmartPhoneAuth: true,
            transferOnSmartPhoneAuth: true,
            approvalOnSmartPhoneAuth: true,
            preSettingAccountInfoOnSmartPhoneAuth: true,
            settingSecurityOnSmartPhoneAuth: true,
            preSettingAccountInfoAuth: true,
            settingSecurityAuth: true
          },
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn();

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      // GET /api/bank/refusal-flag APIのモックレスポンスを設定
      backendRequestSpy.mockResolvedValueOnce({
        data: {
          existsRefusalFlag: false
        }
      });
      // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
      backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

      // テスト実行
      await authController.checkAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      // 検証
      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/bank/refusal-flag'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/bank/auth/refresh-token'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': '',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(Error);
    });
  });

  describe('checkGetsAuthorization', () => {
    it('正常系_被仕向送金の権限がある場合、ステータス「1:権限あり」を返す', () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: {
            inboundTransferAuth: true,
            lcExportAuth: false,
            exportBillHandlingAuth: false,
            outboundTransferAuth: false,
            lcImportAuth: false,
            forexInfoExchangeAuth: false,
            corpoForexDeliveryAuth: false,
            parentChildContractAuth: false,
            selfSecurityAccessAuth: false,
            generalSecurityAccessAuth: false
          }
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // テスト実行
      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        getsAuthStatus: '1'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_仕向送金の権限がある場合、ステータス「1:権限あり」を返す', () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: {
            inboundTransferAuth: false,
            lcExportAuth: false,
            exportBillHandlingAuth: false,
            outboundTransferAuth: true,
            lcImportAuth: false,
            forexInfoExchangeAuth: false,
            corpoForexDeliveryAuth: false,
            parentChildContractAuth: false,
            selfSecurityAccessAuth: false,
            generalSecurityAccessAuth: false
          }
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // テスト実行
      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        getsAuthStatus: '1'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_セキュリティ管理（本人のユーザ権限設定）の権限がある場合、ステータス「1:権限あり」を返す', () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: {
            inboundTransferAuth: false,
            lcExportAuth: false,
            exportBillHandlingAuth: false,
            outboundTransferAuth: false,
            lcImportAuth: false,
            forexInfoExchangeAuth: false,
            corpoForexDeliveryAuth: false,
            parentChildContractAuth: false,
            selfSecurityAccessAuth: true,
            generalSecurityAccessAuth: false
          }
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // テスト実行
      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        getsAuthStatus: '1'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_セキュリティ設定の権限がある場合、ステータス「1:権限あり」を返す', () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: {
            inboundTransferAuth: false,
            lcExportAuth: false,
            exportBillHandlingAuth: false,
            outboundTransferAuth: false,
            lcImportAuth: false,
            forexInfoExchangeAuth: false,
            corpoForexDeliveryAuth: false,
            parentChildContractAuth: false,
            selfSecurityAccessAuth: false,
            generalSecurityAccessAuth: true
          }
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // テスト実行
      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        getsAuthStatus: '1'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_Get権限がない場合、ステータス「0:権限なし」を返す', () => {
      // テストデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: {
            inboundTransferAuth: false,
            lcExportAuth: true,
            exportBillHandlingAuth: true,
            outboundTransferAuth: false,
            lcImportAuth: true,
            forexInfoExchangeAuth: true,
            corpoForexDeliveryAuth: true,
            parentChildContractAuth: true,
            selfSecurityAccessAuth: false,
            generalSecurityAccessAuth: false
          }
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // テスト実行
      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        getsAuthStatus: '0'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_userInfoにuserAuthsがない場合、ステータス「0:権限なし」を返す', () => {
      // テストデータ
      const mockUserInfo = {
        id: 'ABCDE12345',
        issueInstant: '**************',
        ninsyoKbn: '01',
        compId: '************34567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        clientId: '**********'
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: 'mockStateInSession',
        freeeOauthState: undefined,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // テスト実行
      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        getsAuthStatus: '0'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_userInfoでエラーを取得した場合にエラーを返す', () => {
      const mockRequest = {
        session: {
          get userInfo() {
            throw new Error('my Error');
          }
        }
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      authController.checkGetsAuthorization(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('loginSuspiciousDetection', () => {
    const mockRequestBody = {
      caulisSessionId: 'caulisSessionId',
      errorMessageId: 'C0007'
    } as unknown as Request;
    const mockBackendRequestBody = {
      isLoginSucceed: false,
      loginErrorCode: 'C0007',
      loginKind: '2'
    } as unknown as Request;
    const mockRequest = {
      body: mockRequestBody,
      headers: {
        requestId: 'mockRequestId'
      },
      header: jest.fn()
    } as unknown as Request;

    beforeEach(() => {
      jest.resetAllMocks();
    });

    it('正常系：正しいパラメーターで呼び出され、バックエンドに怪しさ判定リクエストを実行する', async () => {
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({
        data: {
          relativeSuspiciousValue: 'L'
        }
      });

      // BFFからフロントへの返却値の期待値
      const expectResponse = {
        message: 'ValueDoorログイン失敗時の怪しさ判定リクエストを実行しました。'
      };

      // テスト対象メソッドを実行
      await authController.loginSuspiciousDetection(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/caulis/login-suspicious-detection',
        mockBackendRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            caulisSessionId: 'caulisSessionId',
            channel: 'MOBILE',
            ip: '',
            requestId: 'mockRequestId',
            sessionId: '',
            'user-uid': '',
            userAgent: undefined,
            userHashOfNewAccountCreation: '',
            userHash:
              '474acfc6089c0c247dc5e693f710894aa6eb8d0f8e3533d27e68964e8ca3df24',
            'x-valuedoor-id': ''
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectResponse);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系：バックエンドから返却されたエラーをフロントに返す', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      // テスト対象メソッドを実行
      await authController.loginSuspiciousDetection(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('getWeb21SsoSaml', () => {
    const mockUserInfoOfWeb21Sso: UserInfo = {
      clientId: '**********',
      ninsyoKbn: '01',
      compId: '************34567890',
      compName: 'Web21SAML社',
      userSeiMei: 'Web21SAMLSSO',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      loginType: '217',
      telNo: '***********',
      kigyoCd: '**************'
    };

    const mockSessionOfWeb21Sso: Session = {
      sessionId: 'mockSessionId',
      vdId: 'ABCDE12345',
      userId: 'mockUserId',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfoOfWeb21Sso,
      state: 'mockStateInSession',
      freeeOauthState: undefined,
      expirationTime: '**********',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    const mockedIp = 'mockIp';

    const mockRequestBody = {
      ninsyoKbn: '01',
      loginType: '217',
      loginMethod: 'S'
    } as unknown as Request;

    const mockRequest = {
      params: {
        service: 'web21-approval-top'
      },
      headers: {
        requestId: 'mockRequestId',
        authorization: 'Bearer mockSessionId'
      },
      session: mockSessionOfWeb21Sso,
      header: jest.fn()
    } as unknown as Request;

    beforeEach(() => {
      jest.resetAllMocks();
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
    });

    it('正常系：正しいパラメーターで呼び出され、期待されたレスポンスが返る', async () => {
      // バックエンドAPI（valueDoorSingleSignOnController）の返却値モックデータ
      const mockResponseData = { data: { toVdParam: 'mockToVdParam' } };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue(mockResponseData);

      // BFFからフロントへの返却値の期待値
      const expectGetWeb21SsoSamlResponse = { toWeb21Param: 'mockToVdParam' };

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // テスト対象メソッドを実行
      await authController.getWeb21SsoSaml(mockRequest, mockResponse, mockNext);

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/common/saml/web21-approval-top',
        mockRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'mockUserId',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectGetWeb21SsoSamlResponse
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系：バックエンドから返却されたエラーをフロントに返す', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      // テスト対象メソッドを実行
      await authController.getWeb21SsoSaml(mockRequest, mockResponse, mockNext);

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('getEncryptedCookieForSso', () => {
    const mockUserInfo: UserInfo = {
      clientId: '**********',
      ninsyoKbn: '01',
      compId: '************34567890',
      compName: 'Web21SAML社',
      userSeiMei: 'Web21SAMLSSO',
      compAtrbt: '01',
      userKn: 'リヨウシャメイ',
      Email: '<EMAIL>',
      loginType: '217',
      telNo: '***********',
      kigyoCd: '**************'
    };

    const mockSession: Session = {
      sessionId: 'mockSessionId',
      vdId: 'ABCDE12345',
      userId: 'mockUserId',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfo,
      state: 'mockStateInSession',
      freeeOauthState: undefined,
      expirationTime: '**********',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };

    const mockDtpIdSession: Session = {
      dtpId: '<EMAIL>',
      sessionId: 'mockSessionId',
      vdId: 'ABCDE12345',
      userId: 'mockUserId',
      issueInstant: '2023-11-02T06:24:49.727Z',
      userInfo: mockUserInfo,
      state: 'mockStateInSession',
      freeeOauthState: undefined,
      expirationTime: '**********',
      createdAt: '2023-10-01 10:00:00 GMT+0',
      updatedAt: '2023-10-01 10:00:00 GMT+0'
    };
    const mockedIp = 'mockIp';

    // dtpIdなしの場合のリクエスト
    const mockRequest = {
      params: {
        service: 'web21-approval-top'
      },
      headers: {
        requestId: 'mockRequestId',
        authorization: 'Bearer mockSessionId'
      },
      session: mockSession,
      header: jest.fn()
    } as unknown as Request;

    const mockDtpIdUserRequest = {
      params: {
        service: 'web21-approval-top'
      },
      headers: {
        requestId: 'mockRequestId',
        authorization: 'Bearer mockSessionId'
      },
      session: mockDtpIdSession,
      header: jest.fn()
    } as unknown as Request;

    it('正常系：DTPID利用なしユーザーの暗号化Cookie生成', async () => {
      jest.resetAllMocks();
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
      // BFFからフロントへの返却値の期待値
      const expectedEncryptedCookie = {
        encryptedCookie: 'mockEncryptedCookie'
      };

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValueOnce(
        'mockEncryptedCookie'
      );

      // テスト対象メソッドを実行
      await authController.getEncryptedCookieForSso(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedEncryptedCookie);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系：DTPIDユーザーの暗号化Cookie生成', async () => {
      jest.resetAllMocks();
      (mockDtpIdUserRequest.header as jest.Mock).mockReturnValue(mockedIp);
      // BFFからフロントへの返却値の期待値
      const expectedEncryptedCookie = {
        encryptedCookie: 'mockEncryptedCookie'
      };

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // 暗号化クッキー生成メソッドをモック
      (getIdaasEncryptedCookieValue as jest.Mock).mockReturnValueOnce(
        'mockEncryptedCookie'
      );

      // テスト対象メソッドを実行
      await authController.getEncryptedCookieForSso(
        mockDtpIdUserRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectedEncryptedCookie);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系：暗号化Cookie生成時にエラー発生', async () => {
      jest.resetAllMocks();
      (mockDtpIdUserRequest.header as jest.Mock).mockReturnValue(mockedIp);

      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // 暗号化クッキー生成にてエラーを返すようにモック設定
      const error = new InternalServerError(
        'E005-00009',
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
      );
      (getIdaasEncryptedCookieValue as jest.Mock).mockImplementation(() => {
        throw error;
      });

      // テスト対象メソッドを実行
      await authController.getEncryptedCookieForSso(
        mockDtpIdUserRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(error);
    });
  });

  describe('registerRefusalFlag', () => {
    it('正常系_messageを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '',
        userId: '',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        yyyymmddhhmmss as jest.MockedFunction<typeof yyyymmddhhmmss>
      ).mockReturnValue('**************');
      (getSession as jest.Mock).mockResolvedValueOnce(mockSession);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({
        message: 'OK'
      });

      await authController.registerRefusalFlag(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/refusal-flag',
        {},
        {
          params: {},
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: '認可拒否履歴を登録しました。'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
      (
        yyyymmddhhmmss as jest.MockedFunction<typeof yyyymmddhhmmss>
      ).mockReturnValue('**************');

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await authController.registerRefusalFlag(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/refusal-flag',
        {},
        {
          params: {},
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('deleteRefusalFlag', () => {
    it('正常系_messageを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '',
        userId: '',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );
      (
        yyyymmddhhmmss as jest.MockedFunction<typeof yyyymmddhhmmss>
      ).mockReturnValue('**************');
      (getSession as jest.Mock).mockResolvedValueOnce(mockSession);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'delete');
      backendRequestSpy.mockResolvedValue({
        message: 'OK'
      });

      await authController.deleteRefusalFlag(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/refusal-flag',
        {
          params: {},
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: '認可拒否履歴を削除しました。'
      });
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
      // モックデータ
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '************34567890',
        clientId: '**********',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '**************',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        issueInstant: '2023-11-02T06:24:49.727Z',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
      (
        yyyymmddhhmmss as jest.MockedFunction<typeof yyyymmddhhmmss>
      ).mockReturnValue('**************');

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'delete');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await authController.deleteRefusalFlag(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/bank/refusal-flag',
        {
          params: {},
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': '',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          responseType: 'json'
        }
      );
      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
