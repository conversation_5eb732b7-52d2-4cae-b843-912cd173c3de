import { Request, Response, NextFunction } from 'express';
import freeeLinkController from '../../../src/controllers/freeeLinkController';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import backendRequest from '../../../src/common/api/backendRequest';
import { updateFreeeOauthState } from '../../../src/services/sessionService';
import { jstTime } from '../../../src/utils/dateFormat';
import { ValidationError } from '../../../src/errors/httpErrors';
import { errorCodes } from '../../../src/errors/errorCodes';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('freeeLinkController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  const mockUserInfo: UserInfo = {
    clientId: '012345',
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    kigyoCd: '12345678912345',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: '02itNsEZq0d4jtFAXXdKOtgNMv78toeIiaax71qmaxRDGcKm',
    expirationTime: '1699507328',
    createdAt: '2024-08-09 08:13:00 GMT+0',
    updatedAt: '2024-08-09 08:13:00 GMT+0'
  };

  const mockNotUserInfoSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    userInfo: undefined,
    state: null,
    freeeOauthState: '02itNsEZq0d4jtFAXXdKOtgNMv78toeIiaax71qmaxRDGcKm',
    expirationTime: '1699507328',
    createdAt: '2024-08-09 08:13:00 GMT+0',
    updatedAt: '2024-08-09 08:13:00 GMT+0'
  };

  const mockNotFreeeOauthStateSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: '1699507328',
    createdAt: '2024-08-09 08:13:00 GMT+0',
    updatedAt: '2024-08-09 08:13:00 GMT+0'
  };

  const mockedIp = 'mockIp';

  describe('checkFreeeLinkStatus', () => {
    it('正常系_バックエンドからfreee連携確認情報を取得する', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockFreeeLinkData = {
        isLinked: false
      };

      const expectFreeeLinkData = {
        isLinked: false
      };

      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockFreeeLinkData });

      await freeeLinkController.checkFreeeLinkStatus(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/freee/link',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectFreeeLinkData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_freee連携確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeLinkController.checkFreeeLinkStatus(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('linkToFreee', () => {
    const mockRequestBody = {
      code: 'code',
      stateFromFreee: 'stateFromFreee'
    } as unknown as Request;
    // frontからのAPIリクエストのモック
    const mockRequest = {
      body: mockRequestBody,
      headers: {
        requestId: 'mockRequestId'
      },
      session: mockSession,
      header: jest.fn()
    } as unknown as Request;

    // バックエンド宛APIのRequestbodyの期待値
    const expectRequestBody = {
      clientId: '012345',
      code: 'code',
      stateFromFreee: 'stateFromFreee',
      state: mockSession.freeeOauthState
    };

    it('正常系_freeeの各種トークンを取得しAuroraDBに保存する', async () => {
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({});

      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await freeeLinkController.linkToFreee(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/freee/link',
        expectRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        message: 'freeeアクセストークンを保存しました。'
      });
      expect(mockNext).toHaveBeenCalled();
    });
    it('異常系_clientIdが有効かどうかを確認し、存在しない場合エラーを返却する', async () => {
      const mockRequestBody = {
        code: 'code',
        stateFromFreee: 'stateFromFreee'
      } as unknown as Request;
      // frontからのAPIリクエストのモック
      const mockNotClientIdRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockNotUserInfoSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockNotClientIdRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await freeeLinkController.linkToFreee(
        mockNotClientIdRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(
        new ValidationError(
          errorCodes.INVALID_REQUEST,
          '取引先IDがありませんでした。sessionId=sessionId'
        )
      );
    });
    it('異常系_freeeOauthStateが有効かどうかを確認し、存在しない場合エラーを返却する', async () => {
      const mockRequestBody = {
        code: 'code',
        stateFromFreee: 'stateFromFreee'
      } as unknown as Request;
      // frontからのAPIリクエストのモック
      const mockNotFreeeOauthStateRequest = {
        body: mockRequestBody,
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockNotFreeeOauthStateSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockNotFreeeOauthStateRequest.header as jest.Mock).mockReturnValue(
        mockedIp
      );

      await freeeLinkController.linkToFreee(
        mockNotFreeeOauthStateRequest,
        mockResponse,
        mockNext
      );
      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(
        new ValidationError(
          errorCodes.INVALID_REQUEST,
          'freee権限情報がありませんでした。sessionId=sessionId'
        )
      );
    });

    it('異常系_サーバーエラーでPOSTリクエストできなかった場合にエラーを返す', async () => {
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await freeeLinkController.linkToFreee(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/freee/link',
        expectRequestBody,
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {},
          responseType: 'json'
        }
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });

  describe('getUrlToFreeeSignupPage', () => {
    it('正常系_バックエンドからサインアップ画面へ送るstateを取得しサインアップ画面URLを返却する', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockFreeeLinkPartnerIdData = {
        partnerId: 'partnerId'
      };

      const mockFreeeLinkStateData = {
        state: '02state'
      };

      const mockExistsData = {
        exists: false
      };

      const exppectedData = {
        partnerId: 'partnerId',
        state: '02state',
        exists: false
      };

      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
      (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
        '2023-10-01 10:00:00 GMT+0'
      );

      // backendRequestをモック
      const partnerIdbackendRequestSpy = jest.spyOn(backendRequest, 'post');
      partnerIdbackendRequestSpy.mockResolvedValue({
        data: mockFreeeLinkPartnerIdData
      });
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({ data: mockExistsData });
      backendRequestSpy.mockResolvedValueOnce({ data: mockFreeeLinkStateData });

      await freeeLinkController.getFreeeSsoParams(
        mockRequest,
        mockResponse,
        mockNext
      );

      const mockUpdateSession: Session = {
        sessionId: 'mockSessionId',
        freeeOauthState: '02state',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      expect(backendRequestSpy).toHaveBeenCalledWith(
        'http://localhost:9999/api/freee/link/state',
        {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            channel: 'MOBILE',
            requestId: 'mockRequestId',
            sessionId: 'mockSessionId',
            'user-uid': 'id12345',
            'x-valuedoor-id': 'ABCDE12345',
            ip: 'mockIp'
          },
          params: {
            from: '02'
          },
          responseType: 'json'
        }
      );
      expect(updateFreeeOauthState).toHaveBeenCalledWith(mockUpdateSession);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(exppectedData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_clientIdが有効かどうかを確認し、存在しない場合エラーを返却する', async () => {
      const mockNotClientIdRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockNotUserInfoSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const mockedIp = 'mockIp';
      (mockNotClientIdRequest.header as jest.Mock).mockReturnValue(mockedIp);

      await freeeLinkController.getFreeeSsoParams(
        mockNotClientIdRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(
        new ValidationError(
          errorCodes.INVALID_REQUEST,
          '取引先IDがありませんでした。sessionId=sessionId'
        )
      );
    });

    it('異常系_freee連携確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeLinkController.getFreeeSsoParams(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
  describe('completeFreeeLink', () => {
    it('正常系_completeFreeeLinkが呼ばれた場合、OKを返す', () => {
      const mockRequest = {} as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const expectCompleteFreeeLinkData = {
        message: 'OK'
      };

      freeeLinkController.completeFreeeLink(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectCompleteFreeeLinkData
      );
      expect(mockNext).toHaveBeenCalled();
    });
    describe('getUrlToFreeeReLinkPage', () => {
      it('正常系_バックエンドからサインアップ画面へ送るstateを取得しサインアップ画面URLを返却する', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          json: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        const mockFreeeLinkStateData = {
          state: '02state'
        };

        const expectUrlToFreeeRelinkPageData = {
          redirectUrl: `http://freee.xxxxx.co.jp?provider=smbc&redirect_service_name=accounts&redirect_end_point=public_api%2Fauthorize%3Fclient_id%3Dfreee123456789%26prompt%3Dselect_company%26redirect_uri%3Dhttp%253A%252F%252Ffreee.xxxxx.co.jp%26response_type%3Dcode%26state%3D02state&back_url=http%3A%2F%2Ffreee.xxxxx.co.jp`
        };

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);
        (jstTime as jest.MockedFunction<typeof jstTime>).mockReturnValue(
          '2023-10-01 10:00:00 GMT+0'
        );

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockResolvedValue({ data: mockFreeeLinkStateData });

        await freeeLinkController.getUrlToFreeeReLinkPage(
          mockRequest,
          mockResponse,
          mockNext
        );

        const mockUpdateSession: Session = {
          sessionId: 'mockSessionId',
          freeeOauthState: '02state',
          updatedAt: '2023-10-01 10:00:00 GMT+0'
        };

        expect(backendRequestSpy).toHaveBeenCalledWith(
          'http://localhost:9999/api/freee/link/state',
          {
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              channel: 'MOBILE',
              requestId: 'mockRequestId',
              sessionId: 'mockSessionId',
              'user-uid': 'id12345',
              'x-valuedoor-id': 'ABCDE12345',
              ip: 'mockIp'
            },
            params: {
              from: '02'
            },
            responseType: 'json'
          }
        );
        expect(updateFreeeOauthState).toHaveBeenCalledWith(mockUpdateSession);
        expect(mockResponse.status).toHaveBeenCalledWith(200);
        expect(mockResponse.json).toHaveBeenCalledWith(
          expectUrlToFreeeRelinkPageData
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('異常系_freee連携確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          json: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        await freeeLinkController.getUrlToFreeeReLinkPage(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
      });
    });
    describe('checkFreeeLinksByClientId', () => {
      it('正常系_バックエンドから取引先ID紐付け情報を取得する', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          json: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        const mockFreeeLinksByClientData = {
          exists: true
        };

        const expectFreeeLinksByClientData = {
          exists: true
        };

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockResolvedValue({
          data: mockFreeeLinksByClientData
        });

        await freeeLinkController.checkFreeeLinksByClientId(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(backendRequestSpy).toHaveBeenCalledWith(
          'http://localhost:9999/api/freee/links/012345',
          {
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              channel: 'MOBILE',
              requestId: 'mockRequestId',
              sessionId: 'mockSessionId',
              'user-uid': 'id12345',
              'x-valuedoor-id': 'ABCDE12345',
              ip: 'mockIp'
            },
            params: {},
            responseType: 'json'
          }
        );
        expect(mockResponse.status).toHaveBeenCalledWith(200);
        expect(mockResponse.json).toHaveBeenCalledWith(
          expectFreeeLinksByClientData
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('異常系_clientIdが空だった場合、エラーを返す', async () => {
        const mockEmptyClientId: UserInfo = {
          ninsyoKbn: '01',
          compId: '12345678901234567890',
          compName: '取引先名',
          userSeiMei: '利用者名',
          compAtrbt: '01',
          userKn: 'リヨウシャメイ',
          Email: '<EMAIL>',
          telNo: '09011111111',
          kigyoCd: '12345678912345',
          userAuths: {
            web21: null,
            gets: null
          }
        };

        const mockNoClientIdSession: Session = {
          sessionId: 'mockSessionId',
          vdId: 'ABCDE12345',
          dtpId: '<EMAIL>',
          userId: 'id12345',
          userInfo: mockEmptyClientId,
          state: null,
          freeeOauthState: '02itNsEZq0d4jtFAXXdKOtgNMv78toeIiaax71qmaxRDGcKm',
          expirationTime: '1699507328',
          createdAt: '2024-08-09 08:13:00 GMT+0',
          updatedAt: '2024-08-09 08:13:00 GMT+0'
        };

        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          session: mockNoClientIdSession,
          header: jest.fn()
        } as unknown as Request;
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          json: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        await freeeLinkController.checkFreeeLinksByClientId(
          mockRequest,
          mockResponse,
          mockNext
        );

        const notFoundError = new ValidationError(
          errorCodes.INVALID_REQUEST,
          `取引先IDがありませんでした。sessionId=${mockNoClientIdSession.sessionId}`
        );

        expect(mockNext).toHaveBeenCalledWith(notFoundError);
      });
    });

    describe('getFreeeRedirectUrl', () => {
      const freeeDomain = process.env.FREEE_BASE_DOMAIN!;
      it('正常系_代表者初回ログイン', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          query: {
            ticket: 'ticket',
            loginType: 'firstLogin',
            state: 'state',
            partnerId: 'partnerId'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;

        const mockResponse = {
          cookie: jest.fn(),
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockResponse.cookie).toHaveBeenCalledWith('ticket', 'ticket', {
          httpOnly: true,
          secure: true,
          domain: process.env.IDAAS_COOKIE_DOMAIN!,
          path: '/'
        });
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=calc&redirect_end_point=smbc%2Fterms%3Fstate%3Dstate%26partner_id%3DpartnerId&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error%3Fstate%3Dstate%26partner_id%3DpartnerId`
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('正常系_従業員初回ログイン', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          query: {
            ticket: 'ticket',
            loginType: 'firstEmployeeLogin',
            state: 'state',
            partnerId: 'partnerId'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;

        const mockResponse = {
          cookie: jest.fn(),
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockResponse.cookie).toHaveBeenCalledWith('ticket', 'ticket', {
          httpOnly: true,
          secure: true,
          domain: process.env.IDAAS_COOKIE_DOMAIN!,
          path: '/'
        });
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=calc&redirect_end_point=smbc%2Fterms&redirect_query=state%3Dstate%26partner_id%3DpartnerId&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error%3Fstate%3Dstate%26partner_id%3DpartnerId%26only_login%3D1`
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('正常系_クレジットカード追加(クレジットカード連携期限切れ)', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          query: {
            ticket: 'ticket',
            loginType: 'addCreditCardLogin'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;

        const mockResponse = {
          cookie: jest.fn(),
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockResponse.cookie).toHaveBeenCalledWith('ticket', 'ticket', {
          httpOnly: true,
          secure: true,
          domain: process.env.IDAAS_COOKIE_DOMAIN!,
          path: '/'
        });
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounting&redirect_end_point=orientation%2Fsearch_bank%23credit_card&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('正常系_他行口座追加(他行口座連携期限切れ)', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          query: {
            ticket: 'ticket',
            loginType: 'addBankAccountLogin'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;

        const mockResponse = {
          cookie: jest.fn(),
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockResponse.cookie).toHaveBeenCalledWith('ticket', 'ticket', {
          httpOnly: true,
          secure: true,
          domain: process.env.IDAAS_COOKIE_DOMAIN!,
          path: '/'
        });
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounting&redirect_end_point=orientation%2Fsearch_bank%23bank&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('正常系_メンバー追加', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          query: {
            ticket: 'ticket',
            loginType: 'addMembersLogin'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;

        const mockResponse = {
          cookie: jest.fn(),
          status: jest.fn().mockReturnThis(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockResponse.cookie).toHaveBeenCalledWith('ticket', 'ticket', {
          httpOnly: true,
          secure: true,
          domain: process.env.IDAAS_COOKIE_DOMAIN!,
          path: '/'
        });
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounts&redirect_end_point=products%2Faccounting%2Fmembers&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('正常系_金融機関連携期限切れ', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          query: {
            ticket: 'ticket',
            loginType: 'expirationOfFinanceLinkage'
          },
          session: mockSession,
          header: jest.fn()
        } as unknown as Request;

        const mockResponse = {
          cookie: jest.fn(),
          status: jest.fn().mockReturnThis(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );

        expect(mockResponse.cookie).toHaveBeenCalledWith('ticket', 'ticket', {
          httpOnly: true,
          secure: true,
          domain: process.env.IDAAS_COOKIE_DOMAIN!,
          path: '/'
        });
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounting&redirect_end_point=walletables&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
        );
        expect(mockNext).toHaveBeenCalled();
      });

      it('異常系_異常が発生した場合、エラーページにリダイレクトする', async () => {
        const mockRequest = {
          headers: {
            requestId: 'mockRequestId'
          },
          header: jest.fn()
        } as unknown as Request;
        const mockResponse = {
          status: jest.fn().mockReturnThis(),
          json: jest.fn(),
          redirect: jest.fn()
        } as unknown as Response;
        const mockNext = jest.fn() as NextFunction;

        await freeeLinkController.getFreeeRedirectUrl(
          mockRequest,
          mockResponse,
          mockNext
        );
        expect(mockResponse.redirect).toHaveBeenCalledWith(
          process.env.FREEE_RIDIRECT_ERROR_PAGE!
        );
        expect(mockNext).toHaveBeenCalled();
      });
    });

    it('異常系_freee連携確認情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockRequest = {
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeLinkController.checkFreeeLinksByClientId(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
