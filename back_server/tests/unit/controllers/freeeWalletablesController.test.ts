import { Request, Response, NextFunction } from 'express';
import freeeWalletablesController from '../../../src/controllers/freeeWalletablesController';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import {
  convertToDateTimeSlash,
  serverDateTime
} from '../../../src/utils/dateFormat';
import backendRequest from '../../../src/common/api/backendRequest';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');

describe('freeeWalletablesController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getWalletables', () => {
    it('正常系_バックエンドから口座表示設定を持つfreee口座一覧情報のみを取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        query: {
          type: 'bank_account',
          with_balance: true,
          with_sync_status: true
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // freee口座一覧のモックデータ
      const mockFreeeWalletablesData = {
        walletables: [
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          }
        ]
      };

      // 口座表示設定のモックデータ
      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '1',
            isHidden: false,
            accountType: 'freee'
          },
          {
            accountId: '2',
            isHidden: true,
            accountType: 'freee'
          },
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'web21',
            displayName: '振り込み用口座2'
          },
          {
            accountId: '3',
            isHidden: false,
            accountType: 'freee'
          },
          {
            accountId: '4',
            isHidden: true,
            accountType: 'freee'
          }
        ]
      };

      // 期待値
      const expectFreeeWalletablesData = {
        walletables: [
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 0,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: true,
            displayOrder: 1,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 2,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: true,
            displayOrder: 3,
            accountApiType: 'freee',
            syncStatus: '0'
          }
        ]
      };

      (
        serverDateTime as jest.MockedFunction<typeof serverDateTime>
      ).mockReturnValue('2023/2/1 9:00');
      (
        convertToDateTimeSlash as jest.MockedFunction<
          typeof convertToDateTimeSlash
        >
      ).mockReturnValue('2025/1/1 9:00');

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // freee口座一覧と口座表示設定取得のbackendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      backendRequestSpy.mockResolvedValueOnce({
        data: mockFreeeWalletablesData
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      await freeeWalletablesController.getWalletables(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/freee/walletables'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/common/account-settings'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectFreeeWalletablesData
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドから口座表示設定を持つfreee口座一覧情報と口座表示設定を持たないfreee口座一覧情報を取得する', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        query: {
          type: 'bank_account',
          with_balance: true,
          with_sync_status: true
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // freee口座一覧のモックデータ
      const mockFreeeWalletablesData = {
        walletables: [
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          }
        ]
      };

      // 口座表示設定のモックデータ
      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '1',
            isHidden: false,
            accountType: 'freee'
          },
          {
            accountId: '2',
            isHidden: true,
            accountType: 'freee'
          },
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'web21',
            displayName: '振り込み用口座2'
          }
        ]
      };

      // 期待値
      const expectFreeeWalletablesData = {
        walletables: [
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 0,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: true,
            displayOrder: 1,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 2,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 3,
            accountApiType: 'freee',
            syncStatus: '0'
          }
        ]
      };

      (
        serverDateTime as jest.MockedFunction<typeof serverDateTime>
      ).mockReturnValue('2023/2/1 9:00');

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // freee口座一覧と口座表示設定取得のbackendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      backendRequestSpy.mockResolvedValueOnce({
        data: mockFreeeWalletablesData
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      await freeeWalletablesController.getWalletables(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/freee/walletables'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/common/account-settings'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectFreeeWalletablesData
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_バックエンドから口座表示設定を持たないfreee口座一覧情報のみを取得するx (初回ログイン時)', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        query: {
          type: 'bank_account',
          with_balance: true,
          with_sync_status: true
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // freee口座一覧のモックデータ
      const mockFreeeWalletablesData = {
        walletables: [
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          },
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261,
            lastSyncedAt: '2025-01-01T09:00:00+09:00',
            syncStatus: 'success'
          }
        ]
      };

      // 口座表示設定のモックデータ
      const mockAccountSettingsData = {
        accounts: [
          {
            accountId: '**************',
            isHidden: false,
            accountType: 'web21',
            displayName: '振り込み用口座1'
          },
          {
            accountId: '**************',
            isHidden: true,
            accountType: 'web21',
            displayName: '振り込み用口座2'
          }
        ]
      };

      // 期待値
      const expectFreeeWalletablesData = {
        walletables: [
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 0,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 1,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 2,
            accountApiType: 'freee',
            syncStatus: '0'
          },
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261,
            serverDateTime: '2023/2/1 9:00',
            lastSyncedAt: '2025/1/1 9:00',
            bankName: '',
            isHidden: false,
            displayOrder: 3,
            accountApiType: 'freee',
            syncStatus: '0'
          }
        ]
      };

      (
        serverDateTime as jest.MockedFunction<typeof serverDateTime>
      ).mockReturnValue('2023/2/1 9:00');
      (
        convertToDateTimeSlash as jest.MockedFunction<
          typeof convertToDateTimeSlash
        >
      ).mockReturnValue('2025/1/1 9:00');

      const mockedIp = 'mockIp';
      (mockRequest.header as jest.Mock).mockReturnValue(mockedIp);

      // freee口座一覧と口座表示設定取得のbackendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');

      backendRequestSpy.mockResolvedValueOnce({
        data: mockFreeeWalletablesData
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      await freeeWalletablesController.getWalletables(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(backendRequestSpy.mock.calls[0][0]).toBe(
        'http://localhost:9999/api/freee/walletables'
      );
      expect(backendRequestSpy.mock.calls[0][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });
      expect(backendRequestSpy.mock.calls[1][0]).toBe(
        'http://localhost:9999/api/common/account-settings'
      );
      expect(backendRequestSpy.mock.calls[1][1]!.headers).toEqual({
        'X-Requested-With': 'XMLHttpRequest',
        channel: 'MOBILE',
        requestId: 'mockRequestId',
        sessionId: 'mockSessionId',
        'user-uid': 'id12345',
        'x-valuedoor-id': 'ABCDE12345',
        ip: 'mockIp'
      });

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expectFreeeWalletablesData
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('異常系_freee口座一覧情報がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };
      const mockRequest = {
        query: {
          type: 'balance_account',
          with_balance: true,
          with_sync_status: true
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeWalletablesController.getWalletables(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });

    it('異常系_口座表示設定がサーバーエラーで取得できなかった場合、エラーを返す', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '***********',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '**********',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      // freee口座一覧のモックデータ
      const mockFreeeWalletablesData = {
        walletables: [
          {
            id: 3,
            name: 'freee銀行3',
            bankId: 3,
            type: 'bank_account',
            lastBalance: 1565583,
            walletableBalance: 1340263
          },
          {
            id: 2,
            name: 'freee銀行2',
            bankId: 2,
            type: 'bank_account',
            lastBalance: 1565582,
            walletableBalance: 1340262
          },
          {
            id: 4,
            name: 'freee銀行4',
            bankId: 4,
            type: 'bank_account',
            lastBalance: 1565584,
            walletableBalance: 1340264
          },
          {
            id: 1,
            name: 'freee銀行1',
            bankId: 1,
            type: 'bank_account',
            lastBalance: 1565581,
            walletableBalance: 1340261
          }
        ]
      };

      const mockRequest = {
        query: {
          type: 'balance_account',
          with_balance: true
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({
        data: mockFreeeWalletablesData
      });
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      await freeeWalletablesController.getWalletables(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockNext).toHaveBeenCalledWith(new Error('my Error'));
    });
  });
});
