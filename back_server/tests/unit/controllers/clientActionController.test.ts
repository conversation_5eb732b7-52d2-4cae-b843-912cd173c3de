import { Request, Response, NextFunction } from 'express';
import { Session, UserInfo } from '../../../src/models/session';
import { logger } from '../../../src/utils/logger';
import clientActionController from '../../../src/controllers/clientActionController';

jest.mock('../../../src/services/sessionService');
jest.mock('../../../src/utils/dateFormat');
jest.mock('../../../src/services/clientActionLogService');

describe('clientActionController', () => {
  // テスト実行中のlog出力抑制
  beforeAll(() => {
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('saveClientActionLog', () => {
    it('正常系_saveClientActionLogを呼び出し後"OK"が返却される', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        body: {
          clientFunction: '他行口座出金明細',
          operation: '照会',
          result: '正常',
          errorId: ''
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        // IPアドレスを使用するためモック化
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      const expectClientActionLogData = { message: 'OK' };
      await clientActionController.saveClientActionLog(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectClientActionLogData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_必須項目以外がundefinedの場合、空文字が入り、"OK"が返却される', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        body: {},
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession,
        // IPアドレスを使用するためモック化
        header: jest.fn()
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;
      const expectClientActionLogData = { message: 'OK' };
      await clientActionController.saveClientActionLog(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectClientActionLogData);
      expect(mockNext).toHaveBeenCalled();
    });

    it('正常系_saveClientActionLogを呼び出し後、IPアドレスがないため"NG"が返却される', async () => {
      const mockUserInfo: UserInfo = {
        ninsyoKbn: '01',
        compId: '12345678901234567890',
        compName: '取引先名',
        userSeiMei: '利用者名',
        compAtrbt: '01',
        userKn: 'リヨウシャメイ',
        Email: '<EMAIL>',
        telNo: '09011111111',
        kigyoCd: '12345678912345',
        userAuths: {
          web21: null,
          gets: null
        }
      };

      const mockSession: Session = {
        sessionId: 'mockSessionId',
        vdId: 'ABCDE12345',
        dtpId: '<EMAIL>',
        userId: 'id12345',
        userInfo: mockUserInfo,
        state: null,
        freeeOauthState: null,
        expirationTime: '1699507328',
        createdAt: '2023-10-01 10:00:00 GMT+0',
        updatedAt: '2023-10-01 10:00:00 GMT+0'
      };

      const mockRequest = {
        body: {
          clientFunction: '他行口座出金明細',
          operation: '照会',
          result: '正常',
          errorId: ''
        },
        headers: {
          requestId: 'mockRequestId'
        },
        session: mockSession
      } as unknown as Request;
      const mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn()
      } as unknown as Response;
      const mockNext = jest.fn() as NextFunction;

      const expectClientActionLogData = { message: 'NG' };

      await clientActionController.saveClientActionLog(
        mockRequest,
        mockResponse,
        mockNext
      );

      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expectClientActionLogData);
      expect(mockNext).toHaveBeenCalled();
    });
  });
});
