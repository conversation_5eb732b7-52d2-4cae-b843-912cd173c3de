/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import dynamoDBClient from '../../src/utils/dynamoDB';

describe('ユーザー情報取得', () => {
  const bffPath = '/mobile/api/v1/bank/userInfo';
  const invalidBffPath = '/mobile/api/v1/bank/userInfos';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  // 期待値データ
  const expextUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    Email: '<EMAIL>',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345'
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      res = await request(app)
        .get(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expextUserInfo);
    });
  });

  describe('異常系', () => {
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).get(`${bffPath}`);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    it('DynamoDBにデータがない時、404エラーになること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'Contents is Not Found'
          },
          errorCode: { S: 'W005-00001' }
        },
        {
          errorMessage: {
            S: '認証エラー'
          },
          errorCode: { S: 'I005-00001' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

      res = await request(app)
        .get(`${invalidBffPath}`)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.statusCode).toEqual(404);
      expect(JSON.parse(res.text).errorMessage).toEqual(
        'Contents is Not Found'
      );
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        const mockSend = jest.spyOn(dynamoDBClient, 'send');

        // モックデータを返却
        mockSend.mockReturnValueOnce();

        res = await request(app)
          .get(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
