/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';

jest.mock('../../src/utils/sqs');

describe('顧客操作履歴保存', () => {
  const bffPath = '/mobile/api/v1/clientActionLog';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('履歴の保存が成功し"OK"が返却されること', async () => {
      res = await request(app)
        .post(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId')
        .set('X-Forwarded-For', '127.000.00')
        .send({
          clientFunction: '他行口座出金明細',
          operation: '照会',
          result: '正常',
          errorId: ''
        });
      expect(res.status).toEqual(200);
      expect(res.body.message).toEqual('OK');
    });
    it('履歴の保存が失敗し"NG"が返却されること', async () => {
      res = await request(app)
        .post(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId')
        .send({
          clientFunction: '他行口座出金明細',
          operation: '照会',
          result: '正常',
          errorId: ''
        });
      expect(res.status).toEqual(200);
      expect(res.body.message).toEqual('NG');
    });
  });
});
