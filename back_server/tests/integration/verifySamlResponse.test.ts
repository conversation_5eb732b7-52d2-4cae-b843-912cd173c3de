/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { randomBytes } from 'crypto';
import request from 'supertest';
import { v4 as mockUuidv4 } from 'uuid';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

// 実際にsessionServiceを通り保存されていることを確認すべきだが、AWS KEYを設定するとビルドできなくなるためモック化している
jest.mock('../../src/services/sessionService');
jest.mock('uuid');

const mockHash = {
  update: jest.fn().mockReturnThis(),
  digest: jest.fn(() => 'mockedHashValue')
};

jest.mock('crypto', () => ({
  createHash: jest.fn(() => mockHash),
  createHmac: jest.fn(() => mockHash),
  createCipheriv: jest.fn(() => ({
    update: jest.fn().mockReturnValue(Buffer.from('encryptedDataPart1=')),
    final: jest.fn().mockReturnValue(Buffer.from('encryptedDataPart2'))
  })),
  randomBytes: jest.fn(() => Buffer.from('mockedRandomString', 'hex'))
}));

describe('SAML検証', () => {
  const bffPath = '/mobile/api/v1/saml/verify';

  let res: any;

  // モックデータ
  const mockLoginResponse = {
    ids: {
      dtpId: '<EMAIL>',
      userUid: '*********0',
      valueDoorId: '**********'
    },
    issueInstant: '2023-09-21T05:56:17.369Z',
    valueDoorUser: {
      accessLevel: '01',
      authenticationMethod: '01',
      companyCode: '*********12345',
      companyName: '東京SAML企業',
      email: '<EMAIL>',
      id: 'ABCDE12345',
      issueInstant: '**************',
      representativeAccount: '*********01234',
      telNo: '***********',
      userName: '東京さむる太郎',
      usernameKana: 'トウキョウサムルタロウ',
      userType: '01',
      loginType: '217',
      authorizations: {
        web21: null,
        gets: null
      }
    }
  };

  const mockCaulisResponse = {
    relativeSuspiciousValue: 'L'
  };

  // 期待値データ
  const expectData = {
    sessionId: 'mockSessionId',
    encryptedCookie: {
      ticket: 'ZW5jcnlwdGVkRGF0YVBhcnQxPWVuY3J5cHRlZERhdGFQYXJ0Mg',
      domain: 'dev.biztest.smbc.co.jp',
      path: '/',
      secure: 'true',
      httponly: 'true'
    }
  };

  const expextUserInfo = {
    ninsyoKbn: '01',
    compId: '*********01234',
    compName: '東京SAML企業',
    userSeiMei: '東京さむる太郎',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'トウキョウサムルタロウ',
    Email: '<EMAIL>',
    telNo: '***********',
    kigyoCd: '*********12345',
    loginType: '217',
    userAuths: {
      web21: {
        accountInquiryAuth: false,
        generalTransferAuth: false,
        salaryTransferAuth: false,
        localTaxPaymentAuth: false,
        approvalAuth: false,
        transferAuth: false,
        fileSendingAuth: false,
        taxesOrFeesPaymentAuth: false,
        accountTransferAuth: false,
        transferDestinationCheckAuth: false,
        dataConversionAuth: false,
        accountInquiryOnSmartPhoneAuth: false,
        transferOnSmartPhoneAuth: false,
        approvalOnSmartPhoneAuth: false,
        preSettingAccountInfoOnSmartPhoneAuth: false,
        settingSecurityOnSmartPhoneAuth: false,
        preSettingAccountInfoAuth: false,
        settingSecurityAuth: false
      },
      gets: {
        inboundTransferAuth: false,
        lcExportAuth: false,
        exportBillHandlingAuth: false,
        outboundTransferAuth: false,
        lcImportAuth: false,
        forexInfoExchangeAuth: false,
        corpoForexDeliveryAuth: false,
        parentChildContractAuth: false,
        selfSecurityAccessAuth: false,
        generalSecurityAccessAuth: false
      }
    }
  };

  const expextSession: Session = {
    sessionId: 'mockSessionId',
    vdId: '**********',
    dtpId: '<EMAIL>',
    userId: '*********0',
    issueInstant: '2023-09-21T05:56:17.369Z',
    userInfo: expextUserInfo,
    state: undefined,
    freeeOauthState: undefined,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockTimeStamp = **********; // UNIXタイムスタンプ
  const mockBytes = '*********0'; // randomBytesの返却値

  let originalDateNow: () => number;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
    originalDateNow = Date.now;
    global.Date.now = jest.fn(() => mockTimeStamp * 1000);

    (randomBytes as jest.Mock).mockReturnValue(Buffer.from(mockBytes));
  });

  afterEach(() => {
    global.Date.now = originalDateNow;
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({ data: mockLoginResponse });
      backendRequestSpy.mockResolvedValueOnce({ data: mockCaulisResponse });

      // ランダムに払い出されてしまう部分のみモック
      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      res = await request(app).post(`${bffPath}`).send({
        samlResponse:
          '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('期待通りのレスポンスであること', async () => {
        // backendRequestをモック
        const errorData = {
          data: {
            code: 'I000000001',
            message: 'Bad request',
            details: 'Bad request'
          },
          status: 400
        };
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValueOnce({ response: errorData });

        // ランダムに払い出されてしまう部分のみモック
        const mockedUUID = 'mockSessionId';
        (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
          mockedUUID
        );

        // 期待値
        const expectErrorData = {
          errorCode: 'E005-00009',
          errorMessage:
            '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        };

        res = await request(app).post(`${bffPath}`).send({
          samlResponse:
            '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        });
        expect(res.status).toEqual(400);
        expect(res.body).toEqual(expectErrorData);
      });
    });
    describe('401系エラー', () => {
      it('期待通りのレスポンスであること', async () => {
        // backendRequestをモック
        const errorData = {
          data: {
            code: 'I000000001',
            message: 'Bad Authorization',
            details: 'Bad Authorization'
          },
          status: 401
        };
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValueOnce({ response: errorData });

        // ランダムに払い出されてしまう部分のみモック
        const mockedUUID = 'mockSessionId';
        (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
          mockedUUID
        );

        // 期待値
        const expectErrorData = {
          errorCode: 'E005-00009',
          errorMessage:
            '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        };

        res = await request(app).post(`${bffPath}`).send({
          samlResponse:
            '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        });
        expect(res.status).toEqual(401);
        expect(res.body).toEqual(expectErrorData);
      });
    });

    describe('500系エラー', () => {
      it('期待通りのレスポンスであること', async () => {
        // backendRequestをモック
        const errorData = {
          data: {
            code: 'I000000001',
            message:
              '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。',
            details:
              '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
          },
          status: 500
        };
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValueOnce({ response: errorData });

        // ランダムに払い出されてしまう部分のみモック
        const mockedUUID = 'mockSessionId';
        (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
          mockedUUID
        );

        // 期待値
        const expectErrorData = {
          errorCode: 'E005-00009',
          errorMessage:
            '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        };

        res = await request(app).post(`${bffPath}`).send({
          samlResponse:
            '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
        });
        expect(res.status).toEqual(500);
        expect(res.body).toEqual(expectErrorData);
      });
    });
  });
});
