/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { AxiosResponse } from 'axios';
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('住所検索', () => {
  const bffPath = '/mobile/api/v1/identityVerification/address';

  let res: any;

  // モックデータ
  const mockSearchAddressResponse = [
    {
      entryId: 1,
      postcode: '1000001',
      prefectureKana: 'ﾄｳｷｮｳﾄ',
      cityKana: 'ﾁﾖﾀﾞｸ',
      streetKana: 'ﾁﾖﾀﾞ',
      prefecture: '東京都',
      city: '千代田区',
      street: '千代田'
    }
  ];

  // 期待データ
  const expectSearchAddressResponse = [
    {
      entryId: 1,
      postcode: '1000001',
      prefectureKana: 'ﾄｳｷｮｳﾄ',
      cityKana: 'ﾁﾖﾀﾞｸ',
      streetKana: 'ﾁﾖﾀﾞ',
      prefecture: '東京都',
      city: '千代田区',
      street: '千代田'
    }
  ];

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockSearchAddressResponse });

      res = await request(app).get(bffPath).query({ postcode: '1000001' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectSearchAddressResponse);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエスト不正が発生した場合、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue({ response: errorData });

        res = await request(app).get(bffPath).query({ postcode: '100000' });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('404系エラー', () => {
      it('バックエンドで住所が見つからなかった場合、404エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: '該当する住所が見つかりませんでした。お手数ですが、正しい郵便番号をご確認の上、再度ご入力ください。'
            },
            errorCode: { S: 'I005-00007' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        // backendRequestをモック
        const errorData = {
          code: 'E001-00004',
          message: 'Not Found',
          details: {
            errorDetail: 'there is no documentType: testType'
          }
        };

        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue({
          isAxiosError: true,
          response: {
            data: errorData,
            status: 404
          }
        });

        res = await request(app).get(bffPath).query({ postcode: '1000001' });

        expect(res.statusCode).toEqual(404);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '該当する住所が見つかりませんでした。お手数ですが、正しい郵便番号をご確認の上、再度ご入力ください。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app).get(bffPath).query({ postcode: '1000001' });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
