/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('freeeサインアップ画面URL取得', () => {
  const bffPath = '/mobile/api/v1/freee/relink';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    id: 'ABCDE12345',
    issueInstant: '20231102062449',
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    kigyoCd: '12345678912345',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: '************',
    createdAt: '2023-10-01 10:00:00 GMT+0',
    updatedAt: '2023-10-01 10:00:00 GMT+0'
  };

  // モックデータ
  const mockFreeeLinkStateData = {
    state: '02Uk7u4d8wF7lEB4YSV3ai5uco4f2ok1dS41oRTHPCsOwbFq'
  };

  // 期待値データ
  const expectUrlToFreeeSignupPageData = {
    redirectUrl:
      'http://freee.xxxxx.co.jp?provider=smbc&redirect_service_name=accounts&redirect_end_point=public_api%2Fauthorize%3Fclient_id%3Dfreee123456789%26prompt%3Dselect_company%26redirect_uri%3Dhttp%253A%252F%252Ffreee.xxxxx.co.jp%26response_type%3Dcode%26state%3D02Uk7u4d8wF7lEB4YSV3ai5uco4f2ok1dS41oRTHPCsOwbFq&back_url=http%3A%2F%2Ffreee.xxxxx.co.jp'
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockFreeeLinkStateData });

      res = await request(app)
        .get(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectUrlToFreeeSignupPageData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W001-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W001-00002',
            message: 'Bad Request',
            errorDetail: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue({ response: errorData });
        res = await request(app)
          .get(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).post(`${bffPath}`);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .get(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
