/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import crypto from 'crypto';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import { Session } from '../../src/models/session';
import {
  expirationTime,
  jstTime,
  unixTimestampInMinutes
} from '../../src/utils/dateFormat';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { components } from '../../src/schemas/schema';

describe('web21Otp復号取得', () => {
  const bffPath = '/mobile/api/v1/web21Otp/decrypt';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const authorization = 'Bearer mockSessionId';
  const sharedKey = process.env.SHARED_KEY_FOR_TRANSFER ?? '';

  // 暗号化前のOTPのモックデータ
  const plainOtp = '123456';

  // 復号化の対象となる暗号化OTPを作成する（web21OtpController、encryptDataメソッドの処理と同じ）
  const timestamp = unixTimestampInMinutes();
  const key = sharedKey + timestamp.toString();
  const cipher = crypto.createCipheriv('aes-256-ecb', Buffer.from(key), null);
  let encrypted = cipher.update(plainOtp, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  // 暗号化OTPの作成完了

  // リクエストモックデータ（正当に作成した暗号化OTP）
  const mockDecryptWeb21OtpRequest: components['schemas']['DecryptWeb21Otp'] = {
    encryptedOtp: encrypted
  };

  // 適当なリクエストモックデータ（bae64エンコードして含まれ得ない文字列を含む）
  const encryptedOtp = 'XXXX!';
  const mockErrorDecryptWeb21OtpRequest: components['schemas']['DecryptWeb21Otp'] =
    {
      encryptedOtp
    };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      res = await request(app)
        .post(bffPath)
        .set('authorization', authorization)
        .send(mockDecryptWeb21OtpRequest);

      expect(res.status).toEqual(200);
      expect(res.body).toEqual({ otp: plainOtp });
    });
  });

  describe('400エラー', () => {
    it('リクエストパラメーター不足の際、400エラーになること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
          },
          errorCode: { S: 'W005-00002' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
      res = await request(app)
        .post(bffPath)
        .set('authorization', authorization);
      expect(res.statusCode).toEqual(400);
      expect(JSON.parse(res.text).errorMessage).toEqual(
        'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
      );
    });
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).post(bffPath).send(mockDecryptWeb21OtpRequest);

        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
  });
  describe('500エラー', () => {
    it('内部エラーが発生した場合、500エラーを返すこと', async () => {
      res = await request(app)
        .post(bffPath)
        .set('authorization', authorization)
        .send(mockErrorDecryptWeb21OtpRequest);

      expect(res.status).toEqual(500);
      expect(res.body.errorMessage).toEqual(
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
      );
    });
  });
});
