/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('口座開設行内審査ステータス確認', () => {
  const bffPath =
    '/mobile/api/v1/identityVerification/openAccountApplication/temporary';

  let res: any;

  // モックデータ
  const mocKBackendResponseData = {
    identificationData: [
      {
        role: 'REPRESENTATIVE',
        name: {
          familyName: '若草',
          givenName: '太郎',
          familyNameKana: 'ワカクサ',
          givenNameKana: 'タロウ',
          familyNameAlphabetic: 'WAKAKUSA',
          givenNameAlphabetic: 'TARO'
        },
        position: '部長',
        birthDate: {
          year: 1999,
          month: 1,
          day: 1
        },
        gender: 'MALE',
        address: {
          zipCode: '1350061',
          prefectureCity: '東京都港区六本木',
          blockSection: '6',
          blockNumber: '12',
          houseNumber: '3',
          buildingName: 'SMBCビル12階'
        },
        email: '<EMAIL>'
      },
      {
        role: 'BENEFICIARY',
        name: {
          familyNameKana: 'サトウ',
          givenNameKana: 'ジロウ'
        },
        position: '課長'
      }
    ],
    beneficiaryIdentificationData: {
      2: {
        name: '佐藤 二郎',
        status: 'NECESSARY'
      }
    },
    validationStatus: {
      isPriorConfirmationApplicationDataValid: true,
      isIdentificationApplicationDataValid: false,
      isMainApplicationDataValid: true,
      isIdentificationBeneficiaryApplicationDataValid: true,
      isDocumentSubmissionApplicationDataValid: true
    },
    preConfirmData: {
      applicantRole: 'REPRESENTATIVE'
    }
  };

  // 期待データ
  const expectResponseData = {
    isIdentityVerified: true,
    userType: 'REPRESENTATIVE',
    validationStatus: 'STEP2'
  };

  // 期待データ
  const expectResponseData2 = {
    isIdentityVerified: false,
    userType: 'BENEFICIARY2',
    validationStatus: 'STEP2'
  };

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      res = await request(app)
        .get(bffPath)
        .query({ referenceNumber: '0000005000001' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponseData);
    });

    it('手続き番号が14桁の場合、期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mocKBackendResponseData });

      res = await request(app)
        .get(bffPath)
        .query({ referenceNumber: '00000050000012' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponseData2);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエスト不正が発生した場合、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue({ response: errorData });

        res = await request(app)
          .get(bffPath)
          .query({ referenceNumber: 'あああ' });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .get(bffPath)
          .query({ referenceNumber: '0000005000003' });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
