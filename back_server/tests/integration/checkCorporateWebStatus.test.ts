/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import { checkClosingStatus } from '../../src/utils/checkClosingStatus';

jest.mock('../../src/utils/checkClosingStatus');

describe('法人Web開局状況確認', () => {
  const bffPath = '/mobile/api/v1/identityVerification/corporateWebStatus';

  let res: any;

  // 期待データ
  const expectedData = { isOpened: true };

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      (
        checkClosingStatus as jest.MockedFunction<typeof checkClosingStatus>
      ).mockReturnValue(true);
      res = await request(app).get(`${bffPath}`).send();
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectedData);
    });
  });
});
