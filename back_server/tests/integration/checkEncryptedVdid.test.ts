/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import crypto from 'crypto';
import app from '../../src/server';
import {
  expirationTime,
  jstTime,
  unixTimestampInMinutes
} from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import { components } from '../../src/schemas/schema';

describe('暗号化VDID検証', () => {
  const bffPath = '/mobile/api/v1/encryptedVdid/check';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: '1234567890',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const authorization = 'Bearer mockSessionId';
  const sharedKey = process.env.SHARED_KEY_FOR_TRANSFER ?? '';

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      const expectedMessage = 'VDIDの認証に成功しました。';

      // 復号化の対象となる暗号化VDIDを作成する（web21OtpController、encryptDataメソッドの処理と同じ）
      const timestamp = unixTimestampInMinutes();
      const key = sharedKey + timestamp.toString();
      const cipher = crypto.createCipheriv(
        'aes-256-ecb',
        Buffer.from(key),
        null
      );
      let encrypted = cipher.update(mockSession.vdId!, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      // 暗号化VDIDの作成完了

      // リクエストモックデータ（正当に作成した暗号化VDID）
      const mockCheckEncryptedVdidpRequest: components['schemas']['CheckEncryptedVdid'] =
        {
          encryptedVdid: encrypted
        };

      res = await request(app)
        .post(`${bffPath}`)
        .set('authorization', authorization)
        .send(mockCheckEncryptedVdidpRequest);

      expect(res.status).toEqual(200);
      expect(res.body.message).toEqual(expectedMessage);
    });
  });

  describe('異常系_VDID不一致', () => {
    it('期待通りのレスポンスであること', async () => {
      const expectedMessage =
        'セッションのVDIDとWeb21から受け取った値が不一致です。';
      // エラーマスターテーブルにデータを格納
      response.errorMaster = [
        {
          errorMessage: { S: expectedMessage },
          errorCode: { S: 'E005-00004' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

      // 復号化の対象となる暗号化VDIDを作成する（web21OtpController、encryptDataメソッドの処理と同じ）
      // 「分まで」のタイムスタンプ(UNIXタイム)を取得
      const timestamp = unixTimestampInMinutes();
      // 暗号化キーの生成（固定値+現時点のtimestamp(分まで)）
      const key = sharedKey + timestamp.toString();
      // AES-256、ECB方式で暗号化
      const cipher = crypto.createCipheriv(
        'aes-256-ecb',
        Buffer.from(key),
        null
      );
      // 暗号化してからbase64エンコード、mockSession.vdIdとは異なるVDIDを設定
      let encrypted = cipher.update('2345678901', 'utf8', 'base64');
      encrypted += cipher.final('base64');
      // 暗号化VDIDの作成完了

      // リクエストモックデータ（正当に作成した暗号化VDID、インプットのVDIDがセッションとは異なる）
      const mockErrorCheckEncryptedVdidpRequest: components['schemas']['CheckEncryptedVdid'] =
        {
          encryptedVdid: encrypted
        };

      res = await request(app)
        .post(`${bffPath}`)
        .set('authorization', authorization)
        .send(mockErrorCheckEncryptedVdidpRequest);

      expect(res.status).toEqual(400);
      expect(JSON.parse(res.text).errorMessage).toEqual(expectedMessage);
    });
  });

  describe('異常系', () => {
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).post(`${bffPath}`).send();
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // 適当なリクエストモックデータ（bae64エンコードして含まれ得ない文字列を含む）
        const encryptedVdid = 'ABCDE1234!';
        const mockErrorCheckEncryptedVdidpRequest: components['schemas']['CheckEncryptedVdid'] =
          {
            encryptedVdid
          };

        res = await request(app)
          .post(bffPath)
          .set('authorization', authorization)
          .send(mockErrorCheckEncryptedVdidpRequest);

        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
