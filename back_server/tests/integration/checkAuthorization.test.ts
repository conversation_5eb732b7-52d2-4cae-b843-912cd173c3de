/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';
import { errorCodes } from '../../src/errors/errorCodes';

describe('認可要否確認', () => {
  const bffPath = '/mobile/api/v1/authorization/check';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: {
        accountInquiryAuth: true,
        generalTransferAuth: true,
        salaryTransferAuth: true,
        localTaxPaymentAuth: true,
        approvalAuth: true,
        transferAuth: true,
        fileSendingAuth: true,
        taxesOrFeesPaymentAuth: true,
        accountTransferAuth: true,
        transferDestinationCheckAuth: true,
        dataConversionAuth: true,
        accountInquiryOnSmartPhoneAuth: true,
        transferOnSmartPhoneAuth: true,
        approvalOnSmartPhoneAuth: true,
        preSettingAccountInfoOnSmartPhoneAuth: true,
        settingSecurityOnSmartPhoneAuth: true,
        preSettingAccountInfoAuth: true,
        settingSecurityAuth: true
      },
      gets: null
    }
  };

  const mockUserInfoNotAuth = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockUserInfoNoKigyoCd = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: undefined,
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: {
        accountInquiryAuth: true,
        generalTransferAuth: true,
        salaryTransferAuth: true,
        localTaxPaymentAuth: true,
        approvalAuth: true,
        transferAuth: true,
        fileSendingAuth: true,
        taxesOrFeesPaymentAuth: true,
        accountTransferAuth: true,
        transferDestinationCheckAuth: true,
        dataConversionAuth: true,
        accountInquiryOnSmartPhoneAuth: true,
        transferOnSmartPhoneAuth: true,
        approvalOnSmartPhoneAuth: true,
        preSettingAccountInfoOnSmartPhoneAuth: true,
        settingSecurityOnSmartPhoneAuth: true,
        preSettingAccountInfoAuth: true,
        settingSecurityAuth: true
      },
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockSessionNotAuth: Session = {
    sessionId: 'mockSessionIdNotAuth',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfoNotAuth,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockSessionNoKigyoCd: Session = {
    sessionId: 'mockSessionIdNoKigyoCd',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfoNoKigyoCd,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
    await saveSession(mockSessionNotAuth);
    await saveSession(mockSessionNoKigyoCd);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
    await deleteSession('mockSessionIdNotAuth');
    await deleteSession('mockSessionIdNoKigyoCd');
  });

  describe('正常系', () => {
    it('バックエンドへの認可状態確認でリフレッシュトークンが存在しない場合、認可ステータス「01:要認可」を返すこと', async () => {
      // モックデータ
      const mockRefusalFlagResponse = {
        existsRefusalFlag: false
      };
      const mockRefreshTokenResponse = {
        hasToken: false
      };
      // 期待データ
      const expectAuthorizationStatus = {
        hasKigyoCd: true,
        authorizationStatus: '01',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasInquiryAuth: true,
        hasTransferAuth: true
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefusalFlagResponse
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefreshTokenResponse
      });

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectAuthorizationStatus);
    });
    it('バックエンドへの認可状態確認でリフレッシュトークンが存在する場合、認可ステータス「02:認可済」を返すこと', async () => {
      // モックデータ
      const mockRefusalFlagResponse = {
        existsRefusalFlag: false
      };
      const mockRefreshTokenResponse = {
        hasToken: true
      };

      // 期待データ
      const expectAuthorizationStatus = {
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasInquiryAuth: true,
        hasTransferAuth: true
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefusalFlagResponse
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefreshTokenResponse
      });

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectAuthorizationStatus);
    });
    it('バックエンドへの認可状態確認で権限情報が存在しない場合、権限なしが返却されること', async () => {
      // モックデータ
      const mockRefusalFlagResponse = {
        existsRefusalFlag: false
      };
      const mockRefreshTokenResponse = {
        hasToken: true
      };

      // 期待データ
      const expectAuthorizationStatus = {
        hasKigyoCd: true,
        authorizationStatus: '02',
        hasNoAuth: true,
        hasApprovalAuth: false,
        hasInquiryAuth: false,
        hasTransferAuth: false
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefusalFlagResponse
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefreshTokenResponse
      });

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionIdNotAuth');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectAuthorizationStatus);
    });
    it('バックエンドへの認可状態確認で企業コードが存在しない場合、企業コードなしのエラーと認可権限が返却されること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'サービス時間外です。'
          },
          errorCode: { S: 'I005-00006' },
          hasFaq: { BOOL: true }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
      const errorData = {
        data: {
          code: 'I001-00001',
          message: 'Request error',
          httpStatusCode: 400,
          details: 'Request error'
        },
        status: 400
      };
      // モックデータ
      const mockRefusalFlagResponse = {
        existsRefusalFlag: false
      };
      const mockRefreshTokenResponse = {
        hasToken: true
      };

      // 期待データ
      const expectAuthorizationStatus = {
        hasKigyoCd: false,
        errorCode: errorCodes.ACCOUNT_NOT_KIGYO_CD,
        errorMessage: 'サービス時間外です。',
        hasFaq: true,
        authorizationStatus: '02',
        hasNoAuth: false,
        hasApprovalAuth: true,
        hasInquiryAuth: true,
        hasTransferAuth: true
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefusalFlagResponse
      });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockRefreshTokenResponse
      });

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionIdNoKigyoCd');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectAuthorizationStatus);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('バックエンドへの認可拒否履歴確認で異常が発生した場合、400エラーを返すこと', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'バックエンドエラー'
            },
            errorCode: { S: 'I001-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'I001-00001',
            message: 'Request error',
            httpStatusCode: 400,
            details: 'Request error'
          },
          status: 400
        };

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValueOnce({
          isAxiosError: true,
          response: errorData
        });

        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual('バックエンドエラー');
      });
      it('バックエンドへの認可状態確認で異常が発生した場合、400エラーを返すこと', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
            },
            errorCode: { S: 'I001-00001' }
          },
          {
            errorMessage: {
              S: '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。'
            },
            errorCode: { S: 'I005-00005' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        // GET /api/bank/refusal-flag APIのモックレスポンスを設定
        backendRequestSpy.mockResolvedValueOnce({
          data: {
            existsRefusalFlag: false
          }
        });
        const errorData = {
          data: {
            code: 'I001-00001',
            message: 'Request error',
            httpStatusCode: 400,
            details: 'Request error'
          },
          status: 400
        };
        // GET /api/bank/auth/refresh-token APIのモックレスポンスを設定
        backendRequestSpy.mockRejectedValueOnce({
          isAxiosError: true,
          response: errorData
        });

        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。'
        );
      });
    });
    describe('401系エラー', () => {
      it('バックエンドへの認可拒否履歴確認で認可拒否がある場合、401エラーを返すこと', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '口座情報へアクセスできません。再度認可画面からやり直してください。'
            },
            errorCode: { S: 'E005-00002' }
          },
          {
            errorMessage: {
              S: '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。'
            },
            errorCode: { S: 'I005-00005' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        // モックデータ
        const mockRefusalFlagResponse = {
          existsRefusalFlag: true
        };

        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockResolvedValueOnce({
          data: mockRefusalFlagResponse
        });

        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。'
        );
      });
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).get(bffPath);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('内部エラー発生時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpyError = jest.spyOn(backendRequest, 'get');
        // GET /api/bank/refusal-flag APIのモックレスポンスを設定
        backendRequestSpyError.mockResolvedValueOnce({
          data: {
            existsRefusalFlag: null
          }
        });
        try {
          res = await request(bffPath)
            .get(bffPath)
            .set('authorization', 'Bearer mockSessionId');
        } catch (error) {
          res = {
            status: 500,
            body: { error: 'Internal Server Error' }
          };
        }
        expect(res.status).toEqual(500);
        expect(res.body.error).toBe('Internal Server Error');
      });
    });
  });
});
