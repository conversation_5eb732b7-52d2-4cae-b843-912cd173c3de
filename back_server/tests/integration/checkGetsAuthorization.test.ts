/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import dynamoDBClient from '../../src/utils/dynamoDB';

describe('Gets権限確認', () => {
  const bffPath = '/mobile/api/v1/getsAuthorization/check';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: {
        inboundTransferAuth: true,
        lcExportAuth: false,
        exportBillHandlingAuth: false,
        outboundTransferAuth: false,
        lcImportAuth: false,
        forexInfoExchangeAuth: false,
        corpoForexDeliveryAuth: false,
        parentChildContractAuth: false,
        selfSecurityAccessAuth: false,
        generalSecurityAccessAuth: false
      }
    }
  };

  const mockUserInfoNotAuth = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockSessionNotAuth: Session = {
    sessionId: 'mockSessionIdNotAuth',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfoNotAuth,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
    await saveSession(mockSessionNotAuth);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
    await deleteSession('mockSessionIdNotAuth');
  });

  describe('正常系', () => {
    it('Gets権限確認でステータス「1:権限あり」を返すこと', async () => {
      // 期待データ
      const expectAuthorizationStatus = {
        getsAuthStatus: '1'
      };

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectAuthorizationStatus);
    });
    it('Gets権限確認でステータス「0:権限なし」を返すこと', async () => {
      // 期待データ
      const expectAuthorizationStatus = {
        getsAuthStatus: '0'
      };

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionIdNotAuth');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectAuthorizationStatus);
    });
  });

  describe('異常系', () => {
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーを返すこと', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        res = await request(app).get(bffPath);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        const mockSend = jest.spyOn(dynamoDBClient, 'send');

        // モックデータを返却
        mockSend.mockReturnValueOnce();

        res = await request(app)
          .get(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
