/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime, unixTime } from '../../src/utils/dateFormat';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import { HighRiskUser } from '../../src/models/highRiskUser';
import { EncryptedCookie } from '../../src/models/encryptedCookie';
import { saveHighRiskUser } from '../../src/services/highRiskUserService';
import { saveSession } from '../../src/services/sessionService';
import { createCipheriv, randomBytes } from 'crypto';

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mockSessionId')
}));
jest.mock('../../src/services/sessionService');

describe('セッションID取得', () => {
  const bffPath = '/mobile/api/v1/sessionId';

  // テスト用の暗号化Cookie生成関数
  // UNIXタイムスタンプ生成のためのdayを引数で渡す
  const generateEncryptedCookie = (day: number): EncryptedCookie => {
    // 復号化の対象となる暗号化ticketを作成する（getIdaasEncryptedCookieValueメソッドの処理と同じ）
    const userUid = 'Id12345';
    const emailAddress = '<EMAIL>';
    // Base64URLエンコード関数
    const base64UrlEncode = (buffer: Buffer): string =>
      buffer
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/g, '');
    // 乱数生成関数
    const generateNonce = (length: number) => {
      const characters = '0123456789!@#$%^&*()_+-=[]{};:\'",.<>?';
      return Array.from(
        randomBytes(length),
        (byte) => characters[byte % characters.length]
      ).join('');
    };
    const KEY = Buffer.from(
      process.env.IDAAS_COOKIE_ENCRYPTION_KEY ?? '',
      'hex'
    );
    // 共通化鍵をインプットにSHA-256(ECB)暗号化インスタンス生成
    const cipher = createCipheriv('aes-256-ecb', KEY, null);
    // UNIXタイムスタンプ生成
    const timeStamp = Math.round(day / 1000);

    // 半角数字記号10文字の乱数を生成
    const nonce = generateNonce(10);
    // 平文ticket生成
    const data = `${nonce}|${userUid}|${emailAddress}|${timeStamp}`;
    // 暗号化ticket生成
    const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
    // Base64URLエンコードした暗号化ticket（文字列）を格納
    const ticket = base64UrlEncode(encryptedData);
    // 暗号化ticketを作成完了

    const domain = process.env.IDAAS_COOKIE_DOMAIN ?? '';
    const path = '/';
    const secure = 'true';
    const httponly = 'true';

    const encryptedCookie: EncryptedCookie = {
      ticket: ticket,
      domain: domain,
      path: path,
      secure: secure,
      httponly: httponly
    };

    return encryptedCookie;
  };

  const today = Date.now();
  const yesterday = Date.now() - 24 * 60 * 60 * 1000;

  const mockTicket = generateEncryptedCookie(yesterday).ticket;
  const mockIdaasTicket = generateEncryptedCookie(today).ticket;

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockHighRiskUser: HighRiskUser = {
    highRiskUserId: 'mockHighRiskUserId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockHighRiskUserOverUnixTime: HighRiskUser = {
    highRiskUserId: 'mockHighRiskUserOverUnixTime',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: (unixTime() - 100000).toString(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  // 期待データ
  const expectGetSessionId = {
    sessionId: 'mockSessionId'
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveHighRiskUser(mockHighRiskUser);
    await saveHighRiskUser(mockHighRiskUserOverUnixTime);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockHighRiskUserId')
        .query({
          ticket: mockTicket,
          idaasTicket: mockIdaasTicket
        });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectGetSessionId);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'お客さまのアカウントは現在ログインできません。'
            },
            errorCode: { S: 'I005-00009' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).get(bffPath).query({
          ticket: mockTicket,
          idaasTicket: mockIdaasTicket
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'お客さまのアカウントは現在ログインできません。'
        );
      });

      it('有効期限切れの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'お客さまのアカウントは現在ログインできません。'
            },
            errorCode: { S: 'I005-00011' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockHighRiskUserOverUnixTime')
          .query({
            ticket: mockTicket,
            idaasTicket: mockIdaasTicket
          });
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'お客さまのアカウントは現在ログインできません。'
        );
      });
      it('OTP認証結果検証が失敗の時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
            },
            errorCode: { S: 'I005-00012' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockHighRiskUserOverUnixTime')
          .query({
            ticket: mockTicket,
            idaasTicket: mockIdaasTicket
          });
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
      it('ユーザー情報が存在しない時、404エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'お客さまのアカウントは現在ログインできません。'
            },
            errorCode: { S: 'I005-00010' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockHighRiskUserNotUser')
          .query({
            ticket: mockTicket,
            idaasTicket: mockIdaasTicket
          });
        expect(res.statusCode).toEqual(404);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'お客さまのアカウントは現在ログインできません。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        (saveSession as jest.Mock).mockRejectedValue(new Error('DB Error'));

        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockHighRiskUserId')
          .query({
            ticket: mockTicket,
            idaasTicket: mockIdaasTicket
          });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
