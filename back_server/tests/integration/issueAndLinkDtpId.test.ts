/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { createCipheriv, randomBytes } from 'crypto';
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';
import { components } from '../../src/schemas/schema';

describe('DTPID紐づけ情報登録', () => {
  const bffPath = '/mobile/api/v1/dtpId/link';

  // 復号化の対象となる暗号化ticketを作成する（getIdaasEncryptedCookieValueメソッドの処理と同じ）
  const userUid = 'Id12345';
  const emailAddress = '<EMAIL>';
  // Base64URLエンコード関数
  const base64UrlEncode = (buffer: Buffer): string =>
    buffer
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/g, '');
  // 乱数生成関数
  const generateNonce = (length: number) => {
    const characters = '0123456789!@#$%^&*()_+-=[]{};:\'",.<>?';
    return Array.from(
      randomBytes(length),
      (byte) => characters[byte % characters.length]
    ).join('');
  };
  const KEY = Buffer.from(process.env.IDAAS_COOKIE_ENCRYPTION_KEY ?? '', 'hex');
  // 共通化鍵をインプットにSHA-256(ECB)暗号化インスタンス生成
  const cipher = createCipheriv('aes-256-ecb', KEY, null);
  // UNIXタイムスタンプ生成
  const timeStamp = Math.round(Date.now() / 1000);
  // 半角数字記号10文字の乱数を生成
  const nonce = generateNonce(10);
  // 平文ticket生成
  const data = `${nonce}|${userUid}|${emailAddress}|${timeStamp}`;
  // 暗号化ticket生成
  const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
  // Base64URLエンコードした暗号化ticket（文字列）を格納
  const ticket = base64UrlEncode(encryptedData);
  // 暗号化ticketを作成完了

  const mockEncryptedCookie = {
    ticket: ticket,
    domain: 'domain',
    path: '/',
    secure: 'true',
    httponly: 'true'
  };

  let res: any;

  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };
  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: '1234567890',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-09-21T05:56:17.369Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  beforeAll(async () => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  afterAll(async () => {
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // フロント→bff宛リクエストのモックデータ（正常系）
  const mockIssueAndLinkDtpIdRequest: components['schemas']['IssueAndLinkDtpIdRequest'] =
    {
      encryptedCookie: mockEncryptedCookie
    };

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // バックエンド宛API（api/user/id-links）の返却値のモックデータ
      const mockIdLinksResponse = {
        dtpId: '<EMAIL>',
        userUid: 'Id12345',
        valueDoorId: '1234567890'
      };

      // backendRequestをモック
      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockResolvedValueOnce({ data: {} });

      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockIdLinksResponse });

      res = await request(app)
        .post(bffPath)
        .set('authorization', 'Bearer mockSessionId')
        .send(mockIssueAndLinkDtpIdRequest);

      // bff→フロント宛レスポンスの期待値
      const expectissueAndLinkDtpIdResponse = {
        dtpId: mockIdLinksResponse.dtpId,
        userId: mockIdLinksResponse.userUid,
        valueDoorId: mockIdLinksResponse.valueDoorId,
        encryptedCookie: mockEncryptedCookie
      };

      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectissueAndLinkDtpIdResponse);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // フロント→bff宛リクエストのモックデータ（バリデーションエラー）
        const mockInValidIssueAndLinkDtpIdRequest = {};
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        res = await request(app)
          .post(bffPath)
          .set('authorization', 'Bearer mockSessionId')
          .send(mockInValidIssueAndLinkDtpIdRequest);

        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });

      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        res = await request(app)
          .post(bffPath)
          .send(mockIssueAndLinkDtpIdRequest);

        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

        res = await request(app)
          .post(bffPath)
          .set('authorization', 'Bearer mockSessionId')
          .send(mockIssueAndLinkDtpIdRequest);

        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
