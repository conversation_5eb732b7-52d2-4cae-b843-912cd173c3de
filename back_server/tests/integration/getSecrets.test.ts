/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import {
  clientId,
  clientSecret,
  mobileEncryptSalt
} from '../../src/config/config';

describe('SALT値取得', () => {
  const bffPath = '/mobile/api/v1/classified/salt';

  let res: any;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('SALT値', async () => {
      // front宛レスポンスの期待値
      const expectResponse = {
        mobileEncryptSalt
      };

      res = await request(app).get(`${bffPath}`);

      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponse);
    });
  });
});

describe('クライアント情報取得', () => {
  const bffPath = '/mobile/api/v1/classified/client';

  let res: any;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('クライアント情報', async () => {
      // front宛レスポンスの期待値
      const expectResponse = {
        clientId,
        clientSecret
      };

      res = await request(app).get(`${bffPath}`);

      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponse);
    });
  });
});
