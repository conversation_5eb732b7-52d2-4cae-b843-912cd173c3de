/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { randomBytes } from 'crypto';
import request from 'supertest';
import { v4 as mockUuidv4 } from 'uuid';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import { components } from '../../src/schemas/schema';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';
import { errorCodes } from '../../src/errors/errorCodes';

// 実際にsessionServiceを通り保存されていることを確認すべきだが、AWS KEYを設定するとビルドできなくなるためモック化している
jest.mock('../../src/services/sessionService');
jest.mock('uuid');

const mockHash = {
  update: jest.fn().mockReturnThis(),
  digest: jest.fn(() => 'mockedHashValue')
};

jest.mock('crypto', () => ({
  createHash: jest.fn(() => mockHash),
  createCipheriv: jest.fn(() => ({
    update: jest.fn().mockReturnValue(Buffer.from('encryptedDataPart1=')),
    final: jest.fn().mockReturnValue(Buffer.from('encryptedDataPart2'))
  })),
  randomBytes: jest.fn(() => Buffer.from('mockedRandomString', 'hex'))
}));

describe('DTPID認証', () => {
  const bffPath = '/mobile/api/v1/loginDtpId';

  let res: any;

  // モックデータ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '********9**********0',
    clientId: '**********',
    compName: '取引先名',
    userSeiMei: '利用者名',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    kigyoCd: '**************',
    userAuths: {
      web21: {
        accountInquiryAuth: true,
        generalTransferAuth: true,
        salaryTransferAuth: true,
        localTaxPaymentAuth: true,
        approvalAuth: true,
        transferAuth: true,
        fileSendingAuth: true,
        taxesOrFeesPaymentAuth: true,
        accountTransferAuth: true,
        transferDestinationCheckAuth: true,
        dataConversionAuth: true,
        accountInquiryOnSmartPhoneAuth: true,
        transferOnSmartPhoneAuth: true,
        approvalOnSmartPhoneAuth: true,
        preSettingAccountInfoOnSmartPhoneAuth: true,
        settingSecurityOnSmartPhoneAuth: true,
        preSettingAccountInfoAuth: true,
        settingSecurityAuth: true
      },
      gets: null
    }
  };

  const mockDtpIdLoginRequest: components['schemas']['DtpIdLoginRequest'] = {
    dtpId: '<EMAIL>',
    password: '********',
    caulisSessionId: '550e8400-e29b-41d4-a716-************'
  };

  const mockDtpIdLoginResponse = {
    dtpId: '<EMAIL>',
    userUid: '12345',
    valueDoorId: '**********',
    valueDoorUser: mockUserInfo
  };

  const mockCaulisResponse = {
    relativeSuspiciousValue: 'L'
  };

  // VDIDが空の場合のモックレスポンスデータ
  const mockDtpIdLoginVdidEmptyResponse = {
    dtpId: '<EMAIL>',
    userUid: '12345',
    valueDoorId: ''
  };

  // 期待データ
  const expectDtpIdLoginResponse = {
    sessionId: 'mockSessionId',
    encryptedCookie: {
      ticket: 'ZW5jcnlwdGVkRGF0YVBhcnQxPWVuY3J5cHRlZERhdGFQYXJ0Mg',
      domain: 'dev.biztest.smbc.co.jp',
      path: '/',
      secure: 'true',
      httponly: 'true'
    }
  };

  const mockTimeStamp = **********; // UNIXタイムスタンプ
  const mockBytes = '********90'; // randomBytesの返却値

  let originalDateNow: () => number;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
    originalDateNow = Date.now;
    global.Date.now = jest.fn(() => mockTimeStamp * 1000);

    (randomBytes as jest.Mock).mockReturnValue(Buffer.from(mockBytes));
  });

  afterEach(() => {
    global.Date.now = originalDateNow;
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // ランダムに払い出されてしまう部分のみモック
      const mockedUUID = 'mockSessionId';
      (mockUuidv4 as jest.MockedFunction<typeof mockUuidv4>).mockReturnValue(
        mockedUUID
      );

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValueOnce({ data: mockDtpIdLoginResponse });
      backendRequestSpy.mockResolvedValueOnce({ data: mockCaulisResponse });

      res = await request(app).post(bffPath).send(mockDtpIdLoginRequest);

      expect(backendRequestSpy).toHaveBeenCalledTimes(2);
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectDtpIdLoginResponse);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('DTPIDログイン認証APIで400エラーの時、怪しさ判定API実施対象外であること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'バックエンドでリクエストエラー'
            },
            errorCode: { S: 'I011-00003' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'I011-00003',
            message: 'Request error',
            httpStatusCode: 400,
            details: 'Request error'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
        backendRequestSpy.mockRejectedValueOnce({
          isAxiosError: true,
          response: errorData
        });

        res = await request(app).post(bffPath).send(mockDtpIdLoginRequest);

        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'バックエンドでリクエストエラー'
        );
      });

      it('DTPIDログイン認証APIで500エラーの時、怪しさ判定APIがリクエストされ失敗した際にログイン認証APIのエラーが返却されること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'バックエンドでリクエストエラー'
            },
            errorCode: { S: 'I011-00020' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'I011-00020',
            message: 'Request error',
            httpStatusCode: 500,
            details: 'Request error'
          },
          status: 500
        };
        const suspiciousErrorData = {
          data: {
            code: 'I001-00001',
            message: 'login-suspicious-detection error',
            httpStatusCode: 500,
            details: 'login-suspicious-detection error'
          },
          status: 500
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
        backendRequestSpy.mockRejectedValueOnce({
          isAxiosError: true,
          response: errorData
        });
        // POST /api/caulis/login-suspicious-detection APIのモックレスポンスを設定
        backendRequestSpy.mockRejectedValueOnce({
          data: {
            response: suspiciousErrorData
          }
        });

        res = await request(app).post(bffPath).send(mockDtpIdLoginRequest);
        expect(backendRequestSpy).toHaveBeenCalledTimes(2);
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'バックエンドでリクエストエラー'
        );
      });

      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'バックエンドで認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'I005-00001',
            message: 'Auth error',
            details:'Auth error'
          },
          status: 401
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
        backendRequestSpy.mockRejectedValueOnce({
          isAxiosError: true,
          response: errorData
        });
        res = await request(app).post(bffPath).send(mockDtpIdLoginRequest);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'バックエンドで認証エラー'
        );
      });
    });
    describe('404系エラー', () => {
      it('VDIDが空の場合、口座開設中エラーを返すこと', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockResolvedValueOnce({
          data: mockDtpIdLoginVdidEmptyResponse
        });
        // エラー情報設定
        response.errorMaster = [
          {
            errorMessage: {
              S: '口座開設中のため、ログインできません。口座開設後にログインしてください。'
            },
            errorCode: { S: 'I005-00008' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        res = await request(app).post(bffPath).send(mockDtpIdLoginRequest);
        expect(res.statusCode).toEqual(404);
        expect(JSON.parse(res.text).errorCode).toEqual(
          errorCodes.ACCOUNT_CREATION_IN_PROGRESS
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

        res = await request(app).post(bffPath).send(mockDtpIdLoginRequest);
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
