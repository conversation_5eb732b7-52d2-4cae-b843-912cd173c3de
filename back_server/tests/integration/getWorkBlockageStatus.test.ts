/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';

import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import dynamoDBClient from '../../src/utils/dynamoDB';
import { WorkBlockage } from '../../src/models/workBlockage';

describe('機能閉塞設定取得', () => {
  const bffPath = '/mobile/api/v1/work/blockage';

  let res: any;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること(DBに情報あり)', async () => {
      const mockSend = jest.spyOn(dynamoDBClient, 'send');

      // テストデータ
      const functionId = '0101';

      // 返却値を定義
      const workBlockage: WorkBlockage = {
        status: '1',
        functionId,
        functionName: 'テスト'
      };
      const mockDBResponse = {
        Item: {
          status: { S: workBlockage.status },
          function_id: { S: functionId },
          function_name: { S: workBlockage.functionName }
        }
      };
      // 期待値データ
      const expextBlockageStatus = {
        status: '1'
      };

      // モックデータを返却
      const mockReturnedValue = jest.fn().mockReturnValue(mockDBResponse);
      mockSend.mockImplementation(mockReturnedValue);

      res = await request(app).get(bffPath).query({ functionId });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expextBlockageStatus);
    });

    it('期待通りのレスポンスであること(DBに情報がない場合)', async () => {
      const mockSend = jest.spyOn(dynamoDBClient, 'send');

      // テストデータ
      const functionId = '0101';

      // 期待値データ
      const expextBlockageStatus = {
        status: '0'
      };

      // 返却値を定義
      const mockResponse = {};

      // モックデータを返却
      const mockReturnedValue = jest.fn().mockReturnValue(mockResponse);
      mockSend.mockImplementation(mockReturnedValue);

      res = await request(app).get(bffPath).query({ functionId });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expextBlockageStatus);
    });
  });

  describe('400系エラー', () => {
    it('リクエストパラメーター不足の際、400エラーになること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
          },
          errorCode: { S: 'W005-00002' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
      res = await request(app).get(bffPath);
      expect(res.statusCode).toEqual(400);
      expect(JSON.parse(res.text).errorMessage).toEqual(
        'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
      );
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        const mockSend = jest.spyOn(dynamoDBClient, 'send');
        const functionId = '0101';

        // モックデータを返却
        mockSend.mockReturnValueOnce();

        res = await request(app).get(bffPath).query({ functionId });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
