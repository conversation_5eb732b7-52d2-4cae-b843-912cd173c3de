/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('お手続き番号確認', () => {
  const bffPath = '/mobile/api/v1/identityVerification/uuid';

  let res: any;

  // モックデータ
  const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

  // 期待データ
  const expectUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      res = await request(app).get(`${bffPath}`).query({
        referenceNumber: '1000005000000'
      });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectUuidData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('BFF内でリクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).get(`${bffPath}`).query({
          referenceNumber: 'abcde'
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
      it('バックエンドでリクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValueOnce({ response: errorData });
        res = await request(app).get(`${bffPath}`).query({
          referenceNumber: '1000005000000'
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

        res = await request(app).get(`${bffPath}`).query({
          referenceNumber: '1000005000000'
        });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
