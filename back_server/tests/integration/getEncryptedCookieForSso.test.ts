/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import type {} from '../../src/types/global';

describe('SSO時の暗号化Cookie取得', () => {
  const bffPath = '/mobile/api/v1/idaas/sso/cookie';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: {
        accountInquiryAuth: true,
        generalTransferAuth: true,
        salaryTransferAuth: true,
        localTaxPaymentAuth: true,
        approvalAuth: true,
        transferAuth: true,
        fileSendingAuth: true,
        taxesOrFeesPaymentAuth: true,
        accountTransferAuth: true,
        transferDestinationCheckAuth: true,
        dataConversionAuth: true,
        accountInquiryOnSmartPhoneAuth: true,
        transferOnSmartPhoneAuth: true,
        approvalOnSmartPhoneAuth: true,
        preSettingAccountInfoOnSmartPhoneAuth: true,
        settingSecurityOnSmartPhoneAuth: true,
        preSettingAccountInfoAuth: true,
        settingSecurityAuth: true
      },
      gets: null
    }
  };

  const mockErrorUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: {
        accountInquiryAuth: true,
        generalTransferAuth: true,
        salaryTransferAuth: true,
        localTaxPaymentAuth: true,
        approvalAuth: true,
        transferAuth: true,
        fileSendingAuth: true,
        taxesOrFeesPaymentAuth: true,
        accountTransferAuth: true,
        transferDestinationCheckAuth: true,
        dataConversionAuth: true,
        accountInquiryOnSmartPhoneAuth: true,
        transferOnSmartPhoneAuth: true,
        approvalOnSmartPhoneAuth: true,
        preSettingAccountInfoOnSmartPhoneAuth: true,
        settingSecurityOnSmartPhoneAuth: true,
        preSettingAccountInfoAuth: true,
        settingSecurityAuth: true
      },
      gets: null
    }
  };

  const mockNotDtpIdUserSession: Session = {
    sessionId: 'mockNotDtpIdUserSessionId',
    vdId: 'ABCDE12345',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockDtpIdUserSession: Session = {
    sessionId: 'mockDtpIdUserSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockErrorSession: Session = {
    sessionId: 'mockErrorSessionId',
    vdId: 'ABCDE12345',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockErrorUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const mockTimeStamp = 1627380000; // UNIXタイムスタンプ
  const mockBytes = '1234567890'; // randomBytesの返却値

  beforeEach(async () => {
    await saveSession(mockDtpIdUserSession);
    await saveSession(mockNotDtpIdUserSession);
    await saveSession(mockErrorSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockNotDtpIdUserSessionId');
    await deleteSession('mockDtpIdUserSessionId');
    await deleteSession('mockErrorSessionId');
  });

  // 本来は正常系のテストにて生成された暗号化Cookieが正しい値であることを確認すべきだが
  // cryptをMockすることでDBへのセッション登録ができず、テストが実施不可となるためhttpステータスのみを確認対象とする
  describe('正常系', () => {
    it('正常系：DTPID利用者の場合、userUid,dtpIdを基に暗号化Cookieが生成されること', async () => {
      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockDtpIdUserSessionId');
      expect(res.status).toEqual(200);
    });

    it('正常系：DTPID利用者でない場合VDに登録されたメールアドレス,VDIDを基に暗号化Cookieが生成される事', async () => {
      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockNotDtpIdUserSessionId');
      expect(res.status).toEqual(200);
    });
  });

  describe('異常系', () => {
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).get(bffPath);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('内部エラー発生時、500エラーになること', async () => {
        try {
          res = await request(bffPath)
            .get(bffPath)
            .set('authorization', 'Bearer mockSessionId');
        } catch (error) {
          res = {
            status: 500,
            body: { error: 'Internal Server Error' }
          };
        }
        expect(res.status).toEqual(500);
        expect(res.body.error).toBe('Internal Server Error');
      });
    });
  });
});
