/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('eKYCのWebViewURLを取得', () => {
  const bffPath = '/mobile/api/v1/identityVerification/ekycUrl';

  let res: any;

  // モックデータ
  const mockUuidData = { uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8' };
  const mockEkycUrlData = 'https://www.ekyc';

  // 期待データ
  const expectEkycUrlData = { ekycUrl: 'https://www.ekyc' };

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestGetSpy = jest.spyOn(backendRequest, 'get');
      backendRequestGetSpy.mockResolvedValueOnce({ data: mockUuidData });

      const backendRequestPostSpy = jest.spyOn(backendRequest, 'post');
      backendRequestPostSpy.mockResolvedValueOnce({ data: mockEkycUrlData });

      res = await request(app).post(`${bffPath}`).send({
        referenceNumber: '1000005000000',
        uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
        userType: '01'
      });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectEkycUrlData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('BFF内でリクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: 'abcde',
          uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
          userType: '01'
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
      it('バックエンドでリクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValueOnce({ response: errorData });
        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: '1000005000000',
          uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
          userType: '01'
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
      it('UUIDがAuroraの値と一致しない時、404エラーになること', async () => {
        // データを格納;
        response.errorMaster = [
          {
            errorMessage: {
              S: 'UUIDが見つかりませんでした。'
            },
            errorCode: { S: 'E005-00003' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockResolvedValueOnce({ data: mockUuidData });
        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: '1000005000000',
          uuid: 'abcde',
          userType: '01'
        });
        expect(res.statusCode).toEqual(404);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'UUIDが見つかりませんでした。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValueOnce(new Error('my Error'));

        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: '1000005000000',
          uuid: '6ff7f45f-7caf-4d38-8410-c5b49a21c3d8',
          userType: '01'
        });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
