/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { expirationTime, jstTime } from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';
import { components } from '../../src/schemas/schema';

describe('怪しさ判定', () => {
  const bffPath = '/mobile/api/v1/loginSuspiciousDetection';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  // モックデータ
  const mockRequest: components['schemas']['LoginSuspiciousDetectionRequest'] =
    {
      caulisSessionId: '550e8400-e29b-41d4-a716-446655440000',
      vdId: 'ABCDE12345',
      errorMessageId: 'C0007'
    };

  beforeAll(async () => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  afterAll(async () => {
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // バックエンドAPIレスポンスのモックデータ
      const mockResponseData = {
        data: {
          relativeSuspiciousValue: 'L'
        }
      };

      // BFFからフロントへの返却値の期待値
      const expectResponse = {
        message: 'ValueDoorログイン失敗時の怪しさ判定リクエストを実行しました。'
      };

      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue(mockResponseData);

      res = await request(app).post(bffPath).send(mockRequest);
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponse);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('バックエンド側でリクエスト不正が発生した場合、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W001-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W001-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue({ response: errorData });

        res = await request(app).post(bffPath).send(mockRequest);
        expect(res.status).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });

      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: 'バックエンドで認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'I005-00001',
            message: 'Auth error',
            details: 'Auth error'
          },
          status: 401
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        // POST /api/user/auth/dtpid-login APIのモックレスポンスを設定
        backendRequestSpy.mockRejectedValueOnce({
          response: errorData
        });
        res = await request(app).post(bffPath).send(mockRequest);
        expect(res.status).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'バックエンドで認証エラー'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app).post(bffPath).send(mockRequest);
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
