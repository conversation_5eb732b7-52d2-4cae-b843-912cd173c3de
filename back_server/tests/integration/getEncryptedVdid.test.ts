/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import crypto from 'crypto';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import { Session } from '../../src/models/session';
import type {} from '../../src/types/global';
import {
  expirationTime,
  jstTime,
  unixTimestampInMinutes
} from '../../src/utils/dateFormat';
import { deleteSession, saveSession } from '../../src/services/sessionService';

describe('getEncryptedVdid()', () => {
  const bffPath = '/mobile/api/v1/encryptedVdid/encrypt';
  let res: any;

  const mockVdId = '1234567890';

  // セッション用データ
  const mockSessionId = 'mockSessionId';
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: mockSessionId,
    vdId: mockVdId,
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const sharedKey = process.env.SHARED_KEY_FOR_TRANSFER ?? '';

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession(mockSessionId);
  });

  describe('正常系', () => {
    it('期待通りの暗号化データを返すこと', async () => {
      // テストのリクエストを送信
      res = await request(app)
        .get(bffPath)
        .set('authorization', `Bearer ${mockSessionId}`);

      // 期待値（暗号化VDID）を作成する（web21OtpController、encryptDataメソッドの処理と同じ）
      const timestamp = unixTimestampInMinutes();
      const key = sharedKey + timestamp.toString();
      const cipher = crypto.createCipheriv(
        'aes-256-ecb',
        Buffer.from(key),
        null
      );
      let encrypted = cipher.update(mockVdId, 'utf8', 'base64');
      encrypted += cipher.final('base64');
      // 期待値（暗号化VDID）の作成完了

      // レスポンスの確認
      expect(res.status).toEqual(200);
      expect(res.body.encryptedVdId).toEqual(encrypted);
    });
  });

  describe('400エラー', () => {
    it('認証エラーの時、401エラーになること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: '認証エラー'
          },
          errorCode: { S: 'I005-00001' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
      res = await request(app).get(bffPath);
      expect(res.status).toEqual(401);
      expect(res.body.errorMessage).toEqual('認証エラー');
    });
  });

  describe('500エラー', () => {
    it('内部エラーが発生した場合、500エラーを返すこと', async () => {
      // テスト中のみcreateCipherivをモック
      const mockCipher = {
        update: jest.fn().mockReturnValue('mockedEncryptedPart1'),
        final: jest.fn().mockReturnValue('mockedEncryptedPart2'),
        setAutoPadding: jest.fn()
      };

      // cryptoモジュールのcreateCipheriv関数をスパイしてモックを返す
      jest
        .spyOn(crypto, 'createCipheriv')
        .mockReturnValue(mockCipher as unknown as crypto.Cipher);

      // mockCipher.updateがエラーを投げるように設定
      mockCipher.update.mockImplementationOnce(() => {
        throw new Error('Encryption failed');
      });

      res = await request(app)
        .get(bffPath)
        .set('authorization', `Bearer ${mockSessionId}`);

      expect(res.status).toEqual(500);
      expect(res.body.errorMessage).toEqual(
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
      );
    });
  });
});
