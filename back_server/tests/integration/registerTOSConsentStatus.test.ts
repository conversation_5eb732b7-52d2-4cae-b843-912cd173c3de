/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('利用規定同意状況確認', () => {
  const bffPath = '/mobile/api/v1/tos/consentStatus/register';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-09-21T05:56:17.369Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: '999999999999',
    createdAt: '2023-10-01 10:00:00 GMT+0',
    updatedAt: '2023-10-01 10:00:00 GMT+0'
  };

  // モックデータ
  const mockRegisterResponse = {
    message: 'ok'
  };

  // 期待値データ
  const expectRegisterResponseData = {
    message: 'ok'
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: mockRegisterResponse });

      res = await request(app)
        .post(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId')
        .send({ agreeTerm: 'DTP', channel: '02' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectRegisterResponseData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue({ response: errorData });
        res = await request(app)
          .post(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId')
          .send({ agreeTerm: 'DTP', channel: '02' });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .post(`${bffPath}`)
          .send({ agreeTerm: 'DTP', channel: '02' });
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .post(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId')
          .send({ agreeTerm: 'DTP', channel: '02' });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
