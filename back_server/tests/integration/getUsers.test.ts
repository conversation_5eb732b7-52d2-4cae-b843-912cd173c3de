/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import {
  DATE_FORMAT_SLASH,
  expirationTime,
  jstTime,
  serverDate
} from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('利用者情報取得', () => {
  const bffPath = '/mobile/api/v1/bank/users';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  // モックデータ
  const mockUsersData = {
    count: 4,
    accountList: [
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100003',
        remitterName: 'ｲﾀｸｼｬﾒｲ3'
      },
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100002',
        remitterName: 'ｲﾀｸｼｬﾒｲ2'
      },
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100004',
        remitterName: 'ｲﾀｸｼｬﾒｲ4'
      },
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100001',
        remitterName: 'ｲﾀｸｼｬﾒｲ1'
      }
    ],
    balanceAuthorityStatus: 'ON'
  };

  const mockAccountSettingsData = {
    accounts: [
      {
        accountId: '**************',
        isHidden: false,
        accountType: 'web21',
        displayName: '振り込み用口座1'
      },
      {
        accountId: '**************',
        isHidden: true,
        accountType: 'web21',
        displayName: '振り込み用口座2'
      },
      {
        accountId: '1',
        isHidden: false,
        accountType: 'freee'
      },
      {
        accountId: '2',
        isHidden: true,
        accountType: 'freee'
      }
    ]
  };

  // 期待値
  const expectedUsersData = {
    count: 4,
    accountList: [
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100001',
        remitterName: 'ｲﾀｸｼｬﾒｲ1',
        bankName: '三井住友銀行',
        displayAccountName: '振り込み用口座1',
        isHidden: false,
        index: 0,
        accountApiType: 'web21'
      },
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100002',
        remitterName: 'ｲﾀｸｼｬﾒｲ2',
        bankName: '三井住友銀行',
        displayAccountName: '振り込み用口座2',
        isHidden: true,
        index: 1,
        accountApiType: 'web21'
      },
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100003',
        remitterName: 'ｲﾀｸｼｬﾒｲ3',
        bankName: '三井住友銀行',
        displayAccountName: null,
        isHidden: false,
        index: 2,
        accountApiType: 'web21'
      },
      {
        accountId: '**************',
        account: '************',
        branchNameKana: 'ｱｷﾊﾊﾞﾗ支店',
        branchNameKanji: '秋葉原支店',
        accountType: '普通',
        accountNumber: '1100004',
        remitterName: 'ｲﾀｸｼｬﾒｲ4',
        bankName: '三井住友銀行',
        displayAccountName: null,
        isHidden: false,
        index: 3,
        accountApiType: 'web21'
      }
    ],
    balanceAuthorityStatus: 'ON',
    serverDate: serverDate(DATE_FORMAT_SLASH)
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({ data: mockUsersData });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      res = await request(app)
        .get(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectedUsersData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message:
              'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。',
            details:
              'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue({ response: errorData });
        res = await request(app)
          .get(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).get(`${bffPath}`);
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    it('バックエンドレスポンスがない時、404エラーになること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'Contents is Not Found'
          },
          errorCode: { S: 'W005-00001' }
        },
        {
          errorMessage: {
            S: '認証エラー'
          },
          errorCode: { S: 'I005-00001' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
      const errorData = {
        data: {
          code: 'W005-00001',
          message: 'Not Found',
          details: 'Not Found'
        },
        status: 404
      };
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue({ response: errorData });
      res = await request(app)
        .get(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId');
      expect(res.statusCode).toEqual(404);
      expect(JSON.parse(res.text).errorMessage).toEqual(
        'Contents is Not Found'
      );
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .get(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
