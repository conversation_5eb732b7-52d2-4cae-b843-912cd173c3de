/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';

describe('freee連携完了', () => {
  const bffPath = '/mobile/api/freee/auth';

  let res: any;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // front宛レスポンスの期待値
      const expectResponse = {
        message: 'OK'
      };

      res = await request(app).get(`${bffPath}`);

      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponse);
    });
  });
});
