/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('freeeリダイレクトURL取得', () => {
  const bffPath = '/mobile/api/v1/freee/redirect';
  const freeeDomain = process.env.FREEE_BASE_DOMAIN;
  let res: any;

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  describe('正常系', () => {
    it('初回代表者ログイン', async () => {
      const mockState = 'state';
      const mockPartnerId = 'partnerId';
      const mockTicket = 'ticket';
      const mockLoginType = 'firstLogin';
      res = await request(app).get(
        `${bffPath}?state=${mockState}&partnerId=${mockPartnerId}&ticket=${mockTicket}&loginType=${mockLoginType}`
      );
      expect(res.headers['location']).toBe(
        `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=calc&redirect_end_point=smbc%2Fterms%3Fstate%3Dstate%26partner_id%3DpartnerId&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error%3Fstate%3Dstate%26partner_id%3DpartnerId`
      );
      expect(res.headers['set-cookie'][0]).toBe(
        'ticket=ticket; Domain=dev.biztest.smbc.co.jp; Path=/; HttpOnly; Secure'
      );
    });
    it('初回従業員ログイン', async () => {
      const mockState = 'state';
      const mockPartnerId = 'partnerId';
      const mockTicket = 'ticket';
      const mockLoginType = 'firstEmployeeLogin';
      res = await request(app).get(
        `${bffPath}?state=${mockState}&partnerId=${mockPartnerId}&ticket=${mockTicket}&loginType=${mockLoginType}`
      );
      expect(res.headers['location']).toEqual(
        `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=calc&redirect_end_point=smbc%2Fterms&redirect_query=state%3Dstate%26partner_id%3DpartnerId&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error%3Fstate%3Dstate%26partner_id%3DpartnerId%26only_login%3D1`
      );
      expect(res.headers['set-cookie'][0]).toBe(
        'ticket=ticket; Domain=dev.biztest.smbc.co.jp; Path=/; HttpOnly; Secure'
      );
    });
    it('クレジットカード追加(クレジットカード連携期限切れ)ログイン', async () => {
      const mockState = 'state';
      const mockPartnerId = 'partnerId';
      const mockTicket = 'ticket';
      const mockLoginType = 'addCreditCardLogin';
      res = await request(app).get(
        `${bffPath}?state=${mockState}&partnerId=${mockPartnerId}&ticket=${mockTicket}&loginType=${mockLoginType}`
      );
      expect(res.headers['location']).toBe(
        `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounting&redirect_end_point=orientation%2Fsearch_bank%23credit_card&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
      );
      expect(res.headers['set-cookie'][0]).toBe(
        'ticket=ticket; Domain=dev.biztest.smbc.co.jp; Path=/; HttpOnly; Secure'
      );
    });
    it('他行口座追加(他行口座連携期限切れ)ログイン', async () => {
      const mockState = 'state';
      const mockPartnerId = 'partnerId';
      const mockTicket = 'ticket';
      const mockLoginType = 'addBankAccountLogin';
      res = await request(app).get(
        `${bffPath}?state=${mockState}&partnerId=${mockPartnerId}&ticket=${mockTicket}&loginType=${mockLoginType}`
      );
      expect(res.headers['location']).toBe(
        `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounting&redirect_end_point=orientation%2Fsearch_bank%23bank&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
      );
      expect(res.headers['set-cookie'][0]).toBe(
        'ticket=ticket; Domain=dev.biztest.smbc.co.jp; Path=/; HttpOnly; Secure'
      );
    });
    it('メンバー追加ログイン', async () => {
      const mockState = 'state';
      const mockPartnerId = 'partnerId';
      const mockTicket = 'ticket';
      const mockLoginType = 'addMembersLogin';
      res = await request(app).get(
        `${bffPath}?state=${mockState}&partnerId=${mockPartnerId}&ticket=${mockTicket}&loginType=${mockLoginType}`
      );
      expect(res.headers['location']).toBe(
        `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounts&redirect_end_point=products%2Faccounting%2Fmembers&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
      );
      expect(res.headers['set-cookie'][0]).toBe(
        'ticket=ticket; Domain=dev.biztest.smbc.co.jp; Path=/; HttpOnly; Secure'
      );
    });
    it('金融機関連携期限切れ', async () => {
      const mockState = 'state';
      const mockPartnerId = 'partnerId';
      const mockTicket = 'ticket';
      const mockLoginType = 'expirationOfFinanceLinkage';
      res = await request(app).get(
        `${bffPath}?state=${mockState}&partnerId=${mockPartnerId}&ticket=${mockTicket}&loginType=${mockLoginType}`
      );
      expect(res.headers['location']).toBe(
        `https://accounts.${freeeDomain}/gateway/external_auth?provider=smbc&redirect_service_name=accounting&redirect_end_point=walletables&back_url=https%3A%2F%2Fcashflow.${freeeDomain}%2Ferror%2Fsmbc_error`
      );
      expect(res.headers['set-cookie'][0]).toBe(
        'ticket=ticket; Domain=dev.biztest.smbc.co.jp; Path=/; HttpOnly; Secure'
      );
    });
  });

  describe('異常系', () => {
    it('異常発生時、エラーページにリダイレクトすること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue(new Error('my Error'));

      res = await request(app).get(bffPath);
      expect(res.headers['location']).toBe(
        process.env.FREEE_RIDIRECT_ERROR_PAGE!
      );
    });
  });
});
