/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import {
  DATE_FORMAT_SLASH,
  DATE_FORMAT_YYYY_MM_DD,
  expirationTime,
  jstTime,
  serverDate,
  serverTime
} from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import { components } from '../../src/schemas/schema';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('入出金明細取得', () => {
  const bffPath = '/mobile/api/v1/bank/accounts/';
  const validAccountId = '*************';
  const invalidAccountId = '***********';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '***************67890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '***********',
    loginType: '214',
    kigyoCd: '**************',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  const createTransactionsDetailMock = (format: string) => {
    const transactionsDetailMock: components['schemas']['TransactionsDetail'][] =
      [];

    const createTransactionsDetailMockData: components['schemas']['TransactionsDetail'] =
      {
        inquiryNumber: '********',
        transactionDateJapaneseCalendar: '06-01-01',
        transactionDateAd: serverDate(format),
        valueDateJapaneseCalendar: '06-01-01',
        valueDateAd: serverDate(format),
        depositCreditType: '1',
        transactionType: '13',
        transactionTypeName: '交換（取立入金および交換払）',
        amount: ************,
        checksIssuedByOtherBanksAmount: 50000,
        exchangePresentationDateJapaneseCalendar: null,
        exchangePresentationDateAd: null,
        dishonoredReturnDateJapaneseCalendar: null,
        dishonoredReturnDateAd: null,
        billAndCheckType: null,
        billAndCheckTypeName: '約束手形',
        billAndCheckNumber: '1234567',
        branchCode: null,
        remitterCode: null,
        remitterNameContractorNumber: 'ABCｼｮｳｼﾞABCABCｼｮｳｼﾞABCABCｼｮｳｼﾞABC',
        remittingBankName: null,
        remittingBankBranchName: null,
        abstract: null,
        ediInfo: 'ﾌﾘｺﾐ',
        ediKey: null
      };
    transactionsDetailMock.push(createTransactionsDetailMockData);
    return transactionsDetailMock;
  };

  // モックデータ
  const transactionsMockData: components['schemas']['Transactions'] = {
    inquiryResult: 'inquiryComplete',
    categoryCode: '03',
    createdDateJapaneseCalendar: '05-07-01',
    createdDateAd: '2023-07-01',
    baseDate: serverDate(DATE_FORMAT_YYYY_MM_DD),
    baseTime: serverTime(),
    transactionDateFromJapaneseCalendar: '05/07/01',
    transactionDateFromAd: '2023/07/01',
    transactionDateToJapaneseCalendar: '29/01/31',
    transactionDateToAd: '2017/01/31',
    bankCode: '1234',
    bankNameKana: '1234ｷﾞﾝｺｳ',
    branchCode: '123',
    branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
    accountTypeCode: '01',
    accountType: '普通',
    accountNumber: '**********',
    accountId: '*************',
    accountName: '123ｼﾖｳｶｲ',
    overdraftTypeHeader: '1',
    passbookOrCertificateType: '1',
    balanceBeforeTransaction: ***********,
    transactions1: createTransactionsDetailMock(DATE_FORMAT_YYYY_MM_DD),
    transactions2: [],
    depositCount: 7,
    totalDepositAmount: ***************,
    withdrawalCount: 3,
    totalWithdrawalAmount: ***************,
    overdraftTypeTrailer: '1',
    transactionBalance: 100000,
    transactionDataCount: 6,
    totalCount: 5,
    count: 1,
    hasNext: false,
    itemKey: '20000',
    sessionInfo: 'BZDBIB112150951480831001',
    currencyCode: 'JPY'
  };

  // 期待データ
  const transactionsExpectData: components['schemas']['Transactions'] = {
    inquiryResult: 'inquiryComplete',
    categoryCode: '03',
    createdDateJapaneseCalendar: '05-07-01',
    createdDateAd: '2023-07-01',
    baseDate: serverDate(DATE_FORMAT_SLASH),
    baseTime: serverTime(),
    transactionDateFromJapaneseCalendar: '05/07/01',
    transactionDateFromAd: '2023/07/01',
    transactionDateToJapaneseCalendar: '29/01/31',
    transactionDateToAd: '2017/01/31',
    bankCode: '1234',
    bankNameKana: '1234ｷﾞﾝｺｳ',
    branchCode: '123',
    branchNameKana: 'ﾄｳｷｮｳｼﾃﾝ',
    accountTypeCode: '01',
    accountType: '普通',
    accountNumber: '**********',
    accountId: '*************',
    accountName: '123ｼﾖｳｶｲ',
    overdraftTypeHeader: '1',
    passbookOrCertificateType: '1',
    balanceBeforeTransaction: ***********,
    transactions1: createTransactionsDetailMock(DATE_FORMAT_SLASH),
    transactions2: [],
    depositCount: 7,
    totalDepositAmount: ***************,
    withdrawalCount: 3,
    totalWithdrawalAmount: ***************,
    overdraftTypeTrailer: '1',
    transactionBalance: 100000,
    transactionDataCount: 6,
    totalCount: 5,
    count: 1,
    hasNext: false,
    itemKey: '20000',
    sessionInfo: 'BZDBIB112150951480831001',
    currencyCode: 'JPY'
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: transactionsMockData });

      res = await request(app)
        .get(`${bffPath}${validAccountId}/transactions`)
        .set('authorization', 'Bearer mockSessionId')
        .query({ dateFrom: '2023-10-01', dateTo: '2024-01-01' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(transactionsExpectData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .get(`${bffPath}${invalidAccountId}/transactions`)
          .set('authorization', 'Bearer mockSessionId')
          .query({ dateFrom: '2023-10-01', dateTo: '2024-01-01' });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .get(`${bffPath}${validAccountId}/transactions`)
          .query({ dateFrom: '2023-10-01', dateTo: '2024-01-01' });
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    it('バックエンドレスポンスがない時、404エラーになること', async () => {
      // データを格納
      response.errorMaster = [
        {
          errorMessage: {
            S: 'Contents is Not Found'
          },
          errorCode: { S: 'W005-00001' }
        },
        {
          errorMessage: {
            S: '認証エラー'
          },
          errorCode: { S: 'I005-00001' }
        }
      ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
      const errorData = {
        data: {
          code: 'W005-00001',
          message: 'Not Found',
          details: 'Not Found'
        },
        status: 404
      };
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockRejectedValue({
        response: errorData
      });
      res = await request(app)
        .get(`${bffPath}${validAccountId}/transactions`)
        .set('authorization', 'Bearer mockSessionId')
        .query({ dateFrom: '2023-10-01', dateTo: '2024-01-01' });
      expect(res.statusCode).toEqual(404);
      expect(JSON.parse(res.text).errorMessage).toEqual(
        'Contents is Not Found'
      );
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .get(`${bffPath}${validAccountId}/transactions`)
          .set('authorization', 'Bearer mockSessionId')
          .query({ dateFrom: '2023-10-01', dateTo: '2024-01-01' });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
