/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('本人確認情報保存', () => {
  const bffPath = '/mobile/api/v1/identityVerification';

  let res: any;

  // モックデータ
  const mockRegisterResponse = {
    message: '本人確認情報を保存しました。'
  };

  // 期待値データ
  const expectRegisterResponseData = {
    message: '本人確認情報を保存しました。'
  };

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: mockRegisterResponse });

      res = await request(app).post(`${bffPath}`).send({
        referenceNumber: '1234565890123',
        role: 'AGENT',
        dataSource: 'JPKI',
        familyName: '若草',
        givenName: '太郎',
        familyNameKana: 'ワカクサ',
        givenNameKana: 'タロウ',
        familyNameAlphabetic: 'WAKAKUSA',
        givenNameAlphabetic: 'Taro',
        hepburnStyle: 'true',
        position: '部長',
        birthYear: 1999,
        birthMonth: 11,
        birthDay: 1,
        postCode: '1234567',
        prefecture: '東京都',
        city: '港区六本木',
        sectionNumberAndBuildingName: '1-2-3 SMBCビル12階'
      });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectRegisterResponseData);
    });

    it('実質的支配者 - 期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({ data: mockRegisterResponse });

      res = await request(app).post(`${bffPath}`).send({
        referenceNumber: '1234565890123',
        role: 'AGENT',
        dataSource: 'JPKI'
      });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectRegisterResponseData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('BFF内でリクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: '1000005000001',
          lastNameKana: 'ワカクサ',
          firstNameKana: 'タロウ',
          position: '部長'
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
      it('バックエンドでリクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue({ response: errorData });
        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: '1000005000001',
          role: 'REPRESENTATIVE',
          lastNameKanji: '若草',
          firstNameKanji: '太郎',
          lastNameKana: 'ワカクサ',
          firstNameKana: 'タロウ',
          lastName: 'Wakakusa',
          firstName: 'Taro',
          position: '部長',
          birthYear: 1999,
          birthMonth: 11,
          birthDay: 1,
          gender: 'MALE',
          postcode: '1234567',
          prefectureCity: '東京都港区六本木',
          blockSection: '2',
          blockNumber: '3',
          houseNumber: '2',
          buildingName: 'SMBCビル12階',
          eMail: '<EMAIL>'
        });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app).post(`${bffPath}`).send({
          referenceNumber: '1234565890123',
          role: 'AGENT',
          dataSource: 'JPKI',
          familyName: '若草',
          givenName: '太郎',
          familyNameKana: 'ワカクサ',
          givenNameKana: 'タロウ',
          familyNameAlphabetic: 'WAKAKUSA',
          givenNameAlphabetic: 'Taro',
          hepburnStyle: 'true',
          position: '部長',
          birthYear: 1999,
          birthMonth: 11,
          birthDay: 1,
          postCode: '1234567',
          prefecture: '東京都',
          city: '港区六本木',
          sectionNumberAndBuildingName: '1-2-3 SMBCビル12階'
        });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
