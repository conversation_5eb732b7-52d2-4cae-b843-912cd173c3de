/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';
import { components } from '../../src/schemas/schema';

describe('freee連携', () => {
  const bffPath = '/mobile/api/v1/freee/link';

  let res: any;

  const mockUserInfo = {
    id: 'ABCDE12345',
    issueInstant: '20231102062449',
    clientId: '012345',
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    kigyoCd: '12345678912345',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: '02itNsEZq0d4jtFAXXdKOtgNMv78toeIiaax71qmaxRDGcKm',
    freeeOauthState: 'state',
    expirationTime: '999999999999',
    createdAt: '2024-08-09 08:13:00 GMT+0',
    updatedAt: '2024-08-09 08:13:00 GMT+0'
  };

  // frontからのリクエストボディーのモックデータ
  const mockRequestBody: components['schemas']['FreeeLinkRequest'] = {
    code: 'cb46420e53c24580a4c4e0fe8f888888',
    stateFromFreee: '02a9NcXyG8RwPk3LMsUj7QvH3Kb9Ys4WxTz2BdFq6ZlMn0R5'
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'post');
      backendRequestSpy.mockResolvedValue({});

      // front宛レスポンスの期待値
      const expectResponse = {
        message: 'freeeアクセストークンを保存しました。'
      };

      res = await request(app)
        .post(`${bffPath}`)
        .set('authorization', 'Bearer mockSessionId')
        .send(mockRequestBody);

      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectResponse);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // エラーマスターのデータを格納
        const invalidRequestMessage =
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。';
        response.errorMaster = [
          {
            errorMessage: { S: invalidRequestMessage },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .post(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId');

        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          invalidRequestMessage
        );
      });
    });
    describe('401系エラー', () => {
      it('認証エラーの時、401エラーになること', async () => {
        // エラーマスターのデータを格納
        const authorizationErrorMessage = '認証エラー';
        response.errorMaster = [
          {
            errorMessage: { S: authorizationErrorMessage },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app).post(`${bffPath}`).send(mockRequestBody);

        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          authorizationErrorMessage
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'post');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .post(`${bffPath}`)
          .set('authorization', 'Bearer mockSessionId')
          .send(mockRequestBody);

        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
