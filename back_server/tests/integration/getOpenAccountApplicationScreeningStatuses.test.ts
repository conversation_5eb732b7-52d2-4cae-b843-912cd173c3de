/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import { logger } from '../../src/utils/logger';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('口座開設行内審査ステータス確認', () => {
  const bffPath =
    '/mobile/api/v1/identityVerification/openAccountApplication/screeningStatuses';

  let res: any;

  // モックデータ
  const mockScreeningStatusesData = {
    data: {
      referenceNumber: '*************',
      screeningStatus: 'SCREENING',
      screeningResults: {
        identificationData: [
          {
            role: 'BENEFICIARY1',
            hasFault: true
          },
          {
            role: 'REPRESENTATIVE',
            hasFault: true
          },
          {
            role: 'AGENT',
            hasFault: true
          },
          {
            role: 'BENEFICIARY2',
            hasFault: false
          }
        ]
      }
    }
  };

  // 期待データ
  const expectScreeningStatusesData = {
    isAccountOpeningRequestSubmitted: true,
    isRepresentativeHasFault: true,
    isAgentHasFault: true,
    isBeneficiary1HasFault: true,
    isBeneficiary2HasFault: false,
    isBeneficiary3HasFault: false,
    isBeneficiary4HasFault: false
  };

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockScreeningStatusesData });

      res = await request(app)
        .get(bffPath)
        .query({ referenceNumber: '*************' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectScreeningStatusesData);
    });
    it('手続き番号が14桁の場合、期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValue({ data: mockScreeningStatusesData });

      res = await request(app)
        .get(bffPath)
        .query({ referenceNumber: '*************1' });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectScreeningStatusesData);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエスト不正が発生した場合、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        const errorData = {
          data: {
            code: 'W005-00002',
            message: 'Bad Request',
            details: 'Bad Request'
          },
          status: 400
        };
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue({ response: errorData });

        res = await request(app)
          .get(bffPath)
          .query({ referenceNumber: 'あああ' });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .get(bffPath)
          .query({ referenceNumber: '0000005000003' });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
