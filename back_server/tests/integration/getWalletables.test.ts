/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import request from 'supertest';
import { response } from 'express';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
import app from '../../src/server';
import {
  expirationTime,
  jstTime,
  serverDateTime
} from '../../src/utils/dateFormat';
import { Session } from '../../src/models/session';
import { deleteSession, saveSession } from '../../src/services/sessionService';
import { logger } from '../../src/utils/logger';
import { components } from '../../src/schemas/schema';
import type {} from '../../src/types/global';
import backendRequest from '../../src/common/api/backendRequest';

describe('freee口座一覧取得', () => {
  const bffPath = '/mobile/api/v1/freee/walletables';

  let res: any;

  // 初期データ用データ
  const mockUserInfo = {
    ninsyoKbn: '01',
    compId: '12345678901234567890',
    compName: '取引先名',
    userSeiMei: '利用者名',
    userTyp: '01',
    compAtrbt: '01',
    userKn: 'リヨウシャメイ',
    Email: '<EMAIL>',
    telNo: '09011111111',
    loginType: '214',
    kigyoCd: '12345678912345',
    kigyoCdResultCd: 'EI01-S0001',
    userAuths: {
      web21: null,
      gets: null
    }
  };

  const mockSession: Session = {
    sessionId: 'mockSessionId',
    vdId: 'ABCDE12345',
    dtpId: '<EMAIL>',
    userId: 'id12345',
    issueInstant: '2023-11-02T06:24:49.727Z',
    userInfo: mockUserInfo,
    state: null,
    freeeOauthState: null,
    expirationTime: expirationTime(),
    createdAt: jstTime(),
    updatedAt: jstTime()
  };

  // モックデータ
  const mockFreeeWalletables = {
    walletables: [
      {
        id: 3,
        name: 'freee銀行3',
        bankId: 3,
        type: 'bank_account',
        lastBalance: 1565583,
        walletableBalance: 1340263,
        lastSyncedAt: '2025-01-01T09:00:00+09:00',
        syncStatus: 'success'
      },
      {
        id: 2,
        name: 'freee銀行2',
        bankId: 2,
        type: 'bank_account',
        lastBalance: 1565582,
        walletableBalance: 1340262,
        lastSyncedAt: '2025-01-01T09:00:00+09:00',
        syncStatus: 'success'
      },
      {
        id: 4,
        name: 'freee銀行4',
        bankId: 4,
        type: 'bank_account',
        lastBalance: 1565584,
        walletableBalance: 1340264,
        lastSyncedAt: '2025-01-01T09:00:00+09:00',
        syncStatus: 'success'
      },
      {
        id: 1,
        name: 'freee銀行1',
        bankId: 1,
        type: 'bank_account',
        lastBalance: 1565581,
        walletableBalance: 1340261,
        lastSyncedAt: '2025-01-01T09:00:00+09:00',
        syncStatus: 'success'
      }
    ]
  };

  // 口座表示設定のモックデータ
  const mockAccountSettingsData = {
    accounts: [
      {
        accountId: '1',
        isHidden: false,
        accountType: 'freee'
      },
      {
        accountId: '2',
        isHidden: true,
        accountType: 'freee'
      },
      {
        accountId: '**************',
        isHidden: false,
        accountType: 'web21',
        displayName: '振り込み用口座1'
      },
      {
        accountId: '**************',
        isHidden: true,
        accountType: 'web21',
        displayName: '振り込み用口座2'
      }
    ]
  };

  // 期待データ
  const expectFreeeWalletables: components['schemas']['FreeeWalletables'] = {
    walletables: [
      {
        id: 1,
        name: 'freee銀行1',
        bankId: 1,
        type: 'bank_account',
        lastBalance: 1565581,
        walletableBalance: 1340261,
        serverDateTime: serverDateTime(),
        lastSyncedAt: '2025/1/1 9:00',
        bankName: '',
        isHidden: false,
        displayOrder: 0,
        accountApiType: 'freee',
        syncStatus: '0'
      },
      {
        id: 2,
        name: 'freee銀行2',
        bankId: 2,
        type: 'bank_account',
        lastBalance: 1565582,
        walletableBalance: 1340262,
        serverDateTime: serverDateTime(),
        lastSyncedAt: '2025/1/1 9:00',
        bankName: '',
        isHidden: true,
        displayOrder: 1,
        accountApiType: 'freee',
        syncStatus: '0'
      },
      {
        id: 3,
        name: 'freee銀行3',
        bankId: 3,
        type: 'bank_account',
        lastBalance: 1565583,
        walletableBalance: 1340263,
        serverDateTime: serverDateTime(),
        lastSyncedAt: '2025/1/1 9:00',
        bankName: '',
        isHidden: false,
        displayOrder: 2,
        accountApiType: 'freee',
        syncStatus: '0'
      },
      {
        id: 4,
        name: 'freee銀行4',
        bankId: 4,
        type: 'bank_account',
        lastBalance: 1565584,
        walletableBalance: 1340264,
        serverDateTime: serverDateTime(),
        lastSyncedAt: '2025/1/1 9:00',
        bankName: '',
        isHidden: false,
        displayOrder: 3,
        accountApiType: 'freee',
        syncStatus: '0'
      }
    ]
  };

  beforeEach(async () => {
    // DBに初期データ投入
    await saveSession(mockSession);
  });

  beforeAll(() => {
    // テスト実行中のlog出力抑制
    logger.writeMsg = jest.fn();
  });

  afterEach(async () => {
    jest.clearAllMocks();
    // DBデータ削除
    await deleteSession('mockSessionId');
  });

  describe('正常系', () => {
    it('期待通りのレスポンスであること', async () => {
      // backendRequestをモック
      const backendRequestSpy = jest.spyOn(backendRequest, 'get');
      backendRequestSpy.mockResolvedValueOnce({ data: mockFreeeWalletables });
      backendRequestSpy.mockResolvedValueOnce({
        data: mockAccountSettingsData
      });

      res = await request(app)
        .get(bffPath)
        .set('authorization', 'Bearer mockSessionId')
        .query({
          type: 'bank_account',
          with_balance: true,
          with_sync_status: true
        });
      expect(res.status).toEqual(200);
      expect(res.body).toEqual(expectFreeeWalletables);
    });
  });

  describe('異常系', () => {
    describe('400系エラー', () => {
      it('リクエストパラメータ不正の時、400エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;

        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockSessionId')
          .query({ with_balance: true });
        expect(res.statusCode).toEqual(400);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
        );
      });

      it('認証エラーの時、401エラーになること', async () => {
        // データを格納
        response.errorMaster = [
          {
            errorMessage: {
              S: 'リクエストが不正です。必要なパラメータが欠けているか、入力が無効です。'
            },
            errorCode: { S: 'W005-00002' }
          },
          {
            errorMessage: {
              S: '認証エラー'
            },
            errorCode: { S: 'I005-00001' }
          }
        ] as unknown as Promise<Record<string, AttributeValue>[] | undefined>;
        res = await request(app)
          .get(bffPath)
          .query({ type: 'bank_account', with_balance: true });
        expect(res.statusCode).toEqual(401);
        expect(JSON.parse(res.text).errorMessage).toEqual('認証エラー');
      });
    });
    describe('500系エラー', () => {
      it('サーバー内部エラーの時、500エラーになること', async () => {
        // backendRequestをモック
        const backendRequestSpy = jest.spyOn(backendRequest, 'get');
        backendRequestSpy.mockRejectedValue(new Error('my Error'));

        res = await request(app)
          .get(bffPath)
          .set('authorization', 'Bearer mockSessionId')
          .query({
            type: 'bank_account',
            with_balance: true,
            with_sync_status: true
          });
        expect(res.statusCode).toEqual(500);
        expect(JSON.parse(res.text).errorMessage).toEqual(
          '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。'
        );
      });
    });
  });
});
