export const awsConfig = {
  region: 'ap-northeast-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
};

export const sessionTableName =
  process.env.SESSION_TABLE_NAME ?? 'dtp-mobile-local-session';
export const errorMasterTableName =
  process.env.ERROR_MASTER_TABLE_NAME ?? 'dtp-local-error-master';
export const highRiskUseTablerName =
  process.env.HIGH_RISK_USER_TABLE_NAME ??
  'dtp-mobile-dev-high-risk-device-user';
export const workBlockageMaintenanceTableName =
  process.env.WORK_BLOCKAGE_MAINTENANCE_TABLE_NAME ??
  'dtp-local-work-blockage-maintenance';
export const salt = process.env.SALT_KEY!;
export const longerSessionEndPointList = process.env
  .LONGER_SESSSION_ENDPOINTS ?? ['/v1/sso/web21'];

export const clientId = process.env.CLIENT_ID ?? '';
export const clientSecret = process.env.CLIENT_SECRET ?? '';
export const mobileEncryptSalt = process.env.MOBILE_ENCRYPT_SALT ?? '';
