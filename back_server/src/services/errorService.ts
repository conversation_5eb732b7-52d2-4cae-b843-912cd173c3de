/* eslint-disable no-useless-catch */
import { ScanCommand } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { errorMasterTableName } from '../config/config';
import dynamoDBClient from '../utils/dynamoDB';
import { logger } from '../utils/logger';
import renderError from '../utils/renderError';

/**
 * エラーマスタを取得する
 */
export async function getErrorMaster() {
  const command = new ScanCommand({
    ProjectionExpression: '#ErrorCode, #ErrorMessage, #HasFaq',
    ExpressionAttributeNames: {
      '#ErrorCode': 'errorCode',
      '#ErrorMessage': 'errorMessage',
      '#HasFaq': 'hasFaq'
    },
    TableName: errorMasterTableName
  });

  try {
    const docClient = DynamoDBDocumentClient.from(dynamoDBClient);
    const dynamoDbResponse = (await docClient.send(command)).Items;
    return dynamoDbResponse;
  } catch (error) {
    logger.writeMsg('ICOM000005', [renderError(error as Error)]);
    throw error;
  }
}
