import {
  GetItemCommand,
  GetItemCommandInput,
  GetItemCommandOutput
} from '@aws-sdk/client-dynamodb';
import { workBlockageMaintenanceTableName } from '../config/config';
import { WorkBlockage } from '../models/workBlockage';
import dynamoDBClient from '../utils/dynamoDB';
import { logger } from '../utils/logger';
import renderError from '../utils/renderError';

/**
 * 機能閉塞情報を取得する
 * @param functionId 機能ID
 * @returns 機能IDに紐づく閉塞情報
 */
export async function getWorkBlockage(
  functionId: string
): Promise<WorkBlockage | null> {
  const params: GetItemCommandInput = {
    TableName: workBlockageMaintenanceTableName,
    Key: {
      function_id: { S: functionId }
    }
  };

  try {
    const command = new GetItemCommand(params);
    const result: GetItemCommandOutput = await dynamoDBClient.send(command);
    if (!result.Item) {
      return null;
    }
    const workBlockage: WorkBlockage = {
      functionId: result.Item.function_id.S!,
      functionName: result.Item.function_name.S!,
      status: result.Item.status.S!
    };

    return workBlockage;
  } catch (error) {
    logger.writeMsg('ICOM000005', [renderError(error as Error)]);
    throw error;
  }
}
