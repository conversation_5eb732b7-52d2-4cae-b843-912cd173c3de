/* eslint-disable max-len */
import { v4 as uuidv4 } from 'uuid';
import { UserInfo } from '../models/session';
import { errorCodes } from '../errors/errorCodes';
import { AuthorizationError } from '../errors/httpErrors';
import { ValueDoorUser } from '../types/valueDoorUser';
import { HighRiskUser } from '../models/highRiskUser';
import { Ids } from '../types/ids';
import { expirationTime, jstTime } from '../utils/dateFormat';
import { saveHighRiskUser } from './highRiskUserService';

export async function judgeRisk(
  ids: Ids,
  issueInstant: Date,
  valueDoorUser: ValueDoorUser,
  judgeRiskResult: string,
  loginKind: string
): Promise<void | { highRiskUserId: string }> {
  // 怪しさ判定によるログイン不可
  if (judgeRiskResult === 'H') {
    throw new AuthorizationError(
      errorCodes.JUDGE_RISK_RESPONSE_ERROR,
      '怪しさ判定APIによるエラー'
    );
    // 怪しさ判定によるOTP認証を要求
  } else if (judgeRiskResult === 'M') {
    // VDログインかつVDIDに紐づくメールアドレスがない場合エラーを返す
    if (loginKind === '2' && !valueDoorUser.email) {
      throw new AuthorizationError(
        errorCodes.NOT_EMAIL_WITH_VDID,
        'VDIDに紐づくメールアドレス存在チェックエラー'
      );
    }
    // 高リスクユーザーID払い出し
    const highRiskUserId: string = uuidv4();
    // OTP関連の権限は必要になった際に項目追加
    const userInfoDatafForHighRiskUserData: UserInfo = {
      clientId: valueDoorUser.clientId,
      ninsyoKbn: valueDoorUser.authenticationMethod,
      compId: valueDoorUser.representativeAccount,
      compName: valueDoorUser.companyName,
      userSeiMei: valueDoorUser.userName,
      userTyp: valueDoorUser.userType,
      compAtrbt: valueDoorUser.accessLevel,
      userKn: valueDoorUser.usernameKana,
      Email: valueDoorUser.email,
      telNo: valueDoorUser.telNo,
      kigyoCd: valueDoorUser.companyCode,
      loginType: valueDoorUser.loginType,
      userAuths: valueDoorUser.authorizations,
      keiyakuType: valueDoorUser.contractType
    };

    const highRiskUserData: HighRiskUser = {
      highRiskUserId,
      vdId: ids.valueDoorId,
      dtpId: ids.dtpId,
      userId: ids.userUid,
      issueInstant: issueInstant.toString(),
      userInfo: userInfoDatafForHighRiskUserData,
      state: null,
      freeeOauthState: null,
      expirationTime: expirationTime(),
      createdAt: jstTime(),
      updatedAt: jstTime()
    };

    await saveHighRiskUser(highRiskUserData);

    const result = {
      highRiskUserId
    };

    return result;
    // 妖しさ判定によるログイン可能
  } else {
    // 怪しさ判定結果で問題なければ何もしない
    // eslint-disable-next-line no-useless-return, consistent-return
    return;
  }
}
