import { SendMessageCommand } from '@aws-sdk/client-sqs';
import dotenv from 'dotenv';
import sqsClient from '../utils/sqs';
import { logger } from '../utils/logger';
import { unixTime } from '../utils/dateFormat';
import { ClientActionLog } from '../models/clientActionLog';
import renderError from '../utils/renderError';

dotenv.config();

function validateClientActionLog(clientActionLog: ClientActionLog): void {
  const missingFields: string[] = [];
  if (!clientActionLog.opDate) {
    missingFields.push('操作日時');
  }
  if (!clientActionLog.opChannel) {
    missingFields.push('操作チャネル');
  }
  if (!clientActionLog.ip) {
    missingFields.push('IPアドレス');
  }
  if (missingFields.length > 0) {
    const errorMessage = `${missingFields.join(',')}が存在しません。`;
    throw new Error(errorMessage);
  }
}

function extractUntilFirstComma(ip: string): string {
  const commaIndex = ip.indexOf(',');

  if (commaIndex === -1) {
    return ip;
  }
  return ip.substring(0, commaIndex);
}

export async function saveClientActionLog(
  sessionId: string,
  clientActionLog: ClientActionLog
): Promise<void> {
  try {
    // バリデーションチェック
    validateClientActionLog(clientActionLog);

    // IPアドレスからクライアントIPの抽出
    const extractIp = extractUntilFirstComma(clientActionLog.ip);

    const messageBody = {
      OpDate: clientActionLog.opDate,
      OpChannel: clientActionLog.opChannel,
      IP: extractIp,
      DTPID: clientActionLog.dtpId,
      VDID: clientActionLog.vdId,
      UserName: clientActionLog.userName,
      AccountNumber: clientActionLog.accountNumber,
      Function: clientActionLog.clientFunction,
      Operation: clientActionLog.operation,
      Result: clientActionLog.result,
      ErrorID: clientActionLog.errorId
    };

    const params = {
      QueueUrl: process.env.QUEUE_URL,
      MessageGroupId: sessionId,
      MessageDeduplicationId: `${sessionId}-${unixTime().toString()}`,
      MessageBody: JSON.stringify(messageBody)
    };

    const command = new SendMessageCommand(params);

    await sqsClient.send(command);
  } catch (error) {
    logger.writeMsg('ECOM000004', [
      renderError(error as Error),
      `機能=${clientActionLog.clientFunction!} 操作=${clientActionLog.operation!}`
    ]);
    throw error;
  }
}
