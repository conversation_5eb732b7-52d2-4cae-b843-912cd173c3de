/* eslint-disable max-len */
/* eslint-disable no-useless-catch */
import {
  GetItemCommand,
  GetItemCommandInput,
  GetItemCommandOutput,
  PutItemCommand,
  PutItemCommandInput
} from '@aws-sdk/client-dynamodb';
import dynamoDBClient from '../utils/dynamoDB';
import { highRiskUseTablerName } from '../config/config';
import { HighRiskUser } from '../models/highRiskUser';
import { logger } from '../utils/logger';
import renderError from '../utils/renderError';
import { UserInfo } from '../models/session';
import { getOrDefault } from '../utils/createObject';

/**
 * 高リスクユーザー情報を保存する
 * @param highRiskUser 高リスクユーザー情報
 */
export async function saveHighRiskUser(
  highRiskUser: HighRiskUser
): Promise<void> {
  const params: PutItemCommandInput = {
    TableName: highRiskUseTablerName,
    Item: {
      highRiskUserId: { S: highRiskUser.highRiskUserId },
      vdId: { S: highRiskUser.vdId! },
      dtpId: highRiskUser.dtpId ? { S: highRiskUser.dtpId } : { NULL: true },
      userId: highRiskUser.userId ? { S: highRiskUser.userId } : { NULL: true },
      issueInstant: { S: highRiskUser.issueInstant! },
      userInfo: { S: JSON.stringify(highRiskUser.userInfo) },
      state: highRiskUser.state ? { S: highRiskUser.state } : { NULL: true },
      freeeOauthState: highRiskUser.freeeOauthState
        ? { S: highRiskUser.freeeOauthState }
        : { NULL: true },
      expirationTime: { N: highRiskUser.expirationTime! },
      createdAt: { S: highRiskUser.createdAt! },
      updatedAt: { S: highRiskUser.updatedAt! }
    }
  };

  try {
    const command = new PutItemCommand(params);
    await dynamoDBClient.send(command);
  } catch (error) {
    logger.writeMsg('ICOM000005', [renderError(error as Error)]);
    throw error;
  }
}

/**
 * 高リスクユーザー情報を取得する
 * @param highRiskUserId 高リスクユーザーID
 * @returns 高リスクユーザーIDに紐づく高リスクユーザー情報
 */
export async function getHighRiskUser(
  highRiskUserId: string
): Promise<HighRiskUser | null> {
  const params: GetItemCommandInput = {
    TableName: highRiskUseTablerName,
    Key: {
      highRiskUserId: { S: highRiskUserId }
    }
  };

  try {
    const command = new GetItemCommand(params);
    const result: GetItemCommandOutput = await dynamoDBClient.send(command);

    if (!result.Item) {
      return null;
    }
    const highRiskUser: HighRiskUser = {
      highRiskUserId: result.Item.highRiskUserId.S!,
      vdId: result.Item.vdId.S!,
      dtpId: result.Item.dtpId.S!,
      userId: result.Item.userId.S!,
      issueInstant: result.Item.issueInstant.S!,
      userInfo: {
        clientId: (JSON.parse(result.Item.userInfo.S!) as UserInfo).clientId,
        ninsyoKbn: (JSON.parse(result.Item.userInfo.S!) as UserInfo).ninsyoKbn,
        compId: (JSON.parse(result.Item.userInfo.S!) as UserInfo).compId,
        compName: (JSON.parse(result.Item.userInfo.S!) as UserInfo).compName,
        userSeiMei: (JSON.parse(result.Item.userInfo.S!) as UserInfo)
          .userSeiMei,
        userTyp: (JSON.parse(result.Item.userInfo.S!) as UserInfo).userTyp,
        compAtrbt: (JSON.parse(result.Item.userInfo.S!) as UserInfo).compAtrbt,
        userKn: (JSON.parse(result.Item.userInfo.S!) as UserInfo).userKn,
        Email: (JSON.parse(result.Item.userInfo.S!) as UserInfo).Email,
        telNo: (JSON.parse(result.Item.userInfo.S!) as UserInfo).telNo,
        kigyoCd: (JSON.parse(result.Item.userInfo.S!) as UserInfo).kigyoCd,
        keiyakuType: (JSON.parse(result.Item.userInfo.S!) as UserInfo)
          .keiyakuType,
        userAuths: {
          web21: {
            accountInquiryAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.accountInquiryAuth
            ),
            generalTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.generalTransferAuth
            ),
            salaryTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.salaryTransferAuth
            ),
            localTaxPaymentAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.localTaxPaymentAuth
            ),
            approvalAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.approvalAuth
            ),
            transferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.transferAuth
            ),
            fileSendingAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.fileSendingAuth
            ),
            taxesOrFeesPaymentAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.taxesOrFeesPaymentAuth
            ),
            accountTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.accountTransferAuth
            ),
            transferDestinationCheckAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.transferDestinationCheckAuth
            ),
            dataConversionAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.dataConversionAuth
            ),
            accountInquiryOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.accountInquiryOnSmartPhoneAuth
            ),
            transferOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.transferOnSmartPhoneAuth
            ),
            approvalOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.approvalOnSmartPhoneAuth
            ),
            preSettingAccountInfoOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.preSettingAccountInfoOnSmartPhoneAuth
            ),
            settingSecurityOnSmartPhoneAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.settingSecurityOnSmartPhoneAuth
            ),
            preSettingAccountInfoAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.preSettingAccountInfoAuth
            ),
            settingSecurityAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.web21
                ?.settingSecurityAuth
            )
          },
          gets: {
            inboundTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.inboundTransferAuth
            ),
            lcExportAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.lcExportAuth
            ),
            exportBillHandlingAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.exportBillHandlingAuth
            ),
            outboundTransferAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.outboundTransferAuth
            ),
            lcImportAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.lcImportAuth
            ),
            forexInfoExchangeAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.forexInfoExchangeAuth
            ),
            corpoForexDeliveryAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.corpoForexDeliveryAuth
            ),
            parentChildContractAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.parentChildContractAuth
            ),
            selfSecurityAccessAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.selfSecurityAccessAuth
            ),
            generalSecurityAccessAuth: getOrDefault(
              (JSON.parse(result.Item.userInfo.S!) as UserInfo).userAuths!.gets
                ?.generalSecurityAccessAuth
            )
          }
        },
        loginType: (JSON.parse(result.Item.userInfo.S!) as UserInfo).loginType
        // OTP権限回りの取得は実装時に追加
      },
      state: result.Item.state.S!,
      freeeOauthState: result.Item.freeeOauthState.S!,
      expirationTime: result.Item.expirationTime.N!,
      createdAt: result.Item.createdAt.S!,
      updatedAt: result.Item.updatedAt.S!
    };

    return highRiskUser;
  } catch (error) {
    logger.writeMsg('ICOM000005', [renderError(error as Error)]);
    throw error;
  }
}
