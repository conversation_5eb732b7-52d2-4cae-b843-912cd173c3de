/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * バックエンドからのエラーレスポンスのデータモデル
 */
export interface BackendErrorResponse {
  response: {
    data: {
      code: string;
      message: string;
      httpStatusCode?: number;
      details?: any;
    };
    status: number;
  };
}

/**
 * BFF内部で発生したエラーのデータモデル
 */
export interface BffError {
  statusCode: number;
  errorCode: string;
  errorDetail: Record<string, unknown>;
}

export type ErrorResponse = BackendErrorResponse | BffError | Error;
