/**
 * バックエンドIF_一時保存本人確認データ
 */
export interface IdendificationTemporary {
  role: string;
  dataSource: string;
  name:
    | {
        familyName: string | undefined;
        givenName: string | undefined;
        familyNameKana: string;
        givenNameKana: string;
        familyNameAlphabetic: string;
        givenNameAlphabetic: string;
        hepburnStyle: boolean;
      }
    | undefined;
  position: string | undefined;
  birthDate:
    | {
        year: number | undefined;
        month: number | undefined;
        day: number | undefined;
      }
    | undefined;
  address:
    | {
        postCode: string;
        prefecture: string | undefined;
        city: string | undefined;
        sectionNumberAndBuildingName: string | undefined;
      }
    | undefined;
}

/**
 * バックエンドIF_進行中STEP
 */
export interface ValidationStatus {
  isPriorConfirmationApplicationDataValid: boolean | undefined;
  isIdentificationApplicationDataValid: boolean | undefined;
  isMainApplicationDataValid: boolean | undefined;
  isIdentificationBeneficiaryApplicationDataValid: boolean | undefined;
  isDocumentSubmissionApplicationDataValid: boolean | undefined;
}

/**
 * バックエンドIF_事前確認(手続き者確認)
 */
export interface PreConfirmData {
  applicantRole: string | undefined;
}

/**
 * バックエンドIF_口座開設ステータス
 */
export interface IdentificationData {
  role: string;
  hasFault: boolean;
}

/**
 * バックエンドIF_口座開設ステータスの配列
 */
export interface ScreeningResults {
  identificationData: IdentificationData[];
}

export interface BeneficiaryIdentificationData {
  [key: number]:
    | {
        name: string;
        status: string;
      }
    | undefined;
}
