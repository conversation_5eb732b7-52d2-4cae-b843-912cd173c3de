import { GetsAuthorizationGroup } from '../types/getsAuthorizationGroup';
import { Web21AuthorizationGroup } from '../types/web21AuthorizationGroup';

/**
 * セッションテーブルのデータモデル
 */
export interface Session {
  sessionId: string;
  vdId?: string;
  dtpId?: string;
  userId?: string;
  issueInstant?: string;
  userInfo?: UserInfo;
  state?: string | null;
  freeeOauthState?: string | null;
  expirationTime?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * ユーザー情報
 */
export interface UserInfo {
  /**
   * 認証区分
   */
  ninsyoKbn?: string;
  /**
   * 申込代表口座
   */
  compId?: string;
  /**
   * 取引先を示す一連番号
   */
  clientId?: string;
  /**
   * 取引先名
   */
  compName?: string;
  /**
   * 利用者名
   */
  userSeiMei?: string;
  /**
   * 利用者区分
   */
  userTyp?: string;
  /**
   * 取引先利用者属性
   */
  compAtrbt?: string;
  /**
   * 利用者名カナ
   */
  userKn?: string;
  /**
   * メールアドレス
   */
  Email?: string;
  /**
   * 電話番号
   */
  telNo?: string;
  /**
   * ログインタイプ
   */
  loginType?: string;
  /**
   * OTP通し番号
   */
  otpId?: number;
  /**
   * OTPシリアル番号
   */
  otpSerialNo?: string;
  /**
   * OTP利用区分
   * ValueDoorの返却値は0:無、1:有だが、バックエンドの返却値はbooleanなのでbooleanに合わせる
   */
  otpAplKbn?: boolean;
  /**
   * OTP種類
   */
  otpKind?: string;
  /**
   * OTPデバイス状態
   */
  otpDeviceStatus?: string;
  /**
   * OTP再発行区分
   * ValueDoorの返却値は0:非再発行中、1:再発行中だが、バックエンドの返却値はbooleanなのでbooleanに合わせる
   */
  otpReissueKbn?: boolean;
  /**
   * OTPシリアル番号（再発行）
   */
  otpSerialNoReissue?: string;
  /**
   * OTP種類（再発行）
   */
  otpKindReissue?: string;
  /**
   * OTPデバイス状態(再発行)
   */
  otpDeviceStsReissue?: string;
  /**
   * OTP再発行中動作
   */
  otpReissueAction?: string;
  /**
   * OTP一次要求解除期限
   */
  otpNonOtpExpiredDate?: string;
  /**
   * OTP特殊先判定
   */
  otpServiceCode?: string;
  /**
  /**
   * 企業コード
   */
  kigyoCd?: string;
  /**
   * 利用者権限
   */
  userAuths?: Authorizations;
  /**
   * 契約種別
   */
  keiyakuType?: string;
}

export interface Authorizations {
  web21: Web21AuthorizationGroup | null;
  gets: GetsAuthorizationGroup | null;
}

/**
 * ユーザー情報 + VDID
 */
export interface UserInfoWithIds {
  vdId?: string;
  /**
   * DTPID
   */
  dtpId?: string;
  /**
   * ユーザーID
   */
  userId?: string;
  /**
   * 認証区分
   */
  ninsyoKbn?: string;
  /**
   * 利用者名
   */
  userSeiMei?: string;
  /**
   * 利用者区分
   */
  userTyp?: string;
  /**
   * 取引先名
   */
  compName?: string;
  /**
   * 申込代表口座
   */
  compId?: string;
  /**
   * メールアドレス
   */
  Email?: string;
  /**
   * Web21契約種別
   */
  keiyakuType?: string;
  /**
   * 利用者権限
   */
  userAuths?: string;
}
