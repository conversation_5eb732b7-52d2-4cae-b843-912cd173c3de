import { Router, Request, Response, NextFunction } from 'express';
import classifiedController from '../controllers/classifiedController';
import workBlockageController from '../controllers/workBlockageController';
import { onStart } from '../middlewares/startHandler';
import { onEnd, onSuccess } from '../middlewares/successHandler';
import accountDisplayController from '../controllers/accountDisplayController';
import transactionsController from '../controllers/transactionsController';
import usersController from '../controllers/usersController';
import authController from '../controllers/authController';
import balanceController from '../controllers/balanceController';
import sessionController from '../controllers/sessionController';
import termOfServiceController from '../controllers/termOfServiceController';
import { validate } from '../middlewares/requestValidator';
import {
  accessTokenValidationRules,
  accountDisplayValidationRules,
  balanceValidationRules,
  consentStatusValidationRules,
  freeeTransactionsRules,
  getOpenAccountApplicationScreeningStatusesValidationRules,
  requestEkycUrlValidationRules,
  getWorkBlockageStatusValidationRules,
  identityVerificationValidationRules,
  loginDtpIdValidationRules,
  searchAddressValidationRules,
  transactionsDailyTotalsValidationRules,
  transactionsValidationRules,
  usedTotalAmountValidationRules,
  walletablesValidationRules,
  getOpenAccountApplicationTemporaryValidationRules,
  linkToFreeeValidationRules,
  issueAndLinkDtpIdValidationRules,
  getUuidRules,
  checkEncryptedVdidValidationRules,
  loginSuspiciousDetectionValidationRules,
  decryptWeb21OtpValidationRules
} from '../validators/validationRules';
import freeeLinkController from '../controllers/freeeLinkController';
import freeeWalletablesController from '../controllers/freeeWalletablesController';
import freeeTransactionsController from '../controllers/freeeTransactionsController';
import { authenticateUser } from '../middlewares/authMiddleware';
import { logger, loggerContext } from '../utils/logger';
import clientActionController from '../controllers/clientActionController';
import identityVerificationController from '../controllers/identityVerificationController';
import dtpIdController from '../controllers/dtpIdController';
import web21OtpController from '../controllers/web21OtpController';
import { components } from '../schemas/schema';

const router = Router();

/* eslint-disable @typescript-eslint/ban-types */
function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
// DTPID認証API
router.post(
  '/v1/loginDtpId',
  loginDtpIdValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(dtpIdController.loginDtpId),
  asyncHandler(onEnd)
);

// 怪しさ判定API
router.post(
  '/v1/loginSuspiciousDetection',
  loginSuspiciousDetectionValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(authController.loginSuspiciousDetection),
  asyncHandler(onEnd)
);

// SAML検証API
router.post(
  '/v1/saml/verify',
  asyncHandler(onStart),
  asyncHandler(authController.verifySamlResponse),
  asyncHandler(onEnd)
);

// 口座開設申込情報一時保存確認API
router.get(
  '/v1/identityVerification/openAccountApplication/temporary',
  getOpenAccountApplicationTemporaryValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(
    identityVerificationController.getOpenAccountApplicationTemporary
  ),
  asyncHandler(onEnd)
);

// 口座開設行内審査ステータス確認API
router.get(
  '/v1/identityVerification/openAccountApplication/screeningStatuses',
  getOpenAccountApplicationScreeningStatusesValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(
    identityVerificationController.getOpenAccountApplicationScreeningStatuses
  ),
  asyncHandler(onEnd)
);

// 住所検索API
router.get(
  '/v1/identityVerification/address',
  searchAddressValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.searchAddress),
  asyncHandler(onEnd)
);

// 本人確認情報保存API
router.post(
  '/v1/identityVerification',
  identityVerificationValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.saveIdentityVerification),
  asyncHandler(onEnd)
);

// eKYC URL生成要求API;
router.post(
  '/v1/identityVerification/ekycUrl',
  requestEkycUrlValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.requestEkycUrl),
  asyncHandler(onEnd)
);

// 本人確認情報UUID取得API;
router.get(
  '/v1/identityVerification/uuid',
  getUuidRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.getUuid),
  asyncHandler(onEnd)
);

// 法人Web開局状況確認
router.get(
  '/v1/identityVerification/corporateWebStatus',
  asyncHandler(onStart),
  asyncHandler(identityVerificationController.checkCorporateWebStatus),
  asyncHandler(onEnd)
);

// 機能閉塞設定取得API
router.get(
  '/v1/work/blockage',
  getWorkBlockageStatusValidationRules,
  validate,
  asyncHandler(onStart),
  asyncHandler(workBlockageController.getWorkBlockageStatus),
  asyncHandler(onEnd)
);

// freee連携完了API
router.get(
  '/freee/auth',
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.completeFreeeLink),
  asyncHandler(onEnd)
);

// SALT値取得API
router.get(
  '/v1/classified/salt',
  asyncHandler(onStart),
  asyncHandler(classifiedController.getSaltValue),
  asyncHandler(onEnd)
);

// クライアント情報取得API
router.get(
  '/v1/classified/client',
  asyncHandler(onStart),
  asyncHandler(classifiedController.getClientInfo),
  asyncHandler(onEnd)
);

// セッションID取得API
router.get(
  '/v1/sessionId',
  asyncHandler(onStart),
  asyncHandler(sessionController.getSessionId),
  asyncHandler(onEnd)
);

// freeeリダイレクト先URl取得API
router.get(
  '/v1/freee/redirect',
  asyncHandler(onStart),
  asyncHandler(freeeLinkController.getFreeeRedirectUrl),
  asyncHandler(onEnd)
);

// ユーザー認証ミドルウェアを適用
router.use(asyncHandler(authenticateUser));

// ログ出力設定
router.use((req, res, next) => {
  loggerContext.set('sessionId', req.session ? req.session.sessionId : '');
  loggerContext.set(
    'uuid',
    req.headers ? (req.headers.requestId as string) : ''
  );
  loggerContext.set(
    'ip',
    req.header('X-Forwarded-For') ? req.header('X-Forwarded-For')! : ''
  );
  loggerContext.set('valuedoorId', req.session ? req.session.vdId! : '');
  loggerContext.set('userId', req.session ? req.session.userId! : '');
  // queryにお手続き番号がある場合はqueryより取得してセット
  if (req.query?.referenceNumber) {
    loggerContext.set('referenceNumber', req.query?.referenceNumber.toString());
    // eKYC URL生成要求APIの場合はbodyより取得してセット
  } else if (req.body as components['schemas']['EkycUrlRequest']) {
    const { referenceNumber } =
      req.body as components['schemas']['EkycUrlRequest'];
    loggerContext.set('referenceNumber', referenceNumber);
    // 本人確認情報保存APIの場合はbodyより取得してセット
  } else if (req.body as components['schemas']['IdentityVerification']) {
    const { referenceNumber } =
      req.body as components['schemas']['IdentityVerification'];
    loggerContext.set('referenceNumber', referenceNumber);
    // 上記以外の場合は空文字をセット
  } else {
    loggerContext.set('referenceNumber', '');
  }
  logger.writeMsg('ICOM000001', ['開始']);
  res.on('finish', () => {
    logger.writeMsg('ICOM000001', ['終了']);
  });
  next();
});

// 利用者情報取得API
router.get(
  '/v1/bank/users',
  asyncHandler(usersController.getUsers),
  asyncHandler(onSuccess)
);

// ユーザー情報取得API
router.get(
  '/v1/bank/userInfo',
  asyncHandler(usersController.getUserInfo),
  asyncHandler(onSuccess)
);

// 指定口座残高取得API
router.get(
  '/v1/bank/balance/:accountIds',
  balanceValidationRules,
  validate,
  asyncHandler(balanceController.getBalance),
  asyncHandler(onSuccess)
);

// 口座表示設定取得API
router.get(
  '/v1/accountSettings',
  asyncHandler(accountDisplayController.getAccountsDisplay),
  asyncHandler(onSuccess)
);

// 口座表示設定更新API
router.put(
  '/v1/accountSettings',
  accountDisplayValidationRules,
  validate,
  asyncHandler(accountDisplayController.updateAccountsDisplay),
  asyncHandler(onSuccess)
);

// 入出金明細取得API
router.get(
  '/v1/bank/accounts/:accountId/transactions',
  transactionsValidationRules,
  validate,
  asyncHandler(transactionsController.getTransactions),
  asyncHandler(onSuccess)
);

// 入出金日次合計額取得API
router.get(
  '/v1/bank/accounts/:accountId/transactions/dailyTotals',
  transactionsDailyTotalsValidationRules,
  validate,
  asyncHandler(transactionsController.getTransactionsDailyTotals),
  asyncHandler(onSuccess)
);

// 認可画面呼出情報生成API
router.post(
  '/v1/authScreenInfo/create',
  asyncHandler(authController.createAuthScreenInfo),
  asyncHandler(onSuccess)
);

// トークン要求API
router.post(
  '/v1/token',
  accessTokenValidationRules,
  validate,
  asyncHandler(authController.getAccessToken),
  asyncHandler(onSuccess)
);

// 認可要否確認API
router.get(
  '/v1/authorization/check',
  asyncHandler(authController.checkAuthorization),
  asyncHandler(onSuccess)
);

// Gets権限確認API
router.get(
  '/v1/getsAuthorization/check',
  asyncHandler(authController.checkGetsAuthorization),
  asyncHandler(onSuccess)
);

// Web21SSO用SAML取得
router.get(
  '/v1/sso/:service',
  asyncHandler(authController.getWeb21SsoSaml),
  asyncHandler(onSuccess)
);

// IDaaS・freeeSSO用Cookie取得
router.get(
  '/v1/idaas/sso/cookie',
  asyncHandler(authController.getEncryptedCookieForSso),
  asyncHandler(onSuccess)
);

// セッション情報削除API
router.delete('/v1/session', asyncHandler(sessionController.deleteSession));

// 利用規定同意状況確認API
router.get(
  '/v1/tos/consentStatus/check',
  asyncHandler(termOfServiceController.checkTOSConsentStatus),
  asyncHandler(onSuccess)
);

// 利用規定同意状況登録API
router.post(
  '/v1/tos/consentStatus/register',
  consentStatusValidationRules,
  validate,
  asyncHandler(termOfServiceController.registerTOSConsentStatus),
  asyncHandler(onSuccess)
);

// 顧客操作履歴保存API
// 顧客履歴保存時にはセッションTTLを延長しない
router.post(
  '/v1/clientActionLog',
  asyncHandler(clientActionController.saveClientActionLog),
  asyncHandler(onEnd)
);

// freee連携確認API
router.get(
  '/v1/freee/link',
  asyncHandler(freeeLinkController.checkFreeeLinkStatus),
  asyncHandler(onSuccess)
);

// freee連携API
router.post(
  '/v1/freee/link',
  linkToFreeeValidationRules,
  validate,
  asyncHandler(freeeLinkController.linkToFreee),
  asyncHandler(onSuccess)
);

// freeeSSOパラメーター取得API
router.get(
  '/v1/freee/sso',
  asyncHandler(freeeLinkController.getFreeeSsoParams),
  asyncHandler(onSuccess)
);

// freee再連携画面URL取得API
router.get(
  '/v1/freee/relink',
  asyncHandler(freeeLinkController.getUrlToFreeeReLinkPage),
  asyncHandler(onSuccess)
);

// 取引先ID紐付け確認API
router.get(
  '/v1/freee/links/check/partnerId',
  asyncHandler(freeeLinkController.checkFreeeLinksByClientId),
  asyncHandler(onSuccess)
);

// freee口座一覧取得API
router.get(
  '/v1/freee/walletables',
  walletablesValidationRules,
  validate,
  asyncHandler(freeeWalletablesController.getWalletables),
  asyncHandler(onSuccess)
);

// freee口座明細取得API
router.get(
  '/v1/freee/transactions',
  freeeTransactionsRules,
  validate,
  asyncHandler(freeeTransactionsController.getFreeeTransactions),
  asyncHandler(onSuccess)
);

// freee口座利用合計金額取得API
router.get(
  '/v1/freee/transactions/totalExpense',
  usedTotalAmountValidationRules,
  validate,
  asyncHandler(freeeTransactionsController.getUsedTotalAmount),
  asyncHandler(onSuccess)
);

// DTPID更新API
router.put(
  '/v1/dtpId',
  validate,
  asyncHandler(dtpIdController.updateDtpId),
  asyncHandler(onSuccess)
);

// DTPID紐づけ情報登録API
router.post(
  '/v1/dtpId/link',
  issueAndLinkDtpIdValidationRules,
  validate,
  asyncHandler(dtpIdController.issueAndLinkDtpId),
  asyncHandler(onSuccess)
);

// DTPID紐づけ解除API
router.delete(
  '/v1/dtpId/link',
  asyncHandler(dtpIdController.deleteDtpIdLink),
  asyncHandler(onSuccess)
);

// Web21OTP復号化API
router.post(
  '/v1/web21Otp/decrypt',
  decryptWeb21OtpValidationRules,
  validate,
  asyncHandler(web21OtpController.decryptWeb21Otp),
  asyncHandler(onSuccess)
);

// 暗号化VDID検証API
router.post(
  '/v1/encryptedVdid/check',
  checkEncryptedVdidValidationRules,
  validate,
  asyncHandler(web21OtpController.checkEncryptedVdid),
  asyncHandler(onSuccess)
);

// 暗号化VDID取得API
router.get(
  '/v1/encryptedVdid/encrypt',
  asyncHandler(web21OtpController.getEncryptedVdid),
  asyncHandler(onSuccess)
);

// 認可拒否履歴登録API
router.post(
  '/v1/refusalFlag',
  asyncHandler(authController.registerRefusalFlag),
  asyncHandler(onSuccess)
);

// 認可拒否履歴削除API
router.delete(
  '/v1/refusalFlag',
  asyncHandler(authController.deleteRefusalFlag),
  asyncHandler(onSuccess)
);

export default router;
