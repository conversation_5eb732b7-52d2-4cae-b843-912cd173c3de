/* eslint-disable @typescript-eslint/naming-convention */
import { AxiosRequestConfig } from 'axios';
import express from 'express';
import backendRequest from './backendRequest';

const backendApiHost = process.env.BACKEND_API_HOST;

export function buildBackendUrl(path: string): string {
  return `${backendApiHost!}/${path}`;
}

export const addTokenToConfig = (
  config: AxiosRequestConfig,
  request: express.Request
): AxiosRequestConfig => {
  const { headers, ...rest } = config;
  const params = config.params as Record<string, unknown>;
  return {
    ...rest,
    headers: {
      'x-valuedoor-id': request.session?.vdId ?? '',
      'user-uid': request.session?.userId ?? '',
      sessionId: request.session?.sessionId ?? '',
      requestId: request.headers.requestId ?? '',
      channel: 'MOBILE',
      ip: request.header('X-Forwarded-For')
        ? request.header('X-Forwarded-For')!
        : '',
      'X-Requested-With': 'XMLHttpRequest',
      ...headers
    },
    responseType: 'json',
    params: {
      ...params
    }
  };
};

export function apiClient(
  request: express.Request,
  config: AxiosRequestConfig
): {
  get: <T>(path: string) => Promise<T>;
  post: <T, U>(path: string, payload: T) => Promise<U>;
  update: <T, U>(path: string, payload: T) => Promise<U>;
  remove: (path: string) => Promise<void>;
} {
  const authConfig = addTokenToConfig(config, request);
  return {
    get<T>(path: string): Promise<T> {
      return backendRequest.get(buildBackendUrl(path), authConfig);
    },
    post<T, U>(path: string, payload: T): Promise<U> {
      return backendRequest.post(buildBackendUrl(path), payload, authConfig);
    },
    update<T, U>(path: string, payload: T): Promise<U> {
      return backendRequest.put(buildBackendUrl(path), payload, authConfig);
    },
    remove(path: string): Promise<void> {
      return backendRequest.delete(buildBackendUrl(path), authConfig);
    }
  };
}
