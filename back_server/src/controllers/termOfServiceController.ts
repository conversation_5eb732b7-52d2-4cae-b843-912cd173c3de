import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { components } from '../schemas/schema';
import { apiClient } from '../common/api/baseApi';

const termOfServiceController = {
  checkTOSConsentStatus: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const response = await apiClient(req, {
        responseType: 'json'
      }).get<AxiosResponse<components['schemas']['TosConsentTypes'][]>>(
        'api/common/terms'
      );

      res.status(200).json(response.data);
      next();
    } catch (error) {
      next(error);
    }
  },

  registerTOSConsentStatus: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { agreeTerm, channel } =
        req.body as components['schemas']['TosConsentTypes'];
      const response = await apiClient(req, {
        responseType: 'json'
      }).post<
        { agreeTerm: string; channel: string },
        AxiosResponse<{ message: string }>
      >('api/common/terms', {
        agreeTerm,
        channel
      });

      res.status(200).json(response.data);
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default termOfServiceController;
