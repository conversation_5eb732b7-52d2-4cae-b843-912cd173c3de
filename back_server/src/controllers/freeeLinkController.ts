import dotenv from 'dotenv';
import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { ValidationError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { apiClient } from '../common/api/baseApi';
import { Session } from '../models/session';
import { components } from '../schemas/schema';
import { jstTime } from '../utils/dateFormat';
import { updateFreeeOauthState } from '../services/sessionService';

dotenv.config();

const freeeLinkController = {
  checkFreeeLinkStatus: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const response = await apiClient(req, {}).get<
        AxiosResponse<{ isLinked: boolean }>
      >('api/freee/link');

      res.status(200).json(response.data);
      next();
    } catch (error) {
      next(error);
    }
  },
  linkToFreee: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { code, stateFromFreee } =
        req.body as components['schemas']['FreeeLinkRequest'];
      const { freeeOauthState, userInfo, sessionId } = req.session;
      const clientId = userInfo?.clientId ?? '';

      // clientIdのバリデーションチェック
      if (!clientId) {
        const notFoundError = new ValidationError(
          errorCodes.INVALID_REQUEST,
          `取引先IDがありませんでした。sessionId=${sessionId}`
        );
        return next(notFoundError);
      }

      // freeeOauthStateのバリデーションチェック
      if (!freeeOauthState) {
        const notFoundError = new ValidationError(
          errorCodes.INVALID_REQUEST,
          `freee権限情報がありませんでした。sessionId=${sessionId}`
        );
        return next(notFoundError);
      }

      // バックエンドAPI(linkToFreee)の呼び出し
      await apiClient(req, { responseType: 'json' }).post<
        {
          code: string;
          state: string;
          stateFromFreee: string;
          clientId: string;
        },
        void
      >('api/freee/link', {
        code,
        stateFromFreee,
        state: freeeOauthState,
        clientId
      });

      res
        .status(200)
        .json({ message: 'freeeアクセストークンを保存しました。' });
      return next();
    } catch (error) {
      return next(error);
    }
  },
  getFreeeSsoParams: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { session } = req;
      const clientId = session.userInfo?.clientId ?? '';

      // clientIdのバリデーションチェック
      if (!clientId) {
        const notFoundError = new ValidationError(
          errorCodes.INVALID_REQUEST,
          `取引先IDがありませんでした。sessionId=${session.sessionId}`
        );
        return next(notFoundError);
      }

      // partnerIdを取得
      const responsePartnerId = await apiClient(req, {}).post<
        {
          clientId: string;
        },
        AxiosResponse<{ partnerId: string }>
      >('api/freee/link/partner-id', {
        clientId
      });

      // freee連携済みの企業であるかを確認
      const isAlreadyLinked = await apiClient(req, {}).get<
        AxiosResponse<{ exists: boolean }>
      >(`api/freee/links/${clientId}`);

      const response = await apiClient(req, { params: { from: '02' } }).get<
        AxiosResponse<{ state: string }>
      >('api/freee/link/state');

      const sessionData: Session = {
        sessionId: session.sessionId,
        freeeOauthState: response.data.state,
        updatedAt: jstTime()
      };

      // セッションIDに紐づけてfreeeOauthStateをDynamoDBに保存
      await updateFreeeOauthState(sessionData);

      res.status(200).json({
        state: response.data.state,
        partnerId: responsePartnerId.data.partnerId,
        exists: isAlreadyLinked.data.exists
      });
      return next();
    } catch (error) {
      return next(error);
    }
  },

  getUrlToFreeeReLinkPage: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { session } = req;

      const response = await apiClient(req, { params: { from: '02' } }).get<
        AxiosResponse<{ state: string }>
      >('api/freee/link/state');

      const sessionData: Session = {
        sessionId: session.sessionId,
        freeeOauthState: response.data.state,
        updatedAt: jstTime()
      };

      // セッションIDに紐づけてfreeeOauthStateをDynamoDBに保存
      await updateFreeeOauthState(sessionData);

      // frontに返却するfreee再連携用URLの作成
      const authorizeParams = new URLSearchParams({
        client_id: process.env.FREEE_CLIENT_ID!,
        prompt: 'select_company',
        redirect_uri: process.env.FREEE_REDIRECT_URI!,
        response_type: 'code',
        state: response.data.state
      }).toString();
      const params = new URLSearchParams({
        provider: 'smbc',
        redirect_service_name: 'accounts',
        redirect_end_point: `public_api/authorize?${authorizeParams}`,
        back_url: process.env.FREEE_BACK_URL!
      }).toString();

      res.status(200).json({
        redirectUrl: `${process.env.FREEE_RELINK_BASE_URL!}?${params}`
      });
      next();
    } catch (error) {
      next(error);
    }
  },

  checkFreeeLinksByClientId: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { session } = req;
      const clientId = session.userInfo?.clientId ?? '';

      // clientIdのバリデーションチェック
      if (!clientId) {
        const notFoundError = new ValidationError(
          errorCodes.INVALID_REQUEST,
          `取引先IDがありませんでした。sessionId=${session.sessionId}`
        );
        return next(notFoundError);
      }

      const response = await apiClient(req, {}).get<
        AxiosResponse<{ exists: boolean }>
      >(`api/freee/links/${clientId}`);

      res.status(200).json({
        exists: response.data.exists
      });
      return next();
    } catch (error) {
      return next(error);
    }
  },

  completeFreeeLink: (req: Request, res: Response, next: NextFunction) => {
    res.status(200).json({ message: 'OK' });
    next();
  },

  getFreeeRedirectUrl: (req: Request, res: Response, next: NextFunction) => {
    const errorPageUrl = process.env.FREEE_RIDIRECT_ERROR_PAGE!;
    try {
      let url: string = errorPageUrl;
      const loginType = req.query.loginType as string;
      const ticket = req.query.ticket as string;
      const state = req.query.state as string;
      const partnerId = req.query.partnerId as string;
      const freeeDomain = process.env.FREEE_BASE_DOMAIN!;

      res.cookie('ticket', ticket, {
        domain: process.env.IDAAS_COOKIE_DOMAIN!,
        path: '/',
        secure: true,
        httpOnly: true
      });

      const freeeInfoParams = new URLSearchParams({
        state,
        partner_id: partnerId
      }).toString();
      const redirectEndPoint = `smbc/terms?${freeeInfoParams}`;
      const redirectEndPointForEmployee = `smbc/terms`;

      // 処理完了後のリダイレクト
      // 代表者初回ログイン
      if (loginType === 'firstLogin') {
        const authorizeParams = new URLSearchParams({
          provider: 'smbc',
          redirect_service_name: 'calc',
          redirect_end_point: redirectEndPoint,
          back_url: `https://cashflow.${freeeDomain}/error/smbc_error?${freeeInfoParams}`
        }).toString();
        url = `https://accounts.${freeeDomain}/gateway/external_auth?${authorizeParams}`;
      }

      // 従業員初回ログイン
      if (loginType === 'firstEmployeeLogin') {
        const authorizeParams = new URLSearchParams({
          provider: 'smbc',
          redirect_service_name: 'calc',
          redirect_end_point: redirectEndPointForEmployee,
          redirect_query: freeeInfoParams,
          back_url: `https://cashflow.${freeeDomain}/error/smbc_error?${freeeInfoParams}&only_login=1`
        }).toString();
        url = `https://accounts.${freeeDomain}/gateway/external_auth?${authorizeParams}`;
      }

      // 他行口座追加(他行口座連携期限切れ)
      if (loginType === 'addCreditCardLogin') {
        const authorizeParams = new URLSearchParams({
          provider: 'smbc',
          redirect_service_name: 'accounting',
          redirect_end_point: 'orientation/search_bank#credit_card',
          back_url: `https://cashflow.${freeeDomain}/error/smbc_error`
        }).toString();
        url = `https://accounts.${freeeDomain}/gateway/external_auth?${authorizeParams}`;
      }

      // クレジットカード追加(クレジットカード連携期限切れ)
      if (loginType === 'addBankAccountLogin') {
        const authorizeParams = new URLSearchParams({
          provider: 'smbc',
          redirect_service_name: 'accounting',
          redirect_end_point: 'orientation/search_bank#bank',
          back_url: `https://cashflow.${freeeDomain}/error/smbc_error`
        }).toString();
        url = `https://accounts.${freeeDomain}/gateway/external_auth?${authorizeParams}`;
      }

      // メンバー追加
      if (loginType === 'addMembersLogin') {
        const authorizeParams = new URLSearchParams({
          provider: 'smbc',
          redirect_service_name: 'accounts',
          redirect_end_point: 'products/accounting/members',
          back_url: `https://cashflow.${freeeDomain}/error/smbc_error`
        }).toString();
        url = `https://accounts.${freeeDomain}/gateway/external_auth?${authorizeParams}`;
      }

      // 金融機関連携期限切れ
      if (loginType === 'expirationOfFinanceLinkage') {
        const authorizeParams = new URLSearchParams({
          provider: 'smbc',
          redirect_service_name: 'accounting',
          redirect_end_point: 'walletables',
          back_url: `https://cashflow.${freeeDomain}/error/smbc_error`
        }).toString();
        url = `https://accounts.${freeeDomain}/gateway/external_auth?${authorizeParams}`;
      }

      res.redirect(url);
      next();
    } catch (error) {
      // 異常系発生時にはエラーHTMLに遷移
      res.redirect(errorPageUrl);
      next(error);
    }
  }
};

export default freeeLinkController;
