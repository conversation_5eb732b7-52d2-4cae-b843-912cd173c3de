/* eslint-disable max-len */
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AxiosResponse } from 'axios';
import { createHash } from 'crypto';
import { checkErrorMessageId } from '../utils/checkErrorMessageId';
import { judgeRisk } from '../services/judgeRiskService';
import { AuthorizationError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { components } from '../schemas/schema';
import { saveSession, updateState } from '../services/sessionService';
import { getIdaasEncryptedCookieValue } from '../utils/getIdaasEncryptedCookieValue';
import { Session, UserInfo } from '../models/session';
import { jstTime, expirationTime, yyyymmddhhmmss } from '../utils/dateFormat';
import { apiClient } from '../common/api/baseApi';
import { Ids } from '../types/ids';
import { ValueDoorUser } from '../types/valueDoorUser';
import { logger } from '../utils/logger';
import renderError from '../utils/renderError';
import { salt } from '../config/config';
import { errorCodeMapping } from '../middlewares/errorHandler';

// 認可状態確認
async function authorizationStatusCheck(req: Request) {
  let authorizationStatus;
  const authorizationStatusResponse = await apiClient(req, {
    responseType: 'json'
  }).get<AxiosResponse<{ hasToken: boolean }>>('api/bank/auth/refresh-token');

  if (authorizationStatusResponse.data.hasToken) {
    // 有効期限内のリフレッシュトークンが存在する場合、認可ステータス「02:認可済」を設定
    authorizationStatus = '02';
  } else {
    // リフレッシュトークンが存在しない場合、認可ステータス「01:要認可」を設定
    authorizationStatus = '01';
  }
  return authorizationStatus;
}

const authController = {
  verifySamlResponse: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    let response;
    let encryptedCookie;
    // 初期値はL(低リスク)
    let judgeRiskResponse = 'L';
    const { samlResponse, caulisSessionId } =
      req.body as components['schemas']['SamlResponse'];
    try {
      // SAML検証リクエスト
      response = await apiClient(req, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).post<
        { samlPayload: string },
        AxiosResponse<{
          ids: Ids;
          issueInstant: Date;
          valueDoorUser: ValueDoorUser;
        }>
      >('api/user/auth/vdid-login', {
        samlPayload: samlResponse
      });

      const isDtpIdLinked = response.data.ids.dtpId;

      // 暗号化クッキー生成
      const userId = isDtpIdLinked
        ? response.data.ids.userUid ?? '' // dtpId紐付け有りの場合、userUidを使用
        : response.data.ids.valueDoorId; // dtpId紐付け無しの場合、valueDoorIdを使用

      const emailAddress = isDtpIdLinked // dtpId紐付け有りの場合、dtpIdを使用
        ? response.data.ids.dtpId
        : response.data.valueDoorUser.email; // dtpId紐付け無しの場合、VDIDに紐づくメールアドレスを使用

      encryptedCookie = getIdaasEncryptedCookieValue(
        userId,
        emailAddress ?? ''
      );
    } catch (error) {
      // saml検証APIのエラーを返却
      // VDIDログイン失敗時の怪しさ判定リクエストはフロント側から実行
      return next(error);
    }
    try {
      // ログイン成功時の怪しさ判定API呼び出し (バックエンドのAPIが未実装のためコメントアウト)
      const userHash = createHash('sha256')
        .update(response.data.ids.valueDoorId + salt)
        .digest('hex');
      const userAgent = req.header('user-agent')!;
      const userHashOfNewAccountCreation =
        response.data.ids.dtpId !== undefined
          ? createHash('sha256')
              .update(response.data.ids.dtpId + salt)
              .digest('hex')
          : '';

      const relativeSuspiciousValue = await apiClient(req, {
        headers: {
          caulisSessionId,
          userHash,
          userAgent,
          userHashOfNewAccountCreation
        }
      }).post<
        {
          isLoginSucceed: boolean;
          loginKind: string;
        },
        AxiosResponse<{ relativeSuspiciousValue: string }>
      >('api/caulis/login-suspicious-detection', {
        isLoginSucceed: true,
        loginKind: '2'
      });

      judgeRiskResponse = relativeSuspiciousValue.data.relativeSuspiciousValue;
    } catch (error) {
      // ログイン成功時の怪しさ判定リクエストでのエラーはログ出力のみ
      logger.writeMsg('ICOM000005', [
        'ログイン成功時の怪しさ判定リクエストでのエラー',
        renderError(error as Error)
      ]);
    }
    try {
      // 危険度判定
      const judgeRiskResult = await judgeRisk(
        response.data.ids,
        response.data.issueInstant,
        response.data.valueDoorUser,
        judgeRiskResponse,
        '2'
      );
      if (judgeRiskResult) {
        const { highRiskUserId } = judgeRiskResult;
        res.status(200).json({ highRiskUserId, encryptedCookie });
        return next();
      }

      // セッションID払い出し
      const sessionId: string = uuidv4();

      const userInfoData: UserInfo = {
        ninsyoKbn: response.data.valueDoorUser.authenticationMethod,
        compId: response.data.valueDoorUser.representativeAccount,
        compName: response.data.valueDoorUser.companyName,
        clientId: response.data.valueDoorUser.clientId,
        userSeiMei: response.data.valueDoorUser.userName,
        userTyp: response.data.valueDoorUser.userType,
        compAtrbt: response.data.valueDoorUser.accessLevel,
        userKn: response.data.valueDoorUser.usernameKana,
        Email: response.data.valueDoorUser.email,
        keiyakuType: response.data.valueDoorUser.contractType,
        telNo: response.data.valueDoorUser.telNo,
        kigyoCd: response.data.valueDoorUser.companyCode,
        loginType: response.data.valueDoorUser.loginType,
        userAuths: response.data.valueDoorUser.authorizations,
        otpId: Number(response.data.valueDoorUser.otpId),
        otpSerialNo: response.data.valueDoorUser.otpSerialNo,
        // ValueDoorの返却値は0:無、1:有だが、バックエンドの返却値はbooleanなのでbooleanに合わせる
        otpAplKbn: response.data.valueDoorUser.usesOtp,
        otpKind: response.data.valueDoorUser.otpKind,
        otpDeviceStatus: response.data.valueDoorUser.otpDeviceStatus,
        // ValueDoorの返却値は0:非再発行中、1:再発行中だが、バックエンドの返却値はbooleanなのでbooleanに合わせる
        otpReissueKbn: response.data.valueDoorUser.isOtpReissuing,
        otpSerialNoReissue: response.data.valueDoorUser.otpSerialNoReissue,
        otpKindReissue: response.data.valueDoorUser.otpKindReissue,
        otpDeviceStsReissue: response.data.valueDoorUser.otpDeviceStatusReissue,
        otpReissueAction: response.data.valueDoorUser.otpReissueAction,
        otpNonOtpExpiredDate: response.data.valueDoorUser.otpNonOtpExpiredDate,
        otpServiceCode: response.data.valueDoorUser.otpServiceCode
      };

      const sessionData: Session = {
        sessionId,
        vdId: response.data.ids.valueDoorId,
        dtpId: response.data.ids.dtpId,
        userId: response.data.ids.userUid,
        issueInstant: response.data.issueInstant.toString(),
        userInfo: userInfoData,
        state: null,
        freeeOauthState: null,
        expirationTime: expirationTime(),
        createdAt: jstTime(),
        updatedAt: jstTime()
      };

      // セッションIDをDynamoDBに保存
      await saveSession(sessionData);

      res.status(200).json({ sessionId, encryptedCookie });
      return next();
    } catch (error) {
      return next(error);
    }
  },

  createAuthScreenInfo: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { session } = req;
      // バックエンド側のリクエストの形式にフォーマット
      const formattedIssueInstant = yyyymmddhhmmss(
        new Date(session.issueInstant as string)
      );

      // バックエンド呼び出しのためのリクエスト作成
      const { vdId } = session;
      const {
        compId,
        ninsyoKbn,
        compName,
        userSeiMei,
        userAuths,
        compAtrbt,
        userKn,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        Email,
        telNo,
        kigyoCd
      } = req.session.userInfo!;

      // バックエンドAPI呼び出し
      const response = await apiClient(req, {
        params: { from: '02' } // Mobileからのリクエストなので02を設定
      }).post<
        {
          id: string;
          issueInstant: string;
          representativeAccount: string;
          authenticationMethod: string;
          companyName: string;
          userName: string;
          accessLevel: string;
          usernameKana: string;
          email?: string;
          telNo?: string;
          companyCode: string;
          web21Authorizations: {
            accountInquiryAuth: boolean;
            generalTransferAuth: boolean;
            salaryTransferAuth: boolean;
            localTaxPaymentAuth: boolean;
            approvalAuth: boolean;
            transferAuth: boolean;
            fileSendingAuth: boolean;
            taxesOrFeesPaymentAuth: boolean;
            accountTransferAuth: boolean;
            transferDestinationCheckAuth: boolean;
            dataConversionAuth: boolean;
            accountInquiryOnSmartPhoneAuth: boolean;
            transferOnSmartPhoneAuth: boolean;
            approvalOnSmartPhoneAuth: boolean;
            preSettingAccountInfoOnSmartPhoneAuth: boolean;
            settingSecurityOnSmartPhoneAuth: boolean;
            preSettingAccountInfoAuth: boolean;
            settingSecurityAuth: boolean;
          } | null;
        },
        AxiosResponse<{ state: string; fromEbizParam: string }>
      >('api/bank/auth/generate-anserBizsol-authorization-parameter', {
        id: vdId!,
        issueInstant: formattedIssueInstant,
        representativeAccount: compId!,
        authenticationMethod: ninsyoKbn!,
        companyName: compName!,
        userName: userSeiMei!,
        web21Authorizations: userAuths!.web21!,
        accessLevel: compAtrbt!,
        usernameKana: userKn!,
        email: Email,
        telNo,
        companyCode: kigyoCd!
      });

      const sessionData: Session = {
        sessionId: session.sessionId,
        state: response.data.state,
        updatedAt: jstTime()
      };

      // セッションIDに紐づけてstateをDynamoDBに保存
      await updateState(sessionData);

      res.status(200).json({ fromEbizParam: response.data.fromEbizParam });
      next();
    } catch (error) {
      next(error);
    }
  },

  getAccessToken: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { session } = req;

      // バックエンドAPI呼び出し
      await apiClient(req, {
        responseType: 'json'
      }).post<
        {
          stateInSession: string | null | undefined;
          stateInQueryParam: string;
          code: string;
          userInfo: UserInfo | undefined;
        },
        void
      >('api/bank/auth/access-token', {
        stateInSession: session?.state,
        stateInQueryParam: (req.query.state as string).replaceAll(' ', ''),
        code: req.query.code as string,
        userInfo: session?.userInfo
      });

      res.status(200).json({ message: 'アクセストークンを取得しました。' });
      next();
    } catch (error) {
      next(error);
    }
  },

  checkAuthorization: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { kigyoCd, userAuths } = req.session.userInfo!;
      const web21Auths = userAuths?.web21;
      const hasInquiryAuth: boolean =
        web21Auths?.accountInquiryAuth === true &&
        web21Auths?.accountInquiryOnSmartPhoneAuth === true;
      const hasTransferAuth: boolean =
        web21Auths?.transferAuth === true &&
        web21Auths?.transferOnSmartPhoneAuth === true;
      const hasApprovalAuth: boolean =
        web21Auths?.approvalAuth === true &&
        web21Auths?.approvalOnSmartPhoneAuth === true;
      let hasNoAuth = true;

      // 保有権限なし確認
      if (
        web21Auths?.accountInquiryAuth ||
        web21Auths?.generalTransferAuth ||
        web21Auths?.salaryTransferAuth ||
        web21Auths?.localTaxPaymentAuth ||
        web21Auths?.approvalAuth ||
        web21Auths?.transferAuth ||
        web21Auths?.fileSendingAuth ||
        web21Auths?.taxesOrFeesPaymentAuth ||
        web21Auths?.accountTransferAuth ||
        web21Auths?.transferDestinationCheckAuth ||
        web21Auths?.dataConversionAuth ||
        web21Auths?.accountInquiryOnSmartPhoneAuth ||
        web21Auths?.transferOnSmartPhoneAuth ||
        web21Auths?.approvalOnSmartPhoneAuth ||
        web21Auths?.preSettingAccountInfoOnSmartPhoneAuth ||
        web21Auths?.settingSecurityOnSmartPhoneAuth ||
        web21Auths?.preSettingAccountInfoAuth ||
        web21Auths?.settingSecurityAuth
      ) {
        hasNoAuth = false;
      }

      const response = await apiClient(req, {
        responseType: 'json'
      }).get<AxiosResponse<{ existsRefusalFlag: boolean }>>(
        'api/bank/refusal-flag'
      );

      // 要再連携エラー
      // Web21の再連携が必要な際に返却し、FrontのHOME画面に再連携ボタンを表示させる
      const authorizationError = new AuthorizationError(
        errorCodes.AUTHORIZATION_DENIAL_HISTORY,
        '口座情報へのアクセスが許可されていません。口座残高を表示するにはアクセス許可の設定をしてください。'
      );

      if (response.data.existsRefusalFlag) {
        return next(authorizationError);
      }

      // バックエンドに認可状態を確認
      // エラー発生時にはFrontにて再認可を実施させる
      let authorizationStatus;
      try {
        authorizationStatus = await authorizationStatusCheck(req);
      } catch (error) {
        return next(authorizationError);
      }

      const result = {
        hasKigyoCd: true,
        authorizationStatus,
        hasNoAuth,
        hasInquiryAuth,
        hasTransferAuth,
        hasApprovalAuth
      } as components['schemas']['AuthorizationStatus'];

      // 企業コードチェック
      if (!(kigyoCd ? /\d{14}/.exec(kigyoCd) : undefined)) {
        const errors = await errorCodeMapping(errorCodes.ACCOUNT_NOT_KIGYO_CD);

        const kigyoCodeError = {
          hasKigyoCd: false,
          errorCode: errors.errorCode,
          errorMessage: errors.errorMessage,
          hasFaq: errors.hasFaq
        };

        const resultMerge = {
          ...result,
          ...kigyoCodeError
        } as components['schemas']['AuthorizationStatus'];

        res.status(200).json(resultMerge);
        return next();
      }

      // 認可ステータスを返却
      res.status(200).json(result);
      return next();
    } catch (error) {
      return next(error);
    }
  },

  checkGetsAuthorization: (req: Request, res: Response, next: NextFunction) => {
    try {
      const userAuths = req.session.userInfo?.userAuths;
      // userAuthsがnullの場合、getsAuthStatusを'0'として扱う
      let getsAuthStatus = '0';

      // Getsの利用者権限有無チェックいずれか一つでもtrueであれば1を返却
      if (userAuths?.gets) {
        // Getsの利用者権限の被仕向送金がtrueであるかをチェック
        if (
          userAuths.gets.inboundTransferAuth ||
          // Getsの利用者権限の仕向送金がtrueであるかをチェック
          userAuths.gets.outboundTransferAuth ||
          // Getsの利用者権限のセキュリティ管理（本人のユーザ権限設定）がtrueであるかをチェック
          userAuths.gets.selfSecurityAccessAuth ||
          // Getsの利用者権限のセキュリティ管理がtrueであるかをチェック
          userAuths.gets.generalSecurityAccessAuth
        ) {
          getsAuthStatus = '1';
        }
      }

      res.status(200).json({ getsAuthStatus });
      return next();
    } catch (error) {
      return next(error);
    }
  },

  loginSuspiciousDetection: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { caulisSessionId, vdId, errorMessageId } =
        req.body as components['schemas']['LoginSuspiciousDetectionRequest'];
      const userHash = createHash('sha256')
        .update(vdId + salt)
        .digest('hex');
      const userAgent = req.header('user-agent')!;
      const userHashOfNewAccountCreation = '';

      await apiClient(req, {
        headers: {
          caulisSessionId,
          userHash,
          userHashOfNewAccountCreation,
          userAgent
        }
      }).post<
        {
          isLoginSucceed: boolean;
          loginErrorCode: string;
          loginKind: string;
        },
        AxiosResponse<{ relativeSuspiciousValue: string }>
      >('api/caulis/login-suspicious-detection', {
        isLoginSucceed: checkErrorMessageId(errorMessageId),
        loginErrorCode: errorMessageId,
        loginKind: '2'
      });

      res.status(200).json({
        message: 'ValueDoorログイン失敗時の怪しさ判定リクエストを実行しました。'
      });
      next();
    } catch (error) {
      next(error);
    }
  },

  getWeb21SsoSaml: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // セッションからuserIdとuserInfoを取得
      const { userInfo } = req.session;

      // 遷移先
      const { service } = req.params;

      // バックエンドAPI（valueDoorSingleSignOnController）の呼び出し
      // loginType：VDIDログインの場合はVD認証APIの返却値、DTPIDログインの場合は空
      // loginMethod：S（スマホアプリ）固定
      const response = await apiClient(req, {}).post<
        {
          ninsyoKbn: string;
          loginType: string;
          loginMethod: string;
        },
        AxiosResponse<{ toVdParam: string }>
      >(`api/common/saml/${encodeURIComponent(service)}`, {
        ninsyoKbn: userInfo?.ninsyoKbn ?? '',
        loginType: userInfo?.loginType ?? '',
        loginMethod: 'S'
      });

      res.status(200).json({ toWeb21Param: response.data.toVdParam });
      next();
    } catch (error) {
      next(error);
    }
  },

  // 暗号化Cookieは一度限り有効であるため、クライアントにて都度最新のものを取得するための関数
  getEncryptedCookieForSso: (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { session } = req;
      // 暗号化クッキー生成
      const isDtpIdLinked = session.dtpId;
      const userId = isDtpIdLinked
        ? session.userId // dtpId紐付け有りの場合、userUidを使用
        : session.vdId; // dtpId紐付け無しの場合、valueDoorIdを使用

      const emailAddress = isDtpIdLinked // dtpId紐付け有りの場合、dtpIdを使用
        ? session.dtpId
        : session.userInfo?.Email; // dtpId紐付け無しの場合、VDIDに紐づくメールアドレスを使用

      const encryptedCookie = getIdaasEncryptedCookieValue(
        userId ?? '',
        emailAddress ?? ''
      );
      res.status(200).json({ encryptedCookie });
      next();
    } catch (error) {
      next(error);
    }
  },

  registerRefusalFlag: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      await apiClient(req, {
        responseType: 'json'
      }).post<object, AxiosResponse<{ message: string }>>(
        `api/bank/refusal-flag`,
        {}
      );

      res.status(200).json({ message: '認可拒否履歴を登録しました。' });
      next();
    } catch (error) {
      next(error);
    }
  },

  deleteRefusalFlag: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      await apiClient(req, {}).remove(`api/bank/refusal-flag`);

      res.status(200).json({ message: '認可拒否履歴を削除しました。' });
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default authController;
