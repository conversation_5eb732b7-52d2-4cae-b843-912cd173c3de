import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { deleteSession, saveSession } from '../services/sessionService';
import { getHighRiskUser } from '../services/highRiskUserService';
import {
  AuthorizationError,
  BadRequestError,
  NotFoundError
} from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import { expirationTime, jstTime, unixTime } from '../utils/dateFormat';
import { Session } from '../models/session';
import { checkOtpAuthenticationResult } from '../utils/checkOtpAuthenticationResult';

const sessionController = {
  deleteSession: async (req: Request, res: Response, next: NextFunction) => {
    try {
      await deleteSession(req.session.sessionId);

      res.status(200).json({ message: 'セッション情報を削除しました。' });
    } catch (error) {
      next(error);
    }
  },
  getSessionId: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 高リスクユーザーID取り出し
      const authHeader = req.headers?.authorization;
      const ticket = req.query.ticket as string;
      const idaasTicket = req.query.idaasTicket as string;

      if (!authHeader?.startsWith('Bearer ')) {
        const badRequestError = new BadRequestError(
          errorCodes.AUTH_HEADER_EMPTY_ERROR,
          `authHeaderに値が存在しません。 authHeader=${authHeader as string}`
        );
        return next(badRequestError);
      }

      // 高リスクユーザーデータ取得
      const highRiskUserId = authHeader.split(' ')[1];
      const highRiskUser = await getHighRiskUser(highRiskUserId);

      if (!highRiskUser) {
        const notFoundError = new NotFoundError(
          errorCodes.NOT_HIGH_RISK_USER,
          `高リスクユーザーIDに紐づく高リスクユーザー情報がありませんでした。highRiskUserId=${highRiskUserId}`
        );
        return next(notFoundError);
      }

      if (
        !highRiskUser.expirationTime ||
        Number(highRiskUser.expirationTime) < unixTime()
      ) {
        const authorizationError = new AuthorizationError(
          errorCodes.EXPIRED_HIGH_RISK_USER,
          `高リスクユーザーの有効期限が切れています。highRiskUserId=${highRiskUserId}`
        );
        return next(authorizationError);
      }

      // 高リスクユーザーIDに紐づくメールアドレス
      const emailAddress = highRiskUser.dtpId
        ? highRiskUser.dtpId // dtpId紐付け有りの場合、dtpIdを使用
        : highRiskUser.userInfo?.Email; // dtpId紐付け無しの場合、VDIDに紐づくメールアドレスを使用

      // OTP認証結果検証
      const otpAuthenticationResult = checkOtpAuthenticationResult(
        emailAddress!,
        ticket,
        idaasTicket
      );

      if (!otpAuthenticationResult) {
        // OTP認証結果検証によるログイン不可
        throw new AuthorizationError(
          errorCodes.JUDGE_RISK_RESPONSE_ERROR,
          'セッションID取得APIによるエラー'
        );
      }

      // セッションID払い出し
      const sessionId: string = uuidv4();

      const sessionData: Session = {
        sessionId,
        vdId: highRiskUser.vdId,
        dtpId: highRiskUser.dtpId,
        userId: highRiskUser.userId,
        issueInstant: highRiskUser.issueInstant,
        userInfo: highRiskUser.userInfo,
        state: highRiskUser.state,
        freeeOauthState: highRiskUser.freeeOauthState,
        expirationTime: expirationTime(),
        createdAt: jstTime(),
        updatedAt: jstTime()
      };

      await saveSession(sessionData);
      res.status(200).json({ sessionId });
      return next();
    } catch (error) {
      return next(error);
    }
  }
};

export default sessionController;
