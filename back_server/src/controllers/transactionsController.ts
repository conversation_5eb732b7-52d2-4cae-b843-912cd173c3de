import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { components } from '../schemas/schema';
import { options, nowDate } from '../utils/dateFormat';
import { apiClient } from '../common/api/baseApi';

type StatementSummaryResponse = {
  summary: {
    date: string;
    withdrawal: number;
    deposit: number;
  }[];
};

function convertBackendData(input: StatementSummaryResponse) {
  const output: components['schemas']['TransactionsDailyTotals'] = {
    transactionsDailyTotals: input.summary.map((item) => ({
      date: item.date,
      totalDeposit: item.deposit,
      totalWithdrawal: item.withdrawal
    }))
  };
  return output;
}

const transactionsController = {
  getTransactions: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { accountId } = req.params;

      const response = await apiClient(req, {
        params: {
          from: req.query.dateFrom,
          to: req.query.dateTo
        }
      }).get<AxiosResponse<components['schemas']['Transactions']>>(
        `api/bank/statement/${accountId}`
      );

      const createTransactionsDetail = () => {
        const transactionsDetail: components['schemas']['TransactionsDetail'][] =
          [];

        response.data.transactions1?.forEach((value) => {
          const createTransactionsDetailData: components['schemas']['TransactionsDetail'] =
            {
              inquiryNumber: value.inquiryNumber,
              transactionDateJapaneseCalendar:
                value.transactionDateJapaneseCalendar,
              transactionDateAd:
                // eslint-disable-next-line operator-linebreak
                value.transactionDateAd &&
                new Date(value.transactionDateAd).toLocaleDateString('ja-JP'),
              valueDateJapaneseCalendar: value.valueDateJapaneseCalendar,
              valueDateAd:
                value.valueDateAd &&
                new Date(value.valueDateAd).toLocaleDateString('ja-JP'),
              depositCreditType: value.depositCreditType,
              transactionType: value.transactionType,
              transactionTypeName: value.transactionTypeName,
              amount: value.amount,
              checksIssuedByOtherBanksAmount:
                value.checksIssuedByOtherBanksAmount,
              exchangePresentationDateJapaneseCalendar:
                value.exchangePresentationDateJapaneseCalendar,
              exchangePresentationDateAd: value.exchangePresentationDateAd,
              dishonoredReturnDateJapaneseCalendar:
                value.dishonoredReturnDateJapaneseCalendar,
              dishonoredReturnDateAd: value.dishonoredReturnDateAd,
              billAndCheckType: value.billAndCheckType,
              billAndCheckTypeName: value.billAndCheckTypeName,
              billAndCheckNumber: value.billAndCheckNumber,
              branchCode: value.branchCode,
              remitterCode: value.remitterCode,
              remitterNameContractorNumber: value.remitterNameContractorNumber,
              remittingBankName: value.remittingBankName,
              remittingBankBranchName: value.remittingBankBranchName,
              abstract: value.abstract,
              ediInfo: value.ediInfo,
              ediKey: value.ediKey
            };
          transactionsDetail.push(createTransactionsDetailData);
        });
        return transactionsDetail;
      };

      const createTransactionsDetailSmbc = () => {
        const transactionsDetailSmbc: components['schemas']['TransactionsDetailSmbc'][] =
          [];

        response.data.transactions2?.forEach((value) => {
          // eslint-disable-next-line operator-linebreak
          const createTransactionsDetailSmbcData: components['schemas']['TransactionsDetailSmbc'] =
            {
              identificationNumber: value.identificationNumber,
              transactionDateJapaneseCalendar:
                value.transactionDateJapaneseCalendar,
              transactionDateAd:
                // eslint-disable-next-line operator-linebreak
                value.transactionDateAd &&
                new Date(value.transactionDateAd).toLocaleDateString('ja-JP'),
              valueDateJapaneseCalendar: value.valueDateJapaneseCalendar,
              valueDateAd:
                value.valueDateAd &&
                new Date(value.valueDateAd).toLocaleDateString('ja-JP'),
              depositCreditType: value.depositCreditType,
              transactionType: value.transactionType,
              transactionTypeName: value.transactionTypeName,
              amount: value.amount,
              checksIssuedByOtherBanksAmount:
                value.checksIssuedByOtherBanksAmount,
              exchangePresentationDateJapaneseCalendar:
                value.exchangePresentationDateJapaneseCalendar,
              exchangePresentationDateAd: value.exchangePresentationDateAd,
              dishonoredReturnDateJapaneseCalendar:
                value.dishonoredReturnDateJapaneseCalendar,
              dishonoredReturnDateAd: value.dishonoredReturnDateAd,
              billAndCheckType: value.billAndCheckType,
              billAndCheckTypeName: value.billAndCheckTypeName,
              billAndCheckNumber: value.billAndCheckNumber,
              branchCode: value.branchCode,
              originalDepositDateJapaneseCalendar:
                value.originalDepositDateJapaneseCalendar,
              originalDepositDateAd: value.originalDepositDateAd,
              interestRate: value.interestRate,
              maturityDateJapaneseCalendar: value.maturityDateJapaneseCalendar,
              maturityDateAd:
                value.maturityDateAd &&
                new Date(value.maturityDateAd).toLocaleDateString('ja-JP'),
              period1: value.period1,
              periodInterest: value.periodInterest,
              interimPaymentInterestRate: value.interimPaymentInterestRate,
              interimPaymentType: value.interimPaymentType,
              periodAfterTerm: value.periodAfterTerm,
              interestRateAfterTerm: value.interestRateAfterTerm,
              interestAfterTerm: value.interestAfterTerm,
              totalInterest: value.totalInterest,
              taxType: value.taxType,
              taxTypeName: value.taxTypeName,
              taxRate: value.taxRate,
              taxAmount: value.taxAmount,
              aftertaxInterest: value.aftertaxInterest,
              abstract: value.abstract,
              period2: value.period2,
              periodInterestPositiveAndNegativeDisplay:
                value.periodInterestPositiveAndNegativeDisplay
            };
          transactionsDetailSmbc.push(createTransactionsDetailSmbcData);
        });
        return transactionsDetailSmbc;
      };

      const data: components['schemas']['Transactions'] = {
        inquiryResult: response.data.inquiryResult,
        categoryCode: response.data.categoryCode,
        createdDateJapaneseCalendar: response.data.createdDateJapaneseCalendar,
        createdDateAd: response.data.createdDateAd,
        baseDate: nowDate().toLocaleDateString('ja-JP'),
        baseTime: nowDate().toLocaleTimeString('ja-JP', options),
        transactionDateFromJapaneseCalendar:
          response.data.transactionDateFromJapaneseCalendar,
        transactionDateFromAd: response.data.transactionDateFromAd,
        transactionDateToJapaneseCalendar:
          response.data.transactionDateToJapaneseCalendar,
        transactionDateToAd: response.data.transactionDateToAd,
        bankCode: response.data.bankCode,
        bankNameKana: response.data.bankNameKana,
        branchCode: response.data.branchCode,
        branchNameKana: response.data.branchNameKana,
        accountTypeCode: response.data.accountTypeCode,
        accountType: response.data.accountType,
        accountNumber: response.data.accountNumber,
        accountId: response.data.accountId,
        accountName: response.data.accountName,
        overdraftTypeHeader: response.data.overdraftTypeHeader,
        passbookOrCertificateType: response.data.passbookOrCertificateType,
        balanceBeforeTransaction: response.data.balanceBeforeTransaction,
        transactions1: createTransactionsDetail(),
        transactions2: createTransactionsDetailSmbc(),
        depositCount: response.data.depositCount,
        totalDepositAmount: response.data.totalDepositAmount,
        withdrawalCount: response.data.withdrawalCount,
        totalWithdrawalAmount: response.data.totalWithdrawalAmount,
        overdraftTypeTrailer: response.data.overdraftTypeTrailer,
        transactionBalance: response.data.transactionBalance,
        transactionDataCount: response.data.transactionDataCount,
        totalCount: response.data.totalCount,
        count: response.data.count,
        hasNext: response.data.hasNext,
        itemKey: response.data.itemKey,
        sessionInfo: response.data.sessionInfo,
        currencyCode: response.data.currencyCode
      };

      res.status(200).json(data);
      next();
    } catch (error) {
      next(error);
    }
  },

  getTransactionsDailyTotals: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { accountId } = req.params;

      const response = await apiClient(req, {
        params: {
          from: req.query.from,
          to: req.query.to
        }
      }).get<AxiosResponse<StatementSummaryResponse>>(
        `api/bank/statement/${encodeURIComponent(accountId)}/summary`
      );

      const transactionsDailyTotals = convertBackendData(response.data);

      res.status(200).json(transactionsDailyTotals);
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default transactionsController;
