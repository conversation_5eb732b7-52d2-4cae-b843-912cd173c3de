import { Request, Response, NextFunction } from 'express';
import { getWorkBlockage } from '../services/workBlockageServices';

const workBlockageController = {
  getWorkBlockageStatus: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const functionId = req.query.functionId as string;
      const workBlockage = await getWorkBlockage(functionId.toString());
      if (!workBlockage) {
        res.status(200).json({ status: '0' });
        next();
      } else {
        res.status(200).json({ status: workBlockage?.status });
        next();
      }
    } catch (error) {
      next(error);
    }
  }
};

export default workBlockageController;
