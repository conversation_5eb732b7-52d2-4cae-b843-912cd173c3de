import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { NotFoundError } from '../errors/httpErrors';
import { getUserInfo } from '../services/sessionService';
import { components } from '../schemas/schema';
import { DATE_FORMAT_SLASH, serverDate } from '../utils/dateFormat';
import { errorCodes } from '../errors/errorCodes';
import { apiClient } from '../common/api/baseApi';

const usersController = {
  getUsers: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { vdId, sessionId } = req.session;
      const { requestId } = req.headers;
      const ip = req.header('X-Forwarded-For')
        ? req.header('X-Forwarded-For')!
        : '';

      let data = null;

      // 利用利用者情報取得APIのリクエスト
      const usersResponse = await apiClient(req, {
        headers: {
          channel: 'MOBILE',
          requestId,
          sessionId,
          'x-valuedoor-id': vdId,
          ip
        }
      }).get<AxiosResponse<components['schemas']['Users']>>(
        'api/bank/accounts'
      );

      // 口座表示設定取得APIのリクエスト
      const accountSettingsResponse = await apiClient(req, {}).get<
        AxiosResponse<{
          accounts: {
            accountId: string;
            isHidden: boolean;
            accountType: string;
            displayName: string;
          }[];
        }>
      >('api/common/account-settings');

      const accountSettingsUserResponse =
        accountSettingsResponse.data.accounts.filter(
          (item) => item.accountType === 'web21'
        );

      // 口座表示設定を持つ利用者情報を抽出する
      const hasAccountSettingsUserData = usersResponse.data.accountList?.filter(
        (item) => {
          const accountSettingData = accountSettingsUserResponse?.find(
            ({ accountId }) => item.accountId === accountId
          );
          return accountSettingData;
        }
      );

      // 口座表示設定を持たない利用者情報を抽出する
      const noAccountSettingsUserData = usersResponse.data.accountList?.filter(
        (item) => {
          const accountSettingData = accountSettingsUserResponse?.find(
            ({ accountId }) => item.accountId === accountId
          );
          return !accountSettingData;
        }
      );

      // 利用者情報に口座表示設定をマージ
      // 返却されないのでindexをそれぞれの口座に付与させる
      const hasAccountSettingsDataAccounts = accountSettingsUserResponse
        ?.filter((accountSettingsItem) => {
          const hasAccountSettingData = hasAccountSettingsUserData!.find(
            (item) => accountSettingsItem.accountId === item.accountId
          );
          return hasAccountSettingData !== undefined;
        })
        .map((accountSettingsItem, index) => {
          const hasAccountSettingData = hasAccountSettingsUserData!.find(
            (item) => accountSettingsItem.accountId === item.accountId
          );
          return {
            ...hasAccountSettingData,
            bankName: '三井住友銀行',
            displayAccountName: accountSettingsItem.displayName,
            isHidden: accountSettingsItem.isHidden,
            index,
            accountApiType: accountSettingsItem.accountType
          };
        });

      // 口座表示設定を持たないので初期値を設定
      // 返却されないのでindexをそれぞれの口座に付与させる
      const noAccountSettingsDataAccounts = noAccountSettingsUserData?.map(
        (userItem, index) => ({
          ...userItem,
          bankName: '三井住友銀行',
          displayAccountName: null,
          isHidden: false,
          index: hasAccountSettingsDataAccounts.length + index,
          accountApiType: 'web21'
        })
      );

      // 口座表示設定を持つ利用者情報と口座表示設定を持たない利用者情報をマージする
      const accounts = [
        ...hasAccountSettingsDataAccounts,
        ...noAccountSettingsDataAccounts!
      ];

      data = {
        count: usersResponse.data.count,
        accountList: accounts,
        balanceAuthorityStatus: usersResponse.data.balanceAuthorityStatus,
        serverDate: serverDate(DATE_FORMAT_SLASH)
      };

      res.status(200).json(data);
      next();
    } catch (error) {
      next(error);
    }
  },

  getUserInfo: async (
    req: Request,
    res: Response<components['schemas']['UserInfo']>,
    next: NextFunction
  ) => {
    try {
      const { sessionId } = req.session;
      const userInfo = await getUserInfo(sessionId);

      if (!userInfo) {
        const notFoundError = new NotFoundError(
          errorCodes.NOT_FOUND_USER_INFO,
          `セッションIDに紐づくユーザー情報がありませんでした。sessionId=${sessionId}`
        );
        return next(notFoundError);
      }
      res.status(200).json(userInfo);
      return next();
    } catch (error) {
      return next(error);
    }
  }
};

export default usersController;
