import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import {
  DATE_FORMAT_SLASH,
  DATE_FORMAT_YYYY_MM_DD,
  changeDateFormat,
  serverDate,
  serverDateTime,
  serverTime
} from '../utils/dateFormat';
import { components } from '../schemas/schema';
import { apiClient } from '../common/api/baseApi';

const freeeTransactionsController = {
  getUsedTotalAmount: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { walletable_id, walletable_type } = req.query;
      const response = await apiClient(req, {
        params: {
          walletable_id,
          walletable_type,
          period: 'one_month',
          end_date: serverDate(DATE_FORMAT_YYYY_MM_DD)
        }
      }).get<
        AxiosResponse<components['schemas']['FreeeTransactionsTotalResponse']>
      >('api/freee/transactions/total-expense');

      // レスポンスにサーバー日時を追加
      response.data.serverDateTime = serverDateTime();

      res.status(200).json(response.data);
      next();
    } catch (error) {
      next(error);
    }
  },
  getFreeeTransactions: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { walletable_id, walletable_type, start_date, end_date } =
        req.query;
      const response = await apiClient(req, {
        params: {
          walletable_id,
          walletable_type,
          start_date,
          end_date
        }
      }).get<AxiosResponse<components['schemas']['FreeeTransactions']>>(
        'api/freee/transactions'
      );

      response.data.baseDate = serverDate(DATE_FORMAT_SLASH);
      response.data.baseTime = serverTime();
      response.data.transactions = response.data.transactions.map(
        (transactions) => ({
          id: transactions.id,
          date: changeDateFormat(
            DATE_FORMAT_SLASH,
            new Date(transactions.date)
          ),
          amount: transactions.amount,
          entrySide: transactions.entrySide,
          description: transactions.description
        })
      );

      res.status(200).json(response.data);
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default freeeTransactionsController;
