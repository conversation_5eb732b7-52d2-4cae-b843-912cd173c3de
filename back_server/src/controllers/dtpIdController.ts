/* eslint-disable max-len */
import { NextFunction, Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import axios, { AxiosResponse } from 'axios';
import dayjs from 'dayjs';
import { createHash } from 'node:crypto';
import { NotFoundError } from '../errors/httpErrors';
import { getIdaasEncryptedCookieValue } from '../utils/getIdaasEncryptedCookieValue';
import { Session, UserInfo } from '../models/session';
import { expirationTime, jstTime, nowDate } from '../utils/dateFormat';
import { errorCodes } from '../errors/errorCodes';
import { components } from '../schemas/schema';
import { apiClient } from '../common/api/baseApi';
import {
  saveSession,
  updateDtpId,
  updateDtpIdAndUserId
} from '../services/sessionService';
import { judgeRisk } from '../services/judgeRiskService';
import { Ids } from '../types/ids';
import { ValueDoorUser } from '../types/valueDoorUser';
import { logger } from '../utils/logger';
import renderError from '../utils/renderError';
import { BackendErrorResponse } from '../models/errorResponse';
import { salt } from '../config/config';
import { getIdaasDecryptCookieValue } from '../utils/getIdaasDecryptCookieValue';
import { checkAuthDtpIdLoginErrorCodes } from '../utils/checkAuthDtpIdLoginErrorCodes';

const dtpIdController = {
  loginDtpId: async (req: Request, res: Response, next: NextFunction) => {
    let vdAuthorityAcquisitionResponse;
    let encryptedCookie;
    let userId;
    let vdId;
    // 初期値はL(低リスク)
    let judgeRiskResponse = 'L';
    const { dtpId, password, caulisSessionId } =
      req.body as components['schemas']['DtpIdLoginRequest'];
    try {
      // DTPIDに紐づくVDID・userIDを取得
      const authenticateDtpIdResponse = await apiClient(req, {}).post<
        { dtpId?: string; password?: string },
        AxiosResponse<{
          dtpId: string;
          userUid: string;
          valueDoorId?: string;
          valueDoorUser: ValueDoorUser;
        }>
      >('api/user/auth/dtpid-login', {
        dtpId,
        password
      });

      vdId = authenticateDtpIdResponse.data.valueDoorId;
      userId = authenticateDtpIdResponse.data.userUid;
      vdAuthorityAcquisitionResponse =
        authenticateDtpIdResponse.data.valueDoorUser;

      // VDIDが空の場合は口座開設中のエラーを返却
      if (!vdId) {
        const notFoundError = new NotFoundError(
          errorCodes.ACCOUNT_CREATION_IN_PROGRESS,
          '口座開設中のため、ログインできません。口座開設後にログインしてください。'
        );
        return next(notFoundError);
      }
      // 暗号化クッキー生成
      encryptedCookie = getIdaasEncryptedCookieValue(userId, dtpId);
    } catch (error) {
      try {
        // 怪しさ判定リクエスト（バックエンドへのログイン認証リクエスト失敗時）
        if (axios.isAxiosError(error) && error.response) {
          const backendError = error as BackendErrorResponse;
          if (checkAuthDtpIdLoginErrorCodes(backendError.response.data.code)) {
            const userHash = createHash('sha256')
              .update(dtpId + salt)
              .digest('hex');
            const userAgent = req.header('user-agent')!;
            // ログイン失敗時は空
            const userHashOfNewAccountCreation = '';

            await apiClient(req, {
              headers: {
                caulisSessionId,
                userHash,
                userAgent,
                userHashOfNewAccountCreation
              }
            }).post<
              {
                isLoginSucceed: boolean;
                loginErrorCode: string;
                loginKind: string;
              },
              AxiosResponse<{ relativeSuspiciousValue: string }>
            >('api/caulis/login-suspicious-detection', {
              isLoginSucceed: false,
              loginErrorCode: backendError.response.data.code,
              loginKind: '1'
            });
          }
        }
      } catch (requestError) {
        // ログイン失敗時の怪しさ判定リクエストでのエラーはログ出力のみ
        logger.writeMsg('ICOM000005', [
          'ログイン失敗時の怪しさ判定リクエストでのエラー',
          renderError(requestError as Error)
        ]);
      }
      // ログイン失敗時のエラーを返却
      return next(error);
    }
    try {
      // ログイン成功時の怪しさ判定API呼び出し
      const userHash = createHash('sha256')
        .update(vdId + salt)
        .digest('hex');
      const userAgent = req.header('user-agent')!;
      const userHashOfNewAccountCreation =
        dtpId !== undefined
          ? createHash('sha256')
              .update(dtpId + salt)
              .digest('hex')
          : '';

      const relativeSuspiciousValue = await apiClient(req, {
        headers: {
          caulisSessionId,
          userHash,
          userAgent,
          userHashOfNewAccountCreation
        }
      }).post<
        {
          isLoginSucceed: boolean;
          loginKind: string;
        },
        AxiosResponse<{ relativeSuspiciousValue: string }>
      >('api/caulis/login-suspicious-detection', {
        isLoginSucceed: true,
        loginKind: '1'
      });
      judgeRiskResponse = relativeSuspiciousValue.data.relativeSuspiciousValue;
    } catch (error) {
      // ログイン成功時の怪しさ判定リクエストでのエラーはログ出力のみ
      logger.writeMsg('ICOM000005', [
        'ログイン成功時の怪しさ判定リクエストでのエラー',
        renderError(error as Error)
      ]);
    }
    try {
      const ids: Ids = {
        dtpId,
        userUid: userId,
        valueDoorId: vdId
      };

      // 高リスクユーザーセッションID作成のための時刻
      const date = nowDate();
      const issueInstant = dayjs(date).toISOString(); // 現在時刻

      // 危険度判定
      const judgeRiskResult = await judgeRisk(
        ids,
        new Date(issueInstant),
        vdAuthorityAcquisitionResponse,
        judgeRiskResponse,
        '1'
      );

      if (judgeRiskResult) {
        const { highRiskUserId } = judgeRiskResult;

        res.status(200).json({ highRiskUserId, encryptedCookie });
        return next();
      }

      // セッションID払い出し
      const sessionId: string = uuidv4();

      // DTPIDログイン時の利用者情報クラスに格納
      const userInfoData: UserInfo = {
        ninsyoKbn: vdAuthorityAcquisitionResponse.authenticationMethod,
        compId: vdAuthorityAcquisitionResponse.representativeAccount,
        compName: vdAuthorityAcquisitionResponse.companyName,
        userSeiMei: vdAuthorityAcquisitionResponse.userName,
        userTyp: vdAuthorityAcquisitionResponse.userType,
        compAtrbt: vdAuthorityAcquisitionResponse.accessLevel,
        userKn: vdAuthorityAcquisitionResponse.usernameKana,
        Email: vdAuthorityAcquisitionResponse.email,
        telNo: vdAuthorityAcquisitionResponse.telNo,
        otpId: Number(vdAuthorityAcquisitionResponse.otpId),
        otpSerialNo: vdAuthorityAcquisitionResponse.otpSerialNo,
        // ValueDoorの返却値は0:無、1:有だが、バックエンドの返却値はbooleanなのでbooleanに合わせる
        otpAplKbn: vdAuthorityAcquisitionResponse.usesOtp,
        otpKind: vdAuthorityAcquisitionResponse.otpKind,
        otpDeviceStatus: vdAuthorityAcquisitionResponse.otpDeviceStatus,
        // ValueDoorの返却値は0:非再発行中、1:再発行中だが、バックエンドの返却値はbooleanなのでbooleanに合わせる
        otpReissueKbn: vdAuthorityAcquisitionResponse.isOtpReissuing,
        otpSerialNoReissue: vdAuthorityAcquisitionResponse.otpSerialNoReissue,
        otpKindReissue: vdAuthorityAcquisitionResponse.otpKindReissue,
        otpDeviceStsReissue:
          vdAuthorityAcquisitionResponse.otpDeviceStatusReissue,
        otpReissueAction: vdAuthorityAcquisitionResponse.otpReissueAction,
        otpNonOtpExpiredDate:
          vdAuthorityAcquisitionResponse.otpNonOtpExpiredDate,
        otpServiceCode: vdAuthorityAcquisitionResponse.otpServiceCode,
        userAuths: vdAuthorityAcquisitionResponse.authorizations,
        kigyoCd: vdAuthorityAcquisitionResponse.companyCode,
        keiyakuType: vdAuthorityAcquisitionResponse.contractType,
        clientId: vdAuthorityAcquisitionResponse.clientId
      };

      // セッション情報クラスに格納
      const sessionData: Session = {
        sessionId,
        vdId,
        dtpId,
        userId,
        issueInstant,
        userInfo: userInfoData,
        state: null,
        freeeOauthState: null,
        expirationTime: expirationTime(),
        createdAt: jstTime(),
        updatedAt: jstTime()
      };

      // セッションIDをDynamoDBに保存
      await saveSession(sessionData);

      res.status(200).json({ sessionId, encryptedCookie });
      return next();
    } catch (error) {
      return next(error);
    }
  },
  updateDtpId: async (req: Request, res: Response, next: NextFunction) => {
    try {
      // ID紐付け情報リクエストを実行
      const idLinksResponse = await apiClient(req, {
        responseType: 'json'
      }).get<
        AxiosResponse<{
          dtpId: string | undefined;
          userUid: string | undefined;
          valueDoorId: string | undefined;
        }>
      >('api/user/id-links');

      // ID紐付け情報レスポンスからdtpIdを取得
      const { dtpId } = idLinksResponse.data;

      // セッション情報クラスに格納
      const sessionData: Session = {
        sessionId: req.session.sessionId,
        dtpId,
        updatedAt: jstTime()
      };

      // DynamoDBのセッション情報（dtpId,userId）を更新
      await updateDtpId(sessionData);

      res.status(200).json({ message: 'DTPIDを更新しました' });
      return next();
    } catch (error) {
      return next(error);
    }
  },
  issueAndLinkDtpId: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      // リクエストボディーから暗号化クッキーを取得
      const { encryptedCookie } =
        req.body as components['schemas']['IssueAndLinkDtpIdRequest'];

      // 暗号化クッキーを復号し、ユーザーIDを取得
      const userId = getIdaasDecryptCookieValue(encryptedCookie.ticket!, 1);

      // セッションからvdIdを取得
      const valueDoorId = req.session.vdId;

      // ID紐付け情報登録API実行（w/ userId）
      await apiClient(req, {}).post<
        { userId?: string },
        AxiosResponse<{ dtpId: string; userUid: string; valueDoorId?: string }>
      >('api/user/link-new-ids', {
        userId
      });

      // ID紐付け情報リクエストを実行
      const idLinksResponse = await apiClient(req, {
        responseType: 'json'
      }).get<
        AxiosResponse<{
          dtpId: string | undefined;
          userUid: string | undefined;
          valueDoorId: string | undefined;
        }>
      >('api/user/id-links');

      // ID紐付け情報レスポンスからdtpIdを取得
      const { dtpId } = idLinksResponse.data;

      // セッション情報クラスに格納
      const sessionData: Session = {
        sessionId: req.session.sessionId,
        dtpId,
        userId,
        updatedAt: jstTime()
      };

      // DynamoDBのセッション情報（dtpId,userId）を更新
      await updateDtpIdAndUserId(sessionData);

      res.status(200).json({ dtpId, userId, valueDoorId, encryptedCookie });
      return next();
    } catch (error) {
      return next(error);
    }
  },
  deleteDtpIdLink: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { userId } = req.session;

      await apiClient(req, {}).post<
        { userId?: string },
        AxiosResponse<{ userId: string }>
      >('api/user/unlink-vdid', { userId });

      res.status(200).json({ message: '紐づけ解除に成功しました。' });
      return next();
    } catch (error) {
      return next(error);
    }
  }
};
export default dtpIdController;
