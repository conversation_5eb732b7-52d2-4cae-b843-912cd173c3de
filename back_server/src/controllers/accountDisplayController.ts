import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { components } from '../schemas/schema';
import { apiClient } from '../common/api/baseApi';

const accountDisplayController = {
  getAccountsDisplay: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const response = await apiClient(req, {}).get<
        AxiosResponse<{
          accounts: {
            accountId: string;
            isHidden: boolean;
            accountType: string;
            displayName: string;
          }[];
        }>
      >('api/common/account-settings');

      const result = response.data.accounts?.map((item) => ({
        accountId: item.accountId,
        isHidden: item.isHidden,
        displayName: item.displayName,
        accountApiType: item.accountType
      }));

      const data = {
        accounts: result
      };

      res.status(200).json(data);
      next();
    } catch (error) {
      next(error);
    }
  },

  updateAccountsDisplay: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { accounts } = req.body as components['schemas']['AccountSettings'];

      const requestBody = accounts?.map((data) => ({
        accountId: data.accountId,
        isHidden: data.isHidden,
        displayName: data.displayName,
        accountType: data.accountApiType
      }));

      const data = {
        accounts: requestBody
      };

      await apiClient(req, {
        responseType: 'json'
      }).update<
        {
          accounts: {
            accountId: string;
            isHidden: boolean;
            accountType: 'web21' | 'freee';
            displayName: string | undefined;
          }[];
        },
        AxiosResponse<unknown>
      >('api/common/account-settings', data);

      res.status(200).json({
        message: '口座表示設定を更新しました。'
      });
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default accountDisplayController;
