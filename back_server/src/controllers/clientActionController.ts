import { Request, Response, NextFunction } from 'express';
import { jstTimeSQS } from '../utils/dateFormat';
import { components } from '../schemas/schema';
import { ClientActionLog } from '../models/clientActionLog';
import { saveClientActionLog } from '../services/clientActionLogService';

const clientActionController = {
  saveClientActionLog: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const { vdId, dtpId, sessionId } = req.session;
      const { clientFunction, operation, result, errorId } =
        req.body as components['schemas']['ClientActionRequest'];
      const clientActionLog: ClientActionLog = {
        opDate: jstTimeSQS(),
        opChannel: 'MOBILE',
        ip: req.header('X-Forwarded-For')!,
        dtpId: dtpId ?? '',
        vdId: vdId ?? '',
        userName: req.session.userInfo?.userSeiMei
          ? req.session.userInfo?.userSeiMei
          : '',
        accountNumber: req.session.userInfo?.compId
          ? req.session.userInfo?.compId
          : '',
        clientFunction: clientFunction ?? '',
        operation: operation ?? '',
        result: result ?? '',
        errorId: errorId ?? ''
      };
      await saveClientActionLog(sessionId, clientActionLog);

      res.status(200).json({ message: 'OK' });
      next();
    } catch (error) {
      res.status(200).json({ message: 'NG' });
      next();
    }
  }
};

export default clientActionController;
