import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { components } from '../schemas/schema';
import { DATE_FORMAT_SLASH, serverDate, serverTime } from '../utils/dateFormat';
import { apiClient } from '../common/api/baseApi';

const balanceController = {
  getBalance: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { accountIds } = req.params;

      const response = await apiClient(req, {
        params: { date: req.query.date }
      }).get<AxiosResponse<components['schemas']['Balance']>>(
        `api/bank/balance/${encodeURIComponent(accountIds)}`
      );

      const accounts = response.data.accounts?.map((data) => {
        let baseDate = null;
        let baseTime = null;
        let baseDateTime = null;

        if (!data.baseDate) {
          baseDate = serverDate(DATE_FORMAT_SLASH);
        } else {
          baseDate = new Date(data.baseDate).toLocaleDateString('ja-JP');
        }

        if (!data.baseTime) {
          baseTime = serverTime();
        } else {
          const [hours, minutes] = data.baseTime.split(':').map(Number);
          baseTime = `${hours}:${minutes.toString().padStart(2, '0')}`;
        }

        baseDateTime = `${baseDate} ${baseTime}`;

        return {
          ...data,
          accountId: data.accountId,
          bankName: '三井住友銀行',
          baseDate,
          baseTime,
          baseDateTime
        };
      });

      const data = {
        count: response.data.count,
        accounts,
        serverDate: serverDate(DATE_FORMAT_SLASH)
      };

      res.status(200).json(data);
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default balanceController;
