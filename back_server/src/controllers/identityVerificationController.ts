import { Request, Response, NextFunction } from 'express';
import axios, { AxiosResponse } from 'axios';
import { components } from 'schemas/schema';
import { closingTime } from '../config/timeConfig';
import { checkClosingStatus } from '../utils/checkClosingStatus';
import { BackendErrorResponse } from '../models/errorResponse';
import { apiClient } from '../common/api/baseApi';
import { InternalServerError, NotFoundError } from '../errors/httpErrors';
import { errorCodes } from '../errors/errorCodes';
import {
  BeneficiaryIdentificationData,
  IdendificationTemporary,
  IdentificationData,
  PreConfirmData,
  ScreeningResults,
  ValidationStatus
} from '../models/identification';

// userType判定ロジック
function getUserType(
  applicantRole: string | undefined,
  beneficiaryNo: string
): string | undefined {
  if (beneficiaryNo) {
    // beneficiaryNoに値が存在するの場合、末尾の数字を切り出しBENEFICIARYの末尾に付与
    return `BENEFICIARY${beneficiaryNo}`;
  }
  if (applicantRole != null) {
    // applicantRoleがnullでない場合、代表者 or 代理人を返却
    return applicantRole;
  }
  return undefined;
}

// 実質的支配者のステータス確認
function beneficiaryStatusCheck(
  beneficiaryIdentificationData: BeneficiaryIdentificationData | undefined,
  beneficiaryNo: number
): string | undefined {
  if (!beneficiaryIdentificationData) {
    return undefined;
  }
  const beneficiaryStatus = beneficiaryIdentificationData[beneficiaryNo];
  return beneficiaryStatus?.status;
}

// STEP数判定ロジック
// それぞれundefinedの場合があるため、true以外の場合にSTEPを返却
function getValidationStep(status: ValidationStatus): string {
  if (status.isPriorConfirmationApplicationDataValid !== true) {
    // 事前確認が完了していなければSTEP1
    return 'STEP1';
  }
  if (
    status.isPriorConfirmationApplicationDataValid === true &&
    status.isIdentificationApplicationDataValid !== true
  ) {
    // 本人確認が完了していなければSTEP2
    return 'STEP2';
  }
  if (
    status.isPriorConfirmationApplicationDataValid === true &&
    status.isIdentificationApplicationDataValid === true &&
    status.isMainApplicationDataValid !== true
  ) {
    // 口座開設に必要な情報入力が完了していなければSTEP3
    return 'STEP3';
  }
  if (
    status.isPriorConfirmationApplicationDataValid === true &&
    status.isIdentificationApplicationDataValid === true &&
    status.isMainApplicationDataValid === true &&
    status.isIdentificationBeneficiaryApplicationDataValid !== true
  ) {
    // 実質的支配者による確認が完了していなければSTEP4
    return 'STEP4';
  }
  if (
    status.isPriorConfirmationApplicationDataValid === true &&
    status.isIdentificationApplicationDataValid === true &&
    status.isMainApplicationDataValid === true &&
    status.isIdentificationBeneficiaryApplicationDataValid !== false &&
    status.isDocumentSubmissionApplicationDataValid !== true
  ) {
    // 書類提出が完了していなければSTEP5
    return 'STEP5';
  }
  // いずれにも合致しなければ判定不能とする
  return 'undefined';
}

// 本人確認実施済みフラグ判定ロジック
function getIdentityVerified(
  userType: string,
  identificationData: IdendificationTemporary[] | undefined,
  beneficiaryStatus: string | undefined
): boolean {
  if (userType === 'REPRESENTATIVE') {
    // identificationDataのrole => 代表者があれば、true
    const isIdentityVerified = identificationData?.some(
      (data) => data.role === 'REPRESENTATIVE'
    );
    return isIdentityVerified ?? false;
  }
  if (userType === 'AGENT') {
    // identificationDataのrole => 代理人があれば、true
    const isIdentityVerified = identificationData?.some(
      (data) => data.role === 'AGENT'
    );
    return isIdentityVerified ?? false;
  }
  if (userType.includes('BENEFICIARY')) {
    // 実質的支配者の場合、beneficiaryStatusに応じて実施済みか判定する(NECESSARYは必要なのでfalseになる)
    const isIdentityVerified =
      beneficiaryStatus === 'COMPLETED' ||
      beneficiaryStatus === 'COMPLETED_IN_STEP2';
    return isIdentityVerified ?? false;
  }
  return false;
}

// 本人確認実施不備フラグの判定ロジック
function getFasFault(identificationData: IdentificationData[] | undefined) {
  let isRepresentativeHasFault = false;
  let isAgentHasFault = false;
  let isBeneficiary1HasFault = false;
  let isBeneficiary2HasFault = false;
  let isBeneficiary3HasFault = false;
  let isBeneficiary4HasFault = false;
  identificationData?.forEach((data) => {
    if (data.role === 'REPRESENTATIVE') {
      // 代表者の不備フラグを取得し格納する
      isRepresentativeHasFault = data.hasFault;
    }
    if (data.role === 'AGENT') {
      // 代理人の不備フラグを取得し格納する
      isAgentHasFault = data.hasFault;
    }
    if (data.role === 'BENEFICIARY1') {
      // 代理人の不備フラグを取得し格納する
      isBeneficiary1HasFault = data.hasFault;
    }
    if (data.role === 'BENEFICIARY2') {
      // 代理人の不備フラグを取得し格納する
      isBeneficiary2HasFault = data.hasFault;
    }
    if (data.role === 'BENEFICIARY3') {
      // 代理人の不備フラグを取得し格納する
      isBeneficiary3HasFault = data.hasFault;
    }
    if (data.role === 'BENEFICIARY4') {
      // 代理人の不備フラグを取得し格納する
      isBeneficiary4HasFault = data.hasFault;
    }
  });
  return {
    isRepresentativeHasFault,
    isAgentHasFault,
    isBeneficiary1HasFault,
    isBeneficiary2HasFault,
    isBeneficiary3HasFault,
    isBeneficiary4HasFault
  };
}

const identityVerificationController = {
  getOpenAccountApplicationTemporary: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      let referenceNumber = req.query.referenceNumber as string;
      let beneficiaryNo = '';
      let beneficiaryStatus;

      // 手続き番号が14桁の場合、手続き番号と実質的支配者の特定番号に分割する
      if (referenceNumber.length === 14) {
        beneficiaryNo = referenceNumber.slice(-1);
        referenceNumber = referenceNumber.slice(0, -1);
      }

      const response = await apiClient(req, {}).get<
        AxiosResponse<{
          identificationData: IdendificationTemporary[] | undefined;
          validationStatus: ValidationStatus | undefined;
          preConfirmData: PreConfirmData | undefined;
          beneficiaryIdentificationData:
            | BeneficiaryIdentificationData
            | undefined;
        }>
      >(
        `api/open-bank-account/applications/${encodeURIComponent(
          referenceNumber
        )}`
      );

      // 本人確認実施者
      const userType = getUserType(
        response.data.preConfirmData?.applicantRole,
        beneficiaryNo
      );

      if (!userType) {
        // userTypeの値がない場合、エラーを返却（お手続き番号入力者が実質的支配者だが、13桁の場合も含む）
        const notFoundError = new NotFoundError(
          errorCodes.NOT_FOUND_USER_TYPE,
          '該当する本人確認実施者が見つかりませんでした。手続き番号をお確かめの上、再入力してください。'
        );
        return next(notFoundError);
      }

      // お手続き番号入力者が実質的支配者の場合、本人確認の要否を確認
      if (userType.includes('BENEFICIARY')) {
        beneficiaryStatus = beneficiaryStatusCheck(
          response.data.beneficiaryIdentificationData,
          Number(beneficiaryNo)
        );

        // お手続き番号入力者が実質的支配者(14桁入力)で、対象の実質的支配者がいない場合か不要の場合、エラーを返却
        if (!beneficiaryStatus || beneficiaryStatus === 'UNNECESSARY') {
          const notFoundError = new NotFoundError(
            errorCodes.NOT_FOUND_BENEFICIARY,
            '本人確認が不要な実質的支配者でした。'
          );
          return next(notFoundError);
        }
      }

      // 進行中STEP
      const stepInProgress = {
        isPriorConfirmationApplicationDataValid:
          response.data.validationStatus
            ?.isPriorConfirmationApplicationDataValid,
        isIdentificationApplicationDataValid:
          response.data.validationStatus?.isIdentificationApplicationDataValid,
        isMainApplicationDataValid:
          response.data.validationStatus?.isMainApplicationDataValid,
        isIdentificationBeneficiaryApplicationDataValid:
          response.data.validationStatus
            ?.isIdentificationBeneficiaryApplicationDataValid,
        isDocumentSubmissionApplicationDataValid:
          response.data.validationStatus
            ?.isDocumentSubmissionApplicationDataValid
      };

      // STEP数
      let validationStatus = getValidationStep(stepInProgress);

      // 代表者or代理人の場合、STEP4が書類提出なのでSTEP5に変更する
      if (
        (userType === 'REPRESENTATIVE' || userType === 'AGENT') &&
        validationStatus === 'STEP4'
      ) {
        validationStatus = 'STEP5';
      }

      // 本人確認実施済みフラグ
      const isIdentityVerified = getIdentityVerified(
        userType,
        response.data.identificationData,
        beneficiaryStatus
      );

      // 返却データ
      const data: components['schemas']['OpenAccountApplicationionionTemporaryResponse'] =
        {
          userType,
          isIdentityVerified,
          validationStatus
        };

      res.status(200).json(data);
      return next();
    } catch (error) {
      return next(error);
    }
  },
  getOpenAccountApplicationScreeningStatuses: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      let referenceNumber = req.query.referenceNumber as string;

      // 手続き番号が14桁の場合、手続き番号と実質的支配者の特定番号に分割する
      if (referenceNumber.length === 14) {
        referenceNumber = referenceNumber.slice(0, -1);
      }

      const response = await apiClient(req, {}).get<
        AxiosResponse<{
          // backendの返却値がdataで包まれているため、dataで包む
          data: {
            referenceNumber: string;
            screeningStatus: string;
            screeningResults: ScreeningResults | undefined;
          };
        }>
      >(
        `api/open-bank-account/applications/${encodeURIComponent(
          referenceNumber
        )}/screening`
      );

      // 口座開設ステータスの判定ロジック
      let isAccountOpeningRequestSubmitted = false;
      // 口座開設ステータスが存在し、返却と入力不備残以外の場合は口座開設済とみなし、trueを返却する
      const listOfIncompleteScreeningStatuses = [
        'RETURNED',
        'INCOMPLETE_INPUT'
      ];
      if (
        response.data.data?.screeningStatus &&
        !listOfIncompleteScreeningStatuses.includes(
          response.data.data?.screeningStatus
        )
      ) {
        isAccountOpeningRequestSubmitted = true;
      }

      // 本人確認実施不備フラグの判定ロジック
      const hasFault = getFasFault(
        response.data.data?.screeningResults?.identificationData
      );

      // 返却データ
      const data = {
        // 口座開設ステータスの判定ロジックを参照
        isAccountOpeningRequestSubmitted,
        // 代表者：本人確認実施不備フラグ
        isRepresentativeHasFault: hasFault.isRepresentativeHasFault,
        // 取引責任者（代理人）：本人確認実施不備フラグ
        isAgentHasFault: hasFault.isAgentHasFault,
        // 実質的支配者1：本人確認実施不備フラグ
        isBeneficiary1HasFault: hasFault.isBeneficiary1HasFault,
        // 実質的支配者2：本人確認実施不備フラグ
        isBeneficiary2HasFault: hasFault.isBeneficiary2HasFault,
        // 実質的支配者3：本人確認実施不備フラグ
        isBeneficiary3HasFault: hasFault.isBeneficiary3HasFault,
        // 実質的支配者4：本人確認実施不備フラグ
        isBeneficiary4HasFault: hasFault.isBeneficiary4HasFault
      } as components['schemas']['OpenAccountApplicationScreeningStatusesResponse'];

      res.status(200).json(data);
      next();
    } catch (error) {
      next(error);
    }
  },
  searchAddress: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const postcode = req.query.postcode as string;

      const response = await apiClient(req, {}).get<
        AxiosResponse<components['schemas']['searchAddressResponse']>
      >(`api/open-bank-account/address/${postcode}`);

      res.status(200).json(response.data);
      return next();
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        const backendError = error as BackendErrorResponse;
        if (backendError.response.data.code === 'E001-00004') {
          const notFoundError = new NotFoundError(
            errorCodes.NOT_FOUND_ADDRESS,
            '該当する住所が見つかりません。'
          );
          return next(notFoundError);
        }
      }
      return next(error);
    }
  },
  saveIdentityVerification: async (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    try {
      const {
        referenceNumber,
        role,
        dataSource,
        familyName,
        givenName,
        familyNameKana,
        givenNameKana,
        familyNameAlphabetic,
        givenNameAlphabetic,
        hepburnStyle,
        position,
        birthYear,
        birthMonth,
        birthDay,
        postCode,
        prefecture,
        city,
        sectionNumberAndBuildingName
      } = req.body as components['schemas']['IdentityVerification'];

      // 手続き番号が14桁の場合、13桁に加工する
      let referenceNumberOf13Digit;
      if (referenceNumber.length === 14) {
        referenceNumberOf13Digit = referenceNumber.slice(0, -1);
      } else {
        referenceNumberOf13Digit = referenceNumber;
      }

      const name =
        familyNameKana && givenNameKana
          ? {
              familyName,
              givenName,
              familyNameKana,
              givenNameKana,
              familyNameAlphabetic,
              givenNameAlphabetic,
              hepburnStyle: JSON.parse(hepburnStyle as string) as boolean
            }
          : undefined;

      const birthDate =
        birthYear && birthMonth && birthDay
          ? {
              year: Number(birthYear),
              month: Number(birthMonth),
              day: Number(birthDay)
            }
          : undefined;

      const address = postCode
        ? {
            postCode,
            prefecture,
            city,
            sectionNumberAndBuildingName
          }
        : undefined;
      const request = {
        role,
        dataSource,
        name,
        position,
        // eKYCの場合のみbirthDateオブジェクトを送信する
        ...(dataSource === 'eKYC' && {
          birthDate
        }),
        address
      } as IdendificationTemporary;

      await apiClient(req, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).post<
        {
          role: string;
          dataSource: string;
          name:
            | {
                familyName: string | undefined;
                givenName: string | undefined;
                familyNameKana: string;
                givenNameKana: string;
                familyNameAlphabetic: string;
                givenNameAlphabetic: string;
                hepburnStyle: boolean;
              }
            | undefined;
          position: string | undefined;
          birthDate:
            | {
                year: number | undefined;
                month: number | undefined;
                day: number | undefined;
              }
            | undefined;
          address:
            | {
                postCode: string;
                prefecture: string | undefined;
                city: string | undefined;
                sectionNumberAndBuildingName: string | undefined;
              }
            | undefined;
        },
        AxiosResponse<{ message: string }>
      >(
        `api/open-bank-account/applications/${encodeURIComponent(
          referenceNumberOf13Digit
        )}/identification`,
        request
      );

      res.status(200).json({ message: '本人確認情報を保存しました。' });
      return next();
    } catch (error) {
      // 現在は一律で同じエラーを返している。ph05_sp19のDTPO-3875にて改修する
      const internalServerError = new InternalServerError(
        errorCodes.SAVE_IDENTITYVERIFICATION_FAILED,
        '本人確認情報の保存に失敗しました。'
      );
      return next(internalServerError);
    }
  },
  requestEkycUrl: async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { referenceNumber, uuid, userType } =
        req.body as components['schemas']['EkycUrlRequest'];

      // 手続き番号が14桁の場合、手続き番号と実質的支配者の特定番号に分割する
      let newReferenceNumber = referenceNumber;

      if (referenceNumber.length === 14) {
        newReferenceNumber = referenceNumber.slice(0, -1);
      }

      // お手続き番号をもとにAuroraからUUIDを取得する
      const uuidResponse = await apiClient(req, {}).get<
        AxiosResponse<{
          uuid: string;
        }>
      >(
        `api/open-bank-account/applications/${encodeURIComponent(
          newReferenceNumber
        )}/token`
      );

      // UUIDが改ざんされていないことを確認する
      if (uuid !== uuidResponse.data.uuid) {
        const notFoundError = new NotFoundError(
          errorCodes.NOT_FOUND_UUID,
          'UUIDが見つかりませんでした。'
        );
        return next(notFoundError);
      }

      // ポラリファイからeKYCのURLを取得する
      const ekycUrlResponse = await apiClient(req, {}).post<
        {
          userType: string;
        },
        AxiosResponse<string>
      >(
        `api/open-bank-account/applications/${encodeURIComponent(
          newReferenceNumber
        )}/request-ekyc-url`,
        { userType }
      );

      const data = { ekycUrl: ekycUrlResponse.data };

      res.status(200).json(data);
      return next();
    } catch (error) {
      return next(error);
    }
  },
  getUuid: async (req: Request, res: Response, next: NextFunction) => {
    try {
      let referenceNumber = req.query.referenceNumber as string;

      // 手続き番号が14桁の場合、手続き番号と実質的支配者の特定番号に分割する
      if (referenceNumber.length === 14) {
        referenceNumber = referenceNumber.slice(0, -1);
      }

      // お手続き番号をもとにAuroraからUUIDを取得する
      const uuidResponse = await apiClient(req, {}).get<
        AxiosResponse<{
          uuid: string;
        }>
      >(
        `api/open-bank-account/applications/${encodeURIComponent(
          referenceNumber
        )}/token`
      );

      // UUIDが改ざんされていないことを確認する
      if (!uuidResponse.data.uuid) {
        const notFoundError = new NotFoundError(
          errorCodes.NOT_FOUND_UUID,
          'UUIDが見つかりませんでした。'
        );
        return next(notFoundError);
      }

      res.status(200).json({ uuid: uuidResponse.data.uuid });
      return next();
    } catch (error) {
      return next(error);
    }
  },
  checkCorporateWebStatus: (
    req: Request,
    res: Response,
    next: NextFunction
  ) => {
    // checkClosingTimeを呼び出す
    const response = checkClosingStatus(closingTime);

    res.status(200).json({ isOpened: response });
    return next();
  }
};

export default identityVerificationController;
