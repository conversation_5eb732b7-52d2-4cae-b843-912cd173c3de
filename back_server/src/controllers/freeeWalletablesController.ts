import { Request, Response, NextFunction } from 'express';
import { AxiosResponse } from 'axios';
import { convertToDateTimeSlash, serverDateTime } from '../utils/dateFormat';
import { convertToIsFreeeReConnect } from '../utils/convertToIsFreeeReConnect';
import { components } from '../schemas/schema';
import { apiClient } from '../common/api/baseApi';

const freeeWalletablesController = {
  getWalletables: async (req: Request, res: Response, next: NextFunction) => {
    try {
      let data = null;

      // freee口座一覧取得APIのリクエスト
      const response = await apiClient(req, {
        params: {
          type: req.query.type,
          with_balance: req.query.with_balance,
          with_sync_status: req.query.with_sync_status
        }
      }).get<AxiosResponse<components['schemas']['FreeeWalletables']>>(
        'api/freee/walletables'
      );

      // 口座表示設定取得APIのリクエスト
      const accountSettingsResponse = await apiClient(req, {}).get<
        AxiosResponse<{
          accounts: {
            accountId: string;
            isHidden: boolean;
            accountType: string;
            displayName: string;
          }[];
        }>
      >('api/common/account-settings');

      const accountSettingsFreeeResponse =
        accountSettingsResponse.data.accounts.filter(
          (item) => item.accountType === 'freee'
        );

      // 口座表示設定を持つfreee口座一覧を抽出する
      const hasAccountSettingsWalletablesData =
        response.data.walletables?.filter((item) => {
          const accountSettingData = accountSettingsFreeeResponse?.find(
            ({ accountId }) => item.id.toString() === accountId
          );
          return accountSettingData;
        });

      // 口座表示設定を持たないfreee口座一覧を抽出する
      const noAccountSettingsWalletablesData =
        response.data.walletables?.filter((item) => {
          const accountSettingData = accountSettingsFreeeResponse?.find(
            ({ accountId }) => item.id.toString() === accountId
          );
          return !accountSettingData;
        });

      // freee口座一覧に口座表示設定をマージ
      // 返却されないのでindexをそれぞれの口座に付与させる
      const hasAccountSettingsDataAccounts = accountSettingsFreeeResponse
        .filter((accountSettingsItem) => {
          const hasAccountSettingData = hasAccountSettingsWalletablesData!.find(
            (item) => accountSettingsItem.accountId === item.id.toString()
          );
          return hasAccountSettingData !== undefined;
        })
        .map((accountSettingsItem, index) => {
          const hasAccountSettingData = hasAccountSettingsWalletablesData!.find(
            (item) => accountSettingsItem.accountId === item.id.toString()
          );
          return {
            ...hasAccountSettingData,
            bankName: '',
            isHidden: accountSettingsItem.isHidden,
            displayOrder: index,
            accountApiType: accountSettingsItem.accountType,
            serverDateTime: serverDateTime(),
            lastSyncedAt: convertToDateTimeSlash(
              hasAccountSettingData?.lastSyncedAt
            ),
            syncStatus: convertToIsFreeeReConnect(
              hasAccountSettingData?.syncStatus
            )
          };
        });

      // 口座表示設定を持たないので初期値を設定
      // 返却されないのでindexをそれぞれの口座に付与させる
      const noAccountSettingsDataAccounts =
        noAccountSettingsWalletablesData?.map((walletableItem, index) => ({
          ...walletableItem,
          bankName: '',
          isHidden: false,
          displayOrder: hasAccountSettingsDataAccounts.length + index,
          accountApiType: 'freee',
          serverDateTime: serverDateTime(),
          lastSyncedAt: convertToDateTimeSlash(walletableItem.lastSyncedAt),
          syncStatus: convertToIsFreeeReConnect(walletableItem.syncStatus)
        }));

      // 口座表示設定を持つ利用者情報と口座表示設定を持たない利用者情報をマージする
      const walletables = [
        ...hasAccountSettingsDataAccounts,
        ...noAccountSettingsDataAccounts!
      ];

      data = {
        walletables
      };

      res.status(200).json(data);
      next();
    } catch (error) {
      next(error);
    }
  }
};

export default freeeWalletablesController;
