import { Request, Response, NextFunction } from 'express';
import { clientId, clientSecret, mobileEncryptSalt } from '../config/config';

const classifiedController = {
  getSaltValue: (req: Request, res: Response, next: NextFunction) => {
    res.status(200).json({
      mobileEncryptSalt
    });
    return next();
  },

  getClientInfo: (req: Request, res: Response, next: NextFunction) => {
    res.status(200).json({
      clientId,
      clientSecret
    });
    return next();
  }
};

export default classifiedController;
