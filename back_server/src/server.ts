import express, { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { CustomError } from './errors/customError';
import router from './routes/router';
import errorHandler from './middlewares/errorHandler';
import { DATE_FORMAT_SLASH, serverDate, serverTime } from './utils/dateFormat';
import { loggerContext } from './utils/logger';
import { NotFoundError } from './errors/httpErrors';
import { errorCodes } from './errors/errorCodes';

const app: express.Express = express();

// Expressが生成するX-Powered-Byヘッダを削除
app.disable('x-powered-by');

if (process.env.NODE_ENV !== 'production') {
  app.use(express.static('public'));
}

// body-parserに基づいた着信リクエストの解析
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// logging（開始ログの出力と終了ログのイベント設定）
app.use(loggerContext.initLoggerContext);

app.use((req, res, next) => {
  // リクエストヘッダーにrequestId(UUID)を付与
  req.headers.requestId = uuidv4();
  // レスポンスヘッダーにbaseDateとbaseTimeを付与
  res.setHeader('X-Base-Date', serverDate(DATE_FORMAT_SLASH));
  res.setHeader('X-Base-Time', serverTime());
  loggerContext.set(
    'ip',
    req.header('X-Forwarded-For') ? req.header('X-Forwarded-For')! : ''
  );
  next();
});

app.get('/mobile/health', (req, res) => {
  // 必要なヘルスチェックロジックをここに実装
  // 正常であれば200 OKを返す
  res.status(200).json({ status: 'ok' });
});

// Routerの設定
app.use('/mobile/api', router);

// 404ハンドラー
app.all('*', (req, res, next) => {
  const notFoundError = new NotFoundError(
    errorCodes.NOT_FOUND,
    'Contents is Not Found'
  );
  next(notFoundError);
});

// エラーハンドラミドルウェアの適用
app.use((err: CustomError, req: Request, res: Response, next: NextFunction) => {
  errorHandler(err, req, res).catch(next);
});

export default app;
