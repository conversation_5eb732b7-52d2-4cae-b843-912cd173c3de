import dotenv from 'dotenv';
import { response } from 'express';
import app from './server';
import { getErrorMaster } from './services/errorService';
import { logger } from './utils/logger';
import type {} from './types/global';

dotenv.config();

const PORT = process.env.PORT ?? 3500;

// APIサーバ起動
const server = app.listen(PORT, () => {
  logger.writeMsg('ICOM000005', [`アプリケーションを起動 ${PORT}`]);
  // エラーマスタ取得
  response.errorMaster = getErrorMaster();
});

process.on('SIGTERM', () => {
  logger.writeMsg('ICOM000003', [
    'Received SIGTERM signal. Shutting down gracefully...'
  ]);
  setTimeout(() => {
    logger.writeMsg('ICOM000003', ['Graceful shutdown.']);
    process.exit(0);
  }, 30000);
});

export const closeServer = () => {
  server.close();
};
