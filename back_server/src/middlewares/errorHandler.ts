import { Request, Response, response } from 'express';
import { BackendErrorResponse, ErrorResponse } from '../models/errorResponse';
import { errorCodes } from '../errors/errorCodes';
import {
  InternalServerError,
  BadRequestError,
  AuthorizationError,
  ForbiddenError,
  NotFoundError,
  DBDuplicationError
} from '../errors/httpErrors';
import { CustomError } from '../errors/customError';
import { logger } from '../utils/logger';
import renderError from '../utils/renderError';

function isBackendErrorResponse(
  error: ErrorResponse
): error is BackendErrorResponse {
  return (
    (error as BackendErrorResponse).response !== undefined &&
    (error as BackendErrorResponse).response.data !== undefined &&
    (error as BackendErrorResponse).response.data.code !== undefined &&
    (error as BackendErrorResponse).response.data.message !== undefined
  );
}

// エラーマスタとのマッピング
export async function errorCodeMapping(targetErrorCode: string) {
  const errorMaster = await response.errorMaster;
  const errorObject = errorMaster?.find((data) => {
    let error;
    if (targetErrorCode === data.errorCode.S!) {
      error = data.errorMessage.S!;
    }
    return error;
  });

  const errorCode = errorObject
    ? errorObject.errorCode.S!
    : errorCodes.NOT_ERROR_CODE;
  const errorMessage = errorObject
    ? errorObject.errorMessage.S!
    : '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。';
  // hasFaqフラグを取得。レコードが存在していない場合はfalseで設定。
  const hasFaq = errorObject?.hasFaq ? errorObject.hasFaq.BOOL! : false;

  return { errorCode, errorMessage, hasFaq };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars, max-len
const errorHandler = async (
  err: ErrorResponse,
  req: Request,
  res: Response
) => {
  if (!('errorCode' in err) && !isBackendErrorResponse(err)) {
    logger.writeMsg('ECOM000003', [renderError(err)]);

    return res.status(500).json({
      errorCode: errorCodes.NOT_ERROR_CODE,
      errorMessage:
        '現在データが取得できません。しばらくたってから再度お試しいただけますようお願いいたします。',
      hasFaq: false
    });
  }

  let customError;
  if (isBackendErrorResponse(err)) {
    logger.writeMsg('ICOM000004', [
      `${err.response.data.code}`,
      `${err.response.data.message}`,
      JSON.stringify(err.response.data.details)
    ]);

    if (!err.response.data.code) {
      const { errorCode, errorMessage } = await errorCodeMapping(
        errorCodes.NOT_ERROR_CODE
      );
      return res.status(500).json({ errorCode, errorMessage });
    }
    // バックエンドのエラーハンドリング
    const { errorCode, errorMessage } = await errorCodeMapping(
      err.response.data.code
    );

    switch (err.response.status) {
      case 400:
        customError = new BadRequestError(
          errorCode,
          JSON.stringify(err.response.data.details)
        );
        break;
      case 401:
        customError = new AuthorizationError(
          errorCode,
          JSON.stringify(err.response.data.details)
        );
        break;
      case 403:
        customError = new ForbiddenError(
          errorCode,
          JSON.stringify(err.response.data.details)
        );
        break;
      case 404:
        customError = new NotFoundError(
          errorCode,
          JSON.stringify(err.response.data.details)
        );
        break;
      case 409:
        customError = new DBDuplicationError(
          errorCode,
          JSON.stringify(err.response.data.details)
        );
        break;
      case 500:
        customError = new InternalServerError(
          errorCode,
          JSON.stringify(err.response.data.details)
        );
        break;
      default:
        customError = new CustomError(
          err.response.status,
          errorCode,
          JSON.stringify(err.response.data.details)
        );
    }
    return res.status(customError.statusCode).json({
      errorCode: customError.errorCode,
      errorMessage
    });
  }

  const { errorCode, errorMessage, hasFaq } = await errorCodeMapping(
    err.errorCode
  );

  const errorDetail = err.errorDetail as unknown as string;
  const errorLog = `${errorCode} ${errorDetail}`;
  // BFF内部のエラーを返す
  if (errorCode.startsWith('E')) {
    logger.writeMsg('ECOM000003', [errorLog]);
  } else if (errorCode.startsWith('W')) {
    logger.writeMsg('WCOM000001', [errorLog]);
  } else {
    logger.writeMsg('ICOM000003', [errorLog]);
  }

  return res.status(err.statusCode).json({
    errorCode,
    errorMessage,
    hasFaq
  });
};

export default errorHandler;
