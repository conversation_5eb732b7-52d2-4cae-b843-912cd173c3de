import { Request, Response, NextFunction } from 'express';
import { logger, loggerContext } from '../utils/logger';
import { components } from '../schemas/schema';

export const onStart = (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers?.authorization;
    const sessionId = authHeader?.split(' ')[1];

    loggerContext.set('sessionId', sessionId ?? '');
    loggerContext.set(
      'uuid',
      req.headers ? (req.headers.requestId as string) : ''
    );
    loggerContext.set(
      'ip',
      req.header('X-Forwarded-For') ? req.header('X-Forwarded-For')! : ''
    );
    loggerContext.set('valuedoorId', req.session ? req.session.vdId! : '');
    loggerContext.set('userId', req.session ? req.session.userId! : '');
    // queryにお手続き番号がある場合はqueryより取得してセット
    if (req.query?.referenceNumber) {
      loggerContext.set(
        'referenceNumber',
        req.query?.referenceNumber.toString()
      );
      // eKYC URL生成要求APIの場合はbodyより取得してセット
    } else if (req.body as components['schemas']['EkycUrlRequest']) {
      const { referenceNumber } =
        req.body as components['schemas']['EkycUrlRequest'];
      loggerContext.set('referenceNumber', referenceNumber);
      // 本人確認情報保存APIの場合はbodyより取得してセット
    } else if (req.body as components['schemas']['IdentityVerification']) {
      const { referenceNumber } =
        req.body as components['schemas']['IdentityVerification'];
      loggerContext.set('referenceNumber', referenceNumber);
      // 上記以外の場合は空文字をセット
    } else {
      loggerContext.set('referenceNumber', '');
    }
    logger.writeMsg('ICOM000001', ['開始']);
    next();
  } catch (error) {
    next(error);
  }
};
