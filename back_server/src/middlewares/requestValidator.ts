import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { errorCodes } from '../errors/errorCodes';
import { ValidationError } from '../errors/httpErrors';

export const validate = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const validationError = new ValidationError(
      errorCodes.INVALID_REQUEST,
      JSON.stringify(errors.array({ onlyFirstError: true }))
    );
    return next(validationError);
  }
  return next();
};
