import { Request, Response, NextFunction } from 'express';
import { errorCodes } from '../errors/errorCodes';
import { AuthorizationError } from '../errors/httpErrors';
import { unixTime } from '../utils/dateFormat';
import { getSession } from '../services/sessionService';

export const authenticateUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers?.authorization;

  if (!authHeader?.startsWith('Bearer ')) {
    const authorizationError = new AuthorizationError(
      errorCodes.AUTHORIZATION_ERROR,
      `認証エラー authHeader=${authHeader as string}`
    );
    return next(authorizationError);
  }
  const sessionId = authHeader.split(' ')[1];
  const session = await getSession(sessionId);

  if (!session) {
    const authorizationError = new AuthorizationError(
      errorCodes.NOT_AUTHORIZATION,
      `セッションIDに紐づく認証情報がありませんでした。sessionId=${sessionId}`
    );
    return next(authorizationError);
  }

  if (!session.expirationTime || Number(session.expirationTime) < unixTime()) {
    const authorizationError = new AuthorizationError(
      errorCodes.EXPIRED_SESSION,
      `セッションの有効期限が切れています。sessionId=${sessionId}`
    );
    return next(authorizationError);
  }

  // リクエストにセッション情報を追加
  req.session = session;

  return next();
};
