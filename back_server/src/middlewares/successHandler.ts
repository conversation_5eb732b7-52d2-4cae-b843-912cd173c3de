import { Request, Response, NextFunction } from 'express';
import { expirationTime, jstTime } from '../utils/dateFormat';
import { Session } from '../models/session';
import { updateExpirationTime } from '../services/sessionService';
import { logger } from '../utils/logger';
import { longerSessionEndPointList } from '../config/config';

export const onSuccess = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { session, path } = req;
    // セッション延長フラグ(現状の対象はWeb21SSO共通リクエストのみ)
    const needLongerSession = longerSessionEndPointList.includes(path);
    // セッション有効期限更新
    const sessionData: Session = {
      sessionId: session.sessionId,
      expirationTime: expirationTime(needLongerSession),
      updatedAt: jstTime()
    };
    // セッションIDに紐づけてexpirationTimeを更新
    await updateExpirationTime(sessionData);
  } catch (error) {
    next(error);
  }
};

export const onEnd = (req: Request, res: Response, next: NextFunction) => {
  try {
    res.on('finish', () => {
      logger.writeMsg('ICOM000001', ['終了']);
    });
  } catch (error) {
    next(error);
  }
};
