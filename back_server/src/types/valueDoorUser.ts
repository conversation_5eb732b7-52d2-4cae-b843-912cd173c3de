import { GetsAuthorizationGroup } from './getsAuthorizationGroup';
import { Web21AuthorizationGroup } from './web21AuthorizationGroup';

export type ValueDoorUser = {
  /**
   * @description value door ID
   * @example **********
   */
  id: string;
  /**
   * @description ログイン時の認証方法
   * - 01: ID/PW
   * - 02: ICカード
   * - 03: 電子証明
   *
   * @enum {string}
   */
  authenticationMethod: '01' | '02' | '03';
  /**
   * @description 取引先の申込代表口座
   * 天番（4桁）＋科目（2桁、21:当座、22:普通）＋口座番号（7桁）
   *
   * @example *************
   */
  representativeAccount: string;
  /**
   * @description 取引先を示す一連番号
   * @example **********
   */
  clientId: string;
  /**
   * @description 取引先の日本語名
   * @example 東京SAML企業
   */
  companyName: string;
  /**
   * @description 利用者の日本語名
   * @example 東京さむる太郎
   */
  userName: string;
  /**
   * @description 利用者区分
   * - 01: 管理専用Id (01)
   * - 02: 管理専用Id（副） (02)
   * - 03: 利用者ID (03)
   * - 04: 利用者ID (04)
   * - 05: 電子契約専用ID (05)
   *
   * @enum {string}
   */
  userType: '01' | '02' | '03' | '04' | '05';
  /**
   * @description 取引先利用者属性
   * - 01: 一般利用者
   * - 09: 管理者
   *
   * @enum {string}
   */
  accessLevel: '01' | '09';
  /* eslint-disable no-irregular-whitespace */
  /**
   * @description 利用者名全角カナ
   * @example トウキョウ　サムルタロウ
   */
  usernameKana: string;
  /* eslint-enable no-irregular-whitespace */
  /**
   * @description <EMAIL>
   * @example 利用者のメールアドレス
   */
  email: string | undefined;
  /**
   * @description 利用者の電話番号
   * @example 08011112222
   */
  telNo: string | undefined;
  /**
   * @description web21企業コード
   * @example 0000**********
   */
  companyCode: string | undefined;
  /**
   * @description ログインタイプ
   * - 214: PCでログイン(214)
   * - 215: androidでログイン（215）
   * - 216: iPhoneでログイン（216）
   *
   * @enum {string}
   */
  loginType: '214' | '215' | '216';
  /**
   * @description OTP契約. 通し番号。現状、0固定
   * @example 0
   */
  otpId: string | undefined;
  /**
   * @description OTP契約. シリアル番号
   * @example **********01
   */
  otpSerialNo: string | undefined;
  /** @description OTP契約. ワンタイムパスワード利用フラグ */
  usesOtp: boolean | undefined;
  /**
   * @description OTP契約. ワンタイムパスワード種類
   * - 01: ワンタイムパスワードカード
   * - 02: ワンタイムパスワードアプリ
   * - 03: カメラ付きトークン
   *
   * @enum {string}
   */
  otpKind: '01' | '02' | '03' | undefined;
  /**
   * @description OTP契約．デバイス状態
   * - 01:発送準備中
   * - 02:発送指示済
   * - 03:発送済
   * - 04:利用中
   * - 90:初期不良
   * - 91:発送済(不着)
   * - 99:利用終了
   *
   * @enum {string}
   */
  otpDeviceStatus: '01' | '02' | '03' | '04' | '90' | '91' | '99' | undefined;
  /** @description OTP契約．再発行中フラグ */
  isOtpReissuing: boolean | undefined;
  /**
   * @description OTP契約. シリアル番号(再発行)
   * @example 123456789012
   */
  otpSerialNoReissue: string | undefined;
  /**
   * @description OTP契約. ワンタイムパスワード種類(再発行)
   * - 01: ワンタイムパスワードカード
   * - 02: ワンタイムパスワードアプリ
   * - 03: カメラ付きトークン
   *
   * @enum {string}
   */
  otpKindReissue: '01' | '02' | '03' | undefined;
  /**
   * @description OTP契約．デバイス状態(再発行)
   * - 01:発送準備中
   * - 02:発送指示済
   * - 03:発送済
   * - 91:発送済(不着)
   *
   * @enum {string}
   */
  otpDeviceStatusReissue: '01' | '02' | '03' | '91' | undefined;
  /**
   * @description OTP契約．再発行中の動作
   *   - 1:ログイン不可
   *   - 2:ワンタイムパスワード要求解除
   *   - 3:ワンタイムパスワード要求継続
   *
   * @enum {string}
   */
  otpReissueAction: '1' | '2' | '3' | undefined;
  /**
   * @description 利用者VIEW．OTP一時要求解除期限
   * 日付(YYYYMMDD)
   * 99999999(無期限)
   *
   * @example 20241225
   */
  otpNonOtpExpiredDate: string | undefined;
  /**
   * @description 親密企業.サービスコード＝0002：ワンタイムパスワードWeb21承認時区分（特殊先登録に使用）
   * 親密企業．サービス詳細コード
   * を参照して編集
   * - 0:一般先
   * - 1:特殊先
   *
   * @enum {string}
   */
  otpServiceCode: '0' | '1' | undefined;
  /**
   * @description VD申込代表口座に紐づくWeb21の企業コードの契約種別
   * - 1: エキスパート
   * - 2: Web21-X
   * - 3: デビュー
   * - 4: ライト
   *
   * @enum {string}
   */
  contractType: '1' | '2' | '3' | '4' | undefined;
  authorizations: {
    web21: Web21AuthorizationGroup | null;
    gets: GetsAuthorizationGroup | null;
  };
};
