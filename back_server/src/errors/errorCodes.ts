// エラーコードは仮の値
export const errorCodes = {
  NOT_ERROR_CODE: 'E005-00009',
  NOT_FOUND_USER_INFO: 'E005-00001',
  // E005-00002は不要になったため削除
  NOT_FOUND_UUID: 'E005-00003',
  NOT_CORRECT_VDID: 'E005-00004',
  NOT_CORRECT_OTP: 'E005-00005',
  ENCRYPTION_FAILED: 'E005-00006',
  DECRYPTION_FAILED: 'E005-00007',
  SAVE_IDENTITYVERIFICATION_FAILED: 'E005-00008',
  NOT_FOUND: 'W005-00001',
  INVALID_REQUEST: 'W005-00002',
  AUTHORIZATION_ERROR: 'I005-00001',
  NOT_AUTHORIZATION: 'I005-00002',
  EXPIRED_SESSION: 'I005-00003',
  // I005-00004は不要になったため削除
  AUTHORIZATION_DENIAL_HISTORY: 'I005-00005',
  ACCOUNT_NOT_KIGYO_CD: 'I005-00006',
  NOT_FOUND_ADDRESS: 'I005-00007',
  ACCOUNT_CREATION_IN_PROGRESS: 'I005-00008',
  AUTH_HEADER_EMPTY_ERROR: 'I005-00009',
  NOT_HIGH_RISK_USER: 'I005-00010',
  EXPIRED_HIGH_RISK_USER: 'I005-00011',
  JUDGE_RISK_RESPONSE_ERROR: 'I005-00012',
  NOT_EMAIL_WITH_VDID: 'I005-00013',
  // I005-00014はAPIのレスポンスに応じてフロントで採番になったためBFFになし
  NOT_FOUND_USER_TYPE: 'I005-00015',
  NOT_FOUND_BENEFICIARY: 'I005-00016'
};
