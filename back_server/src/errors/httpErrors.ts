/* eslint-disable max-classes-per-file */
import { CustomError } from './customError';

export class BadRequestError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(400, errorCode, errorDetail);
  }
}

export class ValidationError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(400, errorCode, errorDetail);
  }
}

export class AuthorizationError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(401, errorCode, errorDetail);
  }
}

export class ForbiddenError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(403, errorCode, errorDetail);
  }
}

export class NotFoundError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(404, errorCode, errorDetail);
  }
}

export class DBDuplicationError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(409, errorCode, errorDetail);
  }
}
export class InternalServerError extends CustomError {
  /**
   * @param errorCode エラーコード
   * @param errorDetail エラー詳細
   */
  constructor(errorCode: string, errorDetail: string) {
    super(500, errorCode, errorDetail);
  }
}
