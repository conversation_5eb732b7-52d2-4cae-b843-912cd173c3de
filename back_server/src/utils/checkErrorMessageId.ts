export const checkErrorMessageId = (errorMessageId: string): boolean => {
  const trueErrorMessageIds = [
    'D0989',
    'C0025',
    'C0048',
    'C0049',
    'C0002',
    'C0036',
    'C0046',
    'C0005'
  ];

  const falseErrorMessageIds = [
    'E5110001',
    'E5110004',
    'E5110015',
    'E5110006',
    'C0007',
    'C0001',
    'C0026',
    'D1068',
    'S2001'
  ];

  if (trueErrorMessageIds.includes(errorMessageId)) {
    return true;
  }
  if (falseErrorMessageIds.includes(errorMessageId)) {
    return false;
  }

  // errorMessageIdが両方のリストに含まれていない場合
  throw new Error(`Unknown error message ID: ${errorMessageId}`);
};
