import { ClosingTime } from 'models/closingTime';
import { jstTimeDate } from './dateFormat';

// 開局時間はtrueを返却し閉局時間はfalseを返却する
export const checkClosingStatus = (data: ClosingTime): boolean => {
  // 現在時刻
  const now = jstTimeDate();

  // 閉局開始時刻
  const startTime = new Date(now);

  // 閉局終了時刻
  const endTime = new Date(now);

  // 閉局開始時刻に曜日と時刻の情報を設定
  startTime.setHours(
    Number(data.start.time.split(':')[0]),
    Number(data.start.time.split(':')[1]),
    0,
    0
  );
  startTime.setDate(startTime.getDate() + data.start.day - now.getDay());

  // 閉局終了時刻に曜日と時刻の情報を設定
  endTime.setHours(
    Number(data.end.time.split(':')[0]),
    Number(data.end.time.split(':')[1]),
    0,
    0
  );
  endTime.setDate(endTime.getDate() + data.end.day - now.getDay());

  if (startTime > endTime) {
    return now < startTime && now > endTime;
  }

  return now < startTime || now > endTime;
};
