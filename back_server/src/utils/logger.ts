import { configure } from '@jridx/server-logger';
import messages from './messages';
import ILogger from './Ilogger';

const loggerCreator = configure({
  messages,
  format:
    '[%d{yyyy-MM-dd hh:mm:ss.SSSSS}]-[DTP-MOBILE-BFF]-[MOBILE]-[%p]-[%x{messageNo}]-[%x{valuedoorId}/%x{userId}/%x{referenceNumber}]-[%x{sessionId}]-[%x{method} %x{url}]-%x{message}-[%x{uuid}]-[%x{ip}]'
});

export const logger: ILogger = loggerCreator.getLogger();
export const { loggerContext } = loggerCreator;
